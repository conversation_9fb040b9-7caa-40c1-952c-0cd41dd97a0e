# MySQL到达梦数据库SQL转换指南

## 主要函数转换对照表

### 1. 日期时间函数
| MySQL函数 | 达梦数据库等价函数 | 说明 |
|-----------|-------------------|------|
| `MONTH(date)` | `EXTRACT(MONTH FROM date)` | 提取月份 |
| `YEAR(date)` | `EXTRACT(YEAR FROM date)` | 提取年份 |
| `DAY(date)` | `EXTRACT(DAY FROM date)` | 提取日期 |
| `NOW()` | `SYSDATE` | 当前时间 |
| `CURDATE()` | `TRUNC(SYSDATE)` | 当前日期 |

### 2. 字符串函数
| MySQL函数 | 达梦数据库等价函数 | 说明 |
|-----------|-------------------|------|
| `CONCAT(str1, str2, ...)` | `str1 \|\| str2 \|\| ...` | 字符串连接 |
| `SUBSTRING(str, pos, len)` | `SUBSTR(str, pos, len)` | 字符串截取 |
| `LENGTH(str)` | `LEN(str)` | 字符串长度 |
| `LOCATE(substr, str)` | `INSTR(str, substr)` | 查找子字符串位置 |
| `FIND_IN_SET(str, strlist)` | `INSTR(',' \|\| strlist \|\| ',', ',' \|\| str \|\| ',') > 0` | 在逗号分隔列表中查找 |

### 3. 聚合函数
| MySQL函数 | 达梦数据库等价函数 | 说明 |
|-----------|-------------------|------|
| `GROUP_CONCAT(col)` | `LISTAGG(col, ',') WITHIN GROUP (ORDER BY col)` | 分组连接 |

### 4. 数据类型转换
| MySQL函数 | 达梦数据库等价函数 | 说明 |
|-----------|-------------------|------|
| `CAST(expr AS type)` | `CAST(expr AS type)` | 类型转换（基本兼容） |

## 具体转换示例

### MONTH函数转换
```sql
-- MySQL
SELECT * FROM table WHERE MONTH(date_col) = 1;

-- 达梦数据库
SELECT * FROM table WHERE EXTRACT(MONTH FROM date_col) = 1;
```

### CONCAT函数转换
```sql
-- MySQL
SELECT CONCAT('Hello', ' ', 'World');

-- 达梦数据库
SELECT 'Hello' || ' ' || 'World';
```

### FIND_IN_SET函数转换
```sql
-- MySQL
SELECT * FROM table WHERE FIND_IN_SET('value', column_list) > 0;

-- 达梦数据库
SELECT * FROM table WHERE INSTR(',' || column_list || ',', ',' || 'value' || ',') > 0;
```

### SUBSTRING函数转换
```sql
-- MySQL
SELECT SUBSTRING(column, 1, 10) FROM table;

-- 达梦数据库
SELECT SUBSTR(column, 1, 10) FROM table;
```

### LENGTH函数转换
```sql
-- MySQL
SELECT LENGTH(column) FROM table;

-- 达梦数据库
SELECT LEN(column) FROM table;
```

## 注意事项

1. **递归CTE语法**：达梦数据库支持WITH RECURSIVE语法，与MySQL基本兼容
2. **字符串连接**：达梦数据库推荐使用 `||` 操作符而不是CONCAT函数
3. **日期函数**：达梦数据库的EXTRACT函数功能更强大，推荐使用
4. **自定义函数**：MySQL的自定义函数需要重写为达梦数据库的存储过程或函数
5. **索引语法**：基本兼容，但某些高级特性可能需要调整

## 批量替换建议

对于大量的SQL文件，建议使用以下正则表达式进行批量替换：

1. `MONTH\(([^)]+)\)` → `EXTRACT(MONTH FROM $1)`
2. `CONCAT\(([^)]+)\)` → 需要手动处理，转换为 `||` 连接
3. `SUBSTRING\(([^)]+)\)` → `SUBSTR($1)`
4. `LENGTH\(([^)]+)\)` → `LEN($1)`
5. `LOCATE\(([^,]+),\s*([^)]+)\)` → `INSTR($2, $1)`
