package com.baosight.bscdkj.ki.zl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bscdkj.ki.zl.service.ServiceKIZLApi;
import com.baosight.bscdkj.common.domain.CustomWorkFlow;
import com.baosight.bscdkj.common.ki.domain.TkizlApplyBaseinfo;
import com.baosight.bscdkj.common.ki.domain.TkizlSearchFxjs;
import com.baosight.bscdkj.common.ki.domain.TkizlSearchFxjsEx;
import com.baosight.bscdkj.ki.zl.business.BusinessApplyBaseinfoPatent;
import com.baosight.bscdkj.ki.zl.business.BusinessKIZLMoneyPayConfig;
import com.baosight.bscdkj.ki.zl.business.BusinessMoneyMonthplan;
import com.baosight.bscdkj.ki.zl.business.BusinessSearchFxjsPatent;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bscdkj.utils.BizIdUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
public class ServiceKIZLTest {

    @Autowired
    private BusinessKIZLMoneyPayConfig businessKIZLMoneyPayConfig;
    @Autowired
    private BusinessMoneyMonthplan businessMoneyMonthplan;
    @Autowired
    private ServiceKIZLApi serviceKIZLApi;
    @Autowired
    private BusinessApplyBaseinfoPatent businessApplyBaseinfoPatent;
    @Autowired
    private BusinessSearchFxjsPatent businessSearchFxjsPatent;

    @Before
    public void init() {
        UserSession.setProperty("iplat4j_loginName", "admin");
    }

    @Test
    public void guidTest() {
        System.out.println(BizIdUtil.INSTANCE.nextId());
    }

    @Test
    public void test() {
        Map<String, Object> map = new HashMap<>();
        map.put("applyId", "B12BC19D54196A42482588270041D666");
//        map.put("dynSql", "FIRST_DEPT_PATH like '%BGSG%' and exists( select * from GGMK.T_MPWF_FLOW_INFO wf where ZZZC.T_KIZL_APPLY_BASEINFO.APPLY_ID = wf.BUSINESS_ID and wf.CURRENT_ACTIVITY in('Manual6', 'Manual9'))");
        List<TkizlApplyBaseinfo> list = businessApplyBaseinfoPatent.queryList(map);
        for (TkizlApplyBaseinfo applyBaseinfo : list) {
            WorkFlow workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(applyBaseinfo.getApplyId());
            if (workFlow == null) {
                continue;
            }
            workFlow.setOperator("admin");
            if (StrUtil.isEmpty(workFlow.getTaskId())) {
                continue;
            }
            workFlow.setTaskId(workFlow.getTaskId().split(",")[0]);
            businessApplyBaseinfoPatent.review(applyBaseinfo, CustomWorkFlow.fromWorkFlow(workFlow));
        }
    }

    @Test
    public void testStat1() {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("startDate", "2022-01-01");
        eiInfo.set("endDate", "2022-12-31");
        eiInfo.set("gldwCode", "BGTA00");
        eiInfo.set(EiConstant.serviceId, "S_KI_ZL_017");
        eiInfo = serviceKIZLApi.stat1(eiInfo);
        System.out.println(eiInfo.getAttr());
    }

    @Test
    public void testFxjs() {
        TkizlSearchFxjs load = businessSearchFxjsPatent.load("20230330164219255724993");
        businessSearchFxjsPatent.startWF("admin", BeanUtil.copyProperties(load, TkizlSearchFxjsEx.class));
    }
}
