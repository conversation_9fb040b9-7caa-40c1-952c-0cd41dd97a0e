package com.baosight.bscdkj.ki.zl.service;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;

public class Test {
    /** 空行 */
    private static long nullLines = 0;
    /** 注释行 */
    private static long annoLines = 0;
    /** 代码行 */
    private static long codeLines = 0;
    /** 配置文件行 */
    private static long configLines = 0;
    /** 总行 */
    private static long allLines = 0;
    /**文件个数*/
    private static long fileSize = 0;

    public static void main(String[] args) {
        Test ccl = new Test();
        ccl.listFile("D:\\workspace\\projects\\baoxin\\bscdkj-zzzc\\src\\main\\resources\\templates\\KIZL\\TJ");
        ccl.searFiles("D://workspace/projects/baoxin/bscdkj-zzzc/src/main/resources/templates/KIZL/TJ",".html");
        System.out.println("空行:" + nullLines);
        System.out.println("注释行:" + annoLines);
        System.out.println("代码行：" + codeLines);
        System.out.println("配置文件行：" + configLines);
        System.out.println("总行：" + allLines);
        System.out.println("文件数：" + fileSize);
    }

    /**
     * 循环文件夹统计
     * @param filePath
     */
    private void listFile(String filePath) {
        File f = new File(filePath);
        File[] childs = f.listFiles();

        for (int i = 0; i < childs.length; i++) {
            if (!childs[i].isDirectory()) {
                if (childs[i].getName().matches(".*\\.java$")
                        ||childs[i].getName().endsWith(".yml")

                        || childs[i].getName().endsWith(".xml")
                    || childs[i].getName().endsWith(".properties")
                    || childs[i].getName().endsWith(".html")

                ) {
                    System.out.println(childs[i].getName());
                    sumCode(childs[i]);
                }
            }else {
                listFile(childs[i].getPath());
            }
        }
    }

    /**
     * 统计代码行数
     * @param file
     */
    private void sumCode(File file){
        BufferedReader br = null;
        try{
            br = new BufferedReader(new FileReader(file));
            String line = "";
            while ((line = br.readLine()) != null){
                allLines++;
                if(file.getName().endsWith(".yml")
                        || file.getName().endsWith(".xml")
                        || file.getName().endsWith(".properties")){
                    //配置文件
                    configLines++;
                }else {
                    //java文件
                    String trimStr = line.trim();
                    if (trimStr.length() == 0){
                        //空行
                        nullLines++;
                    }else if (trimStr.startsWith("//")
                            || trimStr.startsWith("/**")
                            || trimStr.startsWith("*")
                            || trimStr.startsWith("*/")
                            || trimStr.startsWith("/*")
                    ){
                        annoLines++;
                    }else {
                        codeLines++;
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }finally {
            try {
                br.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    public static void searFiles(String path,String ... type){

        File file = new File(path);
        if (file.isDirectory()){//目录
            File[] files = file.listFiles();
            for (int i = 0; i < files.length; i++){
                //利用递归调用依次查找目录
                searFiles(files[i].getAbsolutePath(),type);
            }
        }else{//文件
            String absolutePath = file.getAbsolutePath();
            //方法一
//            //获取文件的后缀名
//            int index = absolutePath.lastIndexOf(".");
//            String str = absolutePath.substring(index);
//            //逐个匹配指定类型文件
//            for (int i = 0; i < type.length; i++){
//                if (str.equals(type[i])){
//                    System.out.println(absolutePath);
//                }
//            }
            //方法二
            for (String string : type){
                //检查路径后缀是指定的
                if (absolutePath.endsWith(string)){
                    fileSize++;
                }
            }
        }
    }

}
