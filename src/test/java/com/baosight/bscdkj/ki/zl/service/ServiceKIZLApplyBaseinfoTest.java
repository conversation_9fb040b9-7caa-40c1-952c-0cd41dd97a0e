package com.baosight.bscdkj.ki.zl.service;

import com.baosight.bscdkj.mp.ad.dto.ADOrg;
import com.baosight.bscdkj.mp.ad.utils.OrgUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@RunWith(SpringRunner.class)
public class ServiceKIZLApplyBaseinfoTest {

    @Before
    public void init() {
        UserSession.setProperty("iplat4j_loginName", "admin");
    }

    @Test
    public void test() {
        ADOrg org = OrgUtil.getParentOrgByOrgCode("BSZGAE00");
        System.out.println(org.getOrgCode());
    }
}