// 表格增加行 （表头分组的 卦装），原 sub 中的方法 处理不了多表头的
var groupTableSub = {
  editColumn: function () {
    var dataColumns = [];
    var tableOptionCloumns = [];
    for (var groupIndex = 0; groupIndex < table.options.columns.length; groupIndex++) {
      if (table.options.columns[groupIndex] instanceof Array) {
        for (var columnIndex = 0; columnIndex < table.options.columns[groupIndex].length; columnIndex++) {
          tableOptionCloumns.push(table.options.columns[groupIndex][columnIndex])
        }
      } else {
        tableOptionCloumns.push(table.options.columns[groupIndex])
      }
    }
    for (var columnIndex = 0; columnIndex < tableOptionCloumns.length; columnIndex++) {
      if (tableOptionCloumns[columnIndex].visible != false) {
        dataColumns.push(tableOptionCloumns[columnIndex]);
      }
    }
    var params = new Array();
    var data = $("#" + table.options.id).bootstrapTable('getData');
    var count = data.length;
    for (var dataIndex = 0; dataIndex < count; dataIndex++) {
      var columns = $('#' + table.options.id + ' tr[data-index="' + dataIndex + '"] td');
      var obj = new Object();
      // index 列 物别处理
      obj.index = count;
      for (var i = 0; i < columns.length; i++) {
        var inputRadioValue = $(columns[i]).find('input:radio:checked');
        var inputValue = $(columns[i]).find('input');
        var selectValue = $(columns[i]).find('select');
        var textareaValue = $(columns[i]).find('textarea');
        // var key = dataColumns[i].field;
        // if (key == undefined) continue; // 未设置 field 的 跳过

        if ($.common.isNotEmpty(inputRadioValue.val())) {
          var key = inputRadioValue.attr("fieldName");
          obj[key] = inputRadioValue.val();
        } else if ($.common.isNotEmpty(inputValue.val())) {
          var key = inputValue.attr("fieldName");
          obj[key] = inputValue.val();
        } else if ($.common.isNotEmpty(selectValue.val())) {
          var key = selectValue.attr("fieldName");
          obj[key] = selectValue.val();
        } else if ($.common.isNotEmpty(textareaValue.val())) {
          var key = textareaValue.attr("fieldName");
          obj[key] = textareaValue.val();
        } else {
          // obj[key] = "";
        }
      }
      var item = data[dataIndex];
      var extendObj = $.extend({}, item, obj);
      params.push({
        index: dataIndex,
        row: extendObj
      });
    }
    $("#" + table.options.id).bootstrapTable("updateRow", params);
  },
  delColumn: function (column) {
    console.log(column);
    groupTableSub.editColumn();
    var subColumn = $.common.isEmpty(column) ? "index" : column;
    var ids = $.table.selectColumns(subColumn);
    if (ids.length == 0) {
      $.modal.alertWarning("请至少选择一条记录");
      return;
    }
    $("#" + table.options.id).bootstrapTable('remove', {
      field: subColumn,
      values: ids
    });
  },
  delColumnByIndex: function (indexColumn, index) {
    sub.editColumn();
    var ids = [];
    ids.push(index+"");
    $("#" + table.options.id).bootstrapTable('remove', {
      field: indexColumn,
      values: ids
    });
  },
  addColumn: function (row, tableId) {
    var currentId = $.common.isEmpty(tableId) ? table.options.id : tableId;
    table.set(currentId);
    var count = $("#" + currentId).bootstrapTable('getData').length;
    groupTableSub.editColumn();
    $("#" + currentId).bootstrapTable('insertRow', {
      index: count + 1,
      row: row
    });
  }
};
