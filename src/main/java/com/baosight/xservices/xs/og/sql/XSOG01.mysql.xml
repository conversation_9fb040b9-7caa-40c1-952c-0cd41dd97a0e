<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="XSOG01">

    <!--组织机构所有子节点树查询-->
    <select id="queryOrgChild" resultClass="java.util.HashMap">
        SELECT PARENT_ORG_ID, ORG_ID,ORG_ENAME,ORG_CNAME FROM ${platSchema}.TXSOG01 WHERE FIND_IN_SET(ORG_ID,${platSchema}.getChildrenOrg(#orgId#)) AND IS_DELETED = '0';
    </select>

    <!--根据组织机构编码向上递归组织机构 返回组织结构Id、父组织机构Id、由下向上的层级数-->
    <select id="getRecursiveUpOrg">
        select * from (
        SELECT
        @a as orgId,
        (SELECT org_ename FROM ${platSchema}.TXSOG01 where org_id = @a) as orgEname,
        (SELECT org_Cname FROM ${platSchema}.TXSOG01 where org_id = @a) as orgCname,
        (SELECT org_type FROM ${platSchema}.TXSOG01 where org_id = @a) as orgType,
        (SELECT @a:=parent_org_id from ${platSchema}.txsog01 where org_id = @a limit 0,1) as parentOrgId,
        @b := @b+1 as lv
        FROM (SELECT @a:= (SELECT ORG_ID FROM ${platSchema}.txsog01 WHERE 1=1
        <isNotEmpty prepend=" AND " property="orgId">
            ORG_ID = #orgId#
        </isNotEmpty>
        <isEmpty prepend=" " property="orgId">
            <isNotEmpty prepend=" AND " property="orgEname">
                ORG_ENAME = #orgEname#
            </isNotEmpty>
        </isEmpty>
        AND IS_DELETED = '0' limit 0,1),@b:=0) vars)tt
        where orgId <![CDATA[<>]]> ''
    </select>

    <!--根据组织机构代码获取匹配特定类型的父组织机构 mysql8.0-->
    <select id="getParentOrgByOrgEnameAndOrgType" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        WITH recursive report(orgId,orgEname,orgCname,orgBriefName,orgType,parentOrgId,establishDate,orgLevel,sortIndex,
        orgNodeType,isDelete) AS(
        SELECT
        ORG_ID as "orgId",  <!-- 	组织ID -->
        ORG_ENAME as "orgEname",  <!-- 	组织编码 -->
        ORG_CNAME as "orgCname",  <!-- 组织名称 -->
        ORG_BRIEF_NAME as "orgBriefName",  <!-- 组织别名 -->
        ORG_TYPE as "orgType",  <!-- 组织类型 -->
        PARENT_ORG_ID as "parentOrgId",  <!-- 上级组织编码 -->
        ESTABLISH_DATE as "establishDate",  <!-- 成立日期 -->
        ORG_LEVEL as "orgLevel",  <!-- 组织级别 -->
        SORT_INDEX as "sortIndex",  <!-- 排序 -->
        ORG_NODE_TYPE as "orgNodeType", <!-- 节点类型 -->
        IS_DELETED as "isDelete" <!-- 删除标记 -->
        FROM ${platSchema}.TXSOG01 where org_Ename= #orgEname#
        UNION ALL
        SELECT
        b.ORG_ID as "orgId",  <!-- 	组织ID -->
        b.ORG_ENAME as "orgEname",  <!-- 	组织编码 -->
        b.ORG_CNAME as "orgCname",  <!-- 组织名称 -->
        b.ORG_BRIEF_NAME as "orgBriefName",  <!-- 组织别名 -->
        b.ORG_TYPE as "orgType",  <!-- 组织类型 -->
        b.PARENT_ORG_ID as "parentOrgId",  <!-- 上级组织编码 -->
        b.ESTABLISH_DATE as "establishDate",  <!-- 成立日期 -->
        b.ORG_LEVEL as "orgLevel",  <!-- 组织级别 -->
        b.SORT_INDEX as "sortIndex",  <!-- 排序 -->
        b.ORG_NODE_TYPE as "orgNodeType", <!-- 节点类型 -->
        b.IS_DELETED as "isDelete" <!-- 删除标记 -->
        FROM report a,${platSchema}.TXSOG01 b WHERE a.parentOrgId = b.org_Id
        )
        select * FROM report WHERE orgEname != #orgEname#
        <isNotEmpty prepend=" AND " property="orgType">
            orgType = #orgType#
        </isNotEmpty>
    </select>

    <!--向上递归查询 到组织机构类型不等于company-->
    <select id="queryOneLevelOrgByOrgId" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT org_id as "orgId",org_ename as "orgEname",org_cname as "orgCname" from ${platSchema}.txsog01
        where FIND_IN_SET(org_id,${platSchema}.queryParentOrg(#orgId#,'company')) AND org_Id != 'root';
    </select>
</sqlMap>