<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap PUBLIC "-//iBATIS.com//DTD SQL Map 2.0//EN" "http://www.ibatis.com/dtd/sql-map-2.dtd">

<sqlMap namespace="XSOG01">

    <select id="queryOrgInfo" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        ORG_ID as "orgId",  <!-- 	组织ID -->
        ORG_ENAME as "orgEname",  <!-- 	组织编码 -->
        ORG_CNAME as "orgCname",  <!-- 组织名称 -->
        ORG_BRIEF_NAME as "orgBriefName",  <!-- 组织别名 -->
        ORG_TYPE as "orgType",  <!-- 组织类型 -->
        PARENT_ORG_ID as "parentOrgId",  <!-- 上级组织编码 -->
        ESTABLISH_DATE as "establishDate",  <!-- 成立日期 -->
        ORG_LEVEL as "orgLevel",  <!-- 组织级别 -->
        SORT_INDEX as "sortIndex",  <!-- 排序 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        ARCHIVE_FLAG as "archiveFlag", <!-- 归档标记 -->
        ORG_NODE_TYPE as "orgNodeType", <!-- 节点类型 -->
        IS_DELETED as "isDelete" <!-- 删除标记 -->
        FROM ${platSchema}.TXSOG01 WHERE 1=1
        <isNotEmpty prepend=" AND " property="orgId">
            ORG_ID = #orgId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgEname">
            ORG_ENAME = #orgEname#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isDeleted">
            IS_DELETED = #isDeleted#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isDeletedSQL">
            $isDeletedSQL$
        </isNotEmpty>
        ORDER BY ORG_ENAME,SORT_INDEX ASC
    </select>

    <select id="queryOrgByOrgIdAndEname" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        ORG_ID as "orgId",  <!-- 	组织ID -->
        ORG_ENAME as "orgEname",  <!-- 	组织编码 -->
        ORG_CNAME as "orgCname",  <!-- 组织名称 -->
        ORG_BRIEF_NAME as "orgBriefName",  <!-- 组织别名 -->
        ORG_TYPE as "orgType",  <!-- 组织类型 -->
        PARENT_ORG_ID as "parentOrgId",  <!-- 上级组织编码 -->
        ESTABLISH_DATE as "establishDate",  <!-- 成立日期 -->
        ORG_LEVEL as "orgLevel",  <!-- 组织级别 -->
        SORT_INDEX as "sortIndex",  <!-- 排序 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        ARCHIVE_FLAG as "archiveFlag", <!-- 归档标记 -->
        ORG_NODE_TYPE as "orgNodeType", <!-- 节点类型 -->
        IS_DELETED as "isDelete" <!-- 删除标记 -->
        FROM ${platSchema}.TXSOG01 WHERE 1=1
        <isNotEmpty prepend=" AND " property="orgCname">
            ORG_CNAME LIKE ('%$orgCname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgEname">
            ORG_ENAME LIKE ('%$orgEname$%')
        </isNotEmpty>
    </select>

    <select id="querySubOrgInfoByOrgEname" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.ORG_ID as "orgId",  <!-- 	组织ID -->
        a.ORG_ENAME as "orgEname",  <!-- 	组织编码 -->
        a.ORG_CNAME as "orgCname",  <!-- 组织名称 -->
        a.ORG_BRIEF_NAME as "orgBriefName",  <!-- 组织别名 -->
        a.ORG_TYPE as "orgType",  <!-- 组织类型 -->
        a.PARENT_ORG_ID as "parentOrgId",  <!-- 上级组织编码 -->
        a.ESTABLISH_DATE as "establishDate",  <!-- 成立日期 -->
        a.ORG_LEVEL as "orgLevel",  <!-- 组织级别 -->
        a.SORT_INDEX as "sortIndex",  <!-- 排序 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        a.ARCHIVE_FLAG as "archiveFlag", <!-- 归档标记 -->
        a.ORG_NODE_TYPE as "orgNodeType", <!-- 节点类型 -->
        a.IS_DELETED as "isDelete" <!-- 删除标记 -->
        FROM ${platSchema}.TXSOG01 a
        WHERE a.PARENT_ORG_ID IN (SELECT b.ORG_ID FROM ${platSchema}.TXSOG01 b WHERE  b.IS_DELETED='0'
        <isNotEmpty prepend=" AND " property="orgEname">
            b.ORG_ENAME = #orgEname#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgId">
            b.ORG_ID=#orgId#
        </isNotEmpty> )
        <isNotEmpty prepend=" AND " property="isDeleted">
            a.IS_DELETED = #isDeleted#
        </isNotEmpty>
        ORDER BY ORG_ENAME,SORT_INDEX ASC
    </select>


    <select id="queryChildOrgInfoByOrgEname" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.ORG_ID as "orgId",  <!-- 	组织ID -->
        a.ORG_ENAME as "orgEname",  <!-- 	组织编码 -->
        a.ORG_CNAME as "orgCname",  <!-- 组织名称 -->
        a.ORG_BRIEF_NAME as "orgBriefName",  <!-- 组织别名 -->
        a.ORG_TYPE as "orgType",  <!-- 组织类型 -->
        a.PARENT_ORG_ID as "parentOrgId",  <!-- 上级组织编码 -->
        a.ESTABLISH_DATE as "establishDate",  <!-- 成立日期 -->
        a.ORG_LEVEL as "orgLevel",  <!-- 组织级别 -->
        a.SORT_INDEX as "sortIndex",  <!-- 排序 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        a.ARCHIVE_FLAG as "archiveFlag", <!-- 归档标记 -->
        a.ORG_NODE_TYPE as "orgNodeType", <!-- 节点类型 -->
        a.IS_DELETED as "isDelete" <!-- 删除标记 -->
        FROM ${platSchema}.TXSOG01 a
        WHERE a.PARENT_ORG_ID = (SELECT b.ORG_ID FROM ${platSchema}.TXSOG01 b WHERE b.ORG_ENAME = #orgEname# AND  b.IS_DELETED = 0)
         AND   a.IS_DELETED = 0
        ORDER BY ORG_ENAME,SORT_INDEX ASC
    </select>

    <!--条件查询组织机构-->
    <select id="query" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        ORG_ID as "orgId",  <!-- 	组织ID -->
        ORG_ENAME as "orgEname",  <!-- 	组织编码 -->
        ORG_CNAME as "orgCname",  <!-- 组织名称 -->
        ORG_BRIEF_NAME as "orgBriefName",  <!-- 组织别名 -->
        ORG_TYPE as "orgType",  <!-- 组织类型 -->
        PARENT_ORG_ID as "parentOrgId",  <!-- 上级组织编码 -->
        ESTABLISH_DATE as "establishDate",  <!-- 成立日期 -->
        ORG_LEVEL as "orgLevel",  <!-- 组织级别 -->
        SORT_INDEX as "sortIndex",  <!-- 排序 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        ARCHIVE_FLAG as "archiveFlag", <!-- 归档标记 -->
        ORG_NODE_TYPE as "orgNodeType", <!-- 节点类型 -->
        IS_DELETED as "isDelete" <!-- 删除标记 -->
        FROM ${platSchema}.TXSOG01 WHERE 1=1 AND IS_DELETED = '0'
        <isNotEmpty prepend=" and UPPER(ORG_ENAME) like " property="orgEname">
            UPPER ('%$orgEname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="parentOrgId">
            PARENT_ORG_ID = #parentOrgId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgCname">
            ORG_CNAME like ('%$orgCname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgType">
            ORG_TYPE like ('%$orgType$%')
        </isNotEmpty>
        ORDER BY SORT_INDEX ASC
    </select>

    <!-- 根据id查询组织机构 -->
    <select id="queryByOrgId" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT * FROM ${platSchema}.TXSOG01 WHERE 1=1 AND IS_DELETED = '0'
        <isNotEmpty prepend=" AND " property="orgId">
            ORG_ID = #orgId#
        </isNotEmpty>
    </select>

    <!--旧版：根据组织编码或组织名称条件查询-->
    <select id="queryByOrgName" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        ORG_ID as "orgId",  <!-- 	组织ID -->
        ORG_ENAME as "orgEname",  <!-- 	组织编码 -->
        ORG_CNAME as "orgCname",  <!-- 组织名称 -->
        ORG_BRIEF_NAME as "orgBriefName",  <!-- 组织别名 -->
        ORG_TYPE as "orgType",  <!-- 组织类型 -->
        PARENT_ORG_Id as "parentOrgId",  <!-- 上级组织编码 -->
        ESTABLISH_DATE as "establishDate",  <!-- 成立日期 -->
        ORG_LEVEL as "orgLevel",  <!-- 组织级别 -->
        SORT_INDEX as "sortIndex",  <!-- 排序 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        ARCHIVE_FLAG as "archiveFlag", <!-- 归档标记 -->
        ORG_NODE_TYPE as "orgNodeType", <!-- 节点类型 -->
        IS_DELETED as "isDeleted" <!-- 删除标记 -->
        FROM ${platSchema}.TXSOG01 WHERE 1=1 AND IS_DELETED = '0'
        <isNotEmpty prepend=" AND " property="orgEname">
            ORG_ENAME = #orgEname#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgId">
            ORG_ID = #orgId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgCname">
            ORG_CNAME = #orgCname#
        </isNotEmpty>
    </select>
    <!--新版：根据组织编码或组织名称条件查询，且不管是不是已经逻辑删除-->
    <select id="queryByOrgNameWithDeleted" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        ORG_ID as "orgId",  <!-- 	组织ID -->
        ORG_ENAME as "orgEname",  <!-- 	组织编码 -->
        ORG_CNAME as "orgCname",  <!-- 组织名称 -->
        ORG_BRIEF_NAME as "orgBriefName",  <!-- 组织别名 -->
        ORG_TYPE as "orgType",  <!-- 组织类型 -->
        PARENT_ORG_Id as "parentOrgId",  <!-- 上级组织编码 -->
        ESTABLISH_DATE as "establishDate",  <!-- 成立日期 -->
        ORG_LEVEL as "orgLevel",  <!-- 组织级别 -->
        SORT_INDEX as "sortIndex",  <!-- 排序 -->
        REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时刻 -->
        ARCHIVE_FLAG as "archiveFlag", <!-- 归档标记 -->
        ORG_NODE_TYPE as "orgNodeType", <!-- 节点类型 -->
        IS_DELETED as "isDeleted" <!-- 删除标记 -->
        FROM ${platSchema}.TXSOG01 WHERE 1=1
        <isNotEmpty prepend=" AND " property="orgEname">
            ORG_ENAME = #orgEname#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgId">
            ORG_ID = #orgId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgCname">
            ORG_CNAME = #orgCname#
        </isNotEmpty>
    </select>

    <!--组织机构子节点树查询-->
    <select id="queryOrganiation" resultClass="java.util.HashMap">
        SELECT
        ORG_ID as "label",
        ORG_ENAME as "ename",
        ORG_CNAME as "text",
        ORG_NODE_TYPE as "leaf",
        ORG_TYPE as "type",
        PARENT_ORG_ID as "parentOrgId",
        IS_DELETED as "idDeleted"
        FROM ${platSchema}.TXSOG01 WHERE 1=1
        <isEmpty prepend=" AND " property="deletedQuery">
            IS_DELETED = '0'
        </isEmpty>
        <isNotEmpty prepend=" AND " property="node">
            PARENT_ORG_ID = #node#
        </isNotEmpty>
        order by SORT_INDEX asc
    </select>

    <!--组织机构所有子节点树查询 MYSQL8-->
    <select id="queryOrgChild" resultClass="java.util.HashMap">
        WITH RECURSIVE report(PARENT_ORG_ID, ORG_ID,ORG_ENAME,ORG_CNAME) AS (
        SELECT PARENT_ORG_ID, ORG_ID,ORG_ENAME,ORG_CNAME FROM ${platSchema}.TXSOG01 WHERE ORG_ID = #orgId# and is_deleted = '0'
        UNION ALL
        SELECT b.PARENT_ORG_ID, b.ORG_ID,b.ORG_ENAME,b.ORG_CNAME FROM report a, ${platSchema}.TXSOG01 b WHERE b.PARENT_ORG_ID = a.ORG_ID and b.is_deleted = '0'
        )
        SELECT * FROM report
    </select>

    <!--新增组织机构-->
    <insert id="insert">
        INSERT INTO ${platSchema}.TXSOG01 (
        ORG_ID, <!--组织ID-->
        ORG_ENAME,  <!-- 	组织编码 -->
        ORG_CNAME,  <!-- 组织名称 -->
        ORG_BRIEF_NAME,  <!-- 组织别名 -->
        ORG_TYPE,  <!-- 组织类型 -->
        PARENT_ORG_ID,  <!-- 上级组织编码 -->
        ESTABLISH_DATE,  <!-- 成立日期 -->
        ORG_LEVEL,  <!-- 组织级别 -->
        SORT_INDEX,  <!-- 排序 -->
        REC_CREATOR,  <!-- 记录创建责任者 -->
        REC_CREATE_TIME,  <!-- 记录创建时刻 -->
        REC_REVISOR,  <!-- 记录修改责任者 -->
        REC_REVISE_TIME,  <!-- 记录修改时刻 -->
        ARCHIVE_FLAG,  <!-- 归档标记 -->
        ORG_NODE_TYPE, <!-- 节点类型 -->
        IS_DELETED <!-- 删除标记 -->
        )
        VALUES (#orgId:VARCHAR#,#orgEname:VARCHAR#, #orgCname:VARCHAR#, #orgBriefName:VARCHAR#, #orgType:VARCHAR#,
        #parentOrgId:VARCHAR#, #establishDate:VARCHAR#, #orgLevel:VARCHAR#, #sortIndex#,
        #recCreator:VARCHAR#, #recCreateTime:VARCHAR#, #recRevisor:VARCHAR#, #recReviseTime:VARCHAR#,
        #archiveFlag:VARCHAR#,#orgNodeType:VARCHAR#,#isDeleted:VARCHAR#)
    </insert>

    <!--逻辑删除组织机构-->
    <update id="deleteOrg">
        UPDATE ${platSchema}.TXSOG01
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        IS_DELETED = '1' <!-- 删除标记 -->
        WHERE
        ORG_ID = #orgId#
    </update>

    <!--修改组织机构-->
    <update id="update">
        UPDATE ${platSchema}.TXSOG01
        SET
        ORG_ENAME = #orgEname#,    <!-- 组织编码 -->
        ORG_CNAME = #orgCname#,   <!-- 组织名称 -->
        ORG_BRIEF_NAME = #orgBriefName#,   <!-- 组织别名 -->
        ORG_TYPE = #orgType#,   <!-- 组织类型 -->
        PARENT_ORG_ID = #parentOrgId#,   <!-- 上级组织编码 -->
        ESTABLISH_DATE = #establishDate#,   <!-- 成立日期 -->
        ORG_LEVEL = #orgLevel#,   <!-- 组织级别 -->
        SORT_INDEX = #sortIndex#,   <!-- 排序 -->
        REC_CREATOR = #recCreator#,   <!-- 记录创建责任者 -->
        REC_CREATE_TIME = #recCreateTime#,   <!-- 记录创建时刻 -->
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        ARCHIVE_FLAG = #archiveFlag#,  <!-- 归档标记 -->
        ORG_NODE_TYPE = #orgNodeType#, <!-- 节点类型 -->
        IS_DELETED = #isDeleted# <!-- 删除标记 -->
        WHERE
        ORG_ID = #orgId#
    </update>

    <!--根据组织机构代码获取匹配特定类型的父组织机构 MYSQL8-->
    <select id="getParentOrgByOrgEnameAndOrgType" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        WITH RECURSIVE report(orgId,orgEname,orgCname,orgBriefName,orgType,parentOrgId,establishDate,orgLevel,sortIndex,
        orgNodeType,isDelete) AS(
        SELECT
        ORG_ID as "orgId",  <!-- 	组织ID -->
        ORG_ENAME as "orgEname",  <!-- 	组织编码 -->
        ORG_CNAME as "orgCname",  <!-- 组织名称 -->
        ORG_BRIEF_NAME as "orgBriefName",  <!-- 组织别名 -->
        ORG_TYPE as "orgType",  <!-- 组织类型 -->
        PARENT_ORG_ID as "parentOrgId",  <!-- 上级组织编码 -->
        ESTABLISH_DATE as "establishDate",  <!-- 成立日期 -->
        ORG_LEVEL as "orgLevel",  <!-- 组织级别 -->
        SORT_INDEX as "sortIndex",  <!-- 排序 -->
        ORG_NODE_TYPE as "orgNodeType", <!-- 节点类型 -->
        IS_DELETED as "isDelete" <!-- 删除标记 -->
        FROM ${platSchema}.TXSOG01 where org_Ename= #orgEname#
        UNION ALL
        SELECT
        b.ORG_ID as "orgId",  <!-- 	组织ID -->
        b.ORG_ENAME as "orgEname",  <!-- 	组织编码 -->
        b.ORG_CNAME as "orgCname",  <!-- 组织名称 -->
        b.ORG_BRIEF_NAME as "orgBriefName",  <!-- 组织别名 -->
        b.ORG_TYPE as "orgType",  <!-- 组织类型 -->
        b.PARENT_ORG_ID as "parentOrgId",  <!-- 上级组织编码 -->
        b.ESTABLISH_DATE as "establishDate",  <!-- 成立日期 -->
        b.ORG_LEVEL as "orgLevel",  <!-- 组织级别 -->
        b.SORT_INDEX as "sortIndex",  <!-- 排序 -->
        b.ORG_NODE_TYPE as "orgNodeType", <!-- 节点类型 -->
        b.IS_DELETED as "isDelete" <!-- 删除标记 -->
        FROM report a,${platSchema}.TXSOG01 b WHERE a.parentOrgId = b.org_Id
        )
        select * FROM report WHERE orgEname != #orgEname#
        <isNotEmpty prepend=" AND " property="orgType">
            orgType = #orgType#
        </isNotEmpty>
    </select>

    <!--根据用户及业务属性代码获取组织机构-->
    <select id="getOrgByUserAndClassification" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        c.ORG_ID as "orgId",
        c.ORG_ENAME as "orgEname",
        c.ORG_CNAME as "orgCname",
        c.ORG_BRIEF_NAME as "orgBriefName",
        c.ORG_TYPE as "orgType",
        c.PARENT_ORG_ID as "parentOrgId",
        c.ESTABLISH_DATE as "establishDate",
        c.ORG_LEVEL as "orgLevel",
        c.ARCHIVE_FLAG as "archiveFlag",
        c.ORG_NODE_TYPE as "orgNodeType",
        c.IS_DELETED as "isDelete",
        CASE WHEN a.DEFAULT_RELATION='1' THEN 'true' ELSE 'false' END as "defaultRelation"
        FROM ${platSchema}.TXSOG02 a
        LEFT JOIN ${platSchema}.XS_USER b on a.USER_ID = b.USER_ID
        <isNotEmpty property="value">
            LEFT JOIN ${platSchema}.TXSOG0101 d on a.ORG_ID = d.ID
            LEFT JOIN ${platSchema}.TXSOG01 c on d.ORG_ID = c.ORG_ID
        </isNotEmpty>
        <isEmpty property="value">
            LEFT JOIN ${platSchema}.TXSOG01 c on a.ORG_ID = c.ORG_ID
        </isEmpty>
        WHERE 1=1 AND c.IS_DELETED = '0'
        <isNotEmpty prepend=" AND " property="loginName">
            b.LOGIN_NAME = #loginName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userId">
            b.USER_ID = #userId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="value">
            d.VALUE = #value# AND d.FIELD_ID = 'Classification'
        </isNotEmpty>
        ORDER BY c.ORG_ENAME,c.SORT_INDEX ASC
    </select>

    <select id="queryOrgByUserClassAccountSet"  parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        c.ORG_ID as "orgId",
        c.ORG_ENAME as "orgEname",
        c.ORG_CNAME as "orgCname",
        c.ORG_BRIEF_NAME as "orgBriefName",
        c.ORG_TYPE as "orgType",
        c.PARENT_ORG_ID as "parentOrgId",
        c.ESTABLISH_DATE as "establishDate",
        c.ORG_LEVEL as "orgLevel",
        c.ARCHIVE_FLAG as "archiveFlag",
        c.ORG_NODE_TYPE as "orgNodeType",
        c.IS_DELETED as "isDelete",
        CASE WHEN a.DEFAULT_RELATION='1' THEN 'true' ELSE 'false' END as "defaultRelation"
        FROM ${platSchema}.TXSOG02 a
        LEFT JOIN ${platSchema}.XS_USER b on a.USER_ID = b.USER_ID
        LEFT JOIN ${platSchema}.TXSOG0101 d on a.ORG_ID = d.ID
        LEFT JOIN ${platSchema}.TXSOG01 c on d.ORG_ID = c.ORG_ID
        LEFT JOIN ${platSchema}.TXSOG07 e on c.ORG_ID = e.ORG_ID
        WHERE 1=1 AND c.IS_DELETED = '0'
        <isNotEmpty prepend=" AND " property="loginName">
            b.LOGIN_NAME = #loginName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userId">
            b.USER_ID = #userId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="value">
            d.VALUE = #value# AND d.FIELD_ID = 'Classification'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="companyEname">
            e.COMPANY_ENAME = #companyEname#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="accountSetEname">
            e.SOB_ENAME = #accountSetEname#
        </isNotEmpty>
        ORDER BY c.ORG_ENAME,c.SORT_INDEX ASC
    </select>

    <!--根据用户查询用户组及用户组所属机构-->
    <select id="getUserGroupOrgByUser" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.id as "userGroupId",
        a.GROUP_ENAME as "groupEname",
        a.GROUP_CNAME as "groupCname",
        a.GROUP_TYPE as "groupType",
        e.ORG_ID as "orgId",
        e.ORG_ENAME as "orgEname",
        e.ORG_CNAME as "orgCname",
        e.ORG_TYPE as "orgType",
        e.PARENT_ORG_ID as "parentOrgId",
        e.ESTABLISH_DATE as "establishDate",
        e.IS_DELETED as "isDeleted"
        FROM ${platSchema}.XS_USER_GROUP a
        LEFT JOIN ${platSchema}.XS_USER_GROUP_MEMBER b ON a.id = b.PARENT_ID
        LEFT JOIN ${platSchema}.XS_USER c ON b.Member_id = c.USER_ID
        LEFT JOIN ${platSchema}.TXSOG03 d ON a.ID = d.USER_GROUP_ID
        LEFT JOIN ${platSchema}.TXSOG01 e ON d.ORG_ID = e.ORG_ID
        WHERE 1=1 AND b.MEMBER_TYPE = 'USER'
        <isNotEmpty prepend=" AND " property="loginName">
            c.login_name = #loginName#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userId">
            c.user_id=#userId#
        </isNotEmpty>
    </select>

    <!--组织机构拖动功能修改父节点-->
    <update id="updateTree">
        UPDATE ${platSchema}.TXSOG01
        SET
        REC_REVISOR = #recRevisor#,   <!-- 记录修改责任者 -->
        REC_REVISE_TIME = #recReviseTime#,   <!-- 记录修改时刻 -->
        PARENT_ORG_ID = #parentOrgId#  <!-- 上级组织编码 -->
        WHERE
        ORG_ID = #orgId#
    </update>

    <select id="orgClassificationUser" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.ORG_ID as "orgId",  <!-- 组织ID -->
        c.ORG_CNAME as "orgCname",  <!-- 组织编码 -->
        c.ORG_ENAME as "orgEname",  <!-- 组织名称 -->
        d.USER_ID as "userId",  <!-- 用户ID -->
        d.LOGIN_NAME as "loginName", <!--登录账号-->
        d.USER_NAME as "userName", <!-- 用户姓名 -->
        a.REC_CREATOR as "recCreator",  <!-- 记录创建责任者 -->
        a.REC_CREATE_TIME as "recCreateTime",  <!-- 记录创建时刻 -->
        a.REC_REVISOR as "recRevisor",  <!-- 记录修改责任者 -->
        a.REC_REVISE_TIME as "recReviseTime",  <!-- 记录修改时间 -->
        a.ARCHIVE_FLAG as "archiveFlag", <!-- 归档标记 -->
        a.DEFAULT_RELATION as "defaultRelation", <!--属性组织机构与用户的默认关系标记-->
        b.value as "classification" <!--组织机构业务属性-->
        FROM ${platSchema}.TXSOG02 a
        LEFT JOIN ${platSchema}.TXSOG0101 b ON a.org_id = b.id
        LEFT JOIN ${platSchema}.TXSOG01 c ON b.org_id = c.org_id
        LEFT JOIN ${platSchema}.XS_USER d ON a.user_id = d.User_id
        WHERE 1=1 AND c.IS_DELETED = '0'
        <isNotEmpty prepend=" AND " property="orgId">
            b.ID = #orgId#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loginName">
            d.LOGIN_NAME like ('%$loginName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userName">
            d.USER_NAME like ('%$userName$%')
        </isNotEmpty>
    </select>

    <select id="queryUserOrgAndaccountSet" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT distinct
        a.LOGIN_NAME as "loginName",
        a.USER_NAME as "userName",
        a.MOBILE as "mobile",
        a.USER_TYPE as "userType",
        a.USER_ID as "userId",
        a.EMAIL as "email",
        c.ORG_ID as "orgId",
        c.ORG_ENAME as "orgEname",
        c.ORG_CNAME as "orgCname",
        c.ORG_TYPE as "orgType",
        d.COMPANY_ENAME as "companyEname",
        d.COMPANY_CNAME as "companyCname",
        d.SOB_ENAME as "accountSetEname",
        d.SOB_CNAME as "accountSetCname"
        FROM
        ${platSchema}.XS_USER a
        LEFT JOIN ${platSchema}.TXSOG02 b ON a.USER_ID = b.USER_ID AND b.ORG_ID IN (SELECT ORG_ID FROM ${platSchema}.TXSOG01)
        LEFT JOIN ${platSchema}.TXSOG01 c ON b.ORG_ID = c.ORG_ID AND c.IS_DELETED = '0'
        LEFT JOIN ${platSchema}.TXSOG07 d ON c.ORG_ID = d.ORG_ID
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="loginName">
            a.LOGIN_NAME like ('%$loginName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userName">
            a.USER_NAME like ('%$userName$%')
        </isNotEmpty>
        ORDER BY a.LOGIN_NAME,a.USER_NAME ASC
    </select>

    <!--根据用户登录名、账套、业务组织获取对应的组织机构信息-->
    <select id="queryOrgByLoginNameAndAccountSet" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        b.org_ID as "orgId",
        e.ORG_TYPE as "orgType"
        FROM ${platSchema}.txsog02 a
        LEFT JOIN ${platSchema}.txsog0101 b ON a.org_id = b.id
        LEFT JOIN ${platSchema}.txsog07 c ON b.org_id = c.ORG_ID
        LEFT JOIN ${platSchema}.XS_USER d ON a.user_id = d.user_id
        LEFT JOIN ${platSchema}.TXSOG01 e ON b.ORG_ID = e.ORG_ID
        WHERE 1=1 AND e.IS_DELETED = '0'
        <isNotEmpty prepend=" AND " property="value">
            b.value = #value#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="accountSet">
            c.SOB_ENAME = #accountSet#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="companyEname">
            c.COMPANY_ENAME = #companyEname#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loginName">
            d.LOGIN_NAME = #loginName#
        </isNotEmpty>
    </select>

    <!--递归查询该组织的一级组织 MYSQL8-->
    <select id="queryOneLevelOrgByOrgId" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        WITH RECURSIVE respse(org_id,parent_org_id,org_ename,org_cname,IS_DELETED) as (
        select org_id,parent_org_id,org_ename,org_cname,IS_DELETED FROM ${platSchema}.TXSOG01 where ORG_ID = #orgId#
        UNION ALL
        SELECT b.org_id,b.parent_org_id,b.org_ename,b.org_cname,b.IS_DELETED FROM respse a ,${platSchema}.TXSOG01 b WHERE b.org_id = a.parent_org_id AND b.ORG_TYPE != 'company'
        )
        SELECT org_id as "orgId",org_ename as "orgEname",org_cname as "orgCname" FROM respse WHERE IS_DELETED='0'
    </select>

    <!--根据上一级组织id、登录名、业务属性获取对应的组织机构 MYSQL8-->
    <select id="queryOrgIdByParentOrgIdloginNameCla" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        WITH RECURSIVE respse(org_id,parent_org_id,org_ename,org_cname,IS_DELETED) as (
        SELECT org_id,parent_org_id,org_ename,org_cname,IS_DELETED FROM ${platSchema}.TXSOG01 where ORG_ID IN (
            SELECT b.org_ID as "orgId" FROM ${platSchema}.txsog02 a
            LEFT JOIN ${platSchema}.txsog0101 b ON a.org_id = b.id
            LEFT JOIN ${platSchema}.XS_USER d ON a.user_id = d.user_id
            WHERE 1=1 AND b.value = #value# AND d.LOGIN_NAME = #loginName#
        )
        UNION ALL
        SELECT b.org_id,b.parent_org_id,b.org_ename,b.org_cname,b.IS_DELETED FROM respse a ,${platSchema}.TXSOG01 b WHERE b.org_id = a.parent_org_id AND b.ORG_TYPE != 'company'
        )
        SELECT DISTINCT a.org_id,a.org_ename,a.org_cname,c.DEFAULT_RELATION,DECODE(c.DEFAULT_RELATION,NULL,0,1) as "DEFAULT_NULL" FROM respse a
        LEFT JOIN ${platSchema}.TXSOG0101 b ON a.ORG_ID = b.ORG_ID AND b.VALUE = #value#
        LEFT JOIN ${platSchema}.TXSOG02 c ON b.ID = c.ORG_ID
        WHERE a.parent_org_id = #parentOrgId# AND a.IS_DELETED='0' ORDER BY DEFAULT_NULL DESC,c.DEFAULT_RELATION DESC
    </select>

    <!--根据账套和或公司别获取组织机构信息-->
    <select id="queryOrgByAccountSetAndCompany" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.ORG_ID as "orgId",
        a.ORG_ENAME as "orgEname",
        a.ORG_CNAME as "orgCname",
        ORG_TYPE as "orgType",
        PARENT_ORG_ID as "parentOrgId",
        SORT_INDEX as "sortIndex"
        FROM ${platSchema}.TXSOG01 a
        LEFT JOIN ${platSchema}.TXSOG07 b ON a.org_id = b.org_id
        Where a.is_DELETED = '0'
        <isNotEmpty prepend=" AND " property="company">
            b.COMPANY_ENAME=#company#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="accountSet">
           b.SOB_ENAME=#accountSet#
        </isNotEmpty>
    </select>

    <!--根据组织机构编码 组织机构层级获取组织机构信息 MYSQL8-->
    <select id="queryOrgByOrgEnameAndLevel" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        WITH RECURSIVE respse(org_id,org_Ename,org_cname,org_type,parent_org_id,sort_index,is_deleted) as (
        SELECT org_id,org_Ename,org_cname,org_type,parent_org_id,sort_index,is_deleted FROM ${platSchema}.TXSOG01 WHERE org_ename = #orgEname# AND IS_DELETED ='0'
        UNION ALL
        SELECT b.org_id,b.org_Ename,b.org_cname,b.org_type,b.parent_org_id,b.sort_index,b.is_deleted FROM respse a,${platSchema}.TXSOG01 b WHERE a.PARENT_ORG_ID = b.ORG_ID AND b.IS_DELETED ='0'
        )
        SELECT
        org_id as "orgId",
        org_Ename as "orgEname",
        org_cname as "orgCname",
        org_type as "orgType",
        PARENT_ORG_ID as "parentOrgId",
        SORT_INDEX as "sortIndex"
        FROM respse
    </select>

    <!--递归业务组织到最上一层 MYSQL8-->
    <select id="queryOrgByOrgIdAndClas" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        WITH RECURSIVE respse(org_id,parent_org_id) AS (
        SELECT org_id,parent_org_id FROM ${platSchema}.TXSOG0101 WHERE org_id =#orgId# AND VALUE = #orgClassification#
        UNION ALL
        SELECT b.org_id,b.parent_org_id FROM respse a,${platSchema}.TXSOG0101 b WHERE a.parent_org_id = b.org_id AND VALUE = #orgClassification#
        )
        SELECT org_id as "orgId" FROM respse
    </select>

    <select id="queryUserByOrgUserGroup" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        c.USER_ID as "userId",
        c.LOGIN_NAME as "loginName",
        c.USER_NAME as "userName",
        c.MOBILE as "mobile",
        c.EMAIL as "email",
        c.GENDER as "gender",
        d.ID as "userGroupId",
        d.GROUP_ENAME as "groupEname",
        d.GROUP_CNAME as "groupCname",
        e.ORG_ID as "orgId",
        e.ORG_ENAME as "orgEname",
        e.ORG_CNAME as "orgCname"
        FROM ${platSchema}.TXSOG03 a
        LEFT JOIN ${platSchema}.XS_USER_GROUP_MEMBER b ON a.USER_GROUP_ID = b.PARENT_ID
        LEFT JOIN ${platSchema}.XS_USER c ON b.MEMBER_ID =c.USER_ID
        LEFT JOIN ${platSchema}.XS_USER_GROUP d ON a.USER_GROUP_ID = d.ID
        LEFT JOIN ${platSchema}.TXSOG01 e ON a.ORG_ID = e.ORG_ID
        WHERE e.IS_DELETED = '0' AND b.MEMBER_TYPE = 'USER' AND e.ORG_ENAME = #orgEname# AND d.GROUP_ENAME = #userGroupEname#
    </select>

    <select id="queryUserOrg" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        a.LOGIN_NAME as "loginName",
        a.USER_NAME as "userName",
        a.MOBILE as "mobile",
        a.EMAIL as "email",
        a.GENDER as "gender",
        a.STATUS as "status",
        a.ACCOUNT_EXPIRE_DATE as "accountExpireDate",
        a.PWD_EXPIRE_DATE as "pwdExpireDate",
        a.REC_CREATE_TIME AS "recCreateTime",
        a.REC_REVISE_TIME AS "recReviseTime",
        c.ORG_ENAME as "orgEname",
        c.ORG_CNAME as "orgCname",
        c.ORG_TYPE as "orgType",
        c.PARENT_ORG_ID as "parentOrgId",
        d.ORG_ENAME as "parentOrgEname",
        d.ORG_CNAME as "parentOrgCname",
        d.ORG_TYPE as "parentOrgType"
        FROM ${platSchema}.XS_USER a
        LEFT JOIN ${platSchema}.TXSOG02 b ON a.user_id = b.user_id AND b.ORG_ID IN (select org_id FROM ${platSchema}.TXSOG01 WHERE is_deleted = '0')
        LEFT JOIN ${platSchema}.TXSOG01 c ON b.ORG_ID = c.ORG_ID AND c.IS_DELETED = '0'
        LEFT JOIN ${platSchema}.TXSOG01 d ON c.PARENT_ORG_ID = d.ORG_ID AND d.IS_DELETED = '0'
        WHERE 1=1
        <isNotEmpty prepend=" AND " property="orgEname">
            c.ORG_ENAME LIKE ('%$orgEname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="orgCname">
            c.ORG_CNAME LIKE ('%$orgCname$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="loginName">
            a.LOGIN_NAME LIKE ('%$loginName$%')
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="userName">
            a.USER_NAME LIKE ('%$userName$%')
        </isNotEmpty>
        ORDER BY a.LOGIN_NAME ASC
    </select>

    <select id="queryOrgChildByOrgEname" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        WITH RECURSIVE report(PARENT_ORG_ID, ORG_ID,ORG_ENAME) AS (
        SELECT PARENT_ORG_ID, ORG_ID,ORG_ENAME FROM ${platSchema}.TXSOG01 WHERE ORG_ENAME = #orgEname# and is_deleted = '0'
        UNION ALL
        SELECT b.PARENT_ORG_ID, b.ORG_ID,b.ORG_ENAME FROM report a, ${platSchema}.TXSOG01 b WHERE b.PARENT_ORG_ID = a.ORG_ID and b.is_deleted = '0'
        )
        SELECT * FROM report
    </select>

    <!---->
    <select id="queryUserByGroupAndCompany" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT
        USER_ID as "userId",
        LOGIN_NAME as "loginName",
        USER_NAME as "userName",
        STATUS as "status",
        MOBILE as "mobile",
        EMAIL as "email",
        IS_LOCKED as "isLocked",
        PWD_EXPIRE_DATE as "pwdExpireDate",
        ACCOUNT_EXPIRE_DATE as "accountExpireDate"
        FROM ${platSchema}.XS_USER  WHERE USER_ID in (
        SELECT DISTINCT MEMBER_ID FROM ${platSchema}.XS_USER_GROUP_MEMBER WHERE MEMBER_TYPE = 'USER' AND PARENT_ID IN (
        SELECT XS_USER_GROUP.ID FROM ${platSchema}.XS_USER_GROUP WHERE XS_USER_GROUP.GROUP_ENAME IN (
        SELECT
        CONCAT(#usergoupEname#,CONCAT('@',a.ORG_ENAME)) as "userGroupEname"
        FROM ${platSchema}.TXSOG01 a
        LEFT JOIN ${platSchema}.TXSOG07 b ON a.ORG_ID = b.ORG_ID
        WHERE a.IS_DELETED = '0'
            <isNotEmpty prepend=" AND " property="companyEname">
                b.COMPANY_ENAME = #companyEname#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="accountSet">
                b.SOB_CNAME = #accountSet#
            </isNotEmpty>
        )
        ))
    </select>

    <!-- 根据业务属性、关联用户登录名查询业务属性组织机构第一层 -->
    <select id="queryOrgByClassLoginName" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        WITH RECURSIVE temp(ORG_ID,PARENT_ORG_ID)as(
                SELECT ORG_ID,PARENT_ORG_ID FROM ${platSchema}.TXSOG0101 WHERE ID IN (
                        SELECT b.ID FROM ${platSchema}.TXSOG0101 b
                        LEFT JOIN ${platSchema}.TXSOG02 a ON a.ORG_ID = b.ID
                        LEFT JOIN ${platSchema}.XS_USER c ON a.USER_ID = c.USER_ID
                        where b.VALUE = #value#
                        <isNotEmpty prepend=" AND " property="loginName">
                            c.LOGIN_NAME = #loginName#
                        </isNotEmpty>)
                UNION ALL
                SELECT b.ORG_ID,b.PARENT_ORG_ID FROM temp a,${platSchema}.TXSOG0101 b WHERE b.ORG_ID = a.PARENT_ORG_ID AND b.VALUE = #value#
        )
        SELECT b.ORG_ID as "orgId",b.ORG_ENAME as "orgEname",b.ORG_CNAME as "orgCname",b.IS_DELETED as "isDelete" FROM (
                SELECT DISTINCT * FROM temp
                WHERE parent_org_id NOT IN (select org_id from temp)
                ) temp1
        LEFT JOIN ${platSchema}.TXSOG01 b ON temp1.ORG_ID = b.ORG_ID
        WHERE b.ORG_ID IS NOT NULL ORDER BY b.IS_DELETED ASC
    </select>

    <select id="queryOrgByClassParentUser" parameterClass="java.util.HashMap"
            resultClass="java.util.HashMap">
        SELECT DISTINCT b.ORG_ID as "orgId",b.ORG_ENAME as "orgEname",b.ORG_CNAME as "orgCname",b.IS_DELETED as "isDelete"
        from ${platSchema}.TXSOG0101 a
        left JOIN ${platSchema}.TXSOG01 b ON a.org_id=b.org_Id
        LEFT JOIN ${platSchema}.TXSOG02 c ON a.id = c.ORG_ID
        LEFT JOIN ${platSchema}.XS_USER d ON c.USER_ID = d.USER_ID
        WHERE a.value = 'transferOrg'
        AND a.parent_org_id IN
            ( SELECT ORG_ID FROM ${platSchema}.TXSOG01 WHERE 1=1
            <isNotEmpty prepend=" AND " property="orgEname">
                ORG_ENAME = #orgEname#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="orgId">
                ORG_ID = #orgId#
            </isNotEmpty>)
        <isNotEmpty prepend=" AND " property="loginName">
            d.LOGIN_NAME = #loginName#
        </isNotEmpty>
        ORDER BY b.IS_DELETED ASC
    </select>

    <delete id="clearOrgEhrRela" parameterClass="java.util.HashMap">
        delete from ${platSchema}.TXSLV03 where ORG_ID in (
        select a.ORG_ID from ${platSchema}.TXSOG01 a
        join ${platSchema}.TXSLV03 b on a.ORG_ID = b.ORG_ID
        where a.ORG_ENAME = #orgEname#
        )
    </delete>

    <delete id="clearOrgAuth" parameterClass="java.util.HashMap">
        delete from ${platSchema}.XS_AUTHORIZATION where SUBJECT_ID in (
        SELECT
        c.SUBJECT_ID
        from ${platSchema}.TXSOG01 a
        join ${platSchema}.TXSOG03 b on a.ORG_ID = b.ORG_ID
        join ${platSchema}.XS_AUTHORIZATION c on b.USER_GROUP_ID = c.SUBJECT_ID
        where a.ORG_ENAME = #orgEname#
        )
    </delete>

    <delete id="clearOrgRoleMember" parameterClass="java.util.HashMap">
        delete from ${platSchema}.XS_USER_GROUP_MEMBER where
        PARENT_ID in (
        select
        b.USER_GROUP_ID
        from ${platSchema}.TXSOG01 a
        join ${platSchema}.TXSOG03 b on a.ORG_ID = b.ORG_ID
        where a.ORG_ENAME = #orgEname#
        ) or MEMBER_ID in (
        select
        b.USER_GROUP_ID
        from ${platSchema}.TXSOG01 a
        join ${platSchema}.TXSOG03 b on a.ORG_ID = b.ORG_ID
        where a.ORG_ENAME = #orgEname#
        )
    </delete>

    <delete id="clearOrgRole" parameterClass="java.util.HashMap">
        delete from ${platSchema}.XS_USER_GROUP where ID in (
        select
        b.USER_GROUP_ID
        from ${platSchema}.TXSOG01 a
        join ${platSchema}.TXSOG03 b
        on a.ORG_ID = b.ORG_ID
        where a.ORG_ENAME = #orgEname#
        )
    </delete>


    <delete id="clearOrgRoleRela" parameterClass="java.util.HashMap">
        delete from ${platSchema}.TXSOG03 where ORG_ID in (
        select ORG_ID from ${platSchema}.TXSOG01 a where a.ORG_ENAME = #orgEname#
        )
    </delete>
</sqlMap>