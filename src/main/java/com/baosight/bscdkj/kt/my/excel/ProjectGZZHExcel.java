package com.baosight.bscdkj.kt.my.excel;

import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Getter;
import lombok.Setter;

import java.math.BigDecimal;


/**
 * 综合查询导出excel
 * <AUTHOR>
 *
 */
@Getter
@Setter
public class ProjectGZZHExcel{	
	/** 流水号 */
    @Excel(name = "流水号")
    private String projectNo;

    /** 项目名称 */
    @Excel(name = "项目名称", width = 40)
    private String projectName;

    /** 项目编号 */
    @Excel(name = "项目编号", width = 10)
    private String projectNum;

    /** 项目状态 */
    @Excel(name = "项目状态", width = 10)
    private String projectStatus;

    /** 贸易对象 */
    @Excel(name = "贸易对象", width = 20)
    private String projectTradeunit;

    /** 项目性质 */
    @Excel(name = "项目性质", width = 10)
    private String projectKind;

    /** 合同执行单位 */
    @Excel(name = "合同执行单位")
    private String projectDeptCode;

    /** 合同执行单位名称 */
    @Excel(name = "合同执行单位名称", width = 50)
    private String projectDeptName;

    /** 合同执行单位PATH */
    @Excel(name = "合同执行单位PATH")
    private String projectDeptCodePath;

    /** 项目管理单位 */
    @Excel(name = "项目管理单位")
    private String projectXmglCode;

    /** 项目管理单位 */
    @Excel(name = "项目管理单位", width = 40)
    private String projectXmglName;

    /** 项目管理单位 */
    @Excel(name = "项目管理单位")
    private String projectXmglCodePath;

    /** 参加单位 */
    @Excel(name = "参加单位")
    private String projectCjCode;

    /** 参加单位名称 */
    @Excel(name = "参加单位名称")
    private String projectCjName;

    /** 项目负责人工号 */
    @Excel(name = "项目负责人工号")
    private String projectFzrCode;

    /** 项目负责人名称 */
    @Excel(name = "项目负责人名称", width = 20)
    private String projectFzrName;

    /** 项目负责人联系电话 */
    @Excel(name = "项目负责人联系电话")
    private String projectFzrTel;

    /** 项目主管工号 */
    @Excel(name = "项目主管工号")
    private String projectXmzgCode;

    /** 项目主管名称 */
    @Excel(name = "项目主管名称", width = 40)
    private String projectXmzgName;

    /** 项目主管联系电话 */
    @Excel(name = "项目主管联系电话")
    private String projectXmzgTel;

    /** E-Mail */
    @Excel(name = "E-Mail")
    private String projectXmzgMail;

    /** 知识产权利用情况 */
    @Excel(name = "知识产权利用情况")
    private String projectIsInfo;

    /** 涉外项目 */
    @Excel(name = "涉外项目")
    private String projectCwxm;

    /** 快速项目 */
    @Excel(name = "快速项目", width = 10)
    private String projectKsxm;

    /** 项目形式 */
    @Excel(name = "项目形式", width = 10)
    private String projectMethod;

    /** 是否关键技术 */
    @Excel(name = "是否关键技术", width = 10)
    private String projectIsTech;

    /** 评审组组长工号 */
    @Excel(name = "评审组组长工号")
    private String projectZzCode;

    /** 立项来源 */
    @Excel(name = "立项来源")
    private String projectSource;

    /** 提出人工号 */
    @Excel(name = "提出人工号")
    private String projectTcrUserCode;

    /** 提出人名称 */
    @Excel(name = "提出人名称")
    private String projectTcrUserName;

    /** 提出人日期 */
    @Excel(name = "提出人日期")
    private String projectTcrDate;

    /** 项目申请_贸易主管审批提交 */
    @Excel(name = "项目申请_贸易主管审批提交")
    private String projectSpDate;
    
    /** 合同录入提交生成合同编号 */
    @Excel(name = "合同编号", width = 10)
    private String skHtbh;

    /** 合同名称 */
    @Excel(name = "合同名称", width = 40)
    private String skHtname;
    
    /** 合同额（元） */
    @Excel(name = "合同额（元）", width = 10)
    private BigDecimal skContractTotal;
    
    /** 合同起始日期 */
    @Excel(name = "合同起始日期", width = 10)
    private String skStartDate;

    /** 合同终止日期 */
    @Excel(name = "合同终止日期", width = 10)
    private String skEndDate;
	
}
