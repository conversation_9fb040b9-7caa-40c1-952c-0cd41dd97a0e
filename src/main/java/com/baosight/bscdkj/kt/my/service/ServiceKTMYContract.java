package com.baosight.bscdkj.kt.my.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bscdkj.kt.my.business.*;
import com.baosight.bscdkj.kt.my.vo.CollectionContractVO;
import com.baosight.bscdkj.kt.my.vo.ContractVO;
import com.baosight.bscdkj.kt.my.vo.ProjectApplyVO;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.kt.domain.*;
import com.baosight.bscdkj.common.service.PageService;
import com.baosight.bscdkj.kt.my.business.*;
import com.baosight.bscdkj.kt.my.enums.EnumKTMYContractStatus;
import com.baosight.bscdkj.kt.my.enums.EnumKTMYMemberType;
import com.baosight.bscdkj.kt.my.enums.EnumKTMYSKContractStatus;
import com.baosight.bscdkj.kt.my.enums.KTMYConstant;
import com.baosight.bscdkj.kt.my.utils.KTMYCommonUtil;
import com.baosight.bscdkj.mp.ty.utils.ClobUtil;
import com.baosight.bscdkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目合同Service接口
 *
 * <AUTHOR>
 * @date 2022-04-26
 */
@Service
public class ServiceKTMYContract extends PageService {
    Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private BusinessKTMYContract businessContract;
    @Autowired
    private BusinessKTMYProerty businessKTMYProerty;
    @Autowired
    private BusinessKTMYBudget businessKTMYBudget;
    @Autowired
    private BusinessKTMYProject businessProject;
    @Autowired
    private BusinessKTMYProjectMember businessKTMYProjectMember;
    @Autowired
    private BusinessKTMYCollectionContract businessKTMYCollectionContract;
    @Autowired
    private BusinessKTMYCollectionPeople businessKTMYCollectionPeople;
    @Autowired
    private BusinessKTMYCollectionInfo businessKTMYCollectionInfo;

    /**
     * 初始化
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            TktmyContract bean = businessContract.initBean(loginName, null);
            inInfo.set("contract", bean);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TktmyContract> queryList = businessContract.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo page(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);

            TableDataInfo queryPage = businessContract.queryPage(queryData);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo save(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();

            //保存业务数据
            ProjectApplyVO projectApplyVO = (ProjectApplyVO) inInfo.get("projectApplyVO");
            save(operator, projectApplyVO);

            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo load(EiInfo inInfo) {
        try {
            String htId = (String) inInfo.get("htId");
            TktmyContract query = businessContract.query(htId);
            inInfo.set("contract", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String htId = (String) inInfo.get("htId");
            if (StrUtil.isNotBlank(htId)) {
                for (String id : htId.split(",")) {
                    businessContract.delete(loginName, id);
                }
            }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryDetail(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();

            TktmyContract contract = null;
            String htId = (String) inInfo.get("htId");
            if (StringUtils.isEmpty(htId)) {
                String projectId = (String) inInfo.get("projectId");
                contract = businessContract.queryMainByProjectId(operator, projectId);
            } else {
                contract = businessContract.query(htId);
            }
            if (null == contract) {
                throw new PlatException("找不到对应合同");
            }
            ContractVO contractVO = ContractVO.initParent(contract);

            //产权信息
            List<TktmyProerty> proertyList = businessKTMYProerty.queryByHtId(operator, contract.getHtId());
            if (ObjectUtil.isEmpty(proertyList)) {
                proertyList = businessKTMYProerty.initList(operator, contract.getHtId());
            }
            contractVO.setProertyList(proertyList);
            //经费预算
            List<TktmyBudget> budgetList = businessKTMYBudget.queryByHtId(operator, contract.getHtId());
            if (ObjectUtil.isEmpty(budgetList)) {
                budgetList = businessKTMYBudget.initList(operator, contract.getHtId());
            }
            contractVO.setBudgetList(budgetList);

            //合同相关富文本
            Map<String, Object> htRichText = ClobUtil.getContentMap(contractVO.getHtId());
            contractVO.setHtRichText(htRichText);

            inInfo.set("contractVO", contractVO);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 合同信息列表
     *
     * @param inInfo
     * @return
     */
    public EiInfo pageHT(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);

            TableDataInfo queryPage = businessContract.pageHT(queryData);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 提交或者启动流程
     * 分包合同
     *
     * @param inInfo
     * @return
     */
    public EiInfo doSubmit(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            ProjectApplyVO projectApplyVO = (ProjectApplyVO) inInfo.get("projectApplyVO");

            //保存业务数据
            save(operator, projectApplyVO);

            //流程                  
            String msg = "";
            WorkFlow workFlow = valdateWFNode(operator, projectApplyVO);
            if (EnumKTMYContractStatus.TEMP_TERMINAITION.equals(workFlow.getFlowState())) {//流程终止
                //合同终止
                businessContract.htEnd(operator, workFlow.getBusinessId());
                //流程终止
                msg = SWorkFlowUtil.endProecess(operator, workFlow);
            } else {
                if (StringUtils.isBlank(workFlow.getTaskId())) {//启动流程
                    //当前登录人是项目主管
                    TktmyProject project = businessProject.query(projectApplyVO.getProjectId());
                    if (!operator.equals(project.getProjectXmzgCode())) {
                        throw new PlatException("当前登录人不是项目主管");
                    }
                    //启动流程并跳转 HTSP_XMZG1 合同审批_项目主管
                    if (null == workFlow.getVariable()) {
                        workFlow.setVariable(new HashMap<>());
                    }
                    TktmyContract contract = businessContract.query(workFlow.getBusinessId());
                    workFlow.getVariable().put("isFbht", contract.getIsFbht());
                    workFlow.setBusinessName(contract.getProjectName());
                    msg = SWorkFlowUtil.startProcess(operator, workFlow);
                    businessProject.setActived(operator, projectApplyVO.getProjectId());//项目置为流转中
                    businessContract.setActived(operator, projectApplyVO.getProjectId());//合同置为流转中
                } else {//提交流程
                    msg = SWorkFlowUtil.submit(operator, workFlow);
                }
            }
            //合同流程完成
            if (msg.indexOf("结束") >= 0 || msg.indexOf("运行中") >= 0) {
                businessContract.htRun(operator, workFlow.getBusinessId());
                msg = "运行中";
            }

            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 退回
     *
     * @param inInfo
     * @return
     */
    public EiInfo doReturn(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();

            //保存业务数据
            ProjectApplyVO projectApplyVO = (ProjectApplyVO) inInfo.get("projectApplyVO");
            save(operator, projectApplyVO);

            //流程
            String msg = SWorkFlowUtil.doReturn(operator, projectApplyVO.getWorkFlow());

            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 查看待办详细
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryDBDetail(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();

            String businessId = inInfo.getString("businessId");

            TktmyContract contract = businessContract.query(businessId);
            if (null == contract) {//分包合同
                contract = businessContract.queryDraftByProjectId(operator, businessId);//先找对应的草稿合同
                if (null == contract) {
                    contract = businessContract.initBean(operator, businessId);//businessId为projectId
                    contract.setIsFbht("1");//分包合同
                }
                businessId = contract.getHtId();
            }

            TktmyProject project = businessProject.query(contract.getProjectId());
            ProjectApplyVO projectApplyVO = ProjectApplyVO.initParent(project);
            if (StringUtils.isEmpty(contract.getProjectTradeunit())) {
                contract.setProjectName(project.getProjectName());
                contract.setProjectTradeunit(project.getProjectTradeunit());
                contract.setProjectKind(project.getProjectKind());
            }

            //流程属性
            String taskId = inInfo.getString("taskId");
            WorkFlow workFlow;
            if (StringUtils.isBlank(taskId)) {
                workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(businessId);
                if (null == workFlow) {
                    workFlow = new WorkFlow(operator, KTMYConstant.PROCESS_KEY_CONTRACT_FBHT, KTMYConstant.BUSINESS_TYPE, contract.getHtId());
                }
            } else {
                workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
            }
            projectApplyVO.setWorkFlow(workFlow);

            projectApplyVO = queryDBDetail(operator, projectApplyVO, contract);

            inInfo.set("projectApplyVO", projectApplyVO);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 根据节点查询相关页面需要的信息
     *
     * @param operator
     * @param projectApplyVO
     * @return
     */
    private ProjectApplyVO queryDBDetail(String operator, ProjectApplyVO projectApplyVO, TktmyContract contract) {
        String projectId = projectApplyVO.getProjectId();
        //流程属性
        WorkFlow workFlow = projectApplyVO.getWorkFlow();
        if (StringUtils.isEmpty(projectId)) {
            projectId = contract.getProjectId();
        }

        if (StringUtils.isEmpty(workFlow.getCurrentActivity()) ||
                workFlow.getCurrentActivity().indexOf("HTSP") >= 0) {//合同审批阶段
            //合同VO
            ContractVO contractVO = new ContractVO();
            contractVO = ContractVO.initParent(contract);
            //产权信息
            List<TktmyProerty> proertyList = businessKTMYProerty.queryByHtId(operator, contract.getHtId());
            if (ObjectUtil.isEmpty(proertyList)) {
                proertyList = businessKTMYProerty.initList(operator, contract.getHtId());
            }
            contractVO.setProertyList(proertyList);
            //经费预算
            List<TktmyBudget> budgetList = businessKTMYBudget.queryByHtId(operator, contract.getHtId());
            if (ObjectUtil.isEmpty(budgetList)) {
                budgetList = businessKTMYBudget.initList(operator, contract.getHtId());
            }
            contractVO.setBudgetList(budgetList);

            //合同相关富文本
            contractVO.setHtRichText(ClobUtil.getContentMap(contractVO.getHtId()));

            //评审基本信息
            if ("HTSP_XMZG1".equals(workFlow.getCurrentActivity())) {
                contractVO.setPsBaseInfo(KTMYCommonUtil.getFbPsBaseInfo("ktmy_htzjps", contractVO.getHtId(), projectApplyVO, contract));
            }

            projectApplyVO.setContractVO(contractVO);

            if ("HTSP_XMZG1".equals(workFlow.getCurrentActivity())) {
                TktmyProjectMember projectMember = businessKTMYProjectMember.queryBySource(contractVO.getHtId(), EnumKTMYMemberType.USER_HTSP_ZJPS, operator);
                if (null != projectMember) {
                    projectApplyVO.setPageNo("KTMYHT03");//专家评审跳特定画面
                }
            }
        } else if (workFlow.getCurrentActivity().indexOf("HTLR") >= 0) {//合同录入-收款合同
            //合同VO
            ContractVO contractVO = ContractVO.initParent(contract);
            //合同收款
            TktmyCollectionContract collectionContract = businessKTMYCollectionContract.queryByHtId(operator, contract.getHtId());
            if (null == collectionContract) {
                collectionContract = businessKTMYCollectionContract.initBean(operator, contract.getHtId(), contract.getProjectId());
                collectionContract.setSkHtname(projectApplyVO.getProjectName());
            }
            CollectionContractVO collectionContractVO = CollectionContractVO.initParent(collectionContract);

            //收款合同富文本
            Map<String,Object> richText = ClobUtil.getContentMap(collectionContract.getSkId());
            if (richText==null) {
                richText = new HashMap<>();
                //获取合同相关的富文本，带入到收款合同
                Map<String,Object> htRichText = ClobUtil.getContentMap(contract.getHtId());
                richText.put("htbd", htRichText.get("mydxqk"));//合同标的
                richText.put("htgs", htRichText.get("htxynr"));//合同概述
            }
            collectionContractVO.setRichText(richText);

            //收款合同用户
            collectionContractVO.setCollectionPeople(businessKTMYCollectionPeople.queryBySkId(collectionContract.getSkId()));

            //初始化客商相关信息
            collectionContract = KTMYCommonUtil.getKsInfo(operator, contract.getProjectTradeunit(), collectionContractVO);

            //计划收款信息对象
            List<TktmyCollectionInfo> collectionInfoList = businessKTMYCollectionInfo.queryBySkId(collectionContract.getSkId());
            if (ObjectUtil.isEmpty(collectionInfoList)) {
                collectionInfoList.add(businessKTMYCollectionInfo.initBean(operator, collectionContractVO.getSkId()));
            }
            collectionContractVO.setCollectionInfoList(collectionInfoList);

            contractVO.setCollectionContractVO(collectionContractVO);
            projectApplyVO.setContractVO(contractVO);
        }
        return projectApplyVO;
    }

    private void save(String operator, ProjectApplyVO projectApplyVO) {
        WorkFlow workFlow = projectApplyVO.getWorkFlow();
        if (StringUtils.isEmpty(workFlow.getCurrentActivity()) ||
                workFlow.getCurrentActivity().indexOf("HTSP") >= 0) {//合同审批阶段
            //对应合同
            if (null != projectApplyVO.getContractVO()) {
                //保存合同数据
                TktmyContract contract = businessContract.save(operator, projectApplyVO.getContractVO());

                //合同相关附件
                String attachmentId_HTXG = projectApplyVO.getContractVO().getAttachmentId_HTXG();
                if (StringUtils.isNotEmpty(attachmentId_HTXG)) {
                    SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(contract.getHtId(), attachmentId_HTXG, KTMYConstant.BUSINESS_TYPE, "HTXG", null, null);
                }

                //定价相关附件
                String attachmentId_DJXG = projectApplyVO.getContractVO().getAttachmentId_DJXG();
                if (StringUtils.isNotEmpty(attachmentId_DJXG)) {
                    SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(contract.getHtId(), attachmentId_DJXG, KTMYConstant.BUSINESS_TYPE, "DJXG", null, null);
                }

                //合同相关富文本
                Map<String, Object> htRichText = projectApplyVO.getContractVO().getHtRichText();
                if (null != htRichText) {
                    //保存富文本
                    ClobUtil.addOrUpdateadd(htRichText, contract.getHtId(), TktmyContract.class.toString(), "KTMY", operator);
                }

                //经费预算
                if (null != projectApplyVO.getContractVO().getProertyList()) {
                    List<TktmyBudget> budgetList = businessKTMYBudget.saveList(operator, contract.getHtId(), projectApplyVO.getContractVO().getBudgetList());
                    BigDecimal ysSumTotal = businessKTMYBudget.getHtTotal(budgetList);
                    contract.setYsSumTotal(ysSumTotal);//经费预算总额
                }

                //产权信息
                if (null != projectApplyVO.getContractVO().getProertyList()) {
                    businessKTMYProerty.saveList(operator, contract.getHtId(), projectApplyVO.getContractVO().getProertyList());
                }
            }
        } else if (workFlow.getCurrentActivity().indexOf("HTLR") >= 0) {//合同录入阶段
            //对应合同收款
            if (null != projectApplyVO.getContractVO()) {
                CollectionContractVO collectionContractVO = projectApplyVO.getContractVO().getCollectionContractVO();
                if (null != collectionContractVO) {
                    businessKTMYCollectionContract.save(operator, collectionContractVO);

                    TktmyCollectionPeople collectionPeople = collectionContractVO.getCollectionPeople();
                    if (null != collectionPeople) {
                        collectionPeople.setSkId(collectionContractVO.getSkId());
                        businessKTMYCollectionPeople.save(operator, collectionPeople);
                    }

                    if (null != collectionContractVO.getCollectionInfoList()) {
                        businessKTMYCollectionInfo.saveList(operator, collectionContractVO.getSkId(), collectionContractVO.getCollectionInfoList());
                    }

                    //合同收款附件
                    String attachmentId_HTSK = collectionContractVO.getAttachmentId_HTSK();
                    if (null != attachmentId_HTSK) {
                        SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(collectionContractVO.getSkId(), attachmentId_HTSK, KTMYConstant.BUSINESS_TYPE, "HTSK", null, null);
                    }

                    //收款合同相关富文本
                    Map<String, Object> richText = collectionContractVO.getRichText();
                    if (null != richText) {
                        //保存富文本
                        ClobUtil.addOrUpdateadd(richText, collectionContractVO.getSkId(), TktmyCollectionContract.class.toString(), "KTMY", operator);
                    }
                }
            }
        }

        //暂存流程意见
        if (null != workFlow) {
            if (StringUtils.isNotBlank(workFlow.getTaskId()) && StringUtils.isNotBlank(workFlow.getComment())) {
                SWorkFlowUtil.updateComment(workFlow.getTaskId(), workFlow.getComment());
            }
        }
    }

    /**
     * 关键节点的处理及验证
     *
     * @param operator
     * @param projectApplyVO
     * @return
     */
    private WorkFlow valdateWFNode(String operator, ProjectApplyVO projectApplyVO) {
        String projectId = projectApplyVO.getProjectId();
        WorkFlow workFlow = projectApplyVO.getWorkFlow();
        String htId = workFlow.getBusinessId();
        Map<String, Object> variable = projectApplyVO.getWorkFlow().getVariable();
        if (null == variable) {
            variable = new HashMap<>();
        }
        if (StringUtils.isEmpty(projectId)) {
            projectId = businessContract.query(htId).getProjectId();
        }
        if (StringUtils.isEmpty(workFlow.getCurrentActivity())) {
            TktmyProject project = businessProject.query(projectId);
            variable.put("projectFzrCode", project.getProjectFzrCode());//项目负责人
            variable.put("projectXmglCode", project.getProjectXmglCode());//项目管理单位
            variable.put("projectDeptCode", project.getProjectDeptCode());//项目执行单位
            variable.put("projectXmzgCode", project.getProjectXmzgCode());//项目主管工号
        } else if ("HTSP_MYZG".equals(workFlow.getCurrentActivity())//合同审批_贸易主管
                || "HTSP_ZGBMLD".equals(workFlow.getCurrentActivity())) {//合同审批_主管部门领导  需要传入合同类型和合同金额
            TktmyContract contract = businessContract.query(workFlow.getBusinessId());
            if (null == contract.getInternalPriceFirst()) {
                throw new PlatException("内部价格范围(万元)不能为空!");
            }
            if (null == contract.getIsFbht()) {
                throw new PlatException("是否分包合同不能为空!");
            }
            variable.put("internalPriceFirst", contract.getInternalPriceFirst().intValue());//内部价格范围(万元)
            variable.put("isFbht", contract.getIsFbht());//是否分包合同
        } else if ("HTSP_XMZG1".equals(workFlow.getCurrentActivity())) {//合同审批表_项目主管
            TktmyContract contract = businessContract.query(workFlow.getBusinessId());
    		/*
    		boolean isHtspZjps = false;
			if("HTSP_XMZG1".equals(workFlow.getCurrentActivity())) {
				TktmyProjectMember projectMember = businessKTMYProjectMember.queryBySource(contract.getHtId(), EnumKTMYMemberType.USER_HTSP_ZJPS, operator);
				if(null!=projectMember) {
					isHtspZjps = true;
				}
			}
    		if(!isHtspZjps) {//不是专家评审
        		//验证专家评审是否结束
    			List<TktmyProjectMember> projectMemberList = businessKTMYProjectMember.queryBySource(contract.getHtId(), EnumKTMYMemberType.USER_HTSP_ZJPS);
    			if(ObjectUtil.isEmpty(projectMemberList)) {
    				throw new PlatException("请选择专家");
    			}
    			String wpsUser = projectMemberList.stream()
    					.filter(projectMember -> EnumKTMYMemberStatus.WPS.equals(projectMember.getSourceStatus()))
    					.map(projectMember -> projectMember.getSourceName())
    					.collect(Collectors.joining(","));
    			if(StringUtils.isNotEmpty(wpsUser)) {
    				throw new PlatException(wpsUser+"未评审");
    			}       		
        		//合同置为流转中
        		businessContract.setActived(operator, projectApplyVO.getProjectId());//合同置为流转中
    		}
    		*/
            KTMYCommonUtil.checkPs("ktmy_htzjps", contract.getHtId());

            //合同置为流转中
            businessContract.setActived(operator, projectApplyVO.getProjectId());//合同置为流转中

            TktmyProject project = businessProject.query(projectId);
            variable.put("projectFzrCode", project.getProjectFzrCode());//项目负责人
            variable.put("projectXmglCode", project.getProjectXmglCode());//项目管理单位
            variable.put("projectDeptCode", project.getProjectDeptCode());//项目执行单位
            variable.put("projectXmzgCode", project.getProjectXmzgCode());//项目主管工号
        } else if ("HTLR_XMZG".equals(workFlow.getCurrentActivity())) {//合同录入_项目主管
            String skId = projectApplyVO.getContractVO().getCollectionContractVO().getSkId();

            //生成分包合同编号
            businessKTMYCollectionContract.genFBHTBH(operator, skId);

            //状态设置为未备案
            businessKTMYCollectionContract.changeStatus(operator, skId, EnumKTMYSKContractStatus.RUN_WBA, "");
        }

        workFlow.setVariable(variable);
        return workFlow;
    }

    /**
     * 合同审批专家评审
     * 专家开始评审 回调
     * S_KT_MY_C0
     *
     * @param inInfo
     * @return
     */
    public EiInfo htspStartCallback(EiInfo inInfo) {
        try {
            String operator = (String) inInfo.get("operator");
            if (StringUtils.isEmpty(operator)) {
                operator = "auto";
            }
            Object bean = inInfo.get("reviewReq");
            Map<String, Object> reviewReq = BeanUtil.beanToMap(bean);
            String htId = (String) reviewReq.get("bizId");

            TktmyContract contract = businessContract.query(htId);
            if (null != contract) {
                WorkFlow workFlow = new WorkFlow();
                workFlow.setOperator(operator);
                if ("1".equals(contract.getIsFbht())) {//分包合同 业务ID为合同id
                    workFlow.setBusinessId(contract.getHtId());
                } else {//不是分包合同 业务ID为项目ID
                    workFlow.setBusinessId(contract.getProjectId());
                }
                workFlow.setCurrentActivityName("合同审批(评审中)");
                SWorkFlowUtil.updateFlowInfoByBusinessId(operator, workFlow);
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            } else {
                inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 合同审批专家评审
     * 专家结束评审 回调
     * S_KT_MY_C1
     *
     * @param inInfo
     * @return
     */
    public EiInfo htspEndCallback(EiInfo inInfo) {
        try {
            String operator = (String) inInfo.get("operator");
            if (StringUtils.isEmpty(operator)) {
                operator = "auto";
            }
            Object bean = inInfo.get("reviewReq");
            Map<String, Object> reviewReq = BeanUtil.beanToMap(bean);
            String htId = (String) reviewReq.get("bizId");

            TktmyContract contract = businessContract.query(htId);
            if (null != contract) {
                WorkFlow workFlow = new WorkFlow();
                workFlow.setOperator(operator);
                if ("1".equals(contract.getIsFbht())) {//分包合同 业务ID为合同id
                    workFlow.setBusinessId(contract.getHtId());
                } else {//不是分包合同 业务ID为项目ID
                    workFlow.setBusinessId(contract.getProjectId());
                }
                workFlow.setCurrentActivityName("合同审批(评审完成)");
                SWorkFlowUtil.updateFlowInfoByBusinessId(operator, workFlow);
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            } else {
                inInfo.setStatus(EiConstant.STATUS_DEFAULT);
            }
        } catch (Exception e) {
            e.printStackTrace();
            //inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setStatus(EiConstant.STATUS_DEFAULT);//回调不成功不影响流程
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }
}
