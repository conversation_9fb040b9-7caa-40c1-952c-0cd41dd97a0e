package com.baosight.bscdkj.kt.my.controller;

import com.baosight.bscdkj.common.controller.BaseController;
import com.baosight.bscdkj.common.domain.AjaxResult;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.kt.domain.TktmyRewardNotice;
import com.baosight.bscdkj.utils.excel.ExcelUtil;
import com.baosight.bscdkj.utils.iplat.ServiceUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 奖励通知单Controller
 * 
 * <AUTHOR>
 * @date 2022-04-13
 */
@Controller
@RequestMapping("/ktmy/rewardNotice")
public class ControllerKTMYRewardNotice extends BaseController{
    private String prefix = "/ktmy/rewardNotice";
    
    @GetMapping()
    public String rewardNotice(){
        return prefix + "/rewardNoticeList";
    }
    
    /**
  	 * 通用跳转页面
  	 * 
  	 * @param pageNo
  	 * @param mmap
  	 * @return
  	 */
  	@GetMapping("/toPage/{pageNo}")
  	public String toPage(@PathVariable("pageNo") String pageNo, ModelMap model) {
  		model.addAllAttributes(paramMap());// 获取url中的参数赋值给ModelMap
  		return prefix +"/" + pageNo;
  	}

     /**
     * 查询奖励通知单列表
     */
  	@PostMapping("/list")
	@ResponseBody
	public TableDataInfo list(@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKTMYRewardNotice", "page");
		return getDataTable(query);
	}

    /**
     * 新增奖励通知单
     */
    @GetMapping("/add")
    public String add(ModelMap modelMap){
        modelMap.put("rewardNotice", ServiceUtil.xLocalManagerToData(new EiInfo(), "ServiceKTMYRewardNotice", "initLoad", "rewardNotice"));
        return prefix + "/rewardNotice";
    }

    /**
     * 修改奖励通知单
     */
    @GetMapping("/edit/{noticeId}")
    public String edit(@PathVariable("noticeId") String noticeId, ModelMap modelMap) {
       	EiInfo eiInfo = new EiInfo();
		eiInfo.set("noticeId", noticeId);

        modelMap.put("rewardNotice", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKTMYRewardNotice", "load", "rewardNotice"));
        return prefix + "/rewardNotice";
    }

    /**
     * 保存奖励通知单
     */
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult save(@Validated TktmyRewardNotice rewardNotice){
        EiInfo eiInfo = getEiInfo("i", rewardNotice);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKTMYRewardNotice", "save");
    }
    
    /**
     * 删除奖励通知单
     */
	@PostMapping("/remove")
	@ResponseBody
	public AjaxResult remove(String ids) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("noticeId", ids);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKTMYRewardNotice", "remove");
	}

    /**
     * 导出奖励通知单列表
     */
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		List<TktmyRewardNotice> list = (List<TktmyRewardNotice>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKTMYRewardNotice", "query", "list");
		ExcelUtil<TktmyRewardNotice> util = new ExcelUtil<>(TktmyRewardNotice.class);
		util.setSheet("奖励通知单");
		return util.exportExcel(list);
    }
    
	/**
	 * 
	 * 项目主管相关的奖励分配
	 * @param map
	 * @return
	 */
	@PostMapping("/pageJLFP")
	@ResponseBody
	public TableDataInfo pageJLFP(@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKTMYRewardNotice", "pageJLFP");
		return getDataTable(query);
	}
	
    /**
     * 奖励分配
     */
	@PostMapping("/jlfp/{projectId}")
	@ResponseBody
	public AjaxResult jlfp(@PathVariable("projectId") String projectId) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("projectId", projectId);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKTMYRewardNotice", "jlfp");
	}
	
    /**
     * 奖励分配详细
     * @param pageNo 页面号
     * @param projectId 项目ID
     * @param model
     * @return
     */
	@GetMapping("/queryDetailByProjectId/{pageNo}/{projectId}")
	public String queryDetailByProjectId(@PathVariable("pageNo") String pageNo, @PathVariable("projectId") String projectId, ModelMap model) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("projectId", projectId);
		model.put("conclusionVO", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKTMYRewardNotice", "queryDetail", "conclusionVO"));
		return prefix + "/" + pageNo;
	}
}
