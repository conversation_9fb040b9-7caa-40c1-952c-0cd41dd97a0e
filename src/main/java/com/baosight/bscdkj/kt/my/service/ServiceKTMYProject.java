package com.baosight.bscdkj.kt.my.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.bean.copier.CopyOptions;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.kt.domain.*;
import com.baosight.bscdkj.common.ky.domain.TkysbTradeContract;
import com.baosight.bscdkj.common.service.PageService;
import com.baosight.bscdkj.kt.my.business.*;
import com.baosight.bscdkj.kt.my.enums.EnumKTMYMemberStatus;
import com.baosight.bscdkj.kt.my.enums.EnumKTMYMemberType;
import com.baosight.bscdkj.kt.my.enums.EnumKTMYProcessStatus;
import com.baosight.bscdkj.kt.my.enums.KTMYConstant;
import com.baosight.bscdkj.kt.my.excel.ProjectGZZHExcel;
import com.baosight.bscdkj.kt.my.utils.KTMYCommonUtil;
import com.baosight.bscdkj.kt.my.vo.*;
import com.baosight.bscdkj.mp.ad.dto.ADOrg;
import com.baosight.bscdkj.mp.ad.utils.OrgUtil;
import com.baosight.bscdkj.mp.ad.utils.RoleUtil;
import com.baosight.bscdkj.mp.ad.utils.UserUtil;
import com.baosight.bscdkj.mp.ty.prop.AppContextProp;
import com.baosight.bscdkj.mp.ty.utils.ClobUtil;
import com.baosight.bscdkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bscdkj.mp.ty.utils.SDictUtil;
import com.baosight.bscdkj.mp.wf.dto.SubProcessParam;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bscdkj.utils.BizIdUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.DateUtils;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 技术贸易项目Service接口
 * 
 * <AUTHOR>
 * @date 2022-04-26
 */
@Service
public class ServiceKTMYProject extends PageService{
    @Autowired
	private BusinessKTMYProject businessProject;
    
    @Autowired
	private BusinessKTMYTeachSchedule businessKTMYTeachSchedule;
    
    @Autowired
   	private BusinessKTMYContract businessKTMYContract;
    
    @Autowired
    private BusinessKTMYKettechJs businessKTMYKettechJs;
    
    @Autowired
    private BusinessKTMYProerty businessKTMYProerty;
    
    @Autowired
    private BusinessKTMYBudget businessKTMYBudget;
    
    @Autowired
    private BusinessKTMYProjectMember businessKTMYProjectMember;
    
    @Autowired
   	private BusinessKTMYCollectionContract businessKTMYCollectionContract;
    
    @Autowired
   	private BusinessKTMYCollectionPeople businessKTMYCollectionPeople;
    
    @Autowired
   	private BusinessKTMYCollectionInfo businessKTMYCollectionInfo;
    
    @Autowired
	private BusinessKTMYConclusion businessConclusion;
    
    @Autowired
   	private BusinessKTMYFinalMain businessFinalMain;
    
    @Autowired
   	private BusinessKTMYAccess businessAccess;
    
    @Autowired
   	private BusinessKTMYAward businessAward;
    
    @Autowired
   	private BusinessKTMYRewardNotice businessRewardNotice;
    
    @Autowired
    AppContextProp appContextProp;
    
	Logger logger = LoggerFactory.getLogger(this.getClass());
	
	/**
	 * 初始化
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
		try{
            String loginName = UserSession.getLoginName();
            TktmyProject bean = businessProject.initBean(loginName, inInfo.getString("projectMethod"));
            
            ProjectApplyVO projectApplyVO = new ProjectApplyVO();    	
	    	BeanUtil.copyProperties(bean, projectApplyVO, false);
	    	  		
	  		//流程属性
	    	WorkFlow workFlow = new WorkFlow(loginName, KTMYConstant.PROCESS_KEY_PROJCET_APPLY, KTMYConstant.BUSINESS_TYPE, projectApplyVO.getProjectId());
	    	projectApplyVO.setWorkFlow(workFlow);
            
	    	inInfo.set("projectApplyVO", projectApplyVO);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
	}
	
	@Override
	public EiInfo query(EiInfo inInfo) {
	    try{
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TktmyProject> queryList = businessProject.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	public EiInfo page(EiInfo inInfo) {
	    try{
            Map<String, Object> queryData = getQueryData(inInfo);

            TableDataInfo queryPage = businessProject.queryPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo save(EiInfo inInfo) {
		try{
	    	String operator = UserSession.getLoginName();
	    	
	    	//保存业务数据
	    	ProjectApplyVO projectApplyVO = (ProjectApplyVO)inInfo.get("projectApplyVO");
	    	save(operator, projectApplyVO);
            
            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo load(EiInfo inInfo) {
	    try{
            String projectId = (String) inInfo.get("projectId");
            TktmyProject query = businessProject.query(projectId);
            inInfo.set("project", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo remove(EiInfo inInfo) {
        try{
            String loginName = UserSession.getLoginName();
            String projectId = (String) inInfo.get("projectId");
	        if (StrUtil.isNotBlank(projectId)) {
	            for (String id : projectId.split(",")) {
	                businessProject.deleteLogin(loginName,id);
	            }
	        }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
       }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 查看待办详细
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryDBDetail(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName();
	    	
	    	String businessId = inInfo.getString("businessId");
	    	
	    	TktmyProject project = businessProject.query(businessId);	    	
	    	ProjectApplyVO projectApplyVO = ProjectApplyVO.initParent(project);	
	    	
	    	//流程属性
	    	String taskId = inInfo.getString("taskId");
	    	WorkFlow workFlow;
	    	if(StringUtils.isBlank(taskId)) {
	    		workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(businessId);
	    		if(null==workFlow) {
	    			workFlow = new WorkFlow(operator, KTMYConstant.PROCESS_KEY_PROJCET_APPLY, KTMYConstant.BUSINESS_TYPE, projectApplyVO.getProjectId());
	    		}	    		
	    	}else {
	    		workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
	    	}	    	
	    	projectApplyVO.setWorkFlow(workFlow);		
	    	
	    	projectApplyVO = queryDBDetail(operator, projectApplyVO);

            inInfo.set("projectApplyVO", projectApplyVO);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 提交或者启动流程
	 * @param inInfo
	 * @return
	 */
	public EiInfo doSubmit(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
	    	ProjectApplyVO projectApplyVO = (ProjectApplyVO)inInfo.get("projectApplyVO");
            
            //保存业务数据
	    	save(operator, projectApplyVO);
            
            //流程                  
            String msg = "";
            WorkFlow workFlow = valdateWFNode(operator, projectApplyVO);
            if(EnumKTMYProcessStatus.TEMP_TERMINAITION.equals(workFlow.getFlowState())) {//流程终止
            	//项目逻辑删除
            	businessProject.deleteLogin(operator, workFlow.getBusinessId());
            	//流程终止
            	msg = SWorkFlowUtil.endProecess(operator, workFlow);
            } else if(EnumKTMYProcessStatus.ENDED.equals(workFlow.getFlowState())) {//流程终止
				//项目置为结束
				businessProject.setEnded(operator, workFlow.getBusinessId());
				//流程结束
				msg = SWorkFlowUtil.finshProcess(operator, workFlow);
			}else {
	            if(EnumKTMYProcessStatus.RUN.equals(projectApplyVO.getProjectStatus())){//优先判断是否是分包合同
	            	//当前登录人是项目主管
	            	TktmyProject project = businessProject.query(projectApplyVO.getProjectId());
	            	if(!operator.equals(project.getProjectXmzgCode())) {
	            		throw new PlatException("当前登录人不是项目主管");	
	            	}          	
	            	//激活流程
	            	if(StringUtils.isBlank(workFlow.getTaskId())) {//获取不到taskId需要激活流程	            		
	            		SWorkFlowUtil.activeProcess(operator, new WorkFlow(operator, projectApplyVO.getProjectId()));
	                	WorkFlow workFlowRun = SWorkFlowUtil.getMainFlowInfoByBusinessId(projectApplyVO.getProjectId());
	                	workFlowRun.setVariable(workFlow.getVariable());
	                	workFlow = workFlowRun;
	            	}
	            	//设置跳转节点
	            	workFlow.setJumpActivityKey("HTSP_XMZG1");//合同审批_项目主管
	            	msg = SWorkFlowUtil.doJump(operator, workFlow);
	            	businessProject.setActived(operator, projectApplyVO.getProjectId());//项目置为流转中
	            	businessKTMYContract.setActived(operator, projectApplyVO.getProjectId());//合同置为流转中
	            } else if(StringUtils.isBlank(workFlow.getTaskId())) {//启动流程  
	            	String userLabelM = "";//预处用户
	            	SubProcessParam[] subProcessParamS = null;//询单部门
	            	String projectMethod = projectApplyVO.getProjectMethod();
	            	if("METHOD_XD".equals(projectMethod)) {
	            		subProcessParamS = KTMYCommonUtil.getUserLabel(KTMYConstant.ROLE_KEY_BM_LLY, projectApplyVO.getDeptYdyj());
	            	} else {
	            		userLabelM = projectApplyVO.getUserLxyc();
	            		if(StringUtils.isEmpty(userLabelM)) {	    		
	            			throw new PlatException("找不到对应的预处用户");
	            		}
	            	}
	            	workFlow.setBusinessName(projectApplyVO.getProjectName());//业务名称
	            	
	            	//流程处理
	            	if("METHOD_XD".equals(projectMethod)) {
	            		workFlow.setSubProcessParamS(subProcessParamS);//询单
	            	} else {
	            		workFlow.setUserLabelM(userLabelM);//预处
	            	} 
	            	//启动流程
	            	msg = SWorkFlowUtil.startProcessAndSubmit(operator, workFlow);
	            	
	            	//设置业务状态
	            	businessProject.setActived(operator, projectApplyVO.getProjectId());  
	            } else {//提交流程
	            	msg = SWorkFlowUtil.submit(operator, workFlow);
	            }
            }
            //项目到运行中状态
            if(msg.indexOf("运行中")>=0) {
            	//挂起流程
            	SWorkFlowUtil.suspendProcess(operator, new WorkFlow(operator, workFlow.getBusinessId()));
            	msg = "运行中";
            	
            	//项目置为运行中
            	businessKTMYContract.setRun(operator, workFlow.getBusinessId());
            	businessProject.setRun(operator, workFlow.getBusinessId());
            }
            
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 退回
	 * @param inInfo
	 * @return
	 */
	public EiInfo doReturn(EiInfo inInfo) {
		try{
	    	String operator = UserSession.getLoginName(); 	
	    	
            //保存业务数据
	    	ProjectApplyVO projectApplyVO = (ProjectApplyVO)inInfo.get("projectApplyVO");
	    	save(operator, projectApplyVO);
            
            //流程
            String msg = SWorkFlowUtil.doReturn(operator, projectApplyVO.getWorkFlow());

            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 跳转
	 * @param inInfo
	 * @return
	 */
	public EiInfo doJump(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            
	    	//保存业务数据
	    	ProjectApplyVO projectApplyVO = (ProjectApplyVO)inInfo.get("projectApplyVO");
	    	save(operator, projectApplyVO);
            
            //流程
            String msg = SWorkFlowUtil.doJump(operator, projectApplyVO.getWorkFlow());

            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	/**
	 * 终止流程
	 * @param inInfo
	 * @return
	 */
	public EiInfo endProecess(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
	    	
	    	//保存业务数据
	    	ProjectApplyVO projectApplyVO = (ProjectApplyVO)inInfo.get("projectApplyVO");
            save(operator, projectApplyVO);

            WorkFlow workFlow = projectApplyVO.getWorkFlow();

			//终止项目
			businessProject.setTermination(operator, workFlow.getBusinessId());//终止

			//终止流程
            String msg = SWorkFlowUtil.endProecess(operator, workFlow);

            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	private void save(String operator, ProjectApplyVO projectApplyVO) {
		WorkFlow workFlow = projectApplyVO.getWorkFlow();
		if(StringUtils.isBlank(workFlow.getCurrentActivity())||workFlow.getCurrentActivity().indexOf("XMSQ")>=0) {//项目申请阶段
			//暂存主表
			if(StringUtils.isNotEmpty(projectApplyVO.getProjectId())) {
				TktmyProject project = businessProject.save(operator, projectApplyVO);
				BeanUtil.copyProperties(project, projectApplyVO, false);
			}
			boolean isR = false;//是否是传阅 -- 一般是专家评审之类
			if("XMSQ_LXYC_PS".equals(workFlow.getCurrentActivity())) {//预处评估
				TktmyProjectMember projectMember = businessKTMYProjectMember.queryBySource(workFlow.getBusinessId(), EnumKTMYMemberType.USER_LXYC, operator);
				if(null!=projectMember) {
					projectMember.setSourceComment(workFlow.getComment());
					projectMember.setSourceTime(DateUtils.curDateTimeStr19());
					projectMember.setSourceStatus(EnumKTMYMemberStatus.YPS);
					businessKTMYProjectMember.update(operator, projectMember);
					isR = true;
				}
			}else if("XMSQ_XDBM".equals(workFlow.getCurrentActivity())) {//询单部门
				WorkFlow workFlowSub = SWorkFlowUtil.getWorkFlowByTaskId(workFlow.getTaskId());
				String departmentNo = (String)workFlowSub.getVariable().get("departmentNo");//会签组织
				if(StringUtils.isEmpty(departmentNo)) {
					throw new PlatException("找不到对应的询单部门");
				}
				TktmyProjectMember projectMember = businessKTMYProjectMember.queryBySource(workFlow.getBusinessId(), EnumKTMYMemberType.DEPT_XDYJ, departmentNo);
				if(null!=projectMember) {
					projectMember.setSourceComment(workFlow.getComment());
					projectMember.setSourceTime(DateUtils.curDateTimeStr19());
					projectMember.setSourceStatus(EnumKTMYMemberStatus.YPS);
					projectMember.setExtra1(operator);
					projectMember.setExtra2(UserUtil.getUserName(operator));
					businessKTMYProjectMember.update(operator, projectMember);
					isR = true;
					//相关富文本
					ClobUtil.addOrUpdateadd(projectApplyVO.getProjectMemberVO().getRichText(), projectMember.getRowId(), TktmyProjectMember.class.getName(), 
							KTMYConstant.BUSINESS_TYPE, operator);
					//相关附件
					SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(projectMember.getRowId(), projectApplyVO.getProjectMemberVO().getAttachmentId_XDFJ(), 
							KTMYConstant.BUSINESS_TYPE, "XDFJ", null, null);
				} else {
					throw new PlatException(departmentNo+"找不到对应的询单部门");
				}
			}
			if(!isR) {
				//知识产权利用情况
				if(StringUtils.isNotEmpty(projectApplyVO.getProjectIsInfo())) {
					businessKTMYTeachSchedule.insertList(operator, projectApplyVO.getProjectId(), projectApplyVO.getTeachScheduleList());
				}
		        //立项预处用户
		        if(StringUtils.isNotEmpty(projectApplyVO.getUserLxyc())) {
		        	businessKTMYProjectMember.deleteBySource(operator, projectApplyVO.getProjectId(), EnumKTMYMemberType.USER_LXYC);
		        	businessKTMYProjectMember.saveProjectMember(operator, projectApplyVO.getProjectId(), EnumKTMYMemberType.USER_LXYC, EnumKTMYMemberStatus.WPS, projectApplyVO.getUserLxyc());
		        }
		        //询单部门意见
		        if(StringUtils.isNotEmpty(projectApplyVO.getDeptYdyj())) {
		        	businessKTMYProjectMember.deleteBySource(operator, projectApplyVO.getProjectId(), EnumKTMYMemberType.DEPT_XDYJ);
		        	businessKTMYProjectMember.saveProjectMember(operator, projectApplyVO.getProjectId(), EnumKTMYMemberType.DEPT_XDYJ, EnumKTMYMemberStatus.WPS, projectApplyVO.getDeptYdyj());
		        }
		                
		        //相关附件
		        if(StringUtils.isNotEmpty(projectApplyVO.getProjectId())&&
		        		null!=projectApplyVO.getAttachmentId_XGFJ()) {
		        	SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(projectApplyVO.getProjectId(), projectApplyVO.getAttachmentId_XGFJ(), KTMYConstant.BUSINESS_TYPE, "XGFJ", null, null);
		        }
		        
		        //相关富文本
		        Map<String,Object> projectRichText = projectApplyVO.getProjectRichText();
		        if(ObjectUtil.isNotEmpty(projectRichText)) {
		        	ClobUtil.addOrUpdateadd(projectRichText, projectApplyVO.getProjectId(), TktmyProject.class.getName(), KTMYConstant.BUSINESS_TYPE, operator);
		        }
			}						
		} else if(workFlow.getCurrentActivity().indexOf("GJJS")>=0) {//关键技术阶段
			//关键技术
	        if(null!=projectApplyVO.getKettechJsVO()) {
	        	businessKTMYKettechJs.save(operator, projectApplyVO.getKettechJsVO());
	        	//关键技术富文本
	        	Map<String,Object> jsRichText = projectApplyVO.getKettechJsVO().getJsRichText();
	        	if(ObjectUtil.isNotEmpty(jsRichText)) {
	        		ClobUtil.addOrUpdateadd(jsRichText, projectApplyVO.getKettechJsVO().getJsGuid(), TktmyKettechJs.class.getName(), KTMYConstant.BUSINESS_TYPE, operator);
	        	}
	        }
	        if("GJJS_PS1".equals(workFlow.getCurrentActivity())||"GJJS_PS2".equals(workFlow.getCurrentActivity())) {
				TktmyProjectMember projectMember = businessKTMYProjectMember.queryBySource(projectApplyVO.getKettechJsVO().getJsGuid(), EnumKTMYMemberType.USER_GJJS, operator);
				if(null!=projectMember) {
					projectMember.setSourceComment(workFlow.getComment());
					projectMember.setSourceTime(DateUtils.curDateTimeStr19());
					projectMember.setSourceStatus(EnumKTMYMemberStatus.YPS);
					businessKTMYProjectMember.update(operator, projectMember);
				}
			}
		}else if(workFlow.getCurrentActivity().indexOf("HTSP")>=0) {//合同审批阶段
			boolean isHtspZjps = false;
			/*
			if("HTSP_XMZG1".equals(workFlow.getCurrentActivity())) {
				TktmyContract contract = businessKTMYContract.queryDraftByProjectId(operator, workFlow.getBusinessId());	
				if(null!=contract) {
					TktmyProjectMember projectMember = businessKTMYProjectMember.queryBySource(contract.getHtId(), EnumKTMYMemberType.USER_HTSP_ZJPS, operator);
					if(null!=projectMember) {
						projectMember.setSourceComment(workFlow.getComment());
						projectMember.setSourceTime(DateUtils.curDateTimeStr19());
						projectMember.setSourceStatus(EnumKTMYMemberStatus.YPS);
						businessKTMYProjectMember.update(operator, projectMember);
					}
				}	
			}
			*/
			if(!isHtspZjps) {//不是专家评审才能修改数据
				if(StringUtils.isNotEmpty(projectApplyVO.getProjectId())) {
					//暂存主表
					TktmyProject project = businessProject.save(operator, projectApplyVO);
					BeanUtil.copyProperties(project, projectApplyVO, false);	
				}
				//对应合同
		        if(null!=projectApplyVO.getContractVO()) {
		        	//保存合同数据
		        	TktmyContract contract = businessKTMYContract.save(operator, projectApplyVO.getContractVO());
		        	
		        	//合同相关附件
		        	String attachmentId_HTXG = projectApplyVO.getContractVO().getAttachmentId_HTXG();
		        	if(StringUtils.isNotEmpty(attachmentId_HTXG)) {
		        		SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(contract.getHtId(), attachmentId_HTXG, KTMYConstant.BUSINESS_TYPE, "HTXG", null, null);
		        	}
		        	
		        	//定价相关附件
		        	String attachmentId_DJXG = projectApplyVO.getContractVO().getAttachmentId_DJXG();
		        	if(StringUtils.isNotEmpty(attachmentId_DJXG)) {
		        		SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(contract.getHtId(), attachmentId_DJXG, KTMYConstant.BUSINESS_TYPE, "DJXG", null, null);
		        	}
		        	
		        	//合同相关富文本
		        	Map<String,Object> htRichText = projectApplyVO.getContractVO().getHtRichText();
		        	if(null!=htRichText) {
		        		//保存富文本
			    		ClobUtil.addOrUpdateadd(htRichText, contract.getHtId(), TktmyContract.class.toString(), "KTMY", operator);
		        	}
		        	
		        	//经费预算       	
		        	if(null!=projectApplyVO.getContractVO().getProertyList()) {
		        		List<TktmyBudget> budgetList = businessKTMYBudget.saveList(operator, contract.getHtId(), projectApplyVO.getContractVO().getBudgetList());
		        		BigDecimal ysSumTotal = businessKTMYBudget.getHtTotal(budgetList);
		        		contract.setYsSumTotal(ysSumTotal);//经费预算总额
		        	}
		        	
		        	//产权信息
		        	if(null!=projectApplyVO.getContractVO().getProertyList()) {
		        		businessKTMYProerty.saveList(operator, contract.getHtId(), projectApplyVO.getContractVO().getProertyList());
		        	}
		        }
			}
		} else if(workFlow.getCurrentActivity().indexOf("HTLR")>=0) {//合同录入阶段
			//对应合同收款
			if(null!=projectApplyVO.getContractVO()) {
				CollectionContractVO collectionContractVO = projectApplyVO.getContractVO().getCollectionContractVO();
		        if(null!=collectionContractVO) {
		        	businessKTMYCollectionContract.save(operator, collectionContractVO);
		        	
		        	TktmyCollectionPeople collectionPeople = collectionContractVO.getCollectionPeople();
		        	if(null!=collectionPeople) {
		        		collectionPeople.setSkId(collectionContractVO.getSkId());
		        		businessKTMYCollectionPeople.save(operator, collectionPeople);
		        	}
		        	
		        	if(null!=collectionContractVO.getCollectionInfoList()) {
		        		businessKTMYCollectionInfo.saveList(operator, collectionContractVO.getSkId(), collectionContractVO.getCollectionInfoList());
		        	}
		        	
		        	//合同收款附件
		        	String getAttachmentId_HTSK = collectionContractVO.getAttachmentId_HTSK();
		        	if(null != getAttachmentId_HTSK) {
		        		SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(collectionContractVO.getSkId(), getAttachmentId_HTSK, KTMYConstant.BUSINESS_TYPE, "HTSK", null, null);
		        	}
		        	
		        	//收款合同相关富文本
		        	Map<String,Object> richText = collectionContractVO.getRichText();
		        	if(null!=richText) {
		        		//保存富文本
			    		ClobUtil.addOrUpdateadd(richText, collectionContractVO.getSkId(), TktmyCollectionContract.class.toString(), "KTMY", operator);
		        	}
		        }
			}
		}

        //暂存流程意见
        if(null!=workFlow) {
        	if(StringUtils.isNotBlank(workFlow.getTaskId())&&StringUtils.isNotBlank(workFlow.getComment())) {
        		SWorkFlowUtil.updateComment(workFlow.getTaskId(), workFlow.getComment());
        	}
        }
	}
	
	/**
	 * 关键节点的处理及验证
	 * @param project
	 * @param workFlow
	 * @return
	 */
	private WorkFlow valdateWFNode(String operator, ProjectApplyVO projectApplyVO) {
		String projectId = projectApplyVO.getProjectId();
		WorkFlow workFlow = projectApplyVO.getWorkFlow();
		Map<String,Object> variable = workFlow.getVariable();
    	if(null==variable) {
    		variable = new HashMap<>();
    	}
    	if(StringUtils.isEmpty(projectId)) {
    		projectId = workFlow.getBusinessId();
    	}
		if(StringUtils.isBlank(workFlow.getTaskId())) {//项目申请节点
			TktmyProject project = businessProject.query(projectId);
			if(null==project.getProjectMethod()) {
				throw new PlatException("项目形式不能为空!");
			}		
			variable.put("projectMethod", project.getProjectMethod());//项目形式
			if("METHOD_XD".equals(project.getProjectMethod())) {
				if (StringUtils.isBlank(project.getProjectXmzgCode()) ){
					variable.put("projectXmzgCode", operator);//询单时，当前操作人暂时设置为项目主管
				} else {
					variable.put("projectXmzgCode", project.getProjectXmzgCode());//询单时，当前操作人暂时设置为项目主管
				}
			} else {
				variable.put("projectXmzgCode", project.getProjectXmzgCode());//项目主管工号
			}
		} else if ("XMSQ_XMZG_SQ".equals(workFlow.getCurrentActivity())) {			
			String userLabelM = "";//预处用户
        	SubProcessParam[] subProcessParamS = null;//询单部门
        	String projectMethod = projectApplyVO.getProjectMethod();
        	if("METHOD_XD".equals(projectMethod)) {
        		subProcessParamS = KTMYCommonUtil.getUserLabel(KTMYConstant.ROLE_KEY_BM_LLY, projectApplyVO.getDeptYdyj());
        	} else {
        		userLabelM = projectApplyVO.getUserLxyc();
        		if(StringUtils.isEmpty(userLabelM)) {	    		
        			throw new PlatException("找不到对应的预处用户");
        		}
        	}
        	workFlow.setBusinessName(projectApplyVO.getProjectName());//业务名称       	
        	//流程处理
			TktmyProject project = businessProject.query(projectId);
        	if("METHOD_XD".equals(projectMethod)) {
        		workFlow.setSubProcessParamS(subProcessParamS);//询单
				if (StringUtils.isBlank(project.getProjectXmzgCode()) ){
					variable.put("projectXmzgCode", operator);//询单时，当前操作人暂时设置为项目主管
				} else {
					variable.put("projectXmzgCode", project.getProjectXmzgCode());//询单时，当前操作人暂时设置为项目主管
				}

        	} else {
        		workFlow.setUserLabelM(userLabelM);//预处
        		variable.put("projectXmzgCode", project.getProjectXmzgCode());//项目主管工号
        	}
		} else if("XMSQ_XMZG_QR".equals(workFlow.getCurrentActivity())) {//项目主管确认
			if(null==variable.get("isAgree")) {
				throw new PlatException("是否报主管部门领导需要选择!");
			}
			if("0".equals(variable.get("isAgree"))) {//不同意
				workFlow.setFlowState(EnumKTMYProcessStatus.ENDED);//设置流程结束
				return workFlow;
			}
			
			TktmyProject project = businessProject.query(projectId);
			if(null==project.getProjectFzrCode()) {
				throw new PlatException("项目负责人不能为空!");
			}
			if(null==project.getProjectXmglCode()) {
				throw new PlatException("项目管理单位不能为空!");
			}
			if(null==project.getProjectDeptCode()) {
				throw new PlatException("项目执行单位不能为空!");
			}
			if(null==project.getProjectXmzgCode()) {
				throw new PlatException("项目主管不能为空!");
			}
			if(StringUtils.isBlank(project.getProjectIsTech())) {
				throw new PlatException("项目是否是关键技术不能为空");
			}
			variable.put("projectFzrCode", project.getProjectFzrCode());//项目负责人
    		variable.put("projectXmglCode", project.getProjectXmglCode());//项目管理单位
    		variable.put("projectDeptCode", project.getProjectDeptCode());//项目执行单位
    		variable.put("projectXmzgCode", project.getProjectXmzgCode());//项目主管工号
			variable.put("projectIsTech", project.getProjectIsTech());//项目是否是关键技术
			variable.put("projectZzCode", project.getProjectZzCode());//评审组长
		} else if("GJJS_PS1".equals(workFlow.getCurrentActivity())){//关键技术评审
			TktmyKettechJs kettechJs = businessKTMYKettechJs.queryByProjectId(projectId);
			if("1".equals(kettechJs.getJsIsChooseZj())) {//需要专家评审
				//验证专家评审是否结束
				List<TktmyProjectMember> projectMemberList = businessKTMYProjectMember.queryBySource(kettechJs.getJsGuid(), EnumKTMYMemberType.USER_GJJS);
				if(ObjectUtil.isEmpty(projectMemberList)) {
					throw new PlatException("请选择专家");
				}
				String wpsUser = projectMemberList.stream()
						.filter(projectMember -> EnumKTMYMemberStatus.WPS.equals(projectMember.getSourceStatus()))
						.map(projectMember -> projectMember.getSourceName())
						.collect(Collectors.joining(","));
				if(StringUtils.isNotEmpty(wpsUser)) {
					throw new PlatException(wpsUser+"未评审");
				}
			}
		} else if("GJJS_ZGBMLD".equals(workFlow.getCurrentActivity())) {//关键技术输出审核_主管部门领导
			TktmyKettechJs kettechJs = businessKTMYKettechJs.queryByProjectId(workFlow.getBusinessId());
			if("OUTPUT_N".equals(kettechJs.getJsIsOutput())) {//不予输出
				workFlow.setFlowState(EnumKTMYProcessStatus.ENDED);//设置流程结束
				return workFlow;
			}
			TktmyProject project = businessProject.query(projectId);			
			//isToGSLD 是否报公司领导
			if(null==variable.get("isToGSLD")) {
				throw new PlatException("是否报公司领导需要选择!");
			}
			//isZB 是否总部
			String projectDeptCode  = project.getProjectDeptCode();
			if(null!=projectDeptCode&&OrgUtil.isChildOrg(projectDeptCode, "BGTA00")) {
				variable.put("isZB", "1");//是总部
			}else {
				variable.put("isZB", "0");//否总部
			}	
		} else if("XMSQ_MYZG".equals(workFlow.getCurrentActivity())) {//项目申请_贸易主管	
			//isToZGBMLD 是否报主管部门领导
			if(null==variable.get("isToZGBMLD")) {
				throw new PlatException("是否报主管部门领导需要选择!");
			}
		} else if("HTSP_MYZG".equals(workFlow.getCurrentActivity())//合同审批_贸易主管
				||"HTSP_ZGBMLD".equals(workFlow.getCurrentActivity())) {//合同审批_主管部门领导  需要传入合同类型和合同金额	
			TktmyContract contract = businessKTMYContract.queryActivedByProjectId(operator, projectId);		
			if(null==contract) {
				throw new PlatException("找不到对应合同,项目ID="+projectId);
			}
			if(null==contract.getInternalPriceFirst()) {
				throw new PlatException("内部价格范围(万元)不能为空!");
			}
			if(null==contract.getIsFbht()) {
				throw new PlatException("是否分包合同不能为空!");
			}
			variable.put("internalPriceFirst", contract.getInternalPriceFirst().intValue() );//内部价格范围(万元)
			variable.put("isFbht", contract.getIsFbht());//是否分包合同			
		} else if("GJJS_ZGBMLD".equals(workFlow.getCurrentActivity())) {//关键技术输出审核_主管部门领导
			TktmyKettechJs kettechJs = businessKTMYKettechJs.queryByProjectId(workFlow.getBusinessId());
			if("OUTPUT_N".equals(kettechJs.getJsIsOutput())) {//不予输出
				workFlow.setFlowState(EnumKTMYProcessStatus.ENDED);//设置流程结束
				return workFlow;
			}
			
			TktmyProject project = businessProject.query(projectId);			
			//isToGSLD 是否报公司领导
			if(null==variable.get("isToGSLD")) {
				throw new PlatException("是否报公司领导需要选择!");
			}
			//isZB 是否总部
			String projectDeptCode  = project.getProjectDeptCode();
			if(null!=projectDeptCode&&OrgUtil.isChildOrg(projectDeptCode, "BGTA00")) {
				variable.put("isZB", "1");//是总部
			}else {
				variable.put("isZB", "0");//否总部
			}	
		} else if("GJJS_GSFGLD".equals(workFlow.getCurrentActivity())) {//关键技术输出审核_公司分管领导
			
			TktmyKettechJs kettechJs = businessKTMYKettechJs.queryByProjectId(workFlow.getBusinessId());
			if("OUTPUT_N".equals(kettechJs.getJsIsOutput())) {//不予输出
				workFlow.setFlowState(EnumKTMYProcessStatus.ENDED);//设置流程结束
				return workFlow;
			}
			
			TktmyProject project = businessProject.query(projectId);
			//isZB 是否总部
			String projectDeptCode  = project.getProjectDeptCode();
			if(null!=projectDeptCode&&OrgUtil.isChildOrg(projectDeptCode, "BGTA00")) {
				variable.put("isZB", "1");//是总部
			}else {
				variable.put("isZB", "0");//否总部
			}	
		} else if("GJJS_GSZGBMLD".equals(workFlow.getCurrentActivity())) {//关键技术输出_公司主管部门领导
			TktmyKettechJs kettechJs = businessKTMYKettechJs.queryByProjectId(workFlow.getBusinessId());
			if("OUTPUT_N".equals(kettechJs.getJsIsOutput())) {//不予输出
				workFlow.setFlowState(EnumKTMYProcessStatus.ENDED);//设置流程结束
				return workFlow;
			}
			//isToGSLD 是否报公司领导
			if(null==variable.get("isToGSLD")) {
				throw new PlatException("是否报公司领导需要选择!");
			}
		} else if("GJJS_GSLD".equals(workFlow.getCurrentActivity())) {//关键技术输出_公司领导
			TktmyKettechJs kettechJs = businessKTMYKettechJs.queryByProjectId(workFlow.getBusinessId());
			if("OUTPUT_N".equals(kettechJs.getJsIsOutput())) {//不予输出
				workFlow.setFlowState(EnumKTMYProcessStatus.ENDED);//设置流程结束
				return workFlow;
			}
		}else if("HTSP_XMZG_QR".equals(workFlow.getCurrentActivity())) {//合同匹配确认
			TktmyProject project = businessProject.query(projectId);
			/*
    		//若果没有项目号，需要生成项目号
			if(StringUtils.isEmpty(project.getProjectNum())) {
				//生成项目编号
				project.setProjectNum(SequenceGenerator.getNextSequence(KTMYConstant.NUM_KEY_PROJECT));
				businessProject.update(operator, project);
			}
			*/
			if(StringUtils.isBlank(project.getProjectXmzgCode())) {
				throw new PlatException("项目主管为空");
			}
			variable.put("projectXmzgCode", project.getProjectXmzgCode());
    	} else if("HTSP_XMZG1".equals(workFlow.getCurrentActivity())) {//合同审批表_项目主管
    		TktmyContract contract = businessKTMYContract.queryDraftByProjectId(operator, workFlow.getBusinessId());
			if (null!=contract) {//非退回产生的
				KTMYCommonUtil.checkPs("ktmy_htzjps", contract.getHtId());
				//合同置为流转中
				businessKTMYContract.setActived(operator, projectApplyVO.getProjectId());//合同置为流转中
			}
    	} else if ("HTLR_XMZG".equals(workFlow.getCurrentActivity())) {//合同录入
    		//若果没有项目号，需要生成项目号
			TktmyProject project = businessProject.query(projectId);
			if(StringUtils.isEmpty(project.getProjectNum())) {
				//生成项目编号
				project.setProjectNum(SequenceGenerator.getNextSequence(KTMYConstant.NUM_KEY_PROJECT));
				businessProject.update(operator, project);
			}
			//生成合同编号
			TktmyCollectionContract collectionContract = businessKTMYCollectionContract.genHTBHByProjectId(operator, projectId);

			//如果项目是商标过来还需要调用商标的接口
			if ("SOURCE_SB".equals(project.getProjectSource())&&StringUtils.isNotEmpty(project.getExtra2())) {//来源商标
				Map<String, Object> tradeContract = new HashMap<>();
				tradeContract.put("tradeContractNo", collectionContract.getSkHtbh());//贸易合同号
				tradeContract.put("tradePartnes", collectionContract.getSkCustomUnitname());//贸易对象
				tradeContract.put("tradePermId", project.getExtra2());//许可编号
				tradeContract.put("extra1", appContextProp.getCtxZZZC()+"web/KTMYPD01?projectId="+project.getProjectId());//技术贸易信息url
				EiInfo callSB = new EiInfo();
				callSB.set("operator", operator);
				callSB.set(EiConstant.serviceId, "S_KY_SB_01");
				callSB.set("tradeContract", tradeContract);
				callSB = XServiceManager.call(callSB);
				if (callSB.getStatus()==-1) {
					throw new PlatException("拋送商标接口报错:"+callSB.getMsg());
				}
			}
    	}
		
		workFlow.setVariable(variable);
		return workFlow;
	}
	
	/**
	 * 根据节点查询相关页面需要的信息
	 * @param operator
	 * @param projectApplyVO
	 * @return
	 */
	private ProjectApplyVO queryDBDetail(String operator, ProjectApplyVO projectApplyVO) {
		//项目相关富文本
		projectApplyVO.setProjectRichText(ClobUtil.getContentMap(projectApplyVO.getProjectId()));
		
		//流程属性
		WorkFlow workFlow = projectApplyVO.getWorkFlow();		
		
		//知识产权利用情况
    	projectApplyVO.setTeachScheduleList(businessKTMYTeachSchedule.queryByProjectId(workFlow.getBusinessId()));
		
		if(StringUtils.isEmpty(workFlow.getCurrentActivity())||workFlow.getCurrentActivity().indexOf("XMSQ")>=0) {//项目申请阶段
			if(StringUtils.isEmpty(workFlow.getCurrentActivity())||"XMSQ_XMZG_SQ".equals(workFlow.getCurrentActivity())) {//项目申请节点
				if("METHOD_XD".equals(projectApplyVO.getProjectMethod())) {//询单
		    		List<TktmyProjectMember> list = businessKTMYProjectMember.queryBySource(workFlow.getBusinessId(), EnumKTMYMemberType.DEPT_XDYJ);
		    		if(null!=list) {
		    			projectApplyVO.setDeptYdyj(list.stream().map(projectMember -> projectMember.getSourceCode()).collect(Collectors.joining(",")));
		    		}
		    	} else {//预处
		    		List<TktmyProjectMember> list = businessKTMYProjectMember.queryBySource(workFlow.getBusinessId(), EnumKTMYMemberType.USER_LXYC);
		    		if(null!=list) {	    		
			    		projectApplyVO.setUserLxyc(list.stream().map(projectMember -> projectMember.getSourceCode()).collect(Collectors.joining(",")));
		    		}
		    	}
			} else if ("XMSQ_XDBM".equals(workFlow.getCurrentActivity())) {//询单部门
				WorkFlow workFlowSub = SWorkFlowUtil.getWorkFlowByTaskId(workFlow.getTaskId());
				String departmentNo = (String)workFlowSub.getVariable().get("departmentNo");//会签组织
				if(StringUtils.isEmpty(departmentNo)) {
					throw new PlatException("找不到对应的询单部门");
				}
				TktmyProjectMember projectMember = businessKTMYProjectMember.queryBySource(workFlow.getBusinessId(), EnumKTMYMemberType.DEPT_XDYJ, departmentNo);
				ProjectMemberVO projectMemberVO = ProjectMemberVO.initParent(projectMember);
				
				projectMemberVO.setRichText(ClobUtil.getContentMap(projectMember.getRowId()));
				
				projectApplyVO.setProjectMemberVO(projectMemberVO);
			}
		}else if(workFlow.getCurrentActivity().indexOf("GJJS")>=0) {//关键技术阶段
			//关键技术
	    	TktmyKettechJs kettechJs = businessKTMYKettechJs.queryByProjectId(workFlow.getBusinessId());
	    	if(null == kettechJs) {
	    		kettechJs = businessKTMYKettechJs.initBean(operator, workFlow.getBusinessId());
	    		if(StringUtils.isEmpty(projectApplyVO.getProjectZzCode())) {
	    			throw new PlatException("评审组长不能为空!");
	    		}  
	    		kettechJs.setJsPszzCode(projectApplyVO.getProjectZzCode());
				businessKTMYKettechJs.save(operator, kettechJs);//提前保存关键技术id
	    	}
	    	KettechJsVO kettechJsVO = KettechJsVO.initParent(kettechJs);
	    	//关键技术富文本
	    	kettechJsVO.setJsRichText(ClobUtil.getContentMap(kettechJs.getJsGuid()));
	    	
	    	projectApplyVO.setKettechJsVO(kettechJsVO);
	    	
	    	if("GJJS_PS1".equals(workFlow.getCurrentActivity())||"GJJS_PS2".equals(workFlow.getCurrentActivity())) {
	    		TktmyProjectMember projectMember = businessKTMYProjectMember.queryBySource(kettechJs.getJsGuid(), EnumKTMYMemberType.USER_GJJS, operator);
				if(null!=projectMember) {
					projectApplyVO.setPageNo("KTMYKJ02");
				}
	    	}    	
		} else if("HTSP_RUN".equals(workFlow.getCurrentActivity())){//合同运行中 优先考虑分包合同
			//合同VO
	    	ContractVO contractVO = new ContractVO();
	    	//查询草稿
	    	TktmyContract contract = businessKTMYContract.queryDraftByProjectId(operator, workFlow.getBusinessId());
	    	if(null==contract) {
	    		contract = businessKTMYContract.initBean(operator, workFlow.getBusinessId());
	    		contract.setIsFbht("1");
	    	}
	    	contractVO = ContractVO.initParent(contract);
	    	//产权信息
    		List<TktmyProerty> proertyList = businessKTMYProerty.queryByHtId(operator, contract.getHtId());
    		if(ObjectUtil.isEmpty(proertyList)) {
    			proertyList = businessKTMYProerty.initList(operator, contract.getHtId());
    		}
    		contractVO.setProertyList(proertyList);
    		//经费预算
    		List<TktmyBudget> budgetList = businessKTMYBudget.queryByHtId(operator, contract.getHtId());
    		if(ObjectUtil.isEmpty(budgetList)) {
    			budgetList = businessKTMYBudget.initList(operator, contract.getHtId());
    		}
    		contractVO.setBudgetList(budgetList);
	    	
	    	//合同相关富文本
    		contractVO.setHtRichText(ClobUtil.getContentMap(contractVO.getHtId()));
    		
    		projectApplyVO.setContractVO(contractVO); 
		} else if(workFlow.getCurrentActivity().indexOf("HTSP")>=0) {//合同审批阶段 非分包合同
			//合同VO
	    	ContractVO contractVO = new ContractVO();
	    	//查询活动中的
	    	TktmyContract contract = businessKTMYContract.queryActivedByProjectId(operator, workFlow.getBusinessId());
	    	if(null==contract) {
	    		contract = businessKTMYContract.queryDraftByProjectId(operator, workFlow.getBusinessId());
	    		if(null==contract) {
	    			contract = businessKTMYContract.initBean(operator, workFlow.getBusinessId());
	    			contract.setIsFbht("0");
		    		contract = businessKTMYContract.save(operator, contract);
	    		}
	    	}
	    	contractVO = ContractVO.initParent(contract);
	    	//产权信息
    		List<TktmyProerty> proertyList = businessKTMYProerty.queryByHtId(operator, contract.getHtId());
    		if(ObjectUtil.isEmpty(proertyList)) {
    			proertyList = businessKTMYProerty.initList(operator, contract.getHtId());
    		}
    		contractVO.setProertyList(proertyList);
    		//经费预算
    		List<TktmyBudget> budgetList = businessKTMYBudget.queryByHtId(operator, contract.getHtId());
    		if(ObjectUtil.isEmpty(budgetList)) {
    			budgetList = businessKTMYBudget.initList(operator, contract.getHtId());
    		}
    		contractVO.setBudgetList(budgetList);
	    	
	    	//合同相关富文本
    		contractVO.setHtRichText(ClobUtil.getContentMap(contractVO.getHtId()));
    		
    		projectApplyVO.setContractVO(contractVO);   	
    		
			if("HTSP_XMZG1".equals(workFlow.getCurrentActivity())) {
				/*
				TktmyProjectMember projectMember = businessKTMYProjectMember.queryBySource(contractVO.getHtId(), EnumKTMYMemberType.USER_HTSP_ZJPS, operator);
				if(null!=projectMember) {
					projectApplyVO.setPageNo("KTMYHT03");//专家评审跳特定画面
				}
				*/
				projectApplyVO.setPsBaseInfo(KTMYCommonUtil.getPsBaseInfo("ktmy_htzjps", contractVO.getHtId(), projectApplyVO));
			} else if("HTSP_XMZG_QR".equals(workFlow.getCurrentActivity())) {//合同匹配确认
				Map<String,Object> extra = projectApplyVO.getExtra();
				if(null==extra) {
					extra = new HashMap<String, Object>();
					projectApplyVO.setExtra(extra);
				}
				//if(null==contract.getInternalPriceFirst()|| contract.getInternalPriceFirst().compareTo(new BigDecimal(0))==0) {
				extra.put("canEndProcess", true);
				//}
	    	}
		} else if(workFlow.getCurrentActivity().indexOf("HTLR")>=0) {//合同录入-收款合同
			TktmyContract contract = businessKTMYContract.queryActivedByProjectId(operator, workFlow.getBusinessId());
			//合同VO
	    	ContractVO contractVO = ContractVO.initParent(contract);	    	
	    	//合同收款
			TktmyCollectionContract collectionContract = businessKTMYCollectionContract.queryByHtId(operator, contract.getHtId());
    		if(null==collectionContract) {
    			collectionContract = businessKTMYCollectionContract.initBean(operator, contract.getHtId(), contract.getProjectId());
    			collectionContract.setSkHtname(projectApplyVO.getProjectName());
    		}
    		CollectionContractVO collectionContractVO = CollectionContractVO.initParent(collectionContract);
    		
    		//收款合同富文本
    		Map<String,Object> richText = ClobUtil.getContentMap(collectionContract.getSkId());
    		if(null==richText) {
    			Map<String,Object> htRichText = ClobUtil.getContentMap(contract.getHtId());//合同相关富文本
    			richText = new HashMap<String, Object>();
    			richText.put("htgs", htRichText.get("htxynr"));//带入合同协议内容为概述
    		}
    		collectionContractVO.setRichText(richText);
    		
    		//收款合同用户
    		collectionContractVO.setCollectionPeople(businessKTMYCollectionPeople.queryBySkId(collectionContract.getSkId()));
    		
    		//初始化客商相关信息
			collectionContract = KTMYCommonUtil.getKsInfo(operator, projectApplyVO.getProjectTradeunit(), collectionContractVO);
    		
    		//计划收款信息对象
    		List<TktmyCollectionInfo> collectionInfoList = businessKTMYCollectionInfo.queryBySkId(collectionContract.getSkId());
    		if(ObjectUtil.isEmpty(collectionInfoList)) {
    			collectionInfoList.add(businessKTMYCollectionInfo.initBean(operator,collectionContractVO.getSkId()));
    		}
    		collectionContractVO.setCollectionInfoList(collectionInfoList);

    		contractVO.setCollectionContractVO(collectionContractVO);
			projectApplyVO.setContractVO(contractVO);
		}
		return projectApplyVO;
	}
	
	/**
	 * 草稿列表
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageDraft(EiInfo inInfo) {
	    try{
			String operator = UserSession.getLoginName();
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("projectStatus", EnumKTMYProcessStatus.DRAFT);
			queryData.put("delStatus", "0");//有效状态
			queryData.put("createUserLabel", operator);//获取当前登录人的
            TableDataInfo queryPage = businessProject.queryPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 负责人相关的运行中的项目
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageRun(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("projectStatus", EnumKTMYProcessStatus.RUN);
            queryData.put("projectFzrCode", operator);//项目负责人
            TableDataInfo queryPage = businessProject.queryPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 项目主管相关的运行中的项目
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageXmzgRun(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("projectStatus", EnumKTMYProcessStatus.RUN);
            queryData.put("projectXmzgCode", operator);//项目负责人
			if(null!=queryData.get("projectDeptCode")) {
				String projectDeptCode = (String)queryData.get("projectDeptCode");
				ADOrg org = OrgUtil.getOrgByOrgCode(projectDeptCode);
				if(null!=org) {
					queryData.remove("projectDeptCode");
					queryData.put("projectDeptCodePathLike", org.getOrgPathCode());
				}
			}
            TableDataInfo queryPage = businessProject.queryPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 相关人员的运行中的项目
	 * 相关人员为:负责人和项目主管
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageXgryRun(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("projectStatus", EnumKTMYProcessStatus.RUN);
            queryData.put("projectXgryCode", operator);//相关人员
            TableDataInfo queryPage = businessProject.queryGZPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 项目主管相关的分包合同对应的项目
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageFB(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("projectXmzgCode", operator);//项目负责人
            TableDataInfo queryPage = businessProject.pageFB(queryData);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 项目负责人相关的分包合同对应的项目（收款信息）
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageFBByFZR(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("projectFzrCode", operator);//项目负责人
            queryData.put("htStatus", EnumKTMYProcessStatus.RUN);
			//查询出没有付款的数据付款计划
            queryData.put("extra1","no-pay");
            TableDataInfo queryPage = businessProject.pageFBByFZR(queryData);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 查询项目详细
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryProjectDetail(EiInfo inInfo) {
		try{
			String operator = UserSession.getLoginName();
	    	String projectId = (String)inInfo.get("projectId");
	    	String htId = (String)inInfo.get("htId");
	    	String pgId = (String)inInfo.get("pgId");
	    	if(StringUtils.isBlank(projectId)) {
	    		if(StringUtils.isNotBlank(htId)) {
	    			TktmyContract contract = businessKTMYContract.query(htId);
	    			if(null!=contract) {
	    				projectId = contract.getProjectId();
	    			}
	    		}else if(StringUtils.isNotBlank(pgId)) {
	    			TktmyAccess access = businessAccess.query(pgId);
	    			if(null!=access) {
	    				projectId = access.getProjectId();
	    			}
	    		}
	    	}
	    	if(StringUtils.isBlank(projectId)) {
	    		throw new PlatException("缺少项目ID");
	    	}
	    	TktmyProject project = businessProject.query(projectId);	    	
	    	ProjectApplyVO projectApplyVO = ProjectApplyVO.initParent(project);	
			
			//知识产权利用情况
	    	projectApplyVO.setTeachScheduleList(businessKTMYTeachSchedule.queryByProjectId(projectId));
	    	
	    	//关键技术
	    	//projectApplyVO.setKettechJs(businessKTMYKettechJs.queryByProjectId(projectId));
	    	
	    	//合同
	    	//projectApplyVO.setContractList(businessKTMYContract.queryListByProjectId(projectId));
			
	    	//项目富文本信息
	    	projectApplyVO.setProjectRichText(ClobUtil.getContentMap(projectId));
	    	
	    	//流程信息
	    	projectApplyVO.setWorkFlow(SWorkFlowUtil.getMainFlowInfoByBusinessId(projectId));
	    	
	    	//
	    	Map<String,Object> extra = new HashMap<String, Object>();
	    	boolean haveConTZ = false;//结题通知
	    	boolean haveFinalMain = false;//决算
	    	boolean haveAccess = false;//评估
	    	boolean haveAward = false;//奖励申请
	    	boolean haveRewardNotice = false;//奖励通知
	    	TktmyConclusion conclusion = businessConclusion.queryByProject(projectId);
	    	if(null!=conclusion) {
	    		if(UserUtil.isAdmin(operator)) {
	    			haveConTZ = true;
	    		}
	    		if(EnumKTMYProcessStatus.JIETI.equals(project.getProjectStatus())) {
		    		haveFinalMain = true;
		    		TktmyAccess access = businessAccess.queryByProjectId(projectId);
			    	if(null!=access) {
			    		haveAccess = true;
			    	}
			    	haveAward = true;
			    	haveRewardNotice = true;
		    	} else {
		    		TktmyFinalMain finalMain = businessFinalMain.queryByProjectId(projectId);
			    	if(null!=finalMain) {
			    		haveFinalMain = true;
			    	}
			    	TktmyAccess access = businessAccess.queryByProjectId(projectId);
			    	if(null!=access) {
			    		haveAccess = true;
			    	}
			    	TktmyAward award = businessAward.queryByProjectId(projectId);
			    	if(null!=award) {
			    		haveAward = true;
			    	}
			    	TktmyRewardNotice rewardNotice = businessRewardNotice.queryByProjectId(projectId);
			    	if(null!=rewardNotice) {
			    		haveRewardNotice = true;
			    	}
		    	}
	    		extra.put("businessId", projectId+","+conclusion.getConId());
	    	}else {
	    		extra.put("businessId", projectId);
	    	}
	    	extra.put("haveConTZ", haveConTZ);
	    	extra.put("haveFinalMain", haveFinalMain);
	    	extra.put("haveAccess", haveAccess);
	    	extra.put("haveAward", haveAward);
	    	extra.put("haveRewardNotice", haveRewardNotice);
			if(operator.equals(project.getProjectXmzgCode()) //项目主管
					&& (EnumKTMYProcessStatus.ACTIVED.equals(project.getProjectStatus())
						|| EnumKTMYProcessStatus.RUN.equals(project.getProjectStatus())
						|| EnumKTMYProcessStatus.RUN_2.equals(project.getProjectStatus())
						)
					){
				extra.put("canEndProcess", true);//是否能终止项目
			}
	    	projectApplyVO.setExtra(extra);
	    	
	    	inInfo.set("projectApplyVO", projectApplyVO);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 我负责的项目
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageGZFZ(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("projectFzrCode", operator);//项目负责人
            TableDataInfo queryPage = businessProject.queryGZPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 我管理的项目
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageGZGL(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            if(!UserUtil.isAdmin(operator)) {
            	queryData.put("projectXmzgCode", operator);//项目管理员
            }
            TableDataInfo queryPage = businessProject.queryGZPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 我相关的项目
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageGZXG(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("projectXgryCode", operator);//相关人员
            TableDataInfo queryPage = businessProject.queryGZPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 综合查询
	 * @param inInfo
	 * @return
	 */
	public EiInfo exportGZZH(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            if(!UserUtil.isAdmin(operator)) {
            	queryData.put("loginName", operator);//当前登录用户
            	
            	//取综合查询角色对应的组织
            	List<ADOrg> list = RoleUtil.getOrgByUser(operator, KTMYConstant.ROLE_KEY_ZHCX);
            	if(ObjectUtil.isNotEmpty(list)) {
            		queryData.put("orgCodePath", list.get(0).getOrgPathCode());
            	}
            }
            List<Map<String,Object>> exportGZZHList = businessProject.exportGZZHList(queryData);
            List<ProjectGZZHExcel> result = new ArrayList<>();
            ProjectGZZHExcel projectGZZHExcel = null;
            for (Map map : exportGZZHList) {
            	projectGZZHExcel = BeanUtil.toBean(map, ProjectGZZHExcel.class);
				if (StringUtils.isBlank(projectGZZHExcel.getProjectFzrCode())) {
					projectGZZHExcel.setProjectFzrName("");
				} else {
					projectGZZHExcel.setProjectFzrName(projectGZZHExcel.getProjectFzrCode()+"-"+projectGZZHExcel.getProjectFzrName());
				}
            	projectGZZHExcel.setProjectStatus(EnumKTMYProcessStatus.getNameByCode(projectGZZHExcel.getProjectStatus()));
            	projectGZZHExcel.setProjectXmzgName(projectGZZHExcel.getProjectXmzgCode()+"-"+projectGZZHExcel.getProjectXmzgName());
            	projectGZZHExcel.setProjectKind(SDictUtil.getDictName(KTMYConstant.BUSINESS_TYPE, "PROJECT_KIND", projectGZZHExcel.getProjectKind()));
				projectGZZHExcel.setProjectMethod(SDictUtil.getDictName(KTMYConstant.BUSINESS_TYPE, "PROJECT_METHOD", projectGZZHExcel.getProjectMethod()));
            	projectGZZHExcel.setProjectKsxm(SDictUtil.getDictName("MPTY", "is_yes_no", projectGZZHExcel.getProjectKsxm()));
            	projectGZZHExcel.setProjectIsTech(SDictUtil.getDictName("MPTY", "is_yes_no", projectGZZHExcel.getProjectIsTech()));
            	result.add(projectGZZHExcel);
			}
            inInfo.set(EiConstant.resultBlock, result);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 综合查询
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageGZZH(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            if(!UserUtil.isAdmin(operator)) {
            	queryData.put("loginName", operator);//当前登录用户
            	
            	//取综合查询角色对应的组织
            	List<ADOrg> list = RoleUtil.getOrgByUser(operator, KTMYConstant.ROLE_KEY_ZHCX);
				if (ObjectUtil.isEmpty(list)) {//如果没有综合查询权限，获取部门浏览权限
					list = RoleUtil.getOrgByUser(operator, KTMYConstant.ROLE_KEY_BM_READER);
				}
            	if(ObjectUtil.isNotEmpty(list)) {
            		queryData.put("orgCodePath", list.get(0).getOrgPathCode());
            	}
            }
            TableDataInfo queryPage = businessProject.queryZHPage(queryData);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 我处理过的项目
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageGZYB(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("userLabel", operator);
            TableDataInfo queryPage = businessProject.pageGZYB(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 项目分包的合同
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageGZFB(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            //queryData.put("userLabel", operator);
            queryData.put("isFbht", "1");//分包合同
            if(StringUtils.isEmpty((String)queryData.get("projectId"))&&!UserUtil.isAdmin(operator)) {
            	queryData.put("loginName", operator);
            }
            TableDataInfo queryPage = businessProject.pageFB(queryData);
			List<Map<String,Object>> listData = (List<Map<String,Object>>)queryPage.getRows();
			for (Map<String,Object> data : listData) {
				String projectFzrCode = (String)data.get("projectFzrCode");
				data.put("isFzr", operator.equals(projectFzrCode));
			}
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 查询项目详细
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryProject(EiInfo inInfo) {
		try{
	    	String projectId = (String)inInfo.get("businessId");
	    	TktmyProject project = businessProject.query(projectId);	    	

	    	inInfo.set("project", project);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 根据项目编号获取项目
	 * S_KT_MY_01
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryProjectByProjectNum(EiInfo inInfo) {
		try{
	    	String projectNum = (String)inInfo.get("projectNum");
	    	TktmyProject project = businessProject.queryProjectByProjectNum(projectNum);

	    	inInfo.set("project", project);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 启动流程
	 * S_KT_MY_02
	 * @param inInfo
	 * @return
	 */
	public EiInfo doStart(EiInfo inInfo) {
		try{
			String operator = (String)inInfo.get("operator");;
			if(StringUtils.isBlank(operator)) {
				operator = UserSession.getLoginName();
			}			
			if(StringUtils.isBlank(operator)) {
				throw new PlatException("operator不能为空");
			}
			Object object = (Object)inInfo.get("projectApplyVO");
	    	ProjectApplyVO projectApplyVO = BeanUtil.toBean(object, ProjectApplyVO.class);
			//校验
			if(StringUtils.isBlank(projectApplyVO.getProjectName())) {
				throw new PlatException("项目名称【projectName】不能为空");
			}
			if(StringUtils.isBlank(projectApplyVO.getProjectXmzgCode())) {
				throw new PlatException("项目主管【projectXmzgCode】不能为空");
			}
			if(StringUtils.isBlank(projectApplyVO.getProjectMethod())) {
				projectApplyVO.setProjectMethod("METHOD_YC");//不传为预处
			}
			//初始数据
	    	if(StringUtils.isBlank(projectApplyVO.getProjectId())) {
	    		projectApplyVO.setProjectId(BizIdUtil.INSTANCE.nextId());//提前生成,为了暂存用
	    	}
	    	TktmyProject project = businessProject.initBean(projectApplyVO.getProjectXmzgCode(), projectApplyVO.getProjectMethod());
	    	
	    	CopyOptions copyOptions = CopyOptions.create().setIgnoreCase(false);
	    	copyOptions.setIgnoreNullValue(true);
	    	BeanUtil.copyProperties(project, projectApplyVO, copyOptions);
	    	
	    	//流程属性
	    	WorkFlow workFlow = new WorkFlow(operator, KTMYConstant.PROCESS_KEY_PROJCET_APPLY, KTMYConstant.BUSINESS_TYPE, projectApplyVO.getProjectId());
	    	workFlow.setUserLabelM(projectApplyVO.getProjectXmzgCode());
	    	projectApplyVO.setWorkFlow(workFlow);
	    	
            //保存业务数据
	    	save(operator, projectApplyVO);
            
            //流程                  
            String msg = "";
            workFlow = valdateWFNode(operator, projectApplyVO);

        	workFlow.setBusinessName(projectApplyVO.getProjectName());//业务名称
        	
        	//启动流程
        	msg = SWorkFlowUtil.startProcess(operator, workFlow);
        	
        	//设置业务状态
        	businessProject.setActived(operator, projectApplyVO.getProjectId()); 
        	
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 查询开票项目
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryKPProject(EiInfo inInfo) {
		try{
			List<Map<String,Object>> rtn = new ArrayList<>();
			Map<String,Object> data = null;
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("projectStatus", EnumKTMYProcessStatus.RUN);//运行中项目
            TableDataInfo pageTable = businessProject.querySKPage(queryData);
            for (Map<String, Object> map : (List<Map<String, Object>>)pageTable.getRows()) {
            	data = new HashMap<>();
            	data.put("skId", map.get("skId"));//收款合同id
            	data.put("skHtbh", map.get("skHtbh"));//收款合同编号
            	data.put("projectNum", map.get("projectNum"));//项目编号
            	data.put("projectName", map.get("projectName"));//项目名称
            	data.put("xmzgCode", map.get("projectXmzgCode"));//项目主管
            	data.put("xmzgName", map.get("projectXmzgName"));//项目主管名称
            	rtn.add(data);
			}
            pageTable.setRows(rtn);
            inInfo = setPage(inInfo, pageTable);
            inInfo.set("list", rtn);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 查询开票项目详细
	 * @param inInfo
	 * @return
	 */
	public EiInfo queryKPProjectDetail(EiInfo inInfo) {
		try{
			//String projectNum = (String)inInfo.get("projectNum");
			String skHtbh = (String)inInfo.get("skHtbh");
			if(StringUtils.isBlank(skHtbh)) {
				throw new PlatException("缺少合同编号");
			}
			TktmyCollectionContract collectionContract = businessKTMYCollectionContract.queryBySkHtbh(skHtbh);
			TktmyProject project = businessProject.query(collectionContract.getProjectId());
			Map<String,Object> result = new HashMap<String, Object>();
			
			String[] cwConfig = KTMYCommonUtil.getCwConfig(project.getProjectXmglCode());
			if(null==cwConfig) {
				throw new PlatException("找不到"+project.getProjectXmglCode()+"对应的账套和责任中心，请联系管理员配置.");
			}
			if (null!=collectionContract) {
				result.put("kbinfoXmmc", project.getProjectName());//项目名称
				result.put("kbinfoHtbh", collectionContract.getSkHtbh());//合同号
	            result.put("kbinfoAccountBook", cwConfig[0]);//账套
	            result.put("kbinfoDutyCenter", cwConfig[1]);//责任中心
	            result.put("kbinfoCustomid", collectionContract.getSkCustomid());//客商代码
	            result.put("kbinfoCustomUnitname", collectionContract.getSkCustomUnitname());//客户名称
	            result.put("kbinfoCustomTax", collectionContract.getSkCustomTax());//税号
	            result.put("kbinfoCustomAddress", collectionContract.getSkCustomAddress());//地址
	            result.put("kbinfoCustomBank", collectionContract.getSkCustomBank());//开户银行
	            result.put("kbinfoCustomAccount", collectionContract.getSkCustomAccount());//账号
			}
            inInfo.set(EiConstant.resultBlock, result);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	
	/**
	 * 判断是否是项目主管
	 * @param inInfo
	 * @return
	 */
	public EiInfo isXmzg(EiInfo inInfo) {
		try{
			String operator = UserSession.getLoginName(); 
			String projectId = (String)inInfo.get("projectId");
			if(StringUtils.isBlank(projectId)) {
				throw new PlatException("缺少项目ID");
			}
			if(UserUtil.isAdmin(operator)) {
	            inInfo.set(EiConstant.resultBlock, true);
			} else {
				TktmyProject project = businessProject.query(projectId);
	            inInfo.set(EiConstant.resultBlock, operator.equals(project.getProjectXmzgCode()));
			}
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	/**
	 * 负责人相关的运行中的项目
	 * @param inInfo
	 * @return
	 */
	public EiInfo pageRunSk(EiInfo inInfo) {
	    try{
	    	String operator = UserSession.getLoginName(); 	
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("projectStatus", EnumKTMYProcessStatus.RUN);
            queryData.put("projectFzrCode", operator);//项目负责人
            TableDataInfo queryPage = businessProject.querySKPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	/**
	 * 终止项目
	 * @param inInfo
	 * @return
	 */
	public EiInfo endProject(EiInfo inInfo) {
		try {
			String operator = UserSession.getLoginName();

			//保存业务数据
			ProjectApplyVO projectApplyVO = (ProjectApplyVO) inInfo.get("projectApplyVO");
			//save(operator, projectApplyVO);

			TktmyProject project = businessProject.query(projectApplyVO.getProjectId());
			if(EnumKTMYProcessStatus.TEMP_TERMINAITION.equals(project.getProjectStatus())){
				throw new PlatException("项目已终止");
			}
			String comment = (String) projectApplyVO.getExtra().get("comment");
			if(StringUtils.isBlank(comment)) {
				throw new PlatException("项目终止意见不能为空！");
			}
			WorkFlow workFlow = new WorkFlow();
			workFlow.setOperator(operator);
			workFlow.setComment(comment);
			if (EnumKTMYProcessStatus.ACTIVED.equals(project.getProjectStatus())) {//申请中
				businessProject.setTermination(operator, projectApplyVO.getProjectId());//终止

				WorkFlow curWorkFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(projectApplyVO.getProjectId());
				if (null != curWorkFlow && "active".equals(curWorkFlow.getFlowState())) {
					workFlow.setBusinessId(projectApplyVO.getProjectId());
					SWorkFlowUtil.endProecess(operator, workFlow);
				} else if (null != curWorkFlow && EnumKTMYProcessStatus.TEMP_TERMINAITION.equals(curWorkFlow.getFlowState())){
					workFlow.setBusinessId(projectApplyVO.getProjectId());
					workFlow.setUserLabelM(operator);
					SWorkFlowUtil.addCompletedTask(operator,workFlow);
				}
			}else if(EnumKTMYProcessStatus.RUN.equals(project.getProjectStatus())){//运行中
				businessProject.setTermination(operator, projectApplyVO.getProjectId());//终止
				workFlow.setBusinessId(projectApplyVO.getProjectId());
				workFlow.setUserLabelM(operator);
				SWorkFlowUtil.addCompletedTask(operator,workFlow);
			}else if(EnumKTMYProcessStatus.RUN_2.equals(project.getProjectStatus())){//结题中
				businessProject.setTermination(operator, projectApplyVO.getProjectId());//终止
				TktmyConclusion conclusion = businessConclusion.queryByProject(projectApplyVO.getProjectId());
				if(null!=conclusion){
					businessConclusion.setStatus(operator, conclusion.getConId(), EnumKTMYProcessStatus.TEMP_TERMINAITION);
					WorkFlow curWorkFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(conclusion.getConId());
					if (null != curWorkFlow && "active".equals(curWorkFlow.getFlowState())) {
						workFlow.setBusinessId(conclusion.getConId());
						SWorkFlowUtil.endProecess(operator, workFlow);
					}
				}
			}
			inInfo.setMsg("项目终止");
			inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
			e.printStackTrace();
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
			inInfo.setMsg(e.getMessage());
			if (null != e.getCause()) {
				logger.error(e.getMessage(), e);
				inInfo.setDetailMsg(e.getCause().getMessage());
			} else {
				logger.error(e.getMessage());
			}
		}
		return inInfo;
	}
}
