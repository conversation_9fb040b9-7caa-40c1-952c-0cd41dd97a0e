package com.baosight.bscdkj.kt.jc.business;

import com.baosight.bscdkj.common.kt.domain.TktjcPerRewardDetail;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import com.baosight.bscdkj.utils.BizIdUtil;
import com.baosight.bscdkj.common.business.BusinessBase;
import com.baosight.bscdkj.common.domain.TableDataInfo;

/**
 * 28_15个人奖金明细
 * 
 * <AUTHOR>
 * @date 2022-04-11
 */
 @Component
public class BusinessKTJCPerRewardDetail extends BusinessBase{
	
	private static final String NAME_SPACE = "tktjcPerRewardDetail";

	/**
     * 初始化
     * @return
     */
	public TktjcPerRewardDetail initBean(String operator) {
		TktjcPerRewardDetail perRewardDetail = new TktjcPerRewardDetail();
		return perRewardDetail;
	}

    /***
	 * 查询List
	 *
	 * @param param
	 * @return
	 */
	public List<TktjcPerRewardDetail> queryList(Map<String, Object> param) {
		return dao.query(NAME_SPACE + ".query", param);
	}

	/**
	 * 查询
	 *
	 * @return
	 */
	public TktjcPerRewardDetail query(String id) {
		if (StringUtils.isBlank(id)) {
			return null;
		}
		Map<String, Object> hashMap = new HashMap<>();
		hashMap.put("recordGuid", id);
		List query = dao.query(NAME_SPACE + ".query", hashMap);
		return query != null && !query.isEmpty() ? (TktjcPerRewardDetail) query.get(0) : null;
	}
	
    /***
     * 查询List 分页
     *
     * @param param
     * @return
     */
	public TableDataInfo queryPage(Map<String, Object> param) {
		return getPage(NAME_SPACE + ".query", param);
	}

   /**
	 * 新增记录
	 *
	 * @param perRewardDetail
	 * @return
	 */
	public TktjcPerRewardDetail insert(String operator, TktjcPerRewardDetail perRewardDetail) {
		perRewardDetail.initAdd(operator);
        if(StringUtils.isEmpty(perRewardDetail.getRecordGuid())) {
            perRewardDetail.setRecordGuid(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert(NAME_SPACE + ".insert", perRewardDetail);
        return perRewardDetail;
	}
	
	/**
	 * 修改
	 * @param perRewardDetail
	 * @return
	 */
	public TktjcPerRewardDetail update(String operator, TktjcPerRewardDetail perRewardDetail) {
	    perRewardDetail.initUpdate(operator);
		dao.update(NAME_SPACE + ".update", perRewardDetail);
		return perRewardDetail;
	}

	/**
	 * 逻辑删除
	 * 
	 * @return
	 */
	public TktjcPerRewardDetail deleteLogin(String operator, String recordGuid) {
	    TktjcPerRewardDetail perRewardDetail = new TktjcPerRewardDetail();
	    perRewardDetail.setRecordGuid(recordGuid);
	    perRewardDetail.initLogicDel(operator);
		dao.update(NAME_SPACE + ".update", perRewardDetail);
		return perRewardDetail;
	}
	
    /**
     * 物理删除
     *
     * @return
     */
    public int delete(String operator, String recordGuid) {
        return dao.delete(NAME_SPACE + ".delete", recordGuid);
    }

	/**
	 * 保存
	 *
	 * @param perRewardDetail
	 * @return
	 */
	public TktjcPerRewardDetail save(String operator, TktjcPerRewardDetail perRewardDetail) {
		if(StringUtils.isEmpty(perRewardDetail.getRecordGuid())) {
			return insert(operator, perRewardDetail);
		} else {
			if(null==query(perRewardDetail.getRecordGuid())) {
				return insert(operator, perRewardDetail);
			}
			return update(operator, perRewardDetail);
		}
	}

}
