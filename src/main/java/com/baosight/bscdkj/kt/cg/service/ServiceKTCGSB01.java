package com.baosight.bscdkj.kt.cg.service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.baosight.bscdkj.kt.cg.business.BusinessKTCGNeed;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import com.baosight.bscdkj.common.constant.Constants;
import com.baosight.bscdkj.common.constant.KTCGConstants;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.kt.domain.TktcgNeed;
import com.baosight.bscdkj.common.kt.domain.TktcgTeachContract;
import com.baosight.bscdkj.common.service.PageService;
import com.baosight.bscdkj.kt.cg.business.BusinessKTCGTeachContract;
import com.baosight.bscdkj.mp.ad.dto.ADOrg;
import com.baosight.bscdkj.mp.ad.utils.OrgUtil;
import com.baosight.bscdkj.mp.ad.utils.RoleUtil;
import com.baosight.bscdkj.mp.ty.utils.ClobUtil;
import com.baosight.bscdkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bscdkj.mp.wf.dto.SubProcessParam;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bscdkj.utils.BizIdUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.util.EiInfoUtil;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;

/**
 * <AUTHOR>
 *
 */
public class ServiceKTCGSB01 extends PageService {
	
	Logger logger = LoggerFactory.getLogger(this.getClass());
	
	@Autowired
	private BusinessKTCGNeed btn;
	@Autowired
	private BusinessKTCGTeachContract btc;
	
	
	//列表查询暂存的数据
	public EiInfo queryBNs(EiInfo inInfo) {
    	try {
			Map<String, Object> param = getQueryData(inInfo);
			param.put("applyStatus", KTCGConstants.status_cg);
			param.put("displayOrder", " UPDATE_DATE desc ");
			TableDataInfo tdi = btn.queryPage(param);
			return setPage(inInfo, tdi);
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
		    inInfo.setMsg(e.getMessage());
		    if(null!=e.getCause()) {
			    logger.error(e.getMessage(), e);
			    inInfo.setDetailMsg(e.getCause().getMessage());
		    } else {
		    	logger.error(e.getMessage());
		    }	
		}
		return inInfo;
    }
	//删除选中的暂存
	public EiInfo delete(EiInfo inInfo) {
		try {
			String operator = UserSession.getLoginName();
			System.out.println(inInfo.toJSONString());
			String recordGuid = inInfo.getCellStr("i", 0, "ids");
			if (StrUtil.isNotBlank(recordGuid)) {
				for (String id : recordGuid.split(",")) {
					btn.delete(operator,id);
				}
			}
			inInfo.setMsg("删除成功");
			inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		} catch (Exception e) {
			inInfo.setStatus(EiConstant.STATUS_FAILURE);
		    inInfo.setMsg(e.getMessage());
		    if(null!=e.getCause()) {
			    logger.error(e.getMessage(), e);
			    inInfo.setDetailMsg(e.getCause().getMessage());
		    } else {
		    	logger.error(e.getMessage());
		    }	
		}
		return inInfo;
	}
	//查询零星技术采购申请表详情
	public EiInfo queryTnDetail(EiInfo inInfo) {
		String needId = inInfo.getCellStr("i", 0, "businessGuid");
		Map<String,Object> retMap = new HashMap<String,Object>();
		
		queryTn(needId, retMap);
		TktcgTeachContract tc = btc.queryByNeedId(needId);
		retMap.put("tc", tc.toMap());
		
		WorkFlow workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(needId);
		if (workFlow != null) {
			retMap.put("processInstanceId", workFlow.getProcessInstanceId());
			retMap.put("currentOperator", workFlow.getCurrentOperator());
		} else {
			retMap.put("processInstanceId", "");
			retMap.put("currentOperator", "");
		}
		inInfo.addRow("r", retMap);
		return inInfo;

	}
	//查询合同审批表详情
	public EiInfo queryHtDetail(EiInfo inInfo) {
		String htId = inInfo.getCellStr("i", 0, "businessGuid");
		TktcgTeachContract tc = btc.load(htId);
		Map<String,Object> retMap = new HashMap<String,Object>();
		queryTc(tc.getNeedId(),retMap);
		WorkFlow workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(htId);
		if (workFlow != null) {
			retMap.put("processInstanceId", workFlow.getProcessInstanceId());
			retMap.put("currentOperator", workFlow.getCurrentOperator());
		} else {
			retMap.put("processInstanceId", "");
			retMap.put("currentOperator", "");
		}
		inInfo.addRow("r", retMap);
		return inInfo;
	}
	
	//申报评审方法
	public EiInfo queryKtcgSbps(EiInfo inInfo) {
		String needId = inInfo.getString("needId");
		if(StringUtils.isNotEmpty(needId)) {
			TktcgNeed tn = btn.load(needId);
			JSONObject jsonObject = new JSONObject();
			String jbxx="["
					+ "{'name':'模块类型','value':'零星采购'},"
					+ "{'name':'评审类型','value':'申报评审'},"
					+ "{'name':'项目名称','value':'"+tn.getProjectName()+"'},"
					+ "{'name':'提出人名称','value':'"+tn.getTcrUserName()+"'},"
					+ "{'name':'提出人联系电话','value':'"+tn.getTcrContactTel()+"'},"
					+ "{'name':'提出人所在部门','value':'"+tn.getDeptName()+"'},"
					+ "{'name':'采购类型','value':'"+tn.getNeedType()+"'}"
					+ "]";
			jsonObject.set(needId, JSONUtil.parseArray(jbxx));
        	System.out.println(jsonObject.toJSONString(0));
            String base64 = Base64.encodeUrlSafe (jsonObject.toJSONString(0) );
			inInfo.set("tn", base64);
		}else {
			inInfo.set("tn", "");
		}
		return inInfo;
	}
	//合同评审方法
	public EiInfo queryKtcgHtps(EiInfo inInfo) {
		String htId = inInfo.getString("htId");
		if(StringUtils.isNotEmpty(htId)) {
			TktcgTeachContract tc = btc.load(htId);
			TktcgNeed tn = btn.load(tc.getNeedId());
			JSONObject jsonObject = new JSONObject();
			String jbxx="["
					+ "{'name':'模块类型','value':'零星采购'},"
					+ "{'name':'评审类型','value':'合同评审'},"
					+ "{'name':'定价模式','value':'"+tc.getHtModel()+"'},"
					+ "{'name':'提出人名称','value':'"+tn.getTcrUserName()+"'},"
					+ "{'name':'提出人联系电话','value':'"+tn.getTcrContactTel()+"'},"
					+ "{'name':'提出人所在部门','value':'"+tn.getDeptName()+"'},"
					+ "{'name':'采购类型','value':'"+tn.getNeedType()+"'}"
					+ "]";
			jsonObject.set(htId, JSONUtil.parseArray(jbxx));
        	System.out.println(jsonObject.toJSONString(0));
            String base64 = Base64.encodeUrlSafe (jsonObject.toJSONString(0) );
			inInfo.set("tc", base64);
		}else {
			inInfo.set("tc", "");
		}
		return inInfo;
	}
	
	public EiInfo query(EiInfo inInfo) {
		String ac = inInfo.getCellStr("i", 0, "activityCode");
		String businessGuid = inInfo.getCellStr("i", 0, "businessGuid");//业务id 流程启动id tktcgneed表的主键
		Map<String,Object> retMap = new HashMap<String,Object>();
		TktcgNeed  tn = null;
		if(StringUtils.isNotEmpty(businessGuid))
			tn = btn.load(businessGuid);
		
		if(("CGSB01").equals(ac)) {//零星采购申请入口
			Map tnMap = new HashMap<>();
			if(StringUtils.isNotEmpty(businessGuid)) {
				tn = btn.query(businessGuid);
			}
			if(tn == null) {
				tn = new TktcgNeed();
				String needId = BizIdUtil.INSTANCE.nextId();
				if(StringUtils.isBlank(businessGuid))
					businessGuid = needId;
				tn.setNeedId(needId);
				tn.setTcrUserCode(UserSession.getLoginName());
				tn.setTcrUserName(UserSession.getLoginCName());
				ADOrg adorg = OrgUtil.getMainOrgByUserCode(UserSession.getLoginName());
				tn.setDeptCode(adorg.getOrgCode());
				tn.setDeptCodePath(adorg.getOrgPathCode());
				tn.setDeptName(adorg.getOrgPathName());
				
			}
			
			tnMap = tn.toMap();
			Map<String, Object> contentMap = new HashMap<String, Object>();
			contentMap.put("xyjjwtsm_ktcg_1", "");//通过引进需要解决主要问题说明
			contentMap.put("sshxg_ktcg_2", "");//	预计引进实施后效果（包括技术指标/效益等）
			contentMap.put("yjfy_ktcg_3", "");//预计引进费用（万元）（包括：知识产权费用、差旅费、材料费、资料费等）
			contentMap.put("jlqk_ktcg_4", "");
			tnMap.putAll(contentMap);
			Map<String, Object> contentMap2 = ClobUtil.getContentMap(tn.getNeedId());
			if(contentMap2!=null && contentMap2.size()!=0) {
				tnMap.putAll(contentMap2);
			}
			retMap.put("tn", tnMap);
		}else if(("CGSB02").equals(ac)) {//部门领导审批
			inInfo.set("psEnd",true);
			queryTn(businessGuid,retMap);
		}else if(("CGSB03").equals(ac)) {//项目主管确认
			inInfo.set("psEnd",false);
			EiInfo einfo = new EiInfo();
			einfo.set("bizId",tn.getNeedId());
			einfo.set("moduleCode","ktcg_zjps");
			einfo.set(EiConstant.serviceId, "S_MP_PS_02");
			EiInfo outInfo = XServiceManager.call(einfo);
			List<Map<String,Object>> list = (List<Map<String, Object>>) outInfo.get("list");
			if(list!=null && !list.isEmpty()){
			    Map<String, Object> psMap = list.get(0);
			    Object isEnd = psMap.get("isEnd");
			    if("1".equals(isEnd)){
			        inInfo.set("psEnd",true);
			    }
			}
			queryTn(businessGuid,retMap);
		}else if(("CGSB04").equals(ac)) {//业务主管审批
			inInfo.set("psEnd",true);
			queryTn(businessGuid,retMap);
		}else if(("CGSB05").equals(ac)) {//主管部门领导审核
			inInfo.set("psEnd",true);
			queryTn(businessGuid,retMap);
		}else if(("CGSB06").equals(ac)) {//合同登记
			queryTc(businessGuid,retMap);
		}else if(("CGSB07").equals(ac)) {//合同登记业务主管审批
			queryTc(businessGuid,retMap);
		}else if(("CGSB08").equals(ac)) {//主管部门领导审核
			queryTc(businessGuid,retMap);
		}else if(("CGSB09").equals(ac)) {//公司领导审批
			queryTc(businessGuid,retMap);
		}else if(("CGSB10").equals(ac)) {//公司领导审批
			queryTc(businessGuid,retMap);
		}
		retMap.put("businessGuid", tn.getNeedId());
		
		
		String comment = "";
		if(StringUtils.isNotEmpty(inInfo.getCellStr("i", 0, "taskId"))) {
			comment = SWorkFlowUtil.getComment(inInfo.getCellStr("i", 0, "taskId"));
		}
		inInfo.set("comment", comment);
		inInfo.addRow("r", retMap);
		inInfo.setStatus(1);
		return inInfo;
	}
	
	private void queryTn(String businessGuid, Map<String, Object> retMap) {
		TktcgNeed tn = btn.query(businessGuid);
		
		Map tnMap = tn.toMap();
		Map<String, Object> contentMap = new HashMap<String, Object>();
		Map<String, Object> contentMap2 = ClobUtil.getContentMap(tn.getNeedId());
		contentMap.put("xyjjwtsm_ktcg_1", "");//通过引进需要解决主要问题说明
		contentMap.put("sshxg_ktcg_2", "");//	预计引进实施后效果（包括技术指标/效益等）
		contentMap.put("yjfy_ktcg_3", "");//预计引进费用（万元）（包括：知识产权费用、差旅费、材料费、资料费等）
		contentMap.put("jlqk_ktcg_4", "");
		tnMap.putAll(contentMap);
		if(contentMap2!=null && contentMap2.size()!=0) {
			tnMap.putAll(contentMap2);
		}
		retMap.put("tn", tnMap);
	}
	
	private void queryTc(String businessGuid ,Map<String, Object> retMap) {
		TktcgNeed tn = btn.load(businessGuid);
		TktcgTeachContract tc = btc.queryByNeedId(businessGuid);
		
		Map tcMap = tc.toMap();
		Map tnMap = tn.toMap();
		Map<String, Object> contentMap = new HashMap<String, Object>();
		Map<String, Object> contentMap2 = ClobUtil.getContentMap(tc.getHtId());
		contentMap.put("htnrgs_1", "");//合同内容概述
		contentMap.put("jgqdyj_2", "");//价格确定依据
		tcMap.putAll(contentMap);
		if(contentMap2!=null && contentMap2.size()!=0) {
			tcMap.putAll(contentMap2);
		}
		retMap.put("tn", tnMap);
		retMap.put("tc", tcMap);
	}
	
	public EiInfo doSave(EiInfo inInfo) {
		String ac = inInfo.getCellStr("i", 0, "activityCode");
		if("CGSB01".equals(ac)) {
			saveCG01(inInfo);
		}else if("CGSB02".equals(ac)) {
		}else if("CGSB03".equals(ac)) {
		}else if("CGSB04".equals(ac)) {
		}else if("CGSB05".equals(ac)) {
		}else if("CGSB06".equals(ac)) {
			saveTc06(inInfo);
		}else if("CGSB07".equals(ac)) {
		}else if("CGSB08".equals(ac)) {
		}else if("CGSB09".equals(ac)) {
		}else if("CGSB10".equals(ac)) {
			saveTc10(inInfo);
		}
		//保存流程意见
		String taskId = inInfo.getCellStr("i", 0, "taskId");
		String comment = inInfo.getCellStr("i", 0, "comment");
		if(StringUtils.isNotEmpty(taskId) && StringUtils.isNotEmpty(comment)) {
			SWorkFlowUtil.updateComment(taskId, comment);
		}
		inInfo.setMsg("保存成功");
		return inInfo;
	}
	
	private void saveCG01(EiInfo inInfo) {
		TktcgNeed  tn = (TktcgNeed) EiInfoUtil.getBean(inInfo, "i", 0, TktcgNeed.class);
		tn.setApplyStatus(KTCGConstants.status_cg);//项目状态草稿
		
		if("1".equals(inInfo.getString("isSaveSubmit"))) {
			tn.setApplyStatus(KTCGConstants.status_sqz);//项目状态申请中
			String nextNum = SequenceGenerator.getNextSequence(KTCGConstants.ktcg_apply_num); 
			tn.setProjectNum(nextNum);
		}
		tn.setFzr(UserSession.getLoginName());
		tn.setFzrName(UserSession.getLoginCName());
		tn.setFzDeptCode(tn.getDeptCode());
		tn.setFzDeptName(tn.getDeptName());
		tn.setFzDeptPath(tn.getDeptCodePath());
		tn.setXmzgUserCode(RoleUtil.getUserLabel(KTCGConstants.ktcg_dw_xmzg,tn.getDeptCode()));
		ADOrg gldw = new ADOrg();
		try {
			gldw = RoleUtil.getOrgByChildOrg(tn.getDeptCode(),KTCGConstants.ktcg_dw_ywzg);
		} catch (Exception e) {
			throw e;
		}
		
		tn.setGldwCode(gldw.getOrgCode());
		tn.setGldwName(gldw.getOrgName());
		tn.setGldwCodePath(gldw.getOrgPathName());
		tn.setNeedYear(Integer.toString(new DateTime().year()));
		btn.save(UserSession.getLoginName(), tn);
		Map<String, Object> contentMap = new HashMap<String, Object>();
		contentMap.put("xyjjwtsm_ktcg_1",  inInfo.getCellStr("i", 0, "xyjjwtsm_ktcg_1"));//通过引进需要解决主要问题说明
		contentMap.put("sshxg_ktcg_2", inInfo.getCellStr("i", 0, "sshxg_ktcg_2"));//	预计引进实施后效果（包括技术指标/效益等）
		contentMap.put("yjfy_ktcg_3", inInfo.getCellStr("i", 0, "yjfy_ktcg_3"));//预计引进费用（万元）（包括：知识产权费用、差旅费、材料费、资料费等）
		contentMap.put("jlqk_ktcg_4", inInfo.getCellStr("i", 0, "jlqk_ktcg_4"));//预计引进费用（万元）（包括：知识产权费用、差旅费、材料费、资料费等）
		ClobUtil.save(contentMap, tn.getNeedId(), "t_ktcg_need", Constants.module_ktcg, UserSession.getLoginName());
		
		//附件
		SAttachmentUtil.addAttachmentMapsBySourceGuid(tn.getNeedId(),inInfo.getCellStr("i", 0, "attachmentId14"),"14");
	}
	
	private void saveTc06(EiInfo inInfo) {
		TktcgTeachContract tc = (TktcgTeachContract)EiInfoUtil.getBean(inInfo, "i", 0, TktcgTeachContract.class);
		btc.save(UserSession.getLoginName(), tc);
		
		Map<String, Object> contentMap = new HashMap<String, Object>();
		contentMap.put("htnrgs_1", inInfo.getCellStr("i", 0, "htnrgs_1"));//合同内容概述
		contentMap.put("jgqdyj_2", inInfo.getCellStr("i", 0, "jgqdyj_2"));//价格确定依据
		ClobUtil.save(contentMap, tc.getHtId(), "t_ktcg_teach_conteact", Constants.module_ktcg, UserSession.getLoginName());
		
		//附件
		SAttachmentUtil.addAttachmentMapsBySourceGuid(tc.getHtId(),inInfo.getCellStr("i", 0, "attachmentId15"),"15");
		
		TktcgNeed tn = btn.load(tc.getNeedId());
		//负责部门
		tn.setFzDeptCode(inInfo.getCellStr("i", 0, "fzDeptCode"));
		tn.setFzDeptName(inInfo.getCellStr("i", 0, "fzDeptName"));
		ADOrg adorg = OrgUtil.getMainOrgByUserCode(inInfo.getCellStr("i", 0, "fzr"));
		
		tn.setFzDeptPath(adorg.getOrgPathName());
		//负责人
		tn.setFzr(inInfo.getCellStr("i", 0, "fzr"));
		tn.setFzrName(inInfo.getCellStr("i", 0, "fzrName"));
		tn.setXmcjdeptCode(inInfo.getCellStr("i", 0, "xmcjdeptCode"));
		tn.setNeedYjf(inInfo.getCellStr("i", 0, "needYjf"));
		btn.save(UserSession.getLoginName(), tn);
	}
	
	private void saveTc10(EiInfo inInfo) {
		TktcgTeachContract tc = (TktcgTeachContract)EiInfoUtil.getBean(inInfo, "i", 0, TktcgTeachContract.class);
		TktcgTeachContract tcs = btc.query(tc.getHtId());
		tc.setHtCny(tcs.getHtCny());
		tc.setHtTotal(tcs.getHtTotal());
		tc.setHtCnyTotal(tcs.getHtCnyTotal());
		tc.setHtPrice(tcs.getHtPrice());
		btc.save(UserSession.getLoginName(), tc);
		//附件
		SAttachmentUtil.addAttachmentMapsBySourceGuid(tc.getHtId(),inInfo.getCellStr("i", 0, "attachmentId16"),"16");
		TktcgNeed tn = btn.load(tc.getNeedId());
		tn.setApplyStatus(KTCGConstants.status_yxz);
		btn.save(UserSession.getLoginName(), tn);
	}

	public EiInfo doSubmit(EiInfo inInfo) {
		String ac = inInfo.getCellStr("i", 0, "activityCode");
		String businessGuid = inInfo.getCellStr("i", 0, "businessGuid");
		String taskId = inInfo.getCellStr("i", 0, "taskId");
		String transitionKey = inInfo.getCellStr("i", 0, "transitionKey");
		String comment = inInfo.getCellStr("i", 0, "comment");
		String userLabelM = inInfo.getCellStr("i", 0, "userLabelM");
		SubProcessParam[] subProcessParamS = null;
		Map<String, Object> variables = new HashMap<String, Object>();
		TktcgNeed tn = null;
		if(businessGuid!=null)
			tn = btn.load(businessGuid);
		
		if("CGSB01".equals(ac)) {//零星技术采购申请提交
			inInfo.set("isSaveSubmit", "1");
			
			doSave(inInfo);
			if(taskId == "") {
				doStart(businessGuid);
				WorkFlow workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(businessGuid);
				if(!"".equals(workFlow.getTaskId()))
					taskId = workFlow.getTaskId();
			}
			tn = btn.load(businessGuid);
			transitionKey = "Transition2";
			variables.put("orgParamter", tn.getFzDeptCode());
		}else if("CGSB02".equals(ac)) {
			userLabelM = tn.getXmzgUserCode();
			variables.put("orgParamter", tn.getFzDeptCode());
		}else if("CGSB03".equals(ac)) {
			tn.setXmzgUserCode(UserSession.getLoginName());
			tn.setXmzgUserName(UserSession.getLoginCName());
			btn.save(UserSession.getLoginCName(), tn);
			variables.put("orgParamter", tn.getGldwCode());
		}else if("CGSB04".equals(ac)) {
			variables.put("orgParamter", tn.getGldwCode());
		}else if("CGSB05".equals(ac)) {
			userLabelM = tn.getXmzgUserCode();
			TktcgTeachContract tc = new TktcgTeachContract();
			tc.setNeedId(businessGuid);
			btc.save(UserSession.getLoginName(), tc);
			
			userLabelM = tn.getXmzgUserCode();
		}else if("CGSB06".equals(ac)) {
			doSave(inInfo);
			
			variables.put("orgParamter", tn.getGldwCode());
		}else if("CGSB07".equals(ac)) {
			variables.put("orgParamter", tn.getGldwCode());
		}else if("CGSB08".equals(ac)) {
			TktcgTeachContract tc = btc.queryByNeedId(tn.getNeedId());
			BigDecimal htCnyTotal = tc.getHtCnyTotal();
			if("gdjg".equals(tc.getHtModel())){
				htCnyTotal = tc.getHtTotal();
			}
			
			EiInfo eiInfo = new EiInfo();
			eiInfo.set("type", "ktcg");
			eiInfo.set("roleCode",KTCGConstants.ktcg_dw_zglg);
			eiInfo.set("orgCode",tn.getGldwCode());
			eiInfo.set(EiConstant.serviceId, "S_KY_XM_005"); 
			EiInfo outInfo =XServiceManager.call(eiInfo);
			BigDecimal sped = null;
			if("".equals(outInfo.get("sped"))) {
				sped = (BigDecimal) outInfo.get("sped");
			}else {
				sped = new BigDecimal(  outInfo.get("sped").toString()) ;
			}
			
			if(sped==null || (htCnyTotal!=null && htCnyTotal.compareTo(sped)>0)) {//超过授权额度
				transitionKey = "Transition11";
				variables.put("orgParamter", tn.getGldwCode());
			}else {
				transitionKey = "Transition9";
				userLabelM = tn.getXmzgUserCode();
			}
		}else if("CGSB09".equals(ac)) {
		}else if("CGSB10".equals(ac)) {
			doSave(inInfo);
			SWorkFlowUtil.endProecess(UserSession.getLoginName(), businessGuid, comment);
			String msg = "零星采购申请完成";
			inInfo.setStatus(EiConstant.STATUS_SUCCESS);
			inInfo.setMsg(msg);
			return inInfo;
		}
		//退回后提交到退回节点
		if(StringUtils.isNotEmpty(inInfo.getCellStr("i", 0, "transitionKey")))
			transitionKey = inInfo.getCellStr("i", 0, "transitionKey");
		
		
		
		String msg = SWorkFlowUtil.submit(UserSession.getLoginName(), businessGuid, taskId, transitionKey,comment, userLabelM, subProcessParamS, variables);
		if(StringUtils.isEmpty(msg) || msg.indexOf("结束")>-1) {
			msg = "零星采购申请完成";
		}
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg(msg);
		return inInfo;
	}

	public String doStart(String needId) {
		TktcgNeed tn = btn.load(needId);
		String processCode = KTCGConstants.ktcg_lx_apply;
		String comment = "启动立项流程";
		String userLableM = null;
		Map<String,Object> variable = new HashMap<String,Object>();
		String transitionKey = null;
		String msg = SWorkFlowUtil.startProcess(UserSession.getLoginName(), needId, tn.getProjectName(), Constants.module_ktcg,processCode, comment, userLableM, variable, transitionKey);
		return msg;
	}

	public EiInfo doReturn(EiInfo inInfo) {
		String ac = inInfo.getCellStr("i", 0, "activityCode");
		String businessGuid = inInfo.getCellStr("i", 0, "businessGuid");
		String taskId = inInfo.getCellStr("i", 0, "taskId");
		String transitionKey = null;
		String comment = inInfo.getCellStr("i", 0, "comment");
		String userLabelM = inInfo.getCellStr("i", 0, "userLabelM");
		SubProcessParam[] subProcessParamS = null;
		Map<String, Object> variables = new HashMap<String, Object>();
		String activityKey = inInfo.getCellStr("i", 0, "activityKey");
		
		//保存
		doSave(inInfo);
		
		if("CGSB01".equals(ac)) {//零星采购通知
		}else if("CGSB02".equals(ac)) {//申报部门领导审核
		}else if("CGSB03".equals(ac)) {//项目主管确认
		}else if("CGSB04".equals(ac)) {//业务主管审批
		}else if("CGSB05".equals(ac)) {//主管部门领导审批
		}else if("CGSB06".equals(ac)) {//合同登记
		}else if("CGSB07".equals(ac)) {//合同登记业务主管审批
		}else if("CGSB08".equals(ac)) {//合同登记主管部门领导审批
		}else if("CGSB09".equals(ac)) {//公司领导审批
		}else if("CGSB10".equals(ac)) {//合同信息
		}
		
		String msg = SWorkFlowUtil.doReturn(UserSession.getLoginName(), businessGuid, taskId, activityKey, comment);
		inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		inInfo.setMsg(msg);
		
		return inInfo;
	}
}
