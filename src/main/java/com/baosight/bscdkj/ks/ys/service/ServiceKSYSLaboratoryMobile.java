package com.baosight.bscdkj.ks.ys.service;


import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.baosight.bscdkj.common.constant.KSYSConstants;
import com.baosight.bscdkj.common.constant.YWFJConstants;
import com.baosight.bscdkj.common.ks.domain.*;
import com.baosight.bscdkj.common.service.PageService;
import com.baosight.bscdkj.ks.ys.business.*;
import com.baosight.bscdkj.ks.ys.common.KSYSUtils;
import com.baosight.bscdkj.mp.ad.utils.OrgUtil;
import com.baosight.bscdkj.mp.ty.dto.AttachmentMap;
import com.baosight.bscdkj.mp.ty.prop.AppContextProp;
import com.baosight.bscdkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 联合实验室移动端Service接口
 *
 * <AUTHOR>
 * @date 2022-11-21
 */
@Service
public class ServiceKSYSLaboratoryMobile extends PageService {

    @Autowired
    AppContextProp appContextProp;
    @Autowired
    private BusinessLaboratory businessLaboratory;
    @Autowired
    private BusinessLaboratoryApply businessLaboratoryApply;
    @Autowired
    private BusinessLaboratoryAgreement businessLaboratoryAgreement;
    @Autowired
    private BusinessLaboratoryReport businessLaboratoryReport;
    @Autowired
    private BusinessLaboratoryEnd businessLaboratoryEnd;
    @Autowired
    private BusinessLaboratoryChange businessLaboratoryChange;
    @Autowired
    private BusinessLaboratoryExcellent businessLaboratoryExcellent;
    @Autowired
    private BusinessKSYSSealBaseinfo businessKSYSSealBaseinfo;
    Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 查询联合实验室移动端需要的信息
     * 微服务:S_KS_YD_001
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryMoblieData(EiInfo inInfo) {
        try {
            //获取参数信息
            Map params = (Map) inInfo.get("params");
            String operator = inInfo.getString("operator");
            if (null == params) {
                throw new PlatException("缺少参数params");
            }
            String businessId = (String) params.get("businessId");
            if (StringUtils.isBlank(businessId)) {
                throw new PlatException("缺少业务ID");
            }
            String processCode = (String) params.get("processCode");
            String currentActivity = (String) params.get("currentActivity");
            Map<String, Object> data = new HashMap<>();
            List<AttachmentMap> fileList = new ArrayList<>();
            if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_ZJSQ)) {
                //联合实验室组建申请
                //根据businessid查询申请表信息
                TksysLaboratoryApply laboratoryApply = businessLaboratoryApply.query(businessId);
                data = BeanUtil.beanToMap(laboratoryApply);
                //需要展示联合实验室组建申请的附件
                if (StrUtil.equals(currentActivity, "Manual10") || StrUtil.equals(currentActivity, "Manual11")) {
                    //框架协议编制后的节点 附件需要显示框架协议表的附件
                    TksysLaboratoryAgreementEx laboratoryAgreementEx = businessLaboratoryAgreement.queryByLabId(laboratoryApply.getLabId());
                    fileList = SAttachmentUtil.getAttachmentBySourceIdAndSourceLabel(laboratoryAgreementEx.getRecordId(), "KSYS", "agreementFile", null, null);
                } else {
                    fileList = SAttachmentUtil.getAttachmentBySourceIdAndSourceLabel(laboratoryApply.getRecordId(), "KSYS", "applyFile", null, null);
                }
            } else if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_NDZJ)) {
                //联合实验室年度总结
                //根据businessid查询年度总结信息 并且查询联合实验室主表信息
                TksysLaboratoryReport laboratoryReport = businessLaboratoryReport.query(businessId);
                TksysLaboratory laboratory = businessLaboratory.query(laboratoryReport.getLabId());
                //需要展示的数据处理
                data = BeanUtil.beanToMap(laboratory);
                data.put("ndType", laboratoryReport.getFyear() + "年度" + laboratoryReport.getReportType() + "总结");
                //需要展示的年度总结附件
                fileList = SAttachmentUtil.getAttachmentBySourceIdAndSourceLabel(laboratoryReport.getRecordId(), "KSYS", "reportFile", null, null);
            } else if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_XYZZ)) {
                //联合实验室中止流程
                //根据businessid查询年度总结信息 并且查询联合实验室主表信息
                TksysLaboratoryEnd laboratoryEnd = businessLaboratoryEnd.query(businessId);
                TksysLaboratory laboratory = businessLaboratory.query(laboratoryEnd.getLabId());
                data = BeanUtil.beanToMap(laboratory);
                data.put("reason", laboratoryEnd.getReason());
                //需要展示的联合实验室中止附件
                fileList = SAttachmentUtil.getAttachmentBySourceIdAndSourceLabel(laboratoryEnd.getRecordId(), "KSYS", "suspensionAgreement", null, null);
            } else if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_FZRBG)) {
                //联合实验室负责人变更
                TksysLaboratoryChange laboratoryChange = businessLaboratoryChange.query(businessId);
                TksysLaboratory laboratory = businessLaboratory.query(laboratoryChange.getLabId());
                data = BeanUtil.beanToMap(laboratory);
                data.put("reason", laboratoryChange.getReason());
            } else if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_YXPX)) {
                //联合实验室优秀评选信息
                TksysLaboratoryExcellent laboratoryExcellent = businessLaboratoryExcellent.query(businessId);
                TksysLaboratory laboratory = businessLaboratory.query(laboratoryExcellent.getLabId());
                TksysLaboratoryReport laboratoryReport = businessLaboratoryReport.query(laboratoryExcellent.getReportId());
                data = BeanUtil.beanToMap(laboratory);
                data.put("ndType", laboratoryReport.getFyear() + "年度" + laboratoryReport.getReportType() + "总结");
                data.put("finalScore", laboratoryExcellent.getFinalScore());
                data.put("rewardAmount", laboratoryExcellent.getRewardAmount() + "(元)");
            } else {
                throw new PlatException(processCode + "暂不支持");
            }
            inInfo.set(EiConstant.resultBlock, getMobileContent(operator, data, processCode, fileList));
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 获取移动端联合实验室批信息(包括业务表主要信息 附件信息)
     *
     * @param operator    当前操作人
     * @param data        流程对应的数据
     * @param processCode 流程编码
     * @param fileList    附件
     * @return
     */
    private List<Map<String, Object>> getMobileContent(String operator, Map<String, Object> data, String processCode, List<AttachmentMap> fileList) {
        String[] show = new String[0];
        if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_ZJSQ)) {
            //联合实验室组建申请需要显示的字段
            show = new String[]{"labName:联合实验室名称", "tcDeptName:申报单位", "tcrName:申报人", "hzDept:拟合作单位"};
        } else if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_NDZJ)) {
            //联合实验室年度总结需要显示的字段
            show = new String[]{"labName:联合实验室名称", "xmzgName:项目主管", "fzrName:项目负责人", "ndType:年度"};
        } else if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_XYZZ)) {
            //联合实验室年协议中止需要显示的字段
            show = new String[]{"labName:联合实验室名称", "xmzgName:项目主管", "fzrName:项目负责人", "reason:中止原因"};
        } else if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_FZRBG)) {
            //联合实验室负责人变更需要显示的字段
            show = new String[]{"labName:联合实验室名称", "xmzgName:项目主管", "fzrName:原项目负责人", "reason:负责人变更原因"};
        } else if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_YXPX)) {
            //优秀联合实验室评选需要显示的字段
            show = new String[]{"labName:联合实验室名称", "xmzgName:项目主管", "fzrName:项目负责人", "ndType:年度", "finalScore:总分", "rewardAmount:奖励金额"};
        } else {
            throw new PlatException(processCode + "暂不支持");
        }
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, Object> column = null;
        int order = 0;//序号
        for (int i = 0; i < show.length; i++) {
            order++;
            String[] _column = show[i].split(":");
            column = new HashMap<>();
            column.put("code", _column[0]);
            column.put("name", _column[1]);
            column.put("type", "text");
            column.put("order", order);
            column.put("value", data.get(_column[0]));
            result.add(column);
        }

        //需要展示的附件
        if (ObjectUtil.isNotEmpty(fileList)) {
            for (AttachmentMap attachmentMap : fileList) {
                order++;
                column = new HashMap<>();
                column.put("code", "");
                //可以根据AttachmentMap中的字段具体指定名称
                column.put("name", "附件");
                //在线预览的附件类型 固定
                column.put("type", "docUrl");
                column.put("order", order);
                //附件名称
                column.put("value", attachmentMap.getAttachmentName());
                //附件链接，固定
                column.put("url", appContextProp.getCtxGGMK() + "attachment/nmDownload/" + attachmentMap.getAttachmentId() + "?isMobile=true&operator=" + operator);
                result.add(column);
            }
        }
        return result;
    }


    /**
     * 联合实验室提交流程(默认提交无法满需求)
     * 微服务:S_KS_YD_002
     *
     * @param inInfo
     * @return
     */
    public EiInfo submitWorkFlow(EiInfo inInfo) {
        try {
            //参数信息
            String operator = (String) inInfo.get("operator");
            if (StrUtil.isBlank(operator)) {
                throw new PlatException("operator不能为空");
            }
            Map<String, Object> postData = (Map<String, Object>) inInfo.get("postData");
            WorkFlow workFlow = BeanUtil.toBean(postData, WorkFlow.class);
            //业务主键
            String businessId = workFlow.getBusinessId();
            //流程编码
            String processCode = workFlow.getProcessCode();
            //当前节点
            String currentActivity = workFlow.getCurrentActivity();
            //流程参数
            HashMap<String, Object> variables = new HashMap<>();
            //下一步流转人
            String userLable = null;
            if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_ZJSQ)) {
                //查询联合实验室申请表
                TksysLaboratoryApply laboratoryApply = businessLaboratoryApply.query(businessId);
                //联合实验室组建申请流程
                if (StrUtil.equals(currentActivity, "Manual2")) {
                    //判断申报单位是否是研究院
                    if (KSYSUtils.isResearch(laboratoryApply.getTcDeptCodePath())) {
                        //研究院 研究院长节点审批Manual3
                        variables.put("isResearch", 1);
                        variables.put("orgParamter", laboratoryApply.getTcDeptCode());
                    } else {
                        //非研究院
                        //判断是否是提出部门是否是总部
                        variables.put("isResearch", 0);
                        if (KSYSUtils.isHeadquarters(laboratoryApply.getTcDeptCodePath())) {
                            //总部 到公司综合主管节点审批Manual5
                            variables.put("isZongBu", 1);
                        } else {
                            //非总部(基地) 到单位策划主管节点审批Manual4
                            variables.put("isZongBu", 0);
                        }
                        variables.put("orgParamter", laboratoryApply.getTcDeptCode());
                    }
                } else if (StrUtil.equals(currentActivity, "Manual3")) {
                    //判断是否是提出部门是否是总部
                    if (KSYSUtils.isHeadquarters(laboratoryApply.getTcDeptCodePath())) {
                        //总部 到公司综合主管节点审批Manual5
                        variables.put("isZongBu", 1);
                    } else {
                        //非总部(基地) 到单位策划主管节点审批Manual4
                        variables.put("isZongBu", 0);
                    }
                    variables.put("orgParamter", laboratoryApply.getTcDeptCode());
                } else if (StrUtil.equals(currentActivity, "Manual10")) {
                    variables.put("orgParamter", laboratoryApply.getTcDeptCode());
                } else if (StrUtil.equals(currentActivity, "Manual11")) {
                    workFlow.setUserLabelM(laboratoryApply.getXmzgCode());
                    //公司领导同意后 用印申请签主管部门的领导的章
                    TksysSealBaseinfo sealBaseinfo = businessKSYSSealBaseinfo.queryByLabId(laboratoryApply.getLabId());
                    //用印申请 状态 改为2 签字
                    sealBaseinfo.setStatus("2");
                    businessKSYSSealBaseinfo.update(operator, sealBaseinfo);
                }
            } else if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_NDZJ)) {
                //联合实验室年度总结信息
                TksysLaboratoryReport laboratoryReport = businessLaboratoryReport.query(businessId);
                //联合实验室信息
                TksysLaboratory laboratory = businessLaboratory.query(laboratoryReport.getLabId());
                //联合实验室年度总结流程
                if (StrUtil.equals(currentActivity, "Manual2")) {
                    //判断是全年还是半年总结
                    if (StrUtil.equals(laboratoryReport.getReportType(), KSYSConstants.reportType_haflYear)) {
                        //半年直接提交给项目主管进行评价
                        variables.put("isYear", 0);
                    } else {
                        //全年项目主管组织评审
                        variables.put("isYear", 1);
                    }
                    userLable = laboratory.getXmzgCode();
                }
            } else if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_YXPX)) {
                //优秀联合实验室评选信息
                TksysLaboratoryExcellent laboratoryExcellent = businessLaboratoryExcellent.query(businessId);
                //联合实验室主表
                TksysLaboratory laboratory = businessLaboratory.query(laboratoryExcellent.getLabId());
                //联合实验室年度总结
                TksysLaboratoryReport laboratoryReport = businessLaboratoryReport.query(laboratoryExcellent.getReportId());
                if (StrUtil.equals(currentActivity, "Manual3")) {
                    //判断 建议奖励金额
                    if (laboratoryExcellent.getRewardAmount().compareTo(new BigDecimal(100000)) > -1) {
                        //大于10万
                        variables.put("reward10", 1);
                    } else {
                        variables.put("reward10", 0);
                        //小于10万结束流程
                        TksysLaboratoryExcellent laboratoryExcellentUpdate = new TksysLaboratoryExcellent();
                        laboratoryExcellentUpdate.setRecordId(laboratoryExcellent.getRecordId());
                        laboratoryExcellentUpdate.setStatus(KSYSConstants.ENDED);
                        businessLaboratoryExcellent.update(operator, laboratoryExcellentUpdate);
                        //需要开启发奖流程
                        startFj(operator, businessId);
                    }
                    variables.put("orgParamter", laboratory.getTcDeptCode());
                } else if (StrUtil.equals(currentActivity, "Manual4")) {
                    TksysLaboratoryExcellent laboratoryExcellentUpdate = new TksysLaboratoryExcellent();
                    laboratoryExcellentUpdate.setRecordId(laboratoryExcellent.getRecordId());
                    laboratoryExcellentUpdate.setStatus(KSYSConstants.ENDED);
                    businessLaboratoryExcellent.update(operator, laboratoryExcellentUpdate);
                    //需要开启发奖流程
                    startFj(operator, businessId);
                }
            } else if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_FZRBG)) {
                //获取变更信息
                TksysLaboratoryChange query = businessLaboratoryChange.query(workFlow.getBusinessId());
                //根据联合实验室d查询出联合实验室主表
                TksysLaboratory laboratory = businessLaboratory.query(query.getLabId());
                if (StrUtil.equals(currentActivity, "Manual2")) {
                    workFlow.setUserLabelM(laboratory.getXmzgCode());
                }
            } else if (StrUtil.equals(processCode, KSYSConstants.PROCESS_CODE_XYZZ)) {
                //获取中止信息
                TksysLaboratoryEnd query = businessLaboratoryEnd.query(workFlow.getBusinessId());
                //根据联合实验室d查询出联合实验室主表
                TksysLaboratory laboratory = businessLaboratory.query(query.getLabId());
                if (StrUtil.equals(currentActivity, "Manual2")) {
                    workFlow.setUserLabelM(laboratory.getXmzgCode());
                }
            } else {
                throw new PlatException(processCode + "暂不支持");
            }
            //设置流程参数 提交流程
            workFlow.setVariable(variables);
            workFlow.setUserLabelM(userLable);
            String msg = SWorkFlowUtil.submit(operator, workFlow);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    private String startFj(String operator, String businessId) {
        //获取最新的优秀实验室信息
        TksysLaboratoryExcellent laboratoryExcellent = businessLaboratoryExcellent.query(businessId);
        TksysLaboratory laboratory = businessLaboratory.query(laboratoryExcellent.getLabId());

        //启动发奖流程前插入业务数据,返回业务主表主键, map中的key需一致，不能更改，并且必需
        Map<String, Object> mainYwzb = new HashMap<>();
        mainYwzb.put("ywmainYwmk", "KSYS"); //业务模块
        mainYwzb.put("ywmainYwlb", YWFJConstants.YWFJ_YWLX_24); ////业务类型, 结题奖、优秀团队、利润分享等
        mainYwzb.put("ywlxId", laboratoryExcellent.getRecordId()); //业务类型主键，自己的项目主键、申报主键等都可以，可用于调自己接口数据
        mainYwzb.put("ywmainJllb", ""); //奖励类别 非必需
        mainYwzb.put("ywmainYemc", laboratory.getLabName()); //业务名称 流程业务名称
        mainYwzb.put("ywmainYebh", laboratory.getSerialNo()); //业务编号 可以是项目号
        mainYwzb.put("ywmainDwcodw", laboratory.getTcDeptCode()); //负责单位code 组织编码
        mainYwzb.put("ywmainDw", OrgUtil.getOrgPathName(laboratory.getTcDeptCode())); //单位名称
        mainYwzb.put("ywmainFzrgh", laboratory.getFzrCode()); //项目负责人工号
        mainYwzb.put("ywmainFzr", laboratory.getFzrName()); //项目负责人名称
        mainYwzb.put("ywmainYwzggh", laboratory.getXmzgCode()); //业务主管工号
        mainYwzb.put("ywmainYwzg", laboratory.getXmzgName()); //业务主管名称
        mainYwzb.put("ywmainKfpje", laboratoryExcellent.getRewardAmount()); //可分配金额，实发奖励金额 单位元
        //mainYwzb.put("ywmainYwlink", appContextProp.getCtxZZZC() + "ktjc/projectEndReport/detail/                                                                                                                                           " + projectEndReport.getRecordGuid()); //业务链接，可跳转自己业务详情页面
        mainYwzb.put("ywmainStatus", YWFJConstants.YWFJ_JLFPSC_STATUS_01); //状态，待生成

        EiInfo xInInfo = new EiInfo();
        xInInfo.set("operator", operator);
        xInInfo.set("mainYwzb", mainYwzb);
        //调用插入业务数据远程微服务
        xInInfo.set(EiConstant.serviceId, "S_YW_FJ_01");
        EiInfo outInfo = XServiceManager.call(xInInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        String ywmainId = (String) outInfo.get("ywmainId");

        //调用启动发奖微服务 businessId 为调用上一微服务返回的业务主表主键
        xInInfo.set("businessId", ywmainId);
        xInInfo.set(EiConstant.serviceId, "S_YW_FJ_02");
        outInfo = XServiceManager.call(xInInfo);
        if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
            logger.error(outInfo.toJSONString());
            throw new PlatException(outInfo.getMsg());
        }
        return "发奖流程已启动：" + outInfo.getMsg();
    }
}
