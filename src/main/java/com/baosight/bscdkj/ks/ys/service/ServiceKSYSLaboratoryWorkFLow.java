package com.baosight.bscdkj.ks.ys.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bscdkj.common.constant.KLRFConstants;
import com.baosight.bscdkj.common.constant.KSYSConstants;
import com.baosight.bscdkj.common.constant.YWFJConstants;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.kg.domain.*;
import com.baosight.bscdkj.common.ks.domain.*;
import com.baosight.bscdkj.common.service.PageService;
import com.baosight.bscdkj.kg.zs.business.BusinessKGZSLaboratory;
import com.baosight.bscdkj.kg.zs.business.BusinessKGZSLaboratoryApply;
import com.baosight.bscdkj.kg.zs.business.BusinessKGZSLaboratoryTarget;
import com.baosight.bscdkj.kg.zs.business.BusinessKGZSLaboratoryUnit;
import com.baosight.bscdkj.ks.ys.business.*;
import com.baosight.bscdkj.ks.ys.common.KSYSUtils;
import com.baosight.bscdkj.kt.cg.service.UiService;
import com.baosight.bscdkj.mp.ad.dto.ADOrg;
import com.baosight.bscdkj.mp.ad.utils.OrgUtil;
import com.baosight.bscdkj.mp.ad.utils.RoleUtil;
import com.baosight.bscdkj.mp.ad.utils.UserUtil;
import com.baosight.bscdkj.mp.ty.dto.AttachmentMap;
import com.baosight.bscdkj.mp.ty.utils.ClobUtil;
import com.baosight.bscdkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bscdkj.mp.ty.utils.SDictUtil;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bscdkj.utils.BizIdUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import com.baosight.iplat4j.ed.util.SequenceGenerator;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.hssf.record.DVALRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 联合实验室申请流程Service接口
 *
 * <AUTHOR>
 * @date 2022-03-17
 */
@Service
public class ServiceKSYSLaboratoryWorkFLow extends PageService {
    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BusinessLaboratoryApply businessLaboratoryApply;

    @Autowired
    private BusinessLaboratory businessLaboratory;

    @Autowired
    private BusinessLaboratoryAgreement businessLaboratoryAgreement;

    @Autowired
    private BusinessLaboratoryReport businessLaboratoryReport;

    @Autowired
    private BusinessLaboratoryRenew businessLaboratoryRenew;

    @Autowired
    private BusinessKGZSLaboratory businessKGZSLaboratory;

    @Autowired
    private BusinessKGZSLaboratoryApply businessKGZSLaboratoryApply;

    @Autowired
    private BusinessLaboratoryUnit businessLaboratoryUnit;

    @Autowired
    private BusinessLaboratoryTarget businessLaboratoryTarget;

    @Autowired
    private BusinessKGZSLaboratoryUnit businessKGZSLaboratoryUnit;

    @Autowired
    private BusinessKGZSLaboratoryTarget businessKGZSLaboratoryTarget;

    @Autowired
    private BusinessLaboratoryEnd businessLaboratoryEnd;

    @Autowired
    private BusinessLaboratoryChange businessLaboratoryChange;

    @Autowired
    private BusinessLaboratoryExcellent businessLaboratoryExcellent;

    @Autowired
    private BusinessKSYSSealBaseinfo businessKSYSSealBaseinfo;

    /**
     * 保存并开启联合实验室组建申请流程
     *
     * @param inInfo
     * @return
     */
    public EiInfo startFlow(EiInfo inInfo) {
        try {
            //获取参数
            TksysLaboratoryApplyEx laboratoryApplyEx = (TksysLaboratoryApplyEx) inInfo.get("laboratoryApplyEx");
            //当前登录人
            String loginName = UserSession.getLoginName();
            //新增或者修改联合实验室组建申请信息 将运行状态修改为 运行
            laboratoryApplyEx.setStatus(KSYSConstants.ACTIVED);
            //流程即将开启 设置流水号
            laboratoryApplyEx.setSerialNo(SequenceGenerator.getNextSequence("KSYS_ZJSQ_SERIALNO"));
            String recordId = businessLaboratoryApply.insertAll(loginName, laboratoryApplyEx);
            HashMap<String, Object> variables = new HashMap<>();
            variables.put("orgParamter", laboratoryApplyEx.getTcDeptCode());
            //开启流程
            String msg = SWorkFlowUtil.startProcessToActivity(loginName, recordId, laboratoryApplyEx.getLabName(), KSYSConstants.Business_Type, KSYSConstants.PROCESS_CODE_ZJSQ, "Manual2", null, null, variables, null);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 取联合实验室组建申请当前登陆用户的待办
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryZJSQDB(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            // 获取数据
            TableDataInfo tdi = businessLaboratoryApply.queryDB(UserSession.getLoginName(), queryData);
            setPage(inInfo, tdi);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 联合实验室组建申请待办详细
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryDBZJSQDetail(EiInfo inInfo) {
        try {
            String recordId = (String) inInfo.get("businessId");
            String taskId = inInfo.getString("taskId");
            //查询联合实验室主表加子表信息
            TksysLaboratoryApplyEx tksysLaboratoryApplyEx = businessLaboratoryApply.queryAll(recordId);

            //流程属性
            WorkFlow workFlow = new WorkFlow();
            if (StringUtils.isBlank(taskId)) {
                String operator = UserSession.getLoginName();
                workFlow = new WorkFlow(operator, KSYSConstants.PROCESS_CODE_ZJSQ, KSYSConstants.Business_Type, recordId);
            } else {
                workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
            }
            tksysLaboratoryApplyEx.setWorkFlow(workFlow);
            if (StrUtil.equals("Manual7", workFlow.getCurrentActivity())) {
                //判断评审是否结束
                EiInfo einfo = new EiInfo();
                einfo.set("bizId", recordId);
                einfo.set("moduleCode", "ksys_zjsq");
                einfo.set(EiConstant.serviceId, "S_MP_PS_02");
                EiInfo outInfo = XServiceManager.call(einfo);
                List<Map<String, Object>> list = (List<Map<String, Object>>) outInfo.get("list");
                if (list != null && !list.isEmpty()) {
                    Map<String, Object> psMap = list.get(0);
                    Object isEnd = psMap.get("isEnd");
                    if ("1".equals(isEnd)) {
                        inInfo.set("psEnd", true);
                    }
                }
            }
            if (StrUtil.equals("Manual9", workFlow.getCurrentActivity()) || StrUtil.equals("Manual10", workFlow.getCurrentActivity()) || StrUtil.equals("Manual11", workFlow.getCurrentActivity()) ||
                    StrUtil.equals("Manual12", workFlow.getCurrentActivity())) {
                //如果在 Manual9 Manual10 Manual11 Manual12四个节点 需要额外查询联合实验室框架协议表信息
                TksysLaboratoryAgreementEx laboratoryAgreementEx = businessLaboratoryAgreement.queryByLabId(tksysLaboratoryApplyEx.getLabId());
                inInfo.set("laboratoryAgreementEx", laboratoryAgreementEx);
            }

            //获取上一步操作 判断上一步操作是否是退回操作
            boolean isReturn = false;
            List<Map<String, String>> nextTransitionList = SWorkFlowUtil.getNextTransition(taskId, null);
            for (Map<String, String> nextTransition : nextTransitionList) {
                String transitionKey = nextTransition.get("transitionKey");
                isReturn = StrUtil.contains(transitionKey, "jumpReturnNode@@");
                if (isReturn) {
                    break;
                }
            }
            if (StrUtil.equals("Manual8", workFlow.getCurrentActivity()) && isReturn) {
                //如果在 Manual8 并且上一步是退回操作
                TksysLaboratoryAgreementEx laboratoryAgreementEx = businessLaboratoryAgreement.queryByLabId(tksysLaboratoryApplyEx.getLabId());
                inInfo.set("laboratoryAgreementEx", laboratoryAgreementEx);
                inInfo.set("isReturn", "1");
            } else {
                inInfo.set("isReturn", "0");
            }

            inInfo.set("laboratoryApplyEx", tksysLaboratoryApplyEx);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 联合实验室组建申请提交流程
     *
     * @param inInfo
     * @return
     */
    public EiInfo doSubmit(EiInfo inInfo) {
        try {
            //当前登录人
            String operator = UserSession.getLoginName();
            TksysLaboratoryApplyEx tksysLaboratoryApplyEx = (TksysLaboratoryApplyEx) inInfo.get("tksysLaboratoryApplyEx");
            //流程属性信息
            WorkFlow workFlow = tksysLaboratoryApplyEx.getWorkFlow();
            //根据业务主键获取 联合实验室申请主表信息
            TksysLaboratoryApply tksysLaboratoryApply = businessLaboratoryApply.query(tksysLaboratoryApplyEx.getRecordId());
            //当前节点
            String currentActivity = workFlow.getCurrentActivity();
            //流程参数
            HashMap<String, Object> variables = new HashMap<>();
            //节点判断
            if (StrUtil.equals(currentActivity, "Manual1")) {
                //更新信息
                businessLaboratoryApply.insertAll(operator, tksysLaboratoryApplyEx);
                variables.put("orgParamter", tksysLaboratoryApplyEx.getTcDeptCode());
            } else if (StrUtil.equals(currentActivity, "Manual2")) {
                //判断申报单位是否是研究院
                if (KSYSUtils.isResearch(tksysLaboratoryApply.getTcDeptCodePath())) {
                    //研究院 研究院长节点审批Manual3
                    variables.put("isResearch", 1);
                    variables.put("orgParamter", tksysLaboratoryApply.getTcDeptCode());
                } else {
                    //非研究院
                    //判断是否是提出部门是否是总部
                    variables.put("isResearch", 0);
                    if (KSYSUtils.isHeadquarters(tksysLaboratoryApply.getTcDeptCodePath())) {
                        //总部 到公司综合主管节点审批Manual5
                        variables.put("isZongBu", 1);
                    } else {
                        //非总部(基地) 到单位策划主管节点审批Manual4
                        variables.put("isZongBu", 0);
                    }
                    variables.put("orgParamter", tksysLaboratoryApply.getTcDeptCode());
                }
            } else if (StrUtil.equals(currentActivity, "Manual3")) {
                //判断是否是提出部门是否是总部
                if (KSYSUtils.isHeadquarters(tksysLaboratoryApply.getTcDeptCodePath())) {
                    //总部 到公司综合主管节点审批Manual5
                    variables.put("isZongBu", 1);
                } else {
                    //非总部(基地) 到单位策划主管节点审批Manual4
                    variables.put("isZongBu", 0);
                }
                variables.put("orgParamter", tksysLaboratoryApply.getTcDeptCode());
            } else if (StrUtil.equals(currentActivity, "Manual4")) {
                variables.put("orgParamter", tksysLaboratoryApply.getTcDeptCode());
            } else if (StrUtil.equals(currentActivity, "Manual5")) {
                //公司综合主管节点指定 指定项目主管 保存项目主管到联合实验室申请表
                businessLaboratoryApply.update(operator, tksysLaboratoryApplyEx);
                //更新联合实验室表
                TksysLaboratory tksysLaboratory = new TksysLaboratory();
                tksysLaboratory.setRecordId(tksysLaboratoryApplyEx.getLabId());
                tksysLaboratory.setXmzgCode(tksysLaboratoryApplyEx.getXmzgCode());
                tksysLaboratory.setXmzgName(tksysLaboratoryApplyEx.getXmzgName());
                businessLaboratory.update(operator, tksysLaboratory);
                //流转给 下一步 项目指定的主管
                workFlow.setUserLabelM(tksysLaboratoryApplyEx.getXmzgCode());
            } else if (StrUtil.equals(currentActivity, "Manual6")) {
                //指定负责人节点 保存负责人
                businessLaboratoryApply.update(operator, tksysLaboratoryApplyEx);
                TksysLaboratory tksysLaboratory = new TksysLaboratory();
                tksysLaboratory.setRecordId(tksysLaboratoryApplyEx.getLabId());
                tksysLaboratory.setFzrName(tksysLaboratoryApplyEx.getFzrName());
                tksysLaboratory.setFzrCode(tksysLaboratoryApplyEx.getFzrCode());
                businessLaboratory.update(operator, tksysLaboratory);
                workFlow.setUserLabelM(tksysLaboratoryApply.getXmzgCode());
            } else if (StrUtil.equals(currentActivity, "Manual7")) {
                //判断评审结果
                if (StrUtil.equals(tksysLaboratoryApplyEx.getReviewFlag(), "1")) {
                    //同意 流程状态不变 审批意见更新为同意
                    variables.put("isAgree", 1);
                    workFlow.setUserLabelM(tksysLaboratoryApply.getFzrCode());
                    //判断是否填写了用印申请
                    TksysSealBaseinfo sealBaseinfo = businessKSYSSealBaseinfo.queryByLabId(tksysLaboratoryApply.getLabId());
                    if (ObjectUtil.isEmpty(sealBaseinfo)) {
                        throw new PlatException("请填写用印申请");
                    }
                } else if (StrUtil.equals(tksysLaboratoryApplyEx.getReviewFlag(), "0")) {
                    //不同意 流程状态设置为结束 审批结果设置为不同意 实验室状态设置为已终止
                    variables.put("isAgree", 0);
                    TksysLaboratory tksysLaboratory = new TksysLaboratory();
                    tksysLaboratory.setRecordId(tksysLaboratoryApplyEx.getLabId());
                    tksysLaboratory.setStatus(KSYSConstants.terminated);
                    businessLaboratory.update(operator, tksysLaboratory);

                    tksysLaboratoryApplyEx.setStatus(KSYSConstants.ENDED);
                } else if (StrUtil.equals(tksysLaboratoryApplyEx.getReviewFlag(), "2")) {
                    //不同意,转联合工作室 流程状态设置为结束 审批结果设置为不同意,转工作室 实验室状态设置为已终止
                    variables.put("isAgree", 0);
                    TksysLaboratory tksysLaboratory = new TksysLaboratory();
                    tksysLaboratory.setRecordId(tksysLaboratoryApplyEx.getLabId());
                    tksysLaboratory.setStatus(KSYSConstants.terminated);
                    businessLaboratory.update(operator, tksysLaboratory);
                    tksysLaboratoryApplyEx.setStatus(KSYSConstants.ENDED);
                    //将当前联合实验室转成联合工作室为草稿状态保存
                    insterGzs(workFlow.getBusinessId());
                }
                businessLaboratoryApply.update(operator, tksysLaboratoryApplyEx);
            } else if (StrUtil.equals(currentActivity, "Manual8")) {
                String agreementGuid = tksysLaboratoryApplyEx.getAgreementGuid();

                TksysLaboratoryAgreementEx tksysLaboratoryAgreementEx = new TksysLaboratoryAgreementEx();
                tksysLaboratoryAgreementEx.setLabId(tksysLaboratoryApply.getLabId());
                tksysLaboratoryAgreementEx.setGoal(tksysLaboratoryApplyEx.getGoal());
                tksysLaboratoryAgreementEx.setContent(tksysLaboratoryApplyEx.getContent());
                tksysLaboratoryAgreementEx.setBmZscq(tksysLaboratoryApplyEx.getBmZscq());
                tksysLaboratoryAgreementEx.setAgreementDraft(tksysLaboratoryApplyEx.getAgreementDraft());
                tksysLaboratoryAgreementEx.setOperationSystem(tksysLaboratoryApplyEx.getOperationSystem());
                tksysLaboratoryAgreementEx.setExtra1(tksysLaboratoryApplyEx.getExtra1());
                tksysLaboratoryAgreementEx.setLaboratoryUnitList(tksysLaboratoryApplyEx.getLaboratoryUnitList());
                if (StrUtil.isEmpty(agreementGuid)) {
                    //编制框架协议表保存 框架协议表 处理数据 (新增)
                    tksysLaboratoryAgreementEx.setRecordId(BizIdUtil.INSTANCE.nextId());
                    businessLaboratoryAgreement.insertAll(UserSession.getLoginName(), tksysLaboratoryAgreementEx);
                } else {
                    //编制框架协议表保存 框架协议表 处理数据(更新)
                    tksysLaboratoryAgreementEx.setRecordId(agreementGuid);
                    businessLaboratoryAgreement.updateAll(UserSession.getLoginName(), tksysLaboratoryAgreementEx);
                }

                variables.put("orgParamter", tksysLaboratoryApply.getTcDeptCode());
            } else if (StrUtil.equals(currentActivity, "Manual9")) {
                variables.put("orgParamter", tksysLaboratoryApply.getTcDeptCode());
            } else if (StrUtil.equals(currentActivity, "Manual10")) {
                variables.put("orgParamter", tksysLaboratoryApply.getTcDeptCode());
            } else if (StrUtil.equals(currentActivity, "Manual11")) {
                workFlow.setUserLabelM(tksysLaboratoryApply.getXmzgCode());
                //公司领导同意后 用印申请签主管部门的领导的章
                TksysSealBaseinfo sealBaseinfo = businessKSYSSealBaseinfo.queryByLabId(tksysLaboratoryApply.getLabId());
                //用印申请 状态 改为2 签字
                sealBaseinfo.setStatus("2");
                businessKSYSSealBaseinfo.update(operator, sealBaseinfo);
            } else if (StrUtil.equals(currentActivity, "Manual12")) {
                //更新联合实验室框架协议表
                TksysLaboratoryAgreement tksysLaboratoryAgreement = new TksysLaboratoryAgreement();
                tksysLaboratoryAgreement.setRecordId(tksysLaboratoryApplyEx.getAgreementGuid());
                tksysLaboratoryAgreement.setCycle(tksysLaboratoryApplyEx.getCycle());
                tksysLaboratoryAgreement.setStartDate(tksysLaboratoryApplyEx.getStartDate());
                tksysLaboratoryAgreement.setEndDate(tksysLaboratoryApplyEx.getEndDate());

                //计算并保存续约时间 将结束时间转换为Date
                Date endDate = new Date();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");

                if (StrUtil.equals(tksysLaboratoryApplyEx.getCycle(), "0")) {
                    endDate = simpleDateFormat.parse(tksysLaboratoryApplyEx.getEndDate());
                } else {
                    endDate = DateUtil.offsetMonth(DateUtil.parse(tksysLaboratoryApplyEx.getStartDate()), 36);
                }
                //获取续约时间 结束前180天
                String renewDate = DateUtil.offsetDay(endDate, -180).toString("yyyy-MM-dd");
                tksysLaboratoryAgreement.setRenewDate(renewDate);
                businessLaboratoryAgreement.update(operator, tksysLaboratoryAgreement);

                //更新联合实验室申请表流程状态
                TksysLaboratoryApply tksysLaboratoryApplyNew = new TksysLaboratoryApply();
                tksysLaboratoryApplyNew.setRecordId(tksysLaboratoryApplyEx.getRecordId());
                tksysLaboratoryApplyNew.setStatus(KSYSConstants.ENDED);
                businessLaboratoryApply.update(operator, tksysLaboratoryApplyNew);

                //更新联合实验室主表状态
                TksysLaboratory tksysLaboratory = new TksysLaboratory();
                tksysLaboratory.setRecordId(tksysLaboratoryApplyEx.getLabId());
                tksysLaboratory.setCycle(tksysLaboratoryApplyEx.getCycle());
                tksysLaboratory.setStartDate(tksysLaboratoryApplyEx.getStartDate());
                tksysLaboratory.setEndDate(tksysLaboratoryApplyEx.getEndDate());
                tksysLaboratory.setRenewDate(renewDate);
                tksysLaboratory.setStatus(KSYSConstants.runIng);
                businessLaboratory.update(operator, tksysLaboratory);

                //保存框架协议正式稿附件
                SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(tksysLaboratoryApplyEx.getAgreementGuid(), tksysLaboratoryApplyEx.getAgreementDraft(), "KSYS", "agreementFormalFile", "", "");
            }
            workFlow.setVariable(variables);
            //提交流程
            String msg = SWorkFlowUtil.submit(operator, workFlow);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 将转联合工作室的实验室内容暂存
     */
    public void insterGzs(String applyId) {
        //通过申请表主键查询申请表和实验室信息
        TksysLaboratoryApply laboratoryApply = businessLaboratoryApply.query(applyId);
        TksysLaboratory laboratory = businessLaboratory.query(laboratoryApply.getLabId());
        //保存联合工作室主表信息
        TkgzsLaboratory tkgzsLaboratory = new TkgzsLaboratory();
        BeanUtil.copyProperties(laboratory, tkgzsLaboratory);
        tkgzsLaboratory.setGuid(BizIdUtil.INSTANCE.nextId());
        tkgzsLaboratory.setStatus(KSYSConstants.draft);
        tkgzsLaboratory.setSerialNo(null);
        businessKGZSLaboratory.insert(UserSession.getLoginName(), tkgzsLaboratory);
        //保存联合工作室申请表信息
        TkgzsLaboratoryApplyEx tkgzsLaboratoryApplyEx = new TkgzsLaboratoryApplyEx();
        BeanUtil.copyProperties(laboratoryApply, tkgzsLaboratoryApplyEx);
        tkgzsLaboratoryApplyEx.setGuid(BizIdUtil.INSTANCE.nextId());
        tkgzsLaboratoryApplyEx.setLabGuid(tkgzsLaboratory.getGuid());
        tkgzsLaboratoryApplyEx.setStatus(KSYSConstants.DRAFT);
        tkgzsLaboratoryApplyEx.setSerialNo(null);
        tkgzsLaboratoryApplyEx.setReviewFlag(null);
        businessKGZSLaboratoryApply.insert(UserSession.getLoginName(), tkgzsLaboratoryApplyEx);
        //获取并保存子表信息
        HashMap<String, Object> paramsUnit = new HashMap<>();
        paramsUnit.put("businessId", applyId);
        paramsUnit.put("businessType", KSYSConstants.Business_Type_0);
        List<TksysLaboratoryUnit> tksysLaboratoryUnits = businessLaboratoryUnit.queryList(paramsUnit);
        for (TksysLaboratoryUnit tksysLaboratoryUnit : tksysLaboratoryUnits) {
            TkgzsLaboratoryUnit tkgzsLaboratoryUnit = new TkgzsLaboratoryUnit();
            BeanUtil.copyProperties(tksysLaboratoryUnit, tkgzsLaboratoryUnit);
            tkgzsLaboratoryUnit.setGuid(BizIdUtil.INSTANCE.nextId());
            tkgzsLaboratoryUnit.setBusinessGuid(tkgzsLaboratoryApplyEx.getGuid());
            tkgzsLaboratoryUnit.setLabGuid(tkgzsLaboratory.getGuid());
            businessKGZSLaboratoryUnit.insert(UserSession.getLoginName(), tkgzsLaboratoryUnit);
        }
        HashMap<String, Object> paramsTarget = new HashMap<>();
        paramsTarget.put("applyId", applyId);
        List<TksysLaboratoryTarget> tksysLaboratoryTargets = businessLaboratoryTarget.queryList(paramsTarget);
        for (TksysLaboratoryTarget tksysLaboratoryTarget : tksysLaboratoryTargets) {
            TkgzsLaboratoryTarget tkgzsLaboratoryTarget = new TkgzsLaboratoryTarget();
            BeanUtil.copyProperties(tksysLaboratoryTarget, tkgzsLaboratoryTarget);
            tkgzsLaboratoryTarget.setGuid(BizIdUtil.INSTANCE.nextId());
            tkgzsLaboratoryTarget.setApplyGuid(tkgzsLaboratoryApplyEx.getGuid());
            tkgzsLaboratoryTarget.setLabGuid(tkgzsLaboratory.getGuid());
            businessKGZSLaboratoryTarget.insert(UserSession.getLoginName(), tkgzsLaboratoryTarget);
        }
        //获取富文本 并保存
        Map<String, Object> contentMap = ClobUtil.getContentMap(applyId);
        ClobUtil.addOrUpdateadd(contentMap, tkgzsLaboratoryApplyEx.getGuid(), "T_KGZS_LABORATORY_APPLY", KSYSConstants.Business_Type_KGZS, UserSession.getLoginName());
        //获取附件 并保存
        List<AttachmentMap> applyFile = SAttachmentUtil.getAttachmentBySourceIdAndSourceLabel(applyId, KSYSConstants.Business_Type, "applyFile", null, null);
        String collect = applyFile.stream().map(a -> a.getAttachmentId()).collect(Collectors.joining(","));
        SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(tkgzsLaboratoryApplyEx.getGuid(), collect, KSYSConstants.Business_Type_KGZS, "applyFile", null, null);
    }

    /**
     * 退回联合实验室组建申请流程
     *
     * @param inInfo
     * @return
     */
    public EiInfo doReturn(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TksysLaboratoryApplyEx tksysLaboratoryApplyEx = (TksysLaboratoryApplyEx) inInfo.get("tksysLaboratoryApplyEx");
            //流程
            String msg = SWorkFlowUtil.doReturn(operator, tksysLaboratoryApplyEx.getWorkFlow());
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 查询我申请的联合实验室/我负责的联合实验室/我管理的联合实验室
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryMyLaboratory(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo tableDataInfo = businessLaboratoryApply.queryMyLaboratory(UserSession.getLoginName(), queryData);
            setPage(inInfo, tableDataInfo);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 查询我处理过的联合实验室(也就是已办)
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryMyCLLaboratory(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo tableDataInfo = businessLaboratoryApply.queryMyCLLaboratory(UserSession.getLoginName(), queryData);
            setPage(inInfo, tableDataInfo);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 查询我相关的联合实验室
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryMyXGLaboratory(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo tableDataInfo = businessLaboratoryApply.queryMyXGLaboratory(UserSession.getLoginName(), queryData);
            setPage(inInfo, tableDataInfo);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 综合查询
     *
     * @param inInfo
     * @return
     */
    public EiInfo ZHCXlaboratory(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            //综合查询权限控制
            String loginName = UserSession.getLoginName();
            boolean isAdmin = RoleUtil.isAdmin(loginName);
            //如果是管理员可以查看所有联合实验室
            if (!isAdmin) {
                queryData.put("loginName", loginName);
                //全路径浏览权限
                List<ADOrg> adOrgs = RoleUtil.getOrgByUser(loginName, "KYXM_DW_DC");
                if (ObjectUtil.isNotEmpty(adOrgs)) {
                    StringBuffer dynSql = new StringBuffer("(");
                    for (ADOrg adOrg : adOrgs) {
                        if (dynSql.length() > 1) {
                            dynSql.append(" OR ");
                        }
                        dynSql.append(" A.TC_DEPT_CODE_PATH like '%" + adOrg.getOrgCode() + "%'");
                    }
                    dynSql.append(")");
                    queryData.put("dynSql", dynSql.toString());

                }
            }
            TableDataInfo tableDataInfo = businessLaboratory.ZHCXlaboratory(queryData);
            setPage(inInfo, tableDataInfo);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 综合查询导出
     *
     * @param inInfo
     * @return
     */
    public EiInfo ZHCXexport(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            //综合查询权限控制
            String loginName = UserSession.getLoginName();
            boolean isAdmin = RoleUtil.isAdmin(loginName);
            //如果是管理员可以查看所有联合实验室
            if (!isAdmin) {
                queryData.put("loginName", loginName);
                //全路径浏览权限
                List<ADOrg> adOrgs = RoleUtil.getOrgByUser(loginName, "KYXM_DW_DC");
                if (ObjectUtil.isNotEmpty(adOrgs)) {
                    StringBuffer dynSql = new StringBuffer("(");
                    for (ADOrg adOrg : adOrgs) {
                        if (dynSql.length() > 1) {
                            dynSql.append(" OR ");
                        }
                        dynSql.append(" A.TC_DEPT_CODE_PATH like '%" + adOrg.getOrgCode() + "%'");
                    }
                    dynSql.append(")");
                    queryData.put("dynSql", dynSql.toString());

                }
            }
            List<HashMap<String, Object>> mapList = businessLaboratory.ZHCXexport(queryData);
            List<TksysLaboratoryExport> laboratoryExportList = new ArrayList<>();
            for (HashMap<String, Object> map : mapList) {
                TksysLaboratoryExport laboratoryExport = BeanUtil.toBean(map, TksysLaboratoryExport.class);
                laboratoryExportList.add(laboratoryExport);
            }
            for (TksysLaboratoryExport laboratoryExport : laboratoryExportList) {
                //处理数据字典数据
                laboratoryExport.setStatus(SDictUtil.getDictName(KSYSConstants.Business_Type, "laboratoryStatus", laboratoryExport.getStatus()));
                laboratoryExport.setTechDomain(SDictUtil.getDictName("KYND", "jsly", laboratoryExport.getTechDomain()));
            }
            inInfo.set("laboratoryExportList", laboratoryExportList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 开启联合实验室年度总计及工作计划流程
     */
    public EiInfo startSummaryWorkFlow(EiInfo inInfo) {
        try {
            //获取在在运行中的联合实验室
            HashMap<String, Object> params = new HashMap<>();
            params.put("status", KSYSConstants.runIng);
            params.put("delStaus", "0");
            String loginName = UserSession.getLoginName();
            List<TksysLaboratory> laboratorieList = businessLaboratory.queryList(params);
            //每个运行中的的联合实验室 都要生成一份对应的联合实验室年度总结
            for (TksysLaboratory laboratory : laboratorieList) {
                //保存联合实验室年度报告
                TksysLaboratoryReport laboratoryReport = new TksysLaboratoryReport();
                laboratoryReport.setRecordId(BizIdUtil.INSTANCE.nextId());
                laboratoryReport.setLabId(laboratory.getRecordId());
                laboratoryReport.setFyear(String.valueOf(DateUtil.thisYear()));
                laboratoryReport.setStatus(KSYSConstants.ACTIVED);
                //设置年度报告类型
                String thisMonth = String.valueOf(DateUtil.thisMonth() + 1);
                if (StrUtil.equals(thisMonth, "12")) {
                    laboratoryReport.setReportType(KSYSConstants.reportType_allYear);
                } else {
                    laboratoryReport.setReportType(KSYSConstants.reportType_haflYear);
                }
                businessLaboratoryReport.insert(loginName, laboratoryReport);
                //开启年度总结流程 流转给联合实验室负责人编制年度总结
                String userLabelM = laboratory.getFzrCode();
                SWorkFlowUtil.startProcess("admin", laboratoryReport.getRecordId(), KSYSConstants.PROCESS_NAME_NDZJ, KSYSConstants.Business_Type, KSYSConstants.PROCESS_CODE_NDZJ, null, userLabelM, null, null, null);
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 取联合实验室年度总结及年度计划当前登陆用户的待办
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryNDZJDB(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            // 获取数据
            TableDataInfo tdi = businessLaboratoryReport.queryDB(UserSession.getLoginName(), queryData);
            setPage(inInfo, tdi);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 联合实验室年度总结及工作计划代办详情
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryDBNDZJDetail(EiInfo inInfo) {
        try {
            String recordId = inInfo.getString("businessId");
            String taskId = inInfo.getString("taskId");
            String operator = UserSession.getLoginName();
            TksysLaboratoryReportEx laboratoryReportEx = new TksysLaboratoryReportEx();
            //查询联合实验室年度总结表
            TksysLaboratoryReport laboratoryReport = businessLaboratoryReport.query(recordId);
            BeanUtil.copyProperties(laboratoryReport, laboratoryReportEx);
            //查询联合实验室主表
            TksysLaboratory laboratory = businessLaboratory.query(laboratoryReport.getLabId());
            //流程属性
            WorkFlow workFlow = new WorkFlow();
            if (StringUtils.isBlank(taskId)) {
                workFlow = new WorkFlow(operator, KSYSConstants.PROCESS_CODE_NDZJ, KSYSConstants.Business_Type, recordId);
            } else {
                workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
            }
            laboratoryReportEx.setWorkFlow(workFlow);
            if (StrUtil.equals("Manual3", workFlow.getCurrentActivity())) {
                //判断评审是否结束
                EiInfo einfo = new EiInfo();
                einfo.set("bizId", recordId);
                einfo.set("moduleCode", "ksys_ndzj");
                einfo.set(EiConstant.serviceId, "S_MP_PS_02");
                EiInfo outInfo = XServiceManager.call(einfo);
                List<Map<String, Object>> list = (List<Map<String, Object>>) outInfo.get("list");
                if (list != null && !list.isEmpty()) {
                    Map<String, Object> psMap = list.get(0);
                    Object isEnd = psMap.get("isEnd");
                    if ("1".equals(isEnd)) {
                        inInfo.set("psEnd", true);
                    }
                }
            }
            inInfo.set("laboratoryReportEx", laboratoryReportEx);
            inInfo.set("laboratory", laboratory);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 联合实验室年度总结及工作计划提交流程
     *
     * @param inInfo
     * @return
     */
    public EiInfo doSubmitNDZJ(EiInfo inInfo) {
        try {
            //当前登录人
            String operator = UserSession.getLoginName();
            TksysLaboratoryReportEx laboratoryReportEx = (TksysLaboratoryReportEx) inInfo.get("laboratoryReportEx");
            //流程属性信息
            WorkFlow workFlow = laboratoryReportEx.getWorkFlow();
            //根据流程业务id查询总结表
            TksysLaboratoryReport laboratoryReport = businessLaboratoryReport.query(workFlow.getBusinessId());
            //根据联合实验室id查询出联合实验室主表
            TksysLaboratory laboratory = businessLaboratory.query(laboratoryReport.getLabId());
            //当前节点
            String currentActivity = workFlow.getCurrentActivity();
            //流程参数
            HashMap<String, Object> variables = new HashMap<>();
            //节点判断
            if (StrUtil.equals(currentActivity, "Manual1")) {
                //更新信息
                TksysLaboratoryReport laboratoryReportUpdata = new TksysLaboratoryReport();
                BeanUtil.copyProperties(laboratoryReportEx, laboratoryReportUpdata);
                businessLaboratoryReport.update(operator, laboratoryReportUpdata);
                //保存附件信息
                SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(laboratoryReportEx.getRecordId(), laboratoryReportEx.getReportFile(), "KSYS", "reportFile", "", "");
                variables.put("orgParamter", laboratory.getTcDeptCode());
            } else if (StrUtil.equals(currentActivity, "Manual2")) {
                //判断是全年还是半年总结
                if (StrUtil.equals(laboratoryReport.getReportType(), KSYSConstants.reportType_haflYear)) {
                    //半年直接提交给项目主管进行评价
                    variables.put("isYear", 0);
                } else {
                    //全年项目主管组织评审
                    variables.put("isYear", 1);
                }
                workFlow.setUserLabelM(laboratory.getXmzgCode());
            } else if (StrUtil.equals(currentActivity, "Manual4")) {
                //保存项目主管评价 结束流程
                TksysLaboratoryReport laboratoryReportUpdata = new TksysLaboratoryReport();
                laboratoryReportUpdata.setRecordId(workFlow.getBusinessId());
                laboratoryReportUpdata.setZgEvaluate(laboratoryReportEx.getZgEvaluate());
                laboratoryReportUpdata.setStatus(KSYSConstants.ENDED);
                businessLaboratoryReport.update(operator, laboratoryReportUpdata);
            } else if (StrUtil.equals(currentActivity, "Manual3")) {
                TksysLaboratoryReport laboratoryReportUpdata = new TksysLaboratoryReport();
                laboratoryReportUpdata.setRecordId(workFlow.getBusinessId());
                laboratoryReportUpdata.setStatus(KSYSConstants.ENDED);
                laboratoryReportUpdata.setReviewFlag(laboratoryReportEx.getReviewFlag());
                businessLaboratoryReport.update(operator, laboratoryReportUpdata);
                variables.put("orgParamter", laboratory.getTcDeptCode());
            }
            workFlow.setVariable(variables);
            //提交流程
            String msg = SWorkFlowUtil.submit(operator, workFlow);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 退回联合实验室年度总结及工作计划流程
     *
     * @param inInfo
     * @return
     */
    public EiInfo doReturnNDZJ(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TksysLaboratoryReportEx laboratoryReportEx = (TksysLaboratoryReportEx) inInfo.get("laboratoryReportEx");
            //流程
            String msg = SWorkFlowUtil.doReturn(operator, laboratoryReportEx.getWorkFlow());
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 联合实验室年度总结及年度计划跟踪
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryLaboratoryReportGZ(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            // 获取数据
            TableDataInfo tdi = businessLaboratoryReport.queryLaboratoryReportGZ(UserSession.getLoginName(), queryData);
            setPage(inInfo, tdi);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 联合实验室续约流程的开启
     *
     * @param inInfo
     * @return
     */
    public EiInfo startRenewWorkFlow(EiInfo inInfo) {
        try {
            //获取到所有的运行中的联合实验室
            HashMap<String, Object> params = new HashMap<>();
            params.put("status", KSYSConstants.runIng);
            params.put("delStaus", "0");
            String loginName = UserSession.getLoginName();
            List<TksysLaboratory> laboratorieList = businessLaboratory.queryList(params);
            for (TksysLaboratory laboratory : laboratorieList) {
                //判断续约时间是否等于现在的时间
                String today = DateUtil.today();
                //获取框架协议信息
                TksysLaboratoryAgreementEx laboratoryAgreementEx = businessLaboratoryAgreement.queryByLabId(laboratory.getRecordId());
                if (StrUtil.equals(today, laboratory.getRenewDate())) {
                    //相等保存续约信息,并开启流程
                    TksysLaboratoryRenew laboratoryRenew = new TksysLaboratoryRenew();
                    laboratoryRenew.setRecordId(BizIdUtil.INSTANCE.nextId());
                    laboratoryRenew.setLabId(laboratory.getRecordId());
                    laboratoryRenew.setAgreementGuid(laboratoryAgreementEx.getRecordId());
                    laboratoryRenew.setExtra1(KSYSConstants.ACTIVED);
                    //laboratoryRenew.setRenewDate(laboratoryAgreementEx.getRenewDate());
                    //laboratoryRenew.setEndDate(laboratoryAgreementEx.getEndDate());
                    businessLaboratoryRenew.insert(loginName, laboratoryRenew);
                    //获取此项目的项目负责人
                    String userLabelM = laboratory.getFzrCode();
                    SWorkFlowUtil.startProcess("admin", laboratoryRenew.getRecordId(), KSYSConstants.PROCESS_NAME_XYXQ, KSYSConstants.Business_Type, KSYSConstants.PROCESS_CODE_XYXQ, null, userLabelM, null, null, null);
                }
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 取联合实验室协议续签当前登陆用户的待办
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryXYXQDB(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            // 获取数据
            TableDataInfo tdi = businessLaboratoryRenew.queryDB(UserSession.getLoginName(), queryData);
            setPage(inInfo, tdi);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 联合实验室续签代办详情
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryDBXYXQDetail(EiInfo inInfo) {
        try {
            String recordId = inInfo.getString("businessId");
            String taskId = inInfo.getString("taskId");
            String operator = UserSession.getLoginName();
            TksysLaboratoryRenewEx laboratoryRenewEx = new TksysLaboratoryRenewEx();
            //查询续签表
            TksysLaboratoryRenew laboratoryRenew = businessLaboratoryRenew.query(recordId);
            BeanUtil.copyProperties(laboratoryRenew, laboratoryRenewEx);
            //查询联合实验室主表
            TksysLaboratory laboratory = businessLaboratory.query(laboratoryRenew.getLabId());
            //流程属性
            WorkFlow workFlow = new WorkFlow();
            if (StringUtils.isBlank(taskId)) {
                workFlow = new WorkFlow(operator, KSYSConstants.PROCESS_CODE_XYXQ, KSYSConstants.Business_Type, recordId);
            } else {
                workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
            }
            laboratoryRenewEx.setWorkFlow(workFlow);
            inInfo.set("laboratoryRenewEx", laboratoryRenewEx);
            inInfo.set("laboratory", laboratory);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 联合实验室续签提交流程
     *
     * @param inInfo
     * @return
     */
    public EiInfo doSubmitXYXQ(EiInfo inInfo) {
        try {
            //当前登录人
            String operator = UserSession.getLoginName();
            TksysLaboratoryRenewEx laboratoryRenewEx = (TksysLaboratoryRenewEx) inInfo.get("laboratoryRenewEx");
            //流程属性信息
            WorkFlow workFlow = laboratoryRenewEx.getWorkFlow();
            //获取续约表信息
            TksysLaboratoryRenew laboratoryRenew = businessLaboratoryRenew.query(workFlow.getBusinessId());
            //根据联合实验室id查询出联合实验室主表
            TksysLaboratory laboratory = businessLaboratory.query(laboratoryRenew.getLabId());
            //查询联合实验室协议表信心
            TksysLaboratoryAgreementEx laboratoryAgreementEx = businessLaboratoryAgreement.queryByLabId(laboratory.getRecordId());
            //当前节点
            String currentActivity = workFlow.getCurrentActivity();
            //流程参数
            HashMap<String, Object> variables = new HashMap<>();
            //节点判断
            String endDateStr = laboratory.getEndDate();
            //计算最新的结束时间和续约时间
            DateTime endDate = DateUtil.parse(endDateStr);
            String endDateNew = DateUtil.offsetMonth(endDate, 36).toString("yyyy-MM-dd");
            String renewDateNew = DateUtil.offsetDay(DateUtil.parse(endDateNew), -180).toString("yyyy-MM-dd");
            if (StrUtil.equals(currentActivity, "Manual1")) {
                //判断是否续签
                if (StrUtil.equals(laboratoryRenewEx.getIsRenew(), "1")) {
                    //续签 更新续签表  保存附件
                    laboratoryRenew.setIsRenew("1");
                    laboratoryRenew.setCycle(laboratoryRenewEx.getCycle());
                    if (StrUtil.equals(laboratoryRenewEx.getCycle(), "0")) {
                        laboratoryRenew.setEndDate(endDateNew);
                        laboratoryRenew.setRenewDate(renewDateNew);
                    } else {
                        laboratoryRenew.setRenewDate(renewDateNew);
                    }
                    businessLaboratoryRenew.update(UserSession.getLoginName(), laboratoryRenew);
                    variables.put("isRenew", 1);
                    workFlow.setUserLabelM(laboratory.getXmzgCode());
                    //保存附件
                    SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(laboratoryRenew.getRecordId(), laboratoryRenewEx.getRenewalAgreement(), "KSYS", "renewalAgreement", "", "");
                    SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(laboratoryRenew.getRecordId(), laboratoryRenewEx.getOtherAgreement(), "KSYS", "otherAgreement", "", "");
                } else {
                    variables.put("isRenew", 0);
                    TksysLaboratoryRenew laboratoryRenewUpdata = new TksysLaboratoryRenew();
                    laboratoryRenewUpdata.setRecordId(laboratoryRenew.getRecordId());
                    laboratoryRenewUpdata.setIsRenew("0");
                    laboratoryRenewUpdata.setExtra1(KSYSConstants.ENDED);
                    businessLaboratoryRenew.update(UserSession.getLoginName(), laboratoryRenewUpdata);
                }
            } else if (StrUtil.equals(currentActivity, "Manual2")) {
                laboratory.setCycle(laboratoryRenew.getCycle());
                if (StrUtil.equals(laboratoryRenew.getCycle(), "0")) {
                    laboratory.setRenewDate(renewDateNew);
                    laboratory.setEndDate(endDateNew);
                } else {
                    laboratory.setRenewDate(renewDateNew);
                    laboratory.setEndDate(" ");
                }
                businessLaboratory.update(UserSession.getLoginName(), laboratory);
                laboratoryRenew.setExtra1(KSYSConstants.ENDED);
                businessLaboratoryRenew.update(UserSession.getLoginName(), laboratoryRenew);
            }
            workFlow.setVariable(variables);
            //提交流程
            String msg = SWorkFlowUtil.submit(operator, workFlow);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 保存并开启联合实验室协议中止
     *
     * @param inInfo
     * @return
     */
    public EiInfo startFlowXYZZ(EiInfo inInfo) {
        try {
            //获取参数
            TksysLaboratoryEndEx laboratoryEndEx = (TksysLaboratoryEndEx) inInfo.get("laboratoryEndEx");
            //当前登录人
            String loginName = UserSession.getLoginName();
            //新增或者修改联合实验室组建申请信息 将运行状态修改为 运行
            laboratoryEndEx.setStatus(KSYSConstants.ACTIVED);
            laboratoryEndEx.setRecordId(BizIdUtil.INSTANCE.nextId());
            //保存中止信息
            businessLaboratoryEnd.insert(loginName, laboratoryEndEx);
            //保存附件
            SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(laboratoryEndEx.getRecordId(), laboratoryEndEx.getSuspensionAgreement(), "KSYS", "suspensionAgreement", "", "");
            HashMap<String, Object> variables = new HashMap<>();
            TksysLaboratory query = businessLaboratory.query(laboratoryEndEx.getLabId());
            variables.put("orgParamter", query.getTcDeptCode());
            //开启流程
            String msg = SWorkFlowUtil.startProcessToActivity(loginName, laboratoryEndEx.getRecordId(), KSYSConstants.PROCESS_NAME_XYZZ, KSYSConstants.Business_Type, KSYSConstants.PROCESS_CODE_XYZZ, "Manual2", null, null, variables, null);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 取联实验室组中止协议代办
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryXYZZDB(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            // 获取数据
            TableDataInfo tdi = businessLaboratoryEnd.queryDB(UserSession.getLoginName(), queryData);
            setPage(inInfo, tdi);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 联合实验室中止代办详情
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryDBXYZZDetail(EiInfo inInfo) {
        try {
            String recordId = inInfo.getString("businessId");
            String taskId = inInfo.getString("taskId");
            String operator = UserSession.getLoginName();
            TksysLaboratoryEndEx laboratoryEndEx = new TksysLaboratoryEndEx();
            //查询中止表
            TksysLaboratoryEnd laboratoryEnd = businessLaboratoryEnd.query(recordId);
            BeanUtil.copyProperties(laboratoryEnd, laboratoryEndEx);
            //查询联合实验室主表
            TksysLaboratory laboratory = businessLaboratory.query(laboratoryEnd.getLabId());
            //流程属性
            WorkFlow workFlow = new WorkFlow();
            if (StringUtils.isBlank(taskId)) {
                workFlow = new WorkFlow(operator, KSYSConstants.PROCESS_CODE_XYZZ, KSYSConstants.Business_Type, recordId);
            } else {
                workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
            }
            laboratoryEndEx.setWorkFlow(workFlow);
            inInfo.set("laboratoryEndEx", laboratoryEndEx);
            inInfo.set("laboratory", laboratory);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 提交联合实验室中止
     *
     * @param inInfo
     * @return
     */
    public EiInfo doSubmitXYZZ(EiInfo inInfo) {
        try {
            //当前登录人
            String operator = UserSession.getLoginName();
            TksysLaboratoryEndEx laboratoryEndEx = (TksysLaboratoryEndEx) inInfo.get("laboratoryEndEx");
            //流程属性信息
            WorkFlow workFlow = laboratoryEndEx.getWorkFlow();
            //获取中止表信息
            TksysLaboratoryEnd query = businessLaboratoryEnd.query(workFlow.getBusinessId());
            //根据联合实验室d查询出联合实验室主表
            TksysLaboratory laboratory = businessLaboratory.query(query.getLabId());
            //当前节点
            String currentActivity = workFlow.getCurrentActivity();
            //流程参数
            HashMap<String, Object> variables = new HashMap<>();
            //节点判断
            if (StrUtil.equals(currentActivity, "Manual2")) {
                workFlow.setUserLabelM(laboratory.getXmzgCode());
            } else if (StrUtil.equals(currentActivity, "Manual3")) {
                variables.put("orgParamter", laboratory.getTcDeptCode());
            } else if (StrUtil.equals(currentActivity, "Manual4")) {
                //中止流程
                laboratory.setStatus(KSYSConstants.rescinded);
                businessLaboratory.update(UserSession.getLoginName(), laboratory);
                laboratoryEndEx.setStatus(KSYSConstants.ENDED);
                businessLaboratoryEnd.update(operator, laboratoryEndEx);
            } else if (StrUtil.equals(currentActivity, "Manual1")) {
                //更新
                businessLaboratoryEnd.update(operator, laboratoryEndEx);
                if (StrUtil.isNotBlank(laboratoryEndEx.getSuspensionAgreement())) {
                    SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(laboratoryEndEx.getRecordId(), laboratoryEndEx.getSuspensionAgreement(), KSYSConstants.Business_Type, "suspensionAgreement", "", "");
                }
                variables.put("orgParamter", laboratory.getTcDeptCode());
            }
            workFlow.setVariable(variables);
            //提交流程
            String msg = SWorkFlowUtil.submit(operator, workFlow);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 退回联合实验室协议中止流程
     *
     * @param inInfo
     * @return
     */
    public EiInfo doReturnXYZZ(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TksysLaboratoryEndEx laboratoryEndEx = (TksysLaboratoryEndEx) inInfo.get("laboratoryEndEx");
            //退回流程
            String msg = SWorkFlowUtil.doReturn(operator, laboratoryEndEx.getWorkFlow());
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }

        }
        return inInfo;
    }


    /**
     * 保存并开启联合实验室变更流程
     *
     * @param inInfo
     * @return
     */
    public EiInfo startFlowFZRBG(EiInfo inInfo) {
        try {
            //获取参数
            TksysLaboratoryChangeEx laboratoryChangeEx = (TksysLaboratoryChangeEx) inInfo.get("laboratoryChangeEx");
            //当前登录人
            String loginName = UserSession.getLoginName();
            //新增联合实验室变更 将运行状态修改为 运行
            laboratoryChangeEx.setStatus(KSYSConstants.ACTIVED);
            laboratoryChangeEx.setRecordId(BizIdUtil.INSTANCE.nextId());
            //保存中止信息
            businessLaboratoryChange.insert(loginName, laboratoryChangeEx);

            HashMap<String, Object> variables = new HashMap<>();
            TksysLaboratory query = businessLaboratory.query(laboratoryChangeEx.getLabId());
            variables.put("orgParamter", query.getTcDeptCode());
            //开启流程
            String msg = SWorkFlowUtil.startProcessToActivity(loginName, laboratoryChangeEx.getRecordId(), KSYSConstants.PROCESS_NAME_FZRBG, KSYSConstants.Business_Type, KSYSConstants.PROCESS_CODE_FZRBG, "Manual2", null, null, variables, null);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 取联合实验室组变更协办列表
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryFZRBGDB(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            // 获取数据
            TableDataInfo tdi = businessLaboratoryChange.queryDB(UserSession.getLoginName(), queryData);
            setPage(inInfo, tdi);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 联合实验室负责人变更代办详情
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryDBFZRBGDetail(EiInfo inInfo) {
        try {
            String recordId = inInfo.getString("businessId");
            String taskId = inInfo.getString("taskId");
            String operator = UserSession.getLoginName();
            TksysLaboratoryChangeEx laboratoryChangeEx = new TksysLaboratoryChangeEx();
            //查询变更表
            TksysLaboratoryChange laboratoryChange = businessLaboratoryChange.query(recordId);
            BeanUtil.copyProperties(laboratoryChange, laboratoryChangeEx);
            //查询联合实验室主表
            TksysLaboratory laboratory = businessLaboratory.query(laboratoryChange.getLabId());
            //流程属性
            WorkFlow workFlow = new WorkFlow();
            if (StringUtils.isBlank(taskId)) {
                workFlow = new WorkFlow(operator, KSYSConstants.PROCESS_CODE_KGZS_FZRBG, KSYSConstants.Business_Type, recordId);
            } else {
                workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
            }
            laboratoryChangeEx.setWorkFlow(workFlow);
            inInfo.set("laboratoryChangeEx", laboratoryChangeEx);
            inInfo.set("laboratory", laboratory);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 提交联合实验室变更
     *
     * @param inInfo
     * <AUTHOR>
     */
    public EiInfo doSubmitFZRBG(EiInfo inInfo) {
        try {
            //当前登录人
            String operator = UserSession.getLoginName();
            TksysLaboratoryChangeEx laboratoryChangeEx = (TksysLaboratoryChangeEx) inInfo.get("laboratoryChangeEx");
            //流程属性信息
            WorkFlow workFlow = laboratoryChangeEx.getWorkFlow();
            //获取变更信息
            TksysLaboratoryChange query = businessLaboratoryChange.query(workFlow.getBusinessId());
            //根据联合实验室d查询出联合实验室主表
            TksysLaboratory laboratory = businessLaboratory.query(query.getLabId());
            //当前节点
            String currentActivity = workFlow.getCurrentActivity();
            //流程参数
            HashMap<String, Object> variables = new HashMap<>();
            //节点判断
            if (StrUtil.equals(currentActivity, "Manual2")) {
                workFlow.setUserLabelM(laboratory.getXmzgCode());
            } else if (StrUtil.equals(currentActivity, "Manual3")) {
                //更新变更信息
                TksysLaboratoryChange laboratoryChangeNew = new TksysLaboratoryChange();
                laboratoryChangeNew.setRecordId(workFlow.getBusinessId());
                laboratoryChangeNew.setFzrCodeNew(laboratoryChangeEx.getFzrCodeNew());
                laboratoryChangeNew.setFzrNameNew(laboratoryChangeEx.getFzrNameNew());
                businessLaboratoryChange.update(UserSession.getLoginName(), laboratoryChangeNew);
                variables.put("orgParamter", laboratory.getTcDeptCode());
            } else if (StrUtil.equals(currentActivity, "Manual4")) {
                //更新变更信息
                TksysLaboratoryChange laboratoryChangeNew = new TksysLaboratoryChange();
                laboratoryChangeNew.setRecordId(workFlow.getBusinessId());
                laboratoryChangeNew.setStatus(KSYSConstants.ENDED);

                //更改联合实验室负责人
                TksysLaboratory laboratoryNew = new TksysLaboratory();
                laboratoryNew.setRecordId(laboratory.getRecordId());
                laboratoryNew.setFzrName(query.getFzrNameNew());
                laboratoryNew.setFzrCode(query.getFzrCodeNew());

                //更改联合实验室申请表负责人
                TksysLaboratoryApply laboratoryApplyNew = businessLaboratoryApply.queryByLabId(laboratory.getRecordId());
                laboratoryApplyNew.setFzrCode(query.getFzrCodeNew());
                laboratoryApplyNew.setFzrName(query.getFzrNameNew());

                businessLaboratoryChange.update(UserSession.getLoginName(), laboratoryChangeNew);
                businessLaboratory.update(UserSession.getLoginName(), laboratoryNew);
                businessLaboratoryApply.update(UserSession.getLoginName(), laboratoryApplyNew);
                variables.put("orgParamter", laboratory.getTcDeptCode());
            } else if (StrUtil.equals(currentActivity, "Manual1")) {
                //更新变更原因
                TksysLaboratoryChange laboratoryChangeNew = new TksysLaboratoryChange();
                laboratoryChangeNew.setRecordId(workFlow.getBusinessId());
                laboratoryChangeNew.setReason(laboratoryChangeEx.getReason());
                businessLaboratoryChange.update(UserSession.getLoginName(), laboratoryChangeNew);
            }
            workFlow.setVariable(variables);
            //提交流程
            String msg = SWorkFlowUtil.submit(operator, workFlow);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 退回联合实验室协议变更流程
     *
     * @param inInfo
     * @return
     */
    public EiInfo doReturnFZRBG(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TksysLaboratoryChangeEx laboratoryChangeEx = (TksysLaboratoryChangeEx) inInfo.get("laboratoryChangeEx");
            //退回流程
            String msg = SWorkFlowUtil.doReturn(operator, laboratoryChangeEx.getWorkFlow());
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }

        }
        return inInfo;
    }


    /**
     * 保存并开启优秀联合实验室评选流程
     *
     * @param inInfo
     * @return
     */
    public EiInfo startFlowYXPX(EiInfo inInfo) {
        try {
            //获取需要年度总结id
            String reportIds = (String) inInfo.get("reportIds");
            String[] reportIdArr = reportIds.split(",");
            String operator = UserSession.getLoginName();
            String userName = UserUtil.getUserName(operator);
            int count = 0;
            for (String reportId : reportIdArr) {
                //查询出年度总结信息
                TksysLaboratoryReport laboratoryReport = businessLaboratoryReport.query(reportId);
                //保存优秀联合实验室评选信息
                TksysLaboratoryExcellent laboratoryExcellent = new TksysLaboratoryExcellent();
                laboratoryExcellent.setRecordId(BizIdUtil.INSTANCE.nextId());
                laboratoryExcellent.setReportId(laboratoryReport.getRecordId());
                laboratoryExcellent.setLabId(laboratoryReport.getLabId());
                laboratoryExcellent.setFyear(laboratoryReport.getFyear());
                laboratoryExcellent.setStatus(KSYSConstants.ACTIVED);
                businessLaboratoryExcellent.insert(operator, laboratoryExcellent);
                //更新年度总结信息extra1=1 表示开启了优秀评选
                laboratoryReport.setExtra1("1");
                businessLaboratoryReport.update(operator, laboratoryReport);
                //开启流程
                SWorkFlowUtil.startProcess(operator, laboratoryExcellent.getRecordId(), KSYSConstants.PROCESS_NAME_YXPX, KSYSConstants.Business_Type, KSYSConstants.PROCESS_CODE_YXPX, null, operator, null, null);
                count++;
            }
            inInfo.setMsg("成功开启" + count + "条优秀联合实验室评选流程,下一步操作人" + userName);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 取优秀联合实验室评选待办列表
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryYXPXDB(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            // 获取数据
            TableDataInfo tdi = businessLaboratoryExcellent.queryDB(UserSession.getLoginName(), queryData);
            setPage(inInfo, tdi);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 优秀联合实验室评选代办详情
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryDBYXPXDetail(EiInfo inInfo) {
        try {
            String recordId = inInfo.getString("businessId");
            String taskId = inInfo.getString("taskId");
            String operator = UserSession.getLoginName();
            TksysLaboratoryExcellentEx laboratoryExcellentEx = new TksysLaboratoryExcellentEx();
            //查询优秀实验室评选表
            TksysLaboratoryExcellent laboratoryExcellent = businessLaboratoryExcellent.query(recordId);
            BeanUtil.copyProperties(laboratoryExcellent, laboratoryExcellentEx);
            //查询联合实验室主表
            TksysLaboratory laboratory = businessLaboratory.query(laboratoryExcellent.getLabId());
            //查询联合实验室年度总结
            TksysLaboratoryReport laboratoryReport = businessLaboratoryReport.query(laboratoryExcellent.getReportId());
            //流程属性
            WorkFlow workFlow = new WorkFlow();
            if (StringUtils.isBlank(taskId)) {
                workFlow = new WorkFlow(operator, KSYSConstants.PROCESS_CODE_YXPX, KSYSConstants.Business_Type, recordId);
            } else {
                workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
            }
            if (StrUtil.equals("Manual1", workFlow.getCurrentActivity())) {
                //判断评审是否结束
                EiInfo einfo = new EiInfo();
                einfo.set("bizId", recordId);
                einfo.set("moduleCode", "ksys_yxpx");
                einfo.set(EiConstant.serviceId, "S_MP_PS_02");
                EiInfo outInfo = XServiceManager.call(einfo);
                List<Map<String, Object>> list = (List<Map<String, Object>>) outInfo.get("list");
                if (list != null && !list.isEmpty()) {
                    Map<String, Object> psMap = list.get(0);
                    Object isEnd = psMap.get("isEnd");
                    if ("1".equals(isEnd)) {
                        inInfo.set("psEnd", true);
                    }
                }
            }

            //根据优秀联合实验室评选的最终得分计算 奖励档次 档次对应的分值范围 奖励金额范围 激励奖励金额 激励系数

            laboratoryExcellentEx.setWorkFlow(workFlow);
            inInfo.set("laboratoryExcellentEx", laboratoryExcellentEx);
            inInfo.set("laboratoryReport", laboratoryReport);
            inInfo.set("laboratory", laboratory);
            inInfo.setMsg("查询成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 提交优秀实验室评选
     *
     * @param inInfo
     * <AUTHOR>
     */
    public EiInfo doSubmitYXPX(EiInfo inInfo) {
        try {
            //当前登录人
            String operator = UserSession.getLoginName();
            TksysLaboratoryExcellentEx laboratoryExcellentEx = (TksysLaboratoryExcellentEx) inInfo.get("laboratoryExcellentEx");
            //流程属性信息
            WorkFlow workFlow = laboratoryExcellentEx.getWorkFlow();
            //获取优秀信息
            TksysLaboratoryExcellent laboratoryExcellent = businessLaboratoryExcellent.query(workFlow.getBusinessId());
            //根据联合实验室d查询出联合实验室主表
            TksysLaboratory laboratory = businessLaboratory.query(laboratoryExcellent.getLabId());
            //当前节点
            String currentActivity = workFlow.getCurrentActivity();
            //流程参数
            HashMap<String, Object> variables = new HashMap<>();
            //节点判断
            if (StrUtil.equals(currentActivity, "Manual1")) {
                variables.put("orgParamter", laboratory.getTcDeptCode());
                //只要提交出去说明已经入围了
                TksysLaboratoryExcellent laboratoryExcellentUpdate = new TksysLaboratoryExcellent();
                laboratoryExcellentUpdate.setRecordId(workFlow.getBusinessId());
                laboratoryExcellentUpdate.setExtra1("1");
                businessLaboratoryExcellent.update(operator, laboratoryExcellentUpdate);
            } else if (StrUtil.equals(currentActivity, "Manual2")) {
                //保存建议奖励金额
                TksysLaboratoryExcellent laboratoryExcellentUpdate = new TksysLaboratoryExcellent();
                laboratoryExcellentUpdate.setRecordId(workFlow.getBusinessId());
                laboratoryExcellentUpdate.setRewardAmount(laboratoryExcellentEx.getRewardAmount());
                businessLaboratoryExcellent.update(operator, laboratoryExcellentUpdate);
                //判断 建议奖励金额
                if (laboratoryExcellentEx.getRewardAmount().compareTo(new BigDecimal(50000)) > -1) {
                    //大于5万
                    variables.put("reward5", 1);
                } else {
                    variables.put("reward5", 0);
                    //小于五万结束流程
                    TksysLaboratoryExcellent laboratoryExcellentUpdateEnd = new TksysLaboratoryExcellent();
                    laboratoryExcellentUpdateEnd.setRecordId(workFlow.getBusinessId());
                    laboratoryExcellentUpdateEnd.setStatus(KSYSConstants.ENDED);
                    businessLaboratoryExcellent.update(operator, laboratoryExcellentUpdateEnd);
                }
                variables.put("orgParamter", laboratory.getTcDeptCode());
            } else if (StrUtil.equals(currentActivity, "Manual3")) {
                //判断 建议奖励金额
                if (laboratoryExcellent.getRewardAmount().compareTo(new BigDecimal(100000)) > -1) {
                    //大于10万
                    variables.put("reward10", 1);
                } else {
                    variables.put("reward10", 0);
                    //小于10万结束流程
                    TksysLaboratoryExcellent laboratoryExcellentUpdate = new TksysLaboratoryExcellent();
                    laboratoryExcellentUpdate.setRecordId(workFlow.getBusinessId());
                    laboratoryExcellentUpdate.setStatus(KSYSConstants.ENDED);
                    businessLaboratoryExcellent.update(operator, laboratoryExcellentUpdate);
                }
                variables.put("orgParamter", laboratory.getTcDeptCode());
            } else if (StrUtil.equals(currentActivity, "Manual4")) {
                TksysLaboratoryExcellent laboratoryExcellentUpdate = new TksysLaboratoryExcellent();
                laboratoryExcellentUpdate.setRecordId(workFlow.getBusinessId());
                laboratoryExcellentUpdate.setStatus(KSYSConstants.ENDED);
                businessLaboratoryExcellent.update(operator, laboratoryExcellentUpdate);
            }
            workFlow.setVariable(variables);
            //提交流程
            String msg = SWorkFlowUtil.submit(operator, workFlow);
            //如果流程结束开启发奖流程
            if (msg.contains(KSYSConstants.FLOW_END_MSG)) {
                //获取最新的优秀实验室信息
                TksysLaboratoryExcellent tksysLaboratoryExcellentNew = businessLaboratoryExcellent.query(workFlow.getBusinessId());
                //启动发奖流程前插入业务数据,返回业务主表主键, map中的key需一致，不能更改，并且必需
                Map<String, Object> mainYwzb = new HashMap<>();
                mainYwzb.put("ywmainYwmk", "KSYS"); //业务模块
                mainYwzb.put("ywmainYwlb", YWFJConstants.YWFJ_YWLX_24); ////业务类型, 结题奖、优秀团队、利润分享等
                mainYwzb.put("ywlxId", tksysLaboratoryExcellentNew.getRecordId()); //业务类型主键，自己的项目主键、申报主键等都可以，可用于调自己接口数据
                mainYwzb.put("ywmainJllb", ""); //奖励类别 非必需
                mainYwzb.put("ywmainYemc", laboratory.getLabName()); //业务名称 流程业务名称
                mainYwzb.put("ywmainYebh", laboratory.getSerialNo()); //业务编号 可以是项目号
                mainYwzb.put("ywmainDwcodw", laboratory.getTcDeptCode()); //负责单位code 组织编码
                mainYwzb.put("ywmainDw", OrgUtil.getOrgPathName(laboratory.getTcDeptCode())); //单位名称
                mainYwzb.put("ywmainFzrgh", laboratory.getFzrCode()); //项目负责人工号
                mainYwzb.put("ywmainFzr", laboratory.getFzrName()); //项目负责人名称
                mainYwzb.put("ywmainYwzggh", laboratory.getXmzgCode()); //业务主管工号
                mainYwzb.put("ywmainYwzg", laboratory.getXmzgName()); //业务主管名称
                mainYwzb.put("ywmainKfpje", tksysLaboratoryExcellentNew.getRewardAmount()); //可分配金额，实发奖励金额 单位元
                //mainYwzb.put("ywmainYwlink", appContextProp.getCtxZZZC() + "ktjc/projectEndReport/detail/                                                                                                                                           " + projectEndReport.getRecordGuid()); //业务链接，可跳转自己业务详情页面
                mainYwzb.put("ywmainStatus", YWFJConstants.YWFJ_JLFPSC_STATUS_01); //状态，待生成

                EiInfo xInInfo = new EiInfo();
                xInInfo.set("operator", operator);
                xInInfo.set("mainYwzb", mainYwzb);
                //调用插入业务数据远程微服务
                xInInfo.set(EiConstant.serviceId, "S_YW_FJ_01");
                EiInfo outInfo = XServiceManager.call(xInInfo);
                if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
                    logger.error(outInfo.toJSONString());
                    throw new PlatException(outInfo.getMsg());
                }
                String ywmainId = (String) outInfo.get("ywmainId");

                //调用启动发奖微服务 businessId 为调用上一微服务返回的业务主表主键
                xInInfo.set("businessId", ywmainId);
                xInInfo.set(EiConstant.serviceId, "S_YW_FJ_02");
                outInfo = XServiceManager.call(xInInfo);
                if (EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
                    logger.error(outInfo.toJSONString());
                    throw new PlatException(outInfo.getMsg());
                }
                msg = "发奖流程已启动：" + outInfo.getMsg();
            }
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 退回联合实验室评选流程
     *
     * @param inInfo
     * @return
     */
    public EiInfo doReturnYXPX(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TksysLaboratoryExcellentEx laboratoryExcellentEx = (TksysLaboratoryExcellentEx) inInfo.get("laboratoryExcellentEx");
            //退回流程
            String msg = SWorkFlowUtil.doReturn(operator, laboratoryExcellentEx.getWorkFlow());
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }
}
