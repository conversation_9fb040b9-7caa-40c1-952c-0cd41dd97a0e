package com.baosight.bscdkj.kw.zl.controller;

import com.baosight.bscdkj.kw.zl.utils.CYUtil;
import com.baosight.bscdkj.common.controller.BaseController;
import com.baosight.bscdkj.common.domain.AjaxResult;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.kw.domain.TkwzlApplyGjzd;
import com.baosight.bscdkj.utils.excel.ExcelUtil;
import com.baosight.bscdkj.utils.iplat.ServiceUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

/**
 * 境外专利_国家字典Controller
 * 
 * <AUTHOR>
 * @date 2022-01-21
 */
@Controller
@RequestMapping("KWZL/GJZD")
public class ControllerApplyGjzd extends BaseController{
    private String prefix = "/KWZL/GJZD";
    
    @GetMapping("list")
    public String applyGjzd(){
        return prefix + "/GJZD00";
    }


     /**
     * 查询境外专利_国家字典列表
     */
  	@PostMapping("/list")
	@ResponseBody
	public TableDataInfo list(@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKWZLApplyGjzd", "page");
		return getDataTable(query);
	}

    /**
     * 新增境外专利_国家字典
     */
    @GetMapping("/add")
    public String add(){
        return prefix + "/GJZD01";
    }


    /**
     * 新增保存境外专利_国家字典
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(TkwzlApplyGjzd applyGjzd) {


       EiInfo eiInfo = getEiInfo("i", applyGjzd);
       return  ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKWZLApplyGjzd", "insert");
    }

    /**
     * 修改境外专利_国家字典
     */
    @GetMapping("/edit/{sqgjId}")
    public String edit(@PathVariable("sqgjId") String sqgjId, ModelMap mmap) {
       	EiInfo eiInfo = new EiInfo();
		eiInfo.set("sqgjId", sqgjId);

        mmap.put("applyGjzd", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKWZLApplyGjzd", "load","applyGjzd"));
        return prefix + "/GJZD02";
    }

    /**
     * 修改保存境外专利_国家字典
     */
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(TkwzlApplyGjzd applyGjzd){
        EiInfo eiInfo = getEiInfo("i", applyGjzd);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKWZLApplyGjzd", "update");
    }
    
    /**
     * 删除境外专利_国家字典
     */
	@PostMapping("/remove")
	@ResponseBody
	public AjaxResult remove(String ids) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("sqgjId", ids);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKWZLApplyGjzd", "remove");
	}



    /**
     * 导出境外专利_国家字典列表
     */
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export() {
		EiInfo eiInfo = new EiInfo();
		List<TkwzlApplyGjzd> list = (List<TkwzlApplyGjzd>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKWZLApplyGjzd", "query","list");
		ExcelUtil<TkwzlApplyGjzd> util = new ExcelUtil<>(TkwzlApplyGjzd.class);
		util.setSheet("境外专利_国家字典");
		return util.exportExcel(list);
    }

    /**
     * 选择国家
     *
     * @param selectType S:单选 M:多选 默认S
     * @param mmap
     * @return
     */
    @GetMapping("/selectCY")
    public String selectCountry(String userCode,String userName,String selectType,String callback, ModelMap mmap) {
        mmap.put("selectType", selectType);
        mmap.put("userCode", userCode);
        mmap.put("userName", userName);
        mmap.put("operator", UserSession.getLoginName());
        mmap.put("callback", callback);
        return "KWZL/country";
    }

    /***
     * 根据国家搜索编码
     * @param map
     * @return
     */
    @PostMapping("/cyList")
    @ResponseBody
    public TableDataInfo cyList(@RequestBody Map<String, Object> map) {
        String gjCode = (String) map.get("userCode");
        String gjName = (String) map.get("userName");
        Integer pageNum = (Integer) map.get("pageNum");
        Integer pageSize = (Integer) map.get("pageSize");
        String operator = UserSession.getLoginName();
        String type = (String) map.get("type");
        String search = (String) map.get("search");
        return CYUtil.queryNewAllCountryByOrgId(operator,type,search,gjCode,gjName,pageNum, pageSize);
    }

    @PostMapping("/grsc/add")
    @ResponseBody
    public AjaxResult grscAdd(@RequestParam Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        return  ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKWZLApplyGjzd", "grscAdd");
    }
}
