package com.baosight.bscdkj.kw.zl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.ki.domain.TkizlPatentInfo;
import com.baosight.bscdkj.common.ki.domain.TkizlPsflog;
import com.baosight.bscdkj.common.kw.domain.*;
import com.baosight.bscdkj.common.ky.domain.TkymmTechnology;
import com.baosight.bscdkj.common.mp.domain.TmppsReviewMemo;
import com.baosight.bscdkj.common.service.PageService;
import com.baosight.bscdkj.ki.zl.business.BusinessApplyRyxxPatent;
import com.baosight.bscdkj.ki.zl.business.BusinessPsflogPatent;
import com.baosight.bscdkj.ki.zl.utils.PatentApplyUtil;
import com.baosight.bscdkj.ki.zl.utils.SwsUtil;
import com.baosight.bscdkj.kw.zl.business.*;
import com.baosight.bscdkj.kw.zl.constant.KWZLConstants;
import com.baosight.bscdkj.kw.zl.enums.FlowStatusTypeEnum;
import com.baosight.bscdkj.kw.zl.enums.OrgCodeTypeEnum;
import com.baosight.bscdkj.kw.zl.enums.PaStatusTypeEnum;
import com.baosight.bscdkj.kw.zl.utils.KwzlUtil;
import com.baosight.bscdkj.kw.zl.utils.NumUtil;
import com.baosight.bscdkj.ky.mm.business.BusinessTechnology;
import com.baosight.bscdkj.ky.mm.common.MMConstans;
import com.baosight.bscdkj.ky.mm.mmutlis.RDMMutli;
import com.baosight.bscdkj.mp.ad.dto.ADOrg;
import com.baosight.bscdkj.mp.ad.utils.OrgUtil;
import com.baosight.bscdkj.mp.ad.utils.RoleUtil;
import com.baosight.bscdkj.mp.ad.utils.UserUtil;
import com.baosight.bscdkj.mp.ps.business.BusinessReviewMemoPatent;
import com.baosight.bscdkj.mp.ty.utils.ClobUtil;
import com.baosight.bscdkj.mp.ty.utils.SDictUtil;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bscdkj.utils.expert.ExperReviewUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 境外专利_申请_基本信息Service接口
 *
 * <AUTHOR>
 * @date 2021-11-08
 */
@Service
public class ServiceKWZLApplyBaseinfo extends PageService {
    @Autowired
    private BusinessApplyBaseinfoKwzl businessApplyBaseinfo;

    @Autowired
    private BusinessApplyDfscKwzl businessApplyDfsc;

    @Autowired
    private BusinessGjjdBaseinfoKwzl businessGjjdBaseinfo;

    @Autowired
    private BusinessApplyWcrKwzl businessApplyWcr;

    @Autowired
    private BusinessApplyRyxxPatent businessApplyRyxxPatent;

    @Autowired
    private BusinessApplyDeptdictKwzl businessApplyDeptdict;

    @Autowired
    private BusinessReviewMemoPatent businessReviewMemo;

    @Autowired
    private BusinessPsflogPatent businessPsf;

    @Autowired
    private BusinessApplyHpginfoKwzl businessApplyHpginfo;

    @Autowired
    private BusinessTechnology businessTechnology;

    Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 初始化
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkwzlApplyBaseinfo> queryList = businessApplyBaseinfo.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo clPage(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo queryPage = businessApplyBaseinfo.quertPageYB(queryData);
            if (CollUtil.isEmpty(queryPage.getRows())) {
                queryPage.setRows(new ArrayList<>());
            } else {
                List<Map<String, Object>> rows = (List<Map<String, Object>>) queryPage.getRows().stream().distinct().collect(Collectors.toList());
                rows.forEach(info -> {
                    String businessId = MapUtil.getStr(info, "businessId");
                    String jwsqId = "";
                    switch (MapUtil.getStr(info, "processCode")) {
                        case KWZLConstants.KWZLAPPLY:
                            info.put("processName", "申请");
                            jwsqId = businessId;
                            break;
                        case KWZLConstants.KWZL_HPG:
                            info.put("processName", "后评估");
                            TkwzlApplyHpginfo applyHpginfo = KwzlUtil.getApplyHpgInfoByRecordId(businessId);
                            if (ObjectUtil.isNotEmpty(applyHpginfo)) {
                                jwsqId = applyHpginfo.getApplyId();
                            }
                            break;
                        case KWZLConstants.KWZL_GJJS:
                            info.put("processName", "国际检索");
                            TkwzlApplyDfsc applyDfsc = KwzlUtil.getApplyApplyDfscByJwdfscId(businessId);
                            if (ObjectUtil.isNotEmpty(applyDfsc)) {
                                jwsqId = applyDfsc.getJwzlId();
                            }
                            break;
                        case KWZLConstants.KWZL_SZSC:
                            info.put("processName", "答复审查");
                            TkwzlGjjdDfsc gjjdDfsc = KwzlUtil.getApplyApplyDfscByCountrydfscId(businessId);
                            if (ObjectUtil.isNotEmpty(gjjdDfsc)) {
                                if (StrUtil.isNotBlank(gjjdDfsc.getJwzlId())) {
                                    jwsqId = gjjdDfsc.getJwzlId();
                                } else {
                                    TkwzlGjjdBaseinfo gjjdBaseinfo = KwzlUtil.getGjjdBaseInfoByJwzlId(gjjdDfsc.getCountryId());
                                    jwsqId = gjjdBaseinfo.getJwsqId();
                                }
                            }
                            break;
                    }
                    TkwzlApplyBaseinfoEx applyBaseinfoEx = KwzlUtil.getApplyBaseInfoByJwsqId(jwsqId);
                    if (ObjectUtil.isNotEmpty(applyBaseinfoEx)) {
                        if (StrUtil.isNotBlank(applyBaseinfoEx.getSbbmCode())) {
                            String orgPathName = OrgUtil.getOrgPathName(applyBaseinfoEx.getSbbmCode());
                            if (StrUtil.isNotBlank(orgPathName)) {
                                info.put("sbbmName", orgPathName);
                            }
                        }
                        info.put("inZlmc", applyBaseinfoEx.getInZlmc());
                    }
                    info.put("currentActivityName", MapUtil.getStr(info, "businessName") + "/" + MapUtil.getStr(info, "currentActivityName"));
                    info.put("lastTime", DateUtil.format(DateUtil.parse(MapUtil.getStr(info, "lastTime")), "yyyy-MM-dd HH:mm:ss"));
                });
            }
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo page(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo queryPage = businessApplyBaseinfo.quertPage(queryData);
            if (CollUtil.isEmpty(queryPage.getRows())) {
                queryPage.setRows(new ArrayList<>());
            } else {
                List<TkwzlApplyBaseinfo> rows = (List<TkwzlApplyBaseinfo>) queryPage.getRows();
                rows.forEach(info -> {
                    PaStatusTypeEnum orgCode = PaStatusTypeEnum.getPaStatusType(info.getPaStatus());
                    if (orgCode != null) {
                        info.setPaStatus(orgCode.getDictName());
                    }
                    if (StrUtil.isNotBlank(info.getInZltype())) {
                        info.setInZltype(SDictUtil.getDictName("KIZL", "KI_PATENT_TYPE", info.getInZltype()));
                    }
                    if (StrUtil.isNotBlank(info.getFlowStatus())) {
                        FlowStatusTypeEnum orgCode1 = FlowStatusTypeEnum.getFlowStatusType(info.getFlowStatus());
                        if (orgCode1 != null) {
                            info.setFlowStatus(orgCode1.getDictName());
                        }
                    }
                    if (StrUtil.isNotBlank(info.getJpgTeam())) {
                        info.setJpgTeam(info.getJpgTeam() + "(" + info.getJpgXh() + ")");
                    }
                    if (StrUtil.isNotBlank(info.getSbbmCode())) {
                        String orgPathName = OrgUtil.getOrgPathName(info.getSbbmCode());
                        if (StrUtil.isNotBlank(orgPathName)) {
                            info.setSbbmName(orgPathName);
                        }
                    }
                });
            }
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo sbPage(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo queryPage = businessApplyBaseinfo.sbPage(queryData);
            if (CollUtil.isEmpty(queryPage.getRows())) {
                queryPage.setRows(new ArrayList<>());
            } else {
                List<TkwzlApplyBaseinfo> rows = (List<TkwzlApplyBaseinfo>) queryPage.getRows();
                rows.forEach(info -> {
                    PaStatusTypeEnum orgCode = PaStatusTypeEnum.getPaStatusType(info.getPaStatus());
                    if (orgCode != null) {
                        info.setPaStatus(orgCode.getDictName());
                    }
                    if (StrUtil.isNotBlank(info.getInZltype())) {
                        info.setInZltype(SDictUtil.getDictName("KIZL", "KI_PATENT_TYPE", info.getInZltype()));
                    }
                    if (StrUtil.isNotBlank(info.getSbbmCode())) {
                        String orgPathName = OrgUtil.getOrgPathName(info.getSbbmCode());
                        if (StrUtil.isNotBlank(orgPathName)) {
                            info.setSbbmName(orgPathName);
                        }
                    }
                });
            }
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            Map row = inInfo.getBlock("i").getRow(0);
            TkwzlApplyBaseinfo bean = BeanUtil.toBean(row, TkwzlApplyBaseinfo.class);
            businessApplyBaseinfo.update(UserSession.getLoginName(), bean);
            inInfo.setMsg("修改成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            Map row = inInfo.getBlock("i").getRow(0);
            TkwzlApplyBaseinfo bean = BeanUtil.toBean(row, TkwzlApplyBaseinfo.class);
            businessApplyBaseinfo.insert(UserSession.getLoginName(), bean);
            inInfo.setMsg("添加成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo load(EiInfo inInfo) {
        try {
            String jwsqId = (String) inInfo.get("jwsqId");
            TkwzlApplyBaseinfo query = businessApplyBaseinfo.query(jwsqId);
            inInfo.set("applyBaseinfo", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String jwsqId = (String) inInfo.get("jwsqId");
            if (StrUtil.isNotBlank(jwsqId)) {
                for (String id : jwsqId.split(",")) {
                    businessApplyBaseinfo.delete(loginName, id);
                }
            }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;

    }

    /***
     * 查询详细
     * @param inInfo
     * @return
     */
    public EiInfo queryDataIled(EiInfo inInfo) {
        try {
            String jwsqId = (String) inInfo.get("jwsqId");
            TkwzlApplyBaseinfo bean = this.businessApplyBaseinfo.query(jwsqId);
            TkwzlApplyBaseinfoEx query = TkwzlApplyBaseinfoEx.initParent(bean);
            if (StrUtil.isNotBlank(query.getIsvalid())) {
                if ("1".equals(query.getIsvalid())) {
                    query.setIsvalid("是");
                } else {
                    query.setIsvalid("否");
                }
            }
            if (StrUtil.isNotBlank(query.getPaStatus())) {
                PaStatusTypeEnum paStatusType = PaStatusTypeEnum.getPaStatusType(query.getPaStatus());
                if (ObjectUtil.isNotEmpty(paStatusType)) {
                    query.setPaStatus(paStatusType.getDictName());
                }
            }
            if (StrUtil.isNotBlank(query.getSwsGuid())) {
                query.setSwsName(SwsUtil.getSwsName(query.getSwsGuid()));
            }
            if (StrUtil.isNotBlank(query.getSqgjFinal())) {
                query.setSqgjFinalName(KwzlUtil.getCountryName(query.getSqgjFinal()));
            }
            //查询答复表
            Map<String, Object> param = new HashMap<>();
            param.put("jwzlId", jwsqId);
            param.put("displayOrder", "send_date desc");
            List<TkwzlApplyDfsc> tkwzlApplyDfscs = this.businessApplyDfsc.queryList(param);
            query.setDfsc(tkwzlApplyDfscs);
            //查询国家阶段信息表
            Map<String, Object> gjjdParam = new HashMap<>();
            gjjdParam.put("jwsqId", jwsqId);
            gjjdParam.put("displayOrder", "TO_NUMBER(REGEXP_REPLACE(GJ_SQH,'[^0-9]'))");
            List<TkwzlGjjdBaseinfo> tkwzlGjjdBaseinfos = this.businessGjjdBaseinfo.queryList(gjjdParam);
            if (ObjectUtil.isNotEmpty(tkwzlGjjdBaseinfos)) {
                tkwzlGjjdBaseinfos.forEach(info -> {
                    if (StrUtil.isNotBlank(info.getIsvalid())) {
                        info.setIsvalid(SDictUtil.getDictName("KWZL", "KWZL_ISVALID", info.getIsvalid()));
                    }
                });
            }
            query.setGjjds(tkwzlGjjdBaseinfos);
            List<TkwzlApplyWcr> wcrs = KwzlUtil.getApplyFmr(jwsqId);
            if (ObjectUtil.isNotEmpty(wcrs)) {
                query.setWcrs(wcrs);
            }
            inInfo.set("applyBaseinfo", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryPatentInfoPage(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("isvalid", KWZLConstants.ISVALID_1);
            //有效期一年的受理专利
            String lastDate = KwzlUtil.getLastDate();
            queryData.put("dynSql2", "SLRQ >= '" + lastDate + "'");
            TableDataInfo tableDataInfo = new TableDataInfo();
            //主管管理员
            boolean roleMember = RoleUtil.isRoleMember(KWZLConstants.KWZL_ZGBM_ADMIN, UserSession.getLoginName());
            if (roleMember) {
                List<ADOrg> orgByUser = RoleUtil.getOrgByUser(UserSession.getLoginName(), KWZLConstants.KWZL_ZGBM_ADMIN);
                if (ObjectUtil.isNotEmpty(orgByUser)) {
                    StringBuffer dynSql = new StringBuffer("(");
                    for (ADOrg adOrg : orgByUser) {
                        if (dynSql.length() > 1) {
                            dynSql.append(" OR ");
                        }
                        dynSql.append(" FIRST_DEPT_PATH like '%" + adOrg.getOrgCode() + "%'");
                    }
                    dynSql.append(")");
                    queryData.put("dynSql", dynSql.toString());
                    tableDataInfo = this.businessApplyBaseinfo.queryValidKizl(queryData);
                }
            } else {
                //部门联络员
                boolean KI_BM_ADMIN = RoleUtil.isRoleMember(KWZLConstants.KIZL_BM_ADMIN, UserSession.getLoginName());
                if (KI_BM_ADMIN) {
                    List<ADOrg> orgByUser = RoleUtil.getOrgByUser(UserSession.getLoginName(), KWZLConstants.KIZL_BM_ADMIN);
                    if (ObjectUtil.isNotEmpty(orgByUser)) {
                        StringBuffer dynSql = new StringBuffer("(");
                        for (ADOrg adOrg : orgByUser) {
                            if (dynSql.length() > 1) {
                                dynSql.append(" OR ");
                            }
                            dynSql.append(" FIRST_DEPT_PATH like '%" + adOrg.getOrgCode() + "%'");
                        }
                        dynSql.append(")");
                        queryData.put("dynSql", dynSql.toString());
                        tableDataInfo = this.businessApplyBaseinfo.queryValidKizl(queryData);
                    }
                } else {
                    queryData.remove("dynSql");
                    queryData.put("jpgGw", UserSession.getLoginName());
                    //金苹果
                    tableDataInfo = this.businessApplyBaseinfo.queryValidKizl(queryData);
                }
            }
            if (CollUtil.isEmpty(tableDataInfo.getRows())) {
                tableDataInfo.setRows(new ArrayList<>());
            } else {
                List<TkizlPatentInfo> patentInfoList = (List<TkizlPatentInfo>) tableDataInfo.getRows();
                Iterator<TkizlPatentInfo> iterator = patentInfoList.iterator();
                while (iterator.hasNext()) {
                    TkizlPatentInfo info = iterator.next();
                    Map<String, Object> param = new HashMap<>();
                    param.put("inSqh", info.getPatentNo());
                    int count = this.businessApplyBaseinfo.count(param);
                    if (count >= 1) {
                        iterator.remove();
                        tableDataInfo.setTotal(tableDataInfo.getTotal() - 1);
                    }
                }
            }
            inInfo = setPage(inInfo, tableDataInfo);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /***
     * 查询境内专利表详细
     * @param inInfo
     * @return
     */
    public EiInfo queryPatentInfo(EiInfo inInfo) {
        try {
            String patentNo = (String) inInfo.get("patentNo");
            Map<String, Object> param = new HashMap<>();
            param.put("inSqh", patentNo);
            TkwzlApplyBaseinfo tkwzlApplyBaseinfo = this.businessApplyBaseinfo.load(param);
            if (ObjectUtil.isNotEmpty(tkwzlApplyBaseinfo)) {
                inInfo.set("data", tkwzlApplyBaseinfo);
            } else {
                TkizlPatentInfo tkizlPatentInfo = PatentApplyUtil.getPatentInfoByPatentNo(patentNo);
                if (ObjectUtil.isNotEmpty(tkizlPatentInfo)) {
                    //境内申请信息
                    TkwzlApplyBaseinfoEx query = this.businessApplyBaseinfo.getApplyBaseInfo(tkizlPatentInfo);
                    inInfo.set("data", query);
                } else {
                    TkwzlApplyBaseinfoEx query = new TkwzlApplyBaseinfoEx();
                    inInfo.set("data", query);
                }
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo ksQuery(EiInfo inInfo) {
        try {
            String inBgbh = (String) inInfo.get("inBgbh");
            Map<String, Object> param = new HashMap<>();
            param.put("inBgbh", inBgbh);
            param.put("flowStatus", KWZLConstants.STATUS_RUN);
            TkwzlApplyBaseinfo query = this.businessApplyBaseinfo.load(param);
            if (ObjectUtil.isNotEmpty(query)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("该国内专利清能编号正在流转中！");
                return inInfo;
            }
            Map<String, Object> param2 = new HashMap<>();
            param2.put("inBgbh", inBgbh);
            param2.put("flowStatus", KWZLConstants.STATUS_END);
            TkwzlApplyBaseinfo query2 = this.businessApplyBaseinfo.load(param2);
            if (ObjectUtil.isNotEmpty(query2)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("该当前国内专利清能编号已申报境外专利！");
                return inInfo;
            }
            TkizlPatentInfo tkizlPatentInfo = PatentApplyUtil.getPatentInfoByBgbh(inBgbh);
            if (ObjectUtil.isNotEmpty(tkizlPatentInfo)) {
                //境内申请信息
                TkwzlApplyBaseinfo tkwzlApplyBaseinfo = this.businessApplyBaseinfo.getApplyBaseInfo(tkizlPatentInfo);
                inInfo.set("data", tkwzlApplyBaseinfo);
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            } else {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("未查询到此国内专利清能编号信息！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo ksQueryPatentInfo(EiInfo inInfo) {
        try {
            String inBgbh = (String) inInfo.get("inBgbh");
            Map<String, Object> param = new HashMap<>();
            param.put("inBgbh", inBgbh);
            TkwzlApplyBaseinfo tkwzlApplyBaseinfo = this.businessApplyBaseinfo.load(param);
            if (ObjectUtil.isNotEmpty(tkwzlApplyBaseinfo)) {
                TkwzlApplyBaseinfoEx query = TkwzlApplyBaseinfoEx.initParent(tkwzlApplyBaseinfo);
                query.setBzr(UserSession.getLoginName());
                query.setBzrName(UserSession.getLoginCName());
                inInfo.set("data", query);
            } else {
                TkwzlApplyBaseinfoEx query = new TkwzlApplyBaseinfoEx();
                query.setBzr(UserSession.getLoginName());
                query.setBzrName(UserSession.getLoginCName());
                TkizlPatentInfo tkizlPatentInfo = PatentApplyUtil.getPatentInfoByBgbh(inBgbh);
                if (ObjectUtil.isNotEmpty(tkizlPatentInfo)) {
                    //境内申请信息
                    query = this.businessApplyBaseinfo.getApplyBaseInfo(tkizlPatentInfo);
                    inInfo.set("data", query);
                } else {
                    inInfo.set("data", query);
                }
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /***
     * 启动流程并保存信息
     */
    @Transactional
    public EiInfo doSave(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TkwzlApplyBaseinfoEx tkwzlApplyBaseinfoEx = (TkwzlApplyBaseinfoEx) inInfo.get("applyBaseinfo");
            EiInfo retInfo = this.businessApplyBaseinfo.doSave(operator, tkwzlApplyBaseinfoEx);
            inInfo.setMsg(retInfo.getMsg());
            inInfo.setCell("i", 0, "jwsqId", retInfo.getBlock("i").getRow(0).get("jwsqId"));
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /***
     * 提交待办
     */
    @Transactional
    public EiInfo submitWF(EiInfo inInfo) {
        EiInfo retInfo = null;
        try {
            //当前登录用户
            String loginName = UserSession.getLoginName();
            TkwzlApplyBaseinfoEx tkwzlApplyBaseinfoEx = (TkwzlApplyBaseinfoEx) inInfo.get("bean");
            retInfo = this.businessApplyBaseinfo.submitWF(loginName, tkwzlApplyBaseinfoEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg(retInfo.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /***
     * 终止申请
     */
    @Transactional
    public EiInfo doEnd(EiInfo inInfo) {
        EiInfo retInfo = null;
        try {
            //当前登录用户
            String operator = UserSession.getLoginName();
            TkwzlApplyBaseinfoEx tkwzlApplyBaseinfoEx = (TkwzlApplyBaseinfoEx) inInfo.get("bean");
            tkwzlApplyBaseinfoEx.setIsvalid(KWZLConstants.ISVALID_0);
            retInfo = this.businessApplyBaseinfo.doSave(operator, tkwzlApplyBaseinfoEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg(retInfo.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /***
     * 境外专利申请专家评审异步通知
     * @param inInfo
     * @return
     */
    @Transactional
    public EiInfo zjpsASync(EiInfo inInfo) {
        try {
            //当前登录用户
            String operator = UserSession.getLoginName();
            Object reviewInfos = inInfo.get("reviewInfos");
            if (ObjectUtil.isNotEmpty(reviewInfos)) {
                List<Map<String, Object>> listMapReviewInfos = (List<Map<String, Object>>) reviewInfos;
                for (Map<String, Object> listMapReviewInfo : listMapReviewInfos) {
                    String reviewFlag = (String) listMapReviewInfo.get("reviewFlag");
                    String reviewComment = (String) listMapReviewInfo.get("reviewComment");
                    String reviewDate = (String) listMapReviewInfo.get("reviewDate");
                    String reviewUser = (String) listMapReviewInfo.get("reviewUser");
                    String reviewUserName = (String) listMapReviewInfo.get("reviewUserName");
                    String createUserLabel = (String) listMapReviewInfo.get("createUserLabel");
                    String extra4 = (String) listMapReviewInfo.get("extra4");
                    String bizId = (String) listMapReviewInfo.get("bizId");
                    TkwzlApplyBaseinfo query = this.businessApplyBaseinfo.query(bizId);
                    if (ObjectUtil.isNotEmpty(query)) {
                        Set<String> set = new HashSet<>();
                        if (StrUtil.isNotBlank(query.getSqgjExpert())) {
                            set.addAll(Arrays.stream(query.getSqgjExpert().split(",")).collect(Collectors.toSet()));
                        }
                        if (StrUtil.isNotBlank(extra4)) {
                            set.addAll(Arrays.stream(extra4.split(",")).collect(Collectors.toSet()));
                        }
                        if (ObjectUtil.isNotEmpty(set)) {
                            query.setSqgjExpert(set.stream().collect(Collectors.joining(",")));
                            this.businessApplyBaseinfo.update(operator, query);
                        }
//                        //激活流程
//                        String s = SWorkFlowUtil.activeProcess(operator, query.getJwsqId());
//                        logger.info("KWZL-当前申报启动专家评审激活主流程{}", s);
                        //保存评审费
                        WorkFlow workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(bizId);
                        this.reviewRecord(reviewUser,workFlow.getBusinessId(),"SQ",reviewComment,reviewFlag,workFlow.getProcessCode(),workFlow.getCurrentActivity());
                    }
                }
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    public EiInfo suspendPS(EiInfo info) {
        try {
            String operator = UserSession.getLoginName();
            String bizGuid = (String) info.get("bizGuid");
//            String s = SWorkFlowUtil.suspendProcess(operator, bizGuid);
//            logger.info("KWZL-当前申报启动专家评审挂起主流程{}", s);
            info.setStatus(EiConstant.STATUS_SUCCESS);
            info.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            info.setStatus(EiConstant.STATUS_FAILURE);
            info.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                info.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return info;
    }

    /**
     * 专利评审结果
     *
     * @param info
     * @return
     */
    public EiInfo getPSXX(EiInfo info) {
        try {
            String operator = UserSession.getLoginName();
            String bizGuids = (String) info.get("bizGuid");
            String base64 = base64BaseApplyInfo(operator, bizGuids);
            info.set("jbxx", base64);
            info.setStatus(EiConstant.STATUS_SUCCESS);
            info.setMsg("成功");
        } catch (Exception e) {
            e.printStackTrace();
            info.setStatus(EiConstant.STATUS_FAILURE);
            info.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                info.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return info;
    }

    public String base64BaseApplyInfo(String operator, String bizGuids) {
        if (StrUtil.isNotBlank(bizGuids)) {
            JSONObject jsonObject = new JSONObject();
            String[] splits = bizGuids.split(",");
            for (String bizGuid : splits) {
                TkwzlApplyBaseinfo query = businessApplyBaseinfo.query(bizGuid);
                Map<String, Object> map = new HashMap();
                map.put("境外专利申报名称", query.getInZlmc());
                jsonObject = ExperReviewUtil.getReviewInfoEncapsulationToJsonStr("kwzl_zlsq", map, bizGuid);
                JSONObject map1 = new JSONObject();
                map1.putOpt("20211201155833971626688", query.getSqgjTcr());
                map1.putOpt("20211201155833971626688Name", KwzlUtil.getCountryName(query.getSqgjTcr()));
                jsonObject.putOpt(bizGuid + "Data", map1);
            }
            System.out.println(jsonObject.toJSONString(0));
            return Base64.encodeUrlSafe(jsonObject.toJSONString(0));
        }
        return null;
    }

    /***
     * 退回流程
     */
    @Transactional
    public EiInfo returnWF(EiInfo inInfo) {
        EiInfo retInfo = null;
        try {
            //当前登录用户
            String operator = UserSession.getLoginName();
            TkwzlApplyBaseinfoEx tkwzlApplyBaseinfoEx = (TkwzlApplyBaseinfoEx) inInfo.get("bean");
            retInfo = this.businessApplyBaseinfo.returnWF(operator, tkwzlApplyBaseinfoEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg(retInfo.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /***
     * 快速启动
     * @param inInfo
     * @return
     */
    @Transactional
    public EiInfo ksStartWF(EiInfo inInfo) {
        EiInfo retInfo;
        try {
            //当前登录用户
            String operator = UserSession.getLoginName();
            TkwzlApplyBaseinfoEx tkwzlApplyBaseinfoEx = (TkwzlApplyBaseinfoEx) inInfo.get("bean");
            retInfo = this.businessApplyBaseinfo.ksStartWF(operator, tkwzlApplyBaseinfoEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg(retInfo.getMsg());
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    public EiInfo zhcxQuery(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo queryPage;
            boolean isAdmin = RoleUtil.isAdmin(loginName);
            boolean roleMember = RoleUtil.isRoleMember("KWZL_ALL_READALL", loginName);
            if (!isAdmin || !roleMember) {
                //全路径浏览权限
                List<ADOrg> adOrgs = RoleUtil.getOrgByUser(loginName, "KWZL_ZGBM_READALL");
                if (ObjectUtil.isNotEmpty(adOrgs)) {
                    StringBuffer dynSql = new StringBuffer("(");
                    for (ADOrg adOrg : adOrgs) {
                        if (dynSql.length() > 1) {
                            dynSql.append(" OR ");
                        }
                        dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                    }
                    dynSql.append(")");
                    queryData.put("dynSql", dynSql.toString());
                } else {
                    List<ADOrg> adOrgs2 = RoleUtil.getOrgByUser(loginName, KWZLConstants.KWZL_ZGBM_ADMIN);
                    if (ObjectUtil.isNotEmpty(adOrgs2)) {
                        StringBuffer dynSql = new StringBuffer("(");
                        for (ADOrg adOrg : adOrgs2) {
                            if (dynSql.length() > 1) {
                                dynSql.append(" OR ");
                            }
                            dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                        }
                        dynSql.append(")");
                        queryData.put("dynSql", dynSql.toString());
                    } else {
                        List<ADOrg> jfOrgs = RoleUtil.getOrgByUser(loginName, KWZLConstants.KWZL_ZGBM_MONEY);
                        if (ObjectUtil.isNotEmpty(jfOrgs)) {
                            StringBuffer dynSql = new StringBuffer("(");
                            for (ADOrg adOrg : jfOrgs) {
                                if (dynSql.length() > 1) {
                                    dynSql.append(" OR ");
                                }
                                dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                            }
                            dynSql.append(")");
                            queryData.put("dynSql", dynSql.toString());
                        } else {
                            List<ADOrg> adOrgsBmAll = RoleUtil.getOrgByUser(loginName, "KWZL_BM_READALL");
                            if (ObjectUtil.isNotEmpty(adOrgsBmAll)) {
                                StringBuffer dynSql = new StringBuffer("(");
                                for (ADOrg adOrg : adOrgsBmAll) {
                                    if (dynSql.length() > 1) {
                                        dynSql.append(" OR ");
                                    }
                                    dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                                }
                                dynSql.append(")");
                                queryData.put("dynSql", dynSql.toString());
                            } else {
                                List<ADOrg> adOrgsBm = RoleUtil.getOrgByUser(loginName, KWZLConstants.KIZL_BM_ADMIN);
                                if (ObjectUtil.isNotEmpty(adOrgsBm)) {
                                    StringBuffer dynSql = new StringBuffer("(");
                                    for (ADOrg adOrg : adOrgsBm) {
                                        if (dynSql.length() > 1) {
                                            dynSql.append(" OR ");
                                        }
                                        dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                                    }
                                    dynSql.append(")");
                                    queryData.put("dynSql", dynSql.toString());
                                } else {
                                    List<ADOrg> adOrgsFc = RoleUtil.getOrgByUser(loginName, "KWZL_FC_READALL");
                                    if (ObjectUtil.isNotEmpty(adOrgsFc)) {
                                        StringBuffer dynSql = new StringBuffer("(");
                                        for (ADOrg adOrg : adOrgsFc) {
                                            if (dynSql.length() > 1) {
                                                dynSql.append(" OR ");
                                            }
                                            dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                                        }
                                        dynSql.append(")");
                                        queryData.put("dynSql", dynSql.toString());
                                    } else {
                                        queryData.put("dynSql2", "BZR ='" + loginName + "' OR JPG_GW = '" + loginName + "'");
                                    }
                                }
                            }
                        }
                    }
                }
            }
            queryData.put("displayOrder", "tkab.IN_SQH DESC");
            queryPage = businessApplyBaseinfo.zhcxSlQuery(queryData);
            if (CollUtil.isEmpty(queryPage.getRows())) {
                queryPage.setRows(new ArrayList<>());
            } else {
                List<Map> rows = (List<Map>) queryPage.getRows();
                rows.forEach(info -> {
                    if (StrUtil.isNotBlank(MapUtil.getStr(info, "paStatus"))) {
                        PaStatusTypeEnum orgCode = PaStatusTypeEnum.getPaStatusType(MapUtil.getStr(info, "paStatus"));
                        if (orgCode != null) {
                            info.put("paStatus", orgCode.getDictName());
                        }
                    }
                    if (StrUtil.isNotBlank(MapUtil.getStr(info, "inZltype"))) {
                        info.put("inZltype", SDictUtil.getDictName("KIZL", "KI_PATENT_TYPE", MapUtil.getStr(info, "inZltype")));
                    }
                    if (StrUtil.isNotBlank(MapUtil.getStr(info, "flowStatus"))) {
                        FlowStatusTypeEnum orgCode1 = FlowStatusTypeEnum.getFlowStatusType(MapUtil.getStr(info, "flowStatus"));
                        if (orgCode1 != null) {
                            info.put("flowStatus", orgCode1.getDictName());
                        }
                    }
                    if (StrUtil.isNotBlank(MapUtil.getStr(info, "sbbmCode"))) {
                        String orgPathName = OrgUtil.getOrgPathName(MapUtil.getStr(info, "sbbmCode"));
                        if (StrUtil.isNotBlank(orgPathName)) {
                            info.put("sbbmName", orgPathName);
                        }
                    }
                });
            }
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo zhcxQueryGJ(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            if (StrUtil.isNotBlank(MapUtil.getStr(queryData, "sqStatus"))) {
                switch (MapUtil.getStr(queryData, "sqStatus")) {
                    case "1":
                        queryData.put("isSqrq", "1");
                        break;
                    case "2":
                        queryData.put("isGwrq", "1");
                        break;
                    default:
                        break;
                }
            }
            if (StrUtil.isNotBlank(MapUtil.getStr(queryData, "isEp"))) {
                queryData.put("stateCode", "EP," + MapUtil.getStr(queryData, "stateCode"));
            }
            if (StrUtil.isNotBlank(MapUtil.getStr(queryData, "stateCode"))) {
                List<String> stateCode = Arrays.asList(MapUtil.getStr(queryData, "stateCode").split(","));
                queryData.put("stateCode", stateCode);
            }
            String loginName = UserSession.getLoginName();
            TableDataInfo queryPage;
            boolean isAdmin = RoleUtil.isAdmin(loginName);
            boolean roleMember = RoleUtil.isRoleMember("KWZL_ALL_READALL", loginName);
            if (!isAdmin || !roleMember) {
                //全路径浏览权限
                List<ADOrg> adOrgs = RoleUtil.getOrgByUser(loginName, "KWZL_ZGBM_READALL");
                if (ObjectUtil.isNotEmpty(adOrgs)) {
                    StringBuffer dynSql = new StringBuffer("(");
                    for (ADOrg adOrg : adOrgs) {
                        if (dynSql.length() > 1) {
                            dynSql.append(" OR ");
                        }
                        dynSql.append(" tkab.FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                    }
                    dynSql.append(")");
                    queryData.put("dynSql", dynSql.toString());
                } else {
                    List<ADOrg> adOrgs2 = RoleUtil.getOrgByUser(loginName, KWZLConstants.KWZL_ZGBM_ADMIN);
                    if (ObjectUtil.isNotEmpty(adOrgs2)) {
                        StringBuffer dynSql = new StringBuffer("(");
                        for (ADOrg adOrg : adOrgs2) {
                            if (dynSql.length() > 1) {
                                dynSql.append(" OR ");
                            }
                            dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                        }
                        dynSql.append(")");
                        queryData.put("dynSql", dynSql.toString());
                    } else {
                        List<ADOrg> adOrgsBmAll = RoleUtil.getOrgByUser(loginName, "KWZL_BM_READALL");
                        if (ObjectUtil.isNotEmpty(adOrgsBmAll)) {
                            StringBuffer dynSql = new StringBuffer("(");
                            for (ADOrg adOrg : adOrgsBmAll) {
                                if (dynSql.length() > 1) {
                                    dynSql.append(" OR ");
                                }
                                dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                            }
                            dynSql.append(")");
                            queryData.put("dynSql", dynSql.toString());
                        } else {
                            List<ADOrg> adOrgsBm = RoleUtil.getOrgByUser(loginName, KWZLConstants.KIZL_BM_ADMIN);
                            if (ObjectUtil.isNotEmpty(adOrgsBm)) {
                                StringBuffer dynSql = new StringBuffer("(");
                                for (ADOrg adOrg : adOrgsBm) {
                                    if (dynSql.length() > 1) {
                                        dynSql.append(" OR ");
                                    }
                                    dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                                }
                                dynSql.append(")");
                                queryData.put("dynSql", dynSql.toString());
                            } else {
                                List<ADOrg> adOrgsFc = RoleUtil.getOrgByUser(loginName, "KWZL_FC_READALL");
                                if (ObjectUtil.isNotEmpty(adOrgsFc)) {
                                    StringBuffer dynSql = new StringBuffer("(");
                                    for (ADOrg adOrg : adOrgsFc) {
                                        if (dynSql.length() > 1) {
                                            dynSql.append(" OR ");
                                        }
                                        dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                                    }
                                    dynSql.append(")");
                                    queryData.put("dynSql", dynSql.toString());
                                } else {
                                    queryData.put("dynSql2", "tkab.BZR ='" + loginName + "' OR tkab.JPG_GW = '" + loginName + "'");
                                }
                            }
                        }
                    }
                }
            }
            queryData.put("displayOrder", "tkab.IN_SQH DESC");
            queryPage = businessApplyBaseinfo.zhcxQuery(queryData);
            if (CollUtil.isEmpty(queryPage.getRows())) {
                queryPage.setRows(new ArrayList<>());
            }
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo zhcxExport(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            Map<String, Object> queryData = getQueryData(inInfo);
            boolean isAdmin = RoleUtil.isAdmin(loginName);
            boolean roleMember = RoleUtil.isRoleMember("KWZL_ALL_READALL", loginName);
            if (!isAdmin || !roleMember) {
                //全路径浏览权限
                List<ADOrg> adOrgs = RoleUtil.getOrgByUser(loginName, "KWZL_ZGBM_READALL");
                if (ObjectUtil.isNotEmpty(adOrgs)) {
                    StringBuffer dynSql = new StringBuffer("(");
                    for (ADOrg adOrg : adOrgs) {
                        if (dynSql.length() > 1) {
                            dynSql.append(" OR ");
                        }
                        dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                    }
                    dynSql.append(")");
                    queryData.put("dynSql", dynSql.toString());
                } else {
                    List<ADOrg> adOrgs2 = RoleUtil.getOrgByUser(loginName, KWZLConstants.KWZL_ZGBM_ADMIN);
                    if (ObjectUtil.isNotEmpty(adOrgs2)) {
                        StringBuffer dynSql = new StringBuffer("(");
                        for (ADOrg adOrg : adOrgs2) {
                            if (dynSql.length() > 1) {
                                dynSql.append(" OR ");
                            }
                            dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                        }
                        dynSql.append(")");
                        queryData.put("dynSql", dynSql.toString());
                    } else {
                        List<ADOrg> adOrgsBmAll = RoleUtil.getOrgByUser(loginName, "KWZL_BM_READALL");
                        if (ObjectUtil.isNotEmpty(adOrgsBmAll)) {
                            StringBuffer dynSql = new StringBuffer("(");
                            for (ADOrg adOrg : adOrgsBmAll) {
                                if (dynSql.length() > 1) {
                                    dynSql.append(" OR ");
                                }
                                dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                            }
                            dynSql.append(")");
                            queryData.put("dynSql", dynSql.toString());
                        } else {
                            List<ADOrg> adOrgsBm = RoleUtil.getOrgByUser(loginName, KWZLConstants.KIZL_BM_ADMIN);
                            if (ObjectUtil.isNotEmpty(adOrgsBm)) {
                                StringBuffer dynSql = new StringBuffer("(");
                                for (ADOrg adOrg : adOrgsBm) {
                                    if (dynSql.length() > 1) {
                                        dynSql.append(" OR ");
                                    }
                                    dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                                }
                                dynSql.append(")");
                                queryData.put("dynSql", dynSql.toString());
                            } else {
                                List<ADOrg> adOrgsFc = RoleUtil.getOrgByUser(loginName, "KWZL_FC_READALL");
                                if (ObjectUtil.isNotEmpty(adOrgsFc)) {
                                    StringBuffer dynSql = new StringBuffer("(");
                                    for (ADOrg adOrg : adOrgsFc) {
                                        if (dynSql.length() > 1) {
                                            dynSql.append(" OR ");
                                        }
                                        dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                                    }
                                    dynSql.append(")");
                                    queryData.put("dynSql", dynSql.toString());
                                } else {
                                    queryData.put("dynSql2", "BZR ='" + loginName + "' OR JPG_GW = '" + loginName + "'");
                                }
                            }
                        }
                    }
                }
            }
            queryData.put("displayOrder", "tkab.IN_SQH DESC");
            List<Map> queryPage = businessApplyBaseinfo.zhcxSlExport(queryData);
            if (CollUtil.isEmpty(queryPage)) {
                queryPage = new ArrayList<>();
            } else {
                queryPage.forEach(info -> {
                    if (StrUtil.isNotBlank(MapUtil.getStr(info, "paStatus"))) {
                        PaStatusTypeEnum orgCode = PaStatusTypeEnum.getPaStatusType(MapUtil.getStr(info, "paStatus"));
                        if (orgCode != null) {
                            info.put("paStatus", orgCode.getDictName());
                        }
                    }
                    if (StrUtil.isNotBlank(MapUtil.getStr(info, "inZltype"))) {
                        info.put("inZltype", SDictUtil.getDictName("KIZL", "KI_PATENT_TYPE", MapUtil.getStr(info, "inZltype")));
                    }
                    if (StrUtil.isNotBlank(MapUtil.getStr(info, "flowStatus"))) {
                        FlowStatusTypeEnum orgCode1 = FlowStatusTypeEnum.getFlowStatusType(MapUtil.getStr(info, "flowStatus"));
                        if (orgCode1 != null) {
                            info.put("flowStatus", orgCode1.getDictName());
                        }
                    }
                    if (StrUtil.isNotBlank(MapUtil.getStr(info, "sbbmCode"))) {
                        String orgPathName = OrgUtil.getOrgPathName(MapUtil.getStr(info, "sbbmCode"));
                        if (StrUtil.isNotBlank(orgPathName)) {
                            info.put("sbbmName", orgPathName);
                        }
                    }
                });
            }
            inInfo.set("list", queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo zhcxExportGJ(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            if (StrUtil.isNotBlank(MapUtil.getStr(queryData, "sqStatus"))) {
                switch (MapUtil.getStr(queryData, "sqStatus")) {
                    case "1":
                        queryData.put("isSqrq", "1");
                        break;
                    case "2":
                        queryData.put("isGwrq", "1");
                        break;
                    default:
                        break;
                }
            }
            if (StrUtil.isNotBlank(MapUtil.getStr(queryData, "isEp"))) {
                queryData.put("stateCode", "EP," + MapUtil.getStr(queryData, "stateCode"));
            }
            if (StrUtil.isNotBlank(MapUtil.getStr(queryData, "stateCode"))) {
                List<String> stateCode = Arrays.asList(MapUtil.getStr(queryData, "stateCode").split(","));
                queryData.put("stateCode", stateCode);
            }
            String loginName = UserSession.getLoginName();
            boolean isAdmin = RoleUtil.isAdmin(loginName);
            boolean roleMember = RoleUtil.isRoleMember("KWZL_ALL_READALL", loginName);
            if (!isAdmin || !roleMember) {
                //全路径浏览权限
                List<ADOrg> adOrgs = RoleUtil.getOrgByUser(loginName, "KWZL_ZGBM_READALL");
                if (ObjectUtil.isNotEmpty(adOrgs)) {
                    StringBuffer dynSql = new StringBuffer("(");
                    for (ADOrg adOrg : adOrgs) {
                        if (dynSql.length() > 1) {
                            dynSql.append(" OR ");
                        }
                        dynSql.append(" tkab.FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                    }
                    dynSql.append(")");
                    queryData.put("dynSql", dynSql.toString());
                } else {
                    List<ADOrg> adOrgs2 = RoleUtil.getOrgByUser(loginName, KWZLConstants.KWZL_ZGBM_ADMIN);
                    if (ObjectUtil.isNotEmpty(adOrgs2)) {
                        StringBuffer dynSql = new StringBuffer("(");
                        for (ADOrg adOrg : adOrgs2) {
                            if (dynSql.length() > 1) {
                                dynSql.append(" OR ");
                            }
                            dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                        }
                        dynSql.append(")");
                        queryData.put("dynSql", dynSql.toString());
                    } else {
                        List<ADOrg> adOrgsBmAll = RoleUtil.getOrgByUser(loginName, "KWZL_BM_READALL");
                        if (ObjectUtil.isNotEmpty(adOrgsBmAll)) {
                            StringBuffer dynSql = new StringBuffer("(");
                            for (ADOrg adOrg : adOrgsBmAll) {
                                if (dynSql.length() > 1) {
                                    dynSql.append(" OR ");
                                }
                                dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                            }
                            dynSql.append(")");
                            queryData.put("dynSql", dynSql.toString());
                        } else {
                            List<ADOrg> adOrgsBm = RoleUtil.getOrgByUser(loginName, KWZLConstants.KIZL_BM_ADMIN);
                            if (ObjectUtil.isNotEmpty(adOrgsBm)) {
                                StringBuffer dynSql = new StringBuffer("(");
                                for (ADOrg adOrg : adOrgsBm) {
                                    if (dynSql.length() > 1) {
                                        dynSql.append(" OR ");
                                    }
                                    dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                                }
                                dynSql.append(")");
                                queryData.put("dynSql", dynSql.toString());
                            } else {
                                List<ADOrg> adOrgsFc = RoleUtil.getOrgByUser(loginName, "KWZL_FC_READALL");
                                if (ObjectUtil.isNotEmpty(adOrgsFc)) {
                                    StringBuffer dynSql = new StringBuffer("(");
                                    for (ADOrg adOrg : adOrgsFc) {
                                        if (dynSql.length() > 1) {
                                            dynSql.append(" OR ");
                                        }
                                        dynSql.append(" FIRST_DEPT_PATH LIKE '%" + adOrg.getOrgCode() + "%'");
                                    }
                                    dynSql.append(")");
                                    queryData.put("dynSql", dynSql.toString());
                                } else {
                                    queryData.put("dynSql2", "tkab.BZR ='" + loginName + "' OR tkab.JPG_GW = '" + loginName + "'");
                                }
                            }
                        }
                    }
                }
            }
            queryData.put("displayOrder", "tkab.IN_SQH DESC");
            List<Map> queryPage = businessApplyBaseinfo.zhcxExport(queryData);
            inInfo.set("list", queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /***
     * 查询详细
     * @param inInfo
     * @return
     */
    public EiInfo getDetailsByGwSqh(EiInfo inInfo) {
        try {
            Map<String, Object> hashMap = new HashMap<>();
            String gwSqh = (String) inInfo.get("gwSqh");
            hashMap.put("gwSqh", gwSqh);
            TkwzlApplyBaseinfo bean = this.businessApplyBaseinfo.load(hashMap);
            TkwzlApplyBaseinfoEx query = new TkwzlApplyBaseinfoEx();
            BeanUtil.copyProperties(bean, query);
            String jwsqId = bean.getJwsqId();
            //查询答复表
            Map<String, Object> param = new HashMap<>();
            param.put("jwsqId", jwsqId);
            param.put("displayOrder", "send_date desc");
            List<TkwzlApplyDfsc> tkwzlApplyDfscs = this.businessApplyDfsc.queryList(param);
            if (ObjectUtil.isNotEmpty(tkwzlApplyDfscs)) {
                tkwzlApplyDfscs.forEach(info -> {
                    Map<String, Object> contentMap = ClobUtil.getContentMap(info.getJwdfscId());
                    if (ObjectUtil.isNotEmpty(contentMap)) {
                        info.setContentStart(MapUtil.getStr(contentMap, "contentStart"));
                        info.setContentFmr(MapUtil.getStr(contentMap, "contentFmr"));
                    }
                });
            }
            query.setDfsc(tkwzlApplyDfscs);
            //查询国家阶段信息表
            Map<String, Object> gjjdParam = new HashMap<>();
            gjjdParam.put("jwsqId", jwsqId);
            List<TkwzlGjjdBaseinfo> tkwzlGjjdBaseinfos = this.businessGjjdBaseinfo.queryList(gjjdParam);
            if (ObjectUtil.isNotEmpty(tkwzlGjjdBaseinfos)) {
                tkwzlGjjdBaseinfos.forEach(info -> {
                    if (StrUtil.isNotBlank(info.getIsvalid())) {
                        info.setIsvalid(SDictUtil.getDictName("KWZL", "KWZL_ISVALID", info.getIsvalid()));
                    }
                });
            }
            query.setGjjds(tkwzlGjjdBaseinfos);
            inInfo.set("applyBaseinfo", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * S_KW_ZL_02根据年度，发明人工号获取（已受理，已授权）专利
     *
     * @param
     * @return
     */
    public EiInfo querySlsqzl(EiInfo inInfo) {
        try {
            String year = (String) inInfo.get("year");
            String empIds = (String) inInfo.get("empIds");
            if (ObjectUtil.isEmpty(year)) {
                inInfo.setMsg("year 年份获取失败！");
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                return inInfo;
            }
            if (ObjectUtil.isEmpty(empIds)) {
                inInfo.setMsg("empId 发明人工号获取失败！");
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                return inInfo;
            }
            List<String> empIdList = Arrays.asList(empIds.split(","));
            Map param = new HashMap();
            param.put("year", year);
            param.put("empIdList", empIdList);
            List<Map> queryList = KwzlUtil.querySlSqzl(param);
            inInfo.set("list", queryList);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * S_KW_ZL_03根据受理日期查询已授权专利
     *
     * @param
     * @return
     */
    public EiInfo querySqzl(EiInfo inInfo) {
        try {
            List<Map<String, Object>> list = new ArrayList<>();
            String startDate = (String) inInfo.get("startDate");
            String endDate = (String) inInfo.get("endDate");
            Map param = new HashMap();
            param.put("startDate", startDate);
            param.put("endDate", endDate);
            List<Map<String, Object>> result = KwzlUtil.querySqzl(param);
            Set<String> strings = result.get(0).keySet();
            for (String str : strings) {
                OrgCodeTypeEnum orgCode = OrgCodeTypeEnum.getOrgCode(str);
                Map resMap = new HashMap();
                if ("OTHER".equals(orgCode.getOrgCode())) {
                    resMap.put("jdCode", "all");
                    resMap.put("jdName", "股份");
                    resMap.put("count", result.get(0).get(str));
                    list.add(resMap);
                } else {
                    resMap.put("jdCode", orgCode.getOrgCode());
                    resMap.put("jdName", orgCode.getOrgName());
                    resMap.put("count", result.get(0).get(str));
                    list.add(resMap);
                }
            }
            inInfo.set("list", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo test(EiInfo inInfo) {
        try {
            Map<String, Object> param = new HashMap<>();
            param.put("isjpg", "1");
            List<TkwzlApplyBaseinfo> tkwzlApplyBaseinfos = this.businessApplyBaseinfo.queryList(param);
            tkwzlApplyBaseinfos.stream().forEach(info -> {
                TkizlPatentInfo patentInfo = PatentApplyUtil.getPatentInfoByPatentNo(info.getSqh());
                if (ObjectUtil.isNotEmpty(patentInfo)) {
                    info.setJpgGw(patentInfo.getJpgGw());
                    this.businessApplyBaseinfo.update("admin", info);
                }
            });
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getCause().getMessage());
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            inInfo.setDetailMsg(e.getCause().getMessage());
        }
        return inInfo;
    }

    /**
     * 批量提交
     *
     * @param inInfo
     * @return
     */
    public EiInfo batchSumbit(EiInfo inInfo) {
        try {
            String msg = "";
            Map row = inInfo.getBlock("i").getRow(0);
            String operator = UserSession.getLoginName();
            //审批提交数据vo对象
            List<Map<String, Object>> taskList = (List<Map<String, Object>>) row.get("taskList");
            String comment = (String) row.get("comment");
            String state = (String) row.get("state");
            boolean b = KWZLConstants.ISVALID_1.equals(state);
            //否定
            int success_c = 0;
            int file_c = 0;
            for (Map<String, Object> map : taskList) {
                String transitionKey = "";
                //业务主键
                String businessGuid = MapUtil.getStr(map, "businessId");
                //任务id
                String taskId = MapUtil.getStr(map, "taskId");
                String activityCode = MapUtil.getStr(map, "currentActivity");
                TkwzlApplyBaseinfo query = this.businessApplyBaseinfo.query(businessGuid);
                Map<String, Object> variables = new HashMap<String, Object>();
                variables.put("orgParamter", query.getSbbmCode());
                List<Map<String, Object>> kwzl_zlsq = KwzlUtil.getPsxx(businessGuid, "kwzl_zlsq");
                if (ObjectUtil.isNotEmpty(kwzl_zlsq)) {
                    if (!KwzlUtil.isPsxx(kwzl_zlsq)) {
                        msg = query.getInZlmc() + ":专家评审未结束;";
                        file_c = file_c + 1;
                        continue;
                    }
                }
                switch (activityCode) {
                    case "Manual5":
                        transitionKey = "Transition7";
                        break;
                    case "Manual7":
                        if (b) {
                            //否定
                            transitionKey = "Transition15";
                            query.setFlowStatus(FlowStatusTypeEnum.END.getDictCode());
                        } else {
                            transitionKey = "Transition9";
                        }
                        break;
                    case "Manual8":
                        if (b) {
                            //否定
                            transitionKey = "Transition11";
                            query.setFlowStatus(FlowStatusTypeEnum.END.getDictCode());
                        } else {
                            /**判断是否进入领导审批*/
                            String sbbmCode = query.getSbbmCode();
                            ADOrg adOrg = OrgUtil.getDirCompany(sbbmCode);
                            TkwzlApplyDeptdict deptdict = this.businessApplyDeptdict.queryOrganizationReview(adOrg.getOrgCode());
                            if (ObjectUtil.isNotEmpty(deptdict)) {
                                if ("0".equals(deptdict.getIsLeader())) {
                                    query.setJrgjcsrq(DateUtil.today());
                                    query.setFlowStatus(KWZLConstants.STATUS_END);
                                    query.setPaStatus(PaStatusTypeEnum.INT.getDictCode());
                                    query.setZxh(NumUtil.getNumSort(query.getSbbmCode(), KWZLConstants.KWZL_ZXH));
                                    Set<String> set = new HashSet<>();
                                    if (StrUtil.isNotBlank(query.getSqgjExpert())) {
                                        set.addAll(Arrays.stream(query.getSqgjExpert().split(",")).collect(Collectors.toSet()));
                                    }
                                    if (StrUtil.isNotBlank(query.getSqgjTcr())) {
                                        set.addAll(Arrays.stream(query.getSqgjTcr().split(",")).collect(Collectors.toSet()));
                                    }
                                    if (ObjectUtil.isNotEmpty(set)) {
                                        query.setSqgjFinal(set.stream().collect(Collectors.joining(",")));
                                    }
                                    transitionKey = "Transition17";
                                } else {
                                    transitionKey = "Transition10";
                                }
                            } else {
                                query.setJrgjcsrq(DateUtil.today());
                                query.setFlowStatus(KWZLConstants.STATUS_END);
                                query.setPaStatus(PaStatusTypeEnum.INT.getDictCode());
                                query.setZxh(NumUtil.getNumSort(query.getSbbmCode(), KWZLConstants.KWZL_ZXH));
                                Set<String> set = new HashSet<>();
                                if (StrUtil.isNotBlank(query.getSqgjExpert())) {
                                    set.addAll(Arrays.stream(query.getSqgjExpert().split(",")).collect(Collectors.toSet()));
                                }
                                if (StrUtil.isNotBlank(query.getSqgjTcr())) {
                                    set.addAll(Arrays.stream(query.getSqgjTcr().split(",")).collect(Collectors.toSet()));
                                }
                                if (ObjectUtil.isNotEmpty(set)) {
                                    query.setSqgjFinal(set.stream().collect(Collectors.joining(",")));
                                }
                                transitionKey = "Transition17";
                            }
                        }
                        break;
                    case "Manual9":
                        if (b) {
                            //否定
                            transitionKey = "Transition16";
                        } else {
                            transitionKey = "Transition12";
                            query.setIsvalid(KWZLConstants.ISVALID_1);
                        }
                        query.setFlowStatus(FlowStatusTypeEnum.END.getDictCode());
                        break;
                    default:
                        break;

                }
                if (b) {
                    //否定
                    query.setIsvalid(KWZLConstants.ISVALID_0);
                    query.setPaStatus(PaStatusTypeEnum.STOP.getDictCode());
                }
                this.businessApplyBaseinfo.update(operator, query);
                msg = SWorkFlowUtil.submit(operator, businessGuid, taskId, transitionKey, comment, null, null, variables);
                success_c = success_c + 1;
            }
            inInfo.setMsg("成功" + success_c + "条，" + "失败" + file_c + "条，" + msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * S_KW_ZL_06 科技统计
     *
     * @param
     * @return
     */
    public EiInfo queryApi01(EiInfo inInfo) {
        try {
            String startDate = (String) inInfo.get("startDate");
            String endDate = (String) inInfo.get("endDate");
            String gldwCode = (String) inInfo.get("gldwCode");
            Map param = new HashMap();
            param.put("startDate", startDate);
            param.put("endDate", endDate);
            param.put("gldwCode", gldwCode);

            int kwzl_count = 0;//累计申请国际专利数量

            int kwzl_cy_fm_count = 0;//拥有有效国际发明专利数
            int kwzl_cy_yss_count = 0;//已被实施
            int kwzl_cy_count = 0;//拥有有效国际专利数

            int kwzl_ts_sq_count = 0;//当年国际专利授权数
            int kwzl_ts_sq_fm_count = 0;//当年发明专利授权数

            int kwzl_ts_sl_count = 0;//当年国际专利申请数
            int kwzl_ts_sl_fm_count = 0;//当年国际发明专利申请数
            int kwzl_ts_sl_pct_count = 0;//当年国际专利pct申请数
            int kwzl_ts_sl_blgy_count = 0;//当年国际专利巴黎公约申请数

            //当年专利申请数
            param.put("rangeField", "slrq");
            List<Map<String, Integer>> slCount = this.businessApplyBaseinfo.querySlApi(param);
            kwzl_ts_sl_count = slCount.stream().map(a -> a.get("count")).reduce(0, Integer::sum);
            kwzl_ts_sl_fm_count = slCount.stream().map(a -> a.get("fmCount")).reduce(0, Integer::sum);
            kwzl_ts_sl_pct_count = slCount.stream().map(a -> a.get("pctCount")).reduce(0, Integer::sum);
            //累计申请国际专利数量
            param.put("rangeField", "gjrq");
            List<Map<String, Integer>> gjCount = this.businessApplyBaseinfo.querySlApi(param);
            kwzl_count = gjCount.stream().map(a -> a.get("count")).reduce(0, Integer::sum);
            //当年专利授权数
            param.put("rangeField", "sqrq");
            List<Map<String, Integer>> sqCount = this.businessApplyBaseinfo.querySqApi(param);
            kwzl_ts_sq_count = sqCount.stream().map(a -> a.get("count")).reduce(0, Integer::sum);
            kwzl_ts_sq_fm_count = sqCount.stream().map(a -> a.get("fmCount")).reduce(0, Integer::sum);
            //累计拥有有效的专利数
            param.put("isValid", true);
            List<Map<String, Integer>> sqyxCount = this.businessApplyBaseinfo.querySqApi(param);
            kwzl_cy_count = sqyxCount.stream().map(a -> a.get("count")).reduce(0, Integer::sum);
            kwzl_cy_fm_count = sqyxCount.stream().map(a -> a.get("fmCount")).reduce(0, Integer::sum);
            inInfo.set("kwzl_count", kwzl_count);
            inInfo.set("kwzl_cy_fm_count", kwzl_cy_fm_count);
            inInfo.set("kwzl_cy_yss_count", kwzl_cy_yss_count);
            inInfo.set("kwzl_cy_count", kwzl_cy_count);
            inInfo.set("kwzl_ts_sq_count", kwzl_ts_sq_count);
            inInfo.set("kwzl_ts_sq_fm_count", kwzl_ts_sq_fm_count);
            inInfo.set("kwzl_ts_sl_count", kwzl_ts_sl_count);
            inInfo.set("kwzl_ts_sl_fm_count", kwzl_ts_sl_fm_count);
            inInfo.set("kwzl_ts_sl_pct_count", kwzl_ts_sl_pct_count);
            inInfo.set("kwzl_ts_sl_blgy_count", kwzl_ts_sl_blgy_count);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * S_KW_ZL_08 科研项目
     *
     * @param
     * @return
     */
    public EiInfo queryApi02(EiInfo inInfo) {
        try {
//            List<Map<String, Object>> list = new ArrayList<>();
//            String projectCodes = (String) inInfo.get("projectCodes");
//            String startDate = (String) inInfo.get("startDate");
//            String endDate = (String) inInfo.get("endDate");
//            String year = (String) inInfo.get("year");
//            Map param = new HashMap();
//            param.put("startDate", startDate);
//            param.put("endDate", endDate);
//            param.put("year", year);
//            if (StrUtil.isNotBlank(projectCodes)) {
//                Set<String> set = new HashSet(Arrays.asList(projectCodes.split(",")));
//                List<String> lists = new ArrayList<>(set);
//                param.put("projectCodes", lists);
//            }
//            List<Map<String, Object>> result = KwzlUtil.queryKwzlByFromNo(param);
//            System.out.println(result);
//            inInfo.set("list", result);


            TkymmTechnology technology = this.businessTechnology.query("20240703154936780299008");
            technology.setStatus(MMConstans.aprove_status_identified);
            technology.setConfirmNum (RDMMutli.getConfirmNum("20240703154936780299008"));
            technology.setConfirmTime(DateUtil.today());
            businessTechnology.update(UserSession.getLoginName(),technology);

            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }



    /**
     * 专家评审信息录入
     *
     * @param operator
     * @param businessId
     * @param reviewType
     * @param reviewResults
     * @param processCode
     * @param activityCode
     */
    public void reviewRecord(String operator, String businessId, String reviewType, String reviewResults, String reviewFlag, String processCode, String activityCode) {
        String logGh = operator;
        String logXm = UserUtil.getUserName(logGh);
        String logDate = DateUtil.formatDate(new Date());
        String logIdent = null;
        String logYj = reviewFlag;
        String flowName = null;
        String lsh = null;
        String orgMc = null;
        //主流程
        switch (processCode) {
            case KWZLConstants.KWZLAPPLY:
                flowName = "境外专利申请";
                TkwzlApplyBaseinfo applyBaseinfo = this.businessApplyBaseinfo.query(businessId);
                orgMc = OrgUtil.getOrgPathName(applyBaseinfo.getSbbmCode());
                switch (activityCode) {
                    case "Manual6":
                        logIdent = "境外专利申请_公司主管部门管理员";
                        break;
                    case "Manual7":
                        logIdent = "境外专利申请_公司主管部门主管";
                        break;
                    default:
                        //获取节点配置的角色
                        String roleCode = SWorkFlowUtil.getRoleValue(processCode, activityCode);
                        //根据角色获取名称
                        logIdent = "境外专利申请_" + RoleUtil.getRoleName(roleCode);
                        break;
                }
                break;
            case KWZLConstants.KWZL_HPG:
                flowName = "外专利后评估";
                TkwzlApplyHpginfo applyHpginfo = this.businessApplyHpginfo.query(businessId);
                TkwzlApplyBaseinfo applyBaseinfo2 = this.businessApplyBaseinfo.query(applyHpginfo.getApplyId());
                orgMc = OrgUtil.getOrgPathName(applyBaseinfo2.getSbbmCode());
                switch (activityCode) {
                    case "Manual3":
                        logIdent = "境外专利后评估_部门管理员";
                        break;
                    case "Manual4":
                        logIdent = "境外专利后评估_部门领导";
                        break;
                    case "Manual5":
                        logIdent = "境外专利后评估_事业部管理员";
                        break;
                    case "Manual8":
                        logIdent = "境外专利后评估_公司主管部门管理员";
                        break;
                    default:
                        //获取节点配置的角色
                        String roleCode = SWorkFlowUtil.getRoleValue(processCode, activityCode);
                        //根据角色获取名称
                        logIdent = "境外专利后评估_" + StrUtil.nullToEmpty(RoleUtil.getRoleName(roleCode));
                        break;
                }
                break;
            default:
                break;
        }
        //评审费明细表GGMK.T_MPPS_REVIEW_MEMO
        TmppsReviewMemo reviewMemo = new TmppsReviewMemo();
        reviewMemo.setEmpId(operator);
        reviewMemo.setEmpName(UserUtil.getUserName(operator));
        //获取部门  获取单位
        ADOrg adorg = OrgUtil.getMainOrgByUserCode(operator);
        if (ObjectUtil.isNotEmpty(adorg)) {
            reviewMemo.setDeptCode(adorg.getOrgCode());
            reviewMemo.setDeptName(adorg.getOrgPathName());
        }
        reviewMemo.setReviewType(reviewType);
        reviewMemo.setExtra1(businessId);
        reviewMemo.setExtra2(logIdent);
        reviewMemo.setExtra3(logYj);
        reviewMemo.setExtra4(activityCode);
        reviewMemo.setExtra5("0");//扩展字段5已启评审流程1是0否

        HashMap param = new HashMap();
        param.put("reviewType", reviewType);
        param.put("extra1", businessId);
        param.put("empId", operator);
        List<TmppsReviewMemo> list = businessReviewMemo.queryList(param);
        if (ObjectUtil.isNotEmpty(list)) {
            //更新
            String psmemoId = list.get(0).getPsmemoId();
            reviewMemo.setPsmemoId(psmemoId);
            this.businessReviewMemo.update(operator, reviewMemo);
        } else {
            //插入
            this.businessReviewMemo.insert(operator, reviewMemo);
        }
        //评审费主表ZZZC.T_KIZL_PSFLOG
        TkizlPsflog psflog = new TkizlPsflog();
        psflog.setDocId(businessId);
        psflog.setOrgmc(orgMc);
        psflog.setLoggh(logGh);
        psflog.setLogxm(logXm);
        psflog.setLogdate(logDate);
        psflog.setLogIdent(logIdent);
        psflog.setLogYj(logYj);
        psflog.setFlowName(flowName);
        psflog.setExtra1("100");
        psflog.setExtra2(KWZLConstants.MODULE_CODE);
        HashMap psmap = new HashMap();
        psmap.put("docId", businessId);
        psmap.put("loggh", operator);
        psmap.put("flowName", flowName);
        List<TkizlPsflog> pslist = this.businessPsf.queryList(psmap);
        if (ObjectUtil.isNotEmpty(list)) {
            String logId = pslist.get(0).getLogId();
            psflog.setLogId(logId);
            this.businessPsf.update(operator, psflog);
        } else {
            this.businessPsf.insert(logGh, psflog);
        }
    }

}
