package com.baosight.bscdkj.kw.zl.utils;


import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bscdkj.common.kw.domain.TkwzlHpgExport;
import com.baosight.bscdkj.kw.zl.constant.KWZLConstants;
import com.baosight.bscdkj.mp.ad.dto.ADOrg;
import com.baosight.bscdkj.mp.ad.utils.OrgUtil;
import com.baosight.iplat4j.core.data.dao.DaoFactory;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

public class KwzlTjUtil {

    public static List<Map<String, Object>> querySqSqxx(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.querySqSqxx";
        param.put("isValid", KWZLConstants.ISVALID_1);
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        query.stream().forEach(info -> {
            String deptCode = MapUtil.getStr(info, "deptCode");
            if (StrUtil.isNotBlank(deptCode)) {
                if (deptCode.contains("/")) {
                    deptCode = StrUtil.removePrefix(deptCode, "/");
                    info.put("deptCode", deptCode);
                }
                ADOrg adOrg = OrgUtil.getOrgByOrgCode(deptCode);
                if (ObjectUtil.isNotEmpty(adOrg)) {
                    info.put("deptName", adOrg.getOrgName());
                }
            }
        });
        return query;
    }

    public static List<Map<String, Object>> querySqSqxxQuarter(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.querySqSqxxQuarter";
        param.put("isValid", KWZLConstants.ISVALID_1);
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        return query;
    }

    public static List<Map<String, Object>> querySqSqxxMonth(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.querySqSqxxMonth";
        param.put("isValid", KWZLConstants.ISVALID_1);
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        return query;
    }

    public static List<Map<String, Object>> querySqSqxxJpg(Map param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.querySqSqxxJpg";
        param.put("isValid", KWZLConstants.ISVALID_1);
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        return query;
    }

    public static List<Map<String, Object>> querySqSqxxJpgMonth(Map param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.querySqSqxxJpgMonth";
        param.put("isValid", KWZLConstants.ISVALID_1);
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        return query;
    }

    public static List<Map<String, Object>> querySqSqxxJpgQuarter(Map param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.querySqSqxxJpgQuarter";
        param.put("isValid", KWZLConstants.ISVALID_1);
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        return query;
    }

    public static List<Map<String, Object>> queryGjsqrq(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryGjsqrq";
        param.put("isValid", KWZLConstants.ISVALID_1);
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryGjsqrqCt(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryGjsqrqCt";
        param.put("isValid", KWZLConstants.ISVALID_1);
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        return query;
    }

    public static List<Map<String, Object>> queryGjsqrqJpg(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryGjsqrqJpg";
        param.put("isValid", KWZLConstants.ISVALID_1);
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryGjsqrqJpgCt(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryGjsqrqJpgCt";
        param.put("isValid", KWZLConstants.ISVALID_1);
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        return query;
    }

    public static Map<String, Object> sqxxCollect(Map<String, Object> map) {
        Map<String, Object> param = new HashMap();
        String zllx = MapUtil.getStr(map, "zllx");
        String tjlx = MapUtil.getStr(map, "tjlx");
        String startDate = MapUtil.getStr(map, "startDate");
        String endDate = MapUtil.getStr(map, "endDate");
        String jd = MapUtil.getStr(map, "jd");
        if (StrUtil.isNotBlank(jd)) {
            //基地
            if (jd.equals("BGTA")) {//宝山
                param.put("bsJd", true);
            }else {
                param.put("jd", jd);
            }
        }
        //专利类型
        if (zllx.equals("QB")) {//全部
            param.put("zllx", "");
        } else if(zllx.equals("GTCP")) {
            param.put("zllx", "");
            param.put("isGtcp", true);
        }else {
            param.put("zllx", zllx);
        }
        //统计类型
        switch (tjlx) {
            case "01"://进入国际阶段日期
                param.put("intDate", startDate);
                param.put("intDateEd", endDate);
                break;
            case "02"://申请日期
                param.put("applyDate", startDate);
                param.put("applyDateEd", endDate);
                break;
            case "03"://国际授权日期
                param.put("cyDate", startDate);
                param.put("cyDateEd", endDate);
                param.put("gjsq", true);
                param.put("isSt", true);
                break;
            case "04"://国家授权日期
                param.put("cyDate", startDate);
                param.put("cyDateEd", endDate);
                param.put("cysq", true);
                param.put("isSt", true);
                break;
            default:
                break;
        }
        return param;
    }


    public static List<TkwzlHpgExport> queryHpgExpInfo(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryHpgExpInfo";
        int count = platSqlDao.count(name, param);
        List<TkwzlHpgExport> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryGjhjtj(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryGjhjtj";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryNoGjhjtj(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryNoGjhjtj";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryLnGjhjtj(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryLnGjhjtj";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryLnNoGjhjtj(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryLnNoGjhjtj";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryMgZlSqTj(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryMgZlSqTj";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static Map queryzlsqsCount(Map<String, Object> param) {
        Map map = new HashMap();
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryzlsqsCount";
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        if (ObjectUtil.isNotEmpty(query)) {
            for (Map<String, Object> resMap : query) {
                if ("CN".equals(MapUtil.getStr(resMap,"type"))) {
                    map.put("snsq",MapUtil.getStr(resMap,"count"));
                } else {
                    map.put("ausq",MapUtil.getStr(resMap,"count"));
                }
            }
        } else {
            map.put("snsq",0);
            map.put("ausq",0);
        }
        return map;
    }

    public static List<Map<String, Object>> queryzlsqsCountGroupBydw(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryzlsqsCountGroupBydw";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryGjsqList(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryGjsqList";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryGwSqsCount(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryGwSqsCount";
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        return query;
    }

    public static List<Map<String, Object>> queryzlsqsGroupBy(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryzlsqsGroupBy";
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        return query;
    }

    /***
     * 过滤
     * @param list
     * @return
     */
    public static List<Map<String, Object>> mapGroupViaLam(List<Map<String, Object>> list, String specify) {
        List<String> groupKeys = new ArrayList<>(Arrays.asList(specify.split(",")));
        String groupNameStr = reBuildGroupMap(list, groupKeys);
        System.out.println("--------分组前-------------");
        list.forEach(x -> {
            System.out.println(x);
        });
        List<Map<String, Object>> result = new ArrayList<>();
        Map<String, List<Map<String, Object>>> glist = list.stream()
                .collect(Collectors.groupingBy(e -> e.get(groupNameStr).toString()));
        glist.forEach((k, slist) -> {
            Map<String, Object> nmap = new HashMap<>();
            slist.stream().forEach(param -> {
                String patentType = MapUtil.getStr(param, "name");
                String count = MapUtil.getStr(param, "count");
                if (StrUtil.isBlank(count)) {
                    count = "0";
                }
                if ("A".equals(patentType)) {
                    param.put("sq",count );
                    param.put("nosq", "0");
                } else if ("B".equals(patentType)) {
                    param.put("nosq", count);
                    param.put("sq", "0");
                }
            });
            DoubleSummaryStatistics sumcc = slist.stream().collect(Collectors.summarizingDouble(e -> Double.valueOf(e.get("sq").toString())));
            DoubleSummaryStatistics sumcc2 = slist.stream().collect(Collectors.summarizingDouble(e -> Double.valueOf(e.get("nosq").toString())));
            nmap.put("sbbmCode", slist.get(0).get("sbbmCode"));
            nmap.put("sbbmName", slist.get(0).get("sbbmName"));
            DecimalFormat decimalFormat = new DecimalFormat("#");
            nmap.put("sq", decimalFormat.format(sumcc.getSum()));
            nmap.put("nosq", decimalFormat.format(sumcc2.getSum()));
            result.add(nmap);
        });
        System.out.println("--------分组后-------------");
        result.forEach(x -> {
            System.out.println(x);
            String sbbmCode = MapUtil.getStr(x, "sbbmCode");
            String orgName = OrgUtil.getOrgPathName(sbbmCode);
            if (StrUtil.isNotBlank(orgName)) {
                x.put("sbbmName", orgName);
            }
        });
        return result;
    }

    /**
     * 重建需要group by 的map 添加一列数据保存所有groupby拼接字段
     *
     * @param excelDatas
     * @param groupKeys
     */
    public static String reBuildGroupMap(List<Map<String, Object>> excelDatas, List<String> groupKeys) {
        String groupStr = "";
        if (excelDatas != null && !excelDatas.isEmpty() && groupKeys != null && !groupKeys.isEmpty()) {
            StringBuffer newKey = new StringBuffer();
            StringBuffer newVal = new StringBuffer();
            for (int i = 0, excelDataSize = excelDatas.size(); i < excelDataSize; i++) {
                Map<String, Object> excelData = excelDatas.get(i);
                Set<Map.Entry<String, Object>> eSets = excelData.entrySet();
                for (Map.Entry<String, Object> entry : eSets) {
                    String key = entry.getKey();
                    String val = entry.getValue() == null ? "" : entry.getValue().toString();
                    if (groupKeys.contains(key)) {
                        newKey.append(key);
                        newVal.append(val);
                    }
                }
                if (newKey.length() > 0 && newVal.length() > 0) {
                    if (i == 0) {
                        groupStr = newKey.toString();
                    }
                    excelData.put(newKey.toString(), newVal.toString());
                }
                newKey.setLength(0);
                newVal.setLength(0);
            }
        }
        return groupStr;
    }

    public static List<Map<String, Object>> queryGwSqsList(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryGwSqsList";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryJfTjList(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryJfTjList";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryJfTjSum(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryJfTjSum";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryMROtjList(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryMROtjList";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryAUtjCount(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryAUtjCount";
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        return query;
    }

    public static List<Map<String, Object>> queryAUtjList(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryAUtjList";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryZjpsCount(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryZjpsCount";
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        return query;
    }

    public static List<Map<String, Object>> queryZjpsList(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryZjpsList";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> countHpgYear() {
        Map<String, Object> param = new HashMap();
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.countHpgYear";
        List<Map<String, Object>> query = platSqlDao.query(name, param);
        return query;
    }

    public static List<Map<String, Object>> queryHpgGZCount(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryHpgGZCount";
        List<Map<String, Object>> query = platSqlDao.query(name, param);
        query.stream().forEach(info -> {
            String zs = MapUtil.getStr(info, "zs");
            String[] tableNames = {
                    "fmrwpg", "zjps", "bmgly", "bmld", "bmfq", "dwgly", "dwld", "dwfq", "zgbmgly", "zgbmzg", "zgbmbz", "zgbmld", "pgjs", "zs", "fq"
            };
            if (StrUtil.isBlank(zs)) {
                zs = "0";
            }
            for (int i = 0; i < tableNames.length; i++) {
                String str = tableNames[i];
                String value = MapUtil.getStr(info, str);
                if (StrUtil.isBlank(value)) {
                    value = "0";
                }
                info.put(str, value + "(" + getPercent(Double.valueOf(value), Double.valueOf(zs)) + ")");
            }
        });
        return query;
    }

    public static List<Map<String, Object>> queryHpgGZTotal(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryHpgGZTotal";
        List<Map<String, Object>> query = platSqlDao.query(name, param);
        query.stream().forEach(info -> {
            String zs = MapUtil.getStr(info, "zs");
            String[] tableNames = {
                    "fmrwpg", "zjps", "bmgly", "bmld", "bmfq", "dwgly", "dwld", "dwfq", "zgbmgly", "zgbmzg", "zgbmbz", "zgbmld", "pgjs", "zs", "fq"
            };
            if (StrUtil.isBlank(zs)) {
                zs = "0";
            }
            for (int i = 0; i < tableNames.length; i++) {
                String str = tableNames[i];
                String value = MapUtil.getStr(info, str);
                if (StrUtil.isBlank(value)) {
                    value = "0";
                }
                info.put(str, value + "(" + getPercent(Double.valueOf(value), Double.valueOf(zs)) + ")");
            }
        });
        return query;
    }

    public static List<Map<String, Object>> queryHpgGZList(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryHpgGZList";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count / 1000 + 1; i++) {
            query.addAll(platSqlDao.query(name, param, i * 1000, 1000));
        }
        return query;
    }

    /***
     * 计算百分比
     * @param x
     * @param total
     * @return
     */
    private static String getPercent(double x, double total) {
        double f = 0;
        if (total != 0) {
            f = x * 100 / total;
        }
        BigDecimal bg = new BigDecimal(f);
        BigDecimal f1 = bg.setScale(2, BigDecimal.ROUND_HALF_UP);
        return f1.toString() + "%";
    }

    public static List<Map<String, Object>> queryHpgGZXZCount(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryHpgGZXZCount";
        List<Map<String, Object>> query = platSqlDao.query(name, param);
        query.stream().forEach(info -> {
            String zs = MapUtil.getStr(info, "zs");
            String[] tableNames = {
                    "fmrwpg", "zjps", "bmgly", "bmld", "bmfq", "dwgly", "dwld", "dwfq", "zgbmgly", "zgbmzg", "zgbmbz", "zgbmld", "pgjs", "zs", "fq"
            };
            if (StrUtil.isBlank(zs)) {
                zs = "0";
            }
            for (int i = 0; i < tableNames.length; i++) {
                String str = tableNames[i];
                String value = MapUtil.getStr(info, str);
                if (StrUtil.isBlank(value)) {
                    value = "0";
                }
                info.put(str, value + "(" + getPercent(Double.valueOf(value), Double.valueOf(zs)) + ")");
            }
        });
        return query;
    }

    public static List<Map<String, Object>> queryHpgGZXZTotal(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryHpgGZXZTotal";
        List<Map<String, Object>> query = platSqlDao.query(name, param);
        query.stream().forEach(info -> {
            String zs = MapUtil.getStr(info, "zs");
            String[] tableNames = {
                    "fmrwpg", "zjps", "bmgly", "bmld", "bmfq", "dwgly", "dwld", "dwfq", "zgbmgly", "zgbmzg", "zgbmbz", "zgbmld", "pgjs", "zs", "fq"
            };
            if (StrUtil.isBlank(zs)) {
                zs = "0";
            }
            for (int i = 0; i < tableNames.length; i++) {
                String str = tableNames[i];
                String value = MapUtil.getStr(info, str);
                if (StrUtil.isBlank(value)) {
                    value = "0";
                }
                info.put(str, value + "(" + getPercent(Double.valueOf(value), Double.valueOf(zs)) + ")");
            }
        });
        return query;
    }

    public static Map<String, Object> queryHpgZjpsyj(String recordId) {
        if (StrUtil.isBlank(recordId)) {
            return null;
        }
        Map<String, Object> param = new HashMap<>();
        param.put("recordId", recordId);
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryHpgZjpsyj";
        List<Map<String, Object>> query = platSqlDao.query(name, param);
        if (ObjectUtil.isNotEmpty(query)) {
            param.put("bmZjps", query.get(0).get("bmZjps"));
            param.put("sybZjps", query.get(0).get("sybZjps"));
            return param;
        }
        return null;
    }

    public static List<Map<String, Object>> queryHpgJGCount(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryHpgJGCount";
        List<Map<String, Object>> query = platSqlDao.query(name, param);
        return query;
    }

    public static List<Map<String, Object>> queryHpgJGList(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryHpgJGList";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count / 1000 + 1; i++) {
            query.addAll(platSqlDao.query(name, param, i * 1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> queryCBSqSqxx(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryCBSqSqxx";
        param.put("isValid", KWZLConstants.ISVALID_1);
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        query.stream().forEach(info -> {
            String deptCode = MapUtil.getStr(info, "deptCode");
            if (StrUtil.isNotBlank(deptCode)) {
                if (deptCode.contains("/")) {
                    deptCode = StrUtil.removePrefix(deptCode, "/");
                    info.put("deptCode", deptCode);
                }
                ADOrg adOrg = OrgUtil.getOrgByOrgCode(deptCode);
                if (ObjectUtil.isNotEmpty(adOrg)) {
                    info.put("deptName", adOrg.getOrgName());
                }
            }
        });
        return query;
    }


    public static List<Map<String, Object>> queryAUSqtjCount(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryAUSqtjCount";
        List<Map<String, Object>> query = platSqlDao.query(name,param);
        return query;
    }

    public static List<Map<String, Object>> queryAUSqtjList(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.queryAUSqtjList";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

    public static List<Map<String, Object>> zhcxSlQuery(Map<String, Object> param) {
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        String name = "tkwzlExpand.zhcxSlQuery";
        int count = platSqlDao.count(name, param);
        List<Map<String, Object>> query = new ArrayList<>();//数据库查询的原始数据
        for (int i = 0; i < count/1000+1; i++) {
            query.addAll(platSqlDao.query(name, param, i*1000, 1000));
        }
        return query;
    }

}
