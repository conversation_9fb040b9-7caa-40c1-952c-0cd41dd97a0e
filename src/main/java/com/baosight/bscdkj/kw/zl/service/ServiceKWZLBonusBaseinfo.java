package com.baosight.bscdkj.kw.zl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.baosight.bscdkj.kw.ht.util.KwhtUtil;
import com.baosight.bscdkj.kw.zl.enums.FJTypeEnum;
import com.baosight.bscdkj.kw.zl.enums.FlowStatusTypeEnum;
import com.baosight.bscdkj.kw.zl.utils.KwzlFJUtil;
import com.baosight.bscdkj.kw.zl.utils.KwzlUtil;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.exception.BusinessException;
import com.baosight.bscdkj.common.kw.domain.*;
import com.baosight.bscdkj.common.service.PageService;
import com.baosight.bscdkj.kw.zl.business.BusinessBonusBaseinfoKwzl;
import com.baosight.bscdkj.kw.zl.business.BusinessBonusPainfoKwzl;
import com.baosight.bscdkj.kw.zl.business.BusinessBonusPersonKwzl;
import com.baosight.bscdkj.mp.ad.utils.UserUtil;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 境外专利_发奖_下发Service接口
 *
 * <AUTHOR>
 * @date 2021-11-08
 */
@Service
public class ServiceKWZLBonusBaseinfo extends PageService {
    @Autowired
    private BusinessBonusBaseinfoKwzl businessBonusBaseinfo;
    @Autowired
    private BusinessBonusPainfoKwzl businessBonusPainfo;
    @Autowired
    private BusinessBonusPersonKwzl businessBonusPerson;

    Logger logger = LoggerFactory.getLogger(this.getClass());

    /**
     * 初始化
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkwzlBonusBaseinfo> queryList = businessBonusBaseinfo.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo page(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo queryPage = businessBonusBaseinfo.quertPage(queryData);
            if (CollUtil.isEmpty(queryPage.getRows())) {
                queryPage.setRows(new ArrayList<>());
            } else {
                List<TkwzlBonusBaseinfo> rows = (List<TkwzlBonusBaseinfo>) queryPage.getRows();
                rows.forEach(info -> {
                    if (StrUtil.isNotBlank(info.getStatus())) {
                        FlowStatusTypeEnum orgCode1 = FlowStatusTypeEnum.getFlowStatusType(info.getStatus());
                        if (orgCode1 != null) {
                            info.setExtra1(orgCode1.getDictName());
                        }
                    }
                });
            }
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryCL(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);


            TableDataInfo queryPage = businessBonusBaseinfo.quertPage(queryData);
            if (CollUtil.isEmpty(queryPage.getRows())) {
                queryPage.setRows(new ArrayList<>());
            } else {
                List<TkwzlBonusBaseinfo> rows = (List<TkwzlBonusBaseinfo>) queryPage.getRows();
                rows.forEach(info -> {
                    if (StrUtil.isNotBlank(info.getStatus())) {
                        FlowStatusTypeEnum orgCode1 = FlowStatusTypeEnum.getFlowStatusType(info.getStatus());
                        if (orgCode1 != null) {
                            info.setExtra1(orgCode1.getDictName());
                        }
                    }
                });
            }
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            Map row = inInfo.getBlock("i").getRow(0);
            TkwzlBonusBaseinfo bean = BeanUtil.toBean(row, TkwzlBonusBaseinfo.class);
            businessBonusBaseinfo.update(UserSession.getLoginName(), bean);
            inInfo.setMsg("修改成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            Map row = inInfo.getBlock("i").getRow(0);
            TkwzlBonusBaseinfo bean = BeanUtil.toBean(row, TkwzlBonusBaseinfo.class);
            businessBonusBaseinfo.insert(UserSession.getLoginName(), bean);
            inInfo.setMsg("添加成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo load(EiInfo inInfo) {
        try {
            String downId = (String) inInfo.get("downId");
            TkwzlBonusBaseinfo query = businessBonusBaseinfo.query(downId);
            if (null != query.getTotal()) {
                query.setTotal(KwhtUtil.clearNoUseZeroForBigDecimal(query.getTotal()));
            }
            TkwzlBonusBaseinfoEx data = new TkwzlBonusBaseinfoEx();
            BeanUtil.copyProperties(query, data);
            Map<String, Object> hashMap = new HashMap<>();
            hashMap.put("downId", query.getDownId());
            List<TkwzlBonusPainfo> bonusPainfos = this.businessBonusPainfo.queryList(hashMap);
            List<Map<String, Object>> defition = new ArrayList<>();
            if (ObjectUtil.isNotEmpty(bonusPainfos)) {
                for (TkwzlBonusPainfo bonusPainfo : bonusPainfos) {
                    if (StrUtil.isNotBlank(bonusPainfo.getStatus())) {
                        FlowStatusTypeEnum flowStatusType = FlowStatusTypeEnum.getFlowStatusType(bonusPainfo.getStatus());
                        if (ObjectUtil.isNotEmpty(flowStatusType)) {
                            bonusPainfo.setStatus(flowStatusType.getDictName());
                        }
                    }
                    if (null != bonusPainfo.getTotalJe()) {
                        bonusPainfo.setTotalJe(KwhtUtil.clearNoUseZeroForBigDecimal(bonusPainfo.getTotalJe()));
                    }
                    FJTypeEnum fjType = FJTypeEnum.getFJType(bonusPainfo.getBonusType());
                    switch (fjType) {
                        case SL:
                            TkwzlApplyBaseinfoEx applyBaseinfo = KwzlUtil.getApplyBaseInfoByJwsqId(bonusPainfo.getJwzlId());
                            if (ObjectUtil.isNotEmpty(applyBaseinfo)) {
                                bonusPainfo.setJwsqId(applyBaseinfo.getJwsqId());
                                bonusPainfo.setGwSqh(applyBaseinfo.getGwSqh());
                                bonusPainfo.setInZlmc(applyBaseinfo.getInZlmc());
                                bonusPainfo.setInSqh(applyBaseinfo.getInSqh());
                                bonusPainfo.setFirstPerson(applyBaseinfo.getFirstPerson());
                                bonusPainfo.setFirstPersonName(applyBaseinfo.getFirstPersonName());
                                String sqgjFinal = "";
                                List<TkwzlGjjdBaseinfo> gjjdBaseInfos = KwzlUtil.getGjjdBaseInfoByJwsqId(applyBaseinfo.getJwsqId());
                                List<String> list = new ArrayList<>();
                                if (ObjectUtil.isNotEmpty(gjjdBaseInfos)) {
                                    for (TkwzlGjjdBaseinfo info : gjjdBaseInfos) {
                                        list.add(info.getStateName());
                                    }
                                    sqgjFinal = list.stream().collect(Collectors.joining(","));
                                    bonusPainfo.setSqgjFinal(sqgjFinal);
                                    bonusPainfo.setJrgjjdrq(applyBaseinfo.getJrgjjdrq());
                                }
                            }
                            break;
                        case SQ:
                            TkwzlGjjdBaseinfo gjjdBaseInfo = KwzlUtil.getGjjdBaseInfoByJwzlId(bonusPainfo.getJwzlId());
                            if (ObjectUtil.isNotEmpty(gjjdBaseInfo)) {
                                bonusPainfo.setJwsqId(gjjdBaseInfo.getJwsqId());
                                bonusPainfo.setStateName(gjjdBaseInfo.getStateName());
                                bonusPainfo.setStateSqrq(gjjdBaseInfo.getStateSqrq());
                                bonusPainfo.setGjSqh(gjjdBaseInfo.getGjSqh());
                                TkwzlApplyBaseinfoEx gjInfo = KwzlUtil.getApplyBaseInfoByJwsqId(gjjdBaseInfo.getJwsqId());
                                bonusPainfo.setInZlmc(gjInfo.getInZlmc());
                                bonusPainfo.setInSqh(gjInfo.getInSqh());
                                bonusPainfo.setGwSqh(gjInfo.getGwSqh());
                                bonusPainfo.setFirstPerson(gjInfo.getFirstPerson());
                                bonusPainfo.setFirstPersonName(gjInfo.getFirstPersonName());
                            }
                            break;
                        default:
                            break;
                    }
                    Map<String, Object> map = new HashMap<>();
                    map = JSON.parseObject(JSON.toJSONString(bonusPainfo), Map.class);
                    map.put("operator", UserSession.getLoginName() +"-"+UserSession.getLoginCName());
                    defition.add(map);
                }
                data.setDefitions(defition);
            }
            inInfo.set("data", data);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String downId = (String) inInfo.get("downId");
            if (StrUtil.isNotBlank(downId)) {
                for (String id : downId.split(",")) {
                    businessBonusBaseinfo.delete(loginName, id);
                    this.businessBonusPainfo.deleteByC(loginName, downId);
                }
            }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;

    }

    public EiInfo queryDetailsRule(EiInfo inInfo) {
        try {
            TkwzlBonusBaseinfoEx bean = (TkwzlBonusBaseinfoEx) inInfo.get("query");
            if (ObjectUtil.isEmpty(bean)) {
                throw new BusinessException("没有查询到可发奖的项目");
            }
            FJTypeEnum fjType = FJTypeEnum.getFJType(bean.getBonusType());
            List<Map<String, Object>> queryList = KwzlUtil.queryDataIled(fjType.getDictCode(), bean.getStartDate(), bean.getEndDate());
            if (ObjectUtil.isEmpty(queryList)) {
                throw new BusinessException("没有查询到可发奖的项目");
            }
            BigDecimal amount = KwzlFJUtil.getAmountBasedOnType(fjType.getDictCode());
            if (amount == null || amount == BigDecimal.ZERO) {
                throw new BusinessException("该类型发奖规则未配置");
            }
            List<Map<String, Object>> dataList = new ArrayList<>();
            for (Map<String, Object> resMap : queryList) {
                Map<String, Object> painfoMap = new HashMap<>();
                switch (fjType) {
                    case SL:
                        painfoMap.put("jwzlId", MapUtil.getStr(resMap, "jwsqId"));
                        break;
                    case SQ:
                        painfoMap.put("jwzlId", MapUtil.getStr(resMap, "jwzlId"));
                        break;
                    default:
                        break;
                }
                resMap.put("sqgjFinal",KwzlUtil.getCountryName(MapUtil.getStr(resMap,"sqgjFinal")));
                painfoMap.put("bonusType", fjType.getDictCode());
                List<TkwzlBonusPainfo> tkwzlBonusPainfos = this.businessBonusPainfo.queryList(painfoMap);
                if (ObjectUtil.isEmpty(tkwzlBonusPainfos)) {
                    FlowStatusTypeEnum flowStatus = FlowStatusTypeEnum.getFlowStatusType(MapUtil.getStr(resMap, "flowStatus"));
                    if (null != flowStatus) {
                        resMap.put("status",flowStatus.getDictName() );
                    }
                    resMap.put("operator", UserSession.getLoginName() + "-" + UserSession.getLoginCName());
                    resMap.put("totalJe", amount);
                    resMap.put("isWhfmr","1");
                    dataList.add(resMap);
                }
            }
            if (ObjectUtil.isEmpty(dataList)) {
                throw new BusinessException("没有查询到可发奖的项目");
            }
            bean.setDefitions(dataList);
            int sl = dataList.size();
            BigDecimal total = NumberUtil.mul(amount.toString(), sl + "");
            bean.setBeginDate(DateUtil.today());
            bean.setSl(Long.valueOf(sl));
            bean.setTotal(total);
            bean.setQdUserName(UserSession.getLoginCName());
            bean.setQdUserCode(UserSession.getLoginName());
            inInfo.set("data", bean);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 流程启动
     *
     * @param inInfo
     * @return
     */
    public EiInfo startWF(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TkwzlBonusBaseinfoEx beanEx = (TkwzlBonusBaseinfoEx) inInfo.get("beanEx");
            EiInfo info = this.businessBonusBaseinfo.doSave(operator, beanEx);
            String downId = (String) info.getBlock("i").getRow(0).get("downId");
            beanEx.setDownId(downId);
            //启动
            String msg = this.businessBonusBaseinfo.startWF(beanEx);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 流程提交
     *
     * @param inInfo
     * @return
     */
    public EiInfo submitWF(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TkwzlApplyBaseinfoEx beanEx = (TkwzlApplyBaseinfoEx) inInfo.get("beanEx");
            WorkFlow mainFlowInfoByBusinessId = SWorkFlowUtil.getMainFlowInfoByBusinessId(beanEx.getBusinessGuid());
            Map<String, Object> variable = mainFlowInfoByBusinessId.getVariable();
            String msg = "";
            if (FJTypeEnum.SL.getDictCode().equals(MapUtil.getStr(variable, "fjType"))) {
                msg = this.businessBonusBaseinfo.submitWFBySl(operator, beanEx);
            } else {
                msg = this.businessBonusBaseinfo.submitWFBySq(operator, beanEx);
            }
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo zcSave(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TkwzlBonusBaseinfoEx beanEx = (TkwzlBonusBaseinfoEx) inInfo.get("beanEx");
            EiInfo retInfo = this.businessBonusBaseinfo.doSave(operator, beanEx);
            inInfo.setMsg(retInfo.getMsg());
            inInfo.setCell("i", 0, "downId", retInfo.getBlock("i").getRow(0).get("downId"));
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /***
     * 境外专利发奖异步通知
     * @param inInfo
     * @return
     * S_KW_ZL_01
     */
    @Transactional
    public EiInfo fjASync(EiInfo inInfo) {
        try {
            //当前登录用户
            String operator = (String) inInfo.get("operator");
            String ywlxId = (String) inInfo.get("ywlxId");
            TkwzlBonusPainfo bean = this.businessBonusPainfo.query(ywlxId);
            if (ObjectUtil.isEmpty(bean)) {
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
                inInfo.setMsg("未找到该发奖信息");
            }
            bean.setStatus(FlowStatusTypeEnum.END.getDictCode());
            this.businessBonusPainfo.update(operator, bean);
            try {
                //更新下发主表状态
                Map hashMap = new HashMap();
                hashMap.put("downId",bean.getDownId());
                List<TkwzlBonusPainfo> list = this.businessBonusPainfo.queryList(hashMap);
                boolean bool = list.stream().anyMatch(info -> FlowStatusTypeEnum.END.getDictCode().equals(info.getStatus()));
                if (bool) {
                    TkwzlBonusBaseinfo baseinfo = new TkwzlBonusBaseinfo();
                    baseinfo.setStatus(FlowStatusTypeEnum.END.getDictCode());
                    baseinfo.setDownId(bean.getDownId());
                    this.businessBonusBaseinfo.update(operator,baseinfo);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            Object bonusPoolList = inInfo.get("bonusPoolList");
            if (ObjectUtil.isNotEmpty(bonusPoolList)) {
                List<Map<String, Object>> listMapReviewInfos = (List<Map<String, Object>>) bonusPoolList;
                for (Map<String, Object> map : listMapReviewInfos) {
                    TkwzlBonusPerson bonusPerson = new TkwzlBonusPerson();
                    bonusPerson.setDownId(bean.getDownId());
                    bonusPerson.setFjzlId(bean.getDownzlId());
                    bonusPerson.setZlfpId("");
                    bonusPerson.setEmpId(MapUtil.getStr(map, "empId"));
                    bonusPerson.setEmpName(MapUtil.getStr(map, "empName"));
                    bonusPerson.setDeptCode(MapUtil.getStr(map, "deptCode"));
                    bonusPerson.setIsLeavejob(MapUtil.getStr(map, "personHrMove"));
                    bonusPerson.setPostLevel(MapUtil.getStr(map, "personHrLevel"));
                    bonusPerson.setCardNo(MapUtil.getStr(map, "personHrCard"));
                    bonusPerson.setGxxs(new BigDecimal(MapUtil.getStr(map, "personXs")));
                    bonusPerson.setJe(new BigDecimal(MapUtil.getStr(map, "personAward")));
                    bonusPerson.setQrr(MapUtil.getStr(map, "personSpr"));
                    bonusPerson.setQrrName(UserUtil.getUserName(MapUtil.getStr(map, "personSpr")));
                    if ("03".equals(MapUtil.getStr(map, "personFjflag"))) {
                        //已发奖
                        bonusPerson.setQrrResult("1");
                    } else if ("99".equals(MapUtil.getStr(map, "personFjflag"))) {
                        //不发奖
                        bonusPerson.setQrrResult("0");
                    }
                    this.businessBonusPerson.insert(operator,bonusPerson);
                }
            }
            inInfo.setMsg("SUCCESS");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


}
