package com.baosight.bscdkj.kw.ht.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baosight.bscdkj.kw.ht.business.BusinessRecommonBaseinfo;
import com.baosight.bscdkj.kw.ht.util.zypt.ZyptApi;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.kw.domain.TkwhtRecommonBaseinfo;
import com.baosight.bscdkj.common.kw.domain.TkwhtRecommonBaseinfoEx;
import com.baosight.bscdkj.common.service.PageService;
import com.baosight.bscdkj.kw.zl.enums.FlowStatusTypeEnum;
import com.baosight.bscdkj.mp.ad.dto.ADOrg;
import com.baosight.bscdkj.mp.ad.utils.OrgUtil;
import com.baosight.bscdkj.mp.ad.utils.RoleUtil;
import com.baosight.bscdkj.mp.ty.utils.ClobUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 对外合作_机构推荐_基本信息Service接口
 * 
 * <AUTHOR>
 * @date 2022-02-23
 */
@Service
public class ServiceKWHTRecommonBaseinfo extends PageService{
    @Autowired
	private BusinessRecommonBaseinfo businessRecommonBaseinfo;

    @Autowired
    private ZyptApi zyptApi;

	Logger logger = LoggerFactory.getLogger(this.getClass());
	/**
	 * 初始化
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
		return inInfo;
	}
	@Override
	public EiInfo query(EiInfo inInfo) {
	    try{
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkwhtRecommonBaseinfo> queryList = businessRecommonBaseinfo.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	public EiInfo page(EiInfo inInfo) {
	    try{
            Map<String, Object> queryData = getQueryData(inInfo);
            //综合查询权限控制
            String loginName = UserSession.getLoginName();
            boolean isAdmin = RoleUtil.isAdmin(loginName);
            boolean roleMember = RoleUtil.isRoleMember("KWHT_ALL_READER", loginName);
            if (isAdmin || roleMember) {

            } else {
                //全路径浏览权限
                List<ADOrg> adOrgs = RoleUtil.getOrgByUser(loginName, "KWHT_ZGBM_READER");
                if (ObjectUtil.isNotEmpty(adOrgs)) {
                    StringBuffer dynSql = new StringBuffer("(");
                    for (ADOrg adOrg : adOrgs) {
                        if (dynSql.length() > 1) {
                            dynSql.append(" OR ");
                        }
                        dynSql.append(" apply_dept_code_path like '%" + adOrg.getOrgCode() + "%'");
                    }
                    dynSql.append(")");
                    queryData.put("dynSql", dynSql.toString());
                } else {
                    queryData.put("createUserLabel", loginName);
                }
            }
            TableDataInfo queryPage = businessRecommonBaseinfo.queryPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
    @Override
	public EiInfo update(EiInfo inInfo) {
	    try{
            Map row = inInfo.getBlock("i").getRow(0);
            TkwhtRecommonBaseinfo bean = BeanUtil.toBean(row, TkwhtRecommonBaseinfo.class);
            businessRecommonBaseinfo.update(UserSession.getLoginName(),bean);
            inInfo.setMsg("修改成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
    @Override
	public EiInfo insert(EiInfo inInfo) {
	    try{
            Map row = inInfo.getBlock("i").getRow(0);
            TkwhtRecommonBaseinfo bean = BeanUtil.toBean(row, TkwhtRecommonBaseinfo.class);
            businessRecommonBaseinfo.insert(UserSession.getLoginName(),bean);
            inInfo.setMsg("添加成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo load(EiInfo inInfo) {
	    try{
            String recommonId = (String) inInfo.get("recommonId");
            TkwhtRecommonBaseinfo query = businessRecommonBaseinfo.query(recommonId);
            TkwhtRecommonBaseinfoEx beanEx = new TkwhtRecommonBaseinfoEx();
            BeanUtil.copyProperties(query,beanEx);
            Map<String, Object> contentMap = ClobUtil.getContentMap(beanEx.getRecommonId());
            if (ObjectUtil.isNotEmpty(contentMap)) {
                beanEx.setTechnicalAdvant(MapUtil.getStr(contentMap,"technicalAdvant"));
            }
            inInfo.set("data", beanEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo remove(EiInfo inInfo) {
        try{
            String loginName=UserSession.getLoginName();
            String recommonId = (String) inInfo.get("recommonId");
	        if (StrUtil.isNotBlank(recommonId)) {
	            for (String id : recommonId.split(",")) {
	                businessRecommonBaseinfo.delete(loginName,id);
	            }
	        }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
       }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;

	}

    public EiInfo doSave(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TkwhtRecommonBaseinfoEx beanEx = (TkwhtRecommonBaseinfoEx) inInfo.get("beanEx");
            EiInfo retInfo = this.businessRecommonBaseinfo.doSave(operator, beanEx);
            inInfo.setMsg(retInfo.getMsg());
            inInfo.setCell("i", 0, "recommonId", retInfo.getBlock("i").getRow(0).get("recommonId"));
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /***
     * 提交待办
     */
    @Transactional
    public EiInfo submitWF(EiInfo inInfo) {
        try {
            //当前登录用户
            String operator = UserSession.getLoginName();
            TkwhtRecommonBaseinfoEx beanEx = (TkwhtRecommonBaseinfoEx) inInfo.get("beanEx");
            String msg = this.businessRecommonBaseinfo.submitWF(operator, beanEx);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /***
     * 退回流程
     */
    @Transactional
    public EiInfo returnWF(EiInfo inInfo) {
        try {
            //当前登录用户
            String operator = UserSession.getLoginName();
            TkwhtRecommonBaseinfoEx beanEx = (TkwhtRecommonBaseinfoEx) inInfo.get("beanEx");
            String msg = this.businessRecommonBaseinfo.returnWF(operator, beanEx);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    public EiInfo queryRecommon(EiInfo inInfo) {
        try{
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo queryPage = businessRecommonBaseinfo.queryPage(queryData);
            if (CollUtil.isEmpty(queryPage.getRows())) {
                queryPage.setRows(new ArrayList<>());
            } else {
                List<TkwhtRecommonBaseinfo> rows = (List<TkwhtRecommonBaseinfo>) queryPage.getRows();
                rows.forEach(info -> {
                    info.setUserCode(info.getRecommonId());
                    info.setUserName(info.getChnFullname());
                });
            }
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo getZy(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkwhtRecommonBaseinfo> rows = this.businessRecommonBaseinfo.queryList(queryData);
            for (TkwhtRecommonBaseinfo info : rows) {
                if (StrUtil.isBlank(info.getCustomCode())) {
                    continue;
                }
                JSONObject jsonCompany = zyptApi.queryCompany(info.getChnFullname(),info.getCustomCode());
                System.out.println("众研平台查询u代码返回信息：" + jsonCompany);
                //status	状态码	00-正常；01-该企业未在BSP注册；03-验签失败
                //message	错误消息
                //openCA	状态码	0-有效 1-未申请 2-已过期
                //name	负责人姓名
                //email	邮箱
                //mobile	联系电话
                //uCode	U代码
                String status = (String) jsonCompany.get("status");
                if ("01".equals(status)) {
                    continue;
                }
                info.setBspCompanyCode((String) jsonCompany.get("uCode"));
                this.businessRecommonBaseinfo.update("9WT539",info);
            }
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    public EiInfo getKs(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkwhtRecommonBaseinfo> rows = this.businessRecommonBaseinfo.queryList(queryData);
            for (TkwhtRecommonBaseinfo info : rows) {
                if (StrUtil.isBlank(info.getCustomCode())) {
                    continue;
                }
                EiInfo inInfo2 = new EiInfo();
                inInfo2.set(EiConstant.serviceId, "S_MP_KS_01");
                inInfo2.set("operator", "9WT539");//操作人
                inInfo2.set("clientName",info.getChnFullname());//客商名称
                inInfo2.set("sysCode", "8P");//系统别
                EiInfo outInfo = XServiceManager.call(inInfo2);
                System.out.println(outInfo.getMsg());
                if(EiConstant.STATUS_SUCCESS != outInfo.getStatus()) {
                    continue;
                }
                Map<String, String> clientDTO = (Map<String, String>) outInfo.get("clientDTO");
                /* 常用的字段如下,其他参考 bscdkj-ggmk 的类 com.baosight.bscdkj.mp.ks.dto.ClientDTO*/
                String userNum = (String) clientDTO.get("userNum");//用户/供应商代码
                String countryNameAbbr = (String) clientDTO.get("countryNameAbbr");//国别
                String chineseUserName = (String) clientDTO.get("chineseUserName");//中文全称
                String chineseUsnmAbbr = (String) clientDTO.get("chineseUsnmAbbr");//中文简称
                String englishUserName = (String) clientDTO.get("englishUserName");//
                String englishUsnmAbbr = (String) clientDTO.get("englishUsnmAbbr");//
                String taxNum = (String) clientDTO.get("taxNum");//税号
                String businNum = (String) clientDTO.get("businNum");//工商登记号

                String corpFlag = (String) clientDTO.get("corpFlag");//是否法人标志
                String corpCode = (String) clientDTO.get("corpCode");//法人代码
                String orgCode = (String) clientDTO.get("orgCode");//宝钢组织机构码
                String addrRegst = (String) clientDTO.get("addrRegst");//注册地省份
                String chineseAddress = (String) clientDTO.get("chineseAddress");//注册地址
                String accountNum = (String) clientDTO.get("accountNum"); //帐号
                String accountName = (String) clientDTO.get("accountName"); //帐号名称
                String bankId = (String) clientDTO.get("bankId");//银行代码
                String bankBranchName = (String) clientDTO.get("bankBranchName");//银行名称
                String appliceId = (String) clientDTO.get("appliceId");//联系人工号
                String appliceName = (String) clientDTO.get("appliceName");//联系人姓名
                String appliceTel = (String) clientDTO.get("appliceTel");//联系电话
                String email = (String) clientDTO.get("email");//联系电子邮件
                info.setGldwName(OrgUtil.getOrgName(info.getGldwCode()));
                info.setChnName(chineseUsnmAbbr);
                info.setEngFullname(englishUserName);
                info.setEngName(englishUsnmAbbr);
                info.setCountry(countryNameAbbr);
                info.setProvince(addrRegst);
                info.setShuiCode(taxNum);
                info.setCreditCode(businNum);
                info.setEntrustLegal(corpCode);
                info.setDeptCode(orgCode);
                info.setAddress(chineseAddress);
                info.setAccount(accountNum);
                info.setOpenBank(bankBranchName);
                info.setContactPerson(appliceName);
                info.setContactPhone(appliceTel);
                this.businessRecommonBaseinfo.update(UserSession.getLoginName(),info);
            }
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 定时任务更新客商代码微服务
     * S_KW_HT_02
     *
     * @param inInfo
     * @return
     */
    public EiInfo updateCustomCode(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            Map<String, Object> param = new HashMap<>();
            param.put("delStatus","0");
            param.put("flowStatus",FlowStatusTypeEnum.END.getDictCode());
            param.put("finIsrk","0");
            param.put("dynSql", "(custom_code is null or custom_code = '')"); //客商代码为null 或空字符串
            String msg = "";
            List<TkwhtRecommonBaseinfo> list = this.businessRecommonBaseinfo.queryList(param);
            int num = 0;
            for (TkwhtRecommonBaseinfo info : list) {
                EiInfo xInfo = new EiInfo();
                xInfo.set(EiConstant.serviceId, "S_MP_KS_01");
                xInfo.set("operator", operator);//操作人
                xInfo.set("clientName", info.getChnFullname());//客商名称
                xInfo.set("sysCode", "8P");//系统别
                EiInfo outInfo = XServiceManager.call(xInfo);
                Map<String, String> clientDTO = (Map<String, String>) outInfo.get("clientDTO");
                if (clientDTO != null) {
                    String userNum = (String) clientDTO.get("userNum");//用户/供应商代码
                    if (StrUtil.isNotBlank(userNum)) {
                        info.setCustomCode(userNum);
                        this.businessRecommonBaseinfo.update(operator, info);
                        num++;
                        msg += info.getBspCompanyCode() + ",";
                    }
                }
            }
            inInfo.setMsg(num + ":" + msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }
}
