package com.baosight.bscdkj.zz.kj.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.io.IoUtil;
import com.baosight.bscdkj.common.constant.ZZZCConstants;
import com.baosight.bscdkj.common.controller.BaseController;
import com.baosight.bscdkj.common.domain.AjaxResult;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.mp.ad.utils.RoleUtil;
import com.baosight.bscdkj.mp.ty.utils.SDictUtil;
import com.baosight.bscdkj.utils.Docx4jUtils;
import com.baosight.bscdkj.utils.excel.ExcelUtil;
import com.baosight.bscdkj.utils.iplat.ServiceUtil;
import com.baosight.bscdkj.zz.kj.domain.TzzkjFrameEx;
import com.baosight.bscdkj.zz.kj.domain.planNeedExcel;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.docx4j.convert.in.xhtml.XHTMLImporterImpl;
import org.docx4j.openpackaging.packages.WordprocessingMLPackage;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Controller;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 框架协议Controller
 *
 * <AUTHOR>
 * @date 2021-12-06
 */
@Controller
@RequestMapping("/zzkj/frame")
public class ControllerFrame extends BaseController {
    private final String prefix = "/zzkj/frame";

    @GetMapping()
    public String frame() {
        return prefix + "/frame";
    }

    /**
     * 询框架协议列表 list
     * 我管理的询框架协议  manage
     * 我相关的询框架协议  related
     *
     * @return
     */
    @GetMapping("/{queryType}")
    public String query(@PathVariable("queryType") String queryType, ModelMap mmap) {
        mmap.put("queryType", queryType);
        mmap.put("constants", new ZZZCConstants());
        return prefix + "/frame";
    }


    /**
     * 查询框架协议列表·
     */
    @PostMapping("/list")
    @ResponseBody
    public TableDataInfo list(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceZZKJFrame", "page");
        return getDataTable(query);
    }

    /**
     * 查询年度计划列表·
     */
    @PostMapping("/planNeedPage")
    @ResponseBody
    public TableDataInfo planNeedPage(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceZZKJFrame", "planNeedPage");
        return getDataTable(query);
    }

    /**
     * 查询年度计划列表·
     */
    @PostMapping("/planMainPage")
    @ResponseBody
    public TableDataInfo planMainPage(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceZZKJFrame", "planMainPage");
        return getDataTable(query);
    }

    /**
     * 新增框架协议
     */
    @GetMapping("/add")
    public String add(ModelMap mmap, String frameId) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("frameId", frameId);
        TzzkjFrameEx frameEx = (TzzkjFrameEx) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceZZKJFrame", "addEditLoad", "frameEx");
        mmap.put("frameEx", frameEx);
        mmap.put("taskId", frameEx.getTaskId());
        return prefix + "/add";
    }


    /**
     * 新增保存框架协议
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated TzzkjFrameEx frame) {


        EiInfo eiInfo = getEiInfo("i", frame);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceZZKJFrame", "insert");
    }

    /**
     * 修改框架协议
     */
    @GetMapping("/edit/{frameId}")
    public String edit(@PathVariable("frameId") String frameId, ModelMap mmap) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("frameId", frameId);

        mmap.put("frameEx", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceZZKJFrame", "addEditLoad", "frameEx"));
        return prefix + "/edit";
    }


    /**
     * 修改框架协议
     */
    @GetMapping("/detail/{frameId}")
    public String detail(@PathVariable("frameId") String frameId, ModelMap mmap) {
        String loginName = UserSession.getLoginName();
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("frameId", frameId);
        mmap.put("isZG", RoleUtil.isRoleMember("KTTG_XMZG",loginName) || RoleUtil.isRoleMember("KTTG_GF_XMZG",loginName));
        mmap.put("frameEx", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceZZKJFrame", "load", "frameEx"));
        return prefix + "/detail";
    }

    /**
     * 修改保存框架协议
     */
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated TzzkjFrameEx frame) {
        EiInfo eiInfo = getEiInfo("i", frame);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceZZKJFrame", "update");
    }

    /**
     * 删除框架协议
     */
    @PostMapping("/remove")
    @ResponseBody
    public AjaxResult remove(String ids) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("frameId", ids);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceZZKJFrame", "remove");
    }


    /**
     * 导出框架协议列表
     */
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export() {
        EiInfo eiInfo = new EiInfo();
        List<TzzkjFrameEx> list = (List<TzzkjFrameEx>) ServiceUtil.xLocalManagerToData(eiInfo, "ServiceZZKJFrame", "query", "list");
        ExcelUtil<TzzkjFrameEx> util = new ExcelUtil<>(TzzkjFrameEx.class);
        util.setSheet("框架协议");
        return util.exportExcel(list);
    }

    /**
     * 暂存_基本信息
     */
    @PostMapping("/saveHandler")
    @ResponseBody
    public AjaxResult saveHandler(TzzkjFrameEx frameEx) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("frameEx", frameEx);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceZZKJFrame", "saveHandler");
    }

    /**
     * 提交流程
     *
     * @param
     * @return
     */
    @PostMapping("/submitProcess")
    @ResponseBody
    @Transactional
    public AjaxResult submitProcess(TzzkjFrameEx frameEx) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("frameEx", frameEx);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceZZKJFrame", "submitProcess");
    }


    /**
     * 查看费用实际
     * @param frameNum 框架协议号
     * @param mmap
     * @return
     */
    @GetMapping("/costBudget/{frameNum}")
    public String costBudget(@PathVariable("frameNum") String frameNum, ModelMap mmap) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("projectNum", frameNum);
        mmap.put("frameEx", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceZZKJFrame", "queryByFrameNum", "result"));
        return prefix + "/costBudget";
    }
    /**
     * 查看协议项目清单
     * @param frameNum 框架协议号
     * @param mmap
     * @return
     */
    @GetMapping("/projectList/{frameNum}")
    public String projectList(@PathVariable("frameNum") String frameNum, ModelMap mmap) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("projectNum", frameNum);
        mmap.put("frameEx", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceZZKJFrame", "queryByFrameNum", "result"));
        return prefix + "/projectList";
    }


    /**
     * 查询项目费用
     * @param map
     * @return
     */
    @PostMapping("/costBudget")
    @ResponseBody
    public TableDataInfo costBudget(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceZZKJFrame", "costBudgetPage");
        return getDataTable(query);
    }

    /**
     * 查询联络单费用
     * @param map
     * @return
     */
    @PostMapping("/costContact")
    @ResponseBody
    public TableDataInfo costContact(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceZZKJFrame", "costContactPage");
        return getDataTable(query);
    }

    /**
     * 询框架协议列表 list
     * 我管理的询框架协议  manage
     * 我相关的询框架协议  related
     *
     * @return
     */

    @GetMapping("/costBudgetStatistics")
    public String costBudgetStatistics() {
        return prefix + "/costBudgetStatistics";
    }

    /**
     * 查询联络单费用
     * @param map
     * @return
     */
    @PostMapping("/costBudgetStatistics")
    @ResponseBody
    public TableDataInfo costBudgetStatistics(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceZZKJFrame", "costBudgetStatistics");
        return getDataTable(query);
    }

    /**
     * 导出年度计划列表
     */
    @PostMapping("/exportPlanNeed/{frameId}")
    @ResponseBody
    public AjaxResult exportPlanNeed(@PathVariable("frameId")String frameId) {
        Map map = new HashMap();
        map.put("orderByColumn"," ");
        map.put("isAsc"," ");
        map.put("frameId",frameId);
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceZZKJFrame", "planNeedPage");
        TableDataInfo tableDataInfo = getDataTable(query);
        List<planNeedExcel> infoExcel1sList = new ArrayList<>();
        EiInfo eiInfo1 = new EiInfo();
        if (tableDataInfo.getRows().size() > 0){
            List<planNeedExcel> infoExcel1List = (List<planNeedExcel>) tableDataInfo.getRows();
            for (int i = 0; i < infoExcel1List.size(); i++) {
                planNeedExcel infoExcel1 = BeanUtil.toBean(infoExcel1List.get(i), planNeedExcel.class);
                infoExcel1.setXh((i+1) + "");
                infoExcel1sList.add(infoExcel1);
                String dictName = SDictUtil.getDictName("KTTG", "projectType", infoExcel1.getProjectType());
                infoExcel1.setProjectType(dictName);
            }
        }
        ExcelUtil<planNeedExcel> util = new ExcelUtil<>(planNeedExcel.class);
        util.setSheet("年度计划");
        return util.exportExcel(infoExcel1sList);
    }

    @GetMapping("/exportFrame/{frameId}")
    public void exportContract(@PathVariable("frameId") String frameId,ModelMap map) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("frameId", frameId);
        eiInfo.set("manual", "Manual4");
        eiInfo = ServiceUtil.xLocalManager(eiInfo, "ServiceZZKJFrame", "loadExportAll");
        map.putAll(eiInfo.getAttr());
//       return "zzlx/FX/contractWord";

        //湛江经济技术开发区东简街道办岛东大道18号
        //上海市宝山区漠河路151号1816室 宝钢国际
        //南京市雨花台区中华门外新建 梅山
        //上海市富锦路885号宝钢指挥中心 宝钢总部
        //武汉市青山区股份公司机关 武汉钢铁
        //黄石市黄石新港（物流）工业园区海洲大道18号 黄石涂镀板
        renderAndFlushWordCon("zzkj/frame/frameWord", eiInfo.getAttr(), (String) eiInfo.get("fileName"));
    }

    public void renderAndFlushWordCon(String tmplPath, Map<String, Object> variables, String fileName) {
        String html = render(tmplPath, variables);
        html = html.replace("&nbsp;", "\u00A0");
        OutputStream outputStream = null;
        try {
            response.setContentType("application/docx");
            response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(fileName, "UTF-8")+".docx");
            outputStream = response.getOutputStream();
            // 创建 docx
            WordprocessingMLPackage wordMLPackage = WordprocessingMLPackage.createPackage();
            // 设置中文字体
            Docx4jUtils.configFont(wordMLPackage);
            // 解析 html，放入空的 docx 对象中
            XHTMLImporterImpl xhtmlImporter = new XHTMLImporterImpl(wordMLPackage);
            xhtmlImporter.setHyperlinkStyle("Hyperlink");
            List<Object> list = xhtmlImporter.convert(html, new ClassPathResource("/").getURL().toExternalForm());
            wordMLPackage.getMainDocumentPart().getContent().addAll(list);

            wordMLPackage.save(outputStream);
            outputStream.flush();
            outputStream.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            IoUtil.close(outputStream);
        }
    }
}
