package com.baosight.bscdkj.zz.lx.business;

import cn.hutool.core.bean.BeanUtil;
import com.baosight.bscdkj.zz.lx.domain.TzzlxNeedEx;
import com.baosight.bscdkj.common.business.BusinessBase;
import com.baosight.bscdkj.common.constant.ZZZCConstants;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.zz.domain.TzzlxMain;
import com.baosight.bscdkj.mp.ad.dto.ADOrg;
import com.baosight.bscdkj.mp.ad.utils.OrgUtil;
import com.baosight.bscdkj.mp.ad.utils.RoleUtil;
import com.baosight.bscdkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bscdkj.utils.BizIdUtil;
import com.baosight.bscdkj.zz.lx.domain.TzzlxMainEx;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 推广移植主
 *
 * <AUTHOR>
 * @date 2021-11-16
 */
@Component
public class BusinessMain extends BusinessBase {


    /**
     * 查询
     *
     * @return
     */
    public TzzlxMainEx load(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("mainId", id);
        List query = dao.query("tzzlxMain.load", hashMap);
        return query != null && !query.isEmpty() ? (TzzlxMainEx) query.get(0) : null;
    }

    /**
     * 查询List
     *
     * @param param
     * @return
     */
    public List<TzzlxMain> queryList(Map<String, Object> param) {
        return dao.query("tzzlxMain.query", param);
    }

    /**
     * 查询List,供先进科技使用接口
     *
     * @param userCode
     * @param year
     * @return
     */
    public List<TzzlxMain> queryForKjxj(String userCode,String year) {
        Map<String, Object> param = new HashMap<>();
        param.put("userCode",userCode);
        param.put("year",year);
        String [] projectStatusLi = {ZZZCConstants.STATUS_JT,ZZZCConstants.STATUS_WC};
        param.put("projectStatusLi", projectStatusLi);//项目结题中，已完成
        return dao.query("tzzlxMain.queryForKjxj", param);
    }


    /**
     * 查询
     *
     * @return
     */
    public TzzlxMain query(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("mainId", id);
        List query = dao.query("tzzlxMain.query", hashMap);
        return query != null && !query.isEmpty() ? (TzzlxMain) query.get(0) : null;
    }

    public TzzlxMain queryMainByCode(String projectNum) {
        if (!StringUtils.isNotEmpty(projectNum)) {
            return null;
        }
        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("projectNum", projectNum);
        hashMap.put("displayOrder", " create_date desc ");
        List<TzzlxMain> list = dao.query("tzzlxMain.query", hashMap);
        return (list != null && list.size() > 0) ? list.get(0) : null;
    }

    public TzzlxNeedEx addEditLoad(String id) {
        if (StringUtils.isBlank(id)) {
            return null;
        }

        Map<String, Object> hashMap = new HashMap<>();
        hashMap.put("mainId", id);
        List query = dao.query("tzzlxNeed.queryEx", hashMap);
        return query != null && !query.isEmpty() ? (TzzlxNeedEx) query.get(0) : null;
    }

    public TableDataInfo queryZZReport(Map<String, Object> param) {
        param.put("pageSize",1);
        return getPage("tzzlxMain.queryZZReport",param);
    }

    /**
     * 查询跟踪 <br/>
     * 我提出的项目 myProposed <br/>
     * 我负责的项目 myResponsible <br/>
     * 我管理的项目 myManage <br/>
     * 我相关的项目 myRelated <br/>
     * 我处理过的项目 myProcessed <br/>
     * 综合查询 comprehensive <br/>
     *
     * @param param
     * @return
     */
    public TableDataInfo queryMyPage(Map<String, Object> param) {
        String loginName = UserSession.getLoginName();// 当前登陆人
        String queryType = (String) param.get("queryType");// 查询类型
        param.put("delStatus", 0);
        if ("myProposed".equals(queryType)) {// 我提出的项目
            param.put("createUserLabel", loginName);

        } else if ("myResponsible".equals(queryType)) {// 我负责的项目
            param.put("fzrCode", loginName);

        } else if ("myManage".equals(queryType)) {// 我管理的项目
            param.put("loginName", loginName);

        } else if ("myProcessed".equals(queryType) || "myRelated".equals(queryType) || "comprehensive".equals(queryType)) {// 我处理过的项目  我相关的项目
            param.put("loginName", loginName);
            if ("comprehensive".equals(queryType)) {// 综合查询、权限过滤
                if(!RoleUtil.isAdmin(UserSession.getLoginName()) && !RoleUtil.isRoleMember("KTTG_DW_DC", UserSession.getLoginName())) {//系统管理员查全部
                    List<ADOrg> adOrgs = RoleUtil.getOrgByUser(loginName,ZZZCConstants.ZZZC_DEPT_ADMIN);
                    if(adOrgs!=null&&adOrgs.size()>0){
                        StringBuffer dynSql1 = new StringBuffer("(");
                        if(adOrgs.size()>1){
                            for (ADOrg adOrg : adOrgs) {
                                if (dynSql1.length() > 1) {
                                    dynSql1.append(" OR ");
                                }
                                String str = "";
                                if(adOrg.getOrgCode().length()>=6) {
                                    str = adOrg.getOrgCode().substring(0, 6);
                                }else {
                                    str =  adOrg.getOrgCode();
                                }
                                dynSql1.append(" A.TGF_DEPT_PATH_CODE like '%" + str + "%' or A.SRF_DEPT_PATH_CODE like '%" + str + "%'");
                            }
                            dynSql1.append(")");
                            param.put("dynSql", dynSql1.toString());
                        }else {
                            ADOrg adOrg = adOrgs.get(0);
                            String str = "";
                            if(adOrg.getOrgCode().length()>=6) {
                                str = adOrg.getOrgCode().substring(0, 6);
                                param.put("deptPathCode",str);
                            }else {
                                param.put("deptPathCode", adOrg != null ? adOrg.getOrgCode() : "");
                            }
                        }
                    }else {
                        ADOrg adOrg = OrgUtil.getMainOrgByUserCode(loginName);
                        param.put("deptPathCode", adOrg != null ? adOrg.getOrgCode() : "");
                    }
                }else{
                    param.remove("loginName");
                    param.put("queryType","");
                }
            }
            return getPage("tzzlxMain.load", param);
        }
        return getPage("tzzlxMain.query", param);
    }

    /**
     * 导出主表信息
     * @param param
     * @return
     */
    public List<Map<String,Object>> queryExcelPage(Map<String, Object> param) {
        String loginName = UserSession.getLoginName();// 当前登陆人
        String queryType = (String) param.get("queryType");// 查询类型
        if(!RoleUtil.isAdmin(UserSession.getLoginName()) && !RoleUtil.isRoleMember("KTTG_DW_DC", UserSession.getLoginName())) {//系统管理员查全部
            ADOrg adOrg = OrgUtil.getMainOrgByUserCode(loginName);
            param.put("deptPathCode", adOrg != null ? adOrg.getOrgCode() : "");
        }else{
            param.remove("loginName");
            param.put("queryType","");
        }
        param.put("loginName",loginName);
        int count = dao.count("tzzlxMain.queryZHExcel", param);
        if(count>1000) {
            List<Map<String,Object>> rtn = new ArrayList<>();
            for (int i = 0; i < count/1000+1; i++) {
                rtn.addAll(dao.query("tzzlxMain.queryZHExcel", param, 1000*i, 1000));
            }
            return rtn;
        } else {
            return dao.query("tzzlxMain.queryZHExcel", param);
        }
    }

    /**
     * 查询List 分页
     *
     * @param param
     * @return
     */
    public TableDataInfo quertPage(Map<String, Object> param) {
        return getPage("tzzlxMain.query", param);
    }

    /**
     * 新增记录
     *
     * @param main
     * @return
     */
    public void insert(String operator, TzzlxMain main) {
        main.initAdd(operator);
        if (StringUtils.isBlank(main.getMainId())) {
            main.setMainId(BizIdUtil.INSTANCE.nextId());
        }
        dao.insert("tzzlxMain.insert", main);
    }

    /**
     * 修改
     *
     * @return
     */
    public int update(String operator, TzzlxMain main) {
        if (StringUtils.isBlank(main.getMainId())) {
            throw new PlatException("主键为空");
        }
//        main.initUpdate(operator);
        return dao.update("tzzlxMain.update", main);
    }

    /**
     * 查询中央研究院的项目
     * @param param
     * @return
     */
    public List<TzzlxMain> queryYfData(Map<String, Object> param) {
        return dao.query("tzzlxMain.queryYfData", param);
    }


    /**
     * 逻辑删除
     *
     * @return
     */
    public int logicDelete(String operator, String mainId) {
        if (StringUtils.isBlank(mainId)) {
            throw new PlatException("主键为空");
        }
        TzzlxMain main = new TzzlxMain();
        main.setMainId(mainId);
        main.initLogicDel(operator);
        return dao.delete("tzzlxMain.update", mainId);
    }

    /**
     * 删除
     *
     * @return
     */
    public int delete(String operator, String mainId) {
        if (StringUtils.isBlank(mainId)) {
            throw new PlatException("主键为空");
        }
        return dao.delete("tzzlxMain.delete", mainId);
    }

    /**
     * 中止项目
     * @param operator
     * @param beanEx
     * @return
     */
    public String closeProject(String operator,TzzlxMainEx beanEx) {
        String businessGuid = beanEx.getMainId();
        TzzlxMain main = query(businessGuid);
        if(BeanUtil.isNotEmpty(main)) {
            main.setProjectStatus(ZZZCConstants.STATUS_ZZ);    //终止
            main.setStatus(ZZZCConstants.PROCESS_STOP);    //终止 流程状态
            main.setExtra1(beanEx.getEndComment()); //中止意见
            update(operator, main);
            //中止相关附件
            if (StringUtils.isNotEmpty(beanEx.getEndFjId())) {
                SAttachmentUtil.addAttachmentMapsBySourceGuid(beanEx.getMainId(), beanEx.getEndFjId(), "END_XGFJ");
            }
            WorkFlow workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(businessGuid);
            if (BeanUtil.isNotEmpty(workFlow)) {
                //结束流程
                SWorkFlowUtil.endProecess(operator, workFlow);
            }
        }
        return "项目已中止！";
    }
}
