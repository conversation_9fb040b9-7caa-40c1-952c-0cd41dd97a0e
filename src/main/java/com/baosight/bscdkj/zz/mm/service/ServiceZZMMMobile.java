package com.baosight.bscdkj.zz.mm.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.dubbo.common.utils.StringUtils;
import com.baosight.bscdkj.common.ky.domain.TkymmTechMember;
import com.baosight.bscdkj.common.ky.domain.TkymmTechOwner;
import com.baosight.bscdkj.common.service.PageService;
import com.baosight.bscdkj.ky.mm.business.BusinessTechImpl;
import com.baosight.bscdkj.ky.mm.business.BusinessTechnology;
import com.baosight.bscdkj.ky.mm.common.MMConstans;
import com.baosight.bscdkj.ky.mm.domain.R;
import com.baosight.bscdkj.ky.mm.domain.TkymmTechImplEx;
import com.baosight.bscdkj.ky.mm.domain.TkymmTechnologyEx;
import com.baosight.bscdkj.ky.mm.factory.ImplFactory;
import com.baosight.bscdkj.ky.mm.factory.JMFactory;
import com.baosight.bscdkj.ky.mm.factory.RdFactory;
import com.baosight.bscdkj.ky.mm.mminterface.IMMIMPLProcess;
import com.baosight.bscdkj.ky.mm.mminterface.IMMJMProcess;
import com.baosight.bscdkj.ky.mm.mminterface.IMMRDProcess;
import com.baosight.bscdkj.mp.ty.dto.AttachmentMap;
import com.baosight.bscdkj.mp.ty.prop.AppContextProp;
import com.baosight.bscdkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bscdkj.mp.ty.utils.SDictUtil;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 技术秘密移动端接口
 * 
 * <AUTHOR>
 * @date 2022-11-18
 */
@Service
public class ServiceZZMMMobile extends PageService{

	Logger logger = LoggerFactory.getLogger(this.getClass());

	@Autowired
    BusinessTechnology businessTechnology;

	@Autowired
    BusinessTechImpl businessTechImpl;

    @Autowired
    AppContextProp appContextProp;

	/**
	 * 初始化
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
		return inInfo;
	}

    /****
     * 查询移动项目详细  S_ZZ_MM_07
     * @param inInfo
     * @return
     */
    public EiInfo queryMobileInfo(EiInfo inInfo) {
        try{
            String operator = inInfo.getString("operator");
            Map params = (Map)inInfo.get("params");
            if(null==params) {
                throw new PlatException("缺少参数params");
            }
            String businessId = (String)params.get("businessId");
            if(StringUtils.isBlank(businessId)) {
                throw new PlatException("缺少业务ID");
            }

            TkymmTechnologyEx technologyEx = null;
            TkymmTechImplEx techImplEx = null;

            String processCode = (String)params.get("processCode");
            if(StringUtils.isBlank(processCode)) {
                throw new PlatException("缺少流程code");
            }
            if(MMConstans.process_rd.equals(processCode)) {//认定奖申请流程
                technologyEx = businessTechnology.queryDetailed(businessId);

            } else if (MMConstans.KYMM_GXXS.equals(processCode)) {//贡献系数确认流程
                technologyEx = businessTechnology.queryDetailed(businessId);

            }else if(MMConstans.kymmreward.equals(processCode)){//实施奖申请流程
                techImplEx = businessTechImpl.queryDetailed(businessId);
                technologyEx = businessTechnology.queryDetailed(techImplEx.getTechnologyId());

            }else if(MMConstans.kymmsondeptps.equals(processCode)){//实施奖部门审批流程
                techImplEx = businessTechImpl.queryDetailed(businessId);
                technologyEx = businessTechnology.queryDetailed(techImplEx.getTechnologyId());

            } else if(MMConstans.process_jm.equals(processCode)){//解密流程



            } else {
                throw new PlatException(processCode+"暂不支持");
            }
            inInfo.set(EiConstant.resultBlock, getMobileContent(operator, processCode, technologyEx, techImplEx));
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     *
     * @param operator
     * @param processCode
     * @param technologyEx
     * @param techImplEx
     * @return
     */
    private List<Map<String, Object>> getMobileContent(String operator, String processCode, TkymmTechnologyEx technologyEx, TkymmTechImplEx techImplEx) {
        Map<String, Object> data = null;
        //需要展示的字段
        String[] show = null;
        if ("kymmapply".equals(processCode)) {
            //认定主表
            show = new String[]{"technologyName:技术秘密名称"
                    , "confirmNum:认定号"
                    , "confirmTime:认定时间"
                    , "secretLevel:密级"
                    , "applyDate:提出日期"
                    , "contactPersonGh:联系人工号"
                    , "contactPersonName:联系人姓名"
                    , "firstdeptName:第一申报部门名称"
            };
            data = BeanUtil.beanToMap(technologyEx);
        } else if (MMConstans.KYMM_GXXS.equals(processCode)) {
            show = new String[]{"technologyName:技术秘密名称"
                    , "confirmNum:认定号"
                    , "confirmTime:认定时间"
                    , "secretLevel:密级"
                    , "applyDate:提出日期"
                    , "contactPersonGh:联系人工号"
                    , "contactPersonName:联系人姓名"
                    , "firstdeptName:第一申报部门名称"
            };
            data = BeanUtil.beanToMap(technologyEx);
        }  else if ("kymmdecrypt".equals(processCode)) {
            show = new String[]{"technologyName:技术秘密名称"
                    , "confirmNum:认定号"
                    , "confirmTime:认定时间"
                    , "secretLevel:密级"
                    , "applyDate:提出日期"
                    , "contactPersonGh:联系人工号"
                    , "contactPersonName:联系人姓名"
                    , "firstdeptName:第一申报部门名称"
            };
            data = BeanUtil.beanToMap(techImplEx);

        } else if ("kymmreward".equals(processCode)) {
            show = new String[]{"technologyName:技术秘密名称"
                    , "confirmNum:认定号"
                    , "confirmTime:认定时间"
                    , "firstdeptName:第一申报部门"
                    , "applyUserName:申报人"
                    , "applyType:申报类型"
                    , "applyTimes:申报次数"
                    , "allowapplyTimes:允许申报次数"
            };
            data = BeanUtil.beanToMap(techImplEx);

        } else {
            throw new PlatException(processCode+"暂不支持");
        }

        List<Map<String, Object>> result = new ArrayList<>();
        //主表
        Map<String, Object> column = null;
        //序号
        int order = 0;
        for (int i = 0; i < show.length; i++) {
            order++;
            String[] _column = show[i].split(":");
            column = new HashMap<>();
            column.put("code", _column[0]);
            column.put("name", _column[1]);
            column.put("type", "text");
            column.put("order", order);
            switch (_column[0]) {
                case "secretLevel":
                    column.put("value", SDictUtil.getDictName(MMConstans.business_type, "secretLevel", (String) data.get(_column[0])));
                    break;
                case "sourceType":
                    column.put("value", SDictUtil.getDictName(MMConstans.business_type, "sourceType", (String) data.get(_column[0])));
                    break;
                case "applyType":
                    column.put("value", SDictUtil.getDictName(MMConstans.business_type, "applyType", (String) data.get(_column[0])));
                    break;
                case "technologyName":
                    column.put("value", technologyEx.getTechnologyName());
                    break;
                case "confirmNum":
                    column.put("value", technologyEx.getConfirmNum());
                    break;
                case "confirmTime":
                    column.put("value", technologyEx.getConfirmTime());
                    break;
                case "firstdeptName":
                    column.put("value", technologyEx.getFirstdeptName());
                    break;
                default:
                    column.put("value", data.get(_column[0]));
            }
            result.add(column);
        }

        //需要展示的附件
        String businessId = null;
        switch (processCode) {
            case MMConstans.process_rd:
                businessId = technologyEx.getTechnologyId();
                break;
            case MMConstans.KYMM_GXXS:
                businessId = technologyEx.getTechnologyId();
                break;
            case MMConstans.kymmreward:
                businessId = techImplEx.getTechImplId();
                break;
            default:
                businessId = techImplEx.getTechnologyId();
        }
        List<AttachmentMap> attachmentList = SAttachmentUtil.getAttachmentBySourceId(businessId, MMConstans.business_type);
        for (AttachmentMap attachmentMap : attachmentList) {
            order++;
            column = new HashMap<>();
            column.put("code", "");
            switch (attachmentMap.getSourceLabel1()) {
                case "SBFILE":
                    column.put("name", "认定相关附件");
                    break;
                case "Property_Rights":
                    column.put("name", "知识产权协议附件");
                    break;
                case "IMPLEMENTATION_SUMMARY":
                    column.put("name", "实施相关附件");
                    break;
                case "PROOF_IMPLEMENTATION":
                    column.put("name", "实施情况证明");
                    break;
                case "AFILE":
                    column.put("name", "需解密的技术秘密的补充描述");
                    break;
                default:
//                    column.put("name", "相关附件");
            }
//            column.put("name", "相关附件");//可以根据AttachmentMap中的字段具体指定名称
            column.put("type", "docUrl");//在线预览的附件类型 固定
            column.put("order", order);
            column.put("value", attachmentMap.getAttachmentName());//附件名称
            column.put("url", appContextProp.getCtxGGMK() + "attachment/nmDownload/" + attachmentMap.getAttachmentId() + "?isMobile=true&operator=" + operator);//附件链接，固定
            result.add(column);
        }

        return result;
    }

    /**
     * 获取移动审批信息_bak
     *
     * @param operator
     * @param technologyEx
     * @return
     */
    private List<Map<String, Object>> getMobileContent_bak(String operator, String processCode, TkymmTechnologyEx technologyEx, TkymmTechImplEx techImplEx) {
        Map<String, Object> data = BeanUtil.beanToMap(technologyEx);
        List<TkymmTechMember> dataMembers = technologyEx.getMembers();
        List<TkymmTechOwner> dataOwners = technologyEx.getOwners();
        //需要展示的字段
        //认定主表
        String[] show = {"technologyId:技术秘密业务主键"
                , "technologyName:技术秘密名称"
                , "confirmNum:认定号"
                , "confirmTime:认定时间"
                , "secretLevel:密级"
                , "applyDate:提出日期"
                , "contactPersonGh:联系人工号"
                , "contactPersonName:联系人姓名"
                , "contactpersonEmail:联系人Email"
                , "contactpersonPh:联系人手机"
                , "contactpersonTel:联系人电话"
                , "firstdeptCode:第一申报部门code"
                , "firstdeptName:第一申报部门名称"
                , "sourceType:来源类型"
                , "sourceNum:来源编号"
                , "sourceName:来源名称"
                , "ohterSource:其他来源情况说明"
                , "samesourcePatent:同来源专利"
                , "samesourceTech:同来源技术秘密"
                , "members:提出人信息"
                , "owners:权利人信息"
        };
        //提出人子表
        String[] memberShow = {"techMemberId:主键"
                , "technologyId:技术秘密主键"
                , "memberType:类型"
                , "memberGh:工号"
                , "memberName:姓名"
                , "deptCode:人员组织代码"
                , "deptName:人员组织名称"
                , "postTitel:职称"
                , "postion:岗位"
                , "contribution:贡献系数"
                , "mark:备注"
        };
        //权利人子表
        String[] ownerShow = {"techOwnerId:技术秘密名称"
                , "technologyId:技术秘密主键"
                , "techOwnerCode:权利人单位代码"
                , "techOwnerName:权利人单位"
        };
        List<Map<String, Object>> result = new ArrayList<>();
        //主表
        Map<String, Object> column = null;
        //提出人子表
        Map<String, Object> columnMember = null;
        //权利人子表
        Map<String, Object> columnOwner = null;
        //序号
        int order = 0;
        int orderMember = 0;
        int orderOwner = 0;
        for (int i = 0; i < show.length; i++) {
            order++;
            String[] _column = show[i].split(":");
            column = new HashMap<>();
            column.put("code", _column[0]);
            column.put("name", _column[1]);
            column.put("type", "text");
            column.put("order", order);
            switch (_column[0]) {
                case "secretLevel":
                    column.put("value", SDictUtil.getDictName(MMConstans.business_type, "secretLevel", (String) data.get(_column[0])));
                    break;
                case "sourceType":
                    column.put("value", SDictUtil.getDictName(MMConstans.business_type, "sourceType", (String) data.get(_column[0])));
                    break;
                case "members":
                    Map<String, Object> dataM = null;
                    List<Map<String, Object>> memberList = null;
                    List<List<Map<String, Object>>> memberLists = new ArrayList<>();
                    for (TkymmTechMember dataMember : dataMembers) {
                        dataM = BeanUtil.beanToMap(dataMember);
                        orderMember = 0;
                        memberList = new ArrayList<>();
                        for (int m = 0; m < memberShow.length; m++) {
                            orderMember++;
                            String[] _columnMember = memberShow[m].split(":");
                            columnMember = new HashMap<>();
                            columnMember.put("code", _columnMember[0]);
                            columnMember.put("name", _columnMember[1]);
                            columnMember.put("type", "text");
                            columnMember.put("order", orderMember);
                            columnMember.put("value", dataM.get(_columnMember[0]));
                            memberList.add(columnMember);
                        }
                        memberLists.add(memberList);
                    }
                    column.put("type", "list");
                    column.put("members", memberLists);
                    break;
                case "owners":
                    Map<String, Object> dataO = null;
                    List<Map<String, Object>> ownerList = null;
                    List<List<Map<String, Object>>> ownerLists = new ArrayList<>();
                    for (TkymmTechOwner dataOwner : dataOwners) {
                        dataO = BeanUtil.beanToMap(dataOwner);
                        orderOwner = 0;
                        ownerList = new ArrayList<>();
                        for (int o = 0; o < ownerShow.length; o++) {
                            orderOwner++;
                            String[] _columnOwner = ownerShow[o].split(":");
                            columnOwner = new HashMap<>();
                            columnOwner.put("code", _columnOwner[0]);
                            columnOwner.put("name", _columnOwner[1]);
                            columnOwner.put("type", "text");
                            columnOwner.put("order", orderOwner);
                            columnOwner.put("value", dataO.get(_columnOwner[0]));
                            ownerList.add(columnOwner);
                        }
                        column.put("type", "list");
                        ownerLists.add(ownerList);
                    }
                    column.put("owners", ownerLists);
                    break;
                default:
                    column.put("value", data.get(_column[0]));
            }
            result.add(column);
        }

        //需要展示的附件
        String businessId = null;
        switch (processCode) {
            case MMConstans.process_rd:
                businessId = technologyEx.getTechnologyId();
                break;
            case MMConstans.kymmreward:
                businessId = techImplEx.getTechImplId();
                break;
            default:
                businessId = techImplEx.getTechnologyId();
        }
        List<AttachmentMap> attachmentList = SAttachmentUtil.getAttachmentBySourceId(businessId, MMConstans.business_type);
        for (AttachmentMap attachmentMap : attachmentList) {
            order++;
            column = new HashMap<>();
            column.put("code", "");
            switch (attachmentMap.getSourceLabel1()) {
                case "SBFILE":
                    column.put("name", "认定相关附件");
                    break;
                case "Property_Rights":
                    column.put("name", "知识产权协议附件");
                    break;
                case "IMPLEMENTATION_SUMMARY":
                    column.put("name", "实施相关附件");
                    break;
                case "PROOF_IMPLEMENTATION":
                    column.put("name", "实施情况证明");
                    break;
                case "AFILE":
                    column.put("name", "需解密的技术秘密的补充描述");
                    break;
                default:
                    column.put("name", "相关附件");
            }
//            column.put("name", "相关附件");//可以根据AttachmentMap中的字段具体指定名称
            column.put("type", "docUrl");//在线预览的附件类型 固定
            column.put("order", order);
            column.put("value", attachmentMap.getAttachmentName());//附件名称
            column.put("url", appContextProp.getCtxGGMK() + "attachment/nmDownload/" + attachmentMap.getAttachmentId() + "?isMobile=true&operator=" + operator);//附件链接，固定
            result.add(column);
        }

        return result;
    }


    /**
     * 移动端提交  S_ZZ_MM_08
     *    rd-(送公司领导、同意、否定)
     *    impl-(提交、启动发奖)
     *    根据提交参数判断
     * @param eiInfo
     * @throws Exception
     */
    public EiInfo mobileSubmit(EiInfo eiInfo) throws Exception {

        Map<String,Object> postData = (Map<String, Object>) eiInfo.get("postData");
        String businessId = (String) postData.get("businessId");
        String processCode = (String) postData.get("processCode");
        String activityCode = (String) postData.get("currentActivity");
        postData.put("approvecomment", postData.get("comment"));
        postData.put("operator", eiInfo.get("operator"));
        postData.put("activityCode", activityCode);
        postData.put("processInstanceId", postData.get("processInstanceId"));

        switch(processCode){
            case MMConstans.process_rd:
                //认定标记--按钮传递参数
                String approvalResults = (String) postData.get("approvalResults");
                postData.put("approvalResults",approvalResults);
                IMMRDProcess instanceRd = RdFactory.getInstance((String) postData.get("currentActivity"));
                if (ObjectUtil.isNotEmpty(instanceRd)) {
                    R r = instanceRd.mmRdProcess(postData);
                    eiInfo.setMsg(r.getMessage());
                    eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
                }
                break;
            case MMConstans.KYMM_GXXS:
                //认定标记--按钮传递参数
                String contributionConfirmation = (String) postData.get("contributionConfirmation");
                postData.put("contributionConfirmation",contributionConfirmation);
                IMMRDProcess instance = RdFactory.getInstance((String) postData.get("currentActivity"));
                if (ObjectUtil.isNotEmpty(instance)) {
                    R r = instance.mmRdProcess(postData);
                    eiInfo.setMsg(r.getMessage());
                    eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
                }
                break;
            case MMConstans.process_jm:
                //认定标记--按钮传递参数
                postData.put("approvalResults",(String) postData.get("approvalResults"));
                IMMJMProcess instanceJm = JMFactory.getJmInstance((String) postData.get("currentActivity"));
                if (ObjectUtil.isNotEmpty(instanceJm)) {
                    R r = instanceJm.mmJmProcess(postData);
                    eiInfo.setMsg(r.getMessage());
                    eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
                }
                break;
            case MMConstans.kymmreward:
                String transitionKey = (String) postData.get("transitionKey");
                postData.put("transitionKey",transitionKey);
                IMMIMPLProcess instanceImpl = ImplFactory.getInstance((String) postData.get("currentActivity"));
                if (ObjectUtil.isNotEmpty(instanceImpl)) {
                    R r = instanceImpl.mmImplProcess(postData);
                    eiInfo.setMsg(r.getMessage());
                    eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
                }
                break;
            default:

        }
        return eiInfo;
    }

    /**
     *  移动端退回 S_ZZ_MM_09
     * @param eiInfo
     * @return
     * @throws Exception
     */
    public EiInfo mobileReturn(EiInfo eiInfo) throws Exception {

        Map<String,Object> postData = (Map<String, Object>) eiInfo.get("postData");
        String businessId = (String) postData.get("businessId");
        String approvecomment = (String) postData.get("comment");
        String taskId = (String) postData.get("taskId");

        postData.put("approvecomment", postData.get("comment"));
        postData.put("operator", eiInfo.get("operator"));
        postData.put("activityCode", eiInfo.get("currentActivity"));

        WorkFlow workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
        String returnActivityCode = (String) postData.get("returnCode");

        if (StringUtils.isNotEmpty(returnActivityCode) && null != workFlow) {
            workFlow.setReturnActivityKey(returnActivityCode);
            workFlow.setComment(approvecomment);
            String nextUser = SWorkFlowUtil.doReturn(workFlow.getOperator(), workFlow);
            eiInfo.setMsg("退回成功,接收人:"+nextUser);
            eiInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } else {
            eiInfo.setMsg("异常:移动端未获取到相关参数");
            eiInfo.setStatus(EiConstant.STATUS_FAILURE);
        }
        return eiInfo;
    }
}
