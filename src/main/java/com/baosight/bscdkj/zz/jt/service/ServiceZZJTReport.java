package com.baosight.bscdkj.zz.jt.service;

import java.util.List;
import java.util.Map;

import com.baosight.bscdkj.utils.BizIdUtil;
import com.baosight.bscdkj.zz.jt.business.BusinessReport;
import com.baosight.bscdkj.zz.jt.domain.TzzjtPropertyList;
import com.baosight.bscdkj.zz.jt.domain.TzzjtReportEx;
import com.baosight.bscdkj.common.zz.domain.TzzjtCheck;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.service.PageService;

import com.baosight.iplat4j.core.ei.EiConstant;

import com.baosight.bscdkj.common.zz.domain.TzzjtReport;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 结题报告表Service接口
 * 
 * <AUTHOR>
 * @date 2021-11-20
 */
@Service
public class ServiceZZJTReport extends PageService{
    @Autowired
	private BusinessReport businessReport;

	Logger logger = LoggerFactory.getLogger(this.getClass());
	/**
	 * 初始化
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
		return inInfo;
	}
	@Override
	public EiInfo query(EiInfo inInfo) {
	    try{
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TzzjtReport> queryList = businessReport.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
	public EiInfo page(EiInfo inInfo) {
	    try{
            Map<String, Object> queryData = getQueryData(inInfo);

            TableDataInfo queryPage = businessReport.quertPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

    public EiInfo queryByBizId(EiInfo inInfo) {
        try{
            String bizId = (String) inInfo.get("bizId");
            TzzjtReportEx query = businessReport.queryByBizId(bizId);
            if(query==null){
                query=new TzzjtReportEx();
                inInfo.set("numPf", 0);
            }else{
                //项目评估-知识产权评分
                int num = businessReport.setFen(query.getReportId());
                inInfo.set("numPf", num);
            }
            inInfo.set("reportEx", query);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }
    @Override
	public EiInfo update(EiInfo inInfo) {
	    try{
            Map row = inInfo.getBlock("i").getRow(0);
            TzzjtReport bean = BeanUtil.toBean(row, TzzjtReport.class);
            businessReport.update(UserSession.getLoginName(),bean);
            inInfo.setMsg("修改成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
    @Override
	public EiInfo insert(EiInfo inInfo) {
	    try{
            Map row = inInfo.getBlock("i").getRow(0);
            TzzjtReportEx bean = BeanUtil.toBean(row, TzzjtReportEx.class);
            TzzjtPropertyList jtpropertyList=new TzzjtPropertyList();
            if(inInfo.getBlock("j")!=null) {
                Map map = inInfo.getBlock("j").getRow(0);
                jtpropertyList = (TzzjtPropertyList) map.get("propertyList");
            }
            TzzjtReport report = new TzzjtReport();
            BeanUtil.copyProperties(bean, report);
            String reportId=report.getReportId();
            if(StringUtils.isNotEmpty(reportId)){
                businessReport.update(UserSession.getLoginName(),report);
                inInfo.setMsg("修改成功");
            }else{
                reportId= BizIdUtil.INSTANCE.nextId();
                report.setReportId(reportId);
                bean.setReportId(reportId);
                businessReport.insert(UserSession.getLoginName(),report);
                inInfo.setMsg("添加成功");
            }
            inInfo.setCell("i", 0, "reportId", reportId);
            businessReport.insertOrUpdateEx(UserSession.getLoginName(),bean,jtpropertyList);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo load(EiInfo inInfo) {
	    try{
            String reportId = (String) inInfo.get("reportId");
            TzzjtReport query = businessReport.query(reportId);
            inInfo.set("report", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo remove(EiInfo inInfo) {
        try{
            String loginName=UserSession.getLoginName();
            String reportId = (String) inInfo.get("reportId");
        if (StrUtil.isNotBlank(reportId)) {
            for (String id : reportId.split(",")) {
                businessReport.delete(loginName,id);
            }
        }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
       }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;

	}

    /**
     * 流程提交
     * @param inInfo
     * @return
     */
    public EiInfo submitWF(EiInfo inInfo){
        try {
            String operator = UserSession.getLoginName();
            Map row = inInfo.getBlock("i").getRow(0);
            TzzjtCheck bean = BeanUtil.toBean(row, TzzjtCheck.class);
            inInfo=insert(inInfo);
            if(inInfo.getStatus()==EiConstant.STATUS_SUCCESS){
                String msg = businessReport.submitWF(operator,row);
                inInfo.setMsg(msg);
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }
}
