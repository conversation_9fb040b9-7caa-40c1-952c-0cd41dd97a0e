package com.baosight.bscdkj.zz.gc.service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.baosight.bscdkj.mp.ty.utils.ClobUtil;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bscdkj.zz.gc.business.BusinessTeachMonth;
import com.baosight.bscdkj.zz.gc.domain.TzzgcTeachMonthEx;
import com.baosight.bscdkj.zz.gc.domain.TzzgcTeachMonthExport;
import com.baosight.bscdkj.zz.lx.business.BusinessMain;
import com.baosight.bscdkj.zz.lx.business.BusinessTeachPlantask;
import com.baosight.bscdkj.common.constant.ZZZCConstants;
import com.baosight.bscdkj.common.zz.domain.TzzlxMain;
import com.baosight.bscdkj.common.zz.domain.TzzlxTeachPlantask;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.service.PageService;

import com.baosight.iplat4j.core.ei.EiConstant;

import com.baosight.bscdkj.common.zz.domain.TzzgcTeachMonth;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 项目月报表Service接口
 * 
 * <AUTHOR>
 * @date 2021-11-20
 */
@Service
public class ServiceZZGCTeachMonth extends PageService{
    @Autowired
	private BusinessTeachMonth businessTeachMonth;
	Logger logger = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private BusinessMain businessMain;
    @Autowired
    private BusinessTeachPlantask businessTeachPlantask;

    /**
	 * 初始化
	 */
	@Override
	public EiInfo initLoad(EiInfo inInfo) {
		return inInfo;
	}
	@Override
	public EiInfo query(EiInfo inInfo) {
	    try{
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TzzgcTeachMonth> queryList = businessTeachMonth.queryList(queryData);
            for (TzzgcTeachMonth month:queryList){
                TzzgcTeachMonthEx beanEx = new TzzgcTeachMonthEx();
                BeanUtil.copyProperties(month, beanEx);
                WorkFlow workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(beanEx.getMonthId());
                if(workFlow!=null){
                    beanEx.setProcessInstanceId(workFlow.getProcessInstanceId());
                    beanEx.setCurrentOperator(workFlow.getCurrentOperator());
                }
            }
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

    public EiInfo queryFlowInfo(EiInfo inInfo) {
        try{
            Map<String, Object> queryData = getQueryData(inInfo);
            List<Map<String,Object>> queryList = businessTeachMonth.queryFlowInfo(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

	public EiInfo queryXmzg(EiInfo inInfo){
        try{
            List<TzzgcTeachMonthEx> queryList = businessTeachMonth.queryXmzg();
            inInfo.set("xmzgList",queryList);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

	public EiInfo page(EiInfo inInfo) {
	    try{
            Map<String, Object> queryData = getQueryData(inInfo);

            TableDataInfo queryPage = businessTeachMonth.quertPage(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

    public EiInfo queryMonthReport(EiInfo inInfo) {
        try{
            Map<String, Object> queryData = getQueryData(inInfo);
            String excel = (String) queryData.get("excel");
            if(StringUtils.isNotEmpty(excel) && "excel".equals(excel)){
                queryData.put("pageNum", 1);
                queryData.put("pageSize", -999999);
            }
            TableDataInfo queryPage = businessTeachMonth.queryMonthReport(queryData);
            inInfo=setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryGenZong(EiInfo inInfo) {
        try{
            Map<String, Object> queryData = getQueryData(inInfo);
            String excel = (String) queryData.get("excel");
            if(StringUtils.isNotEmpty(excel) && "excel".equals(excel)){
                queryData.put("pageNum", 1);
                queryData.put("pageSize", -999999);
            }
            TableDataInfo queryPage = businessTeachMonth.queryGenZong(queryData);
            List<TzzgcTeachMonthExport> listInfo = new ArrayList<>();
            List<TzzgcTeachMonthExport> list = (List<TzzgcTeachMonthExport>) queryPage.getRows();
            if(queryPage.getRows().size()>0) {
                for (int i = 0; i < list.size(); i++) {
                    TzzgcTeachMonthExport bean = BeanUtil.toBean(list.get(i), TzzgcTeachMonthExport.class);
                    bean.setXh(i + 1);
                    Map<String, Object> contentMap = ClobUtil.getContentMap(bean.getMonthId());
                    if (ObjectUtil.isNotEmpty(contentMap)) {
                        bean.setMonthWork((String) contentMap.get("monthWork"));
                        bean.setMonthPromote((String) contentMap.get("monthPromote"));
                    }
                    listInfo.add(bean);
                }
            }
            queryPage.setRows(listInfo);
            inInfo =setPage(inInfo, queryPage);
            inInfo.set(EiConstant.resultBlock, listInfo);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 定时启动月报
     */
	public EiInfo startMonthQuartz(EiInfo inInfo){
	    try{
            Map param = new HashMap();
            param.put("projectStatus",ZZZCConstants.STATUS_YX); //查询所有运行中的项目开启月报
            List<TzzlxMain> mains = businessMain.queryList(param);
            businessTeachMonth.doStartQuartz(mains);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public static void main(String[] args) throws ParseException {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        String date = DateUtil.today();
        DateTime thisDate = new DateTime(date);
        Calendar cld = Calendar.getInstance();
        cld.setTime(thisDate);

    //月份-1，天设置为1。下个月第0天，就是这个月最后一天
        cld.add(Calendar.MONTH, -1);
        cld.set(Calendar.DAY_OF_MONTH, 1);
        String dateMin = sdf.format(cld.getTime());

        Calendar cld2 = Calendar.getInstance();
        cld2.setTime(thisDate);
        cld2.set(Calendar.DAY_OF_MONTH,1);
        cld2.add(Calendar.DATE,-1);
        String lastDay = sdf.format(cld2.getTime());
        System.out.println(dateMin+"--"+lastDay);
    }

    /**
     * 定时将上月所有未填写完毕月报进行标记  未及时填写、未按时填写
     * 6日凌晨2点启动定时任务 ，查找说有待办任务还在负责人界面置成 未及时填写
     * @param inInfo
     * @return
     */
    public EiInfo editMonStatusQuartz(EiInfo inInfo){
        try{
            businessTeachMonth.editMonthStatus();
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

	public  EiInfo doStart(EiInfo inInfo){
	    String bizId = (String) inInfo.get("bizId");
	    businessTeachMonth.doStart(bizId);
	    inInfo.setMsg("启动成功");
	    return inInfo;
    }

    /**
     * 新增修改
     * @param inInfo
     * @return
     */
    public EiInfo addEx(EiInfo inInfo){
        try{
            TzzgcTeachMonthEx bean = (TzzgcTeachMonthEx) inInfo.get("teachMonth");
            String monthId = businessTeachMonth.addEx(UserSession.getLoginName(),bean);
            inInfo.setCell("i",0,"monthId",monthId);
            inInfo.setMsg("暂存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 提交
     * @param inInfo
     * @return
     */
    public EiInfo submitWF(EiInfo inInfo){
        try{
            TzzgcTeachMonthEx bean = (TzzgcTeachMonthEx) inInfo.get("teachMonth");
            String msg =  businessTeachMonth.submitWF(bean);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryByBizId(EiInfo inInfo){
        try{
            String mainId = (String) inInfo.get("mainId");
            TzzgcTeachMonth query = businessTeachMonth.queryByBizId(mainId);
            TzzgcTeachMonthEx beanEx = new TzzgcTeachMonthEx();
            BeanUtil.copyProperties(query, beanEx);
            loadClod(query.getMonthId(),beanEx);
            inInfo.set("month", beanEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryById(EiInfo inInfo){
        try{
            String monthId = (String) inInfo.get("monthId");
            TzzgcTeachMonth query = businessTeachMonth.query(monthId);
            TzzgcTeachMonthEx beanEx = new TzzgcTeachMonthEx();
            BeanUtil.copyProperties(query, beanEx);
            loadClod(monthId,beanEx);
            inInfo.set("month", beanEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
	    return inInfo;
    }

    /**
     * 查看详情（流程数据
     * @param inInfo
     * @return
     */
    public EiInfo queryOpen(EiInfo inInfo){
        try{
            String monthId = (String) inInfo.get("monthId");
            TzzgcTeachMonth query = businessTeachMonth.query(monthId);
            TzzgcTeachMonthEx beanEx = new TzzgcTeachMonthEx();
            BeanUtil.copyProperties(query, beanEx);
            loadClod(monthId,beanEx);
//            businessTeachMonth.queryFlow(beanEx);
            TzzlxMain main = businessMain.query(query.getBizId());
            inInfo.set("main",main);
            inInfo.set("month", beanEx);
            TzzlxTeachPlantask plan = businessTeachPlantask.queryByBiz(main.getMainId());
            inInfo.set("plan", plan);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public void loadClod(String monthId,TzzgcTeachMonthEx beanEx){
        Map<String, Object> contentMap = ClobUtil.getContentMap(monthId);
        if(ObjectUtil.isNotEmpty(contentMap)) {
            beanEx.setMonthWork((String) contentMap.get("monthWork"));
            beanEx.setMonthNextplan((String) contentMap.get("monthNextplan"));
            beanEx.setMonthPromote((String) contentMap.get("monthPromote"));
            beanEx.setMonthSolunction((String) contentMap.get("monthSolunction"));
        }
        WorkFlow workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(monthId);
        if(workFlow!=null){
            beanEx.setProcessInstanceId(workFlow.getProcessInstanceId());
            beanEx.setCurrentOperator(workFlow.getCurrentOperator());
        }
    }

    @Override
	public EiInfo update(EiInfo inInfo) {
	    try{
            Map row = inInfo.getBlock("i").getRow(0);
            TzzgcTeachMonth bean = BeanUtil.toBean(row, TzzgcTeachMonth.class);
            businessTeachMonth.update(UserSession.getLoginName(),bean);
            inInfo.setMsg("修改成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}
    @Override
	public EiInfo insert(EiInfo inInfo) {
	    try{
            Map row = inInfo.getBlock("i").getRow(0);
            TzzgcTeachMonth bean = BeanUtil.toBean(row, TzzgcTeachMonth.class);
            businessTeachMonth.insert(UserSession.getLoginName(),bean);
            inInfo.setMsg("添加成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo load(EiInfo inInfo) {
	    try{
            String monthId = (String) inInfo.get("monthId");
            TzzgcTeachMonth query = businessTeachMonth.query(monthId);
            inInfo.set("teachMonth", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
		}catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;
	}

	public EiInfo remove(EiInfo inInfo) {
        try{
            String loginName=UserSession.getLoginName();
            String monthId = (String) inInfo.get("monthId");
        if (StrUtil.isNotBlank(monthId)) {
            for (String id : monthId.split(",")) {
                businessTeachMonth.delete(loginName,id);
            }
        }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
       }catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
		return inInfo;

	}
}
