package com.baosight.bscdkj.ki.zl.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.ki.domain.*;
import com.baosight.bscdkj.common.service.PageService;
import com.baosight.bscdkj.ki.zl.business.*;
import com.baosight.bscdkj.ki.zl.common.KIZLConstants;
import com.baosight.bscdkj.ki.zl.domain.TkizlDlExcel;
import com.baosight.bscdkj.ki.zl.domain.TkizlDsqtzExcel;
import com.baosight.bscdkj.ki.zl.domain.TkizlDsqxxExcel;
import com.baosight.bscdkj.ki.zl.domain.TkizlPatentAttachment;
import com.baosight.bscdkj.ki.zl.utils.PatentApplyUtil;
import com.baosight.bscdkj.ki.zl.utils.PatentUtil;
import com.baosight.bscdkj.ki.zl.utils.SqfUtil;
import com.baosight.bscdkj.ki.zl.utils.TerminationUtil;
import com.baosight.bscdkj.mp.ad.dto.ADOrg;
import com.baosight.bscdkj.mp.ad.utils.OrgUtil;
import com.baosight.bscdkj.mp.ad.utils.RoleUtil;
import com.baosight.bscdkj.mp.ty.dto.AttachmentMap;
import com.baosight.bscdkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bscdkj.mp.ty.utils.SDictUtil;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class ServiceKIZLPatentInfo extends PageService {

    Logger logger = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private BusinessPatentInfoPatent businessPatentInfo;
    @Autowired
    private BusinessApplyBaseinfoPatent businessApplyBaseinfo;
    @Autowired
    private BusinessApplySqrPatent businessApplySqr;
    @Autowired
    private BusinessSsjBaseinfoPatent businessSsjBaseinfo;
    @Autowired
    private BusinessApplyRyxxPatent businessApplyRyxxPatent;


    public EiInfo load(EiInfo inInfo) {
        try {
            String patentId = (String) inInfo.get("patentId");
            TkizlPatentInfo query = businessPatentInfo.load(patentId);
            if (query.getFml() == null) {
                query.setFml(PatentApplyUtil.getInventorUserNameByApplyId(query.getApplyId()));//获取发明人名称
            }
            if (query.getApplyName() == null) {
                query.setApplyName(businessApplyBaseinfo.load(query.getApplyId()).getApplyName());//获取专利申请名称
            }
            inInfo.set("data", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    private void allocationParam(Map param) {
        List<ADOrg> orgs = RoleUtil.getOrgByUser(UserSession.getLoginName(), KIZLConstants.KI_ZGBM_ADMIN);
        if (orgs != null && orgs.size() > 0) {
            StringBuffer sb = new StringBuffer("(");
            for (ADOrg adOrg : orgs) {
                if (sb.length() > 1) {
                    sb.append(" OR ");
                }
                sb.append(" GLDW_CODE like '%" + adOrg.getOrgCode() + "%' ");
            }
            sb.append(")");
            param.put("dynSql", sb.toString());
        } else {
            param.put("dynSql", "1=2");
        }
    }

    public EiInfo queryIntegrated(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            if (!RoleUtil.isAdmin(UserSession.getLoginName())) {//系统管理员查全部
                allocationParam(queryData);
            }
            TableDataInfo queryPage = businessPatentInfo.queryIntegrated(queryData);
            for (Object row : queryPage.getRows()) {
                TkizlPatentInfo info = (TkizlPatentInfo) row;
                String patentType = SDictUtil.getDictName("KIZL", "KI_PATENT_TYPE", info.getPatentType());
                info.setPatentType(patentType);
                String flowStatus = SDictUtil.getDictName("KIZL", "KI_FLOW_STATUS", info.getFlowStatus());
                info.setFlowStatus(flowStatus);
//				String patentStatus = SDictUtil.getDictName("KIZL", "KI_PATENT_STATUS", info.get("patentStatus")+"");
                String flzt = SDictUtil.getDictName("KIZL", "KI_FLZT", info.getFlzt());
                info.setPatentStatus(flzt);
            }
            return setPage(inInfo, queryPage);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryIntegratedPlus(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo queryPage = businessPatentInfo.queryIntegratedPlus(queryData);
            for (Object row : queryPage.getRows()) {
                TkizlPatentInfo info = (TkizlPatentInfo) row;
                String patentType = SDictUtil.getDictName("KIZL", "KI_PATENT_TYPE", info.getPatentType());
                info.setPatentType(patentType);
                String flowStatus = SDictUtil.getDictName("KIZL", "KI_FLOW_STATUS", info.getFlowStatus());
                info.setFlowStatus(flowStatus);
//				String patentStatus = SDictUtil.getDictName("KIZL", "KI_PATENT_STATUS", info.get("patentStatus")+"");
                String flzt = SDictUtil.getDictName("KIZL", "KI_FLZT", info.getFlzt());
                info.setPatentStatus(flzt);
            }
            return setPage(inInfo, queryPage);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-综合查询
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryAllPatent(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            System.out.println("——————————————————————————————————————————————专利综合查询——————————————————————————————————————————————");
            TableDataInfo queryPage = businessPatentInfo.queryAllPatent(queryData);
            for (Object row : queryPage.getRows()) {
                TkizlPatentInfoZh info = (TkizlPatentInfoZh) row;
                String patentType = SDictUtil.getDictName("KIZL", "KI_PATENT_TYPE", info.getPatentType());
                info.setPatentType(patentType);//专利类型
                String projectType = SDictUtil.getDictName("KYXM", "project_type", info.getProjectType());
                info.setProjectType(projectType);//项目类型
                String applyId = info.getApplyId();
                String fml = PatentApplyUtil.getInventorUserNameByApplyId(applyId);
                String gxxs = PatentApplyUtil.getInventorUserGxxsByApplyId(applyId);
                String sqr = PatentApplyUtil.getApplyUsersByApplyId(applyId);
                String zjps = PatentApplyUtil.getZjpsByApplyId(applyId);
                info.setFml(fml);//发明人
                if (fml != null && gxxs != null && !fml.equals("") && !gxxs.equals("")) {
                    String[] spilt = fml.split(",");
                    String[] spilt2 = gxxs.split(",");
                    int i = 0;
                    for (String str : spilt) {
                        if (i == 0) {
                            gxxs = str + "-" + spilt2[i];
                        } else {
                            gxxs = gxxs + "," + str + "-" + spilt2[i];
                        }
                        i++;
                    }
                }
                if(StrUtil.isNotBlank(info.getFlzt())) {
                    if (info.getFlzt().equals("06") || info.getFlzt().equals("09") || info.getFlzt().equals("11")) {
                        info.setActName("无效");
                    }
                }
                info.setGxxs(gxxs);//贡献系数
                info.setSqr(sqr);//申请人
                info.setZjps(zjps);//专家评审
                String flzt = SDictUtil.getDictName("KIZL", "KI_FLZT", info.getFlzt());
                info.setFlzt(flzt);//法律状态
            }
            return setPage(inInfo, queryPage);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo exportAllPatent(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkizlPatentInfoZh> list = businessPatentInfo.exportAllPatent(queryData);
            for (TkizlPatentInfoZh info : list) {
                String patentType = SDictUtil.getDictName("KIZL", "KI_PATENT_TYPE", info.getPatentType());
                info.setPatentType(patentType);//专利类型
                String projectType = SDictUtil.getDictName("KYXM", "project_type", info.getProjectType());
                info.setProjectType(projectType);//项目类型
                String applyId = info.getApplyId();
                String fml = PatentApplyUtil.getInventorUserNameByApplyId(applyId);
                String gxxs = PatentApplyUtil.getInventorUserGxxsByApplyId(applyId);
                String sqr = PatentApplyUtil.getApplyUsersByApplyId(applyId);
                String zjps = PatentApplyUtil.getZjpsByApplyId(applyId);
                info.setFml(fml);//发明人
                info.setGxxs(gxxs);//贡献系数
                info.setSqr(sqr);//申请人
                info.setZjps(zjps);//专家评审
                String flzt = SDictUtil.getDictName("KIZL", "KI_FLZT", info.getFlzt());
                info.setFlzt(flzt);//法律状态
            }
            inInfo.set("list", list);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-技术交底清单
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryjsjdqd(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo queryPage = businessPatentInfo.queryjsjdqd(queryData);
            return setPage(inInfo, queryPage);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 导出-技术交底清单
     *
     * @param inInfo
     * @return
     */
    public EiInfo exportJsjdqd(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkizlPatentInfoQd> list = businessPatentInfo.exportJsjdqd(queryData);
            inInfo.set("list", list);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-专利交底发明人信息
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryzljdqd(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo queryPage = businessPatentInfo.queryzljdqd(queryData);
            return setPage(inInfo, queryPage);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 导出-专利交底发明人信息
     *
     * @param inInfo
     * @return
     */
    public EiInfo exportJdfmrxx(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<Map> list = businessPatentInfo.exportJdfmrxx(queryData);
            inInfo.set("list", list);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryByBgbh(EiInfo inInfo) {
        try {
            String bgbh = (String) inInfo.get("bgbh");
            TkizlPatentInfo query = PatentApplyUtil.getPatentInfoByBgbh(bgbh);
            inInfo.set("data", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryByApply(EiInfo inInfo) {
        try {
            String applyId = (String) inInfo.get("businessGuid");
            TkizlPatentInfo query = businessPatentInfo.queryByApplyId(applyId);
            if (query != null) {
                TkizlApplyBaseinfo applyBaseinfo = businessApplyBaseinfo.load(applyId);
                if (query.getFml() == null) {
                    query.setFml(PatentApplyUtil.getInventorUserNameByApplyId(applyId));//获取发明人名称
                }
                if (query.getApplyName() == null) {
                    query.setApplyName(applyBaseinfo.getApplyName());//获取专利申请名称
                }
                if (query.getMoneyDlf() == null) {
                    if (SqfUtil.getJeByName(KIZLConstants.dlf).compareTo(BigDecimal.ZERO) != 0) {
                        query.setMoneyDlf(SqfUtil.getJeByName(KIZLConstants.dlf));//初始化  代理费
                    }
                }
                if (query.getMoneyYhs() == null) {
                    if (SqfUtil.getJeByName(KIZLConstants.yhs).compareTo(BigDecimal.ZERO) != 0) {
                        query.setMoneyYhs(SqfUtil.getJeByName(KIZLConstants.yhs));//初始化  印花税
                    }
                }
				/*if(query.getMoneyGgysf()==null) {
					if(SqfUtil.getJeByName(KIZLConstants.ggysf).compareTo(BigDecimal.ZERO)!=0) {
						query.setMoneyGgysf(SqfUtil.getJeByName(KIZLConstants.ggysf));//初始化  公告印刷费
					}
				}*/
            } else {
                query = new TkizlPatentInfo();
                query.setApplyId(applyId);
            }
            inInfo.set("data", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo querySsj(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            TableDataInfo queryPage = businessPatentInfo.querySsj(queryData);
            for (Object info : queryPage.getRows()) {
                TkizlPatentInfo row = (TkizlPatentInfo) info;
                HashMap param = new HashMap();
                param.put("patentId", row.getPatentId());
                param.put("displayOrder", "CREATE_DATE DESC");
                List<TkizlSsjBaseinfo> list = businessSsjBaseinfo.queryList(param);
                if (list.size() > 0) {
                    TkizlSsjBaseinfo kizlSsjBaseinfo = list.get(0);
                    if (kizlSsjBaseinfo != null && kizlSsjBaseinfo.getSsjlx().equals("SP")) {
                        row.setExtra1("已申报水平奖");
                    }
                }
            }
            return setPage(inInfo, queryPage);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo query(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkizlPatentInfo> list = businessPatentInfo.queryList(queryData);
            inInfo.set("data", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryList(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TkizlPatentInfo> rows = businessPatentInfo.queryList(queryData);
            inInfo.addBlock("r").setRows(rows);

            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryPage(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            queryData.put("displayOrder", "CREATE_DATE desc");
            TableDataInfo queryPage = businessPatentInfo.queryPage(queryData);
            return setPage(inInfo, queryPage);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-知识产权当年实绩
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryZscqdnsjList(EiInfo inInfo) {
        try {
            List<Map> list = new ArrayList<Map>();
            Map<String, Object> queryData = getQueryData(inInfo);
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            if (month == 1) {
                year = year - 1;
                queryData.put("startDate", year + "-01-01");
                queryData.put("endDate", year + "-12-31");
            } else if (month == 2) {
                queryData.put("startDate", year + "-01-01");
                queryData.put("endDate", year + "-01-31");
            } else {
                month = month - 1;
                queryData.put("startDate", year + "-01-01");
                if ((month + "").length() < 2) {
                    queryData.put("endDate", year + "-0" + month + "-31");
                } else {
                    queryData.put("endDate", year + "-" + month + "-31");
                }
            }
            System.out.println("————————————知识产权当年实绩报表————————————");
            List<Map> queryC = businessPatentInfo.queryZscqdnsjC(queryData);//其他单位部门统计数据
            for (Map row : queryC) {
                if (ObjectUtil.isNotEmpty(row.get("FMS")) && ObjectUtil.isNotEmpty(row.get("SQS"))) {
                    row.put("proportion", 0);
                    BigDecimal fms = new BigDecimal(row.get("FMS") + "");
                    BigDecimal sqs = new BigDecimal(row.get("SQS") + "");
                    if (!sqs.equals(BigDecimal.ZERO)) {
                        BigDecimal proportion = fms.multiply(new BigDecimal(100)).divide(sqs, 1, BigDecimal.ROUND_HALF_UP);
                        row.put("proportion", proportion);
                    }
                }
                list.add(row);
            }
            inInfo.set("data", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-知识产权当年实绩-专利明细列表合计
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryZscqmxhj(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            System.out.println("————————————知识产权当年实绩明细合计————————————");
            List<Map> listmap = businessPatentInfo.queryZscqdnsjhj(queryData);
            BigDecimal gxxszh = BigDecimal.ZERO;
            for (Map row : listmap) {
                if (ObjectUtil.isNotEmpty(row.get("gxxs"))) {
                    gxxszh = gxxszh.add(new BigDecimal(row.get("gxxs") + ""));
                }
            }
            inInfo.set("gxxszh", gxxszh);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-知识产权当年实绩-专利明细列表
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryZscqmx(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            System.out.println("————————————知识产权当年实绩明细————————————");
            TableDataInfo queryPage = businessPatentInfo.queryZscqdnsjmx(queryData);
            return setPage(inInfo, queryPage);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-知识产权当年实绩-技术秘密明细列表合计
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryJsmmhj(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            if (month == 1) {
                year = year - 1;
                queryData.put("startDate", year + "-01-01");
                queryData.put("endDate", year + "-12-31");
            } else if (month == 2) {
                queryData.put("startDate", year + "-01-01");
                queryData.put("endDate", year + "-01-31");
            } else {
                month = month - 1;
                queryData.put("startDate", year + "-01-01");
                if ((month + "").length() < 2) {
                    queryData.put("endDate", year + "-0" + month + "-31");
                } else {
                    queryData.put("endDate", year + "-" + month + "-31");
                }
            }
            if (queryData.get("deptCode") != null && !queryData.get("deptCode").equals("")) {
                String dept = queryData.get("deptCode") + "";
                if (dept.equals("BGTAEC00")) {
                    queryData.put("deptCode", "/BSTA/BGTA/BGTA00/BGTAEC00");
                } else if (dept.equals("BGTM00")) {
                    queryData.put("deptCode", "/BSTA/BGTA/BGTM/" + dept);
                } else {
                    queryData.put("deptCode", "/BSTA/BGTA/" + dept);
                }
            }
            List<Map> listmap = businessPatentInfo.queryJsmmhj(queryData);
            BigDecimal gxxszh = BigDecimal.ZERO;
            for (Map row : listmap) {
                if (ObjectUtil.isNotEmpty(row.get("gxxs"))) {
                    gxxszh = gxxszh.add(new BigDecimal(row.get("gxxs") + ""));
                }
            }
            inInfo.set("gxxszh", gxxszh);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-知识产权当年实绩-技术秘密明细列表
     *
     * @param inInfo
     * @return
     */
    public EiInfo queryJsmm(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            Calendar calendar = Calendar.getInstance();
            int year = calendar.get(Calendar.YEAR);
            int month = calendar.get(Calendar.MONTH) + 1;
            if (month == 1) {
                year = year - 1;
                queryData.put("startDate", year + "-01-01");
                queryData.put("endDate", year + "-12-31");
            } else if (month == 2) {
                queryData.put("startDate", year + "-01-01");
                queryData.put("endDate", year + "-01-31");
            } else {
                month = month - 1;
                queryData.put("startDate", year + "-01-01");
                if ((month + "").length() < 2) {
                    queryData.put("endDate", year + "-0" + month + "-31");
                } else {
                    queryData.put("endDate", year + "-" + month + "-31");
                }
            }
            if (queryData.get("deptCode") != null && !queryData.get("deptCode").equals("")) {
                String dept = queryData.get("deptCode") + "";
                if (dept.equals("BGTAEC00")) {
                    queryData.put("deptCode", "/BSTA/BGTA/BGTA00/BGTAEC00");
                } else if (dept.equals("BGTM00")) {
                    queryData.put("deptCode", "/BSTA/BGTA/BGTM/" + dept);
                } else {
                    queryData.put("deptCode", "/BSTA/BGTA/" + dept);
                }
            }
            TableDataInfo queryPage = businessPatentInfo.queryJsmm(queryData);
            return setPage(inInfo, queryPage);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-单位申请授权信息（单位）
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySqxxTjdw(EiInfo inInfo) {
        try {
            List<Map> list = new ArrayList<Map>();
            Map<String, Object> queryData = getQueryData(inInfo);
            System.out.println("————————————单位申请授权信息-主报表————————————");
            List<Map> listTjdw = businessPatentInfo.querySqxxTjdw(queryData);
            System.out.println("————————————单位申请授权信息-主报表-月度————————————");
            List<Map> listMonth = businessPatentInfo.querySqxxMonth(queryData);
            System.out.println("————————————单位申请授权信息-主报表-季度————————————");
            List<Map> listQuarter = businessPatentInfo.querySqxxQuarter(queryData);
//			String strYear = queryData.get("strYear")+"";
            for (Map row : listTjdw) {
                //算发明比例
                if (ObjectUtil.isNotEmpty(row.get("invention")) && ObjectUtil.isNotEmpty(row.get("tongji"))) {
                    BigDecimal invention = new BigDecimal(row.get("invention") + "");
                    BigDecimal tongji = new BigDecimal(row.get("tongji") + "");
                    BigDecimal proportion = BigDecimal.ZERO;
                    if (invention.compareTo(BigDecimal.ZERO) != 0 && tongji.compareTo(BigDecimal.ZERO) != 0) {
                        proportion = invention.divide(tongji, 1, BigDecimal.ROUND_HALF_UP);
                    }
                    row.put("proportion", proportion);
                } else {
                    row.put("proportion", "0.0");
                }
				
				/*HashMap param = new HashMap();
				param.put("year", strYear);
				param.put("deptCode", row.get("deptId"));
				List<Map> completeProportionlist = businessPatentInfo.querySqxxCompleteProportion(param);
				if(completeProportionlist.size()>0) {
					Map completeProportion = completeProportionlist.get(0);
					if(completeProportion.get("threePropInv")!=null&&!completeProportion.get("threePropInv").equals("")) {
						row.put("fmblz1", completeProportion.get("threePropInv"));
						BigDecimal threePropInv = new BigDecimal(completeProportion.get("threePropInv")+"");
						BigDecimal proportion = new BigDecimal(row.get("proportion")+"");
						row.put("wancheng1", proportion.multiply(new BigDecimal(100)).divide(threePropInv,2,BigDecimal.ROUND_HALF_DOWN));
					}
					if(completeProportion.get("threePropBast")!=null&&!completeProportion.get("threePropBast").equals("")) {
						row.put("fmblz2", completeProportion.get("threePropBast"));
						BigDecimal threePropBast = new BigDecimal(completeProportion.get("threePropBast")+"");
						BigDecimal proportion = new BigDecimal(row.get("proportion")+"");
						row.put("wancheng2", proportion.multiply(new BigDecimal(100)).divide(threePropBast,2,BigDecimal.ROUND_HALF_DOWN));
					}
				}*/
                list.add(row);
            }
            for (Map row : listMonth) {//月度申请、授权量
                //算发明比例
                if (ObjectUtil.isNotEmpty(row.get("invention")) && ObjectUtil.isNotEmpty(row.get("tongji"))) {
                    BigDecimal invention = new BigDecimal(row.get("invention") + "");
                    BigDecimal tongji = new BigDecimal(row.get("tongji") + "");
                    BigDecimal proportion = BigDecimal.ZERO;
                    if (invention.compareTo(BigDecimal.ZERO) != 0 && tongji.compareTo(BigDecimal.ZERO) != 0) {
                        proportion = invention.divide(tongji, 1, BigDecimal.ROUND_HALF_UP);
                    }
                    row.put("proportion", proportion);
                } else {
                    row.put("proportion", "0.0");
                }
                list.add(row);
            }
            for (Map row : listQuarter) {//季度申请、授权量
                list.add(row);
            }
            inInfo.set("data", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-单位申请授权信息-明细（发明人）
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySqxxTjFmr(EiInfo inInfo) {
        try {
            List<Map> list = new ArrayList<Map>();
            Map<String, Object> queryData = getQueryData(inInfo);
            System.out.println("————————————单位申请授权信息-月份发明人明细————————————");
            List<Map> listTjmx = businessPatentInfo.querySqxxTjFmr(queryData);
            System.out.println("————————————单位申请授权信息-月份发明人明细-合计————————————");
            List<Map> listTjmxhj = businessPatentInfo.querySqxxTjmxhj(queryData);
            for (Map row : listTjmx) {
                list.add(row);
            }
            for (Map row : listTjmxhj) {
                list.add(row);
            }
            inInfo.set("data", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-单位申请授权信息-明细（所有）
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySqxxTjmx(EiInfo inInfo) {
        try {
            List<Map> list = new ArrayList<Map>();
            Map<String, Object> queryData = getQueryData(inInfo);
            System.out.println("————————————单位申请授权信息-月份明细————————————");
            List<Map> listTjmx = businessPatentInfo.querySqxxTjmx(queryData);
            System.out.println("————————————单位申请授权信息-月份明细-合计————————————");
            List<Map> listTjmxhj = businessPatentInfo.querySqxxTjmxhj(queryData);
            for (Map row : listTjmx) {
                list.add(row);
            }
            for (Map row : listTjmxhj) {
                list.add(row);
            }
            inInfo.set("data", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-单位申请授权信息（部门）
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySqxxTjbm(EiInfo inInfo) {
        try {
            List<Map> list = new ArrayList<Map>();
            Map<String, Object> queryData = getQueryData(inInfo);
            String code = "";
            String name = "";
            if (queryData.get("deptCode") != null && !queryData.get("deptCode").equals("")) {
                String orgCode = queryData.get("deptCode") + "";
                if (orgCode.indexOf("/") > -1) {
                    orgCode = orgCode.substring(0, orgCode.indexOf("/"));
                }
                ADOrg org = OrgUtil.getOrgByOrgCode(orgCode);
                if (org != null) {
                    name = org.getOrgPathName();
                    String pathCode = org.getOrgPathCode();
                    queryData.put("deptCode", pathCode);
                } else {
                    name = "";
                    queryData.put("deptCode", "");
                }
                code = orgCode;
            } else {
                queryData.put("deptCode", "999999");
            }
            System.out.println("————————————单位申请授权信息（部门）-查询明细————————————");
            List<Map> listTjbm = businessPatentInfo.querySqxxTjbm(queryData);
            System.out.println("————————————单位申请授权信息（部门）-月份明细————————————");
            List<Map> listMonth = businessPatentInfo.querySqxxMonth(queryData);
            System.out.println("————————————单位申请授权信息（部门）-季度明细————————————");
            List<Map> listQuarter = businessPatentInfo.querySqxxQuarter(queryData);
            for (Map row : listTjbm) {
                if (ObjectUtil.isNotEmpty(row.get("deptId"))) {//处理部门获取全路径名称
                    String deptId = row.get("deptId") + "";
                    if (deptId.indexOf("/") > -1) {
                        deptId = deptId.substring(0, deptId.indexOf("/"));
                    }
                    ADOrg org = OrgUtil.getOrgByOrgCode(deptId);
                    if (org != null) {
                        row.put("deptName", org.getOrgPathName());
                    } else {
                        row.put("deptName", row.get("depName"));
                    }
                } else {//挂在单位上的取本单位名称，deptId为空
                    row.put("deptName", name);
                }

                if (ObjectUtil.isNotEmpty(row.get("invention")) && ObjectUtil.isNotEmpty(row.get("tongji"))) {
                    BigDecimal invention = new BigDecimal(row.get("invention") + "");
                    BigDecimal tongji = new BigDecimal(row.get("tongji") + "");
                    BigDecimal proportion = BigDecimal.ZERO;
                    if (invention.compareTo(BigDecimal.ZERO) != 0 && tongji.compareTo(BigDecimal.ZERO) != 0) {
                        proportion = invention.divide(tongji, 1, BigDecimal.ROUND_HALF_UP);
                    }
                    row.put("proportion", proportion);
                } else {
                    row.put("proportion", "0.0");
                }
                list.add(row);
            }
            for (Map row : listMonth) {//月度申请、授权量
                //算发明比例
                if (ObjectUtil.isNotEmpty(row.get("invention")) && ObjectUtil.isNotEmpty(row.get("tongji"))) {
                    BigDecimal invention = new BigDecimal(row.get("invention") + "");
                    BigDecimal tongji = new BigDecimal(row.get("tongji") + "");
                    BigDecimal proportion = BigDecimal.ZERO;
                    if (invention.compareTo(BigDecimal.ZERO) != 0 && tongji.compareTo(BigDecimal.ZERO) != 0) {
                        proportion = invention.divide(tongji, 1, BigDecimal.ROUND_HALF_UP);
                    }
                    row.put("proportion", proportion);
                } else {
                    row.put("proportion", "0.0");
                }
                list.add(row);
            }
            for (Map row : listQuarter) {//季度申请、授权量
                list.add(row);
            }
            inInfo.set("code", code);
            inInfo.set("data", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-单位放弃信息-专利数
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySqxxFqdw(EiInfo inInfo) {
        try {
            List<Map> list = new ArrayList<Map>();
            Map<String, Object> queryData = getQueryData(inInfo);
            List<Map> listFqdw = businessPatentInfo.querySqxxFqdw(queryData);
            List<Map> listHj = businessPatentInfo.querySqxxFqhj(queryData);
            for (Map row : listFqdw) {
                list.add(row);
            }
            for (Map row : listHj) {
                list.add(row);
            }
            inInfo.set("data", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计放弃-单位放弃信息-专利数
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySqxxFqbm(EiInfo inInfo) {
        try {
            List<Map> list = new ArrayList<Map>();
            Map<String, Object> queryData = getQueryData(inInfo);
            List<Map> listFqbm = businessPatentInfo.querySqxxFqbm(queryData);
            List<Map> listHj = businessPatentInfo.querySqxxFqhj(queryData);
            for (Map row : listFqbm) {
                list.add(row);
            }
            for (Map row : listHj) {
                list.add(row);
            }
            inInfo.set("data", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计放弃-单位放弃信息-明细（所有）
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySqxxFqmx(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<Map> list = businessPatentInfo.querySqxxFqmx(queryData);
            inInfo.set("data", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-单位申请授权信息（金苹果）
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySqxxJpg(EiInfo inInfo) {
        try {
            List<Map> list = new ArrayList<Map>();
            Map<String, Object> queryData = getQueryData(inInfo);
            List<Map> listJpg = businessPatentInfo.querySqxxJpg(queryData);
            List<Map> listMonth = businessPatentInfo.querySqxxJpgMonth(queryData);
            List<Map> listQuarter = businessPatentInfo.querySqxxJpgQuarter(queryData);
            for (Map row : listJpg) {
                String fzrName = row.get("fzrName") + "";
                String fzr = row.get("fzr") + "";
                if (ObjectUtil.isNotEmpty(fzrName) && ObjectUtil.isNotEmpty(fzr)) {
                    fzr = fzr.replace(",", ",-");
                    String[] splitName = fzrName.split(",");
                    String[] splitCode = fzr.split(",");
                    if (splitName.length == splitCode.length) {
                        String fmr = "";
                        for (int j = 0; j < splitName.length; j++) {
                            if (j + 1 == splitName.length) {
                                fmr = fmr + splitName[j] + splitCode[j];
                            } else {
                                fmr = fmr + splitName[j] + splitCode[j] + ",";
                            }
                        }
                        row.put("fzrName", fmr.replace("-", ""));
                    }
                }
                list.add(row);
            }
            for (Map row : listMonth) {
                list.add(row);
            }
            for (Map row : listQuarter) {
                list.add(row);
            }
            inInfo.set("data", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-单位申请授权信息（金苹果子项项目信息）
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySqxxJpgmx(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<Map> list = businessPatentInfo.querySqxxJpgmx(queryData);
            inInfo.set("data", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 统计-单位申请授权信息（金苹果专利信息）
     *
     * @param inInfo
     * @return
     */
    public EiInfo querySqxxJpgzlmx(EiInfo inInfo) {
        try {
            List<Map> list = new ArrayList<Map>();
            Map<String, Object> queryData = getQueryData(inInfo);
            List<Map> listJpgzlmx = businessPatentInfo.querySqxxJpgzlmx(queryData);
            List<Map> listJpgzlhj = businessPatentInfo.querySqxxJpgzlhj(queryData);
            for (Map row : listJpgzlmx) {
                list.add(row);
            }
            for (Map row : listJpgzlhj) {
                list.add(row);
            }
            inInfo.set("data", list);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 保存-授权通知录入保存
     *
     * @param inInfo
     * @return
     */
    @Transactional
    public EiInfo doSavePatent(EiInfo inInfo) {
        String operator = UserSession.getLoginName();
        TkizlPatentInfo patentInfo = (TkizlPatentInfo) inInfo.get("kizlPatentInfo");
        TkizlApplyBaseinfoEx beanEx = (TkizlApplyBaseinfoEx) inInfo.get("kizlApplyBaseinfoEx");
        businessPatentInfo.update(operator, patentInfo);
        if (ObjectUtil.isNotEmpty(beanEx.getTaskId()) && ObjectUtil.isNotEmpty(beanEx.getComment())) {
            SWorkFlowUtil.updateComment(beanEx.getTaskId(), beanEx.getComment());//暂存流程意见
        }
        if (ObjectUtil.isNotEmpty(beanEx.getField1())) {
            SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(beanEx.getApplyId(), beanEx.getField1(), "KIZL", "KIZL_ApplyBaseinfo", "xg", null);
        }
        //授权证书
        if (ObjectUtil.isNotEmpty(beanEx.getSqzsId())) {
            SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(beanEx.getApplyId(), beanEx.getSqzsId(), "KIZL", "KIZL_ApplyBaseinfo", "authCert", null);
        }
        //专利附件-专利申请单-授权相关文件
        if (ObjectUtil.isNotEmpty(beanEx.getSqwjId())) {
            SAttachmentUtil.addAttachmentMapsBySourceGuid(beanEx.getApplyId(), beanEx.getSqwjId(), KIZLConstants.sqwj);
        }
        inInfo.setCell("i", 0, "applyId", beanEx.getApplyId());
        inInfo.setMsg("保存成功！");
        return inInfo;
    }

    /**
     * 保存-处理中保存
     *
     * @param inInfo
     * @return
     */
    @Transactional
    public EiInfo doSavePatentPlus(EiInfo inInfo) {
        String operator = UserSession.getLoginName();
        TkizlApplyBaseinfoEx beanEx = (TkizlApplyBaseinfoEx) inInfo.get("kizlApplyBaseinfoEx");
        if (ObjectUtil.isNotEmpty(beanEx.getTaskId()) && ObjectUtil.isNotEmpty(beanEx.getComment())) {
            SWorkFlowUtil.updateComment(beanEx.getTaskId(), beanEx.getComment());//暂存流程意见
        }
        if (ObjectUtil.isNotEmpty(beanEx.getAlist())) {
            //换算申请人所占系数
            String personxs = PatentUtil.getPersonxs(beanEx.getAlist());
            beanEx.setPersonxs(personxs);
        }
        businessApplyBaseinfo.update(operator, beanEx);
        //申请人
        businessApplySqr.saveData(operator, beanEx);
        TkizlPatentInfo patentBean = (TkizlPatentInfo) inInfo.get("kizlPatentInfo");
        businessPatentInfo.update(operator, patentBean);
        //专利附件-专利申请单-代理中-申请相关文件
        SAttachmentUtil.addAttachmentMapsBySourceGuid(beanEx.getApplyId(), beanEx.getXgwjId(), KIZLConstants.xgwj);
        //专利附件-专利申请单-代理中-共同申请协议
        SAttachmentUtil.addAttachmentMapsBySourceGuid(beanEx.getApplyId(), beanEx.getZscqId(), KIZLConstants.zscq);
        SAttachmentUtil.addAttachmentMapsBySourceGuid(beanEx.getApplyId(), beanEx.getZscqId(), KIZLConstants.gtsq);
        inInfo.setCell("i", 0, "applyId", beanEx.getApplyId());
        inInfo.setMsg("保存成功！");
        return inInfo;
    }

    public EiInfo dl_doSave(EiInfo inInfo) {
        String operator = UserSession.getLoginName();
        TkizlDlExcel bean = (TkizlDlExcel) inInfo.get("bean");
        try {
            String msg = this.businessPatentInfo.dlDoSave(operator, bean);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo dsqtz_doSave(EiInfo inInfo) {
        String operator = UserSession.getLoginName();
        TkizlDsqtzExcel bean = (TkizlDsqtzExcel) inInfo.get("bean");
        try {
            String msg = this.businessPatentInfo.dsqtzDoSave(operator, bean);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);

        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo dsqxx_doSave(EiInfo inInfo) {
        String operator = UserSession.getLoginName();
        TkizlDsqxxExcel bean = (TkizlDsqxxExcel) inInfo.get("bean");
        try {
            String msg = this.businessPatentInfo.dsqxxDoSave(operator, bean);
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);

        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo kizlSlxxSubmit(EiInfo inInfo) {
        TkizlDlExcel bean = (TkizlDlExcel) inInfo.get("bean");
        try {
            EiInfo retInfo = this.businessPatentInfo.kizlSlxxSubmit(bean);
            inInfo.setMsg(retInfo.getMsg());
            inInfo.setCell("i", 0, "fails", retInfo.getBlock("i").getRow(0).get("fails"));
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 流程提交
     *
     * @param inInfo
     * @return
     */
    public EiInfo submitWF(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();
            TkizlApplyBaseinfoEx beanEx = (TkizlApplyBaseinfoEx) inInfo.get("kizlApplyBaseinfoEx");
            TkizlPatentInfo patentBean = (TkizlPatentInfo) inInfo.get("kizlPatentInfo");
            if (StrUtil.isNotBlank(beanEx.getXgwjId())) {
                //专利附件-专利申请单-代理中-申请相关文件
                SAttachmentUtil.addAttachmentMapsBySourceGuid(beanEx.getApplyId(), beanEx.getXgwjId(), KIZLConstants.xgwj);
            }
            if (StrUtil.isNotBlank(beanEx.getGtsqId())) {
                //专利附件-专利申请单-代理中-共同申请协议
                SAttachmentUtil.addAttachmentMapsBySourceGuid(beanEx.getApplyId(), beanEx.getGtsqId(), KIZLConstants.gtsq);
            }
            if (StrUtil.isNotBlank(beanEx.getSqwjId())) {
                //专利附件-专利申请单-授权相关文件
                SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(beanEx.getApplyId(), beanEx.getSqwjId(), KIZLConstants.sqwj, null, null, null);
            }
            if (StrUtil.isNotBlank(beanEx.getSltzId())) {
                //专利附件-专利申请单-代理中-受理通知书
                SAttachmentUtil.addAttachmentMapsBySourceGuid(beanEx.getApplyId(), beanEx.getSltzId(), KIZLConstants.sltz);
            }
            if (StrUtil.isNotBlank(beanEx.getField2())) {
                SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(beanEx.getApplyId(), beanEx.getField2(), "KIZL", "KIZL_ApplyBaseinfo", "authCert", null);
            }
            String msg = businessPatentInfo.submitWF(operator, beanEx, patentBean);
            //专利申请所有提交时-更新记录专利退回否定历史表
            TerminationUtil.updateTermination(operator, beanEx.getApplyId());
            inInfo.setMsg(msg);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }

        return inInfo;
    }

    /**
     * 专利申请流程-批量提交流程  待交底、交底
     *
     * @param inInfo
     * @return
     */
    public EiInfo batchSubmitWFS(EiInfo inInfo) {
        TkizlPatentInfoEx beanEx = (TkizlPatentInfoEx) inInfo.get("kizlPatentInfoEx");
        String flag = (String) inInfo.get("flag");
        String msg = businessPatentInfo.batchSubmitWFS(beanEx, flag);
        EiInfo outInfo = new EiInfo();
        outInfo.setMsg(msg);
        return outInfo;
    }

    /**
     * 专利申请流程-批量否定  待交底、交底
     *
     * @param inInfo
     * @return
     */
    public EiInfo batchFd(EiInfo inInfo) {
        TkizlPatentInfoEx beanEx = (TkizlPatentInfoEx) inInfo.get("kizlPatentInfoEx");
        String flag = (String) inInfo.get("flag");
        String msg = businessPatentInfo.batchFd(beanEx, flag);
        EiInfo outInfo = new EiInfo();
        outInfo.setMsg(msg);
        return outInfo;
    }

    /**
     * 子流程-专利确认
     *
     * @param inInfo
     * @return
     */
    public EiInfo startQR(EiInfo inInfo) {
        String operator = UserSession.getLoginName();
        TkizlApplyBaseinfoEx beanEx = (TkizlApplyBaseinfoEx) inInfo.get("kizlApplyBaseinfoEx");
        TkizlPatentInfo patentBean = (TkizlPatentInfo) inInfo.get("kizlPatentInfo");
        String msg = "";
        if ("Manual6".equals(beanEx.getActivityCode())) {//接收
            msg = businessPatentInfo.startJSQR(operator, beanEx, patentBean);
        } else {
            msg = businessPatentInfo.startQR(operator, beanEx, patentBean);
        }
        EiInfo outInfo = new EiInfo();
        outInfo.setMsg(msg);
        return outInfo;
    }

    /**
     * 专利状态保存
     *
     * @param inInfo
     * @return
     */
    public EiInfo addFLZT(EiInfo inInfo) {
        String operator = UserSession.getLoginName();
        TkizlPatentInfoEx kizlPatentInfoEx = (TkizlPatentInfoEx) inInfo.get("kizlPatentInfoEx");
        String patentId = businessPatentInfo.saveFLZT(operator, kizlPatentInfoEx);
        EiInfo outInfo = new EiInfo();
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setCell("i", 0, "patentId", patentId);
        outInfo.setMsg("保存成功！");
        return outInfo;
    }

    /**
     * 专利状态修改
     *
     * @param inInfo
     * @return
     */
    public EiInfo saveFLZT(EiInfo inInfo) {
        String operator = UserSession.getLoginName();
        TkizlPatentInfoEx kizlPatentInfoEx = (TkizlPatentInfoEx) inInfo.get("kizlPatentInfoEx");
        TkizlPatentInfo bean = businessPatentInfo.load(kizlPatentInfoEx.getPatentId());
        kizlPatentInfoEx.setZzbj(bean.getFlzt());//状态管理标记  保存原法律状态
        String patentId = businessPatentInfo.saveFLZT(operator, kizlPatentInfoEx);
        EiInfo outInfo = new EiInfo();
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setCell("i", 0, "patentId", patentId);
        outInfo.setMsg("提交成功！");
        return outInfo;
    }

    /**
     * 恢复专利
     *
     * @param inInfo
     * @return
     */
    public EiInfo recoverFLZT(EiInfo inInfo) {
        String operator = UserSession.getLoginName();
        TkizlPatentInfoEx kizlPatentInfoEx = (TkizlPatentInfoEx) inInfo.get("kizlPatentInfoEx");
        TkizlPatentInfo bean = businessPatentInfo.load(kizlPatentInfoEx.getPatentId());
        kizlPatentInfoEx.setFlzt(bean.getZzbj());//恢复专利状态
        String patentId = businessPatentInfo.recoverFLZT(operator, kizlPatentInfoEx);
        EiInfo outInfo = new EiInfo();
        outInfo.setStatus(EiConstant.STATUS_SUCCESS);
        outInfo.setCell("i", 0, "patentId", patentId);
        outInfo.setMsg("恢复成功！");
        return outInfo;
    }

    public EiInfo insert(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();

            Map row = inInfo.getBlock("i").getRow(0);
            TkizlPatentInfo bean = BeanUtil.toBean(row, TkizlPatentInfo.class);
            businessPatentInfo.insert(operator, bean);

            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo update(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();

            Map row = inInfo.getBlock("i").getRow(0);
            TkizlPatentInfo bean = BeanUtil.toBean(row, TkizlPatentInfo.class);
            businessPatentInfo.update(operator, bean);

            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo delete(EiInfo inInfo) {
        try {
            String operator = UserSession.getLoginName();

            String ids = (String) inInfo.getAttr().get("ids");
            String[] arr = ids.split(",");
            for (String id : arr) {
                businessPatentInfo.delete(operator, id);
            }

            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo queryPatentAttach(EiInfo inInfo) {
        try {
            String patentId = (String) inInfo.get("patentId");
            TkizlPatentAttachment patentAttachment = new TkizlPatentAttachment();
            if (StrUtil.isNotBlank(patentId)) {
                Map queryParam = new HashMap();
                queryParam.put("patentId", patentId);
                List<TkizlPatentInfo> list = businessPatentInfo.queryList(queryParam);
                if (ObjectUtil.isNotNull(list) && list.size() > 0) {
                    TkizlPatentInfo patentInfo = list.get(0);
                    patentAttachment.setPatentId(patentId);
                    patentAttachment.setJsbh(patentInfo.getJsbh());
                    patentAttachment.setBgbh(patentInfo.getBgbh());
                    patentAttachment.setPatentNo(patentInfo.getPatentNo());

                }
                List<AttachmentMap> attachmentMaps = SAttachmentUtil.getAttachmentBySourceId(patentId, "KIZL");
                if (ObjectUtil.isNotNull(attachmentMaps) && attachmentMaps.size() > 0) {
                    String attachmentIds = attachmentMaps.stream()
                            .map(attachmentMap -> attachmentMap.getAttachmentId())
                            .collect(Collectors.joining(","));
                    patentAttachment.setAttachmentIds(attachmentIds);
                }
                inInfo.set("data", patentAttachment);
                inInfo.setMsg("success");
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            } else {
                inInfo.setMsg("未查询到专利信息");
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
            }

        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public List<TkizlPatentInfo> queryList(Map map) {
        List<TkizlPatentInfo> tkizlPatentInfos = this.businessPatentInfo.queryList(map);
        return tkizlPatentInfos;
    }

    public void test(TkizlPatentInfo bean) {
//        ADOrg orgByOrgCode = OrgUtil.getMainOrgByUserCode(bean.getLxrCode());
//        bean.setFirstDeptCode(orgByOrgCode.getOrgCode());
//        bean.setFirstDeptName(orgByOrgCode.getOrgPathName());
//        bean.setFirstDeptPath(orgByOrgCode.getOrgPathCode());
//        bean.setSerialNum(SequenceGenerator.getNextSequence("KIZL_SQ_SERIANO"));
//        bean.setJsbh(SerialNumUtil.getJsbh(bean.getFirstDeptCode(), null));
//        bean.setBgbh(SerialNumUtil.getBgbh(bean.getFirstDeptCode()));
//        bean.setSlrq(DateUtil.format(DateUtil.parse(bean.getSlrq()), "yyyy-MM-dd"));
////        bean.setSqrq(DateUtil.format(DateUtil.parse(bean.getSqrq()),"yyyy-MM-dd"));
//        TkizlApplyBaseinfo tkizlApplyBaseinfo = BeanUtil.toBean(bean, TkizlApplyBaseinfo.class);
//        tkizlApplyBaseinfo.setApplyDate(DateUtil.format(DateUtil.parse(bean.getSlrq()), "yyyy-MM-dd"));
//        this.businessApplyBaseinfo.insert("admin", tkizlApplyBaseinfo);
        Map<String, Object> resMap = new HashMap<>();
        resMap.put("legalId", bean.getPatentNo());
//        List<TkizlApplySqr> tkizlApplySqrs = this.businessApplySqr.queryList(resMap);
//        for (TkizlApplySqr sqr : tkizlApplySqrs) {
//            sqr.setApplyId(bean.getApplyId());
//            this.businessApplySqr.update("admin", sqr);
//        }
        List<TkizlApplyRyxx> tkizlApplyRyxxes = this.businessApplyRyxxPatent.queryList(resMap);
        List<String> fmr = new ArrayList<>();
        for (TkizlApplyRyxx ryxx : tkizlApplyRyxxes) {
//            ryxx.setApplyId(bean.getApplyId());
//            ryxx.setPatentId(bean.getPatentId());
            ADOrg orgByOrgCode = OrgUtil.getMainOrgByUserCode(ryxx.getEmpId());
            if (ObjectUtil.isNotEmpty(orgByOrgCode)) {
                ryxx.setCodePath(orgByOrgCode.getOrgPathCode());
                ryxx.setDeptCode(orgByOrgCode.getOrgCode());
                ryxx.setDeptName(orgByOrgCode.getOrgPathName());
                this.businessApplyRyxxPatent.update("admin", ryxx);
            }
//            fmr.add(ryxx.getEmpName());
        }
//        bean.setFml(fmr.stream().collect(Collectors.joining(",")));
//        bean.setFlowStatus("active");

//        bean.setQs(tkizlApplySqrs.size() + "");
        if (StrUtil.equals(bean.getFlzt(),"10")) {
            bean.setZlh("ZL" + bean.getPatentNo());
        }
        this.businessPatentInfo.update("admin", bean);
//        TkizlApplyBaseinfo load = this.businessApplyBaseinfo.load(bean.getApplyId());
//        load.setFirstDeptCode(bean.getFirstDeptCode());
//        load.setFirstDeptName(bean.getFirstDeptName());
//        load.setFirstDeptPath(bean.getFirstDeptPath());
//        load.setOwnership(tkizlApplySqrs.size() + "");
//        this.businessApplyBaseinfo.update("admin",load);


//        try {
//            String operator = "D78233";
//            Map<String, Object> variable = new HashMap<>();
//            variable.put("orgParamter", bean.getFirstDeptCode());
//            variable.put("isBmld", 0);
//            String msg = SWorkFlowUtil.startProcessToActivity(operator, bean.getApplyId(), bean.getApplyName(), "KIZL", KIZLConstants.KIZL_ApplyBaseinfo, "Manual15", "", "D78233", variable, "Transition2");
//            System.out.println(msg);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }

    }
}
