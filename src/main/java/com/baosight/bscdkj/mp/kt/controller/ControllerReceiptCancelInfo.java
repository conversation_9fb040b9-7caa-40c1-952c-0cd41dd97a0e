package com.baosight.bscdkj.mp.kt.controller;

import com.baosight.bscdkj.utils.excel.ExcelUtil;
import com.baosight.bscdkj.utils.iplat.ServiceUtil;
import com.baosight.bscdkj.common.controller.BaseController;
import com.baosight.bscdkj.common.domain.AjaxResult;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.mp.domain.TmpktReceiptCancelInfo;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 26.0_04核销信息Controller
 * 
 * <AUTHOR>
 * @date 2021-12-28
 */
@Controller
@RequestMapping("/mpkt/receiptCancelInfo")
public class ControllerReceiptCancelInfo extends BaseController{
    private String prefix = "/mpkt/receiptCancelInfo";
    
    @GetMapping()
    public String receiptCancelInfo(){
        return prefix + "/receiptCancelInfo";
    }


     /**
     * 查询26.0_04核销信息列表
     */
  	@PostMapping("/list")
	@ResponseBody
	public TableDataInfo list(@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceMPKTReceiptCancelInfo", "page");
		return getDataTable(query);
	}

    @PostMapping("/queryList")
    @ResponseBody
    public TableDataInfo queryList(@RequestParam("receiptId") String receiptId) {
        Map<String, Object> map = new HashMap<>();
        map.put("receiptId",receiptId);
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceMPKTReceiptCancelInfo", "page");
        return getDataTable(query);
    }

    /**
     * 新增26.0_04核销信息
     */
    @GetMapping("/add")
    public String add(){
        return prefix + "/add";
    }


    /**
     * 新增保存26.0_04核销信息
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated TmpktReceiptCancelInfo receiptCancelInfo) {
    

       EiInfo eiInfo = getEiInfo("i", receiptCancelInfo);
       return  ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceMPKTReceiptCancelInfo", "insert");
    }

    /**
     * 修改26.0_04核销信息
     */
    @GetMapping("/edit/{cancelId}")
    public String edit(@PathVariable("cancelId") String cancelId, ModelMap mmap) {
       	EiInfo eiInfo = new EiInfo();
		eiInfo.set("cancelId", cancelId);

        mmap.put("receiptCancelInfo", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceMPKTReceiptCancelInfo", "load","receiptCancelInfo"));
        return prefix + "/edit";
    }

    /**
     * 修改保存26.0_04核销信息
     */
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated TmpktReceiptCancelInfo receiptCancelInfo){
        EiInfo eiInfo = getEiInfo("i", receiptCancelInfo);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceMPKTReceiptCancelInfo", "update");
    }
    
    /**
     * 删除26.0_04核销信息
     */
	@PostMapping("/remove")
	@ResponseBody
	public AjaxResult remove(String ids) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("cancelId", ids);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceMPKTReceiptCancelInfo", "remove");
	}



    /**
     * 导出26.0_04核销信息列表
     */
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export() {
		EiInfo eiInfo = new EiInfo();
		List<TmpktReceiptCancelInfo> list = (List<TmpktReceiptCancelInfo>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceMPKTReceiptCancelInfo", "query","list");
		ExcelUtil<TmpktReceiptCancelInfo> util = new ExcelUtil<>(TmpktReceiptCancelInfo.class);
		util.setSheet("26.0_04核销信息");
		return util.exportExcel(list);
    }
}
