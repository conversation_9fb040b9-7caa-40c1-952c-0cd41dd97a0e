package com.baosight.bscdkj.mp.kt.service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import com.baosight.bscdkj.mp.di.dto.XBus8PBCS1;
import com.baosight.bscdkj.mp.di.dto.XBus8PBCS2;
import com.baosight.bscdkj.mp.di.dto.XBus8PBCS3;
import com.baosight.bscdkj.mp.di.dto.XBus8PBCS9;
import com.baosight.bscdkj.utils.BizIdUtil;
import com.baosight.bscdkj.zz.kj.business.BusinessFrame;
import com.baosight.bscdkj.zz.lx.business.BusinessMain;
import com.baosight.bscdkj.zz.zc.business.BusinessDict;
import com.baosight.bscdkj.zz.zc.domain.TzzzcDictEx;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.baosight.bscdkj.common.constant.ZZZCConstants;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.mp.domain.TmpksClient;
import com.baosight.bscdkj.common.mp.domain.TmpksClientBank;
import com.baosight.bscdkj.common.mp.domain.TmpktKbInfo;
import com.baosight.bscdkj.common.mp.domain.TmpktKbInfoDetail;
import com.baosight.bscdkj.common.service.PageService;
import com.baosight.bscdkj.common.zz.domain.TzzkjFrame;
import com.baosight.bscdkj.common.zz.domain.TzzlxMain;
import com.baosight.bscdkj.mp.kt.business.BusinessKbInfo;
import com.baosight.bscdkj.mp.kt.constans.MPKTConstants;
import com.baosight.bscdkj.mp.kt.domin.TmpktKbInfoEx;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;

/**
 * 26.0_01开票信息Service接口
 *
 * <AUTHOR>
 * @date 2021-12-28
 */
@Service
public class ServiceMPKTKbInfo extends PageService {
    @Autowired
    private BusinessKbInfo businessKbInfo;
    @Autowired
    private BusinessMain businessMain;
    @Autowired
    private BusinessDict businessDict;
    @Autowired
    private BusinessFrame businessFrame;

    Logger logger = LoggerFactory.getLogger(this.getClass());

    ServiceMPKTXBus serviceMPKTXBus = new ServiceMPKTXBus();

    /**
     * 初始化
     */
    @Override
    public EiInfo initLoad(EiInfo inInfo) {
        return inInfo;
    }

    @Override
    public EiInfo query(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            List<TmpktKbInfo> queryList = businessKbInfo.queryList(queryData);
            inInfo.set("list", queryList);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo page(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            if(null==queryData.get("displayOrder")) {
            	queryData.put("displayOrder", "UPDATE_DATE DESC");
    		}
            queryData.put("delStatus","0");
            TableDataInfo queryPage = businessKbInfo.quertPageS(queryData);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 查询待核销的开票发票信息
     * @param inInfo
     * @return
     */
    public EiInfo queryKbInfo(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData(inInfo);
            // 获取数据
            TableDataInfo tdi = businessKbInfo.queryKbInfo(UserSession.getLoginName(),queryData);
            setPage ( inInfo, tdi );
            inInfo.setMsg ( "success" );
            inInfo.setStatus ( EiConstant.STATUS_SUCCESS );
        } catch (Exception e) {
            e.printStackTrace ();
            inInfo.setStatus ( EiConstant.STATUS_FAILURE );
            inInfo.setMsg ( e.getMessage () );
            if (null != e.getCause ()) {
                logger.error ( e.getMessage (), e );
                inInfo.setDetailMsg ( e.getCause ().getMessage () );
            } else {
                logger.error ( e.getMessage () );
            }
        }
        return inInfo;
    }
    @Override
    public EiInfo update(EiInfo inInfo) {
        try {
            Map row = inInfo.getBlock("i").getRow(0);
            TmpktKbInfo bean = BeanUtil.toBean(row, TmpktKbInfo.class);
            businessKbInfo.update(UserSession.getLoginName(), bean);
            inInfo.setMsg("修改成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    @Override
    public EiInfo insert(EiInfo inInfo) {
        try {
            Map row = inInfo.getBlock("i").getRow(0);
            TmpktKbInfo bean = BeanUtil.toBean(row, TmpktKbInfo.class);
            businessKbInfo.insert(UserSession.getLoginName(), bean);
            inInfo.setMsg("添加成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo load(EiInfo inInfo) {
        try {
            String kbinfoId = (String) inInfo.get("kbinfoId");
            TmpktKbInfo query = businessKbInfo.query(kbinfoId);
            inInfo.set("kbInfo", query);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String kbinfoId = (String) inInfo.get("kbinfoId");
            if (StrUtil.isNotBlank(kbinfoId)) {
                for (String id : kbinfoId.split(",")) {
                    businessKbInfo.delete(loginName, id);
                }
            }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;

    }

    //根据项目编号查看项目详细信息
    public EiInfo querybkinfo(EiInfo inInfo) {
        try {
            TmpktKbInfoEx tmpktKbInfoEx = (TmpktKbInfoEx) inInfo.get("tmpktKbInfoEx");
            String kbinfoXmbh = tmpktKbInfoEx.getKbinfoXmbh();
            EiInfo eiInfo = new EiInfo();
            eiInfo.set("projectNum", kbinfoXmbh);
            eiInfo.set(EiConstant.serviceId, "S_ZZ_LX_001");
            EiInfo outInfo = XServiceManager.call(eiInfo);
            Map map = new HashMap();
            boolean isLx = true;
            if (EiConstant.STATUS_SUCCESS == outInfo.getStatus()) {
                map = (Map<String, Object>) outInfo.get("result");
            }
            if(map.isEmpty()){
                eiInfo.set(EiConstant.serviceId, "S_ZZ_KJ_003");
                EiInfo einfo = XServiceManager.call(eiInfo);
                if (EiConstant.STATUS_SUCCESS == einfo.getStatus()) {
                    map = (Map<String, Object>) einfo.get("result");
                    isLx = false;
                }
                if(map.isEmpty()){
                   /* map.put("kbinfoCustomid","");//客商代码
                    map.put("kbinfoCustomUnitname","");//客户名称
                    map.put("kbinfoCustomAccount","");//账号
                    map.put("kbinfoCustomTax","");//税号
                    map.put("kbinfoCustomUnitphone","");//电话*/
                    map.put("kbinfoAccountBook","");//账套
                    map.put("kbinfoDutyCenter","");//责任中心
                    map.put("kbinfoXmmc","");//项目名称
                    map.put("kbinfoHtbh","");//合同号
                }
            }

            if(isLx){
                //立项
                map.put("kbinfoXmmc",map.get("projectName"));//项目名称
                map.put("kbinfoHtbh",map.get("projectHtNum"));//合同号
            }else {
                //框架协议
                map.put("kbinfoXmmc",map.get("frameName"));//项目名称
                String kbinfoHtbh = kbinfoXmbh.substring(2);
                kbinfoHtbh = kbinfoHtbh.replaceAll("-","");
                map.put("kbinfoHtbh",kbinfoHtbh);//合同号
            }


           /* Map map = new HashMap();
            map.put("kbinfoXmmc","项目名称");//项目名称
            map.put("kbinfoHtbh","合同号");//合同号
            map.put("kbinfoCustomid","客商代码");//客商代码
            map.put("kbinfoCustomUnitname","客户名称");//客户名称
            map.put("kbinfoCustomAccount","账号");//账号
            map.put("kbinfoCustomTax","税号");//税号
            map.put("kbinfoCustomUnitphone","电话");//电话
            map.put("kbinfoAccountBook","账套");//账套
            map.put("kbinfoDutyCenter","责任中心");//责任中心
            map.put("kbinfoXmmc",map.get("projectName"));//项目名称
            map.put("kbinfoHtbh",map.get("projectHtNum"));//合同号*/
            inInfo.set("map",map);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;

    }

    //根据项目编号查看项目详细信息
    public EiInfo querybkinfoTwo(EiInfo inInfo) {
        try {
            TmpktKbInfoEx tmpktKbInfoEx = (TmpktKbInfoEx) inInfo.get("tmpktKbInfoEx");
            String kbinfoXmbh = tmpktKbInfoEx.getKbinfoXmbh();
            String kbinfoCustomid = "";
            boolean isLx = true;

            TzzlxMain query = businessMain.queryMainByCode(kbinfoXmbh);
            Map mainMap = new HashMap();
            if(query!=null){
                mainMap=query.toMap();
                //根据推广方查询账套信息
                Map tgfMap=new HashMap();
                tgfMap.put("dictCode", query.getTgfDwdeptCode());//推广方单位代码
                tgfMap.put("parentDictId", ZZZCConstants.MAINTENANCE_CATEGORY_FINANCIAL_DATA);
                TzzzcDictEx tgfDictEx=businessDict.queryDict(tgfMap);
                if(tgfDictEx!=null){
                    mainMap.put("kbinfoAccountBook",tgfDictEx.getExtra2());//账套
                    mainMap.put("kbinfoDutyCenter",tgfDictEx.getExtra3());//责任中心
                }

                //根据受让方查询客商信息
                Map srfMap=new HashMap();
                srfMap.put("dictCode", query.getSrfDwdeptCode());//受让方单位代码
                srfMap.put("parentDictId", ZZZCConstants.MAINTENANCE_CATEGORY_FINANCIAL_DATA);
                TzzzcDictEx srfDictEx = businessDict.queryDict(srfMap);
                if(srfDictEx!=null){
                    kbinfoCustomid = srfDictEx.getExtra1();
                    mainMap.put("kbinfoCustomid",srfDictEx.getExtra1());//客商代码
                }
            }else{
                TzzkjFrame queryFrame = businessFrame.queryMainByCode(kbinfoXmbh);
                if(queryFrame!=null){
                    mainMap=queryFrame.toMap();
                    //根据推广方查询账套信息
                    Map dictMap=new HashMap();
                    dictMap.put("dictCode", queryFrame.getFrameTgfCode());//推广方单位代码
                    dictMap.put("parentDictId", ZZZCConstants.MAINTENANCE_CATEGORY_FINANCIAL_DATA);
                    TzzzcDictEx dictEx=businessDict.queryDict(dictMap);
                    if(dictEx!=null){
                        mainMap.put("kbinfoAccountBook",dictEx.getExtra2());//账套
                        mainMap.put("kbinfoDutyCenter",dictEx.getExtra3());//责任中心
                    }

                    //根据受让方查询客商信息
                    Map srfMap=new HashMap();
                    srfMap.put("dictCode", queryFrame.getFrameSrfCode());//受让方单位代码
                    srfMap.put("parentDictId", ZZZCConstants.MAINTENANCE_CATEGORY_FINANCIAL_DATA);
                    TzzzcDictEx srfDictEx = businessDict.queryDict(srfMap);
                    if(srfDictEx!=null){
                        kbinfoCustomid = srfDictEx.getExtra1();
                        mainMap.put("kbinfoCustomid",srfDictEx.getExtra1());//客商代码
                    }
                    isLx = false;
                }

                if(mainMap.isEmpty()){
                   /*
                    map.put("kbinfoCustomUnitphone","");//电话*/
                    mainMap.put("kbinfoCustomid","");//客商代码
                    mainMap.put("kbinfoAccountBook","");//账套
                    mainMap.put("kbinfoDutyCenter","");//责任中心
                    mainMap.put("kbinfoXmmc","");//项目名称
                    mainMap.put("kbinfoHtbh","");//合同号
                }
            }

            if(kbinfoCustomid != null){
                TmpksClient tmpksClient = businessFrame.queryCLIENT(kbinfoCustomid);
                if(tmpksClient!=null){
                    mainMap.put("kbinfoCustomUnitname",tmpksClient.getChineseUserName());//客户名称
                    mainMap.put("kbinfoCustomTax",tmpksClient.getTaxNum());//税号
                    mainMap.put("kbinfoCustomAddress",tmpksClient.getChineseAddress());//地址
                }else{
                    mainMap.put("kbinfoCustomUnitname","");//客户名称
                    mainMap.put("kbinfoCustomTax","");//税号
                    mainMap.put("kbinfoCustomAddress","");//地址
                }

                TmpksClientBank tmpksClientBank = businessFrame.queryCLIENTBANK(kbinfoCustomid);
                if(tmpksClientBank!=null){
                    mainMap.put("kbinfoCustomAccount",tmpksClientBank.getAccountNum());//账号
                    mainMap.put("kbinfoCustomBank",tmpksClientBank.getBankBranchName());//开户银行
                }else{
                    mainMap.put("kbinfoCustomAccount","");//账号
                    mainMap.put("kbinfoCustomBank","");//开户银行
                }
            }else{
                mainMap.put("kbinfoCustomUnitname","");//客户名称
                mainMap.put("kbinfoCustomTax","");//税号
                mainMap.put("kbinfoCustomAddress","");//地址
                mainMap.put("kbinfoCustomAccount","");//账号
                mainMap.put("kbinfoCustomBank","");//开户银行
            }

            if(isLx){
                //立项
                mainMap.put("kbinfoXmmc",mainMap.get("projectName"));//项目名称
                mainMap.put("kbinfoHtbh",mainMap.get("projectHtNum"));//合同号
            }else {
                //框架协议
                mainMap.put("kbinfoXmmc",mainMap.get("frameName"));//项目名称
                String kbinfoHtbh = kbinfoXmbh.substring(2);
                kbinfoHtbh = kbinfoHtbh.replaceAll("-","");
                mainMap.put("kbinfoHtbh",kbinfoHtbh);//合同号
            }

            inInfo.set("map",mainMap);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;

    }

    //根据状态查询技术贸易或推广移植数据
    public EiInfo bkinfoAdd(EiInfo inInfo) {
        try {
            String kbSource = (String) inInfo.get("kbSource");
            TmpktKbInfo tmpktKbInfo = new TmpktKbInfo();
            if(MPKTConstants.kbSource_KTTG.equals(kbSource)){
                //技术推广
                tmpktKbInfo.setKbSource("技术推广");
            } else if(MPKTConstants.kbSource_KTMY.equals(kbSource)) {
            	//技术贸易
                tmpktKbInfo.setKbSource("技术贸易");
            } else{
                //技术推广
                tmpktKbInfo.setKbSource("推广移植");
            }
            //单据类型
            tmpktKbInfo.setKbinfoType("S01001");
            //币种
            tmpktKbInfo.setAccountCurrency("CNY");
            //产品副代码
            tmpktKbInfo.setKindCode("B2020");
            //会计期默认当天
            Date date = new Date();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            String AccountDate = formatter.format(date);
            tmpktKbInfo.setAccountDate(AccountDate);
            //结算方式默认赊销
            tmpktKbInfo.setAccountNethod(MPKTConstants.AccountNethod_C);
            //发票种类 默认增值税
            tmpktKbInfo.setInvoiceType(MPKTConstants.InvoiceType_11);

            TmpktKbInfoEx tmpktKbInfoEx = new TmpktKbInfoEx();
            BeanUtil.copyProperties(tmpktKbInfo, tmpktKbInfoEx);
            //制单人
            tmpktKbInfoEx.setSettleId("185015");

            inInfo.set("tmpktKbInfoEx", tmpktKbInfoEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;

    }
    
    //根据状态查询技术贸易或推广移植数据
    public EiInfo bkinfoAdd2(EiInfo inInfo) {
        try {
            String kbSource = (String) inInfo.get("kbSource");
            String kbinfoXmbh = (String) inInfo.get("kbinfoXmbh");
            String extra5 = (String) inInfo.get("extra5");
            if(StringUtils.isBlank(kbinfoXmbh)||StringUtils.isBlank(extra5)) {
            	throw new PlatException("项目编号不能为空");
            }
            Map<String,Object> param = new HashMap<String, Object>();
			param.put("kbinfoXmbh", kbinfoXmbh);
			param.put("extra5", extra5);
			List<TmpktKbInfo> kpList = businessKbInfo.queryList(param);
			if(null!=kpList&&kpList.size()>0) {
				throw new PlatException("已开票，请到开票信息里查看相关信息。");
			}
            TmpktKbInfo tmpktKbInfo = new TmpktKbInfo();
            tmpktKbInfo.setKbinfoXmbh(kbinfoXmbh);
            tmpktKbInfo.setExtra5(extra5);            
            if(MPKTConstants.kbSource_KTTG.equals(kbSource)){
                //技术推广
                tmpktKbInfo.setKbSource("技术推广");
            } else if(MPKTConstants.kbSource_KTMY.equals(kbSource)) {
            	//技术贸易
                tmpktKbInfo.setKbSource("技术贸易");
            } else{
                //技术推广
                tmpktKbInfo.setKbSource("推广移植");
            }
            //单据类型
            tmpktKbInfo.setKbinfoType("S01001");
            //币种
            tmpktKbInfo.setAccountCurrency("CNY");
            //产品副代码
            tmpktKbInfo.setKindCode("B2020");
            //会计期默认当天
            Date date = new Date();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            String AccountDate = formatter.format(date);
            tmpktKbInfo.setAccountDate(AccountDate);
            //结算方式默认赊销
            tmpktKbInfo.setAccountNethod(MPKTConstants.AccountNethod_C);
            //发票种类 默认增值税
            tmpktKbInfo.setInvoiceType(MPKTConstants.InvoiceType_11);

            TmpktKbInfoEx tmpktKbInfoEx = new TmpktKbInfoEx();
            BeanUtil.copyProperties(tmpktKbInfo, tmpktKbInfoEx);
            //制单人
            tmpktKbInfoEx.setSettleId("185015");

            inInfo.set("tmpktKbInfoEx", tmpktKbInfoEx);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;

    }

    /** 暂存信息 */
    public EiInfo addbkinfo(EiInfo inInfo) {
        try {
            TmpktKbInfoEx bean = (TmpktKbInfoEx) inInfo.get("TmpktKbInfoEx");
            //String kbSource = bean.getKbSource();

            Map map = businessKbInfo.temporarily(UserSession.getLoginName(), bean);
            String kbinfoId = (String) map.get("kbinfoId");
            String kbinfoBillNum = (String) map.get("kbinfoBillNum");
            inInfo.setCell("i", 0, "kbinfoId", kbinfoId);
            inInfo.setCell("i", 0, "kbinfoBillNum", kbinfoBillNum);

            inInfo.setMsg("保存成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /** 保存信息 */
    public EiInfo submitbkinfo(EiInfo inInfo) {
        try {
            TmpktKbInfoEx bean = (TmpktKbInfoEx) inInfo.get("TmpktKbInfoEx");
            Map map = businessKbInfo.submitbkinfo(UserSession.getLoginName(), bean);
            String kbinfoId = (String) map.get("kbinfoId");
            String kbinfoBillNum = (String) map.get("kbinfoBillNum");
            inInfo.setCell("i", 0, "kbinfoId", kbinfoId);
            inInfo.setCell("i", 0, "kbinfoBillNum", kbinfoBillNum);
            String voucherDesc = (String) map.get("voucherDesc");

            //根据开票方账套获取开票相关信息
            if(StringUtils.isBlank(bean.getKbinfoAccountBook())) {
                throw new PlatException("开票单位账套不能为空");
            }
            Map tgfMap=new HashMap();
            tgfMap.put("parentDictId","financialData");
            tgfMap.put("extra2", bean.getKbinfoAccountBook());//根据开票账套查询
            tgfMap.put("delStatus", "0");
            TzzzcDictEx tzzzcDictEx = businessDict.queryDict(tgfMap);

            String extra8 = tzzzcDictEx.getExtra8();
            if(StringUtils.isBlank(extra8)){
                //extra8 = "BGTAZF06";
            	throw new PlatException("开票财务责任中心不能为空,请联系管理员配置！");
            }
            String extra9 = tzzzcDictEx.getExtra9();
            if(StringUtils.isBlank(extra9)){
                //extra9 = "9001";
            	throw new PlatException("开票单位名称不能为空,请联系管理员配置！");
            }
            String extra10 = tzzzcDictEx.getExtra10();
            if(StringUtils.isBlank(extra10)){
                //extra10 = "12340000485408900H";
                throw new PlatException("开票单位税号不能为空,请联系管理员配置！");
            }
            String extra11 = tzzzcDictEx.getExtra11();
            if(StringUtils.isBlank(extra11)){
                //extra11 = "***********";
                throw new PlatException("开票单位地址电话不能为空,请联系管理员配置！");
            }
            String extra12 = tzzzcDictEx.getExtra12();
            if(StringUtils.isBlank(extra12)){
                //extra12 = "中国工商银行股份公司马鞍山马钢支行";
                throw new PlatException("开票单位银行名称不能为空,请联系管理员配置！");
            }
            String extra13 = tzzzcDictEx.getExtra13();
            if(StringUtils.isBlank(extra13)){
                //extra13 = "1306020429300143154";
                throw new PlatException("开票单位银行账号不能为空,请联系管理员配置！");
            }

            List li = bean.getTmpktKbInfoDetaillist();

            //获取开票单位
            //业务单据号
            String businessId = (String) map.get("kbinfoBillNum");
            //接口 开票发票信息头接口
            XBus8PBCS1 xBus8PBCS1 = new XBus8PBCS1();
            //账套
            xBus8PBCS1.setCompanyCode(bean.getKbinfoAccountBook());
            //单据类型
            xBus8PBCS1.setBillType(bean.getKbinfoType());
            //业务单据号
            xBus8PBCS1.setBillNo(businessId);
            //发票种类
            xBus8PBCS1.setInvoiceSort(bean.getInvoiceType());
            //结算用户类型 必输，K-供应商，Y员工
            xBus8PBCS1.setUserType(bean.getAccountUserType());
            //结算用户代码 必输，6位客商代码，开票单位
            xBus8PBCS1.setSettleUserCode(bean.getKbinfoCustomid());
            //最终用户代码  6位客商代码，最终收货单位
            xBus8PBCS1.setFinUserCode(bean.getKbinfoCustomid());
            //财务责任中心
            xBus8PBCS1.setFinaCostCenter(extra8);
            // 责任中心(业务方) : 8位责任中心
            xBus8PBCS1.setCostCenter(bean.getKbinfoDutyCenter());
            //发票金额 不含税金额，整数，需要乘以1,000,000
            BigDecimal invoiceAmount = bean.getInvoiceAmount(); //发票金额含税
            invoiceAmount =invoiceAmount.setScale(2, RoundingMode.HALF_UP);
            BigDecimal rate = bean.getRate();//税率
            BigDecimal invoiceNoTax = bean.getInvoiceNoTax(); //invoiceAmount.subtract(invoiceTax);//发票不含税金额
            BigDecimal invoiceTax = invoiceNoTax.multiply(rate); //发票税金额
            invoiceTax =invoiceTax.setScale(2, RoundingMode.HALF_UP);
            invoiceNoTax  = invoiceNoTax.multiply(BigDecimal.valueOf(100));
            xBus8PBCS1.setInvoiceAmt(invoiceNoTax);
            //发票价格  开票单价 乘以100
            BigDecimal InvoicePrice = invoiceAmount.multiply(BigDecimal.valueOf(100));
            xBus8PBCS1.setInvoicePrice(InvoicePrice);
            //发票税金额
            invoiceTax = invoiceTax.multiply(BigDecimal.valueOf(100));
            xBus8PBCS1.setInvoiceTaxAmt(invoiceTax);
            //发票价税合计金额
            invoiceAmount = invoiceAmount.multiply(BigDecimal.valueOf(100));
            xBus8PBCS1.setInvoiceSumAmt(invoiceAmount);
            //发票重量
            int wt = 0 ;
            if(li!=null){
                wt = li.size();
            }
            BigDecimal InvoiceWt = BigDecimal.valueOf(wt);
            InvoiceWt = InvoiceWt.multiply(BigDecimal.valueOf(1000000));
            xBus8PBCS1.setInvoiceWt(InvoiceWt);
            //税率
            BigDecimal TaxAmount = bean.getRate();
            BigDecimal TaxRate = TaxAmount.multiply(BigDecimal.valueOf(100));
            xBus8PBCS1.setTaxRate(TaxRate);
            //购方单位税号 必输
            xBus8PBCS1.setPurTaxNo(bean.getKbinfoCustomTax());
            //购方地址电话 必输
            xBus8PBCS1.setPurAddrTel(bean.getKbinfoCustomAddress()+" "+bean.getKbinfoCustomUnitphone());
            //购方银行名称 必输
            xBus8PBCS1.setPurBankName(bean.getKbinfoCustomBank());
            //购方银行帐号 必输
            xBus8PBCS1.setPurAcctNo(bean.getKbinfoCustomAccount());
            //结算方式
            xBus8PBCS1.setSettleMethod(bean.getAccountNethod());
            //（销售）贸易方式 : 默认为0
            xBus8PBCS1.setSaleMode("0");
            //结算人工号 : 必输，财务操作人员工号，制单人 185015
            xBus8PBCS1.setSettleId(bean.getSettleId());
            //预设会计期 : 必输，当前月份
            String month = new SimpleDateFormat("yyyyMM", Locale.CHINESE).format(new Date());
            xBus8PBCS1.setDestineAcctPeriod(month);
            //凭证摘要
            xBus8PBCS1.setVoucherDesc(voucherDesc);
            //币种代码 : 必输，见小代码
            xBus8PBCS1.setCurrencyCode(bean.getAccountCurrency());
            //系统别
            xBus8PBCS1.setSystemId("8P");
            //开票单位名称 : 公司（账套）全称
            xBus8PBCS1.setDrewDeptName(extra9);
            //开票单位税号 : 本公司税号
            xBus8PBCS1.setDrewDeptTaxNo(extra10);
            //开票地址电话 : 本公司地址电话
            xBus8PBCS1.setDrewAddrTel(extra11);
            // 开票银行名称 : 本公司开户行名称
            xBus8PBCS1.setDrewBankName(extra12);
            // 开票银行帐号 : 本公司开户账号
            xBus8PBCS1.setDrewBankAcct(extra13);
            // 附件张数 : 整数，默认2
            xBus8PBCS1.setAffixNum(new BigDecimal("2"));
            //单价

            //发票号 如在标财开票，则为空。如属地自行开票，则必输
//            xBus8PBCS1.setInvoiceNo();
            //发票代码 如在标财开票，则为空。如属地自行开票，则必输
//            xBus8PBCS1.setInvoiceCode();
            //开票日期 如在标财开票，则为空。如属地自行开票，则必输
//            xBus8PBCS1.setPaperDrewDate();
            //开票人 如在标财开票，则为空。如属地自行开票，则必输
//            xBus8PBCS1.setPaperDrewName();
            //运费额 如无运费则不填
//            xBus8PBCS1.setFreightAmt();



            //已开票发票明细信息发送 接口
            XBus8PBCS2 xBus8PBCS2 = new XBus8PBCS2();
            // 公司别 : 同头信息
            xBus8PBCS2.setCompanyCode(bean.getKbinfoAccountBook());
            //责任中心 必输，8位责任中心代码
            xBus8PBCS2.setCostCenter(bean.getKbinfoDutyCenter());
            //单据类型 同头信息
            xBus8PBCS2.setBillType(bean.getKbinfoType());
            //业务单据号
            xBus8PBCS2.setBillNo(businessId);
            //发票号 同头信息    如在标财开票，则为空。如属地自行开票，则必输
//            xBus8PBCS2.setInvoiceNo();
            //发票代码 同头信息  如在标财开票，则为空。如属地自行开票，则必输
//            xBus8PBCS2.setInvoiceCode();
            //三单号(业务单据子项号) 必输，结齐单号
            xBus8PBCS2.setThreeReadyNo(MPKTConstants.SYSTEM_ID+ BizIdUtil.INSTANCE.nextId());
            //三单量 必输，同开票数量，整数，需要乘以1000000
            BigDecimal ThreeReadyWt = BigDecimal.valueOf(wt);
            ThreeReadyWt = ThreeReadyWt.multiply(BigDecimal.valueOf(1000000));
            xBus8PBCS2.setThreeReadyWt(ThreeReadyWt);
            //开票数量
            xBus8PBCS2.setDrewQuantity(ThreeReadyWt);
            //产副品代码/费用类型
            xBus8PBCS2.setProductCode(bean.getKindCode());
            //费用类型名称 产副品代码名称
            xBus8PBCS2.setProductCodeName(bean.getKindName());
            //结算方式 同头信息
            xBus8PBCS2.setSettleMethod(bean.getAccountNethod());
            //合约号 放项目号
            xBus8PBCS2.setContractNo(bean.getKbinfoXmbh());
            //开票计量单位 吨、立方等
            xBus8PBCS2.setDrewMeasureUnit("1");
            //货款 不含税金额，整数，需乘以100
            xBus8PBCS2.setGoodAmt(invoiceNoTax);
            //税额 整数，需乘以100  三单量*销售单价*税率
            xBus8PBCS2.setTaxAmt(invoiceTax);
            //结算用户代码 必输，6位客商代码
            xBus8PBCS2.setSettleUserCode(bean.getKbinfoCustomid());
            //最终用户代码 必输，6位客商代码
            xBus8PBCS2.setFinUserCode(bean.getKbinfoCustomid());
            //上抛日期
            Date date = new Date();
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
            String UploadDate = formatter.format(date);
            xBus8PBCS2.setUploadDate(UploadDate);
            //结齐日期(入帐日期) 必输，8位年月日
            xBus8PBCS2.setThreeReadyDate(UploadDate);
            //税率 必输，与S1层一致
            xBus8PBCS2.setTaxRate(TaxRate);
            //系统别 必输
            xBus8PBCS2.setSystemId(MPKTConstants.SYSTEM_ID);
            //运费额 0
            xBus8PBCS2.setFreightAmt(BigDecimal.valueOf(0));
            //三单运费行数 0
            xBus8PBCS2.setThreeQuantity("0");

            //开票明细信息 接口
            List s3Li = new ArrayList();
            if(li!=null){
                for(int i = 0;i<li.size();i++){
                    TmpktKbInfoDetail tmpktKbInfoDetail = (TmpktKbInfoDetail) li.get(i);
                    //开票明细信息
                    XBus8PBCS3 xBus8PBCS3 = new XBus8PBCS3();
                    // 公司别 : 4位账套代码
                    xBus8PBCS3.setCompanyCode(bean.getKbinfoAccountBook());
                    //责任中心 必输
                    xBus8PBCS3.setCostCenter(bean.getKbinfoDutyCenter());
                    // 单据类型 : S01001-待开票信息
                    xBus8PBCS3.setBillType(bean.getKbinfoType());
                    // 业务单据号 : 单据流水号
                    xBus8PBCS3.setBillNo(businessId);
                    //序号 : 必输，如1,2,3,4
                    xBus8PBCS3.setSerialNo(String.valueOf(i));
                    //品名 : 开票名称
                    xBus8PBCS3.setProductCode(tmpktKbInfoDetail.getGoods());
                    //数量 : 必输，整数，需要乘以1,000,000
                    BigDecimal kbinfoCount = tmpktKbInfoDetail.getKbinfoCount();
                    BigDecimal DrewQuantity = kbinfoCount.multiply(BigDecimal.valueOf(1000000));
                    xBus8PBCS3.setDrewQuantity(DrewQuantity);
                    //单价 : 必输，整数，需要乘以1,000,000
                    BigDecimal kbinfoPrice = tmpktKbInfoDetail.getKbinfoPrice();
                    kbinfoPrice =kbinfoPrice.setScale(2, RoundingMode.HALF_UP);
                    BigDecimal SaleUnitPrice = kbinfoPrice.multiply(BigDecimal.valueOf(1000000));
                    xBus8PBCS3.setSaleUnitPrice(SaleUnitPrice);
                    //金额 : 必输，不含税金额，需要乘以100
                    BigDecimal money = kbinfoCount.multiply(kbinfoPrice);//不含税金额
                    money =money.setScale(2, RoundingMode.HALF_UP);
                    BigDecimal S3TaxAmount = money.multiply(bean.getRate());
                    S3TaxAmount =S3TaxAmount.setScale(2, RoundingMode.HALF_UP);
                    //含税金额 :
                    BigDecimal SettleAmt = money.add(S3TaxAmount);//不含税金额+税额

                    BigDecimal invoiceNo = money.multiply(BigDecimal.valueOf(100));//money.subtract(S3TaxAmount).multiply(BigDecimal.valueOf(100));
                    xBus8PBCS3.setGoodAmt(invoiceNo);
                    //税额 : 必输，需要乘以100
                    S3TaxAmount = S3TaxAmount.multiply(BigDecimal.valueOf(100));
                    xBus8PBCS3.setTaxAmount(S3TaxAmount);
                    //含税金额：必输，需要乘以100
                    SettleAmt=SettleAmt.multiply(BigDecimal.valueOf(100));
                    xBus8PBCS3.setSettleAmt(SettleAmt);
                    //税率 : 必输，同S1
                    xBus8PBCS3.setTaxRate(TaxRate);
                    xBus8PBCS3.setSystemId(MPKTConstants.SYSTEM_ID);
                    //财税编码 : 必输，开票用，问相关财务要
                    xBus8PBCS3.setTaxCode(bean.getAccountSubkind());

                    s3Li.add(xBus8PBCS3);
                }
            }
            inInfo.set("XBus8PBCS1",xBus8PBCS1);
            inInfo.set("XBus8PBCS2",xBus8PBCS2);
            inInfo.set("s3Li",s3Li);
            inInfo.set("businessId",businessId);
            EiInfo ei = serviceMPKTXBus.send8PBCS1(inInfo);

            if(ei.getStatus()>-1) {
            	inInfo.setMsg("提交成功！");
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            }else {
            	return ei;
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /** 修改查询 */
    public EiInfo editquery(EiInfo inInfo) {
        try {
            String kbinfoId = (String) inInfo.get("kbinfoId");
            inInfo.set("TmpktKbInfoEx", businessKbInfo.editquery(kbinfoId));
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /** 查询表格 */
    public EiInfo queryTmpktKbInfoDetail(EiInfo inInfo) {
        try {
            String kbinfoId = (String) inInfo.get("kbinfoId");
            TableDataInfo queryPage = businessKbInfo.queryTmpktKbInfoDetail(kbinfoId);

            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /** 查询发票 */
    public EiInfo queryKbInfoS(EiInfo inInfo) {
        try {
            String kbinfoBillNum = (String) inInfo.get("kbinfoBillNum");
            TableDataInfo queryPage = businessKbInfo.queryKbInfoS(kbinfoBillNum);
            inInfo = setPage(inInfo, queryPage);
            inInfo.setMsg("success");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    // 发票回撤
    public EiInfo revoke(EiInfo inInfo) {
        try {
            String kbinfoId = (String) inInfo.get("kbinfoId");
            TmpktKbInfo tmpktKbInfo = businessKbInfo.query(kbinfoId);

            if(MPKTConstants.PROCESS_HC.equals(tmpktKbInfo.getExtra2())&&
                    MPKTConstants.KBINFOSTATUS_HC.equals(tmpktKbInfo.getKbinfoStatus())){//说明还没有返回
                throw new PlatException("财务尚未反馈请稍后再试");
            }

            //修改发票表状态 回撤
            tmpktKbInfo.setExtra2(MPKTConstants.PROCESS_HC);
            tmpktKbInfo.setKbinfoStatus(MPKTConstants.KBINFOSTATUS_HC);
            businessKbInfo.update(UserSession.getLoginName(),tmpktKbInfo);

            XBus8PBCS9 xBus8PBCS9 = new XBus8PBCS9();
            // 撤销类型
            xBus8PBCS9.setCancleType("C");
            // 账套
            xBus8PBCS9.setCompanyCode(tmpktKbInfo.getKbinfoAccountBook());
            // 系统别
            xBus8PBCS9.setSystemId(MPKTConstants.SYSTEM_ID);
            //业务单据号
            xBus8PBCS9.setBillNo(tmpktKbInfo.getKbinfoBillNum());
            //发票号
//            xBus8PBCS9.setInvoiceNo();
            //发票代码
//            xBus8PBCS9.setInvoiceCode();
            //撤销描述
            xBus8PBCS9.setResultDesc("C");
            //备注
            xBus8PBCS9.setRemark("C");

            inInfo.set("XBus8PBCS9",xBus8PBCS9);
            inInfo.set("businessId",tmpktKbInfo.getKbinfoBillNum());
            EiInfo ei = serviceMPKTXBus.send8PBCS9(inInfo);
            //String msg = ei.getMsg();
/*
            String[] MSG = null;
            if (msg != null && msg.length() > 0) {
                MSG = msg.split(",");
                String first = MSG[0];
                MSG[0] = first.substring(first.indexOf("[") + 1);
                String last = MSG[MSG.length - 1];
                MSG[MSG.length - 1] = last.substring(0, last.indexOf("]"));
            }
            if(MSG != null){
                String c = MSG[0];
                String a = MSG[1];
                String b = MSG[2];
            }*/

            //修改状态
//            TmpktKbInfo tmpktKbInfo1 = new TmpktKbInfo();
//            tmpktKbInfo1.setKbinfoId(kbinfoId);
//            //开票状态 待财务开票
//            tmpktKbInfo1.setKbinfoStatus(MPKTConstants.KBINFOSTATUS_DKP);
//            //状态 已提交
//            tmpktKbInfo1.setExtra2(MPKTConstants.PROCESS_REFER);
            if(ei.getStatus()>-1) {
            	inInfo.setMsg("提交成功！");
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            }else {
            	return ei;
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo selectLi(EiInfo inInfo) {
        try {
            Map<String, Object> queryData = getQueryData ( inInfo );
            TableDataInfo queryPage = businessKbInfo.selectLi ( queryData );
            inInfo = setPage ( inInfo, queryPage );
            inInfo.setMsg ( "success" );
            inInfo.setStatus ( EiConstant.STATUS_SUCCESS );
        } catch (Exception e) {
            e.printStackTrace ();
            inInfo.setStatus ( EiConstant.STATUS_FAILURE );
            inInfo.setMsg ( e.getMessage () );
            if (null != e.getCause ()) {
                logger.error ( e.getMessage (), e );
                inInfo.setDetailMsg ( e.getCause ().getMessage () );
            } else {
                logger.error ( e.getMessage () );
            }
        }
        return inInfo;
    }

}
