package com.baosight.bscdkj.mp.ty.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSONObject;
import com.baosight.bscdkj.mp.ad.utils.AttachmentUtil;
import com.baosight.bscdkj.mp.ty.dto.*;
import com.baosight.bscdkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bscdkj.utils.BizIdUtil;
import com.baosight.bscdkj.common.exception.BusinessException;
import com.baosight.bscdkj.common.ky.domain.TkysbTradeContinue;
import com.baosight.bscdkj.common.ky.domain.TkysbTradeState;
import com.baosight.bscdkj.common.service.PageService;
import com.baosight.bscdkj.ky.sb.business.BusinessKYSBTradeContinue;
import com.baosight.bscdkj.ky.sb.business.BusinessKYSBTradeState;
import com.baosight.bscdkj.mp.ty.business.BusinessAttachment;
import com.baosight.bscdkj.mp.ty.business.BusinessAttachmentMap;
import com.baosight.bscdkj.mp.ty.dto.*;
import com.baosight.iplat4j.core.ProjectInfo;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.ei.json.EiInfo2Json2;
import com.baosight.iplat4j.core.ei.json.Json2EiInfo2;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.ioc.spring.PlatApplicationContext;
import com.baosight.iplat4j.core.log.Logger;
import com.baosight.iplat4j.core.log.LoggerFactory;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.ResponseHandler;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.BasicResponseHandler;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
public class ServiceMPTYAttachmentMap extends PageService {

    @Resource
    BusinessAttachmentMap businessAttachmentMap;
    @Resource
    BusinessAttachment businessAttachment;
    @Resource
    BusinessKYSBTradeState businessKYSBTradeState;
    @Autowired
    BusinessKYSBTradeContinue businessKYSBTradeContinue;

    Logger logger = LoggerFactory.getLogger(this.getClass());

    private AttachmentMap getAttachmentMap(EiInfo inInfo) {
        Object obj = inInfo.get("attachment");
        if (obj == null) {
            throw new PlatException("无附件相关信息!");
        }
        AttachmentMap attachment = null;
        if (obj instanceof Map) {
            attachment = BeanUtil.toBean(obj, AttachmentMap.class);
        } else {
            attachment = (AttachmentMap) obj;
        }
        return attachment;
    }

    /**
     * 设置附件关系
     * S_MP_TY_01
     *
     * @param inInfo
     * @return
     */
    public EiInfo addAttachmentMap(EiInfo inInfo) {
        try {
            AttachmentMap attachmentMap = getAttachmentMap(inInfo);
            if (StringUtils.isEmpty(attachmentMap.getOperator())) {
                throw new PlatException("operator不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getAttachmentId())) {
                throw new PlatException("attachmentId不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getSourceId())) {
                throw new PlatException("sourceId不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getSourceModule())) {
                throw new PlatException("sourceModule不能为空");
            }
            String[] attachmentIdS = attachmentMap.getAttachmentId().split(",");
            for (String attachmentId : attachmentIdS) {
                if (StringUtils.isNotBlank(attachmentId)) {
                    if (StringUtils.isEmpty(attachmentMap.getSourceLabel1())
                            && StringUtils.isEmpty(attachmentMap.getSourceLabel2())
                            && StringUtils.isEmpty(attachmentMap.getSourceLabel3())) {
                        AttachmentUtil.addAttachmentMap(attachmentMap.getOperator(), attachmentId,
                                attachmentMap.getSourceId(), attachmentMap.getSourceModule());
                    } else {
                        AttachmentUtil.addAttachmentMap(attachmentMap.getOperator(), attachmentId,
                                attachmentMap.getSourceId(), attachmentMap.getSourceModule(),
                                attachmentMap.getSourceLabel1(), attachmentMap.getSourceLabel2(),
                                attachmentMap.getSourceLabel3());
                    }
                }
            }
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("success");
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 根据来源ID删除附件关系
     * S_MP_TY_02
     *
     * @param inInfo
     * @return
     */
    public EiInfo deleteAttachmentMapBySourceId(EiInfo inInfo) {
        try {
            AttachmentMap attachmentMap = getAttachmentMap(inInfo);
            if (StringUtils.isEmpty(attachmentMap.getOperator())) {
                throw new PlatException("operator不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getSourceId())) {
                throw new PlatException("sourceId不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getSourceModule())) {
                throw new PlatException("sourceModule不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getSourceLabel1())) {
                AttachmentUtil.deleteAttachmentMapBySourceId(attachmentMap.getOperator(), attachmentMap.getSourceId(),
                        attachmentMap.getSourceModule());
            } else {
                AttachmentUtil.deleteAttachmentMapBySourceIdAndSourceLabel(attachmentMap.getOperator(), attachmentMap.getSourceId(),
                        attachmentMap.getSourceModule(), attachmentMap.getSourceLabel1(), attachmentMap.getSourceLabel2(),
                        attachmentMap.getSourceLabel3());
            }


            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("success");
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 根据来源ID获取相关附件
     * S_MP_TY_03
     *
     * @param inInfo
     * @return
     */
    public EiInfo getAttachmentBySourceId(EiInfo inInfo) {
        try {
            AttachmentMap attachmentMap = getAttachmentMap(inInfo);
            if (StringUtils.isEmpty(attachmentMap.getSourceId())) {
                throw new PlatException("sourceId不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getSourceModule())) {
                throw new PlatException("sourceModule不能为空");
            }
            List<TmptyAttachmentEx> listAttachment = AttachmentUtil.getAttachmentBySourceId(attachmentMap.getSourceId(), attachmentMap.getSourceModule());
            List<AttachmentMap> result = new ArrayList<AttachmentMap>();
            for (TmptyAttachment tmptyAttachment : listAttachment) {
                AttachmentMap attachmentMapRtn = new AttachmentMap();
                BeanUtil.copyProperties(tmptyAttachment, attachmentMapRtn, false);
                result.add(attachmentMapRtn);
            }

            inInfo.addBlock(EiConstant.resultBlock).addRows(result);

            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("success");
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 根据附件ID查询附件
     * S_MP_TY_04
     *
     * @param inInfo
     * @return
     */
    public EiInfo getAttachment(EiInfo inInfo) {
        try {
            AttachmentMap attachmentMap = getAttachmentMap(inInfo);
            if (StringUtils.isEmpty(attachmentMap.getAttachmentId())) {
                throw new PlatException("attachmentId不能为空");
            }
            TmptyAttachment attachment = AttachmentUtil.getAttachment(attachmentMap.getAttachmentId());
            if (attachment != null) {
                BeanUtil.copyProperties(attachment, attachmentMap, false);
            }
            inInfo.addBlock(EiConstant.resultBlock).addRow(attachment);

            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("success");
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * 根据附件ID集合查询附件集合
     * S_MP_TY_05
     *
     * @param inInfo
     * @return
     */
    public EiInfo getAttachmentList(EiInfo inInfo) {
        try {
            List<String> attachmentIdList = (List<String>) inInfo.get("attachmentIdList");
            inInfo.set("attachmentIdList", attachmentIdList);
            if (null == attachmentIdList || attachmentIdList.isEmpty()) {
                throw new PlatException("attachmentIdList不能为空");
            }
            List<AttachmentMap> result = new ArrayList<AttachmentMap>();
            List<TmptyAttachment> listAttachment = AttachmentUtil.getAttachmentList(attachmentIdList);
            for (TmptyAttachment tmptyAttachment : listAttachment) {
                AttachmentMap attachmentMap = new AttachmentMap();
                BeanUtil.copyProperties(tmptyAttachment, attachmentMap, false);
                result.add(attachmentMap);
            }
            inInfo.addBlock(EiConstant.resultBlock).addRows(result);

            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("success");
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    /**
     * 根据来源ID删除附件关系
     * S_MP_TY_06
     *
     * @param inInfo
     * @return
     */
    public EiInfo deleteAttachmentMapBySourceIdAndSourceLabel(EiInfo inInfo) {
        try {
            AttachmentMap attachmentMap = getAttachmentMap(inInfo);
            if (StringUtils.isEmpty(attachmentMap.getOperator())) {
                throw new PlatException("operator不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getSourceId())) {
                throw new PlatException("sourceId不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getSourceModule())) {
                throw new PlatException("sourceModule不能为空");
            }
            AttachmentUtil.deleteAttachmentMapBySourceIdAndSourceLabel(attachmentMap.getOperator(), attachmentMap.getSourceId(),
                    attachmentMap.getSourceModule(), attachmentMap.getSourceLabel1(),
                    attachmentMap.getSourceLabel2(), attachmentMap.getSourceLabel3());

            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("success");
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    /**
     * S_MP_TY_14
     * 根据来源ID和标识获取相关附件
     * sourceId
     * sourceModule
     * sourceLabel1
     * sourceLabel2
     * sourceLabel3
     *
     * @return
     */
    public EiInfo getAttachmentBySourceIdAndSourceLabel(EiInfo inInfo) {
        try {
            AttachmentMap attachmentMap = getAttachmentMap(inInfo);
            List<TmptyAttachmentEx> attachmentBySourceIdAndSourceLabel =
                    AttachmentUtil.getAttachmentBySourceIdAndSourceLabel(attachmentMap.getSourceId(), attachmentMap.getSourceModule(),
                    attachmentMap.getSourceLabel1(), attachmentMap.getSourceLabel2(), attachmentMap.getSourceLabel3());
            List<Map<String, Object>> collect = attachmentBySourceIdAndSourceLabel.stream().map(attachment -> BeanUtil.beanToMap(attachment)).collect(Collectors.toList());
            inInfo.set("attachmentList", collect);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("success");
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }
    public EiInfo getAttachmentBySourceModuleAndSourceLabel(EiInfo inInfo) {
        try {
            AttachmentMap attachmentMap = getAttachmentMap(inInfo);
            List<TmptyAttachmentEx> attachmentBySourceIdAndSourceLabel =
                    AttachmentUtil.getAttachmentBySourceModuleAndSourceLabelList(attachmentMap.getSourceModule(),
                            attachmentMap.getSourceLabel1());

            List<Map<String, Object>> collect = attachmentBySourceIdAndSourceLabel.stream().map(attachment -> BeanUtil.beanToMap(attachment)).collect(Collectors.toList());

            inInfo.set("attachmentList", collect);
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("success");
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo deleteAttachmentBySourceIdAndSourceLabel(EiInfo inInfo) {
        try {
            AttachmentMap attachmentMap = getAttachmentMap(inInfo);
            if (StringUtils.isEmpty(attachmentMap.getOperator())) {
                throw new PlatException("operator不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getSourceId())) {
                throw new PlatException("sourceId不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getSourceModule())) {
                throw new PlatException("sourceModule不能为空");
            }
            List<TmptyAttachmentEx> TmptyAttachmentExList = AttachmentUtil.getAttachmentBySourceId(attachmentMap.getSourceId(), attachmentMap.getSourceModule());
            List<AttachmentMap> attachmentMapList = new ArrayList<AttachmentMap>();
            for (TmptyAttachment tmptyAttachment : TmptyAttachmentExList) {
                AttachmentMap attachmentMapRtn = new AttachmentMap();
                BeanUtil.copyProperties(tmptyAttachment, attachmentMapRtn, false);
                attachmentMapList.add(attachmentMapRtn);
            }

            attachmentMapList.stream()
                    .collect(Collectors.toMap(AttachmentMap::getAttachmentId, v -> v))
                    .forEach((id, aMap) -> {
                        // 删除附件关系
                        AttachmentUtil.deleteAttachmentMapBySourceIdAndSourceLabel(attachmentMap.getOperator(), aMap.getSourceId(),
                                aMap.getSourceModule(), aMap.getSourceLabel1(),
                                aMap.getSourceLabel2(), aMap.getSourceLabel3());
                        // 删除附件
                        SAttachmentUtil.deleteAttachment(attachmentMap.getOperator(), id);
                    });
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("success");
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo deleteAttachmentByAttachmentId(EiInfo inInfo) {
        try {
            AttachmentMap attachmentMap = (AttachmentMap) inInfo.get("attachmentMap");
            if (StringUtils.isEmpty(attachmentMap.getOperator())) {
                throw new PlatException("operator不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getSourceId())) {
                throw new PlatException("sourceId不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getSourceModule())) {
                throw new PlatException("sourceModule不能为空");
            }

            // 删除附件关系
            businessAttachmentMap.deleteAttachmentMapByAttachmentId(attachmentMap.getAttachmentId(), attachmentMap.getSourceId(),
                    attachmentMap.getSourceModule(), attachmentMap.getSourceLabel1(),
                    attachmentMap.getSourceLabel2(), attachmentMap.getSourceLabel3());
            // 删除附件
            SAttachmentUtil.deleteAttachment(attachmentMap.getOperator(), attachmentMap.getAttachmentId());

            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            inInfo.setMsg("success");
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }


    public EiInfo load(EiInfo inInfo) {
        try {
            String rowId = (String) inInfo.get("rowId");
            if (StringUtils.isNotBlank(rowId)) {
                TmptyAttachmentMap tmptyAttachmentMap = businessAttachmentMap.query(rowId);
                AttachmentMapVo attachmentMapVo = new AttachmentMapVo();
                BeanUtil.copyProperties(tmptyAttachmentMap, attachmentMapVo, false);
                inInfo.set("attachmentMapVo", attachmentMapVo);
                inInfo.setStatus(EiConstant.STATUS_SUCCESS);
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo remove(EiInfo inInfo) {
        try {
            String loginName = UserSession.getLoginName();
            String rowIds = (String) inInfo.get("rowIds");
            if (StrUtil.isNotBlank(rowIds)) {
                for (String id : rowIds.split(",")) {
                    TmptyAttachmentMap tmptyAttachmentMap = businessAttachmentMap.query(id);
                    // 删除附件关系
                    businessAttachmentMap.delete(loginName, id);
                    // 删除附件
                    SAttachmentUtil.deleteAttachment(loginName, tmptyAttachmentMap.getAttachmentId());
                }
            }
            inInfo.setMsg("删除成功");
            inInfo.setStatus(EiConstant.STATUS_SUCCESS);
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo add(EiInfo inInfo) {
        try {
            AttachmentMap attachmentMap = (AttachmentMap) inInfo.get("attachmentMap");

            String userCode = UserSession.getLoginName();

            if (attachmentMap != null) {
                if (StringUtils.isBlank(attachmentMap.getAttachmentId())) {
                    inInfo.setMsg("attachmentId不能为空，请输入后重试");
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return inInfo;
                }
//                if (StringUtils.isBlank(attachmentMap.getSourceId())) {
//                    inInfo.setMsg("sourceId不能为空，请输入后重试");
//                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
//                    return inInfo;
//                }
                if (StringUtils.isBlank(attachmentMap.getSourceModule())) {
                    inInfo.setMsg("sourceModule不能为空，请输入后重试");
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return inInfo;
                }
                if ("".equals(attachmentMap.getSourceLabel1())) {
                    attachmentMap.setSourceLabel1(null);
                }
                if ("".equals(attachmentMap.getSourceLabel2())) {
                    attachmentMap.setSourceLabel2(null);
                }
                if ("".equals(attachmentMap.getSourceLabel3())) {
                    attachmentMap.setSourceLabel3(null);
                }
                attachmentMap.setOperator(userCode);
                // 新增的附件关系
                List<String> attachmentAddIds = Arrays.stream(attachmentMap.getAttachmentId().split(",")).collect(Collectors.toList());
                // 新增附件关系
                int num = 0;
                for (String attachmentId : attachmentAddIds) {
                    TmptyAttachment attachment = businessAttachment.query(attachmentId);
                    String regigterNo = attachment.getAttachmentName()
                            .substring(0,attachment.getAttachmentName().lastIndexOf("."));
                    Map<String, Object> stateParam = new HashMap<>();
                    stateParam.put("regigterNo",regigterNo);
                    List<TkysbTradeState> queryList = businessKYSBTradeState.queryList(stateParam);
                    if(queryList==null || queryList.size()==0){
                       continue;
                    }
                    //上传商标注册证
                    attachmentMap.setSourceId(queryList.get(0).getTradeRegistId());
                    businessAttachmentMap.addAttachmentMap(attachmentMap.getOperator(),
                            attachmentId,
                            attachmentMap.getSourceId(),
                            attachmentMap.getSourceModule(),
                            attachmentMap.getSourceLabel1(),
                            attachmentMap.getSourceLabel2(),
                            attachmentMap.getSourceLabel3());
                    num++;
                    inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                }
                if(num==0)inInfo.setMsg("添加成功数量:"+num);
            } else {
                inInfo.setMsg("添加附件失败，请联系管理员");
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public EiInfo edit(EiInfo inInfo) {
        try {
            AttachmentMap attachmentMap = (AttachmentMap) inInfo.get("attachmentMap");
            String userCode = UserSession.getLoginName();
            if (attachmentMap != null) {
                if (StringUtils.isBlank(attachmentMap.getAttachmentId())) {
                    inInfo.setMsg("attachmentId不能为空，请输入后重试");
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return inInfo;
                }
//                if (StringUtils.isBlank(attachmentMap.getSourceId())) {
//                    inInfo.setMsg("sourceId不能为空，请输入后重试");
//                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
//                    return inInfo;
//                }
                if (StringUtils.isBlank(attachmentMap.getSourceModule())) {
                    inInfo.setMsg("sourceModule不能为空，请输入后重试");
                    inInfo.setStatus(EiConstant.STATUS_FAILURE);
                    return inInfo;
                }
                if ("".equals(attachmentMap.getSourceLabel1())) {
                    attachmentMap.setSourceLabel1(null);
                }
                if ("".equals(attachmentMap.getSourceLabel2())) {
                    attachmentMap.setSourceLabel2(null);
                }
                if ("".equals(attachmentMap.getSourceLabel3())) {
                    attachmentMap.setSourceLabel3(null);
                }
                attachmentMap.setOperator(userCode);

                List<TmptyAttachmentEx> list = businessAttachmentMap.getAttachmentBySourceModuleAndSourceLabelList(
                        attachmentMap.getSourceModule(),attachmentMap.getSourceLabel1());
                List<String> existIds = list.stream().map(TmptyAttachment::getAttachmentId).collect(Collectors.toList());
                // 新增的附件关系
                List<String> attachmentAddIds = Arrays.stream(attachmentMap.getAttachmentId().split(",")).collect(Collectors.toList());
                attachmentAddIds.removeAll(existIds);
                // 新增附件关系
                for (String attachmentId : attachmentAddIds) {
                    String res = fileUpload(attachmentMap,attachmentId);
                    if(res==null)continue;
                    attachmentMap.setSourceId(res);
                    List<TmptyAttachmentEx> listEx= businessAttachmentMap.
                            getAttachmentBySourceIdLabel1(attachmentMap.getSourceId(),"KYSB"
                                    ,attachmentMap.getSourceLabel1());
                    for (TmptyAttachmentEx ex:listEx) {
                        businessAttachmentMap.deleteAttachmentMapByAttachmentId(
                                ex.getAttachmentId(),
                                ex.getSourceId(),
                                ex.getSourceModule(),
                                ex.getSourceLabel1(),
                                ex.getSourceLabel2(),
                                attachmentMap.getSourceLabel3());
                    }

                    businessAttachmentMap.addAttachmentMap(attachmentMap.getOperator(),
                            attachmentId,
                            attachmentMap.getSourceId(),
                            attachmentMap.getSourceModule(),
                            attachmentMap.getSourceLabel1(),
                            attachmentMap.getSourceLabel2(),
                            attachmentMap.getSourceLabel3());
                    inInfo.setMsg("添加成功");
                    inInfo.setStatus(EiConstant.STATUS_SUCCESS);
                }
                // 需要删除的附件关系
//                List<String> attachmentExistIds2 = Arrays.stream(attachmentMap.getAttachmentId().split(",")).collect(Collectors.toList());
//                existIds.removeAll(attachmentExistIds2);
                // 删除附件关系
//                for (String attachmentId : existIds) {
//                    businessAttachmentMap.deleteAttachmentMapByAttachmentId(
//                            attachmentId,
//                            attachmentMap.getSourceId(),
//                            attachmentMap.getSourceModule(),
//                            attachmentMap.getSourceLabel1(),
//                            attachmentMap.getSourceLabel2(),
//                            attachmentMap.getSourceLabel3());
//                    inInfo.setMsg("添加成功");
//                    inInfo.setStatus(EiConstant.STATUS_SUCCESS);
//                }

            } else {
                inInfo.setMsg("添加附件失败，请联系管理员");
                inInfo.setStatus(EiConstant.STATUS_FAILURE);
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    public String fileUpload( AttachmentMap attachmentMap,String attachmentId){
        //如果是注册证明或变更证明
        TmptyAttachment attachment = businessAttachment.query(attachmentId);
        if(!attachment.getAttachmentType().equals("pdf")){
            throw new BusinessException("只能上传pdf文件");
        }
        String regigterNo = attachment.getAttachmentName()
                .substring(0,attachment.getAttachmentName().lastIndexOf("."));
        Map<String, Object> stateParam = new HashMap<>();
        stateParam.put("regigterNo",regigterNo);
        stateParam.put("displayOrder", " END_DATE desc ");
        List<TkysbTradeState> queryList = businessKYSBTradeState.queryList(stateParam);
        TkysbTradeState descState = queryList.get(0);
        if(attachmentMap.getSourceLabel1().equals("tradeSbzczFile")
                || attachmentMap.getSourceLabel1().equals("tradeStateUpdateFile")
                  ){
            if(queryList==null || queryList.size()==0){
                return null;
            }
            return  descState.getTradeRegistId();
        }else if(attachmentMap.getSourceLabel1().equals("continueFile") ) {
            //如果是续展证明
            if (StringUtils.isEmpty(descState.getTradeContinueId())) {
                TkysbTradeContinue tradeContinue = new TkysbTradeContinue();
                tradeContinue.setTradeContinueId(BizIdUtil.INSTANCE.nextId());
                tradeContinue.setTradeRegistId(descState.getTradeRegistId());
                tradeContinue.setStatus("end");
                tradeContinue.setOpinion("商标续展信息维护使用");
                businessKYSBTradeContinue.saveByState("admin", tradeContinue);
                descState.setTradeContinueId(tradeContinue.getTradeContinueId());
                businessKYSBTradeState.update("admin", descState);
                return tradeContinue.getTradeContinueId();

            }
            return descState.getTradeContinueId();
        }
        return null;
    }
    /**
     * S_MP_TY_26
     * 获取预览url
     *
     * @return
     */
    public EiInfo getPreviewUrl(EiInfo inInfo) {
        try {
            AttachmentMap attachmentMap = getAttachmentMap(inInfo);
            if (StringUtils.isEmpty(attachmentMap.getOperator())) {
                throw new PlatException("operator不能为空");
            }
            if (StringUtils.isEmpty(attachmentMap.getAttachmentId())) {
                throw new PlatException("attachmentId不能为空");
            }
            Map<String, Object> attachment = SAttachmentUtil.downAttachment(attachmentMap.getOperator(), attachmentMap.getAttachmentId());
            if (attachment != null) {
                String storeType = (String) attachment.get("storeType");
                String docUrl = (String) attachment.get("docUrl");
                String attachmentType = (String) attachment.get("attachmentType");
                String attachmentSize = (String) attachment.get("attachmentSize");
                //String hashValue = (String)attachment.get("hashValue");
                String attachmentName = (String) attachment.get("attachmentName");
                if (StringUtils.isBlank(attachmentType)) {
                    throw new PlatException(attachmentName + ",文件类型暂不支持预览!");
                }
                if ("remote".equals(storeType) || "local".equals(storeType)) {
                    throw new PlatException("暂不支持预览!");
                }
                EiInfo inInfo03 = new EiInfo();
                inInfo03.set(EiConstant.serviceId, "S_BE_AW_03");
                inInfo03.set("fileExtension", attachmentType);
                inInfo03.set("fileSize", attachmentSize);
                inInfo03.set("downLoadUrl", docUrl);
                inInfo03.set("appId", ProjectInfo.getComponentName());
                inInfo03.set("userId", attachmentMap.getOperator());
                inInfo03.set("userName", "");
                //inInfo.set("userAvatarUrl","https://bos.baocloud.cn:8082/2copye60001/2022/eplat/20210202.png");
                //EiInfo call = EServiceManager.call(inInfo);
                String eplatServiceUrl = PlatApplicationContext.getProperty("eplat.upload.resourceAddress") + "service";
                return callV6CrossData(inInfo03, eplatServiceUrl);
            }
        } catch (Exception e) {
            e.printStackTrace();
            inInfo.setStatus(EiConstant.STATUS_FAILURE);
            inInfo.setMsg(e.getMessage());
            if (null != e.getCause()) {
                logger.error(e.getMessage(), e);
                inInfo.setDetailMsg(e.getCause().getMessage());
            } else {
                logger.error(e.getMessage());
            }
        }
        return inInfo;
    }

    private static EiInfo callV6CrossData(EiInfo inInfo, String serviceUrl) {
        EiInfo outInfo = new EiInfo();
        CloseableHttpClient httpClient = HttpClients.createDefault();
        ResponseHandler<String> responseHandler = new BasicResponseHandler();
        try {
            JSONObject jsonObject = EiInfo2Json2.toJsonObject(inInfo);

            //第一步：创建HttpClient对象
            httpClient = HttpClients.createDefault();
            //第二步：创建httpPost对象
            HttpPost httpPost = new HttpPost(serviceUrl);
            //第三步：给httpPost设置JSON格式的参数
            StringEntity requestEntity = new StringEntity(jsonObject.toString(), "utf-8");
            requestEntity.setContentEncoding("UTF-8");
            httpPost.setHeader("Content-type", "application/json");
            httpPost.setEntity(requestEntity);
            //第四步：发送HttpPost请求，获取业务返回值
            String returnValue = httpClient.execute(httpPost, responseHandler);
            return Json2EiInfo2.parse(returnValue);
        } catch (Exception e) {
            e.printStackTrace();
            outInfo.setStatus(EiConstant.STATUS_FAILURE);
            outInfo.setMsg(e.getMessage());
        } finally {
            try {
                httpClient.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return outInfo;
    }
}
