<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tzzfyScene">
	<typeAlias alias="tzzfySceneResult" type="com.baosight.bscdkj.common.zz.domain.TzzfyScene"/>
	<typeAlias alias="tzzfySceneEx" type="com.baosight.bscdkj.zz.fy.domin.TzzfySceneEx"/>
	<select id="load" parameterClass="string" resultClass="tzzfySceneResult">
		SELECT 
				scene_id as "sceneId" ,
				biz_id as "bizId" ,
				scene_tel as "sceneTel" ,
				scene_user_code as "sceneUserCode" ,
				scene_user_name as "sceneUserName" ,
				scene_date as "sceneDate" ,
				scene_count as "sceneCount" ,
				bill_count as "billCount" ,
				scene_bz_total as "sceneBzTotal" ,
				scene_tj_total as "sceneTjTotal" ,
				scene_se_total as "sceneSeTotal" ,
				scene_fy_total as "sceneFyTotal" ,
				scene_people_total as "scenePeopleTotal" ,
				scene_bzdh as "sceneBzdh" ,
				scene_fk_date as "sceneFkDate" ,
				scene_submit_date as "sceneSubmitDate" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" 
				FROM ${zzzcSchema}.t_zzfy_scene
		WHERE
				scene_id = #sceneId#
		
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tzzfySceneResult">
		SELECT
				scene_id as "sceneId" ,
				biz_id as "bizId" ,
				scene_tel as "sceneTel" ,
				scene_user_code as "sceneUserCode" ,
				scene_user_name as "sceneUserName" ,
				scene_date as "sceneDate" ,
				scene_count as "sceneCount" ,
				bill_count as "billCount" ,
				scene_bz_total as "sceneBzTotal" ,
				scene_tj_total as "sceneTjTotal" ,
				scene_se_total as "sceneSeTotal" ,
				scene_fy_total as "sceneFyTotal" ,
				scene_people_total as "scenePeopleTotal" ,
				scene_bzdh as "sceneBzdh" ,
				scene_fk_date as "sceneFkDate" ,
				scene_submit_date as "sceneSubmitDate" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" 
				FROM ${zzzcSchema}.t_zzfy_scene
			<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="sceneId">scene_id =  #sceneId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="bizId">biz_id =  #bizId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneTel">scene_tel =  #sceneTel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneUserCode">scene_user_code =  #sceneUserCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneUserName">scene_user_name =  #sceneUserName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneDate">scene_date =  #sceneDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneDateLi">scene_date like '%$sceneDateLi$%'</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneCount">scene_count =  #sceneCount#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="billCount">bill_count =  #billCount#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneBzTotal">scene_bz_total =  #sceneBzTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneTjTotal">scene_tj_total =  #sceneTjTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneSeTotal">scene_se_total =  #sceneSeTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneFyTotal">scene_fy_total =  #sceneFyTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="scenePeopleTotal">scene_people_total =  #scenePeopleTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneBzdh">scene_bzdh =  #sceneBzdh#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneFkDate">scene_fk_date =  #sceneFkDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneSubmitDate">scene_submit_date =  #sceneSubmitDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.t_zzfy_scene
		<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="sceneId">scene_id =  #sceneId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="bizId">biz_id =  #bizId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneTel">scene_tel =  #sceneTel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneUserCode">scene_user_code =  #sceneUserCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneUserName">scene_user_name =  #sceneUserName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneDate">scene_date =  #sceneDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneCount">scene_count =  #sceneCount#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="billCount">bill_count =  #billCount#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneBzTotal">scene_bz_total =  #sceneBzTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneTjTotal">scene_tj_total =  #sceneTjTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneSeTotal">scene_se_total =  #sceneSeTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneFyTotal">scene_fy_total =  #sceneFyTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="scenePeopleTotal">scene_people_total =  #scenePeopleTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneBzdh">scene_bzdh =  #sceneBzdh#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneFkDate">scene_fk_date =  #sceneFkDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneSubmitDate">scene_submit_date =  #sceneSubmitDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
					<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tzzfySceneResult">
		INSERT INTO ${zzzcSchema}.t_zzfy_scene ( 
		<dynamic prepend=" ">
						<isNotEmpty prepend=" , " property="sceneId">scene_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="bizId">biz_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneTel">scene_tel</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneUserCode">scene_user_code</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneUserName">scene_user_name</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneDate">scene_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneCount">scene_count</isNotEmpty>
						<isNotEmpty prepend=" , " property="billCount">bill_count</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneBzTotal">scene_bz_total</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneTjTotal">scene_tj_total</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneSeTotal">scene_se_total</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneFyTotal">scene_fy_total</isNotEmpty>
						<isNotEmpty prepend=" , " property="scenePeopleTotal">scene_people_total</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneBzdh">scene_bzdh</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneFkDate">scene_fk_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneSubmitDate">scene_submit_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version</isNotEmpty>
				</dynamic>
		) VALUES (
    <dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="sceneId">#sceneId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="bizId">#bizId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneTel">#sceneTel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneUserCode">#sceneUserCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneUserName">#sceneUserName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneDate">#sceneDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneCount">#sceneCount#</isNotEmpty>
						<isNotEmpty prepend=" , " property="billCount">#billCount#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneBzTotal">#sceneBzTotal#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneTjTotal">#sceneTjTotal#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneSeTotal">#sceneSeTotal#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneFyTotal">#sceneFyTotal#</isNotEmpty>
						<isNotEmpty prepend=" , " property="scenePeopleTotal">#scenePeopleTotal#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneBzdh">#sceneBzdh#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneFkDate">#sceneFkDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneSubmitDate">#sceneSubmitDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.t_zzfy_scene
		WHERE 
		    scene_id = #value#

	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.t_zzfy_scene
		WHERE 
		<dynamic prepend=" ">
						<isNotEmpty prepend=" AND " property="sceneId">scene_id=#sceneId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="bizId">biz_id=#bizId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneTel">scene_tel=#sceneTel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneUserCode">scene_user_code=#sceneUserCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneUserName">scene_user_name=#sceneUserName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneDate">scene_date=#sceneDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneCount">scene_count=#sceneCount#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="billCount">bill_count=#billCount#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneBzTotal">scene_bz_total=#sceneBzTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneTjTotal">scene_tj_total=#sceneTjTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneSeTotal">scene_se_total=#sceneSeTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneFyTotal">scene_fy_total=#sceneFyTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="scenePeopleTotal">scene_people_total=#scenePeopleTotal#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneBzdh">scene_bzdh=#sceneBzdh#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneFkDate">scene_fk_date=#sceneFkDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="sceneSubmitDate">scene_submit_date=#sceneSubmitDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>	
	</delete>

	<update id="update" parameterClass="tzzfySceneResult">
		UPDATE  ${zzzcSchema}.t_zzfy_scene	
		SET 
		<dynamic prepend=" ">
					<isNotEmpty prepend=" , " property="sceneId">scene_id=#sceneId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="bizId">biz_id=#bizId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneTel">scene_tel=#sceneTel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneUserCode">scene_user_code=#sceneUserCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneUserName">scene_user_name=#sceneUserName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneDate">scene_date=#sceneDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneCount">scene_count=#sceneCount#</isNotEmpty>
						<isNotEmpty prepend=" , " property="billCount">bill_count =  #billCount#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneBzTotal">scene_bz_total=#sceneBzTotal#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneTjTotal">scene_tj_total=#sceneTjTotal#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneSeTotal">scene_se_total=#sceneSeTotal#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneFyTotal">scene_fy_total=#sceneFyTotal#</isNotEmpty>
						<isNotEmpty prepend=" , " property="scenePeopleTotal">scene_people_total=#scenePeopleTotal#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneBzdh">scene_bzdh=#sceneBzdh#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneFkDate">scene_fk_date=#sceneFkDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="sceneSubmitDate">scene_submit_date=#sceneSubmitDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>
		WHERE
		 scene_id =#sceneId#
	</update>


	<select id="sceneList"  parameterClass="hashmap" resultClass="tzzfySceneEx">
		SELECT
		scene_id AS "sceneId" ,
		biz_id AS "bizId" ,
		scene_tel AS "sceneTel" ,
		scene_user_code AS "sceneUserCode" ,
		scene_user_name AS "sceneUserName" ,
		scene_date AS "sceneDate" ,
		scene_count AS "sceneCount" ,
		bill_count AS "billCount" ,
		scene_bz_total AS "sceneBzTotal" ,
		scene_tj_total AS "sceneTjTotal" ,
		scene_se_total AS "sceneSeTotal" ,
		scene_fy_total AS "sceneFyTotal" ,
		scene_people_total AS "scenePeopleTotal" ,
		scene_bzdh AS "sceneBzdh" ,
		scene_fk_date AS "sceneFkDate" ,
		scene_submit_date AS "sceneSubmitDate" ,
		extra1 AS "extra1" ,
		extra2 AS "extra2" ,
		extra3 AS "extra3" ,
		extra4 AS "extra4" ,
		extra5 AS "extra5" ,
		del_status AS "delStatus" ,
		create_user_label AS "createUserLabel" ,
		create_date AS "createDate" ,
		update_user_label AS "updateUserLabel" ,
		update_date AS "updateDate" ,
		delete_user_label AS "deleteUserLabel" ,
		delete_date AS "deleteDate" ,
		record_version AS "recordVersion",
		project_name AS "projectName" ,
		project_num AS "projectNum" ,
		project_area AS "projectArea" ,
		srf_dwdept_code AS "srfDwdeptCode" ,
		srf_dwdept_name AS "srfDwdeptName" ,
		tgf_dwdept_code AS "tgfDwdeptCode" ,
		tgf_dwdept_name AS "tgfDwdeptName" ,
		tgf_fzr_code AS "tgfFzrCode" ,
		tgf_fzr_name AS "tgfFzrName" ,
		srf_fzr_code AS "srfFzrCode" ,
		srf_fzr_name AS "srfFzrName" ,
		status AS "status",
		current_operator AS "currentOperator" ,
		current_activity_name AS "currentActivityName"
		FROM (

		SELECT
		s.scene_id AS scene_id ,
		s.biz_id AS biz_id ,
		s.scene_tel AS scene_tel ,
		s.scene_user_code AS scene_user_code ,
		s.scene_user_name AS scene_user_name ,
		s.scene_date AS scene_date ,
		s.scene_count AS scene_count ,
		s.bill_count AS bill_count ,
		s.scene_bz_total AS scene_bz_total ,
		s.scene_tj_total AS scene_tj_total ,
		s.scene_se_total AS scene_se_total ,
		s.scene_fy_total AS scene_fy_total ,
		s.scene_people_total AS scene_people_total ,
		s.scene_bzdh AS scene_bzdh ,
		s.scene_fk_date AS scene_fk_date ,
		s.scene_submit_date AS scene_submit_date ,
		s.extra1 AS extra1 ,
		s.extra2 AS extra2 ,
		s.extra3 AS extra3 ,
		s.extra4 AS extra4 ,
		s.extra5 AS extra5 ,
		s.del_status AS del_status ,
		s.create_user_label AS create_user_label ,
		s.create_date AS create_date ,
		s.update_user_label AS update_user_label ,
		s.update_date AS update_date ,
		s.delete_user_label AS delete_user_label ,
		s.delete_date AS delete_date ,
		s.record_version AS record_version,
		m.project_name AS project_name ,
		m.project_num AS project_num ,
		m.project_area AS project_area ,
		m.srf_dwdept_code AS srf_dwdept_code ,
		m.srf_dwdept_name AS srf_dwdept_name ,
		m.tgf_dwdept_code AS tgf_dwdept_code ,
		m.tgf_dwdept_name AS tgf_dwdept_name ,
		m.tgf_fzr_code AS tgf_fzr_code ,
		m.tgf_fzr_name AS tgf_fzr_name ,
		m.srf_fzr_code AS srf_fzr_code ,
		m.srf_fzr_name AS srf_fzr_name ,
		m.status AS status,
		wf.current_operator AS current_operator ,
		wf.current_activity_name AS current_activity_name
		FROM
		${zzzcSchema}.T_ZZLX_MAIN m
		LEFT JOIN ${zzzcSchema}.t_zzfy_scene s ON
		s.biz_id = m.MAIN_ID
		LEFT JOIN ${ggmkSchema}.t_mpwf_flow_info wf ON
		wf.business_id = s.scene_id
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="sceneId">s.scene_id =  #sceneId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="bizId">s.biz_id =  #bizId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneTel">s.scene_tel =  #sceneTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneUserCode">s.scene_user_code =  #sceneUserCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneUserName">s.scene_user_name =  #sceneUserName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneDate">s.scene_date =  #sceneDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneCount">s.scene_count =  #sceneCount#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="billCount">s.bill_count =  #billCount#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneBzTotal">s.scene_bz_total =  #sceneBzTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneTjTotal">s.scene_tj_total =  #sceneTjTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneSeTotal">s.scene_se_total =  #sceneSeTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneFyTotal">s.scene_fy_total =  #sceneFyTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="scenePeopleTotal">s.scene_people_total =  #scenePeopleTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneBzdh">s.scene_bzdh = #sceneBzdh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneBzdhLike">s.scene_bzdh like '$sceneBzdhLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneFkDate">s.scene_fk_date =  #sceneFkDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneSubmitDate">s.scene_submit_date =  #sceneSubmitDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">s.extra1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">s.extra2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">s.extra3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">s.extra4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">s.extra5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">s.del_status =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">s.create_user_label =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">s.create_date =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">s.update_user_label =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">s.update_date =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">s.delete_user_label =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">s.delete_date =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">s.record_version =  #recordVersion#</isNotEmpty>

			<isNotEmpty prepend=" AND " property="mainId">m.main_id =  #mainId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">m.project_name = #projectName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectNameLike">m.project_name like '%$projectNameLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectNum">m.project_num =  #projectNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectNumLike">m.project_num like '%$projectNumLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectHtNum">m.project_ht_num =  #projectHtNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfDeptCode">m.tgf_dept_code =  #tgfDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="srfDwdeptCode">m.srf_dwdept_code =  #srfDwdeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="srfDwdeptCodeLike">m.srf_dwdept_code like '%$srfDwdeptCodeLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="srfDwdeptName">m.srf_dwdept_name =  #srfDwdeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="srfDwdeptNameLike">m.srf_dwdept_name like '%$srfDwdeptNameLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfDwdeptCode">m.tgf_dwdept_code =  #tgfDwdeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfDwdeptCodeLike">m.tgf_dwdept_code like '%$tgfDwdeptCodeLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfDwdeptName">m.tgf_dwdept_name =  #tgfDwdeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfDwdeptNameLike">m.tgf_dwdept_name like '%$tgfDwdeptNameLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfFzrCode">m.tgf_fzr_code =  #tgfFzrCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfFzrCodeLike">m.tgf_fzr_code like '%$tgfFzrCodeLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfFzrName">m.tgf_fzr_name =  #tgfFzrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfFzrNameLike">m.tgf_fzr_name like '%$tgfFzrNameLike$%'</isNotEmpty>

			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>


		UNION ALL

		SELECT
		s.scene_id AS scene_id ,
		s.biz_id AS biz_id ,
		s.scene_tel AS scene_tel ,
		s.scene_user_code AS scene_user_code ,
		s.scene_user_name AS scene_user_name ,
		s.scene_date AS scene_date ,
		s.scene_count AS scene_count ,
		s.bill_count AS bill_count ,
		s.scene_bz_total AS scene_bz_total ,
		s.scene_tj_total AS scene_tj_total ,
		s.scene_se_total AS scene_se_total ,
		s.scene_fy_total AS scene_fy_total ,
		s.scene_people_total AS scene_people_total ,
		s.scene_bzdh AS scene_bzdh ,
		s.scene_fk_date AS scene_fk_date ,
		s.scene_submit_date AS scene_submit_date ,
		s.extra1 AS extra1 ,
		s.extra2 AS extra2 ,
		s.extra3 AS extra3 ,
		s.extra4 AS extra4 ,
		s.extra5 AS extra5 ,
		s.del_status AS del_status ,
		s.create_user_label AS create_user_label ,
		s.create_date AS create_date ,
		s.update_user_label AS update_user_label ,
		s.update_date AS update_date ,
		s.delete_user_label AS delete_user_label ,
		s.delete_date AS delete_date ,
		s.record_version AS record_version,
		l.LIST_NAME AS project_name ,
		l.LIST_BH AS project_num ,
		l.LIST_AREA AS project_area ,
		l.srf_dwdept_code AS srf_dwdept_code ,
		l.srf_dwdept_name AS srf_dwdept_name ,
		l.tgf_dwdept_code AS tgf_dwdept_code ,
		l.tgf_dwdept_name AS tgf_dwdept_name ,
		l.tgf_fzr_code AS tgf_fzr_code ,
		l.tgf_fzr_name AS tgf_fzr_name ,
		l.srf_fzr_code AS srf_fzr_code ,
		l.srf_fzr_name AS srf_fzr_name ,
		'' AS status,
		wf.current_operator AS current_operator ,
		wf.current_activity_name AS current_activity_name
		FROM
		${zzzcSchema}.T_ZZLL_CONTACT_LIST l
		LEFT JOIN ${zzzcSchema}.t_zzfy_scene s  ON
		s.biz_id = l.LIST_ID
		LEFT JOIN ${ggmkSchema}.t_mpwf_flow_info wf ON
		wf.business_id = s.scene_id
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="sceneId">s.scene_id =  #sceneId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="bizId">s.biz_id =  #bizId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneTel">s.scene_tel =  #sceneTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneUserCode">s.scene_user_code =  #sceneUserCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneUserName">s.scene_user_name =  #sceneUserName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneDate">s.scene_date =  #sceneDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneCount">s.scene_count =  #sceneCount#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="billCount">bill_count =  #billCount#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneBzTotal">s.scene_bz_total =  #sceneBzTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneTjTotal">s.scene_tj_total =  #sceneTjTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneSeTotal">s.scene_se_total =  #sceneSeTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneFyTotal">s.scene_fy_total =  #sceneFyTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="scenePeopleTotal">s.scene_people_total =  #scenePeopleTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneBzdh">s.scene_bzdh = #sceneBzdh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneBzdhLike">s.scene_bzdh like '$sceneBzdhLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneFkDate">s.scene_fk_date =  #sceneFkDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneSubmitDate">s.scene_submit_date =  #sceneSubmitDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">s.extra1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">s.extra2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">s.extra3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">s.extra4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">s.extra5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">s.del_status =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">s.create_user_label =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">s.create_date =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">s.update_user_label =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">s.update_date =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">s.delete_user_label =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">s.delete_date =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">s.record_version =  #recordVersion#</isNotEmpty>

			<isNotEmpty prepend=" AND " property="mainId">l.list_ID =  #mainId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">l.list_name = #projectName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectNameLike">l.list_name like '%$projectNameLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectNum">l.list_bh =  #projectNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectNumLike">l.list_bh like '%$projectNumLike$%'</isNotEmpty>
		    <isNotEmpty prepend=" AND " property="tgfDeptCode">l.tgf_dept_code =  #tgfDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="srfDwdeptCode">l.srf_dwdept_code =  #srfDwdeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="srfDwdeptCodeLike">l.srf_dwdept_code like '%$srfDwdeptCodeLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="srfDwdeptName">l.srf_dwdept_name =  #srfDwdeptName#</isNotEmpty>
		    <isNotEmpty prepend=" AND " property="srfDwdeptNameLike">l.srf_dwdept_name like '%$srfDwdeptNameLike$%'</isNotEmpty>
		    <isNotEmpty prepend=" AND " property="tgfDwdeptCode">l.tgf_dwdept_code =  #tgfDwdeptCode#</isNotEmpty>
		    <isNotEmpty prepend=" AND " property="tgfDwdeptCodeLike">l.tgf_dwdept_code like '%$tgfDwdeptCodeLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfDwdeptName">l.tgf_dwdept_name =  #tgfDwdeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfDwdeptNameLike">l.tgf_dwdept_name like '%$tgfDwdeptNameLike$%'</isNotEmpty>
		    <isNotEmpty prepend=" AND " property="tgfFzrCode">l.tgf_fzr_code =  #tgfFzrCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfFzrCodeLike">l.tgf_fzr_code like '%$tgfFzrCodeLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfFzrName">l.tgf_fzr_name =  #tgfFzrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tgfFzrNameLike">l.tgf_fzr_name like '%$tgfFzrNameLike$%'</isNotEmpty>
		</dynamic>) A
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ desc</isNotEmpty>
	</select>

	<select id="queryContactList"  parameterClass="hashmap" resultClass="tzzfySceneEx">
		SELECT
		s.scene_id as "sceneId" ,
		s.biz_id as "bizId" ,
		s.scene_tel as "sceneTel" ,
		s.scene_user_code as "sceneUserCode" ,
		s.scene_user_name as "sceneUserName" ,
		s.scene_date as "sceneDate" ,
		s.scene_count as "sceneCount" ,
		s.bill_count as "billCount" ,
		s.scene_bz_total as "sceneBzTotal" ,
		s.scene_tj_total as "sceneTjTotal" ,
		s.scene_se_total as "sceneSeTotal" ,
		s.scene_fy_total as "sceneFyTotal" ,
		s.scene_people_total as "scenePeopleTotal" ,
		s.scene_bzdh as "sceneBzdh" ,
		s.scene_fk_date as "sceneFkDate" ,
		s.scene_submit_date as "sceneSubmitDate" ,
		s.extra1 as "extra1" ,
		s.extra2 as "extra2" ,
		s.extra3 as "extra3" ,
		s.extra4 as "extra4" ,
		s.extra5 as "extra5" ,
		s.del_status as "delStatus" ,
		s.create_user_label as "createUserLabel" ,
		s.create_date as "createDate" ,
		s.update_user_label as "updateUserLabel" ,
		s.update_date as "updateDate" ,
		s.delete_user_label as "deleteUserLabel" ,
		s.delete_date as "deleteDate" ,
		s.record_version as "recordVersion",
		l.list_id as "listId",
		l.list_lsh as "listLsh",
		l.list_bh as "listBh",
		l.list_name as "listName"
		FROM ${zzzcSchema}.t_zzfy_scene s
		LEFT JOIN ${zzzcSchema}.T_ZZLX_MAIN m ON s.biz_id = m.MAIN_ID
		LEFT JOIN ${zzzcSchema}.T_ZZLL_CONTACT_LIST ON l.list_name = m.project_name
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="sceneId">s.scene_id =  #sceneId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="bizId">s.biz_id =  #bizId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneTel">s.scene_tel =  #sceneTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneUserCode">s.scene_user_code =  #sceneUserCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneUserName">s.scene_user_name =  #sceneUserName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneDate">s.scene_date =  #sceneDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneCount">s.scene_count =  #sceneCount#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="billCount">bill_count =  #billCount#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneBzTotal">s.scene_bz_total =  #sceneBzTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneTjTotal">s.scene_tj_total =  #sceneTjTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneSeTotal">s.scene_se_total =  #sceneSeTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneFyTotal">s.scene_fy_total =  #sceneFyTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="scenePeopleTotal">s.scene_people_total =  #scenePeopleTotal#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneBzdh">s.scene_bzdh =  #sceneBzdh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneFkDate">s.scene_fk_date =  #sceneFkDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sceneSubmitDate">s.scene_submit_date =  #sceneSubmitDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">s.extra1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">s.extra2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">s.extra3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">s.extra4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">s.extra5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">s.del_status =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">s.create_user_label =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">s.create_date =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">s.update_user_label =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">s.update_date =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">s.delete_user_label =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">s.delete_date =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">s.record_version =  #recordVersion#</isNotEmpty>

			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="queryMain"  parameterClass="hashmap" resultClass="tzzfySceneEx">
		SELECT
			s.scene_id as "sceneId" ,
			s.biz_id as "bizId" ,
			s.scene_tel as "sceneTel" ,
			s.scene_user_code as "sceneUserCode" ,
			s.scene_user_name as "sceneUserName" ,
			s.scene_date as "sceneDate" ,
			s.scene_count as "sceneCount" ,
			s.bill_count as "billCount" ,
			s.scene_bz_total as "sceneBzTotal" ,
			s.scene_tj_total as "sceneTjTotal" ,
			s.scene_se_total as "sceneSeTotal" ,
			s.scene_fy_total as "sceneFyTotal" ,
			s.scene_people_total as "scenePeopleTotal" ,
			s.scene_bzdh as "sceneBzdh" ,
			s.scene_fk_date as "sceneFkDate" ,
			s.scene_submit_date as "sceneSubmitDate" ,
			s.extra1 as "extra1" ,
			s.extra2 as "extra2" ,
			s.extra3 as "extra3" ,
			s.extra4 as "extra4" ,
			s.extra5 as "extra5" ,
			s.del_status as "delStatus" ,
			s.create_user_label as "createUserLabel" ,
			s.create_date as "createDate" ,
			s.update_user_label as "updateUserLabel" ,
			s.update_date as "updateDate" ,
			s.delete_user_label as "deleteUserLabel" ,
			s.delete_date as "deleteDate" ,
			s.record_version as "recordVersion",

			m.main_id as "mainId" ,
			m.project_source as "projectSource" ,
			m.serial_no as "serialNo" ,
			m.project_name as "projectName" ,
			m.project_num as "projectNum" ,
			m.project_ht_num as "projectHtNum" ,
			m.project_ht_name as "projectHtName" ,
			m.project_type as "projectType" ,
			m.project_area as "projectArea" ,
			m.project_kind as "projectKind" ,
			m.report_kind as "reportKind" ,
			m.srf_dept_code as "srfDeptCode" ,
			m.srf_dept_name as "srfDeptName" ,
			m.srf_dwdept_code as "srfDwdeptCode" ,
			m.srf_dwdept_name as "srfDwdeptName" ,
			m.tgf_dept_code as "tgfDeptCode" ,
			m.tgf_dept_name as "tgfDeptName" ,
			m.tgf_dwdept_code as "tgfDwdeptCode" ,
			m.tgf_dwdept_name as "tgfDwdeptName" ,
			m.tgf_xmzg_code as "tgfXmzgCode" ,
			m.tgf_xmzg_name as "tgfXmzgName" ,
			m.srf_xmzg_code as "srfXmzgCode" ,
			m.srf_xmzg_name as "srfXmzgName" ,
			m.tgf_fzr_code as "tgfFzrCode" ,
			m.tgf_fzr_name as "tgfFzrName" ,
			m.srf_fzr_code as "srfFzrCode" ,
			m.srf_fzr_name as "srfFzrName" ,
			m.project_jjxy as "projectJjxy" ,
			m.project_jjxy_date as "projectJjxyDate" ,
			m.project_submit_date as "projectSubmitDate" ,
			m.project_sp_date as "projectSpDate" ,
			m.project_att_sp_date as "projectAttSpDate" ,
			m.project_start_date as "projectStartDate" ,
			m.project_end_date as "projectEndDate" ,
			m.project_lx_date as "projectLxDate" ,
			m.project_dj_sp_date as "projectDjSpDate" ,
			m.project_vis_date as "projectVisDate" ,
			m.project_isjs as "projectIsjs" ,
			m.project_js_date as "projectJsDate" ,
			m.project_jlje as "projectJlje" ,
			m.project_jt_date as "projectJtDate" ,
			m.del_status as "delStatus" ,
			m.status as "status" ,
			m.srf_tel as "srfTel" ,
			m.project_status as "projectStatus" ,
			m.tgf_xmfzr_tel as "tgfXmfzrTel"
		FROM ${zzzcSchema}.t_zzfy_scene s
		LEFT JOIN ${zzzcSchema}.t_zzlx_main m on s.biz_id = m.main_id
		<dynamic prepend="WHERE">
		<isNotEmpty prepend=" AND " property="sceneId">s.scene_id =  #sceneId#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="bizId">s.biz_id =  #bizId#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sceneTel">s.scene_tel =  #sceneTel#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sceneUserCode">s.scene_user_code =  #sceneUserCode#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sceneUserName">s.scene_user_name =  #sceneUserName#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sceneDate">s.scene_date =  #sceneDate#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sceneCount">s.scene_count =  #sceneCount#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="billCount">s.bill_count =  #billCount#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sceneBzTotal">s.scene_bz_total =  #sceneBzTotal#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sceneTjTotal">s.scene_tj_total =  #sceneTjTotal#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sceneSeTotal">s.scene_se_total =  #sceneSeTotal#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sceneFyTotal">s.scene_fy_total =  #sceneFyTotal#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="scenePeopleTotal">s.scene_people_total =  #scenePeopleTotal#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sceneBzdh">s.scene_bzdh =  #sceneBzdh#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sceneFkDate">s.scene_fk_date =  #sceneFkDate#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sceneSubmitDate">s.scene_submit_date =  #sceneSubmitDate#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="extra1">s.extra1 =  #extra1#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="extra2">s.extra2 =  #extra2#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="extra3">s.extra3 =  #extra3#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="extra4">s.extra4 =  #extra4#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="extra5">s.extra5 =  #extra5#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="delStatus">s.del_status =  #delStatus#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="createUserLabel">s.create_user_label =  #createUserLabel#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="createDate">s.create_date =  #createDate#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="updateUserLabel">s.update_user_label =  #updateUserLabel#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="updateDate">s.update_date =  #updateDate#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deleteUserLabel">s.delete_user_label =  #deleteUserLabel#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deleteDate">s.delete_date =  #deleteDate#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="recordVersion">s.record_version =  #recordVersion#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
	</dynamic>
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
</sqlMap>