<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tzzkjFrame">
    <typeAlias alias="costBudgetResult" type="com.baosight.bscdkj.zz.kj.domain.CostBudget"/>
    <typeAlias alias="tzzkjFrameResult" type="com.baosight.bscdkj.zz.kj.domain.TzzkjFrameEx"/>
    <typeAlias alias="tzzlxNeedResult" type="com.baosight.bscdkj.zz.lx.domain.TzzlxNeedEx"/>
    <typeAlias alias="tmpksClientResult" type="com.baosight.bscdkj.common.mp.domain.TmpksClient"/>
    <typeAlias alias="tmpksClientBankResult" type="com.baosight.bscdkj.common.mp.domain.TmpksClientBank"/>
    <select id="load" parameterClass="string" resultClass="tzzkjFrameResult">
        SELECT frame_id           as "frameId",
               frame_num          as "frameNum",
               frame_name         as "frameName",
               frame_year         as "frameYear",
               frame_area         as "frameArea",
               frame_srf_code     as "frameSrfCode",
               frame_srf_name     as "frameSrfName",
               frame_tgf_code     as "frameTgfCode",
               frame_tgf_name     as "frameTgfName",
               edit_user_code     as "editUserCode",
               edit_user_name     as "editUserName",
               edit_date          as "editDate",
               frame_last_total   as "frameLastTotal",
               frame_happen_total as "frameHappenTotal",
               frame_open_total   as "frameOpenTotal",
               frame_total        as "frameTotal",
               extra1             as "extra1",
               extra2             as "extra2",
               extra3             as "extra3",
               extra4             as "extra4",
               extra5             as "extra5",
               del_status         as "delStatus",
               create_user_label  as "createUserLabel",
               create_date        as "createDate",
               update_user_label  as "updateUserLabel",
               update_date        as "updateDate",
               delete_user_label  as "deleteUserLabel",
               delete_date        as "deleteDate",
               record_version     as "recordVersion"
        FROM ${zzzcSchema}.t_zzkj_frame
        WHERE frame_id = #frameId#

    </select>

    <select id="query" parameterClass="hashmap" resultClass="tzzkjFrameResult">
        SELECT
        frame_id as "frameId" ,
        frame_num as "frameNum" ,
        frame_name as "frameName" ,
        frame_year as "frameYear" ,
        frame_area as "frameArea" ,
        frame_srf_code as "frameSrfCode" ,
        frame_srf_name as "frameSrfName" ,
        frame_tgf_code as "frameTgfCode" ,
        frame_tgf_name as "frameTgfName" ,
        edit_user_code as "editUserCode" ,
        edit_user_name as "editUserName" ,
        edit_date as "editDate" ,
        frame_last_total as "frameLastTotal" ,
        frame_happen_total as "frameHappenTotal" ,
        frame_open_total as "frameOpenTotal" ,
        frame_total as "frameTotal" ,
        extra1 as "extra1",
        extra2 as "extra2",
        extra3 as "extra3",
        extra4 as "extra4",
        extra5 as "extra5",
        del_status as "delStatus",
        create_user_label as "createUserLabel",
        create_date as "createDate",
        update_user_label as "updateUserLabel",
        update_date as "updateDate",
        delete_user_label as "deleteUserLabel",
        delete_date as "deleteDate",
        record_version as "recordVersion",
        FLOW_ID as "processInstanceId",
        CURRENT_ACTIVITY as "activityCode",
        CURRENT_ACTIVITY_NAME as "activityName",
        CURRENT_OPERATOR as "currentOperator"
        FROM (SELECT
        f.*,
        A.FLOW_ID ,
        A.CURRENT_ACTIVITY,
        A.CURRENT_ACTIVITY_NAME,
        A.CURRENT_OPERATOR
        FROM ${zzzcSchema}.t_zzkj_frame f
        left join ${ggmkSchema}.T_MPWF_FLOW_INFO A on f.frame_id = A.BUSINESS_ID AND A.FLOW_CODE = 'ZZKJ_FRAME'
        <isNotEmpty prepend=" AND " property="assigneeId">
            CURRENT_OPERATOR LIKE '$%assigneeId%$'
        </isNotEmpty>
        WHERE f.del_status = 0
        <dynamic prepend="">
            <isNotEmpty prepend=" AND " property="editUserCode">edit_user_code = #editUserCode#</isNotEmpty>
            <isNotEmpty property="queryType">
                <isEqual prepend=" AND " property="queryType" compareValue="manage">
                    A.FLOW_ID is not null
                </isEqual>
                <isEqual prepend=" AND " property="queryType" compareValue="related">
                    A.FLOW_ID is not null
                </isEqual>
                <isEqual prepend=" AND " property="queryType" compareValue="list">
                    A.FLOW_ID is null
                </isEqual>
            </isNotEmpty>
            <isNotEmpty prepend=" OR " property="deptCodes">frame_srf_code in
                <iterate conjunction="," open="(" close=")" property="deptCodes">
                    #deptCodes[]#
                </iterate>
                or frame_tgf_code in
                <iterate conjunction="," open="(" close=")" property="deptCodes">
                    #deptCodes[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend=" OR " property="assigneeId">
                exists(SELECT v.BUSINESS_ID from ${ggmkSchema}.V_MPWF_YB v where v.BUSINESS_ID = FRAME_ID
                and v.ASSIGNEE_ID = #assigneeId#)
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
        ORDER BY A.LAST_TIME DESC
        )
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="frameId">frame_id = #frameId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameNum">locate(#frameNum#,frame_num) > 0</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameName">locate(#frameName#,frame_name) > 0</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameYear">frame_year = #frameYear#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameArea">frame_area = #frameArea#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameSrfCode">frame_srf_code = #frameSrfCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameSrfName">frame_srf_name = #frameSrfName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameTgfCode">frame_tgf_code = #frameTgfCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameTgfName">frame_tgf_name = #frameTgfName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="editUserCode">edit_user_code = #editUserCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="editUserName">edit_user_name = #editUserName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="editDate">edit_date = #editDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameLastTotal">frame_last_total = #frameLastTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameHappenTotal">frame_happen_total = #frameHappenTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameOpenTotal">frame_open_total = #frameOpenTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameTotal">frame_total = #frameTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra1">extra1 = #extra1#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra2">extra2 = #extra2#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra3">extra3 = #extra3#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra4">extra4 = #extra4#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra5">extra5 = #extra5#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="delStatus">del_status = #delStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">create_user_label = #createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createDate">create_date = #createDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label = #updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateDate">update_date = #updateDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label = #deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteDate">delete_date = #deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="recordVersion">record_version = #recordVersion#</isNotEmpty>
        </dynamic>
    </select>

    <select id="count" parameterClass="hashmap" resultClass="integer">
        SELECT count(*)
        FROM ${zzzcSchema}.t_zzkj_frame
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="frameId">frame_id = #frameId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameNum">frame_num = #frameNum#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameName">frame_name = #frameName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameYear">frame_year = #frameYear#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameArea">frame_area = #frameArea#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameSrfCode">frame_srf_code = #frameSrfCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameSrfName">frame_srf_name = #frameSrfName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameTgfCode">frame_tgf_code = #frameTgfCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameTgfName">frame_tgf_name = #frameTgfName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="editUserCode">edit_user_code = #editUserCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="editUserName">edit_user_name = #editUserName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="editDate">edit_date = #editDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameLastTotal">frame_last_total = #frameLastTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameHappenTotal">frame_happen_total = #frameHappenTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameOpenTotal">frame_open_total = #frameOpenTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameTotal">frame_total = #frameTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra1">extra1 = #extra1#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra2">extra2 = #extra2#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra3">extra3 = #extra3#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra4">extra4 = #extra4#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra5">extra5 = #extra5#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="delStatus">del_status = #delStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">create_user_label = #createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createDate">create_date = #createDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label = #updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateDate">update_date = #updateDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label = #deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteDate">delete_date = #deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="recordVersion">record_version = #recordVersion#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
    </select>

    <insert id="insert" parameterClass="tzzkjFrameResult">
        INSERT INTO ${zzzcSchema}.t_zzkj_frame (
        <dynamic prepend=" ">
            <isNotEmpty prepend=" , " property="frameId">frame_id</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameNum">frame_num</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameName">frame_name</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameYear">frame_year</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameArea">frame_area</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameSrfCode">frame_srf_code</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameSrfName">frame_srf_name</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameTgfCode">frame_tgf_code</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameTgfName">frame_tgf_name</isNotEmpty>
            <isNotEmpty prepend=" , " property="editUserCode">edit_user_code</isNotEmpty>
            <isNotEmpty prepend=" , " property="editUserName">edit_user_name</isNotEmpty>
            <isNotEmpty prepend=" , " property="editDate">edit_date</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameLastTotal">frame_last_total</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameHappenTotal">frame_happen_total</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameOpenTotal">frame_open_total</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameTotal">frame_total</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra1">extra1</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra2">extra2</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra3">extra3</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra4">extra4</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra5">extra5</isNotEmpty>
            <isNotEmpty prepend=" , " property="delStatus">del_status</isNotEmpty>
            <isNotEmpty prepend=" , " property="createUserLabel">create_user_label</isNotEmpty>
            <isNotEmpty prepend=" , " property="createDate">create_date</isNotEmpty>
            <isNotEmpty prepend=" , " property="updateUserLabel">update_user_label</isNotEmpty>
            <isNotEmpty prepend=" , " property="updateDate">update_date</isNotEmpty>
            <isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label</isNotEmpty>
            <isNotEmpty prepend=" , " property="deleteDate">delete_date</isNotEmpty>
            <isNotEmpty prepend=" , " property="recordVersion">record_version</isNotEmpty>
        </dynamic>
        ) VALUES (
        <dynamic prepend=" ">
            <isNotEmpty prepend=" , " property="frameId">#frameId#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameNum">#frameNum#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameName">#frameName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameYear">#frameYear#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameArea">#frameArea#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameSrfCode">#frameSrfCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameSrfName">#frameSrfName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameTgfCode">#frameTgfCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameTgfName">#frameTgfName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="editUserCode">#editUserCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="editUserName">#editUserName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="editDate">#editDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameLastTotal">#frameLastTotal#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameHappenTotal">#frameHappenTotal#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameOpenTotal">#frameOpenTotal#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameTotal">#frameTotal#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
            <isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
            <isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
        </dynamic>
        )
    </insert>

    <delete id="delete" parameterClass="string">
        DELETE
        FROM ${zzzcSchema}.t_zzkj_frame
        WHERE frame_id = #value#

    </delete>

    <delete id="deleteByC" parameterClass="hashmap">
        DELETE FROM ${zzzcSchema}.t_zzkj_frame
        WHERE
        <dynamic prepend=" ">
            <isNotEmpty prepend=" AND " property="frameId">frame_id=#frameId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameNum">frame_num=#frameNum#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameName">frame_name=#frameName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameYear">frame_year=#frameYear#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameArea">frame_area=#frameArea#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameSrfCode">frame_srf_code=#frameSrfCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameSrfName">frame_srf_name=#frameSrfName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameTgfCode">frame_tgf_code=#frameTgfCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameTgfName">frame_tgf_name=#frameTgfName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="editUserCode">edit_user_code=#editUserCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="editUserName">edit_user_name=#editUserName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="editDate">edit_date=#editDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameLastTotal">frame_last_total=#frameLastTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameHappenTotal">frame_happen_total=#frameHappenTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameOpenTotal">frame_open_total=#frameOpenTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameTotal">frame_total=#frameTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra1">extra1=#extra1#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra2">extra2=#extra2#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra3">extra3=#extra3#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra4">extra4=#extra4#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra5">extra5=#extra5#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="delStatus">del_status=#delStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createDate">create_date=#createDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateDate">update_date=#updateDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
        </dynamic>
    </delete>

    <update id="update" parameterClass="tzzkjFrameResult">
        UPDATE ${zzzcSchema}.t_zzkj_frame
        SET
        <dynamic prepend=" ">
            <isNotEmpty prepend=" , " property="frameId">frame_id=#frameId#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameNum">frame_num=#frameNum#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameName">frame_name=#frameName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameYear">frame_year=#frameYear#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameArea">frame_area=#frameArea#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameSrfCode">frame_srf_code=#frameSrfCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameSrfName">frame_srf_name=#frameSrfName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameTgfCode">frame_tgf_code=#frameTgfCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameTgfName">frame_tgf_name=#frameTgfName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="editUserCode">edit_user_code=#editUserCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="editUserName">edit_user_name=#editUserName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="editDate">edit_date=#editDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameLastTotal">frame_last_total=#frameLastTotal#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameHappenTotal">frame_happen_total=#frameHappenTotal#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameOpenTotal">frame_open_total=#frameOpenTotal#</isNotEmpty>
            <isNotEmpty prepend=" , " property="frameTotal">frame_total=#frameTotal#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra1">extra1=#extra1#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra2">extra2=#extra2#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra3">extra3=#extra3#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra4">extra4=#extra4#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra5">extra5=#extra5#</isNotEmpty>
            <isNotEmpty prepend=" , " property="delStatus">del_status=#delStatus#</isNotEmpty>
            <isNotEmpty prepend=" , " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" , " property="createDate">create_date=#createDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" , " property="updateDate">update_date=#updateDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" , " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
        </dynamic>
        WHERE
        frame_id =#frameId#
    </update>
    <!--	抓取本年度审批结束的年度计划-->
    <select id="grabPlanNeed" parameterClass="hashmap" resultClass="tzzkjFrameResult">
        select SRF_DWDEPT_CODE       as "srfDeptCode",
               TGF_DWDEPT_CODE       as "tgfDeptCode",
               listagg(NEED_ID, ',') as "planNeedId"
        from ${kjglSchema}.T_KTTG_PLAN_NEED
        where PLAN_YEAR = YEAR(CURRENT timestamp)
          and PROJECT_AREA = 'gfn'
          and STATUS = 'end'
          and DEL_STATUS = 0
          and (FRAME_ID is null
            or trim(FRAME_ID) = '')
        group by SRF_DWDEPT_CODE, TGF_DWDEPT_CODE
    </select>

    <!--    更新对应年度计划的框架协议-->
    <update id="updatePlanNeed" parameterClass="tzzkjFrameResult">
        UPDATE ${kjglSchema}.T_KTTG_PLAN_NEED
        SET frame_id = #frameId# WHERE NEED_ID in
        <iterate conjunction="," open="(" close=")" property="needIds">
            #needIds[]#
        </iterate>
    </update>

    <!--查询年度计划-->
    <select id="queryPlanDeed" parameterClass="hashmap" resultClass="tzzlxNeedResult">

        SELECT
        need_id as "needId" ,
        plan_id as "planId" ,
        frame_id as "frameId" ,
        main_id as "mainId" ,
        serial_no as "serialNo" ,
        year_plan_no as "yearPlanNo" ,
        project_name as "projectName" ,
        project_num as "projectNum" ,
        plan_year as "planYear" ,
        plan_month as "planMonth" ,
        tcr_user_code as "tcrUserCode" ,
        tcr_user_name as "tcrUserName" ,
        tcr_contact_tel as "tcrContactTel" ,
        project_type as "projectType" ,
        project_area as "projectArea" ,
        srf_dept_code as "srfDeptCode" ,
        srf_dept_name as "srfDeptName" ,
        srf_dwdept_code as "srfDwdeptCode" ,
        srf_dwdept_name as "srfDwdeptName" ,
        tgf_dept_code as "tgfDeptCode" ,
        tgf_dept_name as "tgfDeptName" ,
        tgf_dwdept_code as "tgfDwdeptCode" ,
        tgf_dwdept_name as "tgfDwdeptName" ,
        tgf_xmzg_code as "tgfXmzgCode" ,
        tgf_xmzg_name as "tgfXmzgName" ,
        srf_xmzg_code as "srfXmzgCode" ,
        srf_xmzg_name as "srfXmzgName" ,
        tgf_fzr_code as "tgfFzrCode" ,
        tgf_fzr_name as "tgfFzrName" ,
        tgf_xmfzr_tel as "tgfXmfzrTel" ,
        srf_fzr_code as "srfFzrCode" ,
        srf_fzr_name as "srfFzrName" ,
        srf_tel as "srfTel" ,
        finish_date as "finishDate" ,
        qt_dept_code as "qtDeptCode" ,
        qt_dept_name as "qtDeptName" ,
        promotion_dept_code as "promotionDeptCode" ,
        promotion_dept_name as "promotionDeptName" ,
        plan_xf_date as "planXfDate" ,
        is_jssc as "isJssc" ,
        xzczwt as "xzczwt" ,
        yyqkfzqs as "yyqkfzqs" ,
        jymb as "jymb" ,
        yqcsxg as "yqcsxg" ,
        qqjlqk as "qqjlqk" ,
        ys_result as "ysResult" ,
        ps_result as "psResult" ,
        status as "status" ,
        comment_text as "commentText" ,
        is_bp as "isBp" ,
        extra1 as "extra1" ,
        extra2 as "extra2" ,
        extra3 as "extra3" ,
        extra4 as "extra4" ,
        extra5 as "extra5" ,
        del_status as "delStatus" ,
        create_user_label as "createUserLabel" ,
        create_date as "createDate" ,
        update_user_label as "updateUserLabel" ,
        update_date as "updateDate" ,
        delete_user_label as "deleteUserLabel" ,
        delete_date as "deleteDate" ,
        record_version as "recordVersion" ,
        plan_domain as "planDomain" ,
        srf_dept_path as "srfDeptPath"
        FROM ${kjglSchema}.t_kttg_plan_need
        WHERE STATUS='end' and DEL_STATUS = 0
        <isNotEmpty prepend=" AND " property="needId">need_id = #needId#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="planId">plan_id = #planId#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="frameId">frame_id = #frameId#</isNotEmpty>
        <isEmpty prepend=" AND " property="frameId">FRAME_ID is null or trim (FRAME_ID) = ''</isEmpty>
        <isNotEmpty prepend=" AND " property="mainId">main_id = #mainId#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="serialNo">serial_no = #serialNo#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="yearPlanNo">year_plan_no = #yearPlanNo#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="projectName">project_name = #projectName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="projectNum">project_num = #projectNum#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="planYear">plan_year = #planYear#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="planMonth">plan_month = #planMonth#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tcrUserCode">tcr_user_code = #tcrUserCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tcrUserName">tcr_user_name = #tcrUserName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tcrContactTel">tcr_contact_tel = #tcrContactTel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="projectType">project_type = #projectType#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="projectArea">project_area = #projectArea#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfDeptCode">srf_dept_code = #srfDeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfDeptName">srf_dept_name = #srfDeptName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfDwdeptCode">srf_dwdept_code = #srfDwdeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfDwdeptName">srf_dwdept_name = #srfDwdeptName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfDeptCode">tgf_dept_code = #tgfDeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfDeptName">tgf_dept_name = #tgfDeptName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfDwdeptCode">tgf_dwdept_code = #tgfDwdeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfDwdeptName">tgf_dwdept_name = #tgfDwdeptName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfXmzgCode">tgf_xmzg_code = #tgfXmzgCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfXmzgName">tgf_xmzg_name = #tgfXmzgName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfXmzgCode">srf_xmzg_code = #srfXmzgCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfXmzgName">srf_xmzg_name = #srfXmzgName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfFzrCode">tgf_fzr_code = #tgfFzrCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfFzrName">tgf_fzr_name = #tgfFzrName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfXmfzrTel">tgf_xmfzr_tel = #tgfXmfzrTel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfFzrCode">srf_fzr_code = #srfFzrCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfFzrName">srf_fzr_name = #srfFzrName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfTel">srf_tel = #srfTel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="finishDate">finish_date = #finishDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="qtDeptCode">qt_dept_code = #qtDeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="qtDeptName">qt_dept_name = #qtDeptName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="promotionDeptCode">promotion_dept_code = #promotionDeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="promotionDeptName">promotion_dept_name = #promotionDeptName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="planXfDate">plan_xf_date = #planXfDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isJssc">is_jssc = #isJssc#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="xzczwt">xzczwt = #xzczwt#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="yyqkfzqs">yyqkfzqs = #yyqkfzqs#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="jymb">jymb = #jymb#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="yqcsxg">yqcsxg = #yqcsxg#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="qqjlqk">qqjlqk = #qqjlqk#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="ysResult">ys_result = #ysResult#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="psResult">ps_result = #psResult#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">status = #status#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="commentText">comment_text = #commentText#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isBp">is_bp = #isBp#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="extra1">extra1 = #extra1#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="extra2">extra2 = #extra2#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="extra3">extra3 = #extra3#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="extra4">extra4 = #extra4#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="extra5">extra5 = #extra5#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="delStatus">del_status = #delStatus#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="createUserLabel">create_user_label = #createUserLabel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="createDate">create_date = #createDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label = #updateUserLabel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="updateDate">update_date = #updateDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label = #deleteUserLabel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="deleteDate">delete_date = #deleteDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="recordVersion">record_version = #recordVersion#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="planDomain">plan_domain = #planDomain#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfDeptPath">srf_dept_path = #srfDeptPath#</isNotEmpty>

        <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>


    <select id="queryTaskId" parameterClass="hashmap" resultClass="string">
        SELECT TASK_ID as "taskId" FROM ${platSchema}.TEWPT00 WHERE STATE = 'open'
        <isNotEmpty prepend=" AND " property="processCode">PROCESS_KEY = #processCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="assigneeId">ASSIGNEE_ID = #assigneeId#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="processInstanceId">PROCESS_INSTANCE_ID = #processInstanceId#</isNotEmpty>
    </select>

    <select id="queryFlowUser" parameterClass="hashmap" resultClass="string">
        select ASSIGNEE_FULLNAME as "srfXmzg" from ${ggmkSchema}.V_MPWF_YB
        where  TASK_DEF_KEY = 'Manual4'
        and BUSINESS_ID = #frameId#
    </select>


    <!--查询框架协议项目清单-->
    <select id="queryPlanMain" parameterClass="hashmap" resultClass="tzzlxNeedResult">

        SELECT
        need_id as "needId" ,
        plan_id as "planId" ,
        frame_id as "frameId" ,
        main_id as "mainId" ,
        serial_no as "serialNo" ,
        year_plan_no as "yearPlanNo" ,
        project_name as "projectName" ,
        project_num as "projectNum" ,
        plan_year as "planYear" ,
        plan_month as "planMonth" ,
        tcr_user_code as "tcrUserCode" ,
        tcr_user_name as "tcrUserName" ,
        tcr_contact_tel as "tcrContactTel" ,
        project_type as "projectType" ,
        project_area as "projectArea" ,
        srf_dept_code as "srfDeptCode" ,
        srf_dept_name as "srfDeptName" ,
        srf_dwdept_code as "srfDwdeptCode" ,
        srf_dwdept_name as "srfDwdeptName" ,
        tgf_dept_code as "tgfDeptCode" ,
        tgf_dept_name as "tgfDeptName" ,
        tgf_dwdept_code as "tgfDwdeptCode" ,
        tgf_dwdept_name as "tgfDwdeptName" ,
        tgf_xmzg_code as "tgfXmzgCode" ,
        tgf_xmzg_name as "tgfXmzgName" ,
        srf_xmzg_code as "srfXmzgCode" ,
        srf_xmzg_name as "srfXmzgName" ,
        tgf_fzr_code as "tgfFzrCode" ,
        tgf_fzr_name as "tgfFzrName" ,
        tgf_xmfzr_tel as "tgfXmfzrTel" ,
        srf_fzr_code as "srfFzrCode" ,
        srf_fzr_name as "srfFzrName" ,
        srf_tel as "srfTel" ,
        finish_date as "finishDate" ,
        qt_dept_code as "qtDeptCode" ,
        qt_dept_name as "qtDeptName" ,
        promotion_dept_code as "promotionDeptCode" ,
        promotion_dept_name as "promotionDeptName" ,
        plan_xf_date as "planXfDate" ,
        is_jssc as "isJssc" ,
        xzczwt as "xzczwt" ,
        yyqkfzqs as "yyqkfzqs" ,
        jymb as "jymb" ,
        yqcsxg as "yqcsxg" ,
        qqjlqk as "qqjlqk" ,
        ys_result as "ysResult" ,
        ps_result as "psResult" ,
        status as "status" ,
        comment_text as "commentText" ,
        is_bp as "isBp" ,
        extra1 as "extra1" ,
        extra2 as "extra2" ,
        extra3 as "extra3" ,
        extra4 as "extra4" ,
        extra5 as "extra5" ,
        del_status as "delStatus" ,
        create_user_label as "createUserLabel" ,
        create_date as "createDate" ,
        update_user_label as "updateUserLabel" ,
        update_date as "updateDate" ,
        delete_user_label as "deleteUserLabel" ,
        delete_date as "deleteDate" ,
        record_version as "recordVersion" ,
        plan_domain as "planDomain" ,
        srf_dept_path as "srfDeptPath"
        FROM ${kjglSchema}.t_kttg_plan_need
        WHERE STATUS='end' and DEL_STATUS = 0
        <isNotEmpty prepend=" AND " property="needId">need_id = #needId#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="planId">plan_id = #planId#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="frameId">frame_id = #frameId#</isNotEmpty>
        <isEmpty prepend=" AND " property="frameId">FRAME_ID is null or trim (FRAME_ID) = ''</isEmpty>
        <isNotEmpty prepend=" AND " property="mainId">main_id = #mainId#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="serialNo">serial_no = #serialNo#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="yearPlanNo">year_plan_no = #yearPlanNo#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="projectName">project_name = #projectName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="projectNum">project_num = #projectNum#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="planYear">plan_year = #planYear#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="planMonth">plan_month = #planMonth#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tcrUserCode">tcr_user_code = #tcrUserCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tcrUserName">tcr_user_name = #tcrUserName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tcrContactTel">tcr_contact_tel = #tcrContactTel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="projectType">project_type = #projectType#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="projectArea">project_area = #projectArea#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfDeptCode">srf_dept_code = #srfDeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfDeptName">srf_dept_name = #srfDeptName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfDwdeptCode">srf_dwdept_code = #srfDwdeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfDwdeptName">srf_dwdept_name = #srfDwdeptName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfDeptCode">tgf_dept_code = #tgfDeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfDeptName">tgf_dept_name = #tgfDeptName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfDwdeptCode">tgf_dwdept_code = #tgfDwdeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfDwdeptName">tgf_dwdept_name = #tgfDwdeptName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfXmzgCode">tgf_xmzg_code = #tgfXmzgCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfXmzgName">tgf_xmzg_name = #tgfXmzgName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfXmzgCode">srf_xmzg_code = #srfXmzgCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfXmzgName">srf_xmzg_name = #srfXmzgName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfFzrCode">tgf_fzr_code = #tgfFzrCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfFzrName">tgf_fzr_name = #tgfFzrName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="tgfXmfzrTel">tgf_xmfzr_tel = #tgfXmfzrTel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfFzrCode">srf_fzr_code = #srfFzrCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfFzrName">srf_fzr_name = #srfFzrName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfTel">srf_tel = #srfTel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="finishDate">finish_date = #finishDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="qtDeptCode">qt_dept_code = #qtDeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="qtDeptName">qt_dept_name = #qtDeptName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="promotionDeptCode">promotion_dept_code = #promotionDeptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="promotionDeptName">promotion_dept_name = #promotionDeptName#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="planXfDate">plan_xf_date = #planXfDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isJssc">is_jssc = #isJssc#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="xzczwt">xzczwt = #xzczwt#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="yyqkfzqs">yyqkfzqs = #yyqkfzqs#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="jymb">jymb = #jymb#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="yqcsxg">yqcsxg = #yqcsxg#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="qqjlqk">qqjlqk = #qqjlqk#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="ysResult">ys_result = #ysResult#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="psResult">ps_result = #psResult#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="status">status = #status#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="commentText">comment_text = #commentText#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isBp">is_bp = #isBp#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="extra1">extra1 = #extra1#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="extra2">extra2 = #extra2#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="extra3">extra3 = #extra3#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="extra4">extra4 = #extra4#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="extra5">extra5 = #extra5#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="delStatus">del_status = #delStatus#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="createUserLabel">create_user_label = #createUserLabel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="createDate">create_date = #createDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label = #updateUserLabel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="updateDate">update_date = #updateDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label = #deleteUserLabel#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="deleteDate">delete_date = #deleteDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="recordVersion">record_version = #recordVersion#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="planDomain">plan_domain = #planDomain#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="srfDeptPath">srf_dept_path = #srfDeptPath#</isNotEmpty>

        <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <select id="queryFrame" parameterClass="hashmap" resultClass="tzzkjFrameResult">
        SELECT
        frame_id as "frameId" ,
        frame_num as "frameNum" ,
        frame_name as "frameName" ,
        frame_year as "frameYear" ,
        frame_area as "frameArea" ,
        frame_srf_code as "frameSrfCode" ,
        frame_srf_name as "frameSrfName" ,
        frame_tgf_code as "frameTgfCode" ,
        frame_tgf_name as "frameTgfName" ,
        edit_user_code as "editUserCode" ,
        edit_user_name as "editUserName" ,
        edit_date as "editDate" ,
        frame_last_total as "frameLastTotal" ,
        frame_happen_total as "frameHappenTotal" ,
        frame_open_total as "frameOpenTotal" ,
        frame_total as "frameTotal" ,
        extra1 as "extra1",
        extra2 as "extra2",
        extra3 as "extra3",
        extra4 as "extra4",
        extra5 as "extra5",
        del_status as "delStatus",
        create_user_label as "createUserLabel",
        create_date as "createDate",
        update_user_label as "updateUserLabel",
        update_date as "updateDate",
        delete_user_label as "deleteUserLabel",
        delete_date as "deleteDate",
        record_version as "recordVersion"
        FROM ${zzzcSchema}.t_zzkj_frame
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="frameId">frame_id = #frameId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameNum">frame_num = #frameNum#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameName">frame_name = #frameName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameYear">frame_year = #frameYear#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameArea">frame_area = #frameArea#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameSrfCode">frame_srf_code = #frameSrfCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameSrfName">frame_srf_name = #frameSrfName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameTgfCode">frame_tgf_code = #frameTgfCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameTgfName">frame_tgf_name = #frameTgfName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="editUserCode">edit_user_code = #editUserCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="editUserName">edit_user_name = #editUserName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="editDate">edit_date = #editDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameLastTotal">frame_last_total = #frameLastTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameHappenTotal">frame_happen_total = #frameHappenTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameOpenTotal">frame_open_total = #frameOpenTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="frameTotal">frame_total = #frameTotal#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra1">extra1 = #extra1#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra2">extra2 = #extra2#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra3">extra3 = #extra3#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra4">extra4 = #extra4#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra5">extra5 = #extra5#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="delStatus">del_status = #delStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">create_user_label = #createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createDate">create_date = #createDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label = #updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateDate">update_date = #updateDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label = #deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteDate">delete_date = #deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="recordVersion">record_version = #recordVersion#</isNotEmpty>
        </dynamic>
    </select>

    <select id="queryCLIENT"  parameterClass="hashmap" resultClass="tmpksClientResult">
        SELECT
        CLIENT_ID as "clientId" ,
        SYS_CODE as "sysCode" ,
        USER_NUM as "userNum" ,
        COUNTRY_NAME_ABBR as "countryNameAbbr" ,
        CHINESE_USER_NAME as "chineseUserName" ,
        CHINESE_USNM_ABBR as "chineseUsnmAbbr" ,
        ENGLISH_USER_NAME as "englishUserName" ,
        ENGLISH_USNM_ABBR as "englishUsnmAbbr" ,
        TAX_NUM as "taxNum" ,
        BUSIN_NUM as "businNum" ,
        ID_CARD as "idCard" ,
        CORP_FLAG as "corpFlag" ,
        CORP_CODE as "corpCode" ,
        ORG_CODE as "orgCode" ,
        OVERSEAS_ID_CARD as "overseasIdCard" ,
        ADDR_REGST as "addrRegst" ,
        CHINESE_ADDRESS as "chineseAddress" ,
        APPLICE_ID as "appliceId" ,
        APPLICE_NAME as "appliceName" ,
        APPLICE_TEL as "appliceTel" ,
        APPLICE_TIME as "appliceTime" ,
        EMAIL as "email" ,
        CANCEL_REASON as "cancelReason" ,
        CLIENT_REMARK as "clientRemark" ,
        SYN_STATUS as "synStatus" ,
        SYN_MESSAGE as "synMessage" ,
        SYN_TIME as "synTime" ,
        DEL_STATUS as "delStatus" ,
        CREATE_USER_LABEL as "createUserLabel" ,
        CREATE_DATE as "createDate" ,
        UPDATE_USER_LABEL as "updateUserLabel" ,
        UPDATE_DATE as "updateDate" ,
        DELETE_USER_LABEL as "deleteUserLabel" ,
        DELETE_DATE as "deleteDate" ,
        RECORD_VERSION as "recordVersion" ,
        LINKMAN as "linkman" ,
        LINKMAN_TEL as "linkmanTel"
        FROM ${ggmkSchema}.T_MPKS_CLIENT
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="clientId">CLIENT_ID =  #clientId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sysCode">SYS_CODE =  #sysCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="userNum">USER_NUM =  #userNum#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="countryNameAbbr">COUNTRY_NAME_ABBR =  #countryNameAbbr#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="chineseUserName">CHINESE_USER_NAME =  #chineseUserName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="chineseUsnmAbbr">CHINESE_USNM_ABBR =  #chineseUsnmAbbr#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="englishUserName">ENGLISH_USER_NAME =  #englishUserName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="englishUsnmAbbr">ENGLISH_USNM_ABBR =  #englishUsnmAbbr#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="taxNum">TAX_NUM =  #taxNum#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="businNum">BUSIN_NUM =  #businNum#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="idCard">ID_CARD =  #idCard#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="corpFlag">CORP_FLAG =  #corpFlag#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="corpCode">CORP_CODE =  #corpCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="orgCode">ORG_CODE =  #orgCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="overseasIdCard">OVERSEAS_ID_CARD =  #overseasIdCard#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="addrRegst">ADDR_REGST =  #addrRegst#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="chineseAddress">CHINESE_ADDRESS =  #chineseAddress#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="appliceId">APPLICE_ID =  #appliceId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="appliceName">APPLICE_NAME =  #appliceName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="appliceTel">APPLICE_TEL =  #appliceTel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="appliceTime">APPLICE_TIME =  #appliceTime#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="email">EMAIL =  #email#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="cancelReason">CANCEL_REASON =  #cancelReason#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="clientRemark">CLIENT_REMARK =  #clientRemark#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="synStatus">SYN_STATUS =  #synStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="synMessage">SYN_MESSAGE =  #synMessage#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="synTime">SYN_TIME =  #synTime#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="linkman">LINKMAN =  #linkman#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="linkmanTel">LINKMAN_TEL =  #linkmanTel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
    </select>

    <select id="queryCLIENTBANK"  parameterClass="hashmap" resultClass="tmpksClientBankResult">
        SELECT
        CLIENT_BANK_ID as "clientBankId" ,
        CLIENT_ID as "clientId" ,
        SYS_CODE as "sysCode" ,
        USER_NUM as "userNum" ,
        ACCOUNT_SEQ_NUM as "accountSeqNum" ,
        ACCOUNT_NUM as "accountNum" ,
        ACCOUNT_NAME as "accountName" ,
        BANK_ID as "bankId" ,
        BANK_BRANCH_NAME as "bankBranchName" ,
        AREA_CODE as "areaCode" ,
        APPLICE_ID as "appliceId" ,
        APPLICE_NAME as "appliceName" ,
        APPLICE_TEL as "appliceTel" ,
        APPLICE_TIME as "appliceTime" ,
        SYN_STATUS as "synStatus" ,
        SYN_MESSAGE as "synMessage" ,
        SYN_TIME as "synTime" ,
        DEL_STATUS as "delStatus" ,
        CREATE_USER_LABEL as "createUserLabel" ,
        CREATE_DATE as "createDate" ,
        UPDATE_USER_LABEL as "updateUserLabel" ,
        UPDATE_DATE as "updateDate" ,
        DELETE_USER_LABEL as "deleteUserLabel" ,
        DELETE_DATE as "deleteDate" ,
        RECORD_VERSION as "recordVersion"
        FROM ${ggmkSchema}.T_MPKS_CLIENT_BANK
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="clientBankId">CLIENT_BANK_ID =  #clientBankId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="clientId">CLIENT_ID =  #clientId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sysCode">SYS_CODE =  #sysCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="userNum">USER_NUM =  #userNum#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="accountSeqNum">ACCOUNT_SEQ_NUM =  #accountSeqNum#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="accountNum">ACCOUNT_NUM =  #accountNum#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="accountName">ACCOUNT_NAME =  #accountName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="bankId">BANK_ID =  #bankId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="bankBranchName">BANK_BRANCH_NAME =  #bankBranchName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="areaCode">AREA_CODE =  #areaCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="appliceId">APPLICE_ID =  #appliceId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="appliceName">APPLICE_NAME =  #appliceName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="appliceTel">APPLICE_TEL =  #appliceTel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="appliceTime">APPLICE_TIME =  #appliceTime#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="synStatus">SYN_STATUS =  #synStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="synMessage">SYN_MESSAGE =  #synMessage#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="synTime">SYN_TIME =  #synTime#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
    </select>



    <!--查询项目费用-->
    <select id="costBudget" parameterClass="hashmap" resultClass="hashmap">
        select
            m.MAIN_ID                                                        as "mainId",
            m.PROJECT_NUM                                                    as "projectNum",
            m.PROJECT_NAME                                                   as "projectName",
            m.TGF_DEPT_NAME                                                  as "tgfDeptName",
            m.TGF_FZR_NAME || m.TGF_FZR_CODE                                 as "tgfFzr",
            m.TGF_XMZG_NAME || m.TGF_XMZG_CODE                               as "tgfXmzg",
            m.SRF_DEPT_NAME                                                  as "srfDeptName",
            m.SRF_FZR_NAME || m.SRF_FZR_CODE                                 as "sgfFzr",
            m.SRF_XMZG_NAME || m.SRF_XMZG_CODE                               as "sgfXmzg",
            cast(c.TRAVEL_COST * 10000 as decimal(16, 2))                    as "travelCostBudget",
            v.SCENE_FY_TOTAL                                                 as "travelCostActual",
            v1.SCENE_FY_TOTAL                                                as "travelCostYear",
            cast(c.COM_COST * 10000 as decimal(16, 2))                       as "comCostBudget",
            v.SCENE_TSF_TOTAL                                                as "comCostActual",
            v1.SCENE_TSF_TOTAL                                               as "comCostYear",
            cast(c.LABOR_COST * 10000 as decimal(16, 2))                     as "laborCostBudget",
            v.PEOPLE_TOTAL                                                   as "laborCostActual",
            v1.PEOPLE_TOTAL                                                  as "laborCostYear",
            cast(c.OTHER_COST * 10000 as decimal(16, 2))                     as "otherCostBudget",
            v.LONG_QT_FYHJ                                                   as "otherCostActual",
            v1.LONG_QT_FYHJ                                                  as "otherCostYear",
            cast(v1.PEOPLE_TOTAL * 0.12 as decimal(16, 2))                   as "managementCost",
            cast(v1.SCENE_FY_TOTAL + v1.SCENE_TSF_TOTAL +
                 v1.PEOPLE_TOTAL + v1.PEOPLE_TOTAL * 0.12 as decimal(16, 2)) as "subtotal"
        from ${zzzcSchema}.T_ZZLX_MAIN m
                 left join (select p.BIZ_ID,
                                   sum(TRAVEL_COST) as TRAVEL_COST,<!--差旅费预算-->
                                   sum(COM_COST)    as COM_COST,<!--通讯费预算-->
                                   sum(LABOR_COST)  as LABOR_COST,<!--人工费预算-->
                                   sum(OTHER_COST)  as OTHER_COST<!--其他人预算-->
                            from ${zzzcSchema}.T_ZZLX_TEACH_PLANTASK p
                                     left join ${zzzcSchema}.T_ZZGG_TEACH_COST c on p.TASK_ID = c.BIZ_ID
                            group by p.BIZ_ID) c on m.MAIN_ID = c.BIZ_ID
                 left join (select BIZ_ID,
                                   sum(SCENE_FY_TOTAL)  as SCENE_FY_TOTAL,<!--差旅费当年实际-->
                                   sum(SCENE_TSF_TOTAL) as SCENE_TSF_TOTAL,<!--通讯费当年实际-->
                                   sum(PEOPLE_TOTAL)    as PEOPLE_TOTAL,<!--人工费当年实际-->
                                   sum(LONG_QT_FYHJ)    as LONG_QT_FYHJ<!--其他费当年实际-->
                            from ${zzzcSchema}.V_ZZLX_FYMX_INFO
                            group by BIZ_ID) v on m.MAIN_ID = v.BIZ_ID
                 left join ${zzzcSchema}.V_ZZLX_FYMX_INFO v1 on m.MAIN_ID = v1.BIZ_ID and v1.SUBMIT_DATE = #year#
        where substr(TGF_DWDEPT_CODE, 1, 4) = #tgfDept#
          and substr(SRF_DWDEPT_CODE, 1, 4) = #srfDept#
          and substr(PROJECT_NUM, 2, 4) between '2014' and #year#
          and (PROJECT_STATUS in ('03', '04')
            or (PROJECT_STATUS = '06' and substr(PROJECT_JS_DATE, 1, 4) >= #year#)
            or (PROJECT_STATUS = '99' and substr(PROJECT_END_DATE, 1, 4) = #year#)
            )
    </select>

    <!--查询联络单费用-->
    <select id="costContact" parameterClass="hashmap" resultClass="hashmap">
        select
            m.LIST_ID                                                                            as "listId",
            m.LIST_BH                                                                            as "projectNum",
            m.LIST_NAME                                                                          as "projectName",
            m.TGF_DEPT_NAME                                                                      as "tgfDeptName",
            m.TGF_FZR_NAME || m.TGF_FZR_CODE                                                     as "tgfFzr",
            m.TGF_XMZG_NAME || m.TGF_XMZG_CODE                                                   as "tgfXmzg",
            m.SRF_DEPT_NAME                                                                      as "srfDeptName",
            m.SRF_FZR_NAME || m.SRF_FZR_CODE                                                     as "sgfFzr",
            m.SRF_XMZG_NAME || m.SRF_XMZG_CODE                                                   as "sgfXmzg",
            v.SCENE_FY_TOTAL                                                                     as "travelCostActual",
            v.SCENE_TSF_TOTAL                                                                    as "comCostActual",
            v.PEOPLE_TOTAL                                                                       as "laborCostActual",
            v.LONG_QT_FYHJ                                                                       as "otherCostActual",
            cast(v.PEOPLE_TOTAL * 0.12 as decimal(16, 2))                                        as "managementCost",
            cast(v.SCENE_FY_TOTAL + v.SCENE_TSF_TOTAL + v.PEOPLE_TOTAL + v.PEOPLE_TOTAL *
            0.12 as decimal(16, 2)) as "subtotal"
        from ${zzzcSchema}.T_ZZLL_CONTACT_LIST m
        left join ${zzzcSchema}.V_ZZLX_FYMX_INFO v on m.LIST_ID = v.BIZ_ID and v.SUBMIT_DATE = #year#
        where substr(TGF_DWDEPT_CODE, 1, 4) = #tgfDept#
        and substr(SRF_DWDEPT_CODE, 1, 4) = #srfDept#
        and substr(LIST_END_DATE, 2, 4) between '2014' and #year#
    </select>


    <select id="costBudgetStatistics" parameterClass="hashmap" resultClass="hashmap">
        select m.MAIN_ID                                                                                  as "mainId",
               ROW_NUMBER() OVER (ORDER BY APPLICATION_DATE asc)                                          as "index",
               m.TGF_DWDEPT_NAME                                                                          as "tgfDWdeptName",
               m.SRF_DWDEPT_NAME                                                                          as "srfDWdeptName",
               substr(SUBMIT_DATE, 1, 4)                                                                  as "year",
               m.PROJECT_NUM                                                                              as "projectNum",
               m.PROJECT_NAME                                                                             as "projectName",
               case when type = 'x' then '现场支撑费用报销' when type = 'y' then '远程支撑费用报销' end   as "type",
               m.TGF_FZR_NAME || m.TGF_FZR_CODE                                                           as "tgfFzr",
               m.TGF_DEPT_NAME                                                                            as "tgfDeptName",
               m.SRF_DEPT_NAME                                                                            as "srfDeptName",
               APPLICATION_DATE                                                                           as "applicationDate",
               SUBMIT_DATE                                                                                as "submitDate",
               BZDH                                                                                       as "bzdh",
               SCENE_FY_TOTAL                                                                             as "travelCostActual",
               people_total                                                                               as "laborCostActual",
               cast(people_total * 0.12 as decimal(16, 2))                                                as "managementCost",
               LONG_QT_FYHJ                                                                               as "otherCostActual",
               cast(SCENE_FY_TOTAL + people_total + LONG_QT_FYHJ + people_total * 0.12 as decimal(16, 2)) as "subtotal"
        from ${zzzcSchema}.T_ZZLX_MAIN m
                 left join (select BIZ_ID,
                                   APPLICATION_DATE,
                                   SUBMIT_DATE,
                                   type,
                                   BZDH,
                                   sum(SCENE_FY_TOTAL)                     as SCENE_FY_TOTAL,
                                   sum(scene_people_total + long_rgf_fyhj) as people_total,
                                   sum(LONG_QT_FYHJ)                       as LONG_QT_FYHJ
                            from (select BIZ_ID,
                                         substr(SCENE_DATE, 1, 12)        as APPLICATION_DATE,
                                         substr(SCENE_SUBMIT_DATE, 1, 12) as SUBMIT_DATE,
                                         SCENE_FY_TOTAL                   as SCENE_FY_TOTAL,
                                         scene_people_total               as scene_people_total,
                                         0                                as long_rgf_fyhj,
                                         0                                as LONG_QT_FYHJ,
                                         SCENE_BZDH                       as BZDH,
                                         'x'                              as type
                                  from ${zzzcSchema}.t_zzfy_scene
                                  union all
                                  select BIZ_ID,
                                         substr(LONG_DATE, 1, 12)        as APPLICATION_DATE,
                                         substr(LONG_SUBMIT_DATE, 1, 12) as SUBMIT_DATE,
                                         0                               as SCENE_FY_TOTAL,
                                         0                               as scene_people_total,
                                         long_rgf_fyhj                   as long_rgf_fyhj,
                                         LONG_QT_FYHJ                    as LONG_QT_FYHJ,
                                         ''                              as BZDH,
                                         'y'                             as type
                                  from ${zzzcSchema}.t_zzfy_long) a
                            group by BIZ_ID, type, BZDH, APPLICATION_DATE, SUBMIT_DATE) t on m.MAIN_ID = t.BIZ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="applicationDateStar">SUBMIT_DATE &gt;=  #applicationDateStar# </isNotEmpty>
            <isNotEmpty prepend=" AND " property="applicationDateEnd">SUBMIT_DATE &lt;=  #applicationDateEnd# </isNotEmpty>
            <isNotEmpty prepend=" AND " property="projectArea">project_area = #projectArea#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="srfDeptCode">srf_dept_code like '%'||#srfDeptCode#||'%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="srfDwdeptCode">srf_dwdept_code like '%'||#srfDwdeptCode#||'%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tgfDeptCode">tgf_dept_code like '%'||#tgfDeptCode#||'%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tgfDwdeptCode">tgf_dwdept_code like '%'||#tgfDwdeptCode#||'%'</isNotEmpty>
        </dynamic>
        order by APPLICATION_DATE asc
    </select>
</sqlMap>
