package com.baosight.bscdkj.common.zz.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bscdkj.common.domain.AbstractDomain;
import cn.afterturn.easypoi.excel.annotation.Excel;
import javax.validation.constraints.Size;
import javax.validation.constraints.NotNull;


/**
 * 1.6-03部门奖励分配表对象 t_zzjl_reward_bmfp
 * 
 * <AUTHOR>
 * @date 2021-11-20
 */
@Getter
@Setter
@ToString
public class TzzjlRewardBmfp extends AbstractDomain{
    private static final long serialVersionUID = 1L;

    /** 奖励部门分配主键 */
    private String bmfpGuid;

    /** 奖励分配主键 */
    @Excel(name = "奖励分配主键")
    @Size(max = 36,message = "奖励分配主键最大为36位字符")
   @NotNull(message = " 奖励分配主键 不能为空")
    private String fpGuid;

    /** 奖励部门分配组织 */
    @Excel(name = "奖励部门分配组织")
    @Size(max = 36,message = "奖励部门分配组织最大为36位字符")
    private String bmfpCode;

    /** 奖励部门分配组织名称 */
    @Excel(name = "奖励部门分配组织名称")
    @Size(max = 100,message = "奖励部门分配组织名称最大为100位字符")
    private String bmfpName;

    /** 奖励部门确认人员工号 */
    @Excel(name = "奖励部门确认人员工号")
    @Size(max = 30,message = "奖励部门确认人员工号最大为30位字符")
    private String bmfpUserCode;

    /** 奖励部门确认人员姓名 */
    @Excel(name = "奖励部门确认人员姓名")
    @Size(max = 100,message = "奖励部门确认人员姓名最大为100位字符")
    private String bmfpUserName;

    /** 奖励部门确认意见 */
    @Excel(name = "奖励部门确认意见")
    @Size(max = 500,message = "奖励部门确认意见最大为500位字符")
    private String bmfpOption;

    /** 扩展字段1 */
    @Excel(name = "扩展字段1")
    @Size(max = 30,message = "扩展字段1最大为30位字符")
    private String extra1;

    /** 扩展字段2 */
    @Excel(name = "扩展字段2")
    @Size(max = 30,message = "扩展字段2最大为30位字符")
    private String extra2;

    /** 扩展字段3 */
    @Excel(name = "扩展字段3")
    @Size(max = 30,message = "扩展字段3最大为30位字符")
    private String extra3;

    /** 扩展字段4 */
    @Excel(name = "扩展字段4")
    @Size(max = 30,message = "扩展字段4最大为30位字符")
    private String extra4;

    /** 扩展字段5 */
    @Excel(name = "扩展字段5")
    @Size(max = 30,message = "扩展字段5最大为30位字符")
    private String extra5;

    /** 删除状态 */
    @Excel(name = "删除状态")
    @Size(max = 10,message = "删除状态最大为10位字符")
    private String delStatus;






   

}
