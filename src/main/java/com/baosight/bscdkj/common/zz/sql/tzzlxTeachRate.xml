<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tzzlxTeachRate">
	<typeAlias alias="tzzlxTeachRateResult" type="com.baosight.bscdkj.common.zz.domain.TzzlxTeachRate"/>
	<select id="load" parameterClass="string" resultClass="tzzlxTeachRateResult">
		SELECT 
				rate_id as "rateId" ,
				att_id as "attId" ,
				order_num as "orderNum" ,
				begin_time as "beginTime" ,
				end_time as "endTime" ,
				stage_name as "stageName" ,
				stage_target as "stageTarget" ,
				peoply as "peoply" ,
				remark as "remark" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" 
				FROM ${zzzcSchema}.t_zzlx_teach_rate
		WHERE
				rate_id = #rateId#
		
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tzzlxTeachRateResult">
		SELECT
				rate_id as "rateId" ,
				att_id as "attId" ,
				order_num as "orderNum" ,
				begin_time as "beginTime" ,
				end_time as "endTime" ,
				stage_name as "stageName" ,
				stage_target as "stageTarget" ,
				peoply as "peoply" ,
				remark as "remark" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" 
				FROM ${zzzcSchema}.t_zzlx_teach_rate
			<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="rateId">rate_id =  #rateId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="attId">att_id =  #attId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="orderNum">order_num =  #orderNum#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="beginTime">begin_time =  #beginTime#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="endTime">end_time =  #endTime#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="stageName">stage_name =  #stageName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="stageTarget">stage_target =  #stageTarget#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="peoply">peoply =  #peoply#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="remark">remark =  #remark#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.t_zzlx_teach_rate
		<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="rateId">rate_id =  #rateId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="attId">att_id =  #attId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="orderNum">order_num =  #orderNum#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="beginTime">begin_time =  #beginTime#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="endTime">end_time =  #endTime#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="stageName">stage_name =  #stageName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="stageTarget">stage_target =  #stageTarget#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="peoply">peoply =  #peoply#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="remark">remark =  #remark#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
					<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tzzlxTeachRateResult">
		INSERT INTO ${zzzcSchema}.t_zzlx_teach_rate (
		<dynamic prepend=" ">
						<isNotEmpty prepend=" , " property="rateId">rate_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="attId">att_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="orderNum">order_num</isNotEmpty>
						<isNotEmpty prepend=" , " property="beginTime">begin_time</isNotEmpty>
						<isNotEmpty prepend=" , " property="endTime">end_time</isNotEmpty>
						<isNotEmpty prepend=" , " property="stageName">stage_name</isNotEmpty>
						<isNotEmpty prepend=" , " property="stageTarget">stage_target</isNotEmpty>
						<isNotEmpty prepend=" , " property="peoply">peoply</isNotEmpty>
						<isNotEmpty prepend=" , " property="remark">remark</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version</isNotEmpty>
				</dynamic>
		) VALUES (
    <dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="rateId">#rateId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="attId">#attId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="orderNum">#orderNum#</isNotEmpty>
						<isNotEmpty prepend=" , " property="beginTime">#beginTime#</isNotEmpty>
						<isNotEmpty prepend=" , " property="endTime">#endTime#</isNotEmpty>
						<isNotEmpty prepend=" , " property="stageName">#stageName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="stageTarget">#stageTarget#</isNotEmpty>
						<isNotEmpty prepend=" , " property="peoply">#peoply#</isNotEmpty>
						<isNotEmpty prepend=" , " property="remark">#remark#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.t_zzlx_teach_rate
		WHERE 
		    rate_id = #value#

	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.t_zzlx_teach_rate
		WHERE 
		<dynamic prepend=" ">
						<isNotEmpty prepend=" AND " property="rateId">rate_id=#rateId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="attId">att_id=#attId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="orderNum">order_num=#orderNum#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="beginTime">begin_time=#beginTime#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="endTime">end_time=#endTime#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="stageName">stage_name=#stageName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="stageTarget">stage_target=#stageTarget#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="peoply">peoply=#peoply#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="remark">remark=#remark#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>	
	</delete>

	<update id="update" parameterClass="tzzlxTeachRateResult">
		UPDATE  ${zzzcSchema}.t_zzlx_teach_rate
		SET 
		<dynamic prepend=" ">
					<isNotEmpty prepend=" , " property="rateId">rate_id=#rateId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="attId">att_id=#attId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="orderNum">order_num=#orderNum#</isNotEmpty>
						<isNotEmpty prepend=" , " property="beginTime">begin_time=#beginTime#</isNotEmpty>
						<isNotEmpty prepend=" , " property="endTime">end_time=#endTime#</isNotEmpty>
						<isNotEmpty prepend=" , " property="stageName">stage_name=#stageName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="stageTarget">stage_target=#stageTarget#</isNotEmpty>
						<isNotEmpty prepend=" , " property="peoply">peoply=#peoply#</isNotEmpty>
						<isNotEmpty prepend=" , " property="remark">remark=#remark#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>
		WHERE
		 rate_id =#rateId#
	</update>
	
	

</sqlMap>