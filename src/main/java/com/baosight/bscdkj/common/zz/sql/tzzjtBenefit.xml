<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tzzjtBenefit">
	<typeAlias alias="tzzjtBenefitResult" type="com.baosight.bscdkj.common.zz.domain.TzzjtBenefit"/>
	<typeAlias alias="tzzjtBenefitFlow" type="com.baosight.bscdkj.zz.jt.domain.TzzjtBenefitFlow"/>
	<select id="load" parameterClass="string" resultClass="tzzjtBenefitResult">
		SELECT 
				settle_id as "settleId" ,
				biz_id as "bizId" ,
				tcr_user_code as "tcrUserCode" ,
				tcr_user_name as "tcrUserName" ,
				tcr_date as "tcrDate" ,
				settle_jjxy as "settleJjxy" ,
				settle_date as "settleDate" ,
				settle_status as "settleStatus" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" 
				FROM ${zzzcSchema}.t_zzjt_benefit
		WHERE
				settle_id = #settleId#
		
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tzzjtBenefitResult">
		SELECT
				settle_id as "settleId" ,
				biz_id as "bizId" ,
				tcr_user_code as "tcrUserCode" ,
				tcr_user_name as "tcrUserName" ,
				tcr_date as "tcrDate" ,
				settle_jjxy as "settleJjxy" ,
				settle_date as "settleDate" ,
				settle_status as "settleStatus" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" 
				FROM ${zzzcSchema}.t_zzjt_benefit
			<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="settleId">settle_id =  #settleId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="bizId">biz_id =  #bizId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="tcrUserCode">tcr_user_code =  #tcrUserCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="tcrUserName">tcr_user_name =  #tcrUserName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="tcrDate">tcr_date =  #tcrDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="settleJjxy">settle_jjxy =  #settleJjxy#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="settleDate">settle_date =  #settleDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="settleStatus">settle_status =  #settleStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.t_zzjt_benefit
		<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="settleId">settle_id =  #settleId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="bizId">biz_id =  #bizId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="tcrUserCode">tcr_user_code =  #tcrUserCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="tcrUserName">tcr_user_name =  #tcrUserName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="tcrDate">tcr_date =  #tcrDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="settleJjxy">settle_jjxy =  #settleJjxy#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="settleDate">settle_date =  #settleDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="settleStatus">settle_status =  #settleStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
					<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tzzjtBenefitResult">
		INSERT INTO ${zzzcSchema}.t_zzjt_benefit ( 
		<dynamic prepend=" ">
						<isNotEmpty prepend=" , " property="settleId">settle_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="bizId">biz_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="tcrUserCode">tcr_user_code</isNotEmpty>
						<isNotEmpty prepend=" , " property="tcrUserName">tcr_user_name</isNotEmpty>
						<isNotEmpty prepend=" , " property="tcrDate">tcr_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="settleJjxy">settle_jjxy</isNotEmpty>
						<isNotEmpty prepend=" , " property="settleDate">settle_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="settleStatus">settle_status</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version</isNotEmpty>
				</dynamic>
		) VALUES (
    <dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="settleId">#settleId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="bizId">#bizId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="tcrUserCode">#tcrUserCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="tcrUserName">#tcrUserName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="tcrDate">#tcrDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="settleJjxy">#settleJjxy#</isNotEmpty>
						<isNotEmpty prepend=" , " property="settleDate">#settleDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="settleStatus">#settleStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.t_zzjt_benefit
		WHERE 
		    settle_id = #value#

	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.t_zzjt_benefit
		WHERE 
		<dynamic prepend=" ">
						<isNotEmpty prepend=" AND " property="settleId">settle_id=#settleId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="bizId">biz_id=#bizId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="tcrUserCode">tcr_user_code=#tcrUserCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="tcrUserName">tcr_user_name=#tcrUserName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="tcrDate">tcr_date=#tcrDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="settleJjxy">settle_jjxy=#settleJjxy#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="settleDate">settle_date=#settleDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="settleStatus">settle_status=#settleStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>	
	</delete>

	<update id="update" parameterClass="tzzjtBenefitResult">
		UPDATE  ${zzzcSchema}.t_zzjt_benefit	
		SET 
		<dynamic prepend=" ">
					<isNotEmpty prepend=" , " property="settleId">settle_id=#settleId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="bizId">biz_id=#bizId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="tcrUserCode">tcr_user_code=#tcrUserCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="tcrUserName">tcr_user_name=#tcrUserName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="tcrDate">tcr_date=#tcrDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="settleJjxy">settle_jjxy=#settleJjxy#</isNotEmpty>
						<isNotEmpty prepend=" , " property="settleDate">settle_date=#settleDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="settleStatus">settle_status=#settleStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>
		WHERE
		 settle_id =#settleId#
	</update>


	<!-- 流程跟踪 -->
	<select id="queryBenefitInfo"  parameterClass="hashmap" resultClass="tzzjtBenefitFlow">
		SELECT
		b.settle_id as "settleId" ,
		b.biz_id as "bizId" ,
		b.tcr_user_code as "tcrUserCode" ,
		b.tcr_user_name as "tcrUserName" ,
		b.tcr_date as "tcrDate" ,
		b.settle_jjxy as "settleJjxy" ,
		b.settle_date as "settleDate" ,
		b.settle_status as "settleStatus" ,
		b.extra1 as "extra1" ,
		b.extra2 as "extra2" ,
		b.extra3 as "extra3" ,
		b.extra4 as "extra4" ,
		b.extra5 as "extra5" ,
		b.del_status as "delStatus" ,
		b.create_user_label as "createUserLabel" ,
		b.create_date as "createDate" ,
		b.update_user_label as "updateUserLabel" ,
		b.update_date as "updateDate" ,
		b.delete_user_label as "deleteUserLabel" ,
		b.delete_date as "deleteDate" ,
		b.record_version as "recordVersion",
		m.main_id as "mainId" ,
		m.project_name as "projectName" ,
		m.project_num as "projectNum" ,
		m.srf_dept_code as "srfDeptCode" ,
		m.srf_dept_name as "srfDeptName" ,
		m.tgf_dept_code as "tgfDeptCode" ,
		m.tgf_dept_name as "tgfDeptName" ,
		m.tgf_xmzg_code as "tgfXmzgCode" ,
		m.tgf_xmzg_name as "tgfXmzgName" ,
		m.srf_xmzg_code as "srfXmzgCode" ,
		m.srf_xmzg_name as "srfXmzgName" ,
		m.tgf_fzr_code as "tgfFzrCode" ,
		m.tgf_fzr_name as "tgfFzrName" ,
		m.srf_fzr_code as "srfFzrCode" ,
		m.srf_fzr_name as "srfFzrName",
		wf.current_operator as "currentOperator" ,
		wf.current_activity_name as "currentActivityName"
		FROM ${zzzcSchema}.t_zzjt_benefit b join  ${zzzcSchema}.t_zzlx_main m on b.biz_id=m.main_id
		join ${ggmkSchema}.t_mpwf_flow_info wf on wf.business_id = b.settle_id
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="settleId">b.settle_id =  #settleId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="bizId">b.biz_id =  #bizId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tcrUserCode">b.tcr_user_code =  #tcrUserCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tcrUserName">b.tcr_user_name =  #tcrUserName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="tcrDate">b.tcr_date =  #tcrDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="settleJjxy">b.settle_jjxy =  #settleJjxy#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="settleDate">b.settle_date =  #settleDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="settleStatus">b.settle_status =  #settleStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">b.extra1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">b.extra2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">b.extra3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">b.extra4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">b.extra5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">b.del_status =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">b.create_user_label =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">b.create_date =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">b.update_user_label =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">b.update_date =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">b.delete_user_label =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">b.delete_date =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">b.record_version =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCode">m.project_num =  #projectNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectNumLike">m.project_num like '%$projectNumLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">m.project_name =  #projectName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectNameLike">m.project_name like '%$projectNameLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrCode">m.tgf_fzr_code like '%$fzrCode$%' or m.srf_fzr_code like '%$fzrCode$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="xmzgCode">m.tgf_xmzg_code like '%$xmzgCode$%' or m.srf_xmzg_code like '%$xmzgCode$%'</isNotEmpty>

			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
		<isEmpty prepend=" " property="displayOrder"> ORDER BY wf.update_date desc</isEmpty>
	</select>


	<select id="queryXyReport"  parameterClass="hashmap" resultClass="hashmap">
		select s.record_id         as "recordId",
		   S.sp_id                 as "spId",
		   S.yw_id                 as "ywId",
		   S.tbspr                 as "tbspr",
		   S.tbspr_name            as "tbsprName",
		   S.tbsprq                as "tbsprq",
		   S.cs_result             as "csResult",
		   S.xyzmcl                as "xyzmcl",
		   S.xysx                  as "xysx",
		   S.extra2                as "extra2",
		   S.extra3                as "extra3",
		   S.del_status            as "delStatus",
		   S.create_user_label     as "createUserLabel",
		   S.create_date           as "createDate",
		   S.update_user_label     as "updateUserLabel",
		   S.update_date           as "updateDate",
		   S.delete_user_label     as "deleteUserLabel",
		   S.delete_date           as "deleteDate",
		   S.record_version        as "recordVersion",
		   S.jjxy                  as "jjxy",
		   S.xy_year               as "xyYear",
		   S.xy_quarter            as "xyQuarter",
		   S.jbxy                  as "jbxy",
		   m.PROJECT_NUM AS "projectNum",
	       m.PROJECT_NAME AS "projectName",
		   m.TGF_FZR_CODE AS "tgfFzrCode",
		   m.TGF_FZR_NAME || m.TGF_FZR_CODE AS "tgfFzrName",
		   m.SRF_FZR_NAME || m.SRF_FZR_CODE AS "srfFzrName",
		   (CASE regexp_count(m.SRF_DEPT_NAME,'/')
		   WHEN 4 THEN SUBSTR(m.SRF_DEPT_NAME,LOCATE('/',m.SRF_DEPT_NAME,LOCATE('/',m.SRF_DEPT_NAME,LOCATE('/',m.SRF_DEPT_NAME)+1)+1)+1,LOCATE('/',m.SRF_DEPT_NAME,LOCATE('/',m.SRF_DEPT_NAME,LOCATE('/',m.SRF_DEPT_NAME))+1)+4)
		   WHEN 3 THEN SUBSTR(m.SRF_DEPT_NAME,LOCATE('/',m.SRF_DEPT_NAME,LOCATE('/',m.SRF_DEPT_NAME)+1)+1,LOCATE('/',m.SRF_DEPT_NAME,LOCATE('/',m.SRF_DEPT_NAME,LOCATE('/',m.SRF_DEPT_NAME,LOCATE('/',m.SRF_DEPT_NAME)+1))+1)+4)
		   ELSE m.SRF_DEPT_NAME
		   END )AS "srfDeptName",
	       m.SRF_DEPT_CODE AS "srfDeptCode",
		   (CASE regexp_count(m.TGF_DEPT_NAME,'/')
	       WHEN 4 THEN SUBSTR(m.TGF_DEPT_NAME,LOCATE('/',m.TGF_DEPT_NAME,LOCATE('/',m.TGF_DEPT_NAME,LOCATE('/',m.TGF_DEPT_NAME)+1)+1)+1,LOCATE('/',m.TGF_DEPT_NAME,LOCATE('/',m.TGF_DEPT_NAME,LOCATE('/',m.TGF_DEPT_NAME))+1)+4)
		   WHEN 3 THEN SUBSTR(m.TGF_DEPT_NAME,LOCATE('/',m.TGF_DEPT_NAME,LOCATE('/',m.TGF_DEPT_NAME)+1)+1,LOCATE('/',m.TGF_DEPT_NAME,LOCATE('/',m.TGF_DEPT_NAME,LOCATE('/',m.TGF_DEPT_NAME,LOCATE('/',m.TGF_DEPT_NAME)+1))+1)+4)
		   ELSE m.TGF_DEPT_NAME
		   END )as "tgfDeptName",
		   m.PROJECT_JJXY          as "projectJjxy",
		   A.CURRENT_ACTIVITY_NAME AS "currentActivityName",
		   A.CURRENT_OPERATOR      as "currentOperator"
		   from ywzt.T_KJXY_SPB s
		 inner join ZZZC.T_ZZLX_MAIN m on s.YW_ID = m.MAIN_ID and s.EXTRA2 = 'zzjt_benefit'
		 left join GGMK.T_MPWF_FLOW_INFO A on A.BUSINESS_ID = s.record_id
		 <dynamic prepend="where">
			 <isNotEmpty prepend="AND" property="loginName">
				s.RECORD_ID in (
				SELECT
				sv.RECORD_ID
				FROM
				KJGL.V_KYXM_MYHANDLE_BASE v,
				YWZT.T_KJXY_SPB sv
				WHERE
				v.BUSINESS_GUID = sv.RECORD_ID
				and v.completer_Id = #loginName#
				)
			 </isNotEmpty>
			 <isNotEmpty prepend=" AND " property="projectNameLike">m.project_name like '%$projectNameLike$%'</isNotEmpty>
			 <isNotEmpty prepend=" AND " property="projectNumLike">m.project_num like '%$projectNumLike$%'</isNotEmpty>
			 <isNotEmpty prepend=" AND " property="srfDeptCode">m.srf_dept_code=#srfDeptCode#</isNotEmpty>
			 <isNotEmpty prepend=" AND " property="srfDeptName">m.srf_dept_name=#srfDeptName#</isNotEmpty>
			 <isNotEmpty prepend=" AND " property="tgfDeptCode">m.tgf_dept_code=#tgfDeptCode#</isNotEmpty>
			 <isNotEmpty prepend=" AND " property="tgfDeptName">m.tgf_dept_name=#tgfDeptName#</isNotEmpty>
			 <isNotEmpty prepend=" AND " property="tgfFzrNameLike">(m.tgf_fzr_name like '%$tgfFzrNameLike$%' or m.tgf_fzr_code like '%$tgfFzrNameLike$%')</isNotEmpty>
			 <isNotEmpty prepend=" AND " property="tbsprqMin">s.tbsprq >= #tbsprqMin#
			 </isNotEmpty>
			 <isNotEmpty prepend=" AND " property="tbsprqMax">s.tbsprq <![CDATA[<=]]>
				 #tbsprqMax#
			 </isNotEmpty>
		 </dynamic>
		ORDER BY s.update_date desc , m.project_num desc


	</select>
	

</sqlMap>