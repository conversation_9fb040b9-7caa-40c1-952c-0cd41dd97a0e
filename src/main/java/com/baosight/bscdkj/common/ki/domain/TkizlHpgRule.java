package com.baosight.bscdkj.common.ki.domain;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.bscdkj.common.domain.AbstractDomain;
import javax.validation.constraints.Size;

/**
 * 境外专利后评估规则表: T_KIZL_HPG_RULE
 * 
 * 
 * <AUTHOR>
 */
public class TkizlHpgRule  extends AbstractDomain{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	//主键 : RULE_ID 
	@Size(max = 36,message = "主键最大为36位字符")	
	private String ruleId; 
	
	//启动主键 : START_ID 
	@Size(max = 36,message = "启动主键最大为36位字符")	
	private String startId; 
	
	//专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计 : PATENT_TYPE 
	@Size(max = 20,message = "专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计最大为20位字符")	
	private String patentType; 
	
	//管理单位代码 : GLDW_CODE 
	@Size(max = 20,message = "管理单位代码最大为20位字符")	
	private String gldwCode; 
	
	//管理单位名称 : GLDW_NAME 
	@Size(max = 50,message = "管理单位名称最大为50位字符")	
	private String gldwName; 
	
	//预留 : HPG_TYPE 
	@Size(max = 2,message = "预留最大为2位字符")	
	private String hpgType; 
	
	//扩展字段1受理日期起 : EXTRA1 
	@Size(max = 30,message = "扩展字段1受理日期起最大为30位字符")	
	private String extra1; 
	
	//扩展字段2受理日期止 : EXTRA2 
	@Size(max = 30,message = "扩展字段2受理日期止最大为30位字符")	
	private String extra2; 
	
	//扩展字段3实施状态 : EXTRA3 
	@Size(max = 30,message = "扩展字段3实施状态最大为30位字符")	
	private String extra3; 
	
	//扩展字段4来源 : EXTRA4 
	@Size(max = 30,message = "扩展字段4来源最大为30位字符")	
	private String extra4; 
	
	//扩展字段5是否应用于技术贸易、推广 : EXTRA5 
	@Size(max = 30,message = "扩展字段5是否应用于技术贸易、推广最大为30位字符")	
	private String extra5; 
	
	//扩展字段10 : EXTRA10 
	@Size(max = 50,message = "扩展字段10最大为50位字符")	
	private String extra10; 
	
	//扩展字段6 : EXTRA6 
	@Size(max = 50,message = "扩展字段6最大为50位字符")	
	private String extra6; 
	
	//扩展字段7 : EXTRA7 
	@Size(max = 50,message = "扩展字段7最大为50位字符")	
	private String extra7; 
	
	//扩展字段8 : EXTRA8 
	@Size(max = 50,message = "扩展字段8最大为50位字符")	
	private String extra8; 
	
	//扩展字段9 : EXTRA9 
	@Size(max = 50,message = "扩展字段9最大为50位字符")	
	private String extra9; 
	
	//扩展字段11 : EXTRA11 
	@Size(max = 50,message = "扩展字段11最大为50位字符")	
	private String extra11; 
	
	//扩展字段12 : EXTRA12 
	@Size(max = 50,message = "扩展字段12最大为50位字符")	
	private String extra12; 
	

	/**
	 * 主键 : RULE_ID
	 * 
	 * @return 
	 */
	public String getRuleId () {
		return ruleId;
	}
	
	/**
	 * 主键 : RULE_ID
	 * 
	 * @return 
	 */
	public void setRuleId (String ruleId) {
		this.ruleId = ruleId;
	}
	/**
	 * 启动主键 : START_ID
	 * 
	 * @return 
	 */
	public String getStartId () {
		return startId;
	}
	
	/**
	 * 启动主键 : START_ID
	 * 
	 * @return 
	 */
	public void setStartId (String startId) {
		this.startId = startId;
	}
	/**
	 * 专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计 : PATENT_TYPE
	 * 
	 * @return 
	 */
	public String getPatentType () {
		return patentType;
	}
	
	/**
	 * 专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计 : PATENT_TYPE
	 * 
	 * @return 
	 */
	public void setPatentType (String patentType) {
		this.patentType = patentType;
	}
	/**
	 * 管理单位代码 : GLDW_CODE
	 * 
	 * @return 
	 */
	public String getGldwCode () {
		return gldwCode;
	}
	
	/**
	 * 管理单位代码 : GLDW_CODE
	 * 
	 * @return 
	 */
	public void setGldwCode (String gldwCode) {
		this.gldwCode = gldwCode;
	}
	/**
	 * 管理单位名称 : GLDW_NAME
	 * 
	 * @return 
	 */
	public String getGldwName () {
		return gldwName;
	}
	
	/**
	 * 管理单位名称 : GLDW_NAME
	 * 
	 * @return 
	 */
	public void setGldwName (String gldwName) {
		this.gldwName = gldwName;
	}
	/**
	 * 预留 : HPG_TYPE
	 * 
	 * @return 
	 */
	public String getHpgType () {
		return hpgType;
	}
	
	/**
	 * 预留 : HPG_TYPE
	 * 
	 * @return 
	 */
	public void setHpgType (String hpgType) {
		this.hpgType = hpgType;
	}
	/**
	 * 扩展字段1受理日期起 : EXTRA1
	 * 
	 * @return 
	 */
	public String getExtra1 () {
		return extra1;
	}
	
	/**
	 * 扩展字段1受理日期起 : EXTRA1
	 * 
	 * @return 
	 */
	public void setExtra1 (String extra1) {
		this.extra1 = extra1;
	}
	/**
	 * 扩展字段2受理日期止 : EXTRA2
	 * 
	 * @return 
	 */
	public String getExtra2 () {
		return extra2;
	}
	
	/**
	 * 扩展字段2受理日期止 : EXTRA2
	 * 
	 * @return 
	 */
	public void setExtra2 (String extra2) {
		this.extra2 = extra2;
	}
	/**
	 * 扩展字段3实施状态 : EXTRA3
	 * 
	 * @return 
	 */
	public String getExtra3 () {
		return extra3;
	}
	
	/**
	 * 扩展字段3实施状态 : EXTRA3
	 * 
	 * @return 
	 */
	public void setExtra3 (String extra3) {
		this.extra3 = extra3;
	}
	/**
	 * 扩展字段4来源 : EXTRA4
	 * 
	 * @return 
	 */
	public String getExtra4 () {
		return extra4;
	}
	
	/**
	 * 扩展字段4来源 : EXTRA4
	 * 
	 * @return 
	 */
	public void setExtra4 (String extra4) {
		this.extra4 = extra4;
	}
	/**
	 * 扩展字段5是否应用于技术贸易、推广 : EXTRA5
	 * 
	 * @return 
	 */
	public String getExtra5 () {
		return extra5;
	}
	
	/**
	 * 扩展字段5是否应用于技术贸易、推广 : EXTRA5
	 * 
	 * @return 
	 */
	public void setExtra5 (String extra5) {
		this.extra5 = extra5;
	}
	/**
	 * 扩展字段10 : EXTRA10
	 * 
	 * @return 
	 */
	public String getExtra10 () {
		return extra10;
	}
	
	/**
	 * 扩展字段10 : EXTRA10
	 * 
	 * @return 
	 */
	public void setExtra10 (String extra10) {
		this.extra10 = extra10;
	}
	/**
	 * 扩展字段6 : EXTRA6
	 * 
	 * @return 
	 */
	public String getExtra6 () {
		return extra6;
	}
	
	/**
	 * 扩展字段6 : EXTRA6
	 * 
	 * @return 
	 */
	public void setExtra6 (String extra6) {
		this.extra6 = extra6;
	}
	/**
	 * 扩展字段7 : EXTRA7
	 * 
	 * @return 
	 */
	public String getExtra7 () {
		return extra7;
	}
	
	/**
	 * 扩展字段7 : EXTRA7
	 * 
	 * @return 
	 */
	public void setExtra7 (String extra7) {
		this.extra7 = extra7;
	}
	/**
	 * 扩展字段8 : EXTRA8
	 * 
	 * @return 
	 */
	public String getExtra8 () {
		return extra8;
	}
	
	/**
	 * 扩展字段8 : EXTRA8
	 * 
	 * @return 
	 */
	public void setExtra8 (String extra8) {
		this.extra8 = extra8;
	}
	/**
	 * 扩展字段9 : EXTRA9
	 * 
	 * @return 
	 */
	public String getExtra9 () {
		return extra9;
	}
	
	/**
	 * 扩展字段9 : EXTRA9
	 * 
	 * @return 
	 */
	public void setExtra9 (String extra9) {
		this.extra9 = extra9;
	}
	/**
	 * 扩展字段11 : EXTRA11
	 * 
	 * @return 
	 */
	public String getExtra11 () {
		return extra11;
	}
	
	/**
	 * 扩展字段11 : EXTRA11
	 * 
	 * @return 
	 */
	public void setExtra11 (String extra11) {
		this.extra11 = extra11;
	}
	/**
	 * 扩展字段12 : EXTRA12
	 * 
	 * @return 
	 */
	public String getExtra12 () {
		return extra12;
	}
	
	/**
	 * 扩展字段12 : EXTRA12
	 * 
	 * @return 
	 */
	public void setExtra12 (String extra12) {
		this.extra12 = extra12;
	}


    public void initMetaData(){
    	EiColumn eiColumn;
    	  	
			eiColumn = new EiColumn("ruleId");
       	eiColumn.setDescName("主键");
        eiColumn.setMaxLength(36);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("startId");
       	eiColumn.setDescName("启动主键");
        eiColumn.setMaxLength(36);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("patentType");
       	eiColumn.setDescName("专利类型 FM-发明 SYXX-实用新型 WGSJ-外观设计");
        eiColumn.setMaxLength(20);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("gldwCode");
       	eiColumn.setDescName("管理单位代码");
        eiColumn.setMaxLength(20);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("gldwName");
       	eiColumn.setDescName("管理单位名称");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("hpgType");
       	eiColumn.setDescName("预留");
        eiColumn.setMaxLength(2);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra1");
       	eiColumn.setDescName("扩展字段1受理日期起");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra2");
       	eiColumn.setDescName("扩展字段2受理日期止");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra3");
       	eiColumn.setDescName("扩展字段3实施状态");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra4");
       	eiColumn.setDescName("扩展字段4来源");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra5");
       	eiColumn.setDescName("扩展字段5是否应用于技术贸易、推广");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("delStatus");
       	eiColumn.setDescName("删除状态");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("createUserLabel");
       	eiColumn.setDescName("创建人");
        eiColumn.setMaxLength(100);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("createDate");
       	eiColumn.setDescName("创建时间");
        eiColumn.setMaxLength(26);
        eiColumn.setType("date");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("updateUserLabel");
       	eiColumn.setDescName("更新人");
        eiColumn.setMaxLength(100);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("updateDate");
       	eiColumn.setDescName("更新时间");
        eiColumn.setMaxLength(26);
        eiColumn.setType("date");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("deleteUserLabel");
       	eiColumn.setDescName("删除人");
        eiColumn.setMaxLength(100);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("deleteDate");
       	eiColumn.setDescName("删除时间");
        eiColumn.setMaxLength(26);
        eiColumn.setType("date");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("recordVersion");
       	eiColumn.setDescName("版本号");
        eiColumn.setMaxLength(1);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra10");
       	eiColumn.setDescName("扩展字段10");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra6");
       	eiColumn.setDescName("扩展字段6");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra7");
       	eiColumn.setDescName("扩展字段7");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra8");
       	eiColumn.setDescName("扩展字段8");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra9");
       	eiColumn.setDescName("扩展字段9");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra11");
       	eiColumn.setDescName("扩展字段11");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra12");
       	eiColumn.setDescName("扩展字段12");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiMetadata.getMeta("ruleId").setPrimaryKey(true);
	        
    }

    public TkizlHpgRule (){
        initMetaData();
    }
    
}
