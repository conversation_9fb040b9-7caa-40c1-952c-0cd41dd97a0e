<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlApplyRyxxHg">
	<typeAlias alias="tKIZLApplyRyxxHg" type="com.baosight.bscdkj.common.ki.domain.TkizlApplyRyxxHg"/>
	<select id="load" parameterClass="string" resultClass="tKIZLApplyRyxxHg">
		SELECT 
				RYXX_ID as "ryxxId" ,		
				APPLY_ID as "applyId" ,		
				PATENT_ID as "patentId" ,		
				RYXH as "ryxh" ,		
				RYL<PERSON> as "rylx" ,		
				LEGAL_ID as "legalId" ,		
				EMP_ID as "empId" ,		
				EMP_NAME as "empName" ,		
				DEPT_CODE as "deptCode" ,		
				DEPT_NAME as "deptName" ,		
				<PERSON><PERSON><PERSON>_TIT<PERSON> as "postTitle" ,		
				POST_LEVEL as "postLevel" ,		
				POST_NAME as "postName" ,		
				GXXS as "gxxs" ,		
				ID_CARD as "idCard" ,		
				CONTENT_MEMO as "contentMemo" ,		
				EXTRA1 as "extra1" ,		
				EXTRA2 as "extra2" ,		
				EXTRA3 as "extra3" ,		
				EXTRA4 as "extra4" ,		
				EXTRA5 as "extra5" ,		
				DEL_STATUS as "delStatus" ,		
				CREATE_USER_LABEL as "createUserLabel" ,		
				CREATE_DATE as "createDate" ,		
				UPDATE_USER_LABEL as "updateUserLabel" ,		
				UPDATE_DATE as "updateDate" ,		
				DELETE_USER_LABEL as "deleteUserLabel" ,		
				DELETE_DATE as "deleteDate" ,		
				RECORD_VERSION as "recordVersion" ,		
				LEGAL_NAME as "legalName" ,		
				GF_TYPE as "gfType" ,		
				CODE_PATH as "codePath" ,		
				NAME_PATH as "namePath" ,		
				GLBM as "glbm" ,		
				XSQR as "xsqr" ,		
				GLRY as "glry" ,		
				DWMCPX as "dwmcpx" ,		
				FGS as "fgs" ,		
				DEPART as "depart" ,		
				DEPTCB as "deptcb" ,		
				DEPTCBBM as "deptcbbm" ,		
				PERSONXS as "personxs" ,		
				GROUP_NAME as "groupName" 		
				FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG
		WHERE   RYXX_ID=#value# 				
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tKIZLApplyRyxxHg">
		SELECT
				RYXX_ID  as "ryxxId" ,		
				APPLY_ID  as "applyId" ,		
				PATENT_ID  as "patentId" ,		
				RYXH  as "ryxh" ,		
				RYLX  as "rylx" ,		
				LEGAL_ID  as "legalId" ,		
				EMP_ID  as "empId" ,		
				EMP_NAME  as "empName" ,		
				DEPT_CODE  as "deptCode" ,		
				DEPT_NAME  as "deptName" ,		
				POST_TITLE  as "postTitle" ,		
				POST_LEVEL  as "postLevel" ,		
				POST_NAME  as "postName" ,		
				GXXS  as "gxxs" ,		
				ID_CARD  as "idCard" ,		
				CONTENT_MEMO  as "contentMemo" ,		
				EXTRA1  as "extra1" ,		
				EXTRA2  as "extra2" ,		
				EXTRA3  as "extra3" ,		
				EXTRA4  as "extra4" ,		
				EXTRA5  as "extra5" ,		
				DEL_STATUS  as "delStatus" ,		
				CREATE_USER_LABEL  as "createUserLabel" ,		
				CREATE_DATE  as "createDate" ,		
				UPDATE_USER_LABEL  as "updateUserLabel" ,		
				UPDATE_DATE  as "updateDate" ,		
				DELETE_USER_LABEL  as "deleteUserLabel" ,		
				DELETE_DATE  as "deleteDate" ,		
				RECORD_VERSION  as "recordVersion" ,		
				LEGAL_NAME  as "legalName" ,		
				GF_TYPE  as "gfType" ,		
				CODE_PATH  as "codePath" ,		
				NAME_PATH  as "namePath" ,		
				GLBM  as "glbm" ,		
				XSQR  as "xsqr" ,		
				GLRY  as "glry" ,		
				DWMCPX  as "dwmcpx" ,		
				FGS  as "fgs" ,		
				DEPART  as "depart" ,		
				DEPTCB  as "deptcb" ,		
				DEPTCBBM  as "deptcbbm" ,		
				PERSONXS  as "personxs" ,		
				GROUP_NAME  as "groupName" 		
				FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="ryxxId">RYXX_ID =  #ryxxId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ryxh">RYXH =  #ryxh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="rylx">RYLX =  #rylx#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="legalId">LEGAL_ID =  #legalId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="empId">EMP_ID =  #empId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="empName">EMP_NAME =  #empName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE =  #deptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptName">DEPT_NAME =  #deptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="postTitle">POST_TITLE =  #postTitle#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="postLevel">POST_LEVEL =  #postLevel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="postName">POST_NAME =  #postName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gxxs">GXXS =  #gxxs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="idCard">ID_CARD =  #idCard#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="contentMemo">CONTENT_MEMO =  #contentMemo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="legalName">LEGAL_NAME =  #legalName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gfType">GF_TYPE =  #gfType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="codePath">CODE_PATH =  #codePath#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="namePath">NAME_PATH =  #namePath#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="glbm">GLBM =  #glbm#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="xsqr">XSQR =  #xsqr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="glry">GLRY =  #glry#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dwmcpx">DWMCPX =  #dwmcpx#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fgs">FGS =  #fgs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="depart">DEPART =  #depart#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptcb">DEPTCB =  #deptcb#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptcbbm">DEPTCBBM =  #deptcbbm#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="personxs">PERSONXS =  #personxs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="groupName">GROUP_NAME =  #groupName#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG 
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="ryxxId">RYXX_ID =  #ryxxId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ryxh">RYXH =  #ryxh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="rylx">RYLX =  #rylx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="legalId">LEGAL_ID =  #legalId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="empId">EMP_ID =  #empId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="empName">EMP_NAME =  #empName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE =  #deptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptName">DEPT_NAME =  #deptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postTitle">POST_TITLE =  #postTitle#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postLevel">POST_LEVEL =  #postLevel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="postName">POST_NAME =  #postName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gxxs">GXXS =  #gxxs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="idCard">ID_CARD =  #idCard#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contentMemo">CONTENT_MEMO =  #contentMemo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="legalName">LEGAL_NAME =  #legalName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gfType">GF_TYPE =  #gfType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="codePath">CODE_PATH =  #codePath#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="namePath">NAME_PATH =  #namePath#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="glbm">GLBM =  #glbm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="xsqr">XSQR =  #xsqr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="glry">GLRY =  #glry#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dwmcpx">DWMCPX =  #dwmcpx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fgs">FGS =  #fgs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="depart">DEPART =  #depart#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptcb">DEPTCB =  #deptcb#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptcbbm">DEPTCBBM =  #deptcbbm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="personxs">PERSONXS =  #personxs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="groupName">GROUP_NAME =  #groupName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tKIZLApplyRyxxHg">
		INSERT INTO ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG ( 
		<dynamic prepend=" ">
					<isNotNull prepend=", " property="ryxxId">RYXX_ID </isNotNull>
					<isNotNull prepend=", " property="applyId">APPLY_ID </isNotNull>
					<isNotNull prepend=", " property="patentId">PATENT_ID </isNotNull>
					<isNotNull prepend=", " property="ryxh">RYXH </isNotNull>
					<isNotNull prepend=", " property="rylx">RYLX </isNotNull>
					<isNotNull prepend=", " property="legalId">LEGAL_ID </isNotNull>
					<isNotNull prepend=", " property="empId">EMP_ID </isNotNull>
					<isNotNull prepend=", " property="empName">EMP_NAME </isNotNull>
					<isNotNull prepend=", " property="deptCode">DEPT_CODE </isNotNull>
					<isNotNull prepend=", " property="deptName">DEPT_NAME </isNotNull>
					<isNotNull prepend=", " property="postTitle">POST_TITLE </isNotNull>
					<isNotNull prepend=", " property="postLevel">POST_LEVEL </isNotNull>
					<isNotNull prepend=", " property="postName">POST_NAME </isNotNull>
					<isNotNull prepend=", " property="gxxs">GXXS </isNotNull>
					<isNotNull prepend=", " property="idCard">ID_CARD </isNotNull>
					<isNotNull prepend=", " property="contentMemo">CONTENT_MEMO </isNotNull>
					<isNotNull prepend=", " property="extra1">EXTRA1 </isNotNull>
					<isNotNull prepend=", " property="extra2">EXTRA2 </isNotNull>
					<isNotNull prepend=", " property="extra3">EXTRA3 </isNotNull>
					<isNotNull prepend=", " property="extra4">EXTRA4 </isNotNull>
					<isNotNull prepend=", " property="extra5">EXTRA5 </isNotNull>
					<isNotNull prepend=", " property="delStatus">DEL_STATUS </isNotNull>
					<isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="createDate">CREATE_DATE </isNotNull>
					<isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="updateDate">UPDATE_DATE </isNotNull>
					<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="deleteDate">DELETE_DATE </isNotNull>
					<isNotNull prepend=", " property="recordVersion">RECORD_VERSION </isNotNull>
					<isNotNull prepend=", " property="legalName">LEGAL_NAME </isNotNull>
					<isNotNull prepend=", " property="gfType">GF_TYPE </isNotNull>
					<isNotNull prepend=", " property="codePath">CODE_PATH </isNotNull>
					<isNotNull prepend=", " property="namePath">NAME_PATH </isNotNull>
					<isNotNull prepend=", " property="glbm">GLBM </isNotNull>
					<isNotNull prepend=", " property="xsqr">XSQR </isNotNull>
					<isNotNull prepend=", " property="glry">GLRY </isNotNull>
					<isNotNull prepend=", " property="dwmcpx">DWMCPX </isNotNull>
					<isNotNull prepend=", " property="fgs">FGS </isNotNull>
					<isNotNull prepend=", " property="depart">DEPART </isNotNull>
					<isNotNull prepend=", " property="deptcb">DEPTCB </isNotNull>
					<isNotNull prepend=", " property="deptcbbm">DEPTCBBM </isNotNull>
					<isNotNull prepend=", " property="personxs">PERSONXS </isNotNull>
					<isNotNull prepend=", " property="groupName">GROUP_NAME </isNotNull>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotNull prepend=", " property="ryxxId">#ryxxId#</isNotNull>
			<isNotNull prepend=", " property="applyId">#applyId#</isNotNull>
			<isNotNull prepend=", " property="patentId">#patentId#</isNotNull>
			<isNotNull prepend=", " property="ryxh">#ryxh#</isNotNull>
			<isNotNull prepend=", " property="rylx">#rylx#</isNotNull>
			<isNotNull prepend=", " property="legalId">#legalId#</isNotNull>
			<isNotNull prepend=", " property="empId">#empId#</isNotNull>
			<isNotNull prepend=", " property="empName">#empName#</isNotNull>
			<isNotNull prepend=", " property="deptCode">#deptCode#</isNotNull>
			<isNotNull prepend=", " property="deptName">#deptName#</isNotNull>
			<isNotNull prepend=", " property="postTitle">#postTitle#</isNotNull>
			<isNotNull prepend=", " property="postLevel">#postLevel#</isNotNull>
			<isNotNull prepend=", " property="postName">#postName#</isNotNull>
			<isNotNull prepend=", " property="gxxs">#gxxs#</isNotNull>
			<isNotNull prepend=", " property="idCard">#idCard#</isNotNull>
			<isNotNull prepend=", " property="contentMemo">#contentMemo#</isNotNull>
			<isNotNull prepend=", " property="extra1">#extra1#</isNotNull>
			<isNotNull prepend=", " property="extra2">#extra2#</isNotNull>
			<isNotNull prepend=", " property="extra3">#extra3#</isNotNull>
			<isNotNull prepend=", " property="extra4">#extra4#</isNotNull>
			<isNotNull prepend=", " property="extra5">#extra5#</isNotNull>
			<isNotNull prepend=", " property="delStatus">#delStatus#</isNotNull>
			<isNotNull prepend=", " property="createUserLabel">#createUserLabel#</isNotNull>
			<isNotNull prepend=", " property="createDate">
			   <isNotEmpty property="createDate">#createDate#</isNotEmpty>
			   <isEmpty property="createDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="updateUserLabel">#updateUserLabel#</isNotNull>
			<isNotNull prepend=", " property="updateDate">
			   <isNotEmpty property="updateDate">#updateDate#</isNotEmpty>
			   <isEmpty property="updateDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">#deleteUserLabel#</isNotNull>
			<isNotNull prepend=", " property="deleteDate">
			   <isNotEmpty property="deleteDate">#deleteDate#</isNotEmpty>
			   <isEmpty property="deleteDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="recordVersion">#recordVersion#</isNotNull>
			<isNotNull prepend=", " property="legalName">#legalName#</isNotNull>
			<isNotNull prepend=", " property="gfType">#gfType#</isNotNull>
			<isNotNull prepend=", " property="codePath">#codePath#</isNotNull>
			<isNotNull prepend=", " property="namePath">#namePath#</isNotNull>
			<isNotNull prepend=", " property="glbm">#glbm#</isNotNull>
			<isNotNull prepend=", " property="xsqr">#xsqr#</isNotNull>
			<isNotNull prepend=", " property="glry">#glry#</isNotNull>
			<isNotNull prepend=", " property="dwmcpx">#dwmcpx#</isNotNull>
			<isNotNull prepend=", " property="fgs">#fgs#</isNotNull>
			<isNotNull prepend=", " property="depart">#depart#</isNotNull>
			<isNotNull prepend=", " property="deptcb">#deptcb#</isNotNull>
			<isNotNull prepend=", " property="deptcbbm">#deptcbbm#</isNotNull>
			<isNotNull prepend=", " property="personxs">#personxs#</isNotNull>
			<isNotNull prepend=", " property="groupName">#groupName#</isNotNull>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG
		WHERE 		RYXX_ID=#value# 	
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG
		WHERE 
		<dynamic prepend=" ">
			<isNotNull prepend=" AND " property="ryxxId">RYXX_ID = #ryxxId#</isNotNull>
			<isNotNull prepend=" AND " property="applyId">APPLY_ID = #applyId#</isNotNull>
			<isNotNull prepend=" AND " property="patentId">PATENT_ID = #patentId#</isNotNull>
			<isNotNull prepend=" AND " property="ryxh">RYXH = #ryxh#</isNotNull>
			<isNotNull prepend=" AND " property="rylx">RYLX = #rylx#</isNotNull>
			<isNotNull prepend=" AND " property="legalId">LEGAL_ID = #legalId#</isNotNull>
			<isNotNull prepend=" AND " property="empId">EMP_ID = #empId#</isNotNull>
			<isNotNull prepend=" AND " property="empName">EMP_NAME = #empName#</isNotNull>
			<isNotNull prepend=" AND " property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
			<isNotNull prepend=" AND " property="deptName">DEPT_NAME = #deptName#</isNotNull>
			<isNotNull prepend=" AND " property="postTitle">POST_TITLE = #postTitle#</isNotNull>
			<isNotNull prepend=" AND " property="postLevel">POST_LEVEL = #postLevel#</isNotNull>
			<isNotNull prepend=" AND " property="postName">POST_NAME = #postName#</isNotNull>
			<isNotNull prepend=" AND " property="gxxs">GXXS = #gxxs#</isNotNull>
			<isNotNull prepend=" AND " property="idCard">ID_CARD = #idCard#</isNotNull>
			<isNotNull prepend=" AND " property="contentMemo">CONTENT_MEMO = #contentMemo#</isNotNull>
			<isNotNull prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
			<isNotNull prepend=" AND " property="legalName">LEGAL_NAME = #legalName#</isNotNull>
			<isNotNull prepend=" AND " property="gfType">GF_TYPE = #gfType#</isNotNull>
			<isNotNull prepend=" AND " property="codePath">CODE_PATH = #codePath#</isNotNull>
			<isNotNull prepend=" AND " property="namePath">NAME_PATH = #namePath#</isNotNull>
			<isNotNull prepend=" AND " property="glbm">GLBM = #glbm#</isNotNull>
			<isNotNull prepend=" AND " property="xsqr">XSQR = #xsqr#</isNotNull>
			<isNotNull prepend=" AND " property="glry">GLRY = #glry#</isNotNull>
			<isNotNull prepend=" AND " property="dwmcpx">DWMCPX = #dwmcpx#</isNotNull>
			<isNotNull prepend=" AND " property="fgs">FGS = #fgs#</isNotNull>
			<isNotNull prepend=" AND " property="depart">DEPART = #depart#</isNotNull>
			<isNotNull prepend=" AND " property="deptcb">DEPTCB = #deptcb#</isNotNull>
			<isNotNull prepend=" AND " property="deptcbbm">DEPTCBBM = #deptcbbm#</isNotNull>
			<isNotNull prepend=" AND " property="personxs">PERSONXS = #personxs#</isNotNull>
			<isNotNull prepend=" AND " property="groupName">GROUP_NAME = #groupName#</isNotNull>
		</dynamic>	
	</delete>

	<update id="update" parameterClass="tKIZLApplyRyxxHg">
		UPDATE  ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG	
		SET 
		<dynamic prepend=" ">
		<isNotNull prepend="," property="ryxxId">RYXX_ID = #ryxxId#</isNotNull>
		<isNotNull prepend="," property="applyId">APPLY_ID = #applyId#</isNotNull>
		<isNotNull prepend="," property="patentId">PATENT_ID = #patentId#</isNotNull>
		<isNotNull prepend="," property="ryxh">RYXH = #ryxh#</isNotNull>
		<isNotNull prepend="," property="rylx">RYLX = #rylx#</isNotNull>
		<isNotNull prepend="," property="legalId">LEGAL_ID = #legalId#</isNotNull>
		<isNotNull prepend="," property="empId">EMP_ID = #empId#</isNotNull>
		<isNotNull prepend="," property="empName">EMP_NAME = #empName#</isNotNull>
		<isNotNull prepend="," property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
		<isNotNull prepend="," property="deptName">DEPT_NAME = #deptName#</isNotNull>
		<isNotNull prepend="," property="postTitle">POST_TITLE = #postTitle#</isNotNull>
		<isNotNull prepend="," property="postLevel">POST_LEVEL = #postLevel#</isNotNull>
		<isNotNull prepend="," property="postName">POST_NAME = #postName#</isNotNull>
		<isNotNull prepend="," property="gxxs">GXXS = #gxxs#</isNotNull>
		<isNotNull prepend="," property="idCard">ID_CARD = #idCard#</isNotNull>
		<isNotNull prepend="," property="contentMemo">CONTENT_MEMO = #contentMemo#</isNotNull>
		<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
		<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
		<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
		<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
		<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
		<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
		<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
		<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
		<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
		<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
		<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		<isNotNull prepend="," property="legalName">LEGAL_NAME = #legalName#</isNotNull>
		<isNotNull prepend="," property="gfType">GF_TYPE = #gfType#</isNotNull>
		<isNotNull prepend="," property="codePath">CODE_PATH = #codePath#</isNotNull>
		<isNotNull prepend="," property="namePath">NAME_PATH = #namePath#</isNotNull>
		<isNotNull prepend="," property="glbm">GLBM = #glbm#</isNotNull>
		<isNotNull prepend="," property="xsqr">XSQR = #xsqr#</isNotNull>
		<isNotNull prepend="," property="glry">GLRY = #glry#</isNotNull>
		<isNotNull prepend="," property="dwmcpx">DWMCPX = #dwmcpx#</isNotNull>
		<isNotNull prepend="," property="fgs">FGS = #fgs#</isNotNull>
		<isNotNull prepend="," property="depart">DEPART = #depart#</isNotNull>
		<isNotNull prepend="," property="deptcb">DEPTCB = #deptcb#</isNotNull>
		<isNotNull prepend="," property="deptcbbm">DEPTCBBM = #deptcbbm#</isNotNull>
		<isNotNull prepend="," property="personxs">PERSONXS = #personxs#</isNotNull>
		<isNotNull prepend="," property="groupName">GROUP_NAME = #groupName#</isNotNull>
		</dynamic>
		WHERE 		RYXX_ID=#ryxxId# 			</update>
	
	<update id="updatewithnull" parameterClass="tKIZLApplyRyxxHg">
		UPDATE ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG	
		SET 
		<dynamic prepend=" ">
	    <isNotNull prepend=", " property="ryxxId">RYXX_ID = #ryxxId#</isNotNull>
	    <isNull prepend=", " property="ryxxId">RYXX_ID = NULL</isNull>
	    <isNotNull prepend=", " property="applyId">APPLY_ID = #applyId#</isNotNull>
	    <isNull prepend=", " property="applyId">APPLY_ID = NULL</isNull>
	    <isNotNull prepend=", " property="patentId">PATENT_ID = #patentId#</isNotNull>
	    <isNull prepend=", " property="patentId">PATENT_ID = NULL</isNull>
	    <isNotNull prepend=", " property="ryxh">RYXH = #ryxh#</isNotNull>
	    <isNull prepend=", " property="ryxh">RYXH = NULL</isNull>
	    <isNotNull prepend=", " property="rylx">RYLX = #rylx#</isNotNull>
	    <isNull prepend=", " property="rylx">RYLX = NULL</isNull>
	    <isNotNull prepend=", " property="legalId">LEGAL_ID = #legalId#</isNotNull>
	    <isNull prepend=", " property="legalId">LEGAL_ID = NULL</isNull>
	    <isNotNull prepend=", " property="empId">EMP_ID = #empId#</isNotNull>
	    <isNull prepend=", " property="empId">EMP_ID = NULL</isNull>
	    <isNotNull prepend=", " property="empName">EMP_NAME = #empName#</isNotNull>
	    <isNull prepend=", " property="empName">EMP_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
	    <isNull prepend=", " property="deptCode">DEPT_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="deptName">DEPT_NAME = #deptName#</isNotNull>
	    <isNull prepend=", " property="deptName">DEPT_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="postTitle">POST_TITLE = #postTitle#</isNotNull>
	    <isNull prepend=", " property="postTitle">POST_TITLE = NULL</isNull>
	    <isNotNull prepend=", " property="postLevel">POST_LEVEL = #postLevel#</isNotNull>
	    <isNull prepend=", " property="postLevel">POST_LEVEL = NULL</isNull>
	    <isNotNull prepend=", " property="postName">POST_NAME = #postName#</isNotNull>
	    <isNull prepend=", " property="postName">POST_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="gxxs">GXXS = #gxxs#</isNotNull>
	    <isNull prepend=", " property="gxxs">GXXS = NULL</isNull>
	    <isNotNull prepend=", " property="idCard">ID_CARD = #idCard#</isNotNull>
	    <isNull prepend=", " property="idCard">ID_CARD = NULL</isNull>
	    <isNotNull prepend=", " property="contentMemo">CONTENT_MEMO = #contentMemo#</isNotNull>
	    <isNull prepend=", " property="contentMemo">CONTENT_MEMO = NULL</isNull>
	    <isNotNull prepend=", " property="extra1">EXTRA1 = #extra1#</isNotNull>
	    <isNull prepend=", " property="extra1">EXTRA1 = NULL</isNull>
	    <isNotNull prepend=", " property="extra2">EXTRA2 = #extra2#</isNotNull>
	    <isNull prepend=", " property="extra2">EXTRA2 = NULL</isNull>
	    <isNotNull prepend=", " property="extra3">EXTRA3 = #extra3#</isNotNull>
	    <isNull prepend=", " property="extra3">EXTRA3 = NULL</isNull>
	    <isNotNull prepend=", " property="extra4">EXTRA4 = #extra4#</isNotNull>
	    <isNull prepend=", " property="extra4">EXTRA4 = NULL</isNull>
	    <isNotNull prepend=", " property="extra5">EXTRA5 = #extra5#</isNotNull>
	    <isNull prepend=", " property="extra5">EXTRA5 = NULL</isNull>
	    <isNotNull prepend=", " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
	    <isNull prepend=", " property="delStatus">DEL_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
	    <isNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="createDate">CREATE_DATE = #createDate#</isNotNull>
	    <isNull prepend=", " property="createDate">CREATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
	    <isNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNull prepend=", " property="updateDate">UPDATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
	    <isNull prepend=", " property="recordVersion">RECORD_VERSION = NULL</isNull>
	    <isNotNull prepend=", " property="legalName">LEGAL_NAME = #legalName#</isNotNull>
	    <isNull prepend=", " property="legalName">LEGAL_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="gfType">GF_TYPE = #gfType#</isNotNull>
	    <isNull prepend=", " property="gfType">GF_TYPE = NULL</isNull>
	    <isNotNull prepend=", " property="codePath">CODE_PATH = #codePath#</isNotNull>
	    <isNull prepend=", " property="codePath">CODE_PATH = NULL</isNull>
	    <isNotNull prepend=", " property="namePath">NAME_PATH = #namePath#</isNotNull>
	    <isNull prepend=", " property="namePath">NAME_PATH = NULL</isNull>
	    <isNotNull prepend=", " property="glbm">GLBM = #glbm#</isNotNull>
	    <isNull prepend=", " property="glbm">GLBM = NULL</isNull>
	    <isNotNull prepend=", " property="xsqr">XSQR = #xsqr#</isNotNull>
	    <isNull prepend=", " property="xsqr">XSQR = NULL</isNull>
	    <isNotNull prepend=", " property="glry">GLRY = #glry#</isNotNull>
	    <isNull prepend=", " property="glry">GLRY = NULL</isNull>
	    <isNotNull prepend=", " property="dwmcpx">DWMCPX = #dwmcpx#</isNotNull>
	    <isNull prepend=", " property="dwmcpx">DWMCPX = NULL</isNull>
	    <isNotNull prepend=", " property="fgs">FGS = #fgs#</isNotNull>
	    <isNull prepend=", " property="fgs">FGS = NULL</isNull>
	    <isNotNull prepend=", " property="depart">DEPART = #depart#</isNotNull>
	    <isNull prepend=", " property="depart">DEPART = NULL</isNull>
	    <isNotNull prepend=", " property="deptcb">DEPTCB = #deptcb#</isNotNull>
	    <isNull prepend=", " property="deptcb">DEPTCB = NULL</isNull>
	    <isNotNull prepend=", " property="deptcbbm">DEPTCBBM = #deptcbbm#</isNotNull>
	    <isNull prepend=", " property="deptcbbm">DEPTCBBM = NULL</isNull>
	    <isNotNull prepend=", " property="personxs">PERSONXS = #personxs#</isNotNull>
	    <isNull prepend=", " property="personxs">PERSONXS = NULL</isNull>
	    <isNotNull prepend=", " property="groupName">GROUP_NAME = #groupName#</isNotNull>
	    <isNull prepend=", " property="groupName">GROUP_NAME = NULL</isNull>
		</dynamic>
		WHERE 		RYXX_ID=#ryxxId# 			</update>
	
	<update id="updateByC" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="ryxxId">RYXX_ID = #ryxxId#</isNotNull>
				<isNotNull prepend="," property="applyId">APPLY_ID = #applyId#</isNotNull>
				<isNotNull prepend="," property="patentId">PATENT_ID = #patentId#</isNotNull>
				<isNotNull prepend="," property="ryxh">RYXH = #ryxh#</isNotNull>
				<isNotNull prepend="," property="rylx">RYLX = #rylx#</isNotNull>
				<isNotNull prepend="," property="legalId">LEGAL_ID = #legalId#</isNotNull>
				<isNotNull prepend="," property="empId">EMP_ID = #empId#</isNotNull>
				<isNotNull prepend="," property="empName">EMP_NAME = #empName#</isNotNull>
				<isNotNull prepend="," property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
				<isNotNull prepend="," property="deptName">DEPT_NAME = #deptName#</isNotNull>
				<isNotNull prepend="," property="postTitle">POST_TITLE = #postTitle#</isNotNull>
				<isNotNull prepend="," property="postLevel">POST_LEVEL = #postLevel#</isNotNull>
				<isNotNull prepend="," property="postName">POST_NAME = #postName#</isNotNull>
				<isNotNull prepend="," property="gxxs">GXXS = #gxxs#</isNotNull>
				<isNotNull prepend="," property="idCard">ID_CARD = #idCard#</isNotNull>
				<isNotNull prepend="," property="contentMemo">CONTENT_MEMO = #contentMemo#</isNotNull>
				<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
				<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
				<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
				<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
				<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
				<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
				<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
				<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
				<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
				<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
				<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
				<isNotNull prepend="," property="legalName">LEGAL_NAME = #legalName#</isNotNull>
				<isNotNull prepend="," property="gfType">GF_TYPE = #gfType#</isNotNull>
				<isNotNull prepend="," property="codePath">CODE_PATH = #codePath#</isNotNull>
				<isNotNull prepend="," property="namePath">NAME_PATH = #namePath#</isNotNull>
				<isNotNull prepend="," property="glbm">GLBM = #glbm#</isNotNull>
				<isNotNull prepend="," property="xsqr">XSQR = #xsqr#</isNotNull>
				<isNotNull prepend="," property="glry">GLRY = #glry#</isNotNull>
				<isNotNull prepend="," property="dwmcpx">DWMCPX = #dwmcpx#</isNotNull>
				<isNotNull prepend="," property="fgs">FGS = #fgs#</isNotNull>
				<isNotNull prepend="," property="depart">DEPART = #depart#</isNotNull>
				<isNotNull prepend="," property="deptcb">DEPTCB = #deptcb#</isNotNull>
				<isNotNull prepend="," property="deptcbbm">DEPTCBBM = #deptcbbm#</isNotNull>
				<isNotNull prepend="," property="personxs">PERSONXS = #personxs#</isNotNull>
				<isNotNull prepend="," property="groupName">GROUP_NAME = #groupName#</isNotNull>
			</dynamic>
		<dynamic prepend=" WHERE ">
		 			<isNotNull prepend=" AND " property="ryxxIdOld">RYXX_ID = #ryxxIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="applyIdOld">APPLY_ID = #applyIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="patentIdOld">PATENT_ID = #patentIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="ryxhOld">RYXH = #ryxhOld#</isNotNull>
			<isNotNull prepend=" AND " property="rylxOld">RYLX = #rylxOld#</isNotNull>
			<isNotNull prepend=" AND " property="legalIdOld">LEGAL_ID = #legalIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="empIdOld">EMP_ID = #empIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="empNameOld">EMP_NAME = #empNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="deptCodeOld">DEPT_CODE = #deptCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="deptNameOld">DEPT_NAME = #deptNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="postTitleOld">POST_TITLE = #postTitleOld#</isNotNull>
			<isNotNull prepend=" AND " property="postLevelOld">POST_LEVEL = #postLevelOld#</isNotNull>
			<isNotNull prepend=" AND " property="postNameOld">POST_NAME = #postNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="gxxsOld">GXXS = #gxxsOld#</isNotNull>
			<isNotNull prepend=" AND " property="idCardOld">ID_CARD = #idCardOld#</isNotNull>
			<isNotNull prepend=" AND " property="contentMemoOld">CONTENT_MEMO = #contentMemoOld#</isNotNull>
			<isNotNull prepend=" AND " property="extra1Old">EXTRA1 = #extra1Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra2Old">EXTRA2 = #extra2Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra3Old">EXTRA3 = #extra3Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra4Old">EXTRA4 = #extra4Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra5Old">EXTRA5 = #extra5Old#</isNotNull>
			<isNotNull prepend=" AND " property="delStatusOld">DEL_STATUS = #delStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabelOld">CREATE_USER_LABEL = #createUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="createDateOld">CREATE_DATE = #createDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabelOld">UPDATE_USER_LABEL = #updateUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateDateOld">UPDATE_DATE = #updateDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabelOld">DELETE_USER_LABEL = #deleteUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDateOld">DELETE_DATE = #deleteDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersionOld">RECORD_VERSION = #recordVersionOld#</isNotNull>
			<isNotNull prepend=" AND " property="legalNameOld">LEGAL_NAME = #legalNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="gfTypeOld">GF_TYPE = #gfTypeOld#</isNotNull>
			<isNotNull prepend=" AND " property="codePathOld">CODE_PATH = #codePathOld#</isNotNull>
			<isNotNull prepend=" AND " property="namePathOld">NAME_PATH = #namePathOld#</isNotNull>
			<isNotNull prepend=" AND " property="glbmOld">GLBM = #glbmOld#</isNotNull>
			<isNotNull prepend=" AND " property="xsqrOld">XSQR = #xsqrOld#</isNotNull>
			<isNotNull prepend=" AND " property="glryOld">GLRY = #glryOld#</isNotNull>
			<isNotNull prepend=" AND " property="dwmcpxOld">DWMCPX = #dwmcpxOld#</isNotNull>
			<isNotNull prepend=" AND " property="fgsOld">FGS = #fgsOld#</isNotNull>
			<isNotNull prepend=" AND " property="departOld">DEPART = #departOld#</isNotNull>
			<isNotNull prepend=" AND " property="deptcbOld">DEPTCB = #deptcbOld#</isNotNull>
			<isNotNull prepend=" AND " property="deptcbbmOld">DEPTCBBM = #deptcbbmOld#</isNotNull>
			<isNotNull prepend=" AND " property="personxsOld">PERSONXS = #personxsOld#</isNotNull>
			<isNotNull prepend=" AND " property="groupNameOld">GROUP_NAME = #groupNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="dynSqlOld"> $dynSqlOld$ </isNotNull>
		</dynamic>
	</update>
	
	<update id="updateNull" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="ryxxId">RYXX_ID = #ryxxId#</isNotNull>
			<isNotNull prepend="," property="applyId">APPLY_ID = #applyId#</isNotNull>
			<isNotNull prepend="," property="patentId">PATENT_ID = #patentId#</isNotNull>
			<isNotNull prepend="," property="ryxh">RYXH = #ryxh#</isNotNull>
			<isNotNull prepend="," property="rylx">RYLX = #rylx#</isNotNull>
			<isNotNull prepend="," property="legalId">LEGAL_ID = #legalId#</isNotNull>
			<isNotNull prepend="," property="empId">EMP_ID = #empId#</isNotNull>
			<isNotNull prepend="," property="empName">EMP_NAME = #empName#</isNotNull>
			<isNotNull prepend="," property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
			<isNotNull prepend="," property="deptName">DEPT_NAME = #deptName#</isNotNull>
			<isNotNull prepend="," property="postTitle">POST_TITLE = #postTitle#</isNotNull>
			<isNotNull prepend="," property="postLevel">POST_LEVEL = #postLevel#</isNotNull>
			<isNotNull prepend="," property="postName">POST_NAME = #postName#</isNotNull>
			<isNotNull prepend="," property="gxxs">GXXS = #gxxs#</isNotNull>
			<isNotNull prepend="," property="idCard">ID_CARD = #idCard#</isNotNull>
			<isNotNull prepend="," property="contentMemo">CONTENT_MEMO = #contentMemo#</isNotNull>
			<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			<isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
			<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
			<isNotNull prepend="," property="legalName">LEGAL_NAME = #legalName#</isNotNull>
			<isNotNull prepend="," property="gfType">GF_TYPE = #gfType#</isNotNull>
			<isNotNull prepend="," property="codePath">CODE_PATH = #codePath#</isNotNull>
			<isNotNull prepend="," property="namePath">NAME_PATH = #namePath#</isNotNull>
			<isNotNull prepend="," property="glbm">GLBM = #glbm#</isNotNull>
			<isNotNull prepend="," property="xsqr">XSQR = #xsqr#</isNotNull>
			<isNotNull prepend="," property="glry">GLRY = #glry#</isNotNull>
			<isNotNull prepend="," property="dwmcpx">DWMCPX = #dwmcpx#</isNotNull>
			<isNotNull prepend="," property="fgs">FGS = #fgs#</isNotNull>
			<isNotNull prepend="," property="depart">DEPART = #depart#</isNotNull>
			<isNotNull prepend="," property="deptcb">DEPTCB = #deptcb#</isNotNull>
			<isNotNull prepend="," property="deptcbbm">DEPTCBBM = #deptcbbm#</isNotNull>
			<isNotNull prepend="," property="personxs">PERSONXS = #personxs#</isNotNull>
			<isNotNull prepend="," property="groupName">GROUP_NAME = #groupName#</isNotNull>
		</dynamic>
		WHERE 		RYXX_ID=#ryxxId# 			</update>	
</sqlMap>