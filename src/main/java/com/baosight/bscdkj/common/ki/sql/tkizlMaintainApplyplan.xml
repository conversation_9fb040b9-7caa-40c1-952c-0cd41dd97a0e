<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlMaintainApplyplan">
	<typeAlias alias="tKIZLMaintainApplyplan" type="com.baosight.bscdkj.common.ki.domain.TkizlMaintainApplyplan"/>
	<select id="load" parameterClass="string" resultClass="tKIZLMaintainApplyplan">
		SELECT 
				PLAN_ID as "planId" ,		
				YEAR as "year" ,		
				DEPT_CODE as "deptCode" ,		
				DEPT_NAME as "deptName" ,		
				DEPT_PATH as "deptPath" ,		
				PLAN as "plan" ,		
				INV_PLAN as "invPlan" ,		
				THREE_YEAR_AVE as "threeYearAve" ,		
				THREE_YEAR_INV as "threeYearInv" ,		
				THREE_PROP_INV as "threePropInv" ,		
				THREE_YEAR_BAST as "threeYearBast" ,		
				THREE_INV_BAST as "threeInvBast" ,		
				THREE_PROP_BAST as "threePropBast" ,		
				NEAR_PROP_BAST as "nearPropBast" ,		
				COMPANY_CODE as "companyCode" ,		
				COMPANY_NAME as "companyName" ,		
				MBZ as "mbz" ,		
				FMMBZ as "fmmbz" ,		
				FMBLMBZ as "fmblmbz" ,		
				EXTRA1 as "extra1" ,		
				EXTRA2 as "extra2" ,		
				EXTRA3 as "extra3" ,		
				EXTRA4 as "extra4" ,		
				EXTRA5 as "extra5" ,		
				DEL_STATUS as "delStatus" ,		
				CREATE_USER_LABEL as "createUserLabel" ,		
				CREATE_DATE as "createDate" ,		
				UPDATE_USER_LABEL as "updateUserLabel" ,		
				UPDATE_DATE as "updateDate" ,		
				DELETE_USER_LABEL as "deleteUserLabel" ,		
				DELETE_DATE as "deleteDate" ,		
				RECORD_VERSION as "recordVersion" 		
				FROM ${zzzcSchema}.T_KIZL_MAINTAIN_APPLYPLAN
		WHERE 	PLAN_ID=#value# 
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tKIZLMaintainApplyplan">
		SELECT
				PLAN_ID  as "planId" ,		
				YEAR  as "year" ,		
				DEPT_CODE  as "deptCode" ,		
				DEPT_NAME  as "deptName" ,		
				DEPT_PATH  as "deptPath" ,		
				PLAN  as "plan" ,		
				INV_PLAN  as "invPlan" ,		
				THREE_YEAR_AVE  as "threeYearAve" ,		
				THREE_YEAR_INV  as "threeYearInv" ,		
				THREE_PROP_INV  as "threePropInv" ,		
				THREE_YEAR_BAST  as "threeYearBast" ,		
				THREE_INV_BAST  as "threeInvBast" ,		
				THREE_PROP_BAST  as "threePropBast" ,		
				NEAR_PROP_BAST  as "nearPropBast" ,		
				COMPANY_CODE  as "companyCode" ,		
				COMPANY_NAME  as "companyName" ,		
				MBZ  as "mbz" ,		
				FMMBZ  as "fmmbz" ,		
				FMBLMBZ  as "fmblmbz" ,		
				EXTRA1  as "extra1" ,		
				EXTRA2  as "extra2" ,		
				EXTRA3  as "extra3" ,		
				EXTRA4  as "extra4" ,		
				EXTRA5  as "extra5" ,		
				DEL_STATUS  as "delStatus" ,		
				CREATE_USER_LABEL  as "createUserLabel" ,		
				CREATE_DATE  as "createDate" ,		
				UPDATE_USER_LABEL  as "updateUserLabel" ,		
				UPDATE_DATE  as "updateDate" ,		
				DELETE_USER_LABEL  as "deleteUserLabel" ,		
				DELETE_DATE  as "deleteDate" ,		
				RECORD_VERSION  as "recordVersion" 		
				FROM ${zzzcSchema}.T_KIZL_MAINTAIN_APPLYPLAN
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="planId">PLAN_ID =  #planId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="year">YEAR =  #year#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE =  #deptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptName">DEPT_NAME =  #deptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptNameLike">DEPT_NAME like  '%$deptNameLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptPath">DEPT_PATH =  #deptPath#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="plan">PLAN =  #plan#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="invPlan">INV_PLAN =  #invPlan#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="threeYearAve">THREE_YEAR_AVE =  #threeYearAve#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="threeYearInv">THREE_YEAR_INV =  #threeYearInv#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="threePropInv">THREE_PROP_INV =  #threePropInv#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="threeYearBast">THREE_YEAR_BAST =  #threeYearBast#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="threeInvBast">THREE_INV_BAST =  #threeInvBast#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="threePropBast">THREE_PROP_BAST =  #threePropBast#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="nearPropBast">NEAR_PROP_BAST =  #nearPropBast#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="companyCode">COMPANY_CODE =  #companyCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="companyName">COMPANY_NAME =  #companyName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="mbz">MBZ =  #mbz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fmmbz">FMMBZ =  #fmmbz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fmblmbz">FMBLMBZ =  #fmblmbz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KIZL_MAINTAIN_APPLYPLAN 
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="planId">PLAN_ID =  #planId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="year">YEAR =  #year#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE =  #deptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptName">DEPT_NAME =  #deptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptNameLike">DEPT_NAME like  '%$deptNameLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptPath">DEPT_PATH =  #deptPath#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="plan">PLAN =  #plan#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="invPlan">INV_PLAN =  #invPlan#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="threeYearAve">THREE_YEAR_AVE =  #threeYearAve#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="threeYearInv">THREE_YEAR_INV =  #threeYearInv#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="threePropInv">THREE_PROP_INV =  #threePropInv#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="threeYearBast">THREE_YEAR_BAST =  #threeYearBast#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="threeInvBast">THREE_INV_BAST =  #threeInvBast#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="threePropBast">THREE_PROP_BAST =  #threePropBast#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="nearPropBast">NEAR_PROP_BAST =  #nearPropBast#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="companyCode">COMPANY_CODE =  #companyCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="companyName">COMPANY_NAME =  #companyName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="mbz">MBZ =  #mbz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fmmbz">FMMBZ =  #fmmbz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fmblmbz">FMBLMBZ =  #fmblmbz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tKIZLMaintainApplyplan">
		INSERT INTO ${zzzcSchema}.T_KIZL_MAINTAIN_APPLYPLAN ( 
		<dynamic prepend=" ">
					<isNotNull prepend=", " property="planId">PLAN_ID </isNotNull>
					<isNotNull prepend=", " property="year">YEAR </isNotNull>
					<isNotNull prepend=", " property="deptCode">DEPT_CODE </isNotNull>
					<isNotNull prepend=", " property="deptName">DEPT_NAME </isNotNull>
					<isNotNull prepend=", " property="deptPath">DEPT_PATH </isNotNull>
					<isNotNull prepend=", " property="plan">PLAN </isNotNull>
					<isNotNull prepend=", " property="invPlan">INV_PLAN </isNotNull>
					<isNotNull prepend=", " property="threeYearAve">THREE_YEAR_AVE </isNotNull>
					<isNotNull prepend=", " property="threeYearInv">THREE_YEAR_INV </isNotNull>
					<isNotNull prepend=", " property="threePropInv">THREE_PROP_INV </isNotNull>
					<isNotNull prepend=", " property="threeYearBast">THREE_YEAR_BAST </isNotNull>
					<isNotNull prepend=", " property="threeInvBast">THREE_INV_BAST </isNotNull>
					<isNotNull prepend=", " property="threePropBast">THREE_PROP_BAST </isNotNull>
					<isNotNull prepend=", " property="nearPropBast">NEAR_PROP_BAST </isNotNull>
					<isNotNull prepend=", " property="companyCode">COMPANY_CODE </isNotNull>
					<isNotNull prepend=", " property="companyName">COMPANY_NAME </isNotNull>
					<isNotNull prepend=", " property="mbz">MBZ </isNotNull>
					<isNotNull prepend=", " property="fmmbz">FMMBZ </isNotNull>
					<isNotNull prepend=", " property="fmblmbz">FMBLMBZ </isNotNull>
					<isNotNull prepend=", " property="extra1">EXTRA1 </isNotNull>
					<isNotNull prepend=", " property="extra2">EXTRA2 </isNotNull>
					<isNotNull prepend=", " property="extra3">EXTRA3 </isNotNull>
					<isNotNull prepend=", " property="extra4">EXTRA4 </isNotNull>
					<isNotNull prepend=", " property="extra5">EXTRA5 </isNotNull>
					<isNotNull prepend=", " property="delStatus">DEL_STATUS </isNotNull>
					<isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="createDate">CREATE_DATE </isNotNull>
					<isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="updateDate">UPDATE_DATE </isNotNull>
					<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="deleteDate">DELETE_DATE </isNotNull>
					<isNotNull prepend=", " property="recordVersion">RECORD_VERSION </isNotNull>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotNull prepend=", " property="planId">#planId#</isNotNull>
			<isNotNull prepend=", " property="year">#year#</isNotNull>
			<isNotNull prepend=", " property="deptCode">#deptCode#</isNotNull>
			<isNotNull prepend=", " property="deptName">#deptName#</isNotNull>
			<isNotNull prepend=", " property="deptPath">#deptPath#</isNotNull>
			<isNotNull prepend=", " property="plan">#plan#</isNotNull>
			<isNotNull prepend=", " property="invPlan">#invPlan#</isNotNull>
			<isNotNull prepend=", " property="threeYearAve">#threeYearAve#</isNotNull>
			<isNotNull prepend=", " property="threeYearInv">#threeYearInv#</isNotNull>
			<isNotNull prepend=", " property="threePropInv">#threePropInv#</isNotNull>
			<isNotNull prepend=", " property="threeYearBast">#threeYearBast#</isNotNull>
			<isNotNull prepend=", " property="threeInvBast">#threeInvBast#</isNotNull>
			<isNotNull prepend=", " property="threePropBast">#threePropBast#</isNotNull>
			<isNotNull prepend=", " property="nearPropBast">#nearPropBast#</isNotNull>
			<isNotNull prepend=", " property="companyCode">#companyCode#</isNotNull>
			<isNotNull prepend=", " property="companyName">#companyName#</isNotNull>
			<isNotNull prepend=", " property="mbz">#mbz#</isNotNull>
			<isNotNull prepend=", " property="fmmbz">#fmmbz#</isNotNull>
			<isNotNull prepend=", " property="fmblmbz">#fmblmbz#</isNotNull>
			<isNotNull prepend=", " property="extra1">#extra1#</isNotNull>
			<isNotNull prepend=", " property="extra2">#extra2#</isNotNull>
			<isNotNull prepend=", " property="extra3">#extra3#</isNotNull>
			<isNotNull prepend=", " property="extra4">#extra4#</isNotNull>
			<isNotNull prepend=", " property="extra5">#extra5#</isNotNull>
			<isNotNull prepend=", " property="delStatus">#delStatus#</isNotNull>
			<isNotNull prepend=", " property="createUserLabel">#createUserLabel#</isNotNull>
			<isNotNull prepend=", " property="createDate">
			   <isNotEmpty property="createDate">#createDate#</isNotEmpty>
			   <isEmpty property="createDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="updateUserLabel">#updateUserLabel#</isNotNull>
			<isNotNull prepend=", " property="updateDate">
			   <isNotEmpty property="updateDate">#updateDate#</isNotEmpty>
			   <isEmpty property="updateDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">#deleteUserLabel#</isNotNull>
			<isNotNull prepend=", " property="deleteDate">
			   <isNotEmpty property="deleteDate">#deleteDate#</isNotEmpty>
			   <isEmpty property="deleteDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="recordVersion">#recordVersion#</isNotNull>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_APPLYPLAN
		WHERE  PLAN_ID=#value# 	
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_APPLYPLAN
		WHERE PLAN_ID=#value# 	
		<dynamic prepend=" ">
			<isNotNull prepend=" AND " property="planId">PLAN_ID = #planId#</isNotNull>
			<isNotNull prepend=" AND " property="year">YEAR = #year#</isNotNull>
			<isNotNull prepend=" AND " property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
			<isNotNull prepend=" AND " property="deptName">DEPT_NAME = #deptName#</isNotNull>
			<isNotNull prepend=" AND " property="deptPath">DEPT_PATH = #deptPath#</isNotNull>
			<isNotNull prepend=" AND " property="plan">PLAN = #plan#</isNotNull>
			<isNotNull prepend=" AND " property="invPlan">INV_PLAN = #invPlan#</isNotNull>
			<isNotNull prepend=" AND " property="threeYearAve">THREE_YEAR_AVE = #threeYearAve#</isNotNull>
			<isNotNull prepend=" AND " property="threeYearInv">THREE_YEAR_INV = #threeYearInv#</isNotNull>
			<isNotNull prepend=" AND " property="threePropInv">THREE_PROP_INV = #threePropInv#</isNotNull>
			<isNotNull prepend=" AND " property="threeYearBast">THREE_YEAR_BAST = #threeYearBast#</isNotNull>
			<isNotNull prepend=" AND " property="threeInvBast">THREE_INV_BAST = #threeInvBast#</isNotNull>
			<isNotNull prepend=" AND " property="threePropBast">THREE_PROP_BAST = #threePropBast#</isNotNull>
			<isNotNull prepend=" AND " property="nearPropBast">NEAR_PROP_BAST = #nearPropBast#</isNotNull>
			<isNotNull prepend=" AND " property="companyCode">COMPANY_CODE = #companyCode#</isNotNull>
			<isNotNull prepend=" AND " property="companyName">COMPANY_NAME = #companyName#</isNotNull>
			<isNotNull prepend=" AND " property="mbz">MBZ = #mbz#</isNotNull>
			<isNotNull prepend=" AND " property="fmmbz">FMMBZ = #fmmbz#</isNotNull>
			<isNotNull prepend=" AND " property="fmblmbz">FMBLMBZ = #fmblmbz#</isNotNull>
			<isNotNull prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>	
	</delete>

	<update id="update" parameterClass="tKIZLMaintainApplyplan">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_APPLYPLAN	
		SET 
		<dynamic prepend=" ">
		<isNotNull prepend="," property="planId">PLAN_ID = #planId#</isNotNull>
		<isNotNull prepend="," property="year">YEAR = #year#</isNotNull>
		<isNotNull prepend="," property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
		<isNotNull prepend="," property="deptName">DEPT_NAME = #deptName#</isNotNull>
		<isNotNull prepend="," property="deptPath">DEPT_PATH = #deptPath#</isNotNull>
		<isNotNull prepend="," property="plan">PLAN = #plan#</isNotNull>
		<isNotNull prepend="," property="invPlan">INV_PLAN = #invPlan#</isNotNull>
		<isNotNull prepend="," property="threeYearAve">THREE_YEAR_AVE = #threeYearAve#</isNotNull>
		<isNotNull prepend="," property="threeYearInv">THREE_YEAR_INV = #threeYearInv#</isNotNull>
		<isNotNull prepend="," property="threePropInv">THREE_PROP_INV = #threePropInv#</isNotNull>
		<isNotNull prepend="," property="threeYearBast">THREE_YEAR_BAST = #threeYearBast#</isNotNull>
		<isNotNull prepend="," property="threeInvBast">THREE_INV_BAST = #threeInvBast#</isNotNull>
		<isNotNull prepend="," property="threePropBast">THREE_PROP_BAST = #threePropBast#</isNotNull>
		<isNotNull prepend="," property="nearPropBast">NEAR_PROP_BAST = #nearPropBast#</isNotNull>
		<isNotNull prepend="," property="companyCode">COMPANY_CODE = #companyCode#</isNotNull>
		<isNotNull prepend="," property="companyName">COMPANY_NAME = #companyName#</isNotNull>
		<isNotNull prepend="," property="mbz">MBZ = #mbz#</isNotNull>
		<isNotNull prepend="," property="fmmbz">FMMBZ = #fmmbz#</isNotNull>
		<isNotNull prepend="," property="fmblmbz">FMBLMBZ = #fmblmbz#</isNotNull>
		<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
		<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
		<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
		<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
		<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
		<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
		<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
		<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
		<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
		<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
		<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 	PLAN_ID=#planId# 	</update>
	
	<update id="updatewithnull" parameterClass="tKIZLMaintainApplyplan">
		UPDATE ${zzzcSchema}.T_KIZL_MAINTAIN_APPLYPLAN	
		SET 
		<dynamic prepend=" ">
	    <isNotNull prepend=", " property="planId">PLAN_ID = #planId#</isNotNull>
	    <isNull prepend=", " property="planId">PLAN_ID = NULL</isNull>
	    <isNotNull prepend=", " property="year">YEAR = #year#</isNotNull>
	    <isNull prepend=", " property="year">YEAR = NULL</isNull>
	    <isNotNull prepend=", " property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
	    <isNull prepend=", " property="deptCode">DEPT_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="deptName">DEPT_NAME = #deptName#</isNotNull>
	    <isNull prepend=", " property="deptName">DEPT_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="deptPath">DEPT_PATH = #deptPath#</isNotNull>
	    <isNull prepend=", " property="deptPath">DEPT_PATH = NULL</isNull>
	    <isNotNull prepend=", " property="plan">PLAN = #plan#</isNotNull>
	    <isNull prepend=", " property="plan">PLAN = NULL</isNull>
	    <isNotNull prepend=", " property="invPlan">INV_PLAN = #invPlan#</isNotNull>
	    <isNull prepend=", " property="invPlan">INV_PLAN = NULL</isNull>
	    <isNotNull prepend=", " property="threeYearAve">THREE_YEAR_AVE = #threeYearAve#</isNotNull>
	    <isNull prepend=", " property="threeYearAve">THREE_YEAR_AVE = NULL</isNull>
	    <isNotNull prepend=", " property="threeYearInv">THREE_YEAR_INV = #threeYearInv#</isNotNull>
	    <isNull prepend=", " property="threeYearInv">THREE_YEAR_INV = NULL</isNull>
	    <isNotNull prepend=", " property="threePropInv">THREE_PROP_INV = #threePropInv#</isNotNull>
	    <isNull prepend=", " property="threePropInv">THREE_PROP_INV = NULL</isNull>
	    <isNotNull prepend=", " property="threeYearBast">THREE_YEAR_BAST = #threeYearBast#</isNotNull>
	    <isNull prepend=", " property="threeYearBast">THREE_YEAR_BAST = NULL</isNull>
	    <isNotNull prepend=", " property="threeInvBast">THREE_INV_BAST = #threeInvBast#</isNotNull>
	    <isNull prepend=", " property="threeInvBast">THREE_INV_BAST = NULL</isNull>
	    <isNotNull prepend=", " property="threePropBast">THREE_PROP_BAST = #threePropBast#</isNotNull>
	    <isNull prepend=", " property="threePropBast">THREE_PROP_BAST = NULL</isNull>
	    <isNotNull prepend=", " property="nearPropBast">NEAR_PROP_BAST = #nearPropBast#</isNotNull>
	    <isNull prepend=", " property="nearPropBast">NEAR_PROP_BAST = NULL</isNull>
	    <isNotNull prepend=", " property="companyCode">COMPANY_CODE = #companyCode#</isNotNull>
	    <isNull prepend=", " property="companyCode">COMPANY_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="companyName">COMPANY_NAME = #companyName#</isNotNull>
	    <isNull prepend=", " property="companyName">COMPANY_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="mbz">MBZ = #mbz#</isNotNull>
	    <isNull prepend=", " property="mbz">MBZ = NULL</isNull>
	    <isNotNull prepend=", " property="fmmbz">FMMBZ = #fmmbz#</isNotNull>
	    <isNull prepend=", " property="fmmbz">FMMBZ = NULL</isNull>
	    <isNotNull prepend=", " property="fmblmbz">FMBLMBZ = #fmblmbz#</isNotNull>
	    <isNull prepend=", " property="fmblmbz">FMBLMBZ = NULL</isNull>
	    <isNotNull prepend=", " property="extra1">EXTRA1 = #extra1#</isNotNull>
	    <isNull prepend=", " property="extra1">EXTRA1 = NULL</isNull>
	    <isNotNull prepend=", " property="extra2">EXTRA2 = #extra2#</isNotNull>
	    <isNull prepend=", " property="extra2">EXTRA2 = NULL</isNull>
	    <isNotNull prepend=", " property="extra3">EXTRA3 = #extra3#</isNotNull>
	    <isNull prepend=", " property="extra3">EXTRA3 = NULL</isNull>
	    <isNotNull prepend=", " property="extra4">EXTRA4 = #extra4#</isNotNull>
	    <isNull prepend=", " property="extra4">EXTRA4 = NULL</isNull>
	    <isNotNull prepend=", " property="extra5">EXTRA5 = #extra5#</isNotNull>
	    <isNull prepend=", " property="extra5">EXTRA5 = NULL</isNull>
	    <isNotNull prepend=", " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
	    <isNull prepend=", " property="delStatus">DEL_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
	    <isNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="createDate">CREATE_DATE = #createDate#</isNotNull>
	    <isNull prepend=", " property="createDate">CREATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
	    <isNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNull prepend=", " property="updateDate">UPDATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
	    <isNull prepend=", " property="recordVersion">RECORD_VERSION = NULL</isNull>
		</dynamic>
		WHERE 	PLAN_ID=#planId# </update>
	
	<update id="updateByC" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_APPLYPLAN	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="planId">PLAN_ID = #planId#</isNotNull>
				<isNotNull prepend="," property="year">YEAR = #year#</isNotNull>
				<isNotNull prepend="," property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
				<isNotNull prepend="," property="deptName">DEPT_NAME = #deptName#</isNotNull>
				<isNotNull prepend="," property="deptPath">DEPT_PATH = #deptPath#</isNotNull>
				<isNotNull prepend="," property="plan">PLAN = #plan#</isNotNull>
				<isNotNull prepend="," property="invPlan">INV_PLAN = #invPlan#</isNotNull>
				<isNotNull prepend="," property="threeYearAve">THREE_YEAR_AVE = #threeYearAve#</isNotNull>
				<isNotNull prepend="," property="threeYearInv">THREE_YEAR_INV = #threeYearInv#</isNotNull>
				<isNotNull prepend="," property="threePropInv">THREE_PROP_INV = #threePropInv#</isNotNull>
				<isNotNull prepend="," property="threeYearBast">THREE_YEAR_BAST = #threeYearBast#</isNotNull>
				<isNotNull prepend="," property="threeInvBast">THREE_INV_BAST = #threeInvBast#</isNotNull>
				<isNotNull prepend="," property="threePropBast">THREE_PROP_BAST = #threePropBast#</isNotNull>
				<isNotNull prepend="," property="nearPropBast">NEAR_PROP_BAST = #nearPropBast#</isNotNull>
				<isNotNull prepend="," property="companyCode">COMPANY_CODE = #companyCode#</isNotNull>
				<isNotNull prepend="," property="companyName">COMPANY_NAME = #companyName#</isNotNull>
				<isNotNull prepend="," property="mbz">MBZ = #mbz#</isNotNull>
				<isNotNull prepend="," property="fmmbz">FMMBZ = #fmmbz#</isNotNull>
				<isNotNull prepend="," property="fmblmbz">FMBLMBZ = #fmblmbz#</isNotNull>
				<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
				<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
				<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
				<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
				<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
				<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
				<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
				<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
				<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
				<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
				<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
			</dynamic>
		<dynamic prepend=" WHERE ">
		 			<isNotNull prepend=" AND " property="planIdOld">PLAN_ID = #planIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="yearOld">YEAR = #yearOld#</isNotNull>
			<isNotNull prepend=" AND " property="deptCodeOld">DEPT_CODE = #deptCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="deptNameOld">DEPT_NAME = #deptNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="deptPathOld">DEPT_PATH = #deptPathOld#</isNotNull>
			<isNotNull prepend=" AND " property="planOld">PLAN = #planOld#</isNotNull>
			<isNotNull prepend=" AND " property="invPlanOld">INV_PLAN = #invPlanOld#</isNotNull>
			<isNotNull prepend=" AND " property="threeYearAveOld">THREE_YEAR_AVE = #threeYearAveOld#</isNotNull>
			<isNotNull prepend=" AND " property="threeYearInvOld">THREE_YEAR_INV = #threeYearInvOld#</isNotNull>
			<isNotNull prepend=" AND " property="threePropInvOld">THREE_PROP_INV = #threePropInvOld#</isNotNull>
			<isNotNull prepend=" AND " property="threeYearBastOld">THREE_YEAR_BAST = #threeYearBastOld#</isNotNull>
			<isNotNull prepend=" AND " property="threeInvBastOld">THREE_INV_BAST = #threeInvBastOld#</isNotNull>
			<isNotNull prepend=" AND " property="threePropBastOld">THREE_PROP_BAST = #threePropBastOld#</isNotNull>
			<isNotNull prepend=" AND " property="nearPropBastOld">NEAR_PROP_BAST = #nearPropBastOld#</isNotNull>
			<isNotNull prepend=" AND " property="companyCodeOld">COMPANY_CODE = #companyCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="companyNameOld">COMPANY_NAME = #companyNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="mbzOld">MBZ = #mbzOld#</isNotNull>
			<isNotNull prepend=" AND " property="fmmbzOld">FMMBZ = #fmmbzOld#</isNotNull>
			<isNotNull prepend=" AND " property="fmblmbzOld">FMBLMBZ = #fmblmbzOld#</isNotNull>
			<isNotNull prepend=" AND " property="extra1Old">EXTRA1 = #extra1Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra2Old">EXTRA2 = #extra2Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra3Old">EXTRA3 = #extra3Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra4Old">EXTRA4 = #extra4Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra5Old">EXTRA5 = #extra5Old#</isNotNull>
			<isNotNull prepend=" AND " property="delStatusOld">DEL_STATUS = #delStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabelOld">CREATE_USER_LABEL = #createUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="createDateOld">CREATE_DATE = #createDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabelOld">UPDATE_USER_LABEL = #updateUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateDateOld">UPDATE_DATE = #updateDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabelOld">DELETE_USER_LABEL = #deleteUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDateOld">DELETE_DATE = #deleteDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersionOld">RECORD_VERSION = #recordVersionOld#</isNotNull>
			<isNotNull prepend=" AND " property="dynSqlOld"> $dynSqlOld$ </isNotNull>
		</dynamic>
	</update>
	
	<update id="updateNull" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_APPLYPLAN	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="planId">PLAN_ID = #planId#</isNotNull>
			<isNotNull prepend="," property="year">YEAR = #year#</isNotNull>
			<isNotNull prepend="," property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
			<isNotNull prepend="," property="deptName">DEPT_NAME = #deptName#</isNotNull>
			<isNotNull prepend="," property="deptPath">DEPT_PATH = #deptPath#</isNotNull>
			<isNotNull prepend="," property="plan">PLAN = #plan#</isNotNull>
			<isNotNull prepend="," property="invPlan">INV_PLAN = #invPlan#</isNotNull>
			<isNotNull prepend="," property="threeYearAve">THREE_YEAR_AVE = #threeYearAve#</isNotNull>
			<isNotNull prepend="," property="threeYearInv">THREE_YEAR_INV = #threeYearInv#</isNotNull>
			<isNotNull prepend="," property="threePropInv">THREE_PROP_INV = #threePropInv#</isNotNull>
			<isNotNull prepend="," property="threeYearBast">THREE_YEAR_BAST = #threeYearBast#</isNotNull>
			<isNotNull prepend="," property="threeInvBast">THREE_INV_BAST = #threeInvBast#</isNotNull>
			<isNotNull prepend="," property="threePropBast">THREE_PROP_BAST = #threePropBast#</isNotNull>
			<isNotNull prepend="," property="nearPropBast">NEAR_PROP_BAST = #nearPropBast#</isNotNull>
			<isNotNull prepend="," property="companyCode">COMPANY_CODE = #companyCode#</isNotNull>
			<isNotNull prepend="," property="companyName">COMPANY_NAME = #companyName#</isNotNull>
			<isNotNull prepend="," property="mbz">MBZ = #mbz#</isNotNull>
			<isNotNull prepend="," property="fmmbz">FMMBZ = #fmmbz#</isNotNull>
			<isNotNull prepend="," property="fmblmbz">FMBLMBZ = #fmblmbz#</isNotNull>
			<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			<isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
			<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 	PLAN_ID=#planId# </update>	
</sqlMap>