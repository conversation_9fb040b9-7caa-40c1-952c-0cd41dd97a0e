package com.baosight.bscdkj.common.ki.domain;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.bscdkj.common.domain.AbstractDomain;
import javax.validation.constraints.Size;

/**
 * 境内专利_费用_年度缴费计划信息: T_KIZL_MONEY_YEARPLAN
 * 
 * 
 * <AUTHOR>
 */
public class TkizlMoneyYearplan  extends AbstractDomain{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	//主键 : YEARPLAN_ID 
	@Size(max = 36,message = "主键最大为36位字符")	
	private String yearplanId; 
	
	//年份 : ND 
	@Size(max = 4,message = "年份最大为4位字符")	
	private String nd; 
	
	//组织代码 : DEPT_CODE 
	@Size(max = 20,message = "组织代码最大为20位字符")	
	private String deptCode; 
	
	//组织名称 : DEPT_NAME 
	@Size(max = 100,message = "组织名称最大为100位字符")	
	private String deptName; 
	
	//预计发明专利数 : YJ_FM 
	@Size(max = 10,message = "预计发明专利数最大为10位字符")	
	private String yjFm; 
	
	//预计实用新型专利数 : YJ_SYXX 
	@Size(max = 10,message = "预计实用新型专利数最大为10位字符")	
	private String yjSyxx; 
	
	//当年可能授权数 : MAYBE_SQS 
	@Size(max = 10,message = "当年可能授权数最大为10位字符")	
	private String maybeSqs; 
	
	//未授权的实用新型专利 : NOSQ_SYXX 
	@Size(max = 10,message = "未授权的实用新型专利最大为10位字符")	
	private String nosqSyxx; 
	
	//3年以上未授权的发明专利 : NOSQ_FM 
	@Size(max = 10,message = "3年以上未授权的发明专利最大为10位字符")	
	private String nosqFm; 
	
	//申请日在2000年7月后已申报效益奖金额 : XYJJE 
	@Size(max = 20,message = "申请日在2000年7月后已申报效益奖金额最大为20位字符")	
	private java.math.BigDecimal xyjje; 
	
	//扩展字段1 : EXTRA1 
	@Size(max = 30,message = "扩展字段1最大为30位字符")	
	private String extra1; 
	
	//扩展字段2 : EXTRA2 
	@Size(max = 30,message = "扩展字段2最大为30位字符")	
	private String extra2; 
	
	//扩展字段3 : EXTRA3 
	@Size(max = 30,message = "扩展字段3最大为30位字符")	
	private String extra3; 
	
	//扩展字段4 : EXTRA4 
	@Size(max = 30,message = "扩展字段4最大为30位字符")	
	private String extra4; 
	
	//扩展字段5 : EXTRA5 
	@Size(max = 30,message = "扩展字段5最大为30位字符")	
	private String extra5; 
	
	//扩展字段6 : EXTRA6 
	@Size(max = 50,message = "扩展字段6最大为50位字符")	
	private String extra6; 
	
	//扩展字段7 : EXTRA7 
	@Size(max = 50,message = "扩展字段7最大为50位字符")	
	private String extra7; 
	
	//扩展字段8 : EXTRA8 
	@Size(max = 50,message = "扩展字段8最大为50位字符")	
	private String extra8; 
	
	//扩展字段9 : EXTRA9 
	@Size(max = 50,message = "扩展字段9最大为50位字符")	
	private String extra9; 
	
	//扩展字段10 : EXTRA10 
	@Size(max = 50,message = "扩展字段10最大为50位字符")	
	private String extra10; 
	
	//扩展字段11 : EXTRA11 
	@Size(max = 50,message = "扩展字段11最大为50位字符")	
	private String extra11; 
	
	//扩展字段12 : EXTRA12 
	@Size(max = 50,message = "扩展字段12最大为50位字符")	
	private String extra12; 
	
	/**
	 * 主键 : YEARPLAN_ID
	 * 
	 * @return 
	 */
	public String getYearplanId () {
		return yearplanId;
	}
	
	/**
	 * 主键 : YEARPLAN_ID
	 * 
	 * @return 
	 */
	public void setYearplanId (String yearplanId) {
		this.yearplanId = yearplanId;
	}
	/**
	 * 年份 : ND
	 * 
	 * @return 
	 */
	public String getNd () {
		return nd;
	}
	
	/**
	 * 年份 : ND
	 * 
	 * @return 
	 */
	public void setNd (String nd) {
		this.nd = nd;
	}
	/**
	 * 组织代码 : DEPT_CODE
	 * 
	 * @return 
	 */
	public String getDeptCode () {
		return deptCode;
	}
	
	/**
	 * 组织代码 : DEPT_CODE
	 * 
	 * @return 
	 */
	public void setDeptCode (String deptCode) {
		this.deptCode = deptCode;
	}
	/**
	 * 组织名称 : DEPT_NAME
	 * 
	 * @return 
	 */
	public String getDeptName () {
		return deptName;
	}
	
	/**
	 * 组织名称 : DEPT_NAME
	 * 
	 * @return 
	 */
	public void setDeptName (String deptName) {
		this.deptName = deptName;
	}
	/**
	 * 预计发明专利数 : YJ_FM
	 * 
	 * @return 
	 */
	public String getYjFm () {
		return yjFm;
	}
	
	/**
	 * 预计发明专利数 : YJ_FM
	 * 
	 * @return 
	 */
	public void setYjFm (String yjFm) {
		this.yjFm = yjFm;
	}
	/**
	 * 预计实用新型专利数 : YJ_SYXX
	 * 
	 * @return 
	 */
	public String getYjSyxx () {
		return yjSyxx;
	}
	
	/**
	 * 预计实用新型专利数 : YJ_SYXX
	 * 
	 * @return 
	 */
	public void setYjSyxx (String yjSyxx) {
		this.yjSyxx = yjSyxx;
	}
	/**
	 * 当年可能授权数 : MAYBE_SQS
	 * 
	 * @return 
	 */
	public String getMaybeSqs () {
		return maybeSqs;
	}
	
	/**
	 * 当年可能授权数 : MAYBE_SQS
	 * 
	 * @return 
	 */
	public void setMaybeSqs (String maybeSqs) {
		this.maybeSqs = maybeSqs;
	}
	/**
	 * 未授权的实用新型专利 : NOSQ_SYXX
	 * 
	 * @return 
	 */
	public String getNosqSyxx () {
		return nosqSyxx;
	}
	
	/**
	 * 未授权的实用新型专利 : NOSQ_SYXX
	 * 
	 * @return 
	 */
	public void setNosqSyxx (String nosqSyxx) {
		this.nosqSyxx = nosqSyxx;
	}
	/**
	 * 3年以上未授权的发明专利 : NOSQ_FM
	 * 
	 * @return 
	 */
	public String getNosqFm () {
		return nosqFm;
	}
	
	/**
	 * 3年以上未授权的发明专利 : NOSQ_FM
	 * 
	 * @return 
	 */
	public void setNosqFm (String nosqFm) {
		this.nosqFm = nosqFm;
	}
	/**
	 * 申请日在2000年7月后已申报效益奖金额 : XYJJE
	 * 
	 * @return 
	 */
	public java.math.BigDecimal getXyjje () {
		return xyjje;
	}
	
	/**
	 * 申请日在2000年7月后已申报效益奖金额 : XYJJE
	 * 
	 * @return 
	 */
	public void setXyjje (java.math.BigDecimal xyjje) {
		this.xyjje = xyjje;
	}
	/**
	 * 扩展字段1 : EXTRA1
	 * 
	 * @return 
	 */
	public String getExtra1 () {
		return extra1;
	}
	
	/**
	 * 扩展字段1 : EXTRA1
	 * 
	 * @return 
	 */
	public void setExtra1 (String extra1) {
		this.extra1 = extra1;
	}
	/**
	 * 扩展字段2 : EXTRA2
	 * 
	 * @return 
	 */
	public String getExtra2 () {
		return extra2;
	}
	
	/**
	 * 扩展字段2 : EXTRA2
	 * 
	 * @return 
	 */
	public void setExtra2 (String extra2) {
		this.extra2 = extra2;
	}
	/**
	 * 扩展字段3 : EXTRA3
	 * 
	 * @return 
	 */
	public String getExtra3 () {
		return extra3;
	}
	
	/**
	 * 扩展字段3 : EXTRA3
	 * 
	 * @return 
	 */
	public void setExtra3 (String extra3) {
		this.extra3 = extra3;
	}
	/**
	 * 扩展字段4 : EXTRA4
	 * 
	 * @return 
	 */
	public String getExtra4 () {
		return extra4;
	}
	
	/**
	 * 扩展字段4 : EXTRA4
	 * 
	 * @return 
	 */
	public void setExtra4 (String extra4) {
		this.extra4 = extra4;
	}
	/**
	 * 扩展字段5 : EXTRA5
	 * 
	 * @return 
	 */
	public String getExtra5 () {
		return extra5;
	}
	
	/**
	 * 扩展字段5 : EXTRA5
	 * 
	 * @return 
	 */
	public void setExtra5 (String extra5) {
		this.extra5 = extra5;
	}


    public String getExtra6() {
		return extra6;
	}

	public void setExtra6(String extra6) {
		this.extra6 = extra6;
	}

	public String getExtra7() {
		return extra7;
	}

	public void setExtra7(String extra7) {
		this.extra7 = extra7;
	}

	public String getExtra8() {
		return extra8;
	}

	public void setExtra8(String extra8) {
		this.extra8 = extra8;
	}

	public String getExtra9() {
		return extra9;
	}

	public void setExtra9(String extra9) {
		this.extra9 = extra9;
	}

	public String getExtra10() {
		return extra10;
	}

	public void setExtra10(String extra10) {
		this.extra10 = extra10;
	}

	public String getExtra11() {
		return extra11;
	}

	public void setExtra11(String extra11) {
		this.extra11 = extra11;
	}

	public String getExtra12() {
		return extra12;
	}

	public void setExtra12(String extra12) {
		this.extra12 = extra12;
	}

	public void initMetaData(){
    	EiColumn eiColumn;
    	  	
		eiColumn = new EiColumn("yearplanId");
       	eiColumn.setDescName("主键");
        eiColumn.setMaxLength(36);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("nd");
       	eiColumn.setDescName("年份");
        eiColumn.setMaxLength(4);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("deptCode");
       	eiColumn.setDescName("组织代码");
        eiColumn.setMaxLength(20);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("deptName");
       	eiColumn.setDescName("组织名称");
        eiColumn.setMaxLength(100);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("yjFm");
       	eiColumn.setDescName("预计发明专利数");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("yjSyxx");
       	eiColumn.setDescName("预计实用新型专利数");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("maybeSqs");
       	eiColumn.setDescName("当年可能授权数");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("nosqSyxx");
       	eiColumn.setDescName("未授权的实用新型专利");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("nosqFm");
       	eiColumn.setDescName("3年以上未授权的发明专利");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("xyjje");
       	eiColumn.setDescName("申请日在2000年7月后已申报效益奖金额");
        eiColumn.setMaxLength(20);
        eiColumn.setType("N");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra1");
       	eiColumn.setDescName("扩展字段1");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra2");
       	eiColumn.setDescName("扩展字段2");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra3");
       	eiColumn.setDescName("扩展字段3");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra4");
       	eiColumn.setDescName("扩展字段4");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra5");
       	eiColumn.setDescName("扩展字段5");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);
        
        eiColumn = new EiColumn("extra6");
       	eiColumn.setDescName("扩展字段6");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);
        
        eiColumn = new EiColumn("extra7");
       	eiColumn.setDescName("扩展字段7");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);
        
        eiColumn = new EiColumn("extra8");
       	eiColumn.setDescName("扩展字段8");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);
        
        eiColumn = new EiColumn("extra9");
       	eiColumn.setDescName("扩展字段9");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);
        
        eiColumn = new EiColumn("extra10");
       	eiColumn.setDescName("扩展字段10");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);
        
        eiColumn = new EiColumn("extra11");
       	eiColumn.setDescName("扩展字段11");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);
        
        eiColumn = new EiColumn("extra12");
       	eiColumn.setDescName("扩展字段12");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);
        
		eiColumn = new EiColumn("delStatus");
       	eiColumn.setDescName("删除状态");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("createUserLabel");
       	eiColumn.setDescName("创建人");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("createDate");
       	eiColumn.setDescName("创建时间");
        eiColumn.setMaxLength(26);
        eiColumn.setType("date");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("updateUserLabel");
       	eiColumn.setDescName("更新人");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("updateDate");
       	eiColumn.setDescName("更新时间");
        eiColumn.setMaxLength(26);
        eiColumn.setType("date");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("deleteUserLabel");
       	eiColumn.setDescName("删除人");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("deleteDate");
       	eiColumn.setDescName("删除时间");
        eiColumn.setMaxLength(26);
        eiColumn.setType("date");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("recordVersion");
       	eiColumn.setDescName("版本号");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiMetadata.getMeta("yearplanId").setPrimaryKey(true);
	        
    }

    public TkizlMoneyYearplan (){
        initMetaData();
    }
    
}
