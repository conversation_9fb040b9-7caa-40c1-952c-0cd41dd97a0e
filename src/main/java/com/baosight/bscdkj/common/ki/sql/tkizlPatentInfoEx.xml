<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlPatentInfoEx">
	<typeAlias alias="tKIZLPatentInfo" type="com.baosight.bscdkj.common.ki.domain.TkizlPatentInfo"/>
	<typeAlias alias="tKIZLPatentInfoZh" type="com.baosight.bscdkj.common.ki.domain.TkizlPatentInfoZh"/>
	<select id="queryHaveDel"  parameterClass="hashmap" resultClass="tKIZLPatentInfo">
		SELECT
				PATENT_ID  as "patentId" ,		
				APPLY_ID  as "applyId" ,		
				SERIAL_NUM  as "serialNum" ,		
				JSBH  as "jsbh" ,		
				<PERSON>GB<PERSON>  as "bgbh" ,		
				APPLY_NAME  as "applyName" ,		
				FIRST_DEPT_CODE  as "firstDeptCode" ,		
				FIRST_DEPT_NAME  as "firstDeptName" ,		
				GLDW_CODE  as "gldwCode" ,		
				GLDW_NAME  as "gldwName" ,		
				LABEL  as "label" ,		
				TECH_AREA  as "techArea" ,		
				USE_PROPOSE  as "usePropose" ,		
				KNOWLEDGE_CLASS  as "knowledgeClass" ,		
				LXR_CODE  as "lxrCode" ,		
				LXR_NAME  as "lxrName" ,		
				LXR_PHONE  as "lxrPhone" ,		
				LXR_EMAIL  as "lxrEmail" ,		
				LXR_MOBILE  as "lxrMobile" ,		
				FROM_TYPE  as "fromType" ,		
				FROM_NO  as "fromNo" ,		
				FROM_NAME  as "fromName" ,		
				FROM_CONTENT  as "fromContent" ,		
				USE_METHOD  as "useMethod" ,		
				USE_DEPT  as "useDept" ,		
				USE_DEPT_NAME  as "useDeptName" ,		
				USE_EXPECTED  as "useExpected" ,		
				USE_EXPECTED_NAME  as "useExpectedName" ,		
				USE_FIRSTDATE  as "useFirstdate" ,		
				REASON_NOUSE  as "reasonNouse" ,		
				CONTENT_NOUSE  as "contentNouse" ,		
				JPG_FLAG  as "jpgFlag" ,		
				JPG_XH  as "jpgXh" ,		
				JPG_GW  as "jpgGw" ,		
				JPG_TEAM  as "jpgTeam" ,		
				ISBIGXM  as "isbigxm" ,		
				JFDW_CODE  as "jfdwCode" ,		
				FLOW_STATUS  as "flowStatus" ,		
				PATENT_STATUS  as "patentStatus" ,		
				DJD_PERSON  as "djdPerson" ,		
				DJD_DATE  as "djdDate" ,		
				JD_PERSON  as "jdPerson" ,		
				JD_DATE  as "jdDate" ,		
				QS  as "qs" ,		
				SLRQ  as "slrq" ,		
				PATENT_NO  as "patentNo" ,		
				PATENT_TYPE  as "patentType" ,		
				YXQR  as "yxqr" ,		
				QLYQSL  as "qlyqsl" ,		
				SMSYS  as "smsys" ,		
				SWS_GUID  as "swsGuid" ,		
				SWSDLR  as "swsdlr" ,		
				SWSDLR_PHONE  as "swsdlrPhone" ,		
				SWSDLR_EMAIL  as "swsdlrEmail" ,		
				MONEY_DLF  as "moneyDlf" ,		
				MONEY_QF  as "moneyQf" ,		
				MONEY_SQF  as "moneySqf" ,		
				MONEY_GBYSF  as "moneyGbysf" ,		
				MONEY_SMSFJF  as "moneySmsfjf" ,		
				MONEY_YHS  as "moneyYhs" ,		
				SQTZ_FWDATE  as "sqtzFwdate" ,		
				MONEY_FIRST  as "moneyFirst" ,		
				ZLH  as "zlh" ,		
				SQRQ  as "sqrq" ,		
				FLZT  as "flzt" ,		
				ISVALID  as "isvalid" ,		
				ISGD_ARCHIVE  as "isgdArchive" ,		
				ARCHIVE_GUID  as "archiveGuid" ,		
				ISGD_DZ  as "isgdDz" ,		
				EXTRA1  as "extra1" ,		
				EXTRA2  as "extra2" ,		
				EXTRA3  as "extra3" ,		
				EXTRA4  as "extra4" ,		
				EXTRA5  as "extra5" ,		
				EXTRA6  as "extra6" ,		
				DEL_STATUS  as "delStatus" ,		
				CREATE_USER_LABEL  as "createUserLabel" ,		
				CREATE_DATE  as "createDate" ,		
				UPDATE_USER_LABEL  as "updateUserLabel" ,		
				UPDATE_DATE  as "updateDate" ,		
				DELETE_USER_LABEL  as "deleteUserLabel" ,		
				DELETE_DATE  as "deleteDate" ,		
				RECORD_VERSION  as "recordVersion" ,		
				FIRST_DEPT_PATH  as "firstDeptPath" ,		
				SWS_NO  as "swsNo" ,		
				DLR_NO  as "dlrNo" ,		
				DJD_PERSON_NAME  as "djdPersonName" ,		
				JD_PERSON_NAME  as "jdPersonName" ,		
				FML  as "fml" ,		
				FMR_CODE  as "fmrCode" ,		
				DJR_DATE  as "djrDate" ,		
				MONEY_GGYSF  as "moneyGgysf" ,		
				ZZRQ  as "zzrq" ,		
				ZZBZ  as "zzbz" ,		
				ZZTXR  as "zztxr" ,		
				ZZTXRQ  as "zztxrq" ,		
				ZZBJ  as "zzbj" ,
				FINUSE_SSQK as "finuseSsqk" ,
				FINUSE_SSDATE as "finuseSsdate" ,
				IPC as "ipc"
				FROM ${zzzcSchema}.T_KIZL_PATENT_INFO
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="jsbhLike">JSBH like '$jsbhLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bgbhLike">BGBH like '$bgbhLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentNoLike">PATENT_NO like '$patentNoLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="serialNum">SERIAL_NUM =  #serialNum#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jsbh">JSBH =  #jsbh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bgbh">BGBH =  #bgbh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="applyName">APPLY_NAME =  #applyName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE =  #firstDeptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME =  #firstDeptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME =  #gldwName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="label">LABEL =  #label#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="techArea">TECH_AREA =  #techArea#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="usePropose">USE_PROPOSE =  #usePropose#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="knowledgeClass">KNOWLEDGE_CLASS =  #knowledgeClass#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrCode">LXR_CODE =  #lxrCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrName">LXR_NAME =  #lxrName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrPhone">LXR_PHONE =  #lxrPhone#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrEmail">LXR_EMAIL =  #lxrEmail#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrMobile">LXR_MOBILE =  #lxrMobile#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE =  #fromType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromNo">FROM_NO =  #fromNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromName">FROM_NAME =  #fromName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromContent">FROM_CONTENT =  #fromContent#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useMethod">USE_METHOD =  #useMethod#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useDept">USE_DEPT =  #useDept#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useDeptName">USE_DEPT_NAME =  #useDeptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useExpected">USE_EXPECTED =  #useExpected#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useExpectedName">USE_EXPECTED_NAME =  #useExpectedName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useFirstdate">USE_FIRSTDATE =  #useFirstdate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="reasonNouse">REASON_NOUSE =  #reasonNouse#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="contentNouse">CONTENT_NOUSE =  #contentNouse#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgFlag">JPG_FLAG =  #jpgFlag#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgXh">JPG_XH =  #jpgXh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgGw">JPG_GW =  #jpgGw#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgTeam">JPG_TEAM =  #jpgTeam#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isbigxm">ISBIGXM =  #isbigxm#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jfdwCode">JFDW_CODE =  #jfdwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="flowStatus">FLOW_STATUS =  #flowStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentStatus">PATENT_STATUS =  #patentStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djdPerson">DJD_PERSON =  #djdPerson#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djdDate">DJD_DATE =  #djdDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jdPerson">JD_PERSON =  #jdPerson#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jdDate">JD_DATE =  #jdDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="qs">QS =  #qs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="slrq">SLRQ =  #slrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentNo">PATENT_NO =  #patentNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="yxqr">YXQR =  #yxqr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="qlyqsl">QLYQSL =  #qlyqsl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="smsys">SMSYS =  #smsys#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsGuid">SWS_GUID =  #swsGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsdlr">SWSDLR =  #swsdlr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsdlrPhone">SWSDLR_PHONE =  #swsdlrPhone#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsdlrEmail">SWSDLR_EMAIL =  #swsdlrEmail#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyDlf">MONEY_DLF =  #moneyDlf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyQf">MONEY_QF =  #moneyQf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneySqf">MONEY_SQF =  #moneySqf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyGbysf">MONEY_GBYSF =  #moneyGbysf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneySmsfjf">MONEY_SMSFJF =  #moneySmsfjf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyYhs">MONEY_YHS =  #moneyYhs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqtzFwdate">SQTZ_FWDATE =  #sqtzFwdate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyFirst">MONEY_FIRST =  #moneyFirst#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zlh">ZLH =  #zlh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqrq">SQRQ =  #sqrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="flzt">FLZT =  #flzt#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isvalid">ISVALID =  #isvalid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isgdArchive">ISGD_ARCHIVE =  #isgdArchive#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="archiveGuid">ARCHIVE_GUID =  #archiveGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isgdDz">ISGD_DZ =  #isgdDz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra6">EXTRA6 =  #extra6#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptPath">FIRST_DEPT_PATH =  #firstDeptPath#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsNo">SWS_NO =  #swsNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dlrNo">DLR_NO =  #dlrNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djdPersonName">DJD_PERSON_NAME =  #djdPersonName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jdPersonName">JD_PERSON_NAME =  #jdPersonName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fml">FML =  #fml#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fmrCode">FMR_CODE =  #fmrCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djrDate">DJR_DATE =  #djrDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyGgysf">MONEY_GGYSF =  #moneyGgysf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zzrq">ZZRQ =  #zzrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zzbz">ZZBZ =  #zzbz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zztxr">ZZTXR =  #zztxr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zztxrq">ZZTXRQ =  #zztxrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zzbj">ZZBJ =  #zzbj#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="finuseSsqk">FINUSE_SSQK =  #finuseSsqk#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="finuseSsdate">FINUSE_SSDATE =  #finuseSsdate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ipc">IPC =  #ipc#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
	
	<select id="queryPlus"  parameterClass="hashmap" resultClass="tKIZLPatentInfo">
		SELECT
				PATENT_ID  as "patentId" ,		
				APPLY_ID  as "applyId" ,		
				SERIAL_NUM  as "serialNum" ,		
				JSBH  as "jsbh" ,		
				BGBH  as "bgbh" ,		
				APPLY_NAME  as "applyName" ,		
				FIRST_DEPT_CODE  as "firstDeptCode" ,		
				FIRST_DEPT_NAME  as "firstDeptName" ,		
				GLDW_CODE  as "gldwCode" ,		
				GLDW_NAME  as "gldwName" ,		
				LABEL  as "label" ,		
				TECH_AREA  as "techArea" ,		
				USE_PROPOSE  as "usePropose" ,		
				KNOWLEDGE_CLASS  as "knowledgeClass" ,		
				LXR_CODE  as "lxrCode" ,		
				LXR_NAME  as "lxrName" ,		
				LXR_PHONE  as "lxrPhone" ,		
				LXR_EMAIL  as "lxrEmail" ,		
				LXR_MOBILE  as "lxrMobile" ,		
				FROM_TYPE  as "fromType" ,		
				FROM_NO  as "fromNo" ,		
				FROM_NAME  as "fromName" ,		
				FROM_CONTENT  as "fromContent" ,		
				USE_METHOD  as "useMethod" ,		
				USE_DEPT  as "useDept" ,		
				USE_DEPT_NAME  as "useDeptName" ,		
				USE_EXPECTED  as "useExpected" ,		
				USE_EXPECTED_NAME  as "useExpectedName" ,		
				USE_FIRSTDATE  as "useFirstdate" ,		
				REASON_NOUSE  as "reasonNouse" ,		
				CONTENT_NOUSE  as "contentNouse" ,		
				JPG_FLAG  as "jpgFlag" ,		
				JPG_XH  as "jpgXh" ,		
				JPG_GW  as "jpgGw" ,		
				JPG_TEAM  as "jpgTeam" ,		
				ISBIGXM  as "isbigxm" ,		
				JFDW_CODE  as "jfdwCode" ,		
				FLOW_STATUS  as "flowStatus" ,		
				PATENT_STATUS  as "patentStatus" ,		
				DJD_PERSON  as "djdPerson" ,		
				DJD_DATE  as "djdDate" ,		
				JD_PERSON  as "jdPerson" ,		
				JD_DATE  as "jdDate" ,		
				QS  as "qs" ,		
				SLRQ  as "slrq" ,		
				PATENT_NO  as "patentNo" ,		
				PATENT_TYPE  as "patentType" ,		
				YXQR  as "yxqr" ,		
				QLYQSL  as "qlyqsl" ,		
				SMSYS  as "smsys" ,		
				SWS_GUID  as "swsGuid" ,		
				SWSDLR  as "swsdlr" ,		
				SWSDLR_PHONE  as "swsdlrPhone" ,		
				SWSDLR_EMAIL  as "swsdlrEmail" ,		
				MONEY_DLF  as "moneyDlf" ,		
				MONEY_QF  as "moneyQf" ,		
				MONEY_SQF  as "moneySqf" ,		
				MONEY_GBYSF  as "moneyGbysf" ,		
				MONEY_SMSFJF  as "moneySmsfjf" ,		
				MONEY_YHS  as "moneyYhs" ,		
				SQTZ_FWDATE  as "sqtzFwdate" ,		
				MONEY_FIRST  as "moneyFirst" ,		
				ZLH  as "zlh" ,		
				SQRQ  as "sqrq" ,		
				FLZT  as "flzt" ,		
				ISVALID  as "isvalid" ,		
				ISGD_ARCHIVE  as "isgdArchive" ,		
				ARCHIVE_GUID  as "archiveGuid" ,		
				ISGD_DZ  as "isgdDz" ,		
				EXTRA1  as "extra1" ,		
				EXTRA2  as "extra2" ,		
				EXTRA3  as "extra3" ,		
				EXTRA4  as "extra4" ,		
				EXTRA5  as "extra5" ,		
				EXTRA6  as "extra6" ,		
				DEL_STATUS  as "delStatus" ,		
				CREATE_USER_LABEL  as "createUserLabel" ,		
				CREATE_DATE  as "createDate" ,		
				UPDATE_USER_LABEL  as "updateUserLabel" ,		
				UPDATE_DATE  as "updateDate" ,		
				DELETE_USER_LABEL  as "deleteUserLabel" ,		
				DELETE_DATE  as "deleteDate" ,		
				RECORD_VERSION  as "recordVersion" ,		
				FIRST_DEPT_PATH  as "firstDeptPath" ,		
				SWS_NO  as "swsNo" ,		
				DLR_NO  as "dlrNo" ,		
				DJD_PERSON_NAME  as "djdPersonName" ,		
				JD_PERSON_NAME  as "jdPersonName" ,		
				FML  as "fml" ,		
				FMR_CODE  as "fmrCode" ,		
				DJR_DATE  as "djrDate" ,		
				MONEY_GGYSF  as "moneyGgysf" ,		
				ZZRQ  as "zzrq" ,		
				ZZBZ  as "zzbz" ,		
				ZZTXR  as "zztxr" ,		
				ZZTXRQ  as "zztxrq" ,		
				ZZBJ  as "zzbj" ,
				FINUSE_SSQK as "finuseSsqk" ,
				FINUSE_SSDATE as "finuseSsdate" ,
				IPC as "ipc"
				FROM ${zzzcSchema}.T_KIZL_PATENT_INFO
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="bgbhLike">BGBH like '$bgbhLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fmrCodeLike">FMR_CODE like '%$fmrCodeLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="serialNum">SERIAL_NUM =  #serialNum#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jsbh">JSBH =  #jsbh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bgbh">BGBH =  #bgbh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="applyName">APPLY_NAME =  #applyName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE =  #firstDeptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME =  #firstDeptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME =  #gldwName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="label">LABEL =  #label#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="techArea">TECH_AREA =  #techArea#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="usePropose">USE_PROPOSE =  #usePropose#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="knowledgeClass">KNOWLEDGE_CLASS =  #knowledgeClass#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrCode">LXR_CODE =  #lxrCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrName">LXR_NAME =  #lxrName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrPhone">LXR_PHONE =  #lxrPhone#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrEmail">LXR_EMAIL =  #lxrEmail#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrMobile">LXR_MOBILE =  #lxrMobile#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE =  #fromType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromNo">FROM_NO =  #fromNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromName">FROM_NAME =  #fromName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromContent">FROM_CONTENT =  #fromContent#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useMethod">USE_METHOD =  #useMethod#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useDept">USE_DEPT =  #useDept#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useDeptName">USE_DEPT_NAME =  #useDeptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useExpected">USE_EXPECTED =  #useExpected#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useExpectedName">USE_EXPECTED_NAME =  #useExpectedName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useFirstdate">USE_FIRSTDATE =  #useFirstdate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="reasonNouse">REASON_NOUSE =  #reasonNouse#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="contentNouse">CONTENT_NOUSE =  #contentNouse#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgFlag">JPG_FLAG =  #jpgFlag#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgXh">JPG_XH =  #jpgXh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgGw">JPG_GW =  #jpgGw#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgTeam">JPG_TEAM =  #jpgTeam#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isbigxm">ISBIGXM =  #isbigxm#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jfdwCode">JFDW_CODE =  #jfdwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="flowStatus">FLOW_STATUS =  #flowStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentStatus">PATENT_STATUS =  #patentStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djdPerson">DJD_PERSON =  #djdPerson#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djdDate">DJD_DATE =  #djdDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jdPerson">JD_PERSON =  #jdPerson#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jdDate">JD_DATE =  #jdDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="qs">QS =  #qs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="slrq">SLRQ =  #slrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isSL">(SLRQ IS NOT NULL OR SLRQ != '')</isNotEmpty>
				<isNotEmpty prepend=" AND " property="stslrq">SLRQ &gt;=  #stslrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zzslrq">SLRQ  &lt;=  #zzslrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentNo">PATENT_NO =  #patentNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="yxqr">YXQR =  #yxqr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="qlyqsl">QLYQSL =  #qlyqsl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="smsys">SMSYS =  #smsys#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsGuid">SWS_GUID =  #swsGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsdlr">SWSDLR =  #swsdlr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsdlrPhone">SWSDLR_PHONE =  #swsdlrPhone#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsdlrEmail">SWSDLR_EMAIL =  #swsdlrEmail#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyDlf">MONEY_DLF =  #moneyDlf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyQf">MONEY_QF =  #moneyQf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneySqf">MONEY_SQF =  #moneySqf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyGbysf">MONEY_GBYSF =  #moneyGbysf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneySmsfjf">MONEY_SMSFJF =  #moneySmsfjf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyYhs">MONEY_YHS =  #moneyYhs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqtzFwdate">SQTZ_FWDATE =  #sqtzFwdate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyFirst">MONEY_FIRST =  #moneyFirst#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zlh">ZLH =  #zlh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqrq">SQRQ =  #sqrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="flzt">FLZT =  #flzt#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isvalid">ISVALID =  #isvalid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isyx">(ISVALID IS NULL OR ISVALID IN ('1',''))</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isgdArchive">ISGD_ARCHIVE =  #isgdArchive#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="archiveGuid">ARCHIVE_GUID =  #archiveGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isgdDz">ISGD_DZ =  #isgdDz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra6">EXTRA6 =  #extra6#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptPath">FIRST_DEPT_PATH =  #firstDeptPath#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsNo">SWS_NO =  #swsNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dlrNo">DLR_NO =  #dlrNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djdPersonName">DJD_PERSON_NAME =  #djdPersonName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jdPersonName">JD_PERSON_NAME =  #jdPersonName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fml">FML =  #fml#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fmrCode">FMR_CODE =  #fmrCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djrDate">DJR_DATE =  #djrDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyGgysf">MONEY_GGYSF =  #moneyGgysf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zzrq">ZZRQ =  #zzrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zzbz">ZZBZ =  #zzbz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zztxr">ZZTXR =  #zztxr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zztxrq">ZZTXRQ =  #zztxrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zzbj">ZZBJ =  #zzbj#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="finuseSsqk">FINUSE_SSQK =  #finuseSsqk#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="finuseSsdate">FINUSE_SSDATE =  #finuseSsdate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ipc">IPC =  #ipc#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="countPlus"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KIZL_PATENT_INFO 
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="bgbhLike">BGBH like '$bgbhLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fmrCodeLike">FMR_CODE like '%$fmrCodeLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="serialNum">SERIAL_NUM =  #serialNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jsbh">JSBH =  #jsbh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="bgbh">BGBH =  #bgbh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyName">APPLY_NAME =  #applyName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE =  #firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME =  #firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME =  #gldwName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="label">LABEL =  #label#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techArea">TECH_AREA =  #techArea#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="usePropose">USE_PROPOSE =  #usePropose#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="knowledgeClass">KNOWLEDGE_CLASS =  #knowledgeClass#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrCode">LXR_CODE =  #lxrCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrName">LXR_NAME =  #lxrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrPhone">LXR_PHONE =  #lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrEmail">LXR_EMAIL =  #lxrEmail#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrMobile">LXR_MOBILE =  #lxrMobile#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE =  #fromType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromNo">FROM_NO =  #fromNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromName">FROM_NAME =  #fromName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromContent">FROM_CONTENT =  #fromContent#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useMethod">USE_METHOD =  #useMethod#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDept">USE_DEPT =  #useDept#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDeptName">USE_DEPT_NAME =  #useDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useExpected">USE_EXPECTED =  #useExpected#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useExpectedName">USE_EXPECTED_NAME =  #useExpectedName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useFirstdate">USE_FIRSTDATE =  #useFirstdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reasonNouse">REASON_NOUSE =  #reasonNouse#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contentNouse">CONTENT_NOUSE =  #contentNouse#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jpgFlag">JPG_FLAG =  #jpgFlag#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jpgXh">JPG_XH =  #jpgXh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jpgGw">JPG_GW =  #jpgGw#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jpgTeam">JPG_TEAM =  #jpgTeam#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isbigxm">ISBIGXM =  #isbigxm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jfdwCode">JFDW_CODE =  #jfdwCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flowStatus">FLOW_STATUS =  #flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentStatus">PATENT_STATUS =  #patentStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="djdPerson">DJD_PERSON =  #djdPerson#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="djdDate">DJD_DATE =  #djdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jdPerson">JD_PERSON =  #jdPerson#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jdDate">JD_DATE =  #jdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="qs">QS =  #qs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="slrq">SLRQ =  #slrq#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentNo">PATENT_NO =  #patentNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="yxqr">YXQR =  #yxqr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="qlyqsl">QLYQSL =  #qlyqsl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="smsys">SMSYS =  #smsys#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsGuid">SWS_GUID =  #swsGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsdlr">SWSDLR =  #swsdlr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsdlrPhone">SWSDLR_PHONE =  #swsdlrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsdlrEmail">SWSDLR_EMAIL =  #swsdlrEmail#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyDlf">MONEY_DLF =  #moneyDlf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyQf">MONEY_QF =  #moneyQf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneySqf">MONEY_SQF =  #moneySqf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyGbysf">MONEY_GBYSF =  #moneyGbysf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneySmsfjf">MONEY_SMSFJF =  #moneySmsfjf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyYhs">MONEY_YHS =  #moneyYhs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqtzFwdate">SQTZ_FWDATE =  #sqtzFwdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyFirst">MONEY_FIRST =  #moneyFirst#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zlh">ZLH =  #zlh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqrq">SQRQ =  #sqrq#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flzt">FLZT =  #flzt#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isvalid">ISVALID =  #isvalid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isgdArchive">ISGD_ARCHIVE =  #isgdArchive#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="archiveGuid">ARCHIVE_GUID =  #archiveGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isgdDz">ISGD_DZ =  #isgdDz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra6">EXTRA6 =  #extra6#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptPath">FIRST_DEPT_PATH =  #firstDeptPath#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsNo">SWS_NO =  #swsNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dlrNo">DLR_NO =  #dlrNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="djdPersonName">DJD_PERSON_NAME =  #djdPersonName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jdPersonName">JD_PERSON_NAME =  #jdPersonName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fml">FML =  #fml#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fmrCode">FMR_CODE =  #fmrCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="djrDate">DJR_DATE =  #djrDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyGgysf">MONEY_GGYSF =  #moneyGgysf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zzrq">ZZRQ =  #zzrq#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zzbz">ZZBZ =  #zzbz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zztxr">ZZTXR =  #zztxr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zztxrq">ZZTXRQ =  #zztxrq#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zzbj">ZZBJ =  #zzbj#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="finuseSsqk">FINUSE_SSQK =  #finuseSsqk#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="finuseSsdate">FINUSE_SSDATE =  #finuseSsdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ipc">IPC =  #ipc#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
	
	
	<select id="queryYsqzl"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
				SUM(CASE WHEN GLDW_CODE ='BGTA00' THEN 1 ELSE 0 END ) AS "bs",
				SUM(CASE WHEN GLDW_CODE LIKE 'BGBW%' THEN 1 ELSE 0 END ) AS "qs",
				SUM(CASE WHEN GLDW_CODE LIKE 'BSZG%' THEN 1 ELSE 0 END ) AS "ds",
				SUM(CASE WHEN GLDW_CODE LIKE 'BGTM%' THEN 1 ELSE 0 END ) AS "ms",
				SUM(CASE WHEN GLDW_CODE ='BGTA00' THEN 1 
				WHEN GLDW_CODE LIKE 'BGBW%' THEN 1 
				WHEN GLDW_CODE LIKE 'BSZG%' THEN 1 
				WHEN GLDW_CODE LIKE 'BGTM%' THEN 1 ELSE 0 END ) AS "total" 
				FROM ${zzzcSchema}.T_KIZL_PATENT_INFO 
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="bgbhLike">BGBH like '$bgbhLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fmrCodeLike">FMR_CODE like '%$fmrCodeLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="serialNum">SERIAL_NUM =  #serialNum#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jsbh">JSBH =  #jsbh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bgbh">BGBH =  #bgbh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="applyName">APPLY_NAME =  #applyName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE =  #firstDeptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME =  #firstDeptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME =  #gldwName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="label">LABEL =  #label#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="techArea">TECH_AREA =  #techArea#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="usePropose">USE_PROPOSE =  #usePropose#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="knowledgeClass">KNOWLEDGE_CLASS =  #knowledgeClass#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE =  #fromType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromNo">FROM_NO =  #fromNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromName">FROM_NAME =  #fromName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromContent">FROM_CONTENT =  #fromContent#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="flowStatus">FLOW_STATUS =  #flowStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentStatus">PATENT_STATUS =  #patentStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="slrq">SLRQ =  #slrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="stslrq">SQRQ &gt;=  #stslrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zzslrq">SQRQ  &lt;=  #zzslrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentNo">PATENT_NO =  #patentNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="yxqr">YXQR =  #yxqr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="qlyqsl">QLYQSL =  #qlyqsl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqrq">SQRQ =  #sqrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="flzt">FLZT =  #flzt#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isvalid">ISVALID =  #isvalid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isyx">(ISVALID IS NULL OR ISVALID IN ('1',''))</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptPath">FIRST_DEPT_PATH =  #firstDeptPath#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
	
	<select id="queryListZLMM"  parameterClass="hashmap" resultClass="tKIZLPatentInfo">
		SELECT
				t.APPLY_ID  as "applyId" ,
				t.JSBH  as "jsbh" ,
				t.FLZT  as "flzt" ,
				t.APPLY_NAME  as "applyName" ,
				t.FIRST_DEPT_NAME  as "firstDeptName" ,
				a.EXTRA10 as "extra5",
				'专利'  as "label" 
				FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO a
				LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO t ON a.APPLY_ID = t.APPLY_ID 
			WHERE a.DEL_STATUS = '0' AND t.DEL_STATUS = '0'
				<isNotEmpty prepend=" AND " property="fromNo">t.FROM_NO =  #fromNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentStatus">t.PATENT_STATUS =  #patentStatus#</isNotEmpty>
				<!-- <isNotEmpty prepend=" AND " property="isvalid">t.ISVALID =  #isvalid#</isNotEmpty> -->
			UNION ALL
		SELECT
				technology_id as "applyId" ,
				confirm_num as "jsbh" ,
				status as "flzt" ,
				technology_name as "applyName" ,
				firstdept_name as "firstDeptName" ,
				extra6 as "extra5",
				'技术秘密'  as "label" 
				FROM ${zzzcSchema}.T_KYMM_TECHNOLOGY
			WHERE DEL_STATUS = '0'
				<isNotEmpty prepend=" AND " property="fromNo">SOURCE_NUM =  #fromNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="status">STATUS =  #status#</isNotEmpty>
	</select>
	
	<!-- 实施奖 -->
	<select id="querySsj"  parameterClass="hashmap" resultClass="tKIZLPatentInfo">
		SELECT distinct
				A.PATENT_ID  as "patentId" ,		
				A.APPLY_ID  as "applyId" ,		
				A.SERIAL_NUM  as "serialNum" ,		
				A.JSBH  as "jsbh" ,		
				A.BGBH  as "bgbh" ,		
				A.APPLY_NAME  as "applyName" ,		
				A.FIRST_DEPT_CODE  as "firstDeptCode" ,		
				A.FIRST_DEPT_NAME  as "firstDeptName" ,		
				A.GLDW_CODE  as "gldwCode" ,		
				A.GLDW_NAME  as "gldwName" ,		
				A.LABEL  as "label" ,		
				A.TECH_AREA  as "techArea" ,		
				A.USE_PROPOSE  as "usePropose" ,		
				A.KNOWLEDGE_CLASS  as "knowledgeClass" ,		
				A.LXR_CODE  as "lxrCode" ,		
				A.LXR_NAME  as "lxrName" ,		
				A.LXR_PHONE  as "lxrPhone" ,		
				A.LXR_EMAIL  as "lxrEmail" ,		
				A.LXR_MOBILE  as "lxrMobile" ,		
				A.FROM_TYPE  as "fromType" ,		
				A.FROM_NO  as "fromNo" ,		
				A.FROM_NAME  as "fromName" ,		
				A.FROM_CONTENT  as "fromContent" ,		
				A.USE_METHOD  as "useMethod" ,		
				A.USE_DEPT  as "useDept" ,		
				A.USE_DEPT_NAME  as "useDeptName" ,		
				A.USE_EXPECTED  as "useExpected" ,		
				A.USE_EXPECTED_NAME  as "useExpectedName" ,		
				A.USE_FIRSTDATE  as "useFirstdate" ,		
				A.REASON_NOUSE  as "reasonNouse" ,		
				A.CONTENT_NOUSE  as "contentNouse" ,		
				A.JPG_FLAG  as "jpgFlag" ,		
				A.JPG_XH  as "jpgXh" ,		
				A.JPG_GW  as "jpgGw" ,		
				A.JPG_TEAM  as "jpgTeam" ,		
				A.ISBIGXM  as "isbigxm" ,		
				A.JFDW_CODE  as "jfdwCode" ,		
				A.FLOW_STATUS  as "flowStatus" ,		
				A.PATENT_STATUS  as "patentStatus" ,		
				A.DJD_PERSON  as "djdPerson" ,		
				A.DJD_DATE  as "djdDate" ,		
				A.JD_PERSON  as "jdPerson" ,		
				A.JD_DATE  as "jdDate" ,		
				A.QS  as "qs" ,		
				A.SLRQ  as "slrq" ,		
				A.PATENT_NO  as "patentNo" ,		
				A.PATENT_TYPE  as "patentType" ,		
				A.YXQR  as "yxqr" ,		
				A.QLYQSL  as "qlyqsl" ,		
				A.SMSYS  as "smsys" ,		
				A.SWS_GUID  as "swsGuid" ,		
				A.SWSDLR  as "swsdlr" ,		
				A.SWSDLR_PHONE  as "swsdlrPhone" ,		
				A.SWSDLR_EMAIL  as "swsdlrEmail" ,		
				A.MONEY_DLF  as "moneyDlf" ,		
				A.MONEY_QF  as "moneyQf" ,		
				A.MONEY_SQF  as "moneySqf" ,		
				A.MONEY_GBYSF  as "moneyGbysf" ,		
				A.MONEY_SMSFJF  as "moneySmsfjf" ,		
				A.MONEY_YHS  as "moneyYhs" ,		
				A.SQTZ_FWDATE  as "sqtzFwdate" ,		
				A.MONEY_FIRST  as "moneyFirst" ,		
				A.ZLH  as "zlh" ,		
				A.SQRQ  as "sqrq" ,		
				A.FLZT  as "flzt" ,		
				A.ISVALID  as "isvalid" ,		
				A.ISGD_ARCHIVE  as "isgdArchive" ,		
				A.ARCHIVE_GUID  as "archiveGuid" ,		
				A.ISGD_DZ  as "isgdDz" ,		
				A.DEL_STATUS  as "delStatus" ,		
				A.CREATE_USER_LABEL  as "createUserLabel" ,		
				A.CREATE_DATE  as "createDate" ,		
				A.UPDATE_USER_LABEL  as "updateUserLabel" ,		
				A.UPDATE_DATE  as "updateDate" ,		
				A.DELETE_USER_LABEL  as "deleteUserLabel" ,		
				A.DELETE_DATE  as "deleteDate" ,		
				A.RECORD_VERSION  as "recordVersion" ,		
				A.FIRST_DEPT_PATH  as "firstDeptPath" ,		
				A.SWS_NO  as "swsNo" ,		
				A.DLR_NO  as "dlrNo" ,		
				A.DJD_PERSON_NAME  as "djdPersonName" ,		
				A.JD_PERSON_NAME  as "jdPersonName" ,		
				A.FML  as "fml" ,  
				A.FMR_CODE  as "fmrCode" ,  
				CASE WHEN B.FLOW_STATUS = 'active' THEN '实施奖流程正在流转中' ELSE '' END  as  "extra1"
				FROM ${zzzcSchema}.T_KIZL_PATENT_INFO A  
				LEFT JOIN ${zzzcSchema}.T_KIZL_SSJ_BASEINFO B ON A.PATENT_ID = B.PATENT_ID AND B.FLOW_STATUS = 'active'
				LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX ryxx ON ryxx.APPLY_ID = A.APPLY_ID
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="empId">ryxx.EMP_ID = #empId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bgbhLike">A.BGBH like '$bgbhLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jsbhLike">A.JSBH like '$jsbhLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentNoLike">A.PATENT_NO like '$patentNoLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fmrCodeLike">A.FMR_CODE like '%$fmrCodeLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentId">A.PATENT_ID =  #patentId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="applyId">A.APPLY_ID =  #applyId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="serialNum">A.SERIAL_NUM =  #serialNum#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jsbh">A.JSBH =  #jsbh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bgbh">A.BGBH =  #bgbh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="applyName">A.APPLY_NAME =  #applyName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptCode">A.FIRST_DEPT_CODE =  #firstDeptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptName">A.FIRST_DEPT_NAME =  #firstDeptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwCode">A.GLDW_CODE =  #gldwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwName">A.GLDW_NAME =  #gldwName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="label">A.LABEL =  #label#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="techArea">A.TECH_AREA =  #techArea#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="usePropose">A.USE_PROPOSE =  #usePropose#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="knowledgeClass">A.KNOWLEDGE_CLASS =  #knowledgeClass#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrCode">A.LXR_CODE =  #lxrCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrName">A.LXR_NAME =  #lxrName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrPhone">A.LXR_PHONE =  #lxrPhone#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrEmail">A.LXR_EMAIL =  #lxrEmail#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrMobile">A.LXR_MOBILE =  #lxrMobile#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromType">A.FROM_TYPE =  #fromType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromNo">A.FROM_NO =  #fromNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromName">A.FROM_NAME =  #fromName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromContent">A.FROM_CONTENT =  #fromContent#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useMethod">A.USE_METHOD =  #useMethod#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useDept">A.USE_DEPT =  #useDept#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useDeptName">A.USE_DEPT_NAME =  #useDeptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useExpected">A.USE_EXPECTED =  #useExpected#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useExpectedName">A.USE_EXPECTED_NAME =  #useExpectedName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useFirstdate">A.USE_FIRSTDATE =  #useFirstdate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="reasonNouse">A.REASON_NOUSE =  #reasonNouse#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="contentNouse">A.CONTENT_NOUSE =  #contentNouse#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgFlag">A.JPG_FLAG =  #jpgFlag#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgXh">A.JPG_XH =  #jpgXh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgGw">A.JPG_GW =  #jpgGw#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgTeam">A.JPG_TEAM =  #jpgTeam#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isbigxm">A.ISBIGXM =  #isbigxm#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jfdwCode">A.JFDW_CODE =  #jfdwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="flowStatus">A.FLOW_STATUS =  #flowStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentStatus">A.PATENT_STATUS =  #patentStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djdPerson">A.DJD_PERSON =  #djdPerson#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djdDate">A.DJD_DATE =  #djdDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jdPerson">A.JD_PERSON =  #jdPerson#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jdDate">A.JD_DATE =  #jdDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="qs">A.QS =  #qs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="slrq">A.SLRQ =  #slrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentNo">A.PATENT_NO =  #patentNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentType">A.PATENT_TYPE =  #patentType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="yxqr">A.YXQR =  #yxqr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="qlyqsl">A.QLYQSL =  #qlyqsl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="smsys">A.SMSYS =  #smsys#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsGuid">A.SWS_GUID =  #swsGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsdlr">A.SWSDLR =  #swsdlr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsdlrPhone">A.SWSDLR_PHONE =  #swsdlrPhone#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsdlrEmail">A.SWSDLR_EMAIL =  #swsdlrEmail#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyDlf">A.MONEY_DLF =  #moneyDlf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyQf">A.MONEY_QF =  #moneyQf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneySqf">A.MONEY_SQF =  #moneySqf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyGbysf">A.MONEY_GBYSF =  #moneyGbysf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneySmsfjf">A.MONEY_SMSFJF =  #moneySmsfjf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyYhs">A.MONEY_YHS =  #moneyYhs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqtzFwdate">A.SQTZ_FWDATE =  #sqtzFwdate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyFirst">A.MONEY_FIRST =  #moneyFirst#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zlh">A.ZLH =  #zlh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqrq">A.SQRQ =  #sqrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="flzt">A.FLZT =  #flzt#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isvalid">A.ISVALID =  #isvalid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isgdArchive">A.ISGD_ARCHIVE =  #isgdArchive#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="archiveGuid">A.ARCHIVE_GUID =  #archiveGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isgdDz">A.ISGD_DZ =  #isgdDz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">A.EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">A.EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">A.EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">A.EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">A.EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra6">A.EXTRA6 =  #extra6#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">A.DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">A.CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">A.CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">A.UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">A.UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">A.DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">A.DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">A.RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptPath">A.FIRST_DEPT_PATH =  #firstDeptPath#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsNo">A.SWS_NO =  #swsNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dlrNo">A.DLR_NO =  #dlrNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djdPersonName">A.DJD_PERSON_NAME =  #djdPersonName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jdPersonName">A.JD_PERSON_NAME =  #jdPersonName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fml">A.FML =  #fml#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fmrCode">A.FMR_CODE =  #fmrCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
	
	<!-- 后评估启动 -->
	<select id="queryHpgStart"  parameterClass="hashmap" resultClass="tKIZLPatentInfo">
		SELECT
				BGBH  as "bgbh" , 
				JSBH  as "jsbh" , 
				PATENT_ID  as "patentId" , 
				PATENT_NO  as "patentNo" , 
				APPLY_ID  as "applyId" , 
				APPLY_NAME  as "applyName", 
				SERIAL_NUM  as "serialNum" , 
				FIRST_DEPT_CODE  as "firstDeptCode" , 
				FIRST_DEPT_NAME  as "firstDeptName" , 
				FLOW_STATUS  as "flowStatus" , 
				PATENT_STATUS  as "patentStatus" ,
				FROM_NO AS "fromNo",
				FROM_TYPE AS "fromType",
				LXR_NAME AS "lxrName",
				JPG_FLAG AS "jpgFlag",
				ISBIGXM AS "isbigxm"
				FROM ${zzzcSchema}.T_KIZL_PATENT_INFO p
				WHERE ISVALID = '1'
				<isNotEmpty prepend=" AND " property="flzt">FLZT =  #flzt#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="slrqStart">SLRQ &gt;=  #slrqStart#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="slrqEnd">SLRQ &lt;=  #slrqEnd#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqrqStart">SQRQ &gt;=  #sqrqStart#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqrqEnd">SQRQ &lt;=  #sqrqEnd#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="year">SUBSTRING(T.SQRQ,0,5) =  #year#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME =  #gldwName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sszt1">USE_METHOD =  '04'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sszt2">USE_METHOD !=  '04'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE =  #fromType#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
	
	<!-- 根据专利号获取专利接口 -->
	<select id="query"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
				p.PATENT_ID as "patentId",
				p.APPLY_ID  as "applyId" ,
				p.APPLY_NAME  as "applyName" ,
				p.PATENT_NO  as "patentNo" ,
				p.PATENT_TYPE as "patentType"	,
				p.FROM_NO as "fromNo",
				p.EXTRA1  as "extra1" ,
				p.EXTRA2  as "extra2" ,
				p.EXTRA3  as "extra3" ,
				p.EXTRA4  as "extra4" ,
				p.ZLH as "zlh",
				p.SLRQ as "slrq",
				p.SQRQ as "sqrq",
				p.FLZT as "flzt"
				FROM ${zzzcSchema}.T_KIZL_PATENT_INFO p
				inner join ${zzzcSchema}.T_KIZL_APPLY_BASEINFO ab on p.apply_id = ab.apply_id
				WHERE p.ISVALID = '1'
				<isNotEmpty prepend=" AND " property="serialNum">p.SERIAL_NUM =  #serialNum#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jsbh">p.JSBH =  #jsbh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bgbh">p.BGBH =  #bgbh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="applyName">p.APPLY_NAME =  #applyName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptCode">p.FIRST_DEPT_CODE =  #firstDeptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptName">p.FIRST_DEPT_NAME =  #firstDeptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwCode">p.GLDW_CODE =  #gldwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwName">p.GLDW_NAME =  #gldwName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromType">p.FROM_TYPE =  #fromType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromNo">p.FROM_NO =  #fromNo#</isNotEmpty>
				<isNotNull prepend="AND" property="fromList">
					p.FROM_NO in
				 	<iterate conjunction="," open="(" close=")" property="fromList">#fromList[]#</iterate>
				</isNotNull>
				<isNotEmpty prepend=" AND " property="fromName">p.FROM_NAME =  #fromName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="flowStatus">p.FLOW_STATUS =  #flowStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentStatus">p.PATENT_STATUS =  #patentStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentNo">p.PATENT_NO =  #patentNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  #patentType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zlh">p.ZLH =  #zlh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isvalid">p.ISVALID =  #isvalid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">p.DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">p.DEL_STATUS = '0'</isEmpty>

				<isNotEmpty prepend=" and " property="startDate">p.slrq &gt;= #startDate#</isNotEmpty>
				<isNotEmpty prepend=" and " property="endDate">p.slrq &lt;= #startDate#</isNotEmpty>
				<isNotEmpty prepend=" and " property="year">substrb(ab.APPLY_DATE, 0, 4) = #year#</isNotEmpty>

				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="queryValidAndKwzl" parameterClass="hashmap" resultClass="tKIZLPatentInfo">
		SELECT
		PATENT_ID  as "patentId" ,
		APPLY_ID  as "applyId" ,
		SERIAL_NUM  as "serialNum" ,
		JSBH  as "jsbh" ,
		BGBH  as "bgbh" ,
		APPLY_NAME  as "applyName" ,
		FIRST_DEPT_CODE  as "firstDeptCode" ,
		FIRST_DEPT_NAME  as "firstDeptName" ,
		GLDW_CODE  as "gldwCode" ,
		GLDW_NAME  as "gldwName" ,
		LABEL  as "label" ,
		TECH_AREA  as "techArea" ,
		USE_PROPOSE  as "usePropose" ,
		KNOWLEDGE_CLASS  as "knowledgeClass" ,
		LXR_CODE  as "lxrCode" ,
		LXR_NAME  as "lxrName" ,
		LXR_PHONE  as "lxrPhone" ,
		LXR_EMAIL  as "lxrEmail" ,
		LXR_MOBILE  as "lxrMobile" ,
		FROM_TYPE  as "fromType" ,
		FROM_NO  as "fromNo" ,
		FROM_NAME  as "fromName" ,
		FROM_CONTENT  as "fromContent" ,
		USE_METHOD  as "useMethod" ,
		USE_DEPT  as "useDept" ,
		USE_DEPT_NAME  as "useDeptName" ,
		USE_EXPECTED  as "useExpected" ,
		USE_EXPECTED_NAME  as "useExpectedName" ,
		USE_FIRSTDATE  as "useFirstdate" ,
		REASON_NOUSE  as "reasonNouse" ,
		CONTENT_NOUSE  as "contentNouse" ,
		JPG_FLAG  as "jpgFlag" ,
		JPG_XH  as "jpgXh" ,
		JPG_GW  as "jpgGw" ,
		JPG_TEAM  as "jpgTeam" ,
		ISBIGXM  as "isbigxm" ,
		JFDW_CODE  as "jfdwCode" ,
		FLOW_STATUS  as "flowStatus" ,
		PATENT_STATUS  as "patentStatus" ,
		DJD_PERSON  as "djdPerson" ,
		DJD_DATE  as "djdDate" ,
		JD_PERSON  as "jdPerson" ,
		JD_DATE  as "jdDate" ,
		QS  as "qs" ,
		SLRQ  as "slrq" ,
		PATENT_NO  as "patentNo" ,
		PATENT_TYPE  as "patentType" ,
		YXQR  as "yxqr" ,
		QLYQSL  as "qlyqsl" ,
		SMSYS  as "smsys" ,
		SWS_GUID  as "swsGuid" ,
		SWSDLR  as "swsdlr" ,
		SWSDLR_PHONE  as "swsdlrPhone" ,
		SWSDLR_EMAIL  as "swsdlrEmail" ,
		MONEY_DLF  as "moneyDlf" ,
		MONEY_QF  as "moneyQf" ,
		MONEY_SQF  as "moneySqf" ,
		MONEY_GBYSF  as "moneyGbysf" ,
		MONEY_SMSFJF  as "moneySmsfjf" ,
		MONEY_YHS  as "moneyYhs" ,
		SQTZ_FWDATE  as "sqtzFwdate" ,
		MONEY_FIRST  as "moneyFirst" ,
		ZLH  as "zlh" ,
		SQRQ  as "sqrq" ,
		FLZT  as "flzt" ,
		ISVALID  as "isvalid" ,
		ISGD_ARCHIVE  as "isgdArchive" ,
		ARCHIVE_GUID  as "archiveGuid" ,
		ISGD_DZ  as "isgdDz" ,
		EXTRA1  as "extra1" ,
		EXTRA2  as "extra2" ,
		EXTRA3  as "extra3" ,
		EXTRA4  as "extra4" ,
		EXTRA5  as "extra5" ,
		EXTRA6  as "extra6" ,
		DEL_STATUS  as "delStatus" ,
		CREATE_USER_LABEL  as "createUserLabel" ,
		CREATE_DATE  as "createDate" ,
		UPDATE_USER_LABEL  as "updateUserLabel" ,
		UPDATE_DATE  as "updateDate" ,
		DELETE_USER_LABEL  as "deleteUserLabel" ,
		DELETE_DATE  as "deleteDate" ,
		RECORD_VERSION  as "recordVersion" ,
		FIRST_DEPT_PATH  as "firstDeptPath" ,
		SWS_NO  as "swsNo" ,
		DLR_NO  as "dlrNo" ,
		DJD_PERSON_NAME  as "djdPersonName" ,
		JD_PERSON_NAME  as "jdPersonName" ,
		FML  as "fml" ,
		FMR_CODE  as "fmrCode" ,
		IPC as "ipc"
		FROM ${zzzcSchema}.T_KIZL_PATENT_INFO
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="firstDeptPath">FIRST_DEPT_PATH LIKE '%$firstDeptPath$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jpgGw">JPG_GW =  #jpgGw#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isvalid">ISVALID =  #isvalid# AND PATENT_NO IS NOT NULL AND PATENT_NO != ''</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jsbh">JSBH =  #jsbh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="bgbh">BGBH =  #bgbh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyName">APPLY_NAME LIKE  '%$applyName$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentNo">PATENT_NO =  #patentNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrName">LXR_NAME LIKE  '%$lxrName$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrPhone">LXR_PHONE =  #lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql2"> $dynSql2$ </isNotEmpty>
		</dynamic>
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
	
	<select id="countYearplan"  parameterClass="hashmap" resultClass="integer">
		SELECT PATENT_ID
		FROM ${zzzcSchema}.T_KIZL_PATENT_INFO 
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="nd">substr(DJR_DATE,1,4) =  #nd#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentStatusIn">PATENT_STATUS in ($patentStatusIn$)</isNotEmpty>
			<isNotEmpty prepend=" AND " property="slrq">#slrq#-DATE_FORMAT(substr(SLRQ,1,10),'YYYYMMdd') &gt; 1095</isNotEmpty>
		</dynamic>
	</select>
	
	<!-- S_KI_ZL_009根据年度，发明人工号获取（已受理，已授权）专利 -->
	<select id="querySlsq"  parameterClass="hashmap" resultClass="hashmap">
		SELECT distinct
				t.APPLY_ID as "applyId" ,
				t.PATENT_NO  as "projectCode" , 
				t.APPLY_NAME  as "projectName" , 
				ryxx.EMP_ID as "memberId" ,
				ryxx.DEPT_CODE as "deptCode" ,
				ryxx.GXXS as "ratio" ,
				t.PATENT_TYPE  as "patentType" , 
				'KL' as "source" ,
				t.SLRQ  as "applyDate" , 
				t.SQRQ  as "authDate" , 
				'受理' as "status" 
				FROM ${zzzcSchema}.T_KIZL_PATENT_INFO t LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX ryxx
				ON ryxx.APPLY_ID = t.APPLY_ID
				WHERE t.FLZT IN ('02','20')
				<isNotEmpty prepend=" AND " property="year">YEAR(t.SLRQ) =  #year#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="empIdList">ryxx.EMP_ID IN
					<iterate  property="empIdList" conjunction="," open="(" close=")">
						#empIdList[]#
					</iterate>
				</isNotEmpty>
				UNION
		SELECT distinct
				t.APPLY_ID as "applyId" ,
				t.PATENT_NO  as "projectCode" , 
				t.APPLY_NAME  as "projectName" , 
				ryxx.EMP_ID as "memberId" ,
				ryxx.DEPT_CODE as "deptCode" ,
				ryxx.GXXS as "ratio" ,
				t.PATENT_TYPE  as "patentType" , 
				'KL' as "source" ,
				t.SLRQ  as "applyDate" , 
				t.SQRQ  as "authDate" , 
				'授权' as "status" 
				FROM ${zzzcSchema}.T_KIZL_PATENT_INFO t LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX ryxx
				ON ryxx.APPLY_ID = t.APPLY_ID
				WHERE t.FLZT IN ('10')
				<isNotEmpty prepend=" AND " property="year">YEAR(t.SQRQ) =  #year#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="empIdList">ryxx.EMP_ID IN
					<iterate  property="empIdList" conjunction="," open="(" close=")">
						#empIdList[]#
					</iterate>
				</isNotEmpty>
	</select>
	
		<select id="queryIntegrated"  parameterClass="hashmap" resultClass="com.baosight.bscdkj.common.ki.domain.TkizlPatentInfo">
		SELECT
		PATENT_ID as "patentId",
		APPLY_ID as "applyId",
		SERIAL_NUM as "serialNum",
		JSBH as "jsbh",
		BGBH as "bgbh",
		APPLY_NAME as "applyName",
		FIRST_DEPT_CODE as "firstDeptCode",
		FIRST_DEPT_NAME as "firstDeptName",
		GLDW_CODE as "gldwCode",
		GLDW_NAME as "gldwName",
		SLRQ as "slrq"
		FROM
		ZZZC.T_KIZL_PATENT_INFO
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="jsbhLike"> JSBH like '$jsbhLike$%'  </isNotEmpty>
			<isNotEmpty prepend=" AND " property="nf">SUBSTR(SLRQ,1,4) =  #nf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
		ORDER BY SLRQ DESC
	</select>
	
	<select id="queryAllPatent"  parameterClass="hashmap" resultClass="tKIZLPatentInfoZh">
			SELECT
					A.apply_id as "applyId",
					min(A.apply_date) as "applyDate",
					min(A.serial_num) as "serialNum",
					case when min(B.apply_name) is not null then min(B.apply_name) else min(A.apply_name) end as "applyName",
					min(B.patent_type) as "patentType",
					min(A.first_dept_code) as "firstDeptCode",
					min(A.first_dept_name) as "firstDeptName",
					min(A.jsbh) as "jsbh",
					min(B.bgbh) as "bgbh",
					min(W.last_time) as "lastTime",
					min(W.current_activity_name) as "actName",
					min(A.gldw_code) as "gldwCode",
					min(A.gldw_name) as "gldwName",
					min(B.patent_status) as "patentStatus",
					min(B.flow_status) as "flowStatus",
					min(B.flzt) as "flzt",
					min(B.sqrq) as "sqrq",
					min(B.slrq) as "slrq",
					min(B.patent_no) as "patentNo",
					min(B.jpg_flag) as "jpgFlag",
					min(B.jpg_xh) as "jpgXh",
					min(B.jpg_gw) as "jpgGw",
					min(B.jpg_team) as "jpgTeam",
					min(B.isbigxm) as "isbigxm",
					min(B.fml) as "fml",
					min(B.fmr_code) as "fmrCode",
					min(S.sws_name) as "swsName",
					min(B.swsdlr) as "swsdlr",
					min(P.project_code) as "projectCode",
					min(P.project_type) as "projectType",
					min(P.project_name) as "projectName",
					min(A.use_firstdate) as "ssrq",
					min(A.use_dept_name) as "ssqy",
					min(A.extra2) as "ph",
					min(HPG.update_date) as "zjdcsj",
					case when min(SSJ.SSJLX) = 'XY' then '已申报效益奖' when min(SSJ.SSJLX) = 'SP' then '已申报' else '未申报' end as "jcqk"
				FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A 
			LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO W ON A.APPLY_ID = W.BUSINESS_ID AND W.FLOW_CODE ='KIZL_ApplyBaseinfo'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO B ON B.APPLY_ID = A.APPLY_ID 
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_SQR SQR ON SQR.APPLY_ID = A.APPLY_ID 
			LEFT JOIN ${platSchema}.TEWPT00 T ON W.FLOW_ID = T.PROCESS_INSTANCE_ID 
			LEFT JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO S ON S.SWS_ID = B.SWS_GUID 
			LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT P ON A.FROM_NO = P.PROJECT_CODE 
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX FML ON FML.APPLY_ID = A.APPLY_ID 
			LEFT JOIN ${zzzcSchema}.T_KIZL_HPG_BASEINFO HPG ON HPG.PATENT_ID = B.PATENT_ID
			LEFT JOIN ${zzzcSchema}.T_KIZL_SSJ_BASEINFO SSJ ON SSJ.BGBH = B.BGBH
			LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_INFO REV ON A.APPLY_ID = REV.BIZ_ID
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_TERMINATION TER ON TER.APPLY_ID = A.APPLY_ID
				WHERE A.DEL_STATUS='0'
				<dynamic prepend=" AND ">
					<isNotEmpty prepend=" AND " property="completerId">T.COMPLETER_ID =  #completerId#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="assigneeId">T.ASSIGNEE_ID =  #assigneeId#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="flowState">W.FLOW_STATE =  #flowState#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="isvalid">B.ISVALID =  #isvalid#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="flztIn">B.FLZT IN ($flztIn$)</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="lxrNameLike">A.LXR_NAME LIKE  '$lxrNameLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="frLike">SQR.LEGAL_NAME LIKE  '%$frLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="serialNumLike">A.SERIAL_NUM LIKE  '$serialNumLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="patentType">B.PATENT_TYPE =  #patentType#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="bgbhLike">B.BGBH LIKE  '$bgbhLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="jsbhLike">A.JSBH LIKE  '$jsbhLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="patentNoLike">B.PATENT_NO LIKE  '$patentNoLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="patentStatus">B.PATENT_STATUS =  #patentStatus#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="qs">B.QS =  #qs#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="extra7Like">A.EXTRA7 LIKE '%$extra7Like$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="firstDeptName">A.FIRST_DEPT_NAME LIKE  '%$firstDeptName$%'</isNotEmpty>
					<isNotEmpty prepend=" AND " property="gldwCode">A.GLDW_CODE =  #gldwCode#</isNotEmpty>
					<isNotEmpty prepend=" AND " property="fromType">A.FROM_TYPE =  #fromType#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="fromNoLike">A.FROM_NO LIKE '$fromNoLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="fromNameLike">A.FROM_NAME LIKE '$fromNameLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="startDate2">A.APPLY_DATE &gt;=  #startDate2#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="endDate2">A.APPLY_DATE &lt;= #endDate2#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="slrqSt">B.SLRQ &gt;=  #slrqSt#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="slrqEd">B.SLRQ &lt;= #slrqEd#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="sqrqSt">B.SQRQ &gt;=  #sqrqSt#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="sqrqEd">B.SQRQ &lt;= #sqrqEd#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="useDeptNameLike">A.USE_DEPT_NAME LIKE  '%$useDeptNameLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="useMethod">A.USE_METHOD in $useMethod$</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="legalNameLike">SQR.LEGAL_NAME LIKE  '%$legalNameLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="techArea">A.TECH_AREA LIKE  '%$techArea$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="usePropose">A.USE_PROPOSE LIKE  '%$usePropose$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="knowledgeClass">A.KNOWLEDGE_CLASS LIKE  '%$knowledgeClass$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="jpgFlag">
		  				( A.JPG_FLAG =  #jpgFlag#
		  				<isNotEmpty prepend=" OR " property="jpgFlag0">
		  					A.JPG_FLAG IS NULL
		  				</isNotEmpty>
		  				)
		  			</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="swsGuid">B.SWS_GUID = #swsGuid#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="dlrNo">B.DLR_NO = #dlrNo#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="empIdLike">FML.EMP_ID LIKE  '%$empIdLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="empNameLike">FML.EMP_NAME LIKE  '%$empNameLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="postName">FML.POST_NAME = #postName#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="postTitle">FML.POST_TITLE = #postTitle#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="dw">FML.CODE_PATH LIKE  '%$dw$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="label">A.LABEL LIKE  '%$label$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="isFq1">HPG.ISFQ = '1'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="isFq0">HPG.ISFQ = '0'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="isDc1">HPG.HPG_ID IS NOT NULL</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="isDc0">HPG.HPG_ID IS NULL</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="ssjqk2">SSJ.SSJLX = 'XY'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="ssjqk1">SSJ.SSJ_ID IS NOT NULL</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="ssjqk0">SSJ.SSJ_ID IS NULL</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="qs1">A.OWNERSHIP = 1</isNotEmpty>
					<isNotEmpty prepend=" AND " property="qs0">A.OWNERSHIP > 1</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="revUserNameLike">REV.REVIEW_USER_NAME LIKE '%$revUserNameLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="dynSql2"> $dynSql2$ </isNotEmpty>
					<isNotEmpty prepend=" AND " property="applyNameLike">A.apply_name LIKE '%$applyNameLike$%'</isNotEmpty>
				</dynamic>
			GROUP BY A.apply_id
		<isNotEmpty property="havingSql">$havingSql$</isNotEmpty>
	</select>
	
	<select id="queryZlxx"  parameterClass="hashmap" resultClass="com.baosight.bscdkj.ki.zl.domain.TkizlPatentInfoResponseEx">
		SELECT
		PATENT_ID  as "patentId" ,
		BGBH  as "bgbh" ,
		APPLY_NAME  as "applyName" ,
		SLRQ  as "slrq" ,
		SQRQ  as "sqrq" ,
		FLZT  as "flzt" ,
		QS  as "qs"
		FROM ${zzzcSchema}.T_KIZL_PATENT_INFO
		WHERE  BGBH >= #bgbhStart# AND BGBH &lt;= #bgbhEnd#
		<isNotNull prepend=" AND " property="gldwCode">$gldwCode$</isNotNull>
		order by bgbh
	</select>

	<select id="queryJpgsjckCount" parameterClass="hashmap" resultClass="hashmap">
		SELECT
		A.PROJECT_CODE AS "projectCode",
		MAX(A.PROJECT_NAME) AS "projectName",
		MAX(A.RECORD_GUID) AS "guid",
		SUM(A.zlzjps) AS "zlzjps",
		SUM(A.djd) AS "djd",
		SUM(A.dlz) AS "dlz",
		SUM(A.qbzl) AS "qbzl",
		SUM(A.fm) AS "fm",
		SUM(A.syxx) AS "syxx",
		SUM(A.gwzl) AS "gwzl",
		SUM(A.sqqbzl) AS "sqqbzl",
		SUM(A.sqfm) AS "sqfm",
		SUM(A.sqsyxx) AS "sqsyxx",
		SUM(A.sqgwzl) AS "sqgwzl",
		SUM(A.sqssxy) AS "sqssxy",
		SUM(A.jsmmzjps) AS "jsmmzjps",
		SUM(A.jsmmrd) AS "jsmmrd",
		SUM(A.jsmmssjxy) AS "jsmmssjxy",
		SUM(A.cgdj) AS "cgdj",
		'0' AS "sbzc"
		FROM
		(
		SELECT
		A.PROJECT_CODE AS PROJECT_CODE,
		MAX(A.PROJECT_NAME) AS PROJECT_NAME,
		MAX(A.RECORD_GUID) AS RECORD_GUID,
		SUM(CASE WHEN E.IS_END = '0'
		<isNotEmpty prepend = " AND " property = "startDate">
			C.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS zlzjps,
		SUM(CASE WHEN B.PATENT_STATUS = '02'
		<isNotEmpty prepend = " AND " property = "startDate">
			C.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS djd,
		SUM(CASE WHEN B.PATENT_STATUS = '04'
		<isNotEmpty prepend = " AND " property = "startDate">
			C.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS dlz,
		SUM(CASE WHEN B.FLOW_STATUS != 'draft'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS qbzl,
		SUM(CASE WHEN B.PATENT_TYPE = 'FM'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS fm,
		SUM(CASE WHEN B.PATENT_TYPE = 'SYXX'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS syxx,
		SUM(CASE WHEN B.ISVALID = '1' AND B.SQRQ IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqqbzl,
		SUM(CASE WHEN B.ISVALID = '1' AND B.SQRQ IS NOT NULL AND B.PATENT_TYPE = 'FM'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqfm,
		SUM(CASE WHEN B.ISVALID = '1' AND B.SQRQ IS NOT NULL AND B.PATENT_TYPE = 'SYXX'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqsyxx,
		SUM(CASE WHEN D.EXTRA1 IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			D.SPRQ_ZGBM <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			D.SPRQ_ZGBM <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN D.EXTRA1 ELSE 0 END) AS sqssxy,
		'0' AS gwzl,
		'0' AS sqgwzl,
		'0' AS jsmmzjps,
		'0' AS jsmmrd,
		'0' AS jsmmssjxy,
		'0' AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO B ON
		A.PROJECT_CODE = B.FROM_NO
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO C ON
		B.APPLY_ID = C.APPLY_ID
		LEFT JOIN ${zzzcSchema}.T_KIZL_SSJ_BASEINFO D ON B.PATENT_ID = D.PATENT_ID
		LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ E ON C.APPLY_ID = E.BIZ_ID AND E.IS_END = '0'
		WHERE
		A.MAIN_PROJECT_GUID IS NULL
		AND A.EXTRA11 = 'a'
		AND SUBSTRING(A.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		A.PROJECT_CODE
		UNION
		SELECT
		Q.PROJECT_CODE AS PROJECT_CODE,
		MAX(Q.PROJECT_NAME) AS PROJECT_NAME,
		MAX(Q.RECORD_GUID) AS RECORD_GUID,
		SUM(CASE WHEN E.IS_END = '0'
		<isNotEmpty prepend = " AND " property = "startDate">
			C.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS zlzjps,
		SUM(CASE WHEN B.PATENT_STATUS = '02'
		<isNotEmpty prepend = " AND " property = "startDate">
			C.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS djd,
		SUM(CASE WHEN B.PATENT_STATUS = '04'
		<isNotEmpty prepend = " AND " property = "startDate">
			C.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS dlz,
		SUM(CASE WHEN B.FLOW_STATUS != 'draft'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS qbzl,
		SUM(CASE WHEN B.PATENT_TYPE = 'FM'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS fm,
		SUM(CASE WHEN B.PATENT_TYPE = 'SYXX'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS syxx,
		SUM(CASE WHEN B.ISVALID = '1' AND B.SQRQ IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqqbzl,
		SUM(CASE WHEN B.ISVALID = '1' AND B.SQRQ IS NOT NULL AND B.PATENT_TYPE = 'FM'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqfm,
		SUM(CASE WHEN B.ISVALID = '1' AND B.SQRQ IS NOT NULL AND B.PATENT_TYPE = 'SYXX'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqsyxx,
		SUM(CASE WHEN D.EXTRA1 IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			D.SPRQ_ZGBM <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			D.SPRQ_ZGBM <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN D.EXTRA1 ELSE 0 END) AS sqssxy,
		'0' AS gwzl,
		'0' AS sqgwzl,
		'0' AS jsmmzjps,
		'0' AS jsmmrd,
		'0' AS jsmmssjxy,
		'0' AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT Q ON
		A.MAIN_PROJECT_GUID = Q.RECORD_GUID
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO B ON
		A.PROJECT_CODE = B.FROM_NO
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO C ON
		B.APPLY_ID = C.APPLY_ID
		LEFT JOIN ${zzzcSchema}.T_KIZL_SSJ_BASEINFO D ON B.PATENT_ID = D.PATENT_ID
		LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ E ON C.APPLY_ID = E.BIZ_ID AND E.IS_END = '0'
		WHERE
		Q.EXTRA11 = 'a'
		AND SUBSTRING(Q.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		Q.PROJECT_CODE
		UNION
		SELECT
		A.PROJECT_CODE AS PROJECT_CODE,
		MAX(A.PROJECT_NAME) AS PROJECT_NAME,
		MAX(A.RECORD_GUID) AS RECORD_GUID,
		'0' AS zlzjps,
		'0' AS djd,
		'0' AS dlz,
		'0' AS qbzl,
		'0' AS fm,
		'0' AS syxx,
		'0' AS sqqbzl,
		'0' AS sqfm,
		'0' AS sqsyxx,
		'0' AS sqssxy,
		SUM(CASE WHEN B.FLOW_STATUS != 'draft'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.GJSQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.GJSQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS gwzl,
		SUM(CASE WHEN C.STATE_SQRQ IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			C.STATE_SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.STATE_SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqgwzl,
		'0' AS jsmmzjps,
		'0' AS jsmmrd,
		'0' AS jsmmssjxy,
		'0' AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.PROJECT_CODE = B.EXTRA4
		LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO C ON B.JWSQ_ID = C.JWSQ_ID
		WHERE
		A.MAIN_PROJECT_GUID IS NULL
		AND A.EXTRA11 = 'a'
		AND SUBSTRING(A.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		A.PROJECT_CODE
		UNION
		SELECT
		Q.PROJECT_CODE AS PROJECT_CODE,
		MAX(Q.PROJECT_NAME) AS PROJECT_NAME,
		MAX(Q.RECORD_GUID) AS RECORD_GUID,
		'0' AS zlzjps,
		'0' AS djd,
		'0' AS dlz,
		'0' AS qbzl,
		'0' AS fm,
		'0' AS syxx,
		'0' AS sqqbzl,
		'0' AS sqfm,
		'0' AS sqsyxx,
		'0' AS sqssxy,
		SUM(CASE WHEN B.FLOW_STATUS != 'draft'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.GJSQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.GJSQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS gwzl,
		SUM(CASE WHEN C.STATE_SQRQ IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			C.STATE_SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.STATE_SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqgwzl,
		'0' AS jsmmzjps,
		'0' AS jsmmrd,
		'0' AS jsmmssjxy,
		'0' AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT Q ON
		A.MAIN_PROJECT_GUID = Q.RECORD_GUID
		LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.PROJECT_CODE = B.EXTRA4
		LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO C ON B.JWSQ_ID = C.JWSQ_ID
		WHERE
		Q.EXTRA11 = 'a'
		AND SUBSTRING(Q.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		Q.PROJECT_CODE
		UNION
		SELECT
		A.PROJECT_CODE AS PROJECT_CODE,
		MAX(A.PROJECT_NAME) AS PROJECT_NAME,
		MAX(A.RECORD_GUID) AS RECORD_GUID,
		'0' AS zlzjps,
		'0' AS djd,
		'0' AS dlz,
		'0' AS qbzl,
		'0' AS fm,
		'0' AS syxx,
		'0' AS sqqbzl,
		'0' AS sqfm,
		'0' AS sqsyxx,
		'0' AS sqssxy,
		'0' AS gwzl,
		'0' AS sqgwzl,
		SUM(CASE WHEN E.IS_END = '0'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS jsmmzjps,
		SUM(CASE WHEN B.CONFIRM_NUM IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			B.CONFIRM_TIME <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.CONFIRM_TIME <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS jsmmrd,
		SUM(CASE WHEN C.BENEFIT_DIRECT_INDI IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			C.EXTRA6 <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.EXTRA6 <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN C.BENEFIT_DIRECT_INDI ELSE 0 END) AS jsmmssjxy,
		'0' AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY B ON A.PROJECT_CODE = B.SOURCE_NUM
		LEFT JOIN ${zzzcSchema}.T_KYMM_TECH_IMPL C ON B.TECHNOLOGY_ID = C.TECHNOLOGY_ID
		LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ E ON B.TECHNOLOGY_ID = E.BIZ_ID AND E.IS_END = '0'
		WHERE
		A.MAIN_PROJECT_GUID IS NULL
		AND A.EXTRA11 = 'a'
		AND SUBSTRING(A.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		A.PROJECT_CODE
		UNION
		SELECT
		Q.PROJECT_CODE AS PROJECT_CODE,
		MAX(Q.PROJECT_NAME) AS PROJECT_NAME,
		MAX(Q.RECORD_GUID) AS RECORD_GUID,
		'0' AS zlzjps,
		'0' AS djd,
		'0' AS dlz,
		'0' AS qbzl,
		'0' AS fm,
		'0' AS syxx,
		'0' AS sqqbzl,
		'0' AS sqfm,
		'0' AS sqsyxx,
		'0' AS sqssxy,
		'0' AS gwzl,
		'0' AS sqgwzl,
		SUM(CASE WHEN E.IS_END = '0'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS jsmmzjps,
		SUM(CASE WHEN B.CONFIRM_NUM IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			B.CONFIRM_TIME <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.CONFIRM_TIME <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS jsmmrd,
		SUM(CASE WHEN C.BENEFIT_DIRECT_INDI IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			C.EXTRA6 <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.EXTRA6 <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN C.BENEFIT_DIRECT_INDI ELSE 0 END) AS jsmmssjxy,
		'0' AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT Q ON
		A.MAIN_PROJECT_GUID = Q.RECORD_GUID
		LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY B ON A.PROJECT_CODE = B.SOURCE_NUM
		LEFT JOIN ${zzzcSchema}.T_KYMM_TECH_IMPL C ON B.TECHNOLOGY_ID = C.TECHNOLOGY_ID
		LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ E ON B.TECHNOLOGY_ID = E.BIZ_ID AND E.IS_END = '0'
		WHERE
		Q.EXTRA11 = 'a'
		AND SUBSTRING(Q.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		Q.PROJECT_CODE
		UNION
		SELECT
		A.PROJECT_CODE AS PROJECT_CODE,
		MAX(A.PROJECT_NAME) AS PROJECT_NAME,
		MAX(A.RECORD_GUID) AS RECORD_GUID,
		'0' AS zlzjps,
		'0' AS djd,
		'0' AS dlz,
		'0' AS qbzl,
		'0' AS fm,
		'0' AS syxx,
		'0' AS sqqbzl,
		'0' AS sqfm,
		'0' AS sqsyxx,
		'0' AS sqssxy,
		'0' AS gwzl,
		'0' AS sqgwzl,
		'0' AS jsmmzjps,
		'0' AS jsmmrd,
		'0' AS jsmmssjxy,
		SUM(CASE WHEN B.FLOW_STATUS = 'end' AND B.CGDJH IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			B.CGDJRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.CGDJRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYCG_REGISTER_INFO B ON A.PROJECT_CODE = B.PROJECT_CODE
		WHERE
		A.MAIN_PROJECT_GUID IS NULL
		AND A.EXTRA11 = 'a'
		AND SUBSTRING(A.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		A.PROJECT_CODE
		UNION
		SELECT
		Q.PROJECT_CODE AS PROJECT_CODE,
		MAX(Q.PROJECT_NAME) AS PROJECT_NAME,
		MAX(Q.RECORD_GUID) AS RECORD_GUID,
		'0' AS zlzjps,
		'0' AS djd,
		'0' AS dlz,
		'0' AS qbzl,
		'0' AS fm,
		'0' AS syxx,
		'0' AS sqqbzl,
		'0' AS sqfm,
		'0' AS sqsyxx,
		'0' AS sqssxy,
		'0' AS gwzl,
		'0' AS sqgwzl,
		'0' AS jsmmzjps,
		'0' AS jsmmrd,
		'0' AS jsmmssjxy,
		SUM(CASE WHEN B.FLOW_STATUS = 'end' AND B.CGDJH IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			B.CGDJRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.CGDJRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT Q ON
		A.MAIN_PROJECT_GUID = Q.RECORD_GUID
		LEFT JOIN ${kjglSchema}.T_KYCG_REGISTER_INFO B ON A.PROJECT_CODE = B.PROJECT_CODE
		WHERE
		Q.EXTRA11 = 'a'
		AND SUBSTRING(Q.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		Q.PROJECT_CODE
		) A
		GROUP BY
		A.PROJECT_CODE
	</select>

	<select id="queryJpgsjckTotal"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
		'合计' AS "projectName",
		SUM(A.zlzjps) AS "zlzjps",
		SUM(A.djd) AS "djd",
		SUM(A.dlz) AS "dlz",
		SUM(A.qbzl) AS "qbzl",
		SUM(A.fm) AS "fm",
		SUM(A.syxx) AS "syxx",
		SUM(A.gwzl) AS "gwzl",
		SUM(A.sqqbzl) AS "sqqbzl",
		SUM(A.sqfm) AS "sqfm",
		SUM(A.sqsyxx) AS "sqsyxx",
		SUM(A.sqgwzl) AS "sqgwzl",
		SUM(A.sqssxy) AS "sqssxy",
		SUM(A.jsmmzjps) AS "jsmmzjps",
		SUM(A.jsmmrd) AS "jsmmrd",
		SUM(A.jsmmssjxy) AS "jsmmssjxy",
		SUM(A.cgdj) AS "cgdj",
		'0' AS "sbzc"
		FROM
		(
		SELECT
		A.PROJECT_CODE AS PROJECT_CODE,
		MAX(A.PROJECT_NAME) AS PROJECT_NAME,
		MAX(A.RECORD_GUID) AS RECORD_GUID,
		SUM(CASE WHEN E.IS_END = '0'
		<isNotEmpty prepend = " AND " property = "startDate">
			C.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS zlzjps,
		SUM(CASE WHEN B.PATENT_STATUS = '02'
		<isNotEmpty prepend = " AND " property = "startDate">
			C.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS djd,
		SUM(CASE WHEN B.PATENT_STATUS = '04'
		<isNotEmpty prepend = " AND " property = "startDate">
			C.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS dlz,
		SUM(CASE WHEN B.FLOW_STATUS != 'draft'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS qbzl,
		SUM(CASE WHEN B.PATENT_TYPE = 'FM'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS fm,
		SUM(CASE WHEN B.PATENT_TYPE = 'SYXX'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS syxx,
		SUM(CASE WHEN B.ISVALID = '1' AND B.SQRQ IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqqbzl,
		SUM(CASE WHEN B.ISVALID = '1' AND B.SQRQ IS NOT NULL AND B.PATENT_TYPE = 'FM'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqfm,
		SUM(CASE WHEN B.ISVALID = '1' AND B.SQRQ IS NOT NULL AND B.PATENT_TYPE = 'SYXX'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqsyxx,
		SUM(CASE WHEN D.EXTRA1 IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			D.SPRQ_ZGBM <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			D.SPRQ_ZGBM <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN D.EXTRA1 ELSE 0 END) AS sqssxy,
		'0' AS gwzl,
		'0' AS sqgwzl,
		'0' AS jsmmzjps,
		'0' AS jsmmrd,
		'0' AS jsmmssjxy,
		'0' AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO B ON
		A.PROJECT_CODE = B.FROM_NO
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO C ON
		B.APPLY_ID = C.APPLY_ID
		LEFT JOIN ${zzzcSchema}.T_KIZL_SSJ_BASEINFO D ON B.PATENT_ID = D.PATENT_ID
		LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ E ON C.APPLY_ID = E.BIZ_ID AND E.IS_END = '0'
		WHERE
		A.MAIN_PROJECT_GUID IS NULL
		AND A.EXTRA11 = 'a'
		AND SUBSTRING(A.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		A.PROJECT_CODE
		UNION
		SELECT
		Q.PROJECT_CODE AS PROJECT_CODE,
		MAX(Q.PROJECT_NAME) AS PROJECT_NAME,
		MAX(Q.RECORD_GUID) AS RECORD_GUID,
		SUM(CASE WHEN E.IS_END = '0'
		<isNotEmpty prepend = " AND " property = "startDate">
			C.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS zlzjps,
		SUM(CASE WHEN B.PATENT_STATUS = '02'
		<isNotEmpty prepend = " AND " property = "startDate">
			C.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS djd,
		SUM(CASE WHEN B.PATENT_STATUS = '04'
		<isNotEmpty prepend = " AND " property = "startDate">
			C.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS dlz,
		SUM(CASE WHEN B.FLOW_STATUS != 'draft'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS qbzl,
		SUM(CASE WHEN B.PATENT_TYPE = 'FM'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS fm,
		SUM(CASE WHEN B.PATENT_TYPE = 'SYXX'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS syxx,
		SUM(CASE WHEN B.ISVALID = '1' AND B.SQRQ IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqqbzl,
		SUM(CASE WHEN B.ISVALID = '1' AND B.SQRQ IS NOT NULL AND B.PATENT_TYPE = 'FM'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqfm,
		SUM(CASE WHEN B.ISVALID = '1' AND B.SQRQ IS NOT NULL AND B.PATENT_TYPE = 'SYXX'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SLRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SLRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "startDate">
			B.SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqsyxx,
		SUM(CASE WHEN D.EXTRA1 IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			D.SPRQ_ZGBM <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			D.SPRQ_ZGBM <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN D.EXTRA1 ELSE 0 END) AS sqssxy,
		'0' AS gwzl,
		'0' AS sqgwzl,
		'0' AS jsmmzjps,
		'0' AS jsmmrd,
		'0' AS jsmmssjxy,
		'0' AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT Q ON
		A.MAIN_PROJECT_GUID = Q.RECORD_GUID
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO B ON
		A.PROJECT_CODE = B.FROM_NO
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO C ON
		B.APPLY_ID = C.APPLY_ID
		LEFT JOIN ${zzzcSchema}.T_KIZL_SSJ_BASEINFO D ON B.PATENT_ID = D.PATENT_ID
		LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ E ON C.APPLY_ID = E.BIZ_ID AND E.IS_END = '0'
		WHERE
		Q.EXTRA11 = 'a'
		AND SUBSTRING(Q.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		Q.PROJECT_CODE
		UNION
		SELECT
		A.PROJECT_CODE AS PROJECT_CODE,
		MAX(A.PROJECT_NAME) AS PROJECT_NAME,
		MAX(A.RECORD_GUID) AS RECORD_GUID,
		'0' AS zlzjps,
		'0' AS djd,
		'0' AS dlz,
		'0' AS qbzl,
		'0' AS fm,
		'0' AS syxx,
		'0' AS sqqbzl,
		'0' AS sqfm,
		'0' AS sqsyxx,
		'0' AS sqssxy,
		SUM(CASE WHEN B.FLOW_STATUS != 'draft'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.GJSQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.GJSQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS gwzl,
		SUM(CASE WHEN C.STATE_SQRQ IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			C.STATE_SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.STATE_SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqgwzl,
		'0' AS jsmmzjps,
		'0' AS jsmmrd,
		'0' AS jsmmssjxy,
		'0' AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.PROJECT_CODE = B.EXTRA4
		LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO C ON B.JWSQ_ID = C.JWSQ_ID
		WHERE
		A.MAIN_PROJECT_GUID IS NULL
		AND A.EXTRA11 = 'a'
		AND SUBSTRING(A.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		A.PROJECT_CODE
		UNION
		SELECT
		Q.PROJECT_CODE AS PROJECT_CODE,
		MAX(Q.PROJECT_NAME) AS PROJECT_NAME,
		MAX(Q.RECORD_GUID) AS RECORD_GUID,
		'0' AS zlzjps,
		'0' AS djd,
		'0' AS dlz,
		'0' AS qbzl,
		'0' AS fm,
		'0' AS syxx,
		'0' AS sqqbzl,
		'0' AS sqfm,
		'0' AS sqsyxx,
		'0' AS sqssxy,
		SUM(CASE WHEN B.FLOW_STATUS != 'draft'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.GJSQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.GJSQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS gwzl,
		SUM(CASE WHEN C.STATE_SQRQ IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			C.STATE_SQRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.STATE_SQRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS sqgwzl,
		'0' AS jsmmzjps,
		'0' AS jsmmrd,
		'0' AS jsmmssjxy,
		'0' AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT Q ON
		A.MAIN_PROJECT_GUID = Q.RECORD_GUID
		LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.PROJECT_CODE = B.EXTRA4
		LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO C ON B.JWSQ_ID = C.JWSQ_ID
		WHERE
		Q.EXTRA11 = 'a'
		AND SUBSTRING(Q.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		Q.PROJECT_CODE
		UNION
		SELECT
		A.PROJECT_CODE AS PROJECT_CODE,
		MAX(A.PROJECT_NAME) AS PROJECT_NAME,
		MAX(A.RECORD_GUID) AS RECORD_GUID,
		'0' AS zlzjps,
		'0' AS djd,
		'0' AS dlz,
		'0' AS qbzl,
		'0' AS fm,
		'0' AS syxx,
		'0' AS sqqbzl,
		'0' AS sqfm,
		'0' AS sqsyxx,
		'0' AS sqssxy,
		'0' AS gwzl,
		'0' AS sqgwzl,
		SUM(CASE WHEN E.IS_END = '0'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS jsmmzjps,
		SUM(CASE WHEN B.CONFIRM_NUM IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			B.CONFIRM_TIME <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.CONFIRM_TIME <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS jsmmrd,
		SUM(CASE WHEN C.BENEFIT_DIRECT_INDI IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			C.EXTRA6 <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.EXTRA6 <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN C.BENEFIT_DIRECT_INDI ELSE 0 END) AS jsmmssjxy,
		'0' AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY B ON A.PROJECT_CODE = B.SOURCE_NUM
		LEFT JOIN ${zzzcSchema}.T_KYMM_TECH_IMPL C ON B.TECHNOLOGY_ID = C.TECHNOLOGY_ID
		LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ E ON B.TECHNOLOGY_ID = E.BIZ_ID AND E.IS_END = '0'
		WHERE
		A.MAIN_PROJECT_GUID IS NULL
		AND A.EXTRA11 = 'a'
		AND SUBSTRING(A.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		A.PROJECT_CODE
		UNION
		SELECT
		Q.PROJECT_CODE AS PROJECT_CODE,
		MAX(Q.PROJECT_NAME) AS PROJECT_NAME,
		MAX(Q.RECORD_GUID) AS RECORD_GUID,
		'0' AS zlzjps,
		'0' AS djd,
		'0' AS dlz,
		'0' AS qbzl,
		'0' AS fm,
		'0' AS syxx,
		'0' AS sqqbzl,
		'0' AS sqfm,
		'0' AS sqsyxx,
		'0' AS sqssxy,
		'0' AS gwzl,
		'0' AS sqgwzl,
		SUM(CASE WHEN E.IS_END = '0'
		<isNotEmpty prepend = " AND " property = "startDate">
			B.APPLY_DATE <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.APPLY_DATE <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS jsmmzjps,
		SUM(CASE WHEN B.CONFIRM_NUM IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			B.CONFIRM_TIME <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.CONFIRM_TIME <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS jsmmrd,
		SUM(CASE WHEN C.BENEFIT_DIRECT_INDI IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			C.EXTRA6 <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			C.EXTRA6 <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN C.BENEFIT_DIRECT_INDI ELSE 0 END) AS jsmmssjxy,
		'0' AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT Q ON
		A.MAIN_PROJECT_GUID = Q.RECORD_GUID
		LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY B ON A.PROJECT_CODE = B.SOURCE_NUM
		LEFT JOIN ${zzzcSchema}.T_KYMM_TECH_IMPL C ON B.TECHNOLOGY_ID = C.TECHNOLOGY_ID
		LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ E ON B.TECHNOLOGY_ID = E.BIZ_ID AND E.IS_END = '0'
		WHERE
		Q.EXTRA11 = 'a'
		AND SUBSTRING(Q.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		Q.PROJECT_CODE
		UNION
		SELECT
		A.PROJECT_CODE AS PROJECT_CODE,
		MAX(A.PROJECT_NAME) AS PROJECT_NAME,
		MAX(A.RECORD_GUID) AS RECORD_GUID,
		'0' AS zlzjps,
		'0' AS djd,
		'0' AS dlz,
		'0' AS qbzl,
		'0' AS fm,
		'0' AS syxx,
		'0' AS sqqbzl,
		'0' AS sqfm,
		'0' AS sqsyxx,
		'0' AS sqssxy,
		'0' AS gwzl,
		'0' AS sqgwzl,
		'0' AS jsmmzjps,
		'0' AS jsmmrd,
		'0' AS jsmmssjxy,
		SUM(CASE WHEN B.FLOW_STATUS = 'end' AND B.CGDJH IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			B.CGDJRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.CGDJRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYCG_REGISTER_INFO B ON A.PROJECT_CODE = B.PROJECT_CODE
		WHERE
		A.MAIN_PROJECT_GUID IS NULL
		AND A.EXTRA11 = 'a'
		AND SUBSTRING(A.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		A.PROJECT_CODE
		UNION
		SELECT
		Q.PROJECT_CODE AS PROJECT_CODE,
		MAX(Q.PROJECT_NAME) AS PROJECT_NAME,
		MAX(Q.RECORD_GUID) AS RECORD_GUID,
		'0' AS zlzjps,
		'0' AS djd,
		'0' AS dlz,
		'0' AS qbzl,
		'0' AS fm,
		'0' AS syxx,
		'0' AS sqqbzl,
		'0' AS sqfm,
		'0' AS sqsyxx,
		'0' AS sqssxy,
		'0' AS gwzl,
		'0' AS sqgwzl,
		'0' AS jsmmzjps,
		'0' AS jsmmrd,
		'0' AS jsmmssjxy,
		SUM(CASE WHEN B.FLOW_STATUS = 'end' AND B.CGDJH IS NOT NULL
		<isNotEmpty prepend = " AND " property = "startDate">
			B.CGDJRQ <![CDATA[ >= ]]> #startDate#
		</isNotEmpty>
		<isNotEmpty prepend = " AND " property = "endDate">
			B.CGDJRQ <![CDATA[ <= ]]> #endDate#
		</isNotEmpty>
		THEN 1 ELSE 0 END) AS cgdj
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT Q ON
		A.MAIN_PROJECT_GUID = Q.RECORD_GUID
		LEFT JOIN ${kjglSchema}.T_KYCG_REGISTER_INFO B ON A.PROJECT_CODE = B.PROJECT_CODE
		WHERE
		Q.EXTRA11 = 'a'
		AND SUBSTRING(Q.PROJECT_CODE,2,2) = #jpglc#
		GROUP BY
		Q.PROJECT_CODE
		) A
	</select>

	<select id="queryKizlFmr"  parameterClass="hashmap" resultClass="hashmap">
		SELECT LISTAGG(EMP_NAME ||  '-' || GXXS ||  '%', ',') AS "fmr"
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX
		WHERE APPLY_ID = #applyId#
	</select>

	<select id="queryJpgsjckKizlList"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
			C.PATENT_ID as "patentId" ,
			C.APPLY_ID as "applyId" ,
			C.SERIAL_NUM as "serialNum" ,
			C.JSBH as "jsbh" ,
			C.BGBH as "bgbh" ,
			C.APPLY_NAME as "applyName" ,
			C.FIRST_DEPT_CODE as "firstDeptCode" ,
			C.FIRST_DEPT_NAME as "firstDeptName" ,
			C.GLDW_CODE as "gldwCode" ,
			C.GLDW_NAME as "gldwName" ,
			C.LABEL as "label" ,
			C.TECH_AREA as "techArea" ,
			C.USE_PROPOSE as "usePropose" ,
			C.KNOWLEDGE_CLASS as "knowledgeClass" ,
			C.LXR_CODE as "lxrCode" ,
			C.LXR_NAME as "lxrName" ,
			C.LXR_PHONE as "lxrPhone" ,
			C.LXR_EMAIL as "lxrEmail" ,
			C.LXR_MOBILE as "lxrMobile" ,
			C.FROM_TYPE as "fromType" ,
			C.FROM_NO as "fromNo" ,
			C.FROM_NAME as "fromName" ,
			C.FROM_CONTENT as "fromContent" ,
			C.USE_METHOD as "useMethod" ,
			C.USE_DEPT as "useDept" ,
			C.USE_DEPT_NAME as "useDeptName" ,
			C.USE_EXPECTED as "useExpected" ,
			C.USE_EXPECTED_NAME as "useExpectedName" ,
			C.USE_FIRSTDATE as "useFirstdate" ,
			C.REASON_NOUSE as "reasonNouse" ,
			C.CONTENT_NOUSE as "contentNouse" ,
			C.JPG_FLAG as "jpgFlag" ,
			C.JPG_XH as "jpgXh" ,
			C.JPG_GW as "jpgGw" ,
			C.JPG_TEAM as "jpgTeam" ,
			C.ISBIGXM as "isbigxm" ,
			C.JFDW_CODE as "jfdwCode" ,
			C.FLOW_STATUS as "flowStatus" ,
			C.PATENT_STATUS as "patentStatus" ,
			C.DJD_PERSON as "djdPerson" ,
			C.DJD_DATE as "djdDate" ,
			C.JD_PERSON as "jdPerson" ,
			C.JD_DATE as "jdDate" ,
			C.QS as "qs" ,
			C.SLRQ as "slrq" ,
			C.PATENT_NO as "patentNo" ,
			C.PATENT_TYPE as "patentType" ,
			C.YXQR as "yxqr" ,
			C.QLYQSL as "qlyqsl" ,
			C.SMSYS as "smsys" ,
			C.SWS_GUID as "swsGuid" ,
			C.SWSDLR as "swsdlr" ,
			C.SWSDLR_PHONE as "swsdlrPhone" ,
			C.SWSDLR_EMAIL as "swsdlrEmail" ,
			C.MONEY_DLF as "moneyDlf" ,
			C.MONEY_QF as "moneyQf" ,
			C.MONEY_SQF as "moneySqf" ,
			C.MONEY_GBYSF as "moneyGbysf" ,
			C.MONEY_SMSFJF as "moneySmsfjf" ,
			C.MONEY_YHS as "moneyYhs" ,
			C.SQTZ_FWDATE as "sqtzFwdate" ,
			C.MONEY_FIRST as "moneyFirst" ,
			C.ZLH as "zlh" ,
			C.SQRQ as "sqrq" ,
			C.FLZT as "flzt" ,
			C.ISVALID as "isvalid" ,
			C.ISGD_ARCHIVE as "isgdArchive" ,
			C.ARCHIVE_GUID as "archiveGuid" ,
			C.ISGD_DZ as "isgdDz" ,
			C.FML as "fml" ,
			C.FMR_CODE as "fmrCode" ,
			C.DJR_DATE as "djrDate" ,
			C.MONEY_GGYSF as "moneyGgysf" ,
			C.ZZRQ as "zzrq" ,
			C.ZZBZ as "zzbz" ,
			C.ZZTXR as "zztxr" ,
			C.ZZTXRQ as "zztxrq" ,
			C.ZZBJ as "zzbj"
		FROM
		${zzzcSchema}.T_KIZL_PATENT_INFO C
		JOIN (SELECT
		A.RECORD_GUID AS RECORD_GUID,
		A.PROJECT_CODE AS PROJECT_CODE,
		A.PROJECT_NAME AS PROJECT_NAME
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT B ON A.MAIN_PROJECT_GUID = B.RECORD_GUID
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="qbzl">A.MAIN_PROJECT_GUID IS NULL AND A.EXTRA11 = 'a' AND SUBSTRING(A.PROJECT_CODE,2,2) = #year# OR(B.EXTRA11 = 'a' AND SUBSTRING(B.PROJECT_CODE,2,2)= #year#)</isNotEmpty>
			<isNotEmpty prepend=" AND " property="guid">A.RECORD_GUID = #guid# OR B.RECORD_GUID = #guid#</isNotEmpty>
		</dynamic>
		) A ON C.FROM_NO = A.PROJECT_CODE
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="patentStatus">C.PATENT_STATUS = #patentStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentType">C.PATENT_TYPE = #patentType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isValid">C.ISVALID = #isValid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isSqrq">C.SQRQ IS NOT NULL</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqStartDate">C.SQRQ <![CDATA[ >= ]]> #sqStartDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqEndDate">C.SQRQ <![CDATA[ <= ]]> #sqEndDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="slStartDate">C.SLRQ <![CDATA[ >= ]]> #slStartDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="slEndDate">C.SLRQ <![CDATA[ <= ]]> #slEndDate#</isNotEmpty>
		</dynamic>
		ORDER BY C.BGBH
	</select>

	<select id="queryJpgsjckKizlSbList"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
			A.APPLY_ID as "applyId" ,
			A.APPLY_NAME "applyName" ,
			A.SERIAL_NUM as "serialNum" ,
			A.FIRST_DEPT_NAME "firstDeptName" ,
			A.FROM_NO as "fromNo" ,
			A.FROM_NAME as "fromName" ,
			A.FML AS "fmr",
			A.LXR_CODE || A.LXR_NAME as "lxrName" ,
			A.LXR_PHONE as "lxrPhone"
		FROM
		(SELECT A.*,B.APPLY_DATE AS APPLY_DATE FROM ${zzzcSchema}.T_KIZL_PATENT_INFO A JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO B ON A.APPLY_ID = B.APPLY_ID) A
		JOIN (SELECT
		A.RECORD_GUID AS RECORD_GUID,
		A.PROJECT_CODE AS PROJECT_CODE,
		A.PROJECT_NAME AS PROJECT_NAME
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT B ON A.MAIN_PROJECT_GUID = B.RECORD_GUID
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="qbzl">A.MAIN_PROJECT_GUID IS NULL AND A.EXTRA11 = 'a' AND SUBSTRING(A.PROJECT_CODE,2,2) = #year# OR(B.EXTRA11 = 'a' AND SUBSTRING(B.PROJECT_CODE,2,2)= #year#)</isNotEmpty>
			<isNotEmpty prepend=" AND " property="guid">A.RECORD_GUID = #guid# OR B.RECORD_GUID = #guid#</isNotEmpty>
		</dynamic>
		) B ON A.FROM_NO = B.PROJECT_CODE
		LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ R ON A.APPLY_ID = R.BIZ_ID
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="zlzjps">R.IS_END = '0'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentStatus">A.PATENT_STATUS = #patentStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentType">A.PATENT_TYPE = #patentType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isValid">A.ISVALID = #isValid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sbStartDate">A.APPLY_DATE <![CDATA[ >= ]]> #sbStartDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sbEndDate">A.APPLY_DATE <![CDATA[ <= ]]> #sbEndDate#</isNotEmpty>
		</dynamic>
	</select>

	<select id="queryJpgsjckKymmList"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
			B.technology_id as "technologyId" ,
			B.confirm_num as "confirmNum" ,
		    B.technology_name as "technologyName" ,
			B.confirm_time as "confirmTime" ,
			B.source_type as "sourceType" ,
			B.source_num as "sourceNum" ,
			B.source_name as "sourceName" ,
			B.firstdept_code as "firstdeptCode" ,
			B.firstdept_name as "firstdeptName" ,
			B.contact_person_gh as "contactPersonGh" ,
			B.contact_person_name as "contactPersonName" ,
			B.contactperson_email as "contactpersonEmail" ,
			B.contactperson_ph as "contactpersonPh" ,
			B.contactperson_tel as "contactpersonTel"
		FROM
		${zzzcSchema}.T_KYMM_TECHNOLOGY B
		JOIN (SELECT
		A.RECORD_GUID AS RECORD_GUID,
		A.PROJECT_CODE AS PROJECT_CODE,
		A.PROJECT_NAME AS PROJECT_NAME
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT B ON A.MAIN_PROJECT_GUID = B.RECORD_GUID
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="qbzl">A.MAIN_PROJECT_GUID IS NULL AND A.EXTRA11 = 'a' AND SUBSTRING(A.PROJECT_CODE,2,2) = #year# OR(B.EXTRA11 = 'a' AND SUBSTRING(B.PROJECT_CODE,2,2)= #year#)</isNotEmpty>
			<isNotEmpty prepend=" AND " property="guid">A.RECORD_GUID = #guid# OR B.RECORD_GUID = #guid#</isNotEmpty>
		</dynamic>
		) C ON B.SOURCE_NUM = C.PROJECT_CODE
		LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ R ON B.TECHNOLOGY_ID = R.BIZ_ID
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="zjps">R.IS_END = '0'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isRdh">B.CONFIRM_NUM IS NOT NULL</isNotEmpty>
			<isNotEmpty prepend=" AND " property="startDate">B.CONFIRM_TIME <![CDATA[ >= ]]> #startDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="endDate">B.CONFIRM_TIME <![CDATA[ <= ]]> #endDate#</isNotEmpty>
		</dynamic>
		ORDER BY B.CONFIRM_NUM
	</select>

	<select id="queryJpgsjckCgdjList"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
			B.PROJECT_NAME "projectName",
			B.CGDJH AS "cgdjh",
			B.CGDJRQ AS "cgdjrq"
		FROM
		${kjglSchema}.T_KYCG_REGISTER_INFO B
		JOIN (SELECT
		A.RECORD_GUID AS RECORD_GUID,
		A.PROJECT_CODE AS PROJECT_CODE,
		A.PROJECT_NAME AS PROJECT_NAME
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT B ON A.MAIN_PROJECT_GUID = B.RECORD_GUID
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="qbzl">A.MAIN_PROJECT_GUID IS NULL AND A.EXTRA11 = 'a' AND SUBSTRING(A.PROJECT_CODE,2,2) = #year# OR(B.EXTRA11 = 'a' AND SUBSTRING(B.PROJECT_CODE,2,2)= #year#)</isNotEmpty>
			<isNotEmpty prepend=" AND " property="guid">A.RECORD_GUID = #guid# OR B.RECORD_GUID = #guid#</isNotEmpty>
		</dynamic>
		) C ON B.PROJECT_CODE = C.PROJECT_CODE
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="isDjh">B.CGDJH IS NOT NULL</isNotEmpty>
			<isNotEmpty prepend=" AND " property="startDate">B.CGDJRQ <![CDATA[ >= ]]> #startDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="endDate">B.CGDJRQ <![CDATA[ <= ]]> #endDate#</isNotEmpty>
		</dynamic>
		ORDER BY B.CGDJH
	</select>

	<select id="queryJpgsjckKwzlSlList"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
		B.JWSQ_ID AS "jwsqId",
		B.GW_SQH AS "gwSqh",
		B.IN_SQH AS "inSqh",
		B.in_bgbh as "inBgbh" ,
		B.in_zlmc as "inZlmc" ,
		B.SBBM_NAME AS "sbbmName",
		B.ALL_PERSON AS "allPerson" ,
		B.SQR AS "sqr",
		B.GJSQRQ AS "gjsqrq",
		B.PRIORITY_DATE AS "priorityDate" ,
		B.PCTSQH AS "pctsqh"
		FROM
		${zzzcSchema}.T_KWZL_APPLY_BASEINFO B
		JOIN (SELECT
			A.RECORD_GUID AS RECORD_GUID,
			A.PROJECT_CODE AS PROJECT_CODE,
			A.PROJECT_NAME AS PROJECT_NAME
			FROM
			${kjglSchema}.T_KYXM_PROJECT A
			LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT B ON A.MAIN_PROJECT_GUID = B.RECORD_GUID
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="qbzl">A.MAIN_PROJECT_GUID IS NULL AND A.EXTRA11 = 'a' AND SUBSTRING(A.PROJECT_CODE,2,2) = #year# OR(B.EXTRA11 = 'a' AND SUBSTRING(B.PROJECT_CODE,2,2)= #year#)</isNotEmpty>
				<isNotEmpty prepend=" AND " property="guid">A.RECORD_GUID = #guid# OR B.RECORD_GUID = #guid#</isNotEmpty>
			</dynamic>
		) C ON B.EXTRA4 = C.PROJECT_CODE
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="isSl">B.GJSQRQ IS NOT NULL</isNotEmpty>
			<isNotEmpty prepend=" AND " property="startDate">B.GJSQRQ <![CDATA[ >= ]]> #startDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="endDate">B.GJSQRQ <![CDATA[ <= ]]> #endDate#</isNotEmpty>
		</dynamic>
		ORDER BY B.IN_SQH
	</select>

	<select id="queryJpgsjckKwzlSqList"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
		B.JWSQ_ID AS "jwsqId",
		B.GW_SQH AS "gwSqh",
		B.IN_SQH AS "inSqh",
		B.in_bgbh as "inBgbh" ,
		B.in_zlmc as "inZlmc" ,
		C.NAME_ENG AS "nameEng" ,
		C.STATE_ZLH AS "stateZlh" ,
		B.SBBM_NAME AS "sbbmName",
		B.ALL_PERSON AS "allPerson" ,
		C.STATE_NAME AS "stateName" ,
		C.STATE_SQRQ AS "stateSqrq" ,
		C.ZRZZRQ AS "zrzzrq" ,
		C.AFTER_FQRQ AS "afterFqrq" ,
		C.STATE_SQH AS "stateSqh" ,
		B.SQR AS "sqr",
		B.GJSQRQ AS "gjsqrq",
		B.PRIORITY_DATE AS "priorityDate" ,
		B.PCTSQH AS "pctsqh"
		FROM
		${kjglSchema}.T_KYXM_PROJECT A
		JOIN ${kjglSchema}.T_KYXM_PROJECT Q ON A.MAIN_PROJECT_GUID = Q.RECORD_GUID
		JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.PROJECT_CODE = B.EXTRA4
		JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO C ON B.JWSQ_ID = C.JWSQ_ID
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="guid">A.RECORD_GUID = #guid# OR Q.RECORD_GUID = #guid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="qbzl">A.MAIN_PROJECT_GUID IS NULL AND A.EXTRA11 = 'a' AND YEAR(A.SIGN_DATE) = #year# OR(Q.EXTRA11 = 'a' AND YEAR(Q.SIGN_DATE)= #year#)</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isSq">B.STATE_SQRQ IS NOT NULL</isNotEmpty>
			<isNotEmpty prepend=" AND " property="startDate">C.STATE_SQRQ <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="endDate">C.STATE_SQRQ <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
		</dynamic>
		ORDER BY B.IN_SQH
	</select>

	<select id="queryJpgsjckKymmMember"  parameterClass="hashmap" resultClass="hashmap">
		SELECT LISTAGG(MEMBER_NAME || MEMBER_GH || '(' || CONTRIBUTION || ')', ',') AS "fmr"
		FROM ZZZC.T_KYMM_TECH_MEMBER
		WHERE TECHNOLOGY_ID = #technologyId#
	</select>

	<select id="queryIntegratedPlus"  parameterClass="hashmap" resultClass="com.baosight.bscdkj.common.ki.domain.TkizlPatentInfo">
		SELECT 
			apply_id as "applyId",
			apply_date as "applyDate",
			serial_num as "serialNum",
			apply_name as "applyName",
			patent_type as "patentType",
			first_dept_code as "firstDeptCode",
			first_dept_name as "firstDeptName",
			jsbh as "jsbh",
			bgbh as "bgbh",
			gldw_code as "gldwCode",
			gldw_name as "gldwName",
			patent_status as "patentStatus",
			flow_status as "flowStatus",
			flzt as "flzt",
			sqrq as "sqrq",
			slrq as "slrq",
			patent_no as "patentNo",
			jpg_flag as "jpgFlag",
			jpg_xh as "jpgXh",
			jpg_gw as "jpgGw",
			jpg_team as "jpgTeam",
			isbigxm as "isbigxm",
			fml as "fml",
			fmr_code as "fmrCode",
			lxr_code as "lxrCode",
			lxr_name as "lxrName",
			lxr_phone as "lxrPhone",
			lxr_email as "lxrEmail",
			lxr_mobile as "lxrMobile",
			zgbm_nc as "extra1",
			sws_name as "swsGuid",
			zzrq as "zzrq",
			isvalid as "isvalid",
			extra5 as "extra5"
		FROM(
			SELECT 
				distinct 
					A.apply_id as apply_id,
					A.apply_date as apply_date,
					B.serial_num as serial_num,
					case when B.apply_name is not null then B.apply_name else A.apply_name end as apply_name,
					B.patent_type as patent_type,
					B.first_dept_code as first_dept_code,
					B.first_dept_name as first_dept_name,
					B.jsbh as jsbh,
					B.bgbh as bgbh,
					B.gldw_code as gldw_code,
					B.gldw_name as gldw_name,
					B.patent_status as patent_status,
					B.flow_status as flow_status,
					B.flzt as flzt,
					B.sqrq as sqrq,
					B.slrq as slrq,
					B.patent_no as patent_no,
					B.jpg_flag as jpg_flag,
					B.jpg_xh as jpg_xh,
					B.jpg_gw as jpg_gw,
					B.jpg_team as jpg_team,
					B.isbigxm as isbigxm,
					B.fml as fml,
					B.fmr_code as fmr_code,
					A.lxr_code as lxr_code,
					A.lxr_name as lxr_name,
					A.lxr_phone as lxr_phone,
					A.lxr_email as lxr_email,
					A.lxr_mobile as lxr_mobile,
					A.zgbm_nc as zgbm_nc,
					S.sws_name as sws_name,
					B.zzrq as zzrq,
					B.isvalid as isvalid,
					B.extra5 as extra5
				FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A 
			LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO W ON A.APPLY_ID = W.BUSINESS_ID
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO B ON B.APPLY_ID = A.APPLY_ID
			LEFT JOIN ${platSchema}.TEWPT00 T ON W.FLOW_ID = T.PROCESS_INSTANCE_ID 
			LEFT JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO S ON S.SWS_ID = B.SWS_GUID 
					WHERE 1=1
				<dynamic prepend=" AND ">
					<isNotEmpty prepend=" AND " property="swsCheck">(S.SWS_NAME &lt;&gt; '合作方委托' OR S.SWS_NAME IS NULL)</isNotEmpty>
					<isNotEmpty prepend=" AND " property="gldwCode">B.GLDW_CODE =  #gldwCode#</isNotEmpty>
					<isNotEmpty prepend=" AND " property="completerId">T.COMPLETER_ID =  #completerId#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="assigneeId">T.ASSIGNEE_ID =  #assigneeId#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="flowState">W.FLOW_STATE =  #flowState#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="isvalid">B.ISVALID =  #isvalid#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="flztIn">B.FLZT IN ($flztIn$)</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="serialNumLike">A.SERIAL_NUM LIKE  '$serialNumLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="patentType">B.PATENT_TYPE =  #patentType#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="bgbhLike">B.BGBH LIKE  '$bgbhLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="jsbhLike">B.JSBH LIKE  '$jsbhLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="patentNoLike">B.PATENT_NO LIKE  '$patentNoLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="patentStatus">B.PATENT_STATUS =  #patentStatus#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="qs">B.QS =  #qs#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="firstDeptCode">A.FIRST_DEPT_PATH LIKE  '%$firstDeptCode$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="fromType">A.FROM_TYPE =  #fromType#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="fromNo">A.FROM_NO =  #fromNo#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="fromName">A.FROM_NAME =  #fromName#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="nf">SUBSTR(A.APPLY_DATE,1,4) =  #nf#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
				</dynamic>
			) temp	
				<dynamic prepend=" WHERE ">
					<isNotEmpty prepend=" AND " property="applyNameLike">apply_name LIKE '$applyNameLike$%'</isNotEmpty>
				</dynamic>
			<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
	
	<select id="queryIntegratedQd"  parameterClass="hashmap" resultClass="com.baosight.bscdkj.common.ki.domain.TkizlPatentInfoQd">
		SELECT 
			apply_id as "applyId",
			apply_date as "applyDate",
			serial_num as "serialNum",
			apply_name as "applyName",
			patent_type as "patentType",
			first_dept_code as "firstDeptCode",
			first_dept_name as "firstDeptName",
			jsbh as "jsbh",
			bgbh as "bgbh",
			gldw_code as "gldwCode",
			gldw_name as "gldwName",
			patent_status as "patentStatus",
			flow_status as "flowStatus",
			flzt as "flzt",
			sqrq as "sqrq",
			slrq as "slrq",
			patent_no as "patentNo",
			jpg_flag as "jpgFlag",
			jpg_xh as "jpgXh",
			jpg_gw as "jpgGw",
			jpg_team as "jpgTeam",
			isbigxm as "isbigxm",
			fml as "fml",
			fmr_code as "fmrCode",
			lxr_code as "lxrCode",
			lxr_name as "lxrName",
			lxr_phone as "lxrPhone",
			lxr_email as "lxrEmail",
			lxr_mobile as "lxrMobile",
			zgbm_nc as "extra1",
			sws_name as "swsGuid",
			zzrq as "zzrq",
			extra5 as "extra5"
		FROM(
			SELECT 
				distinct 
					A.apply_id as apply_id,
					A.apply_date as apply_date,
					B.serial_num as serial_num,
					case when B.apply_name is not null then B.apply_name else A.apply_name end as apply_name,
					B.patent_type as patent_type,
					B.first_dept_code as first_dept_code,
					B.first_dept_name as first_dept_name,
					B.jsbh as jsbh,
					B.bgbh as bgbh,
					B.gldw_code as gldw_code,
					B.gldw_name as gldw_name,
					B.patent_status as patent_status,
					B.flow_status as flow_status,
					B.flzt as flzt,
					B.sqrq as sqrq,
					B.slrq as slrq,
					B.patent_no as patent_no,
					B.jpg_flag as jpg_flag,
					B.jpg_xh as jpg_xh,
					B.jpg_gw as jpg_gw,
					B.jpg_team as jpg_team,
					B.isbigxm as isbigxm,
					B.fml as fml,
					B.fmr_code as fmr_code,
					B.lxr_code as lxr_code,
					B.lxr_name as lxr_name,
					B.lxr_phone as lxr_phone,
					B.lxr_email as lxr_email,
					B.lxr_mobile as lxr_mobile,
					A.zgbm_nc as zgbm_nc,
					S.sws_name as sws_name,
					B.zzrq as zzrq,
					B.extra5 as extra5
				FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A 
			LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO W ON A.APPLY_ID = W.BUSINESS_ID
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO B ON B.APPLY_ID = A.APPLY_ID
			LEFT JOIN ${platSchema}.TEWPT00 T ON W.FLOW_ID = T.PROCESS_INSTANCE_ID 
			LEFT JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO S ON S.SWS_ID = B.SWS_GUID 
				WHERE 1=1
				<dynamic prepend=" AND ">
					<isNotEmpty prepend=" AND " property="swsCheck">(S.SWS_NAME &lt;&gt; '合作方委托' OR S.SWS_NAME IS NULL)</isNotEmpty>
					<isNotEmpty prepend=" AND " property="gldwCode">B.GLDW_CODE =  #gldwCode#</isNotEmpty>
					<isNotEmpty prepend=" AND " property="completerId">T.COMPLETER_ID =  #completerId#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="assigneeId">T.ASSIGNEE_ID =  #assigneeId#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="flowState">W.FLOW_STATE =  #flowState#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="isvalid">B.ISVALID =  #isvalid#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="flztIn">B.FLZT IN ($flztIn$)</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="serialNumLike">A.SERIAL_NUM LIKE  '$serialNumLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="patentType">B.PATENT_TYPE =  #patentType#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="bgbhLike">B.BGBH LIKE  '$bgbhLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="jsbhLike">B.JSBH LIKE  '$jsbhLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="patentNoLike">B.PATENT_NO LIKE  '$patentNoLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="patentStatus">B.PATENT_STATUS =  #patentStatus#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="qs">B.QS =  #qs#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="firstDeptCode">A.FIRST_DEPT_PATH LIKE  '%$firstDeptCode$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="fromType">A.FROM_TYPE =  #fromType#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="fromNo">A.FROM_NO =  #fromNo#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="fromName">A.FROM_NAME =  #fromName#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="nf">SUBSTR(A.APPLY_DATE,1,4) =  #nf#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
				</dynamic>
			) temp	
				<dynamic prepend=" WHERE ">
					<isNotEmpty prepend=" AND " property="applyNameLike">apply_name LIKE '$applyNameLike$%'</isNotEmpty>
				</dynamic>
			<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
	
	<select id="queryIntegratedQe"  parameterClass="hashmap" resultClass="hashmap">
		SELECT 
			apply_id as "applyId",
			apply_date as "applyDate",
			serial_num as "serialNum",
			apply_name as "applyName",
			patent_type as "patentType",
			first_dept_code as "firstDeptCode",
			first_dept_name as "firstDeptName",
			jsbh as "jsbh",
			bgbh as "bgbh",
			gldw_code as "gldwCode",
			gldw_name as "gldwName",
			patent_status as "patentStatus",
			flow_status as "flowStatus",
			flzt as "flzt",
			sqrq as "sqrq",
			slrq as "slrq",
			patent_no as "patentNo",
			jpg_flag as "jpgFlag",
			jpg_xh as "jpgXh",
			jpg_gw as "jpgGw",
			jpg_team as "jpgTeam",
			isbigxm as "isbigxm",
			fml as "fml",
			fmr_code as "fmrCode",
			lxr_code as "lxrCode",
			lxr_name as "lxrName",
			lxr_phone as "lxrPhone",
			lxr_email as "lxrEmail",
			lxr_mobile as "lxrMobile",
			zgbm_nc as "extra1",
			sws_name as "swsGuid",
			zzrq as "zzrq"
		FROM(
			SELECT 
				distinct 
					A.apply_id as apply_id,
					A.apply_date as apply_date,
					B.serial_num as serial_num,
					case when B.apply_name is not null then B.apply_name else A.apply_name end as apply_name,
					B.patent_type as patent_type,
					B.first_dept_code as first_dept_code,
					B.first_dept_name as first_dept_name,
					B.jsbh as jsbh,
					B.bgbh as bgbh,
					B.gldw_code as gldw_code,
					B.gldw_name as gldw_name,
					B.patent_status as patent_status,
					B.flow_status as flow_status,
					B.flzt as flzt,
					B.sqrq as sqrq,
					B.slrq as slrq,
					B.patent_no as patent_no,
					B.jpg_flag as jpg_flag,
					B.jpg_xh as jpg_xh,
					B.jpg_gw as jpg_gw,
					B.jpg_team as jpg_team,
					B.isbigxm as isbigxm,
					B.fml as fml,
					B.fmr_code as fmr_code,
					B.lxr_code as lxr_code,
					B.lxr_name as lxr_name,
					B.lxr_phone as lxr_phone,
					B.lxr_email as lxr_email,
					B.lxr_mobile as lxr_mobile,
					A.zgbm_nc as zgbm_nc,
					S.sws_name as sws_name,
					B.zzrq as zzrq
				FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO A 
			LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO W ON A.APPLY_ID = W.BUSINESS_ID
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO B ON B.APPLY_ID = A.APPLY_ID
			LEFT JOIN ${platSchema}.TEWPT00 T ON W.FLOW_ID = T.PROCESS_INSTANCE_ID 
			LEFT JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_SWSINFO S ON S.SWS_ID = B.SWS_GUID 
				WHERE 1=1
				<dynamic prepend=" AND ">
					<isNotEmpty prepend=" AND " property="swsCheck">(S.SWS_NAME &lt;&gt; '合作方委托' OR S.SWS_NAME IS NULL)</isNotEmpty>
					<isNotEmpty prepend=" AND " property="gldwCode">B.GLDW_CODE =  #gldwCode#</isNotEmpty>
					<isNotEmpty prepend=" AND " property="completerId">T.COMPLETER_ID =  #completerId#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="assigneeId">T.ASSIGNEE_ID =  #assigneeId#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="flowState">W.FLOW_STATE =  #flowState#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="isvalid">B.ISVALID =  #isvalid#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="flztIn">B.FLZT IN ($flztIn$)</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="serialNumLike">A.SERIAL_NUM LIKE  '$serialNumLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="patentType">B.PATENT_TYPE =  #patentType#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="bgbhLike">B.BGBH LIKE  '$bgbhLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="jsbhLike">B.JSBH LIKE  '$jsbhLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="patentNoLike">B.PATENT_NO LIKE  '$patentNoLike$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="patentStatus">B.PATENT_STATUS =  #patentStatus#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="qs">B.QS =  #qs#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="firstDeptCode">A.FIRST_DEPT_PATH LIKE  '%$firstDeptCode$%'</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="fromType">A.FROM_TYPE =  #fromType#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="fromNo">A.FROM_NO =  #fromNo#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="fromName">A.FROM_NAME =  #fromName#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="nf">SUBSTR(A.APPLY_DATE,1,4) =  #nf#</isNotEmpty>
		  			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
				</dynamic>
			) temp	
				<dynamic prepend=" WHERE ">
					<isNotEmpty prepend=" AND " property="applyNameLike">apply_name LIKE '$applyNameLike$%'</isNotEmpty>
				</dynamic>
			<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>


	<update id="updateArchive"  parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_PATENT_INFO
		SET ISGD_ARCHIVE = null ,ARCHIVE_GUID = null
		where ARCHIVE_GUID=#archiveGuid#
	</update>
	<update id="delPart"  parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_PATENT_INFO
		SET ISGD_ARCHIVE = null ,ARCHIVE_GUID = null
		where PATENT_ID in
		<iterate open="(" close=")" property="patentIds" conjunction=",">
			#patentIds[]#
		</iterate>
	</update>


	<select id="getZlFmCount"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
		SUM(CASE WHEN PATENT_TYPE ='FM' THEN 1 ELSE 0 END ) AS "fmZs",
		SUM(CASE WHEN PATENT_TYPE ='SYXX' THEN 1 ELSE 0 END ) AS "syxxZs",
		SUM(CASE WHEN PATENT_TYPE IN('FM','SYXX') THEN 1 ELSE 0 END ) AS "zs"
		FROM ${zzzcSchema}.T_KIZL_PATENT_INFO
		<dynamic prepend=" WHERE ">
			<isNotEmpty prepend=" AND " property="isValid">ISVALID =  #isValid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsGuid">SWS_GUID =  #swsGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>


</sqlMap>