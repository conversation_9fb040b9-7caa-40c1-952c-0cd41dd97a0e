<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlPatentArchive">
	<typeAlias alias="tKIZLPatentArchive" type="com.baosight.bscdkj.common.ki.domain.TkizlPatentArchive"/>
	<select id="load" parameterClass="string" resultClass="tKIZLPatentArchive">
		SELECT 
				ARCHIVE_ID as "archiveId" ,		
				PATENT_ID as "patentId" ,		
				SQRQ_START as "sqrqStart" ,		
				SQRQ_END as "sqrqEnd" ,		
				ARCHIVE_DATE as "archiveDate" ,		
				EXTRA1 as "extra1" ,		
				EXTRA2 as "extra2" ,		
				EXTRA3 as "extra3" ,		
				EXTRA<PERSON> as "extra4" ,		
				<PERSON>XT<PERSON><PERSON> as "extra5" ,		
				DEL_STATUS as "delStatus" ,		
				CREATE_USER_LABEL as "createUserLabel" ,		
				CREATE_DATE as "createDate" ,		
				UPDATE_USER_LABEL as "updateUserLabel" ,		
				UPDATE_DATE as "updateDate" ,		
				DELETE_USER_LABEL as "deleteUserLabel" ,		
				DELETE_DATE as "deleteDate" ,		
				RECORD_VERSION as "recordVersion" 		
				FROM ${zzzcSchema}.T_KIZL_PATENT_ARCHIVE
		WHERE   ARCHIVE_ID=#value# 				
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tKIZLPatentArchive">
		SELECT
				ARCHIVE_ID  as "archiveId" ,		
				PATENT_ID  as "patentId" ,		
				SQRQ_START  as "sqrqStart" ,		
				SQRQ_END  as "sqrqEnd" ,		
				ARCHIVE_DATE  as "archiveDate" ,		
				EXTRA1  as "extra1" ,		
				EXTRA2  as "extra2" ,		
				EXTRA3  as "extra3" ,		
				EXTRA4  as "extra4" ,		
				EXTRA5  as "extra5" ,		
				DEL_STATUS  as "delStatus" ,		
				CREATE_USER_LABEL  as "createUserLabel" ,		
				CREATE_DATE  as "createDate" ,		
				UPDATE_USER_LABEL  as "updateUserLabel" ,		
				UPDATE_DATE  as "updateDate" ,		
				DELETE_USER_LABEL  as "deleteUserLabel" ,		
				DELETE_DATE  as "deleteDate" ,		
				RECORD_VERSION  as "recordVersion" 		
				FROM ${zzzcSchema}.T_KIZL_PATENT_ARCHIVE
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="archiveId">ARCHIVE_ID =  #archiveId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqrqStart">SQRQ_START =  #sqrqStart#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqrqEnd">SQRQ_END =  #sqrqEnd#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="archiveDate">ARCHIVE_DATE =  #archiveDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KIZL_PATENT_ARCHIVE 
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="archiveId">ARCHIVE_ID =  #archiveId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqrqStart">SQRQ_START =  #sqrqStart#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqrqEnd">SQRQ_END =  #sqrqEnd#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="archiveDate">ARCHIVE_DATE =  #archiveDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tKIZLPatentArchive">
		INSERT INTO ${zzzcSchema}.T_KIZL_PATENT_ARCHIVE ( 
		<dynamic prepend=" ">
					<isNotNull prepend=", " property="archiveId">ARCHIVE_ID </isNotNull>
					<isNotNull prepend=", " property="patentId">PATENT_ID </isNotNull>
					<isNotNull prepend=", " property="sqrqStart">SQRQ_START </isNotNull>
					<isNotNull prepend=", " property="sqrqEnd">SQRQ_END </isNotNull>
					<isNotNull prepend=", " property="archiveDate">ARCHIVE_DATE </isNotNull>
					<isNotNull prepend=", " property="extra1">EXTRA1 </isNotNull>
					<isNotNull prepend=", " property="extra2">EXTRA2 </isNotNull>
					<isNotNull prepend=", " property="extra3">EXTRA3 </isNotNull>
					<isNotNull prepend=", " property="extra4">EXTRA4 </isNotNull>
					<isNotNull prepend=", " property="extra5">EXTRA5 </isNotNull>
					<isNotNull prepend=", " property="delStatus">DEL_STATUS </isNotNull>
					<isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="createDate">CREATE_DATE </isNotNull>
					<isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="updateDate">UPDATE_DATE </isNotNull>
					<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="deleteDate">DELETE_DATE </isNotNull>
					<isNotNull prepend=", " property="recordVersion">RECORD_VERSION </isNotNull>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotNull prepend=", " property="archiveId">#archiveId#</isNotNull>
			<isNotNull prepend=", " property="patentId">#patentId#</isNotNull>
			<isNotNull prepend=", " property="sqrqStart">#sqrqStart#</isNotNull>
			<isNotNull prepend=", " property="sqrqEnd">#sqrqEnd#</isNotNull>
			<isNotNull prepend=", " property="archiveDate">#archiveDate#</isNotNull>
			<isNotNull prepend=", " property="extra1">#extra1#</isNotNull>
			<isNotNull prepend=", " property="extra2">#extra2#</isNotNull>
			<isNotNull prepend=", " property="extra3">#extra3#</isNotNull>
			<isNotNull prepend=", " property="extra4">#extra4#</isNotNull>
			<isNotNull prepend=", " property="extra5">#extra5#</isNotNull>
			<isNotNull prepend=", " property="delStatus">#delStatus#</isNotNull>
			<isNotNull prepend=", " property="createUserLabel">#createUserLabel#</isNotNull>
			<isNotNull prepend=", " property="createDate">
			   <isNotEmpty property="createDate">#createDate#</isNotEmpty>
			   <isEmpty property="createDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="updateUserLabel">#updateUserLabel#</isNotNull>
			<isNotNull prepend=", " property="updateDate">
			   <isNotEmpty property="updateDate">#updateDate#</isNotEmpty>
			   <isEmpty property="updateDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">#deleteUserLabel#</isNotNull>
			<isNotNull prepend=", " property="deleteDate">
			   <isNotEmpty property="deleteDate">#deleteDate#</isNotEmpty>
			   <isEmpty property="deleteDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="recordVersion">#recordVersion#</isNotNull>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_PATENT_ARCHIVE
		WHERE 		ARCHIVE_ID=#value# 	
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_PATENT_ARCHIVE
		WHERE 
		<dynamic prepend=" ">
			<isNotNull prepend=" AND " property="archiveId">ARCHIVE_ID = #archiveId#</isNotNull>
			<isNotNull prepend=" AND " property="patentId">PATENT_ID = #patentId#</isNotNull>
			<isNotNull prepend=" AND " property="sqrqStart">SQRQ_START = #sqrqStart#</isNotNull>
			<isNotNull prepend=" AND " property="sqrqEnd">SQRQ_END = #sqrqEnd#</isNotNull>
			<isNotNull prepend=" AND " property="archiveDate">ARCHIVE_DATE = #archiveDate#</isNotNull>
			<isNotNull prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>	
	</delete>

	<update id="update" parameterClass="tKIZLPatentArchive">
		UPDATE  ${zzzcSchema}.T_KIZL_PATENT_ARCHIVE	
		SET 
		<dynamic prepend=" ">
		<isNotNull prepend="," property="archiveId">ARCHIVE_ID = #archiveId#</isNotNull>
		<isNotNull prepend="," property="patentId">PATENT_ID = #patentId#</isNotNull>
		<isNotNull prepend="," property="sqrqStart">SQRQ_START = #sqrqStart#</isNotNull>
		<isNotNull prepend="," property="sqrqEnd">SQRQ_END = #sqrqEnd#</isNotNull>
		<isNotNull prepend="," property="archiveDate">ARCHIVE_DATE = #archiveDate#</isNotNull>
		<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
		<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
		<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
		<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
		<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
		<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
		<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
		<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
		<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
		<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
		<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		ARCHIVE_ID=#archiveId# 			</update>
	
	<update id="updatewithnull" parameterClass="tKIZLPatentArchive">
		UPDATE ${zzzcSchema}.T_KIZL_PATENT_ARCHIVE	
		SET 
		<dynamic prepend=" ">
	    <isNotNull prepend=", " property="archiveId">ARCHIVE_ID = #archiveId#</isNotNull>
	    <isNull prepend=", " property="archiveId">ARCHIVE_ID = NULL</isNull>
	    <isNotNull prepend=", " property="patentId">PATENT_ID = #patentId#</isNotNull>
	    <isNull prepend=", " property="patentId">PATENT_ID = NULL</isNull>
	    <isNotNull prepend=", " property="sqrqStart">SQRQ_START = #sqrqStart#</isNotNull>
	    <isNull prepend=", " property="sqrqStart">SQRQ_START = NULL</isNull>
	    <isNotNull prepend=", " property="sqrqEnd">SQRQ_END = #sqrqEnd#</isNotNull>
	    <isNull prepend=", " property="sqrqEnd">SQRQ_END = NULL</isNull>
	    <isNotNull prepend=", " property="archiveDate">ARCHIVE_DATE = #archiveDate#</isNotNull>
	    <isNull prepend=", " property="archiveDate">ARCHIVE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="extra1">EXTRA1 = #extra1#</isNotNull>
	    <isNull prepend=", " property="extra1">EXTRA1 = NULL</isNull>
	    <isNotNull prepend=", " property="extra2">EXTRA2 = #extra2#</isNotNull>
	    <isNull prepend=", " property="extra2">EXTRA2 = NULL</isNull>
	    <isNotNull prepend=", " property="extra3">EXTRA3 = #extra3#</isNotNull>
	    <isNull prepend=", " property="extra3">EXTRA3 = NULL</isNull>
	    <isNotNull prepend=", " property="extra4">EXTRA4 = #extra4#</isNotNull>
	    <isNull prepend=", " property="extra4">EXTRA4 = NULL</isNull>
	    <isNotNull prepend=", " property="extra5">EXTRA5 = #extra5#</isNotNull>
	    <isNull prepend=", " property="extra5">EXTRA5 = NULL</isNull>
	    <isNotNull prepend=", " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
	    <isNull prepend=", " property="delStatus">DEL_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
	    <isNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="createDate">CREATE_DATE = #createDate#</isNotNull>
	    <isNull prepend=", " property="createDate">CREATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
	    <isNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNull prepend=", " property="updateDate">UPDATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
	    <isNull prepend=", " property="recordVersion">RECORD_VERSION = NULL</isNull>
		</dynamic>
		WHERE 		ARCHIVE_ID=#archiveId# 			</update>
	
	<update id="updateByC" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_PATENT_ARCHIVE	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="archiveId">ARCHIVE_ID = #archiveId#</isNotNull>
				<isNotNull prepend="," property="patentId">PATENT_ID = #patentId#</isNotNull>
				<isNotNull prepend="," property="sqrqStart">SQRQ_START = #sqrqStart#</isNotNull>
				<isNotNull prepend="," property="sqrqEnd">SQRQ_END = #sqrqEnd#</isNotNull>
				<isNotNull prepend="," property="archiveDate">ARCHIVE_DATE = #archiveDate#</isNotNull>
				<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
				<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
				<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
				<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
				<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
				<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
				<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
				<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
				<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
				<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
				<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
			</dynamic>
		<dynamic prepend=" WHERE ">
		 			<isNotNull prepend=" AND " property="archiveIdOld">ARCHIVE_ID = #archiveIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="patentIdOld">PATENT_ID = #patentIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="sqrqStartOld">SQRQ_START = #sqrqStartOld#</isNotNull>
			<isNotNull prepend=" AND " property="sqrqEndOld">SQRQ_END = #sqrqEndOld#</isNotNull>
			<isNotNull prepend=" AND " property="archiveDateOld">ARCHIVE_DATE = #archiveDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="extra1Old">EXTRA1 = #extra1Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra2Old">EXTRA2 = #extra2Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra3Old">EXTRA3 = #extra3Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra4Old">EXTRA4 = #extra4Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra5Old">EXTRA5 = #extra5Old#</isNotNull>
			<isNotNull prepend=" AND " property="delStatusOld">DEL_STATUS = #delStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabelOld">CREATE_USER_LABEL = #createUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="createDateOld">CREATE_DATE = #createDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabelOld">UPDATE_USER_LABEL = #updateUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateDateOld">UPDATE_DATE = #updateDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabelOld">DELETE_USER_LABEL = #deleteUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDateOld">DELETE_DATE = #deleteDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersionOld">RECORD_VERSION = #recordVersionOld#</isNotNull>
			<isNotNull prepend=" AND " property="dynSqlOld"> $dynSqlOld$ </isNotNull>
		</dynamic>
	</update>
	
	<update id="updateNull" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_PATENT_ARCHIVE	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="archiveId">ARCHIVE_ID = #archiveId#</isNotNull>
			<isNotNull prepend="," property="patentId">PATENT_ID = #patentId#</isNotNull>
			<isNotNull prepend="," property="sqrqStart">SQRQ_START = #sqrqStart#</isNotNull>
			<isNotNull prepend="," property="sqrqEnd">SQRQ_END = #sqrqEnd#</isNotNull>
			<isNotNull prepend="," property="archiveDate">ARCHIVE_DATE = #archiveDate#</isNotNull>
			<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			<isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
			<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		ARCHIVE_ID=#archiveId# 			</update>

	<select id="queryArchiveDetils" parameterClass="string" resultClass="hashmap">
		SELECT
		A.PATENT_ID as "patentId",
		A.ARCHIVE_GUID as "archiveGuid",
		A.BGBH  as "bgbh",
		A.APPLY_ID  as "applyId",
		A.ZLH as "zlh",
		A.APPLY_NAME  as "applyName",
		A.PATENT_NO as "patentNo" ,
		A.SLRQ as "slrq",
		A.SQRQ as "sqrq",
		A.FML as "fml",
		B.APPLY_DATE AS "applyDate",
		B.LXR_NAME AS "lxrName",
		C.LEGAL_NAME AS "legalName"
		FROM ZZZC.T_KIZL_APPLY_SQR C
		left join ZZZC.T_KIZL_APPLY_BASEINFO B
		ON C.APPLY_ID =B.APPLY_ID
		LEFT JOIN ZZZC.T_KIZL_PATENT_INFO A ON A.APPLY_ID =B.APPLY_ID
		WHERE A.ARCHIVE_GUID =#archiveId#
	</select>
	<select id="queryPatentInfo" parameterClass="hashmap" resultClass="hashmap">
		SELECT
		A.PATENT_ID as "patentId",
		A.ARCHIVE_GUID as "archiveGuid",
		A.BGBH  as "bgbh",
		A.APPLY_NAME  as "applyName",
		A.PATENT_NO as "patentNo" ,
		A.SLRQ as "slrq"
		FROM ZZZC.T_KIZL_PATENT_INFO A
		<dynamic prepend=" WHERE ">
			<isNotEmpty prepend=" AND " property="gldwCode">A.GLDW_CODE like  '%$gldwCode$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqrqStart">A.SQRQ >=#sqrqStart#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqrqEnd">A.SQRQ <![CDATA[<=]]>  #sqrqEnd#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isgdArchive">(A.ISGD_ARCHIVE <![CDATA[<>]]> #isgdArchive# OR  A.ISGD_ARCHIVE IS NULL)</isNotEmpty>
			<isEmpty prepend=" AND " property="ISVALID">A.ISVALID = '1'</isEmpty>
			<isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
		</dynamic>
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
</sqlMap>