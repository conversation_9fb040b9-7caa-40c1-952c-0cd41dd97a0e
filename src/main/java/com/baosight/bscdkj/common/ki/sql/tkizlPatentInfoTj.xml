<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlPatentInfoTj">
	<typeAlias alias="tKIZLPatentInfo" type="com.baosight.bscdkj.common.ki.domain.TkizlPatentInfo"/>
	<!-- 知识产权当年实绩-主报表 -->
	<select id="queryZscqdnsjC"  parameterClass="hashmap" resultClass="hashmap">
		SELECT 
			tmp.xh AS "xh",
			tmp.deptId AS "deptId",
			tmp.shortName AS "shortName",
			SUM(tmp.SQS)/100 AS "SQS",
			SUM(tmp.FMS)/100 AS "FMS",
			SUM(tmp.YQS)/100 AS "YQS",
			SUM(tmp.FMYQS)/100 AS "FMYQS",
			SUM(tmp.LJYQS)/100 AS "LJYQS",
			SUM(tmp.LJFMYQS)/100 AS "LJFMYQS",
			SUM(tmp.JSMMRD) AS "JSMMRD"
			FROM 
			(
			SELECT 
			'01' AS xh, 
			'BGTAEC00' AS deptId,
			'总部研究院' AS shortName, 
			SUM(CASE WHEN TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS SQS,
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMS, 
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS YQS,
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMYQS, 
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' THEN ryxx.EXTRA2 ELSE 0 END) AS LJYQS,
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS LJFMYQS,
			0 AS JSMMRD 
			FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX ryxx 
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO T ON ryxx.APPLY_ID = T.APPLY_ID AND T.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO TP ON T.APPLY_ID = TP.APPLY_ID AND TP.DEL_STATUS='0'
			WHERE ryxx.DEL_STATUS='0' AND ryxx.EXTRA1 = '1' AND ryxx.CODE_PATH LIKE '%BGTAEC00%' 
			AND T.GLDW_CODE != 'BGPV' AND TP.GLDW_CODE != 'BGPV' 
			UNION ALL
			SELECT <!-- 技术秘密认定数  总部研究院 -->
			'01' AS xh,'BGTAEC00' AS deptId, '总部研究院' AS shortName, 0 AS SQS, 0 AS FMS, 0 AS YQS, 0 AS FMYQS,
			0 AS LJYQS, 0 AS LJFMYQS, SUM(ryxx.EXTRA1)/100 AS JSMMRD 
			FROM ${zzzcSchema}.T_KYMM_TECH_MEMBER ryxx 
			LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY T ON ryxx.TECHNOLOGY_ID = T.TECHNOLOGY_ID AND T.DEL_STATUS='0'
			WHERE ryxx.EXTRA2 LIKE '/BSTA/BGTA/BGTA00/BGTAEC00%' AND T.STATUS = 'identified' AND T.CONFIRM_TIME &gt;=  '$startDate$' AND T.CONFIRM_TIME &lt;= '$endDate$'
			UNION ALL
			SELECT 
			'02' AS xh, 
			'BGTA00' AS deptId,
			'总部直属厂部' AS shortName, 
			SUM(CASE WHEN TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS SQS,
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMS, 
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS YQS,
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMYQS, 
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' THEN ryxx.EXTRA2 ELSE 0 END) AS LJYQS,
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS LJFMYQS, 
			0 AS JSMMRD 
			FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX ryxx 
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO T ON ryxx.APPLY_ID = T.APPLY_ID AND T.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO TP ON T.APPLY_ID = TP.APPLY_ID AND TP.DEL_STATUS='0'
			WHERE ryxx.DEL_STATUS='0' AND ryxx.EXTRA1 = '1' AND (ryxx.CODE_PATH LIKE '%BGTA00%' AND ryxx.CODE_PATH NOT LIKE '%BGTAEC00%') 
			AND T.GLDW_CODE != 'BGPV' AND TP.GLDW_CODE != 'BGPV' 
			UNION ALL
			SELECT <!-- 技术秘密认定数  总部直属厂部-->
			'02' AS xh,'BGTA00' AS deptId, '总部直属厂部' AS shortName, 0 AS SQS, 0 AS FMS, 0 AS YQS, 0 AS FMYQS,
			0 AS LJYQS, 0 AS LJFMYQS, SUM(ryxx.EXTRA1)/100 AS JSMMRD 
			FROM ${zzzcSchema}.T_KYMM_TECH_MEMBER ryxx 
			LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY T ON ryxx.TECHNOLOGY_ID = T.TECHNOLOGY_ID AND T.DEL_STATUS='0'
			WHERE (ryxx.EXTRA2 LIKE '/BSTA/BGTA/BGTA00%' AND ryxx.EXTRA2 NOT LIKE '/BSTA/BGTA/BGTA00/BGTAEC00%') AND T.STATUS = 'identified' AND T.CONFIRM_TIME &gt;=  '$startDate$' AND T.CONFIRM_TIME &lt;= '$endDate$' 
			UNION ALL 
			SELECT <!-- 技术秘密认定数  梅钢/BSTA/BGTA/BGTM/BGTM00 -->
			'05' AS xh, 'BGTM00' AS deptId, '梅钢公司' AS shortName, 0 AS SQS, 0 AS FMS, 0 AS YQS, 0 AS FMYQS,
			0 AS LJYQS, 0 AS LJFMYQS, SUM(ryxx.EXTRA1)/100 AS JSMMRD 
			FROM ${zzzcSchema}.T_KYMM_TECH_MEMBER ryxx 
			LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY T ON ryxx.TECHNOLOGY_ID = T.TECHNOLOGY_ID AND T.DEL_STATUS='0'
			WHERE ryxx.EXTRA2 LIKE '/BSTA/BGTA/BGTM/BGTM00%' AND T.STATUS = 'identified' AND T.CONFIRM_TIME &gt;=  '$startDate$' AND T.CONFIRM_TIME &lt;= '$endDate$'
			UNION ALL
			SELECT <!-- 化工公司 -->
			'07' AS xh, 
			'BGSH' AS deptId,
			'化工公司' AS shortName, 
			SUM(CASE WHEN TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS SQS,
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMS, 
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS YQS,
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMYQS, 
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' THEN ryxx.EXTRA2 ELSE 0 END) AS LJYQS,
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS LJFMYQS,
			0 AS JSMMRD 
			FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG ryxx 
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO_HG TP ON ryxx.PATENT_ID = TP.PATENT_ID AND TP.DEL_STATUS='0'
			WHERE ryxx.EXTRA1 = '1' AND ryxx.DEL_STATUS='0' 
			UNION ALL
			SELECT 
			A.XH AS xh,
			A.DEPT_CODE AS deptId,
			CASE WHEN A.SHORT_NAME = '总部' THEN '总部直属厂部' WHEN A.SHORT_NAME = '研究院' THEN '总部研究院' ELSE A.SHORT_NAME END AS shortName,
			SUM(CASE WHEN TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS SQS,
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMS, 
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS YQS, 
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS FMYQS, 
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' THEN ryxx.EXTRA2 ELSE 0 END) AS LJYQS, 
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS LJFMYQS,  
			0 AS JSMMRD 
			FROM ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME A 
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX ryxx ON ryxx.DEPTCBBM = A.DEPT_CODE
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO T ON ryxx.APPLY_ID = T.APPLY_ID AND T.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO TP ON T.APPLY_ID = TP.APPLY_ID AND T.DEL_STATUS='0'
			WHERE ryxx.DEL_STATUS='0' AND ryxx.EXTRA1 = '1' AND A.SHORT_TYPE = '2' 
			AND T.GLDW_CODE != 'BGPV' AND TP.GLDW_CODE != 'BGPV' 
			AND ryxx.CODE_PATH NOT like '%BGTM%'
			GROUP BY A.XH, A.DEPT_CODE, A.SHORT_NAME
			UNION ALL
			SELECT <!-- 查梅钢 -->
			'05' AS xh,
			'BGTM00' AS deptId,
			'梅钢公司' AS shortName,
			SUM(CASE WHEN TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS SQS,
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMS, 
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS YQS, 
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS FMYQS, 
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' THEN ryxx.EXTRA2 ELSE 0 END) AS LJYQS, 
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS LJFMYQS,  
			0 AS JSMMRD 
			FROM (
			SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%QT%' AND MSPATH NOT LIKE '%,%'
			UNION ALL
			SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' AND MSPATH LIKE '%QT%'
			UNION ALL
			SELECT 
				DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
				FROM 
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%MG%' AND MSPATH NOT LIKE '%,%'
				UNION ALL
				SELECT 
				DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
				FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
				AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BGTM00' 
				UNION ALL
				SELECT 
				DEPT_CODE,NAME_PATH,RYXX_ID,'BGTM00' AS DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
				FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
				AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BSTM' ) ryxx 
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO T ON ryxx.APPLY_ID = T.APPLY_ID AND T.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO TP ON T.APPLY_ID = TP.APPLY_ID AND T.DEL_STATUS='0'
			WHERE ryxx.DEPTCBBM NOT LIKE '%BSTM%' AND T.GLDW_CODE != 'BGPV' AND TP.GLDW_CODE != 'BGPV' 
			UNION ALL
			SELECT <!-- 技术秘密认定数  其他-->
			A.XH AS xh,
			A.DEPT_CODE AS deptId,
			CASE WHEN A.SHORT_NAME = '总部' THEN '总部直属厂部' WHEN A.SHORT_NAME = '研究院' THEN '总部研究院' ELSE A.SHORT_NAME END AS shortName,
			0 AS SQS, 0 AS FMS, 0 AS YQS, 0 AS FMYQS, 0 AS LJYQS, 0 AS LJFMYQS,
			SUM(ryxx.EXTRA1)/100 AS JSMMRD 
			FROM ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME A 
			LEFT JOIN ${zzzcSchema}.T_KYMM_TECH_MEMBER ryxx ON ryxx.EXTRA2 LIKE '/BSTA/BGTA/' || A.DEPT_CODE || '%'
			LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY T ON ryxx.TECHNOLOGY_ID = T.TECHNOLOGY_ID AND T.DEL_STATUS='0'
			WHERE T.STATUS = 'identified' AND T.CONFIRM_TIME &gt;=  '$startDate$' AND T.CONFIRM_TIME &lt;= '$endDate$' AND A.SHORT_TYPE = '2'
			GROUP BY A.XH, A.DEPT_CODE, A.SHORT_NAME
			UNION ALL
			SELECT <!-- 化工公司 -->
			'999' AS xh, 
			'ALL' AS deptId,
			'合计' AS shortName, 
			SUM(CASE WHEN TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS SQS,
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMS, 
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS YQS,
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS FMYQS, 
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' THEN ryxx.EXTRA2 ELSE 0 END) AS LJYQS,
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS LJFMYQS, 
			0 AS JSMMRD 
			FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG ryxx 
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO_HG TP ON ryxx.PATENT_ID = TP.PATENT_ID AND TP.DEL_STATUS='0'
			WHERE ryxx.EXTRA1 = '1' AND ryxx.DEL_STATUS='0' 
			UNION ALL
			SELECT 
			'999' AS xh, 
			'ALL' AS deptId,
			'合计' AS shortName, 
			SUM(CASE WHEN TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS SQS,
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMS, 
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS YQS,
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS FMYQS, 
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' THEN ryxx.EXTRA2 ELSE 0 END) AS LJYQS,
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS LJFMYQS, 
			0 AS JSMMRD 
			FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX ryxx 
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO T ON ryxx.APPLY_ID = T.APPLY_ID AND T.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO TP ON T.APPLY_ID = TP.APPLY_ID AND TP.DEL_STATUS='0'
			WHERE ryxx.DEL_STATUS='0' AND ryxx.EXTRA1 = '1' AND ryxx.CODE_PATH LIKE '%BGTAEC00%' 
			AND T.GLDW_CODE != 'BGPV' AND TP.GLDW_CODE != 'BGPV' 
			UNION ALL
			SELECT <!-- 技术秘密认定数  合计  总部研究院-->
			'999' AS xh,'ALL' AS deptId, '合计' AS shortName, 0 AS SQS, 0 AS FMS, 0 AS YQS, 0 AS FMYQS,
			0 AS LJYQS, 0 AS LJFMYQS, SUM(ryxx.EXTRA1)/100 AS JSMMRD 
			FROM ${zzzcSchema}.T_KYMM_TECH_MEMBER ryxx 
			LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY T ON ryxx.TECHNOLOGY_ID = T.TECHNOLOGY_ID AND T.DEL_STATUS='0'
			WHERE ryxx.EXTRA2 LIKE '/BSTA/BGTA/BGTA00/BGTAEC00%' AND T.STATUS = 'identified' AND T.CONFIRM_TIME &gt;=  '$startDate$' AND T.CONFIRM_TIME &lt;= '$endDate$'
			UNION ALL
			SELECT 
			'999' AS xh, 
			'ALL' AS deptId,
			'合计' AS shortName, 
			SUM(CASE WHEN TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS SQS,
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMS, 
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS YQS,
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMYQS, 
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' THEN ryxx.EXTRA2 ELSE 0 END) AS LJYQS,
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS LJFMYQS,
			0 AS JSMMRD 
			FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX ryxx 
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO T ON ryxx.APPLY_ID = T.APPLY_ID AND T.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO TP ON T.APPLY_ID = TP.APPLY_ID AND TP.DEL_STATUS='0'
			WHERE ryxx.DEL_STATUS='0' AND ryxx.EXTRA1 = '1' AND (ryxx.CODE_PATH LIKE '%BGTA00%' AND ryxx.CODE_PATH NOT LIKE '%BGTAEC00%') 
			AND T.GLDW_CODE != 'BGPV' AND TP.GLDW_CODE != 'BGPV' 
			UNION ALL
			SELECT <!-- 技术秘密认定数  合计  总部直属厂部-->
			'999' AS xh,'ALL' AS deptId, '合计' AS shortName, 0 AS SQS, 0 AS FMS, 0 AS YQS, 0 AS FMYQS,
			0 AS LJYQS, 0 AS LJFMYQS, SUM(ryxx.EXTRA1)/100 AS JSMMRD 
			FROM ${zzzcSchema}.T_KYMM_TECH_MEMBER ryxx 
			LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY T ON ryxx.TECHNOLOGY_ID = T.TECHNOLOGY_ID AND T.DEL_STATUS='0'
			WHERE (ryxx.EXTRA2 LIKE '/BSTA/BGTA/BGTA00%' AND ryxx.EXTRA2 NOT LIKE '%BGTAEC00%') AND T.STATUS = 'identified' AND T.CONFIRM_TIME &gt;=  '$startDate$' AND T.CONFIRM_TIME &lt;= '$endDate$'
			UNION ALL 
			SELECT <!-- 技术秘密认定数  合计  梅钢/BSTA/BGTA/BGTM/BGTM00 -->
			'999' AS xh,'ALL' AS deptId, '合计' AS shortName, 0 AS SQS, 0 AS FMS, 0 AS YQS, 0 AS FMYQS,
			0 AS LJYQS, 0 AS LJFMYQS, SUM(ryxx.EXTRA1)/100 AS JSMMRD 
			FROM ${zzzcSchema}.T_KYMM_TECH_MEMBER ryxx 
			LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY T ON ryxx.TECHNOLOGY_ID = T.TECHNOLOGY_ID AND T.DEL_STATUS='0'
			WHERE ryxx.EXTRA2 LIKE '/BSTA/BGTA/BGTM/BGTM00%' AND T.STATUS = 'identified' AND T.CONFIRM_TIME &gt;=  '$startDate$' AND T.CONFIRM_TIME &lt;= '$endDate$'
			UNION ALL 
			SELECT 
			'999' AS xh,
			'ALL' AS deptId,
			'合计' AS shortName,
			SUM(CASE WHEN TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS SQS, 
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMS, 
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS YQS, 
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMYQS, 
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' THEN ryxx.EXTRA2 ELSE 0 END) AS LJYQS,
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS LJFMYQS, 
			0 AS JSMMRD 
			FROM ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME A 
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX ryxx ON ryxx.DEPTCBBM = A.DEPT_CODE
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO T ON ryxx.APPLY_ID = T.APPLY_ID AND T.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO TP ON T.APPLY_ID = TP.APPLY_ID AND TP.DEL_STATUS='0'
			WHERE ryxx.DEL_STATUS='0' AND ryxx.EXTRA1 = '1' AND A.SHORT_TYPE = '2' 
			AND T.GLDW_CODE != 'BGPV' AND TP.GLDW_CODE != 'BGPV' 
			AND ryxx.CODE_PATH NOT like '%BGTM%'
			UNION ALL
			SELECT <!-- 查梅钢 -->
			'999' AS xh,
			'ALL' AS deptId,
			'合计' AS shortName,
			SUM(CASE WHEN TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS SQS, 
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SLRQ &gt;=  '$startDate$' AND TP.SLRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMS, 
			SUM(CASE WHEN TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS YQS, 
			SUM(CASE WHEN TP.PATENT_TYPE = 'FM' AND TP.SQRQ &gt;=  '$startDate$' AND TP.SQRQ &lt;= '$endDate$' THEN ryxx.EXTRA2 ELSE 0 END) AS FMYQS, 
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' THEN ryxx.EXTRA2 ELSE 0 END) AS LJYQS,
			SUM(CASE WHEN TP.FLZT = '10' AND TP.ISVALID = '1' AND TP.PATENT_TYPE = 'FM' THEN ryxx.EXTRA2 ELSE 0 END) AS LJFMYQS, 
			0 AS JSMMRD 
			FROM (
			SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%QT%' AND MSPATH NOT LIKE '%,%'
			UNION ALL
			SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' AND MSPATH LIKE '%QT%'
			UNION ALL
			SELECT 
				DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
				FROM 
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%MG%' AND MSPATH NOT LIKE '%,%'
				UNION ALL
				SELECT 
				DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
				FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
				AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BGTM00' 
				UNION ALL
				SELECT 
				DEPT_CODE,NAME_PATH,RYXX_ID,'BGTM00' AS DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
				FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
				AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BSTM' ) ryxx 
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO T ON ryxx.APPLY_ID = T.APPLY_ID AND T.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO TP ON T.APPLY_ID = TP.APPLY_ID AND TP.DEL_STATUS='0'
			WHERE ryxx.DEPTCBBM NOT LIKE '%BSTM%' AND T.GLDW_CODE != 'BGPV' AND TP.GLDW_CODE != 'BGPV' 
			UNION ALL
			SELECT <!-- 技术秘密认定数  合计  其他-->
			'999' AS xh,
			'ALL' AS deptId,
			'合计' AS shortName,
			0 AS SQS, 0 AS FMS, 0 AS YQS, 0 AS FMYQS, 0 AS LJYQS, 0 AS LJFMYQS,
			SUM(ryxx.EXTRA1)/100 AS JSMMRD 
			FROM ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME A 
			LEFT JOIN ${zzzcSchema}.T_KYMM_TECH_MEMBER ryxx ON ryxx.EXTRA2 LIKE concat(concat('/BSTA/BGTA/',A.DEPT_CODE),'%')
			LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY T ON ryxx.TECHNOLOGY_ID = T.TECHNOLOGY_ID AND T.DEL_STATUS='0'
			WHERE T.STATUS = 'identified' AND T.CONFIRM_TIME &gt;=  '$startDate$' AND T.CONFIRM_TIME &lt;= '$endDate$' AND A.SHORT_TYPE = '2'
			) tmp
				GROUP BY tmp.xh, tmp.deptId, tmp.shortName
				ORDER BY tmp.xh ASC 
	</select>
	
	<!-- 知识产权当年实绩-专利明细 -->
	<select id="queryZscqdnsjmx"  parameterClass="hashmap" resultClass="hashmap">
		 SELECT tmp.applyId AS "applyId",
				MAX(tmp.jsbh) AS "jsbh",
				MAX(tmp.bgbh) AS "bgbh",
				MAX(tmp.patentNo) AS "patentNo",
				MAX(tmp.applyName) AS "applyName",
				SUM(tmp.gxxs) AS "gxxs"
		 FROM  (SELECT <!-- 不含总部研究院 -->
		 		DISTINCT
		 		b.APPLY_ID AS applyId,
				MAX(b.JSBH) AS jsbh,
				MAX(p.BGBH) AS bgbh,
				MAX(p.PATENT_NO) AS patentNo,
				CASE WHEN MAX(p.APPLY_NAME) IS NULL THEN MAX(b.APPLY_NAME) ELSE MAX(p.APPLY_NAME) END AS applyName,
				SUM(t.EXTRA2)/100 AS gxxs
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON t.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '2'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		AND t.CODE_PATH NOT like '%BGTM%'
		<isNotEmpty prepend=" AND " property="apply">
			p.SLRQ &gt;=  '$startDate$' AND p.SLRQ &lt;= '$endDate$'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="yearFlag">p.FLZT = '10' AND p.ISVALID = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auth">
			(p.SQRQ &gt;=  '$startDate$' AND p.SQRQ &lt;= '$endDate$')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			t.CODE_PATH like '%$deptCode$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">t.CODE_PATH NOT LIKE '%BGTAEC00%'</isNotEmpty>
		GROUP BY b.APPLY_ID
		UNION ALL <!-- 总部研究院 -->
		SELECT 
				DISTINCT
				b.APPLY_ID AS applyId,
				MAX(b.JSBH) AS jsbh,
				MAX(p.BGBH) AS bgbh,
				MAX(p.PATENT_NO) AS patentNo,
				CASE WHEN MAX(p.APPLY_NAME) IS NULL THEN MAX(b.APPLY_NAME) ELSE MAX(p.APPLY_NAME) END AS applyName,
				SUM(t.EXTRA2)/100 AS gxxs
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		AND t.CODE_PATH LIKE '%BGTA00%' AND t.CODE_PATH NOT like '%BGTM%'
		<isNotEmpty prepend=" AND " property="apply">
			p.SLRQ &gt;=  '$startDate$' AND p.SLRQ &lt;= '$endDate$'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="yearFlag">p.FLZT = '10' AND p.ISVALID = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auth">
			(p.SQRQ &gt;=  '$startDate$' AND p.SQRQ &lt;= '$endDate$')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			t.CODE_PATH like '%$deptCode$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">t.CODE_PATH NOT LIKE '%BGTAEC00%'</isNotEmpty>
		GROUP BY b.APPLY_ID 
		UNION ALL
		SELECT <!-- 化工公司 -->
				DISTINCT
		 		p.APPLY_ID AS applyId,
				MAX(p.JSBH) AS jsbh,
				MAX(p.BGBH) AS bgbh,
				MAX(p.PATENT_NO) AS patentNo,
				MAX(p.APPLY_NAME) AS applyName,
				SUM(t.EXTRA2)/100 AS gxxs
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG t
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO_HG p ON t.PATENT_ID = p.PATENT_ID AND p.DEL_STATUS='0'
		WHERE t.EXTRA1 = '1' AND t.DEL_STATUS='0' 
		<isNotEmpty prepend=" AND " property="apply">
			p.SLRQ &gt;=  '$startDate$' AND p.SLRQ &lt;= '$endDate$'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="yearFlag">p.FLZT = '10' AND p.ISVALID = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auth">
			(p.SQRQ &gt;=  '$startDate$' AND p.SQRQ &lt;= '$endDate$')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			t.CODE_PATH like '%$deptCode$%'
		</isNotEmpty>
		GROUP BY p.APPLY_ID
		UNION ALL <!-- 查梅钢 -->
		SELECT 
				DISTINCT
				b.APPLY_ID AS applyId,
				MAX(b.JSBH) AS jsbh,
				MAX(p.BGBH) AS bgbh,
				MAX(p.PATENT_NO) AS patentNo,
				CASE WHEN MAX(p.APPLY_NAME) IS NULL THEN MAX(b.APPLY_NAME) ELSE MAX(p.APPLY_NAME) END AS applyName,
				SUM(t.EXTRA2)/100 AS gxxs
		FROM (
		SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%QT%' AND MSPATH NOT LIKE '%,%'
			UNION ALL
		SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' AND MSPATH LIKE '%QT%'
			UNION ALL
		SELECT 
				DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
				FROM 
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%MG%' AND MSPATH NOT LIKE '%,%'
				UNION ALL
				SELECT 
				DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
				FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
				AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BGTM00' 
				UNION ALL
				SELECT 
				DEPT_CODE,NAME_PATH,RYXX_ID,'BGTM00' AS DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
				FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
				AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BSTM' ) t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		WHERE t.DEPTCBBM NOT LIKE '%BSTM%' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		<isNotEmpty prepend=" AND " property="apply">
			p.SLRQ &gt;=  '$startDate$' AND p.SLRQ &lt;= '$endDate$'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="yearFlag">p.FLZT = '10' AND p.ISVALID = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="auth">
			(p.SQRQ &gt;=  '$startDate$' AND p.SQRQ &lt;= '$endDate$')
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			t.CODE_PATH like '%$deptCode$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">t.CODE_PATH NOT LIKE '%BGTAEC00%'</isNotEmpty>
		GROUP BY b.APPLY_ID 
		) tmp
		GROUP BY tmp.applyId
		ORDER BY MAX(tmp.jsbh),MAX(tmp.bgbh),MAX(tmp.patentNo) ASC 
	</select>
	
	<!-- 知识产权当年实绩-技术秘密明细 -->
	<select id="queryJsmm"  parameterClass="hashmap" resultClass="hashmap">
		 SELECT tmp.technologyId AS "technologyId",
		 		MAX(tmp.confirmNum) AS "confirmNum",
				MAX(tmp.technologyName) AS "technologyName",
				SUM(tmp.gxxs) AS "gxxs"
		 FROM  (SELECT T.TECHNOLOGY_ID AS technologyId,
		 		MAX(T.CONFIRM_NUM) AS confirmNum,
				MAX(T.TECHNOLOGY_NAME) AS technologyName,
				SUM(ryxx.EXTRA1)/100 AS gxxs
		FROM ${zzzcSchema}.T_KYMM_TECH_MEMBER ryxx
		LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY T ON ryxx.TECHNOLOGY_ID = T.TECHNOLOGY_ID AND T.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME A ON ryxx.EXTRA2 LIKE concat(concat('/BSTA/BGTA/',A.DEPT_CODE),'%')
		WHERE T.STATUS = 'identified' AND T.CONFIRM_TIME &gt;=  '$startDate$' AND T.CONFIRM_TIME &lt;= '$endDate$' AND A.SHORT_TYPE = '2'
		<isNotEmpty prepend=" AND " property="deptCode">
			ryxx.EXTRA2 like '$deptCode$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">ryxx.EXTRA2 NOT LIKE '/BSTA/BGTA/BGTA00/BGTAEC00%'</isNotEmpty>
		GROUP BY T.TECHNOLOGY_ID
		UNION ALL <!-- 总部的 -->
		SELECT  T.TECHNOLOGY_ID AS technologyId,
				MAX(T.CONFIRM_NUM) AS confirmNum,
				MAX(T.TECHNOLOGY_NAME) AS technologyName,
				SUM(ryxx.EXTRA1)/100 AS gxxs
		FROM ${zzzcSchema}.T_KYMM_TECH_MEMBER ryxx
		LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY T ON ryxx.TECHNOLOGY_ID = T.TECHNOLOGY_ID AND T.DEL_STATUS='0'
		WHERE ryxx.DEL_STATUS='0' AND T.STATUS = 'identified' AND T.CONFIRM_TIME &gt;=  '$startDate$' AND T.CONFIRM_TIME &lt;= '$endDate$' 
		AND ryxx.EXTRA2 LIKE '/BSTA/BGTA/BGTA00%'
		<isNotEmpty prepend=" AND " property="deptCode">
			ryxx.EXTRA2 like '$deptCode$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">ryxx.EXTRA2 NOT LIKE '/BSTA/BGTA/BGTA00/BGTAEC00%'</isNotEmpty>
		GROUP BY T.TECHNOLOGY_ID
		UNION ALL <!-- 梅钢的 -->
		SELECT  T.TECHNOLOGY_ID AS technologyId,
				MAX(T.CONFIRM_NUM) AS confirmNum,
				MAX(T.TECHNOLOGY_NAME) AS technologyName,
				SUM(ryxx.EXTRA1)/100 AS gxxs
		FROM ${zzzcSchema}.T_KYMM_TECH_MEMBER ryxx
		LEFT JOIN ${zzzcSchema}.T_KYMM_TECHNOLOGY T ON ryxx.TECHNOLOGY_ID = T.TECHNOLOGY_ID AND T.DEL_STATUS='0'
		WHERE ryxx.DEL_STATUS='0' AND T.STATUS = 'identified' AND T.CONFIRM_TIME &gt;=  '$startDate$' AND T.CONFIRM_TIME &lt;= '$endDate$' 
		AND ryxx.EXTRA2 LIKE '/BSTA/BGTA/BGTM/BGTM00%'
		<isNotEmpty prepend=" AND " property="deptCode">
			ryxx.EXTRA2 like '$deptCode$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">ryxx.EXTRA2 NOT LIKE '/BSTA/BGTA/BGTA00/BGTAEC00%'</isNotEmpty>
		GROUP BY T.TECHNOLOGY_ID ) tmp
		GROUP BY tmp.technologyId
		ORDER BY MAX(tmp.confirmNum) ASC 
	</select>
	
	<select id="querySqxxTjdw"  parameterClass="hashmap" resultClass="hashmap">
	SELECT 
		tmp.deptId AS "deptId",
		CASE WHEN tmp.shortName IS NOT NULL THEN tmp.shortName ELSE tmp.deptName END AS "deptName", 
		tmp.jan/100 AS "jan",
		tmp.feb/100 AS "feb",
		tmp.mar/100 AS "mar",
		tmp.apr/100 AS "apr",
		tmp.may/100 AS "may",
		tmp.jun/100 AS "jun",
		tmp.jul/100 AS "jul",
		tmp.aug/100 AS "aug",
		tmp.sep/100 AS "sep",
		tmp.oct/100 AS "oct",
		tmp.nov/100 AS "nov",
		tmp.dec/100 AS "dec",
		tmp.tongji/100 AS "tongji",
		tmp.invention AS "invention"
	FROM 
		(
		SELECT <!-- 查总部研究院单位 -->
			MAX(s.XH) AS xh,
			MAX(s.SHORT_NAME) AS shortName,
			MAX(t.DEPTCBBM) AS deptId,
			MAX(t.DWMCPX) AS deptName,
			SUM(CASE WHEN 
			<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 1</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS jan,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 2</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 2</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS feb,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 3</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 3</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS mar,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 4</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 4</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS apr,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 5</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 5</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS may,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 6</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 6</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS jun,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 7</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 7</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS jul,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 8</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 8</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS aug,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 9</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 9</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS sep,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 10</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 10</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS oct,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 11</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 11</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS nov,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 12</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 12</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS dec,
			SUM(t.EXTRA2) AS tongji,
			SUM(CASE WHEN p.PATENT_TYPE = 'FM' THEN t.EXTRA2 ELSE 0 END) AS invention
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON t.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
		AND t.CODE_PATH LIKE '%BGTAEC00%'
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutMs">t.CODE_PATH NOT like '%BGTM%'</isNotEmpty>
		GROUP BY t.DEPTCBBM
		UNION ALL
		SELECT <!-- 查总部其他单位 -->
			MAX(s.XH) AS xh,
			MAX(s.SHORT_NAME) AS shortName,
			MAX(t.DEPTCBBM) AS deptId,
			MAX(t.DWMCPX) AS deptName,
			SUM(CASE WHEN 
			<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 1</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS jan,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 2</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 2</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS feb,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 3</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 3</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS mar,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 4</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 4</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS apr,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 5</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 5</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS may,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 6</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 6</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS jun,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 7</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 7</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS jul,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 8</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 8</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS aug,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 9</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 9</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS sep,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 10</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 10</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS oct,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 11</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 11</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS nov,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 12</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 12</isNotEmpty> THEN t.EXTRA2 ELSE 0 END) AS dec,
			SUM(t.EXTRA2) AS tongji,
			SUM(CASE WHEN p.PATENT_TYPE = 'FM' THEN t.EXTRA2 ELSE 0 END) AS invention
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON t.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
		AND (t.CODE_PATH LIKE '%BGTA00%' AND t.CODE_PATH NOT LIKE '%BGTAEC00%')
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutMs">t.CODE_PATH NOT like '%BGTM%'</isNotEmpty>
		GROUP BY t.DEPTCBBM
	</select>
	
	<!-- 正常查部门 -->
	<select id="querySqxxTjbm"  parameterClass="hashmap" resultClass="hashmap">
	SELECT MAX(tmp.depName) AS "depName",
	tmp.deptId AS "deptId", 
	SUM(tmp.jan) as "jan", SUM(tmp.feb) as "feb", SUM(tmp.mar) as "mar", SUM(tmp.apr) as "apr", SUM(tmp.may) as "may", SUM(tmp.jun) as "jun", 
	SUM(tmp.jul) as "jul", SUM(tmp.aug) as "aug", SUM(tmp.sep) as "sep", SUM(tmp.oct) as "oct", SUM(tmp.nov) as "nov", SUM(tmp.dec) as "dec", 
	SUM(tmp.tongji) as "tongji", SUM(tmp.invention) as "invention", SUM(tmp.zongshu) as "zongshu" FROM 
		(SELECT 
		    MAX(t.NAME_PATH) AS depName,
			CASE WHEN MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))) LIKE '%/%' THEN
			substring(MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))),1,
			length(substring(MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))), 1, locate('/', MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))))-1))) 
			ELSE MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))) END AS deptId,
			SUM(CASE WHEN 
			<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 1</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jan,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 2</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 2</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS feb,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 3</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 3</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS mar,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 4</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 4</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS apr,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 5</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 5</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS may,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 6</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 6</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jun,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 7</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 7</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jul,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 8</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 8</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS aug,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 9</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 9</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS sep,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 10</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 10</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS oct,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 11</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 11</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS nov,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 12</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 12</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS dec,
			SUM(t.EXTRA2)/100 AS tongji,
			SUM(CASE WHEN p.PATENT_TYPE = 'FM' THEN t.EXTRA2 ELSE 0 END) AS invention,
			COUNT(b.APPLY_ID) AS zongshu
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON t.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">t.CODE_PATH NOT LIKE '%BGTAEC00%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutMs">t.CODE_PATH NOT like '%BGTM%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		GROUP BY t.DEPT_CODE
		UNION ALL
		SELECT <!-- 化工公司 -->
			MAX(t.NAME_PATH) AS depName,
			CASE WHEN MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))) LIKE '%/%' THEN
			substring(MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))),1,
			length(substring(MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))), 1, locate('/', MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))))-1))) 
			ELSE MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))) END AS deptId,
			SUM(CASE WHEN 
			<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 1</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jan,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 2</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 2</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS feb,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 3</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 3</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS mar,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 4</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 4</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS apr,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 5</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 5</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS may,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 6</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 6</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jun,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 7</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 7</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jul,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 8</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 8</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS aug,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 9</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 9</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS sep,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 10</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 10</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS oct,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 11</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 11</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS nov,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 12</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 12</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS dec,
			SUM(t.EXTRA2)/100 AS tongji,
			SUM(CASE WHEN p.PATENT_TYPE = 'FM' THEN t.EXTRA2 ELSE 0 END) AS invention,
			COUNT(p.APPLY_ID) AS zongshu
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG t
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO_HG p ON t.PATENT_ID = p.PATENT_ID AND p.DEL_STATUS='0'
		WHERE t.EXTRA1 = '1' AND t.DEL_STATUS='0' 
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">p.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		GROUP BY t.DEPT_CODE
		<isNotEmpty prepend=" UNION ALL " property="withOutMs">
		SELECT <!-- 查梅钢，ps:申请人有梅钢、梅山的要把梅山的贡献系数给梅钢 -->
			MAX(t.NAME_PATH) AS depName,
			CASE WHEN MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))) LIKE '%/%' THEN
			substring(MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))),1,
			length(substring(MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))), 1, locate('/', MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))))-1))) 
			ELSE MAX(substring(t.CODE_PATH,length('$deptCode$')+2,length(t.CODE_PATH))) END AS deptId,
			SUM(CASE WHEN 
			<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 1</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jan,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 2</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 2</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS feb,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 3</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 3</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS mar,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 4</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 4</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS apr,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 5</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 5</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS may,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 6</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 6</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jun,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 7</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 7</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jul,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 8</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 8</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS aug,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 9</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 9</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS sep,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 10</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 10</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS oct,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 11</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 11</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS nov,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 12</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 12</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS dec,
			SUM(t.EXTRA2)/100 AS tongji,
			SUM(CASE WHEN p.PATENT_TYPE = 'FM' THEN t.EXTRA2 ELSE 0 END) AS invention,
			COUNT(p.APPLY_ID) AS zongshu
			FROM (
			SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%QT%' AND MSPATH NOT LIKE '%,%'
			UNION ALL
			SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' AND MSPATH LIKE '%QT%'
			UNION ALL
			SELECT 
			DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
			FROM 
			${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%MG%' AND MSPATH NOT LIKE '%,%'
			UNION ALL
			SELECT 
			DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
			FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
			AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BGTM00' 
			UNION ALL
			SELECT 
			DEPT_CODE,NAME_PATH,RYXX_ID,'BGTM00' AS DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
			FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
			AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BSTM' ) t
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
			WHERE t.DEPTCBBM NOT LIKE '%BSTM%' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
			<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
			<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID AND p.FLZT = '10' </isNotEmpty>
			<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
			<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
			<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptCode">
				<isEmpty prepend=" " property="withBgtm">t.CODE_PATH like '%$deptCode$%'</isEmpty>
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			GROUP BY t.DEPT_CODE
		</isNotEmpty>) tmp 
		GROUP BY tmp.deptId
	</select>
	
	<!-- 查发明人明细 -->
	<select id="querySqxxTjFmr"  parameterClass="hashmap" resultClass="hashmap">
		SELECT tmp.applyId AS "applyId",
			   tmp.jsbh AS "jsbh",
			   tmp.bgbh AS "bgbh",
			   tmp.patentNo AS "patentNo",
			   tmp.applyName AS "applyName",
			   tmp.firstDeptName AS "firstDeptName",
			   tmp.empName AS "empName",
			   tmp.empId AS "empId",
			   tmp.gxxs AS "gxxs" from
		(SELECT 
			b.APPLY_ID AS applyId,
			b.JSBH AS jsbh,
			p.BGBH AS bgbh,
			p.PATENT_NO AS patentNo,
			CASE WHEN p.APPLY_NAME IS NULL THEN b.APPLY_NAME ELSE p.APPLY_NAME END AS applyName,
			b.FIRST_DEPT_NAME AS firstDeptName,
			t.EMP_NAME AS empName,
			t.EMP_ID AS empId,
			t.EXTRA2 AS gxxs
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON t.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
		<isNotEmpty prepend=" AND " property="withOutMs">t.CODE_PATH NOT like '%BGTM%'</isNotEmpty>
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			t.CODE_PATH like '%$deptCode$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">t.CODE_PATH NOT LIKE '%BGTAEC00%'</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		UNION ALL
		SELECT <!-- 化工公司 -->
			p.APPLY_ID AS applyId,
			p.JSBH AS jsbh,
			p.BGBH AS bgbh,
			p.PATENT_NO AS patentNo,
			p.APPLY_NAME AS applyName,
			p.FIRST_DEPT_NAME AS firstDeptName,
			t.EMP_NAME AS empName,
			t.EMP_ID AS empId,
			t.EXTRA2 AS gxxs
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG t
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO_HG p ON t.PATENT_ID = p.PATENT_ID AND p.DEL_STATUS='0'
		WHERE t.EXTRA1 = '1' AND t.DEL_STATUS='0' 
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">p.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			t.CODE_PATH like '%$deptCode$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">t.CODE_PATH NOT LIKE '%BGTAEC00%'</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		<isNotEmpty prepend=" UNION ALL " property="withOutMs">
			SELECT <!-- 查梅钢，ps:申请人有梅钢、梅山的要把梅山的贡献系数给梅钢 -->
			b.APPLY_ID AS applyId,
			b.JSBH AS jsbh,
			p.BGBH AS bgbh,
			p.PATENT_NO AS patentNo,
			p.APPLY_NAME AS applyName,
			b.FIRST_DEPT_NAME AS firstDeptName,
			tk.EMP_NAME AS empName,
			tk.EMP_ID AS empId,
			tk.EXTRA2 AS gxxs
			FROM (
			SELECT
			DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
			FROM
			${zzzcSchema}.V_KIZL_RYXXSQR_INFO
			WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%QT%' AND MSPATH NOT LIKE '%,%'
			UNION ALL
			SELECT
			DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
			FROM
			${zzzcSchema}.V_KIZL_RYXXSQR_INFO
			WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' AND MSPATH LIKE '%QT%'
			UNION ALL
			SELECT
			DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
			FROM
			${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%MG%' AND MSPATH NOT LIKE '%,%'
			UNION ALL
			SELECT
			DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
			FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%'
			AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BGTM00'
			UNION ALL
			SELECT
			DEPT_CODE,NAME_PATH,RYXX_ID,'BGTM00' AS DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
			FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%'
			AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BSTM' ) t
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX tk ON t.RYXX_ID = tk.RYXX_ID
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
			WHERE t.DEPTCBBM NOT LIKE '%BSTM%' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
			<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
			<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
			<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
			<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
			<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptCode">
				<isNotEmpty property="isCompany">
					t.CODE_PATH like '%$deptCode$%'
				</isNotEmpty>
				<isNotEmpty property="isDept">
					<!-- t.DEPT_CODE = '$deptCode$' -->
					t.CODE_PATH like '%$deptCode$%'
				</isNotEmpty>
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="inCompanyNode">
				t.DEPT_CODE = '$inCompanyNode$'
			</isNotEmpty>
			<isNotEmpty property="yf">
				<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</isNotEmpty>
		    ) tmp
		ORDER BY tmp.jsbh, tmp.bgbh, tmp.patentNo ASC
	</select>
	
	<!-- 查发明人明细合计 -->
	<select id="querySqxxTjFmrhj"  parameterClass="hashmap" resultClass="hashmap">
		SELECT SUM(tmp.gxxs) AS "gxxs" from
		(SELECT SUM(t.EXTRA2) AS gxxs
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON t.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
		<isNotEmpty prepend=" AND " property="withOutMs">t.CODE_PATH NOT like '%BGTM%'</isNotEmpty>
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			t.CODE_PATH like '%$deptCode$%'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">t.CODE_PATH NOT LIKE '%BGTAEC00%'</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
		</isNotEmpty>
		UNION ALL
		SELECT <!-- 化工公司 -->
			SUM(t.EXTRA2) AS gxxs
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG t
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO_HG p ON t.PATENT_ID = p.PATENT_ID AND p.DEL_STATUS='0'
		WHERE t.EXTRA1 = '1' AND t.DEL_STATUS='0' 
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">p.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			t.CODE_PATH like '%$deptCode$%'
		</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
		</isNotEmpty>) tmp
	</select>
	
	<!-- 查数字明细 -->
	<select id="querySqxxTjmx"  parameterClass="hashmap" resultClass="hashmap">
		SELECT tmp.applyId AS "applyId",
			MAX(tmp.jsbh) AS "jsbh",
			MAX(tmp.bgbh) AS "bgbh",
			MAX(tmp.patentNo) AS "patentNo",
			MAX(tmp.applyName) AS "applyName",
			SUM(tmp.gxxs) AS "gxxs" from
		(SELECT 
			b.APPLY_ID AS applyId,
			MAX(b.JSBH) AS jsbh,
			MAX(p.BGBH) AS bgbh,
			MAX(p.PATENT_NO) AS patentNo,
			CASE WHEN MAX(p.APPLY_NAME) IS NULL THEN MAX(b.APPLY_NAME) ELSE MAX(p.APPLY_NAME) END AS applyName,
			SUM(t.EXTRA2)/100 AS gxxs
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON t.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutMs">t.CODE_PATH NOT like '%BGTM%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			<isNotEmpty property="isCompany">
				t.CODE_PATH like '%$deptCode$%'
			</isNotEmpty>
			<isNotEmpty property="isDept">
				<!-- t.DEPT_CODE = '$deptCode$' -->
				t.CODE_PATH like '%$deptCode$%'
			</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="inCompanyNode">
			t.DEPT_CODE = '$inCompanyNode$'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">t.CODE_PATH NOT LIKE '%BGTAEC00%'</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		GROUP BY b.APPLY_ID
		UNION ALL
		SELECT <!-- 化工公司 -->
			p.APPLY_ID AS applyId,
			MAX(p.JSBH) AS jsbh,
			MAX(p.BGBH) AS bgbh,
			MAX(p.PATENT_NO) AS patentNo,
			MAX(p.APPLY_NAME) AS applyName,
			SUM(t.EXTRA2)/100 AS gxxs
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG t
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO_HG p ON t.PATENT_ID = p.PATENT_ID AND p.DEL_STATUS='0'
		WHERE t.EXTRA1 = '1' AND t.DEL_STATUS='0' 
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">p.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			<isNotEmpty property="isCompany">
				t.CODE_PATH like '%$deptCode$%'
			</isNotEmpty>
			<isNotEmpty property="isDept">
				<!-- t.DEPT_CODE = '$deptCode$' -->
				t.CODE_PATH like '%$deptCode$%'
			</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="inCompanyNode">
			t.DEPT_CODE = '$inCompanyNode$'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">t.CODE_PATH NOT LIKE '%BGTAEC00%'</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		GROUP BY p.APPLY_ID
		<isNotEmpty prepend=" UNION ALL " property="withOutMs">
		SELECT <!-- 查梅钢，ps:申请人有梅钢、梅山的要把梅山的贡献系数给梅钢 -->
			b.APPLY_ID AS applyId,
			MAX(b.JSBH) AS jsbh,
			MAX(p.BGBH) AS bgbh,
			MAX(p.PATENT_NO) AS patentNo,
			CASE WHEN MAX(p.APPLY_NAME) IS NULL THEN MAX(b.APPLY_NAME) ELSE MAX(p.APPLY_NAME) END AS applyName,
			SUM(t.EXTRA2)/100 AS gxxs
		FROM (
		SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%QT%' AND MSPATH NOT LIKE '%,%'
			UNION ALL
		SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' AND MSPATH LIKE '%QT%'
			UNION ALL
		SELECT 
			DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
			FROM 
			${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%MG%' AND MSPATH NOT LIKE '%,%'
			UNION ALL
			SELECT 
			DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
			FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
			AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BGTM00' 
			UNION ALL
			SELECT 
			DEPT_CODE,NAME_PATH,RYXX_ID,'BGTM00' AS DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
			FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
			AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BSTM' ) t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		WHERE t.DEPTCBBM NOT LIKE '%BSTM%' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			<isNotEmpty property="isCompany">
				t.CODE_PATH like '%$deptCode$%'
			</isNotEmpty>
			<isNotEmpty property="isDept">
				<!-- t.DEPT_CODE = '$deptCode$' -->
				t.CODE_PATH like '%$deptCode$%'
			</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="inCompanyNode">
			t.DEPT_CODE = '$inCompanyNode$'
		</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		GROUP BY b.APPLY_ID
		</isNotEmpty>
		) tmp
		GROUP BY tmp.applyId
		ORDER BY MAX(tmp.jsbh), MAX(tmp.bgbh), MAX(tmp.patentNo) ASC
	</select>
	
	<!-- 查明细合计 -->
	<select id="querySqxxTjmxhj"  parameterClass="hashmap" resultClass="hashmap">
		SELECT SUM(tmp.gxxs) AS "gxxs" from
		(SELECT SUM(t.EXTRA2)/100 AS gxxs
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON t.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutMs">t.CODE_PATH NOT like '%BGTM%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			<isNotEmpty property="isCompany">
				t.CODE_PATH like '%$deptCode$%'
			</isNotEmpty>
			<isNotEmpty property="isDept">
				<!-- t.DEPT_CODE = '$deptCode$' -->
				t.CODE_PATH like '%$deptCode$%'
			</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="inCompanyNode">
			t.DEPT_CODE = '$inCompanyNode$'
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">t.CODE_PATH NOT LIKE '%BGTAEC00%'</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
		</isNotEmpty>
		UNION ALL
		SELECT <!-- 化工公司 -->
			SUM(t.EXTRA2)/100 AS gxxs
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG t
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO_HG p ON t.PATENT_ID = p.PATENT_ID AND p.DEL_STATUS='0'
		WHERE t.EXTRA1 = '1' AND t.DEL_STATUS='0' 
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">p.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			<isNotEmpty property="isCompany">
				t.CODE_PATH like '%$deptCode$%'
			</isNotEmpty>
			<isNotEmpty property="isDept">
				<!-- t.DEPT_CODE = '$deptCode$' -->
				t.CODE_PATH like '%$deptCode$%'
			</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="inCompanyNode">
			t.DEPT_CODE = '$inCompanyNode$'
		</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" UNION ALL " property="withOutMs">
		SELECT <!-- 查梅钢，ps:申请人有梅钢、梅山的要把梅山的贡献系数给梅钢 -->
			SUM(t.EXTRA2)/100 AS gxxs
			FROM (
			SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%QT%' AND MSPATH NOT LIKE '%,%'
			UNION ALL
			SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' AND MSPATH LIKE '%QT%'
			UNION ALL
			SELECT 
			DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
			FROM 
			${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%MG%' AND MSPATH NOT LIKE '%,%'
			UNION ALL
			SELECT 
			DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
			FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
			AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BGTM00' 
			UNION ALL
			SELECT 
			DEPT_CODE,NAME_PATH,RYXX_ID,'BGTM00' AS DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
			FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
			AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BSTM' ) t
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
			WHERE t.DEPTCBBM NOT LIKE '%BSTM%' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
			<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
			<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
			<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
			<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
			<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptCode">
				<isNotEmpty property="isCompany">
					t.CODE_PATH like '%$deptCode$%'
				</isNotEmpty>
				<isNotEmpty property="isDept">
					<!-- t.DEPT_CODE = '$deptCode$' -->
					t.CODE_PATH like '%$deptCode$%'
				</isNotEmpty>
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="inCompanyNode">
				t.DEPT_CODE = '$inCompanyNode$'
			</isNotEmpty>
			<isNotEmpty property="yf">
				<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
			</isNotEmpty>
		</isNotEmpty>
		) tmp
	</select>
	
	<!-- 主报表月度合计 -->
	<select id="querySqxxMonth"  parameterClass="hashmap" resultClass="hashmap">
		SELECT <isNotEmpty property="applyDate">'月申请量' </isNotEmpty>
			<isNotEmpty property="ljDate">'月授权量'</isNotEmpty>
			<isNotEmpty property="authDate">'月授权量'</isNotEmpty> 
			<isNotEmpty property="reauthDate">'月授权量'</isNotEmpty> AS "deptName",
			SUM(tmp.jan) AS "jan",
			SUM(tmp.feb) AS "feb",
			SUM(tmp.mar) AS "mar",
			SUM(tmp.apr) AS "apr",
			SUM(tmp.may) AS "may",
			SUM(tmp.jun) AS "jun",
			SUM(tmp.jul) AS "jul",
			SUM(tmp.aug) AS "aug",
			SUM(tmp.sep) AS "sep",
			SUM(tmp.oct) AS "oct",
			SUM(tmp.nov) AS "nov",
			SUM(tmp.dec) AS "dec",
			SUM(tmp.tongji) AS "tongji",
			SUM(tmp.invention) AS "invention",
			SUM(tmp.zongshu) AS "zongshu" from
		(SELECT <!-- 化工公司 -->
			SUM(CASE WHEN 
			<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 1</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jan,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 2</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 2</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS feb,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 3</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 3</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS mar,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 4</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 4</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS apr,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 5</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 5</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS may,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 6</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 6</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jun,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 7</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 7</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jul,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 8</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 8</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS aug,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 9</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 9</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS sep,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 10</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 10</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS oct,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 11</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 11</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS nov,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 12</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 12</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS dec,
			SUM(t.EXTRA2)/100 AS tongji,
			SUM(CASE WHEN p.PATENT_TYPE = 'FM' THEN t.EXTRA2 ELSE 0 END) AS invention,
			COUNT(p.APPLY_ID) AS zongshu
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG t
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO_HG p ON t.PATENT_ID = p.PATENT_ID AND p.DEL_STATUS='0'
		WHERE t.EXTRA1 = '1' AND t.DEL_STATUS='0' 
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">p.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		UNION ALL
		SELECT <!-- 查所有组织月度申请、授权量 -->
			SUM(CASE WHEN 
			<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 1</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jan,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 2</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 2</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS feb,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 3</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 3</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS mar,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 4</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 4</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS apr,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 5</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 5</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS may,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 6</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 6</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jun,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 7</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 7</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jul,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 8</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 8</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS aug,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 9</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 9</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS sep,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 10</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 10</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS oct,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 11</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 11</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS nov,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 12</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 12</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS dec,
			SUM(t.EXTRA2)/100 AS tongji,
			SUM(CASE WHEN p.PATENT_TYPE = 'FM' THEN t.EXTRA2 ELSE 0 END) AS invention,
			COUNT(b.APPLY_ID) AS zongshu
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON t.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">t.CODE_PATH NOT LIKE '%BGTAEC00%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutMs">t.CODE_PATH NOT like '%BGTM%'</isNotEmpty>
		<isNotEmpty prepend=" UNION ALL " property="withOutMs">
			SELECT <!-- 查梅钢，ps:申请人有梅钢、梅山的要把梅山的贡献系数给梅钢 -->
				SUM(CASE WHEN 
				<!-- 统计类型：申请 --><isNotEmpty property="applyDate">EXTRACT(MONTH FROM p.SLRQ) = 1</isNotEmpty>
				<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">EXTRACT(MONTH FROM p.SQRQ) = 1</isNotEmpty>
				<!-- 统计类型：授权日 --><isNotEmpty property="authDate">EXTRACT(MONTH FROM p.SQRQ) = 1</isNotEmpty>
				<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">EXTRACT(MONTH FROM p.DJR_DATE) = 1</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jan,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 2</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) = 2</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 2</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 2</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS feb,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 3</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) = 3</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 3</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 3</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS mar,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 4</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) = 4</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 4</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 4</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS apr,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 5</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) = 5</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 5</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 5</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS may,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 6</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) = 6</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 6</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 6</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jun,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 7</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) = 7</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 7</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 7</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS jul,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 8</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) = 8</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 8</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 8</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS aug,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 9</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) = 9</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 9</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 9</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS sep,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 10</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) = 10</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 10</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 10</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS oct,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 11</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) = 11</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 11</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 11</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS nov,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 12</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) = 12</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 12</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 12</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS dec,
				SUM(t.EXTRA2)/100 AS tongji,
				SUM(CASE WHEN p.PATENT_TYPE = 'FM' THEN t.EXTRA2 ELSE 0 END) AS invention,
				COUNT(b.APPLY_ID) AS zongshu
			FROM (
			SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%QT%' AND MSPATH NOT LIKE '%,%'
			UNION ALL
			SELECT
				DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
				FROM
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO
				WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' AND MSPATH LIKE '%QT%'
			UNION ALL
			SELECT 
				DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
				FROM 
				${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%MG%' AND MSPATH NOT LIKE '%,%'
				UNION ALL
				SELECT 
				DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
				FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
				AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BGTM00' 
				UNION ALL
				SELECT 
				DEPT_CODE,NAME_PATH,RYXX_ID,'BGTM00' AS DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
				FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' 
				AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BSTM' ) t
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
			WHERE t.DEPTCBBM NOT LIKE '%BSTM%' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
			<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
			<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
			<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
			<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
			<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptCode">
				<isEmpty prepend=" " property="withBgtm">t.CODE_PATH like '%$deptCode$%'</isEmpty>
			</isNotEmpty>
		</isNotEmpty>
		) tmp
	</select>
	
	<!-- 主报表季度合计 -->
	<select id="querySqxxQuarter"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
			<isNotEmpty property="applyDate">'季度申请量' </isNotEmpty>
			<isNotEmpty property="ljDate">'季度授权量'</isNotEmpty> 
			<isNotEmpty property="authDate">'季度授权量'</isNotEmpty> 
			<isNotEmpty property="reauthDate">'季度授权量'</isNotEmpty> AS "deptName",
			SUM(tmp.feb) AS "feb",
			SUM(tmp.may) AS "may",
			SUM(tmp.aug) AS "aug",
			SUM(tmp.nov) AS "nov",
			SUM(tmp.tongji) AS "tongji",
			SUM(tmp.invention) AS "invention" from
		(SELECT <!-- 化工公司 -->
			SUM(CASE WHEN 
			<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (1,2,3) </isNotEmpty>
			<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (1,2,3)</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) IN (1,2,3)</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (1,2,3)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS feb,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (4,5,6)</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (4,5,6)</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) IN (4,5,6)</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (4,5,6)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS may,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (7,8,9)</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (7,8,9)</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) IN (7,8,9)</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (7,8,9)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS aug,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (10,11,12)</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (10,11,12)</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) IN (10,11,12)</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (10,11,12)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS nov,
			SUM(t.EXTRA2)/100 AS tongji,
			SUM(CASE WHEN p.PATENT_TYPE = 'FM' THEN t.EXTRA2 ELSE 0 END) AS invention
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX_HG t
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO_HG p ON t.PATENT_ID = p.PATENT_ID AND p.DEL_STATUS='0'
		WHERE t.EXTRA1 = '1' AND t.DEL_STATUS='0' 
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">p.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		UNION ALL
		SELECT <!-- 查所有组织季度申请、授权量 -->
			SUM(CASE WHEN 
			<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (1,2,3) </isNotEmpty>
			<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (1,2,3)</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) IN (1,2,3)</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (1,2,3)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS feb,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (4,5,6)</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (4,5,6)</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) IN (4,5,6)</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (4,5,6)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS may,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (7,8,9)</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (7,8,9)</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) IN (7,8,9)</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (7,8,9)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS aug,
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (10,11,12)</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (10,11,12)</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) IN (10,11,12)</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (10,11,12)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS nov,
			SUM(t.EXTRA2)/100 AS tongji,
			SUM(CASE WHEN p.PATENT_TYPE = 'FM' THEN t.EXTRA2 ELSE 0 END) AS invention
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON t.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">t.CODE_PATH NOT LIKE '%BGTAEC00%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutMs">t.CODE_PATH NOT like '%BGTM%'</isNotEmpty>
		<isNotEmpty prepend=" UNION ALL " property="withOutMs">
			SELECT <!-- 查梅钢 -->
				SUM(CASE WHEN 
				<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (1,2,3) </isNotEmpty>
				<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (1,2,3)</isNotEmpty>
				<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) IN (1,2,3)</isNotEmpty>
				<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (1,2,3)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS feb,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (4,5,6)</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (4,5,6)</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) IN (4,5,6)</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (4,5,6)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS may,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (7,8,9)</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (7,8,9)</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) IN (7,8,9)</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (7,8,9)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS aug,
				SUM(CASE WHEN 
				<isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (10,11,12)</isNotEmpty>
				<isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (10,11,12)</isNotEmpty>
				<isNotEmpty property="authDate">MONTH(p.SQRQ) IN (10,11,12)</isNotEmpty>
				<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (10,11,12)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS nov,
				SUM(t.EXTRA2)/100 AS tongji,
				SUM(CASE WHEN p.PATENT_TYPE = 'FM' THEN t.EXTRA2 ELSE 0 END) AS invention
			FROM (
				SELECT
					DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
					FROM
					${zzzcSchema}.V_KIZL_RYXXSQR_INFO
					WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%QT%' AND MSPATH NOT LIKE '%,%'
				UNION ALL
				SELECT
					DEPT_CODE, NAME_PATH, RYXX_ID, DEPTCBBM, DWMCPX, EXTRA2 AS EXTRA2, APPLY_ID, CODE_PATH
					FROM
					${zzzcSchema}.V_KIZL_RYXXSQR_INFO
					WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' AND MSPATH LIKE '%QT%'
				UNION ALL
				SELECT 
					DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
					FROM 
					${zzzcSchema}.V_KIZL_RYXXSQR_INFO 
					WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%MG%' AND MSPATH NOT LIKE '%,%'
				UNION ALL
				SELECT 
					DEPT_CODE,NAME_PATH,RYXX_ID,DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
					FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO 
					WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BGTM00' 
				UNION ALL
				SELECT 
					DEPT_CODE,NAME_PATH,RYXX_ID,'BGTM00' AS DEPTCBBM,DWMCPX,EXTRA2 AS EXTRA2,APPLY_ID,CODE_PATH
					FROM ${zzzcSchema}.V_KIZL_RYXXSQR_INFO 
					WHERE DEPTCBBM IN ('BGTM00','BSTM') AND MSPATH LIKE '%,%' AND MSPATH LIKE '%MG%' AND MSPATH LIKE '%MS%' AND DEPTCBBM = 'BSTM' ) t
			LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
			LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
			WHERE t.DEPTCBBM NOT LIKE '%BSTM%' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
			<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
			<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
			<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
			<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
			<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptCode">
				<isEmpty prepend=" " property="withBgtm">t.CODE_PATH like '%$deptCode$%'</isEmpty>
			</isNotEmpty>
		</isNotEmpty>
		) tmp
	</select>
	
	<!-- 主报表发明比例前3年平均值、最高值 -->
	<select id="querySqxxCompleteProportion"  parameterClass="hashmap" resultClass="hashmap">
		SELECT DEPT_CODE AS "deptCode",
			THREE_PROP_INV AS "threePropInv",
			THREE_PROP_BAST AS "threePropBast"
		FROM ${zzzcSchema}.T_KIZL_MAINTAIN_APPLYPLAN
		WHERE YEAR = #year# AND DEPT_CODE = #deptCode#
	</select>
	
	<select id="querySqxxFqdw"  parameterClass="hashmap" resultClass="hashmap">
	SELECT 
		tmp.deptId AS "deptId",
		CASE WHEN tmp.shortName IS NOT NULL THEN tmp.shortName ELSE tmp.deptName END AS "deptName", 
		tmp.jan AS "jan",
		tmp.feb AS "feb",
		tmp.mar AS "mar",
		tmp.apr AS "apr",
		tmp.may AS "may",
		tmp.jun AS "jun",
		tmp.jul AS "jul",
		tmp.aug AS "aug",
		tmp.sep AS "sep",
		tmp.oct AS "oct",
		tmp.nov AS "nov",
		tmp.dec AS "dec",
		tmp.zongshu AS "zongshu"
	FROM 
		(
		SELECT <!-- 化工公司 -->
			'07' AS xh,
			'化工公司' AS shortName,
			MAX(p.GLDW_CODE) AS deptId,
			'化工公司' AS deptName,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 1 THEN 1 ELSE 0 END) AS jan,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 2 THEN 1 ELSE 0 END) AS feb,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 3 THEN 1 ELSE 0 END) AS mar,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 4 THEN 1 ELSE 0 END) AS apr,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 5 THEN 1 ELSE 0 END) AS may,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 6 THEN 1 ELSE 0 END) AS jun,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 7 THEN 1 ELSE 0 END) AS jul,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 8 THEN 1 ELSE 0 END) AS aug,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 9 THEN 1 ELSE 0 END) AS sep,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 10 THEN 1 ELSE 0 END) AS oct,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 11 THEN 1 ELSE 0 END) AS nov,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 12 THEN 1 ELSE 0 END) AS dec,
			COUNT(p.APPLY_ID) AS zongshu
		FROM ${zzzcSchema}.T_KIZL_PATENT_INFO_HG p 
		WHERE p.FLZT='03' AND p.DEL_STATUS='0'
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ISVALID = '0' AND p.ZZRQ IS NOT NULL </isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ZZRQ &gt;=  '$zzDate$' AND p.ZZRQ &lt;= '$zzDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">p.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">p.FIRST_DEPT_PATH like '%$deptCode$%'</isNotEmpty>
		GROUP BY p.GLDW_CODE
		UNION ALL
		SELECT <!-- 查总部研究院单位 -->
			MAX(s.XH) AS xh,
			MAX(s.SHORT_NAME) AS shortName,
			MAX(b.DEPTCBBM) AS deptId,
			MAX(b.DWMCPX) AS deptName,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 1 THEN 1 ELSE 0 END) AS jan,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 2 THEN 1 ELSE 0 END) AS feb,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 3 THEN 1 ELSE 0 END) AS mar,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 4 THEN 1 ELSE 0 END) AS apr,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 5 THEN 1 ELSE 0 END) AS may,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 6 THEN 1 ELSE 0 END) AS jun,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 7 THEN 1 ELSE 0 END) AS jul,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 8 THEN 1 ELSE 0 END) AS aug,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 9 THEN 1 ELSE 0 END) AS sep,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 10 THEN 1 ELSE 0 END) AS oct,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 11 THEN 1 ELSE 0 END) AS nov,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 12 THEN 1 ELSE 0 END) AS dec,
			COUNT(b.APPLY_ID) AS zongshu
		FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b 
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON b.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE p.FLZT='03' AND b.DEL_STATUS='0' AND p.FIRST_DEPT_PATH LIKE '%BGTAEC00%' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		<isNotEmpty prepend=" AND " property="withOutMs">b.GLDW_CODE != 'BSTM' AND p.GLDW_CODE != 'BSTM'</isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ISVALID = '0' AND p.ZZRQ IS NOT NULL </isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ZZRQ &gt;=  '$zzDate$' AND p.ZZRQ &lt;= '$zzDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">p.FIRST_DEPT_PATH like '%$deptCode$%'</isNotEmpty>
		GROUP BY b.DEPTCBBM
		UNION ALL
		SELECT <!-- 查总部其他单位 -->
			MAX(s.XH) AS xh,
			MAX(s.SHORT_NAME) AS shortName,
			MAX(b.DEPTCBBM) AS deptId,
			MAX(b.DWMCPX) AS deptName,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 1 THEN 1 ELSE 0 END) AS jan,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 2 THEN 1 ELSE 0 END) AS feb,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 3 THEN 1 ELSE 0 END) AS mar,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 4 THEN 1 ELSE 0 END) AS apr,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 5 THEN 1 ELSE 0 END) AS may,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 6 THEN 1 ELSE 0 END) AS jun,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 7 THEN 1 ELSE 0 END) AS jul,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 8 THEN 1 ELSE 0 END) AS aug,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 9 THEN 1 ELSE 0 END) AS sep,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 10 THEN 1 ELSE 0 END) AS oct,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 11 THEN 1 ELSE 0 END) AS nov,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 12 THEN 1 ELSE 0 END) AS dec,
			COUNT(b.APPLY_ID) AS zongshu
		FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b 
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON b.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE p.FLZT='03' AND b.DEL_STATUS='0' AND (p.FIRST_DEPT_PATH LIKE '%BGTA00%' AND p.FIRST_DEPT_PATH NOT LIKE '%BGTAEC00%') 
		AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV'
		<isNotEmpty prepend=" AND " property="withOutMs">b.GLDW_CODE != 'BSTM' AND p.GLDW_CODE != 'BSTM'</isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ISVALID = '0' AND p.ZZRQ IS NOT NULL </isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ZZRQ &gt;=  '$zzDate$' AND p.ZZRQ &lt;= '$zzDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">p.FIRST_DEPT_PATH like '%$deptCode$%'</isNotEmpty>
		GROUP BY b.DEPTCBBM
		UNION ALL
		SELECT <!-- 查其他单位 -->
			MAX(s.XH) AS xh,
			MAX(s.SHORT_NAME) AS shortName,
			MAX(b.DEPTCBBM) AS deptId,
			MAX(b.DWMCPX) AS deptName,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 1 THEN 1 ELSE 0 END) AS jan,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 2 THEN 1 ELSE 0 END) AS feb,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 3 THEN 1 ELSE 0 END) AS mar,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 4 THEN 1 ELSE 0 END) AS apr,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 5 THEN 1 ELSE 0 END) AS may,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 6 THEN 1 ELSE 0 END) AS jun,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 7 THEN 1 ELSE 0 END) AS jul,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 8 THEN 1 ELSE 0 END) AS aug,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 9 THEN 1 ELSE 0 END) AS sep,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 10 THEN 1 ELSE 0 END) AS oct,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 11 THEN 1 ELSE 0 END) AS nov,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 12 THEN 1 ELSE 0 END) AS dec,
			COUNT(b.APPLY_ID) AS zongshu
		FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b 
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON b.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE p.FLZT='03' AND b.DEL_STATUS='0' AND p.FIRST_DEPT_PATH NOT LIKE '%BGTA00%' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		<isNotEmpty prepend=" AND " property="withOutMs">b.GLDW_CODE != 'BSTM' AND p.GLDW_CODE != 'BSTM'</isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ISVALID = '0' AND p.ZZRQ IS NOT NULL </isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ZZRQ &gt;=  '$zzDate$' AND p.ZZRQ &lt;= '$zzDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">p.FIRST_DEPT_PATH like '%$deptCode$%'</isNotEmpty>
		GROUP BY b.DEPTCBBM ) tmp
		ORDER BY tmp.xh,tmp.deptName ASC
	</select>
	
	<select id="querySqxxFqbm"  parameterClass="hashmap" resultClass="hashmap">
		SELECT <!-- 查部门 -->
			MAX(p.FIRST_DEPT_CODE) AS "deptId",
			MAX(p.FIRST_DEPT_NAME) AS "deptName",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 1 THEN 1 ELSE 0 END) AS "jan",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 2 THEN 1 ELSE 0 END) AS "feb",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 3 THEN 1 ELSE 0 END) AS "mar",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 4 THEN 1 ELSE 0 END) AS "apr",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 5 THEN 1 ELSE 0 END) AS "may",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 6 THEN 1 ELSE 0 END) AS "jun",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 7 THEN 1 ELSE 0 END) AS "jul",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 8 THEN 1 ELSE 0 END) AS "aug",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 9 THEN 1 ELSE 0 END) AS "sep",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 10 THEN 1 ELSE 0 END) AS "oct",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 11 THEN 1 ELSE 0 END) AS "nov",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 12 THEN 1 ELSE 0 END) AS "dec",
			COUNT(b.APPLY_ID) AS "zongshu"
		FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b 
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s1 ON b.DEPTCBBM = s1.DEPT_CODE AND s1.SHORT_TYPE = '1'
		WHERE p.FLZT='03' AND b.DEL_STATUS='0' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		<isNotEmpty prepend=" AND " property="withOutMs">b.GLDW_CODE != 'BSTM' AND p.GLDW_CODE != 'BSTM'</isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ISVALID = '0' AND p.ZZRQ IS NOT NULL </isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ZZRQ &gt;=  '$zzDate$' AND p.ZZRQ &lt;= '$zzDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">p.FIRST_DEPT_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">p.FIRST_DEPT_PATH not like '%BGTAEC00%'</isNotEmpty>
		GROUP BY p.FIRST_DEPT_CODE
		UNION ALL
		SELECT <!-- 化工公司 -->
			MAX(p.FIRST_DEPT_CODE) AS "deptId",
			MAX(p.FIRST_DEPT_NAME) AS "deptName",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 1 THEN 1 ELSE 0 END) AS "jan",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 2 THEN 1 ELSE 0 END) AS "feb",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 3 THEN 1 ELSE 0 END) AS "mar",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 4 THEN 1 ELSE 0 END) AS "apr",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 5 THEN 1 ELSE 0 END) AS "may",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 6 THEN 1 ELSE 0 END) AS "jun",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 7 THEN 1 ELSE 0 END) AS "jul",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 8 THEN 1 ELSE 0 END) AS "aug",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 9 THEN 1 ELSE 0 END) AS "sep",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 10 THEN 1 ELSE 0 END) AS "oct",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 11 THEN 1 ELSE 0 END) AS "nov",
			SUM(CASE WHEN MONTH(p.ZZRQ) = 12 THEN 1 ELSE 0 END) AS "dec",
			COUNT(p.APPLY_ID) AS "zongshu"
		FROM ${zzzcSchema}.T_KIZL_PATENT_INFO_HG p 
		WHERE p.FLZT='03' AND p.DEL_STATUS='0'
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ISVALID = '0' AND p.ZZRQ IS NOT NULL </isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ZZRQ &gt;=  '$zzDate$' AND p.ZZRQ &lt;= '$zzDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">p.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">p.FIRST_DEPT_PATH like '%$deptCode$%'</isNotEmpty>
		GROUP BY p.FIRST_DEPT_CODE
	</select>
	
	<select id="querySqxxFqhj"  parameterClass="hashmap" resultClass="hashmap">
	SELECT 
			'合计' AS "deptName",
			SUM(tmp.jan) AS "jan",
			SUM(tmp.feb) AS "feb",
			SUM(tmp.mar) AS "mar",
			SUM(tmp.apr) AS "apr",
			SUM(tmp.may) AS "may",
			SUM(tmp.jun) AS "jun",
			SUM(tmp.jul) AS "jul",
			SUM(tmp.aug) AS "aug",
			SUM(tmp.sep) AS "sep",
			SUM(tmp.oct) AS "oct",
			SUM(tmp.nov) AS "nov",
			SUM(tmp.dec) AS "dec",
			SUM(tmp.zongshu) AS "zongshu" from
		(SELECT <!-- 化工公司 -->
			'合计' AS deptName,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 1 THEN 1 ELSE 0 END) AS jan,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 2 THEN 1 ELSE 0 END) AS feb,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 3 THEN 1 ELSE 0 END) AS mar,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 4 THEN 1 ELSE 0 END) AS apr,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 5 THEN 1 ELSE 0 END) AS may,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 6 THEN 1 ELSE 0 END) AS jun,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 7 THEN 1 ELSE 0 END) AS jul,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 8 THEN 1 ELSE 0 END) AS aug,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 9 THEN 1 ELSE 0 END) AS sep,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 10 THEN 1 ELSE 0 END) AS oct,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 11 THEN 1 ELSE 0 END) AS nov,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 12 THEN 1 ELSE 0 END) AS dec,
			COUNT(p.APPLY_ID) AS zongshu
		FROM ${zzzcSchema}.T_KIZL_PATENT_INFO_HG p
		WHERE p.FLZT='03' AND p.DEL_STATUS='0'
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ISVALID = '0' AND p.ZZRQ IS NOT NULL </isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ZZRQ &gt;=  '$zzDate$' AND p.ZZRQ &lt;= '$zzDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">p.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">p.FIRST_DEPT_PATH like '%$deptCode$%'</isNotEmpty>
		UNION ALL
		SELECT <!-- 查所放弃合计 -->
			'合计' AS deptName,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 1 THEN 1 ELSE 0 END) AS jan,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 2 THEN 1 ELSE 0 END) AS feb,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 3 THEN 1 ELSE 0 END) AS mar,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 4 THEN 1 ELSE 0 END) AS apr,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 5 THEN 1 ELSE 0 END) AS may,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 6 THEN 1 ELSE 0 END) AS jun,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 7 THEN 1 ELSE 0 END) AS jul,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 8 THEN 1 ELSE 0 END) AS aug,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 9 THEN 1 ELSE 0 END) AS sep,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 10 THEN 1 ELSE 0 END) AS oct,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 11 THEN 1 ELSE 0 END) AS nov,
			SUM(CASE WHEN MONTH(p.ZZRQ) = 12 THEN 1 ELSE 0 END) AS dec,
			COUNT(b.APPLY_ID) AS zongshu
		FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b 
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s ON b.DEPTCBBM = s.DEPT_CODE AND s.SHORT_TYPE = '1'
		WHERE p.FLZT='03' AND b.DEL_STATUS='0' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		<isNotEmpty prepend=" AND " property="withOutMs">b.GLDW_CODE != 'BSTM' AND p.GLDW_CODE != 'BSTM'</isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ISVALID = '0' AND p.ZZRQ IS NOT NULL </isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ZZRQ &gt;=  '$zzDate$' AND p.ZZRQ &lt;= '$zzDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">p.FIRST_DEPT_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">p.FIRST_DEPT_PATH not like '%BGTAEC00%'</isNotEmpty> ) tmp
	</select>
	
	<!-- 放弃明细 -->
	<select id="querySqxxFqmx"  parameterClass="hashmap" resultClass="hashmap">
		SELECT tmp.applyId AS "applyId",
			tmp.deptPathName AS "deptPathName",
			tmp.bgbh AS "bgbh",
			tmp.jsbh AS "jsbh",
			tmp.patentNo AS "patentNo",
			tmp.sqrq AS "sqrq",
			tmp.applyName AS "applyName",
			tmp.zzrq AS "zzrq",
			tmp.sqrId AS "sqrId" from
		(SELECT <!-- 化工公司 -->
			p.APPLY_ID AS applyId,
			MAX(p.FIRST_DEPT_NAME) AS deptPathName,
			MAX(p.BGBH) AS bgbh,
			MAX(p.JSBH) AS jsbh,
			MAX(p.PATENT_NO) AS patentNo,
			MAX(p.SQRQ) AS sqrq,
			MAX(p.APPLY_NAME) AS applyName,
			MAX(p.ZZRQ) AS zzrq,
			COUNT(p.APPLY_ID) AS sqrId
		FROM ${zzzcSchema}.T_KIZL_PATENT_INFO_HG p
		WHERE p.FLZT='03' AND p.DEL_STATUS='0'
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ISVALID = '0' AND p.ZZRQ IS NOT NULL </isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ZZRQ &gt;=  '$zzDate$' AND p.ZZRQ &lt;= '$zzDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">p.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			<isNotEmpty property="isCompany">
				p.FIRST_DEPT_PATH like '%$deptCode$%'
			</isNotEmpty>
			<isNotEmpty property="isDept">
				p.FIRST_DEPT_CODE = '$deptCode$'
			</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="zzDate">MONTH(p.ZZRQ) = '$yf$'</isNotEmpty>
		</isNotEmpty>
		GROUP BY p.APPLY_ID
		UNION ALL
		SELECT 
			b.APPLY_ID AS applyId,
			MAX(b.DEPT_PATH_NAME) AS deptPathName,
			MAX(p.BGBH) AS bgbh,
			MAX(b.JSBH) AS jsbh,
			MAX(p.PATENT_NO) AS patentNo,
			MAX(p.SQRQ) AS sqrq,
			CASE WHEN MAX(p.APPLY_NAME) IS NULL THEN MAX(b.APPLY_NAME) ELSE MAX(p.APPLY_NAME) END AS applyName,
			MAX(p.ZZRQ) AS zzrq,
			COUNT(s.SQR_ID) AS sqrId
		FROM ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b 
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_SQR s ON b.APPLY_ID = s.APPLY_ID
		INNER JOIN ${zzzcSchema}.T_KIZL_MAINTAIN_DEPTSHORTNAME s1 ON b.DEPTCBBM = s1.DEPT_CODE AND s1.SHORT_TYPE = '1'
		WHERE p.FLZT='03' AND b.DEL_STATUS='0' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		<isNotEmpty prepend=" AND " property="withOutMs">b.GLDW_CODE != 'BSTM' AND p.GLDW_CODE != 'BSTM'</isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ISVALID = '0' AND p.ZZRQ IS NOT NULL </isNotEmpty>
		<!-- 统计类型：放弃 --><isNotEmpty prepend=" AND " property="zzDate">p.ZZRQ &gt;=  '$zzDate$' AND p.ZZRQ &lt;= '$zzDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">
			<isNotEmpty property="isCompany">
				p.FIRST_DEPT_PATH like '%$deptCode$%'
			</isNotEmpty>
			<isNotEmpty property="isDept">
				p.FIRST_DEPT_CODE = '$deptCode$'
			</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withOutYjy">p.FIRST_DEPT_PATH not like '%BGTAEC00%'</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="zzDate">MONTH(p.ZZRQ) = '$yf$'</isNotEmpty>
		</isNotEmpty>
		GROUP BY b.APPLY_ID) tmp
		ORDER BY tmp.jsbh ASC
	</select>
	
	<!-- 以下金苹果 -->
	<select id="queryProjectRounds"  parameterClass="hashmap" resultClass="hashmap">
	SELECT 
		DISTINCT
		SUBSTRING(PROJECT_CODE,2,2) AS "projectCode"
		FROM ${kjglSchema}.T_KYXM_PROJECT
		WHERE DEL_STATUS='0' AND extra11 = 'a' AND PROJECT_CODE IS NOT NULL 
		AND MAIN_PROJECT_GUID IS NULL  <!-- 正式机 -->
		GROUP BY PROJECT_CODE
	<!-- SELECT
		DISTINCT
		SUBSTRING(YEAR(SIGN_DATE),3,2) AS projectCode
		FROM ${kjglSchema}.T_KYXM_PROJECT
		WHERE extra11 = 'a' AND PROJECT_CODE IS NOT NULL
		AND MAIN_PROJECT_GUID IS NULL
		GROUP BY SUBSTRING(YEAR(SIGN_DATE),3,2) -->
	</select>
	
	<select id="querySqxxJpg"  parameterClass="hashmap" resultClass="hashmap">
		SELECT <!-- 按金苹果1查询 -->
			s.RECORD_GUID AS "guid", 
			MAX(s.MAIN_PROJECT_GUID) AS "mainProjectGuid", 
			MAX(s.PROJECT_CODE) AS "projectCode", 
			MAX(s.PROJECT_NAME) AS "projectName", 
			MAX(s.FZR) AS "fzr", 
			MAX(s.FZR_NAME) AS "fzrName", 
			SUM(CASE WHEN 
			<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 1</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "jan",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 2</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 2</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "feb",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 3</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 3</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "mar",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 4</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 4</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "apr",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 5</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 5</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "may",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 6</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 6</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "jun",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 7</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 7</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "jul",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 8</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 8</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "aug",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 9</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 9</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "sep",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 10</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 10</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "oct",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 11</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 11</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "nov",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 12</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 12</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "dec",
			SUM(t.EXTRA2)/100 AS "tongji"
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${kjglSchema}.T_KYXM_PROJECT s ON s.PROJECT_CODE = b.FROM_NO AND s.EXTRA11 = 'a' AND s.DEL_STATUS='0'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		<isNotEmpty prepend=" AND " property="withOutMs">b.GLDW_CODE != 'BSTM' AND p.GLDW_CODE != 'BSTM'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withInMs">b.GLDW_CODE = 'BSTM' AND p.GLDW_CODE = 'BSTM'</isNotEmpty>
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sclx">SUBSTRING(s.PROJECT_CODE,2,2) =  '$sclx$'</isNotEmpty>
		GROUP BY s.RECORD_GUID
	</select>
	
	<select id="querySqxxJpgMonth"  parameterClass="hashmap" resultClass="hashmap">
		SELECT <!-- 按金苹果1查询所有组织月度申请、授权量 -->
			<isNotEmpty property="applyDate">'月申请量' </isNotEmpty>
			<isNotEmpty property="ljDate">'月授权量'</isNotEmpty>
			<isNotEmpty property="authDate">'月授权量'</isNotEmpty> 
			<isNotEmpty property="reauthDate">'月授权量'</isNotEmpty> AS "projectName",
			SUM(CASE WHEN 
			<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) = 1</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 1</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "jan",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 2</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 2</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 2</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "feb",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 3</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 3</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 3</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "mar",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 4</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 4</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 4</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "apr",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 5</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 5</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 5</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "may",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 6</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 6</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 6</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "jun",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 7</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 7</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 7</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "jul",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 8</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 8</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 8</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "aug",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 9</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 9</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 9</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "sep",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 10</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 10</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 10</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "oct",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 11</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 11</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 11</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "nov",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) = 12</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) = 12</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) = 12</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "dec",
			SUM(t.EXTRA2)/100 AS "tongji"
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${kjglSchema}.T_KYXM_PROJECT s ON s.PROJECT_CODE = b.FROM_NO AND s.EXTRA11 = 'a' AND s.DEL_STATUS='0'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		<isNotEmpty prepend=" AND " property="withOutMs">b.GLDW_CODE != 'BSTM' AND p.GLDW_CODE != 'BSTM'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withInMs">b.GLDW_CODE = 'BSTM' AND p.GLDW_CODE = 'BSTM'</isNotEmpty>
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sclx">SUBSTRING(s.PROJECT_CODE,2,2) =  '$sclx$'</isNotEmpty>
	</select>
	
	<select id="querySqxxJpgQuarter"  parameterClass="hashmap" resultClass="hashmap">
		SELECT <!-- 按金苹果1查询所有组织季度申请、授权量 -->
			<isNotEmpty property="applyDate">'季度申请量' </isNotEmpty>
			<isNotEmpty property="ljDate">'季度授权量'</isNotEmpty> 
			<isNotEmpty property="authDate">'季度授权量'</isNotEmpty> 
			<isNotEmpty property="reauthDate">'季度授权量'</isNotEmpty> AS "projectName",
			SUM(CASE WHEN 
			<!-- 统计类型：申请 --><isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (1,2,3) </isNotEmpty>
			<!-- 统计类型：授权有效累计 --><isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (1,2,3)</isNotEmpty>
			<!-- 统计类型：授权日 --><isNotEmpty property="authDate">MONTH(p.SQRQ) IN (1,2,3)</isNotEmpty>
			<!-- 统计类型：预定授权日 --><isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (1,2,3)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "feb",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (4,5,6)</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (4,5,6)</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) IN (4,5,6)</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (4,5,6)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "may",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (7,8,9)</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (7,8,9)</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) IN (7,8,9)</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (7,8,9)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "aug",
			SUM(CASE WHEN 
			<isNotEmpty property="applyDate">MONTH(p.SLRQ) IN (10,11,12)</isNotEmpty>
			<isNotEmpty property="ljDate">MONTH(p.SQRQ) IN (10,11,12)</isNotEmpty>
			<isNotEmpty property="authDate">MONTH(p.SQRQ) IN (10,11,12)</isNotEmpty>
			<isNotEmpty property="reauthDate">MONTH(p.DJR_DATE) IN (10,11,12)</isNotEmpty> THEN t.EXTRA2 ELSE 0 END)/100 AS "nov",
			SUM(t.EXTRA2)/100 AS "tongji"
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${kjglSchema}.T_KYXM_PROJECT s ON s.PROJECT_CODE = b.FROM_NO AND s.EXTRA11 = 'a' AND s.DEL_STATUS='0'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		<isNotEmpty prepend=" AND " property="withOutMs">b.GLDW_CODE != 'BSTM' AND p.GLDW_CODE != 'BSTM'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withInMs">b.GLDW_CODE = 'BSTM' AND p.GLDW_CODE = 'BSTM'</isNotEmpty>
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sclx">SUBSTRING(s.PROJECT_CODE,2,2) =  '$sclx$'</isNotEmpty>
	</select>
	
	<select id="querySqxxJpgmx"  parameterClass="hashmap" resultClass="hashmap">
		SELECT <!-- 按金苹果1查询 -->
			s.PROJECT_CODE AS "projectCode", 
			s.PROJECT_NAME AS "projectName", 
			s.FZR_NAME AS "fzrName", 
			t.PROJECT_NAME AS "teamName"
		FROM ${kjglSchema}.T_KYXM_PROJECT s 
		LEFT JOIN ${kjglSchema}.T_KYXM_PROJECT t ON s.MAIN_PROJECT_GUID = t.RECORD_GUID
		WHERE s.DEL_STATUS='0' AND s.EXTRA11 = 'a' 
		<isNotEmpty prepend=" AND " property="guid">s.RECORD_GUID = #guid#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="mainProjectGuid">s.MAIN_PROJECT_GUID = #mainProjectGuid#</isNotEmpty>
	</select>
	
	<select id="querySqxxJpgzlmx"  parameterClass="hashmap" resultClass="hashmap">
		SELECT <!-- 按金苹果1查询 -->
			b.APPLY_ID AS "applyId",
			MAX(b.JSBH) AS "jsbh",
			MAX(p.BGBH) AS "bgbh",
			MAX(p.PATENT_NO) AS "patentNo",
			CASE WHEN MAX(p.APPLY_NAME) IS NULL THEN MAX(b.APPLY_NAME) ELSE MAX(p.APPLY_NAME) END AS "applyName",
			SUM(t.EXTRA2)/100 AS "gxxs",
			MAX(s.PROJECT_CODE) AS "projectCode", 
			MAX(s.PROJECT_NAME) AS "projectName"
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${kjglSchema}.T_KYXM_PROJECT s ON s.PROJECT_CODE = b.FROM_NO AND s.EXTRA11 = 'a' AND s.DEL_STATUS='0'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		<isNotEmpty prepend=" AND " property="withOutMs">b.GLDW_CODE != 'BSTM' AND p.GLDW_CODE != 'BSTM'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withInMs">b.GLDW_CODE = 'BSTM' AND p.GLDW_CODE = 'BSTM'</isNotEmpty>
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sclx">SUBSTRING(s.PROJECT_CODE,2,2) =  '$sclx$'</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="projectCode">b.FROM_NO =  '$projectCode$'</isNotEmpty>
		GROUP BY b.APPLY_ID
		ORDER BY MAX(b.JSBH) ASC
	</select>
	
	<select id="querySqxxJpgzlhj"  parameterClass="hashmap" resultClass="hashmap">
		SELECT <!-- 按金苹果1查询 合计 -->
			'合计' AS "jsbh",
			SUM(t.EXTRA2)/100 AS "gxxs"
		FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
		LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO b ON b.APPLY_ID = t.APPLY_ID AND b.DEL_STATUS='0'
		LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO p ON b.APPLY_ID = p.APPLY_ID AND p.DEL_STATUS='0'
		INNER JOIN ${kjglSchema}.T_KYXM_PROJECT s ON s.PROJECT_CODE = b.FROM_NO AND s.EXTRA11 = 'a' AND s.DEL_STATUS='0'
		WHERE t.DEL_STATUS='0' AND t.EXTRA1 = '1' AND b.GLDW_CODE != 'BGPV' AND p.GLDW_CODE != 'BGPV' 
		<isNotEmpty prepend=" AND " property="withOutMs">b.GLDW_CODE != 'BSTM' AND p.GLDW_CODE != 'BSTM'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="withInMs">b.GLDW_CODE = 'BSTM' AND p.GLDW_CODE = 'BSTM'</isNotEmpty>
		<!-- 统计类型：申请 --><!-- <isNotEmpty prepend=" AND " property="applyDate">(p.ISVALID IS NULL OR p.ISVALID != '0') </isNotEmpty> -->
		<!-- 统计类型：授权有效累计 --><isNotEmpty prepend=" AND " property="ljDate">p.ISVALID = '1' AND p.FLZT = '10' </isNotEmpty>
		<!-- 统计类型：申请 --><isNotEmpty prepend=" AND " property="applyDate">p.SLRQ &gt;=  '$applyDate$' AND p.SLRQ &lt;= '$applyDateEd$'</isNotEmpty>
		<!-- 统计类型：授权日 --><isNotEmpty prepend=" AND " property="authDate">p.SQRQ &gt;=  '$authDate$' AND p.SQRQ &lt;= '$authDateEd$'</isNotEmpty>
		<!-- 统计类型：预定授权日 --><isNotEmpty prepend=" AND " property="reauthDate">p.SQRQ IS NOT NULL AND p.DJR_DATE &gt;=  '$reauthDate$' AND p.DJR_DATE &lt;= '$reauthDateEd$'</isNotEmpty>
		<!-- 专利类型：发明  实用新型  外观专利 --><isNotEmpty prepend=" AND " property="patentType">p.PATENT_TYPE =  '$patentType$'</isNotEmpty>
		<!-- 专利类型衍生：钢铁产品 --><isNotEmpty prepend=" AND " property="gtcp">b.EXTRA4 = '1'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="deptCode">t.CODE_PATH like '%$deptCode$%'</isNotEmpty>
		<isNotEmpty prepend=" AND " property="sclx">SUBSTRING(s.PROJECT_CODE,2,2) =  '$sclx$'</isNotEmpty>
		<isNotEmpty property="yf">
			<isNotEmpty prepend=" AND " property="applyDate">MONTH(p.SLRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="authDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ljDate">MONTH(p.SQRQ) = '$yf$'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reauthDate">MONTH(p.DJR_DATE) = '$yf$'</isNotEmpty>
		</isNotEmpty>
		<isNotEmpty prepend=" AND " property="projectCode">b.FROM_NO =  '$projectCode$'</isNotEmpty>
	</select>

	<select id="countGroupByPatentType" parameterClass="hashmap" resultClass="hashmap">
		select
		PATENT_TYPE as "patentType",
		count(1) as "count"
		from ${zzzcSchema}.T_KIZL_PATENT_INFO
		<dynamic prepend=" where ">
			<isEqual prepend=" and " property="rangeField" compareValue="slrq">
				SLRQ between #startDate# and #endDate#
			</isEqual>
			<isEqual prepend=" and " property="rangeField" compareValue="sqrq">
				SQRQ between #startDate# and #endDate#
			</isEqual>
			<isNotEmpty prepend=" and " property="gldwCode">
				GLDW_CODE = #gldwCode#
			</isNotEmpty>
		</dynamic>
		group by PATENT_TYPE
	</select>

	<select id="countGroupByPatentTypeAndImplStatus" parameterClass="hashmap" resultClass="hashmap">
		select
		PATENT_TYPE as "patentType",
		case USE_METHOD when '04' then 0 else 1 end as "implStatus",
		count(1) as "count"
		from ${zzzcSchema}.T_KIZL_PATENT_INFO
		<dynamic prepend=" where ">
			<isEqual prepend=" and " property="rangeField" compareValue="slrq">
				SLRQ between #startDate# and #endDate#
			</isEqual>
			<isEqual prepend=" and " property="rangeField" compareValue="sqrq">
				SQRQ between #startDate# and #endDate#
			</isEqual>
			<isNotEmpty prepend=" and " property="gldwCode">
				GLDW_CODE = #gldwCode#
			</isNotEmpty>
			<isNotEmpty prepend=" and " property="isvalid">
				ISVALID = #isvalid#
			</isNotEmpty>
		</dynamic>
		group by PATENT_TYPE, case USE_METHOD when '04' then 0 else 1 end
	</select>

	<select id="queryJpgProject"  parameterClass="hashmap" resultClass="hashmap">
		SELECT
		RECORD_GUID  as "recordGuid" ,
		SERIAL_NO  as "serialNo" ,
		PROJECT_CODE  as "projectCode" ,
		PROJECT_NAME  as "projectName"
		FROM KJGL.T_KYXM_PROJECT
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="anythingLike">(PROJECT_CODE like '%$anythingLike$%' OR PROJECT_NAME like '%$anythingLike$%')</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCode">PROJECT_CODE like '%$projectCode$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">PROJECT_NAME like '%$projectName$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11">EXTRA11 =  #extra11#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

</sqlMap>