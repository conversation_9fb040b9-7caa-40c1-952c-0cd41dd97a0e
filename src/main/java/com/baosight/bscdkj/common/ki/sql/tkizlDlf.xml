<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlDlf">
    <typeAlias alias="tkizlDlf" type="com.baosight.bscdkj.common.ki.domain.TkizlDlf"/>
    <select id="load" parameterClass="string" resultClass="tkizlDlf">
        SELECT
            DLF_ID as "dlfId" ,
            GLD<PERSON>_CODE as "gldwCode" ,
            GLDW_NAME as "gldwName" ,
            SWS_ID as "swsId" ,
            SWS_NAME as "swsName" ,
            PATENT_TYPE as "patentType" ,
            DLF_AMOUNT as "dlfAmount" ,
            DLF_YEAR as "dlfYear" ,
            LOCAT<PERSON> as "location" ,
            <PERSON>XT<PERSON><PERSON> as "extra1" ,
            EXTRA2 as "extra2" ,
            EXTRA<PERSON> as "extra3" ,
            <PERSON>XTRA<PERSON> as "extra4" ,
            EXTRA<PERSON> as "extra5" ,
            DEL_STATUS as "delStatus" ,
            CREATE_USER_LABEL as "createUserLabel" ,
            CREATE_DATE as "createDate" ,
            UPDATE_USER_LABEL as "updateUserLabel" ,
            UPDATE_DATE as "updateDate" ,
            DELETE_USER_LABEL as "deleteUserLabel" ,
            DELETE_DATE as "deleteDate" ,
            RECORD_VERSION as "recordVersion"

        FROM ${zzzcSchema}.T_KIZL_DLF
        WHERE   DLF_ID=#value#
    </select>

    <select id="query"  parameterClass="hashmap" resultClass="tkizlDlf">
        SELECT
        DLF_ID as "dlfId" ,
        GLDW_CODE as "gldwCode" ,
        GLDW_NAME as "gldwName" ,
        SWS_ID as "swsId" ,
        SWS_NAME as "swsName" ,
        PATENT_TYPE as "patentType" ,
        DLF_AMOUNT as "dlfAmount" ,
        DLF_YEAR as "dlfYear" ,
        LOCATION as "location" ,
        EXTRA1 as "extra1" ,
        EXTRA2 as "extra2" ,
        EXTRA3 as "extra3" ,
        EXTRA4 as "extra4" ,
        EXTRA5 as "extra5" ,
        DEL_STATUS as "delStatus" ,
        CREATE_USER_LABEL as "createUserLabel" ,
        CREATE_DATE as "createDate" ,
        UPDATE_USER_LABEL as "updateUserLabel" ,
        UPDATE_DATE as "updateDate" ,
        DELETE_USER_LABEL as "deleteUserLabel" ,
        DELETE_DATE as "deleteDate" ,
        RECORD_VERSION as "recordVersion"
        FROM ${zzzcSchema}.T_KIZL_DLF
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="dlfId">DLF_ID =  #dlfId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsId">SWS_ID =  #swsId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsNameLike">SWS_NAME like  '%$swsNameLike$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dlfAmount">DLF_AMOUNT =  #dlfAmount#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dlfYear">DLF_YEAR =  #dlfYear#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="location">LOCATION =  #location#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
            <isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
    </select>

    <select id="count"  parameterClass="hashmap" resultClass="integer">
        SELECT count(*)
        FROM ${zzzcSchema}.T_KIZL_DLF
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="dlfId">DLF_ID =  #dlfId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsId">SWS_ID =  #swsId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsNameLike">SWS_NAME like  '%$swsNameLike$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dlfAmount">DLF_AMOUNT =  #dlfAmount#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dlfYear">DLF_YEAR =  #dlfYear#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="location">LOCATION =  #location#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
            <isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
    </select>

    <insert id="insert" parameterClass="tkizlDlf">
        INSERT INTO ${zzzcSchema}.T_KIZL_DLF (
        <dynamic prepend=" ">
            <isNotNull prepend=", " property="dlfId">DLF_ID </isNotNull>
            <isNotNull prepend=", " property="swsId">SWS_ID </isNotNull>
            <isNotNull prepend=", " property="swsName">SWS_NAME </isNotNull>
            <isNotNull prepend=", " property="gldwCode">GLDW_CODE </isNotNull>
            <isNotNull prepend=", " property="gldwName">GLDW_NAME </isNotNull>
            <isNotNull prepend=", " property="patentType">PATENT_TYPE </isNotNull>
            <isNotNull prepend=", " property="dlfAmount">DLF_AMOUNT </isNotNull>
            <isNotNull prepend=", " property="dlfYear">DLF_YEAR </isNotNull>
            <isNotNull prepend=", " property="location">LOCATION </isNotNull>
            <isNotNull prepend=", " property="extra1">EXTRA1 </isNotNull>
            <isNotNull prepend=", " property="extra2">EXTRA2 </isNotNull>
            <isNotNull prepend=", " property="extra3">EXTRA3 </isNotNull>
            <isNotNull prepend=", " property="extra4">EXTRA4 </isNotNull>
            <isNotNull prepend=", " property="extra5">EXTRA5 </isNotNull>
            <isNotNull prepend=", " property="delStatus">DEL_STATUS </isNotNull>
            <isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL </isNotNull>
            <isNotNull prepend=", " property="createDate">CREATE_DATE </isNotNull>
            <isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL </isNotNull>
            <isNotNull prepend=", " property="updateDate">UPDATE_DATE </isNotNull>
            <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL </isNotNull>
            <isNotNull prepend=", " property="deleteDate">DELETE_DATE </isNotNull>
            <isNotNull prepend=", " property="recordVersion">RECORD_VERSION </isNotNull>

        </dynamic>
        ) VALUES (
        <dynamic prepend=" ">
            <isNotNull prepend=", " property="dlfId">#dlfId#</isNotNull>
            <isNotNull prepend=", " property="swsId">#swsId#</isNotNull>
            <isNotNull prepend=", " property="swsName">#swsName#</isNotNull>
            <isNotNull prepend=", " property="gldwCode">#gldwCode#</isNotNull>
            <isNotNull prepend=", " property="gldwName">#gldwName#</isNotNull>
            <isNotNull prepend=", " property="patentType">#patentType#</isNotNull>
            <isNotNull prepend=", " property="dlfAmount">#dlfAmount#</isNotNull>
            <isNotNull prepend=", " property="dlfYear">#dlfYear#</isNotNull>
            <isNotNull prepend=", " property="location">#location#</isNotNull>
            <isNotNull prepend=", " property="extra1">#extra1#</isNotNull>
            <isNotNull prepend=", " property="extra2">#extra2#</isNotNull>
            <isNotNull prepend=", " property="extra3">#extra3#</isNotNull>
            <isNotNull prepend=", " property="extra4">#extra4#</isNotNull>
            <isNotNull prepend=", " property="extra5">#extra5#</isNotNull>
            <isNotNull prepend=", " property="delStatus">#delStatus#</isNotNull>
            <isNotNull prepend=", " property="createUserLabel">#createUserLabel#</isNotNull>
            <isNotNull prepend=", " property="createDate">
                <isNotEmpty property="createDate">#createDate#</isNotEmpty>
                <isEmpty property="createDate">NULL</isEmpty>
            </isNotNull>
            <isNotNull prepend=", " property="updateUserLabel">#updateUserLabel#</isNotNull>
            <isNotNull prepend=", " property="updateDate">
                <isNotEmpty property="updateDate">#updateDate#</isNotEmpty>
                <isEmpty property="updateDate">NULL</isEmpty>
            </isNotNull>
            <isNotNull prepend=", " property="deleteUserLabel">#deleteUserLabel#</isNotNull>
            <isNotNull prepend=", " property="deleteDate">
                <isNotEmpty property="deleteDate">#deleteDate#</isNotEmpty>
                <isEmpty property="deleteDate">NULL</isEmpty>
            </isNotNull>
            <isNotNull prepend=", " property="recordVersion">#recordVersion#</isNotNull>
        </dynamic>)
    </insert>

    <delete id="delete" parameterClass="string">
        DELETE FROM  ${zzzcSchema}.T_KIZL_DLF
        WHERE 		DLF_ID=#value#
    </delete>

    <delete id="deleteByC" parameterClass="hashmap">
        DELETE FROM  ${zzzcSchema}.T_KIZL_DLF
        WHERE
        <dynamic prepend=" ">
            <isNotNull prepend=" AND " property="dlfId">DLF_ID = #dlfId#</isNotNull>
            <isNotNull prepend=" AND " property="swsId">SWS_ID = #swsId#</isNotNull>
            <isNotNull prepend=" AND " property="swsName">SWS_NAME = #swsName#</isNotNull>
            <isNotNull prepend=" AND " property="gldwCode">GLDW_CODE = #gldwCode#</isNotNull>
            <isNotNull prepend=" AND " property="gldwName">GLDW_NAME = #gldwName#</isNotNull>
            <isNotNull prepend=" AND " property="patentType">PATENT_TYPE = #patentType#</isNotNull>
            <isNotNull prepend=" AND " property="dlfAmount">DLF_AMOUNT = #dlfAmount#</isNotNull>
            <isNotNull prepend=" AND " property="dlfYear">DLF_YEAR = #dlfYear#</isNotNull>
            <isNotNull prepend=" AND " property="location">LOCATION = #location#</isNotNull>
            <isNotNull prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotNull>
            <isNotNull prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotNull>
            <isNotNull prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotNull>
            <isNotNull prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotNull>
            <isNotNull prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotNull>
            <isNotNull prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
            <isNotNull prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
            <isNotNull prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotNull>
            <isNotNull prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
            <isNotNull prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
            <isNotNull prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
            <isNotNull prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
            <isNotNull prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
        </dynamic>
    </delete>

    <update id="update" parameterClass="tkizlDlf">
        UPDATE  ${zzzcSchema}.T_KIZL_DLF
        SET
        <dynamic prepend=" ">
            <isNotNull prepend="," property="dlfId">DLF_ID = #dlfId#</isNotNull>
            <isNotNull prepend="," property="swsId">SWS_ID = #swsId#</isNotNull>
            <isNotNull prepend="," property="swsName">SWS_NAME = #swsName#</isNotNull>
            <isNotNull prepend="," property="gldwCode">GLDW_CODE = #gldwCode#</isNotNull>
            <isNotNull prepend="," property="gldwName">GLDW_NAME = #gldwName#</isNotNull>
            <isNotNull prepend="," property="patentType">PATENT_TYPE = #patentType#</isNotNull>
            <isNotNull prepend="," property="dlfAmount">DLF_AMOUNT = #dlfAmount#</isNotNull>
            <isNotNull prepend="," property="dlfYear">DLF_YEAR = #dlfYear#</isNotNull>
            <isNotNull prepend="," property="location">LOCATION = #location#</isNotNull>
            <isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
            <isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
            <isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
            <isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
            <isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
            <isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
            <isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
            <isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
            <isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
            <isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
            <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
            <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
            <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
            <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
            <isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
        </dynamic>
        WHERE 		DLF_ID=#dlfId# 			</update>

    <update id="updatewithnull" parameterClass="tkizlDlf">
        UPDATE ${zzzcSchema}.T_KIZL_DLF
        SET
        <dynamic prepend=" ">
            <isNotNull prepend=", " property="dlfId">DLF_ID = #dlfId#</isNotNull>
            <isNull prepend=", " property="dlfId">DLF_ID = NULL</isNull>
            <isNotNull prepend=", " property="swsId">SWS_ID = #swsId#</isNotNull>
            <isNull prepend=", " property="swsId">SWS_ID = NULL</isNull>
            <isNotNull prepend=", " property="swsName">SWS_NAME = #swsName#</isNotNull>
            <isNull prepend=", " property="swsName">SWS_NAME = NULL</isNull>
            <isNotNull prepend=", " property="gldwCode">GLDW_CODE = #gldwCode#</isNotNull>
            <isNull prepend=", " property="gldwCode">GLDW_CODE = NULL</isNull>
            <isNotNull prepend=", " property="gldwName">GLDW_NAME = #gldwName#</isNotNull>
            <isNull prepend=", " property="gldwName">GLDW_NAME = NULL</isNull>
            <isNotNull prepend=", " property="patentType">PATENT_TYPE = #patentType#</isNotNull>
            <isNull prepend=", " property="patentType">PATENT_TYPE = NULL</isNull>
            <isNotNull prepend=", " property="dlfAmount">DLF_AMOUNT = #dlfAmount#</isNotNull>
            <isNull prepend=", " property="dlfAmount">DLF_AMOUNT = NULL</isNull>
            <isNotNull prepend=", " property="dlfYear">DLF_YEAR = #dlfYear#</isNotNull>
            <isNull prepend=", " property="dlfYear">DLF_YEAR = NULL</isNull>
            <isNotNull prepend=", " property="location">LOCATION = #location#</isNotNull>
            <isNull prepend=", " property="location">LOCATION = NULL</isNull>
            <isNotNull prepend=", " property="extra1">EXTRA1 = #extra1#</isNotNull>
            <isNull prepend=", " property="extra1">EXTRA1 = NULL</isNull>
            <isNotNull prepend=", " property="extra2">EXTRA2 = #extra2#</isNotNull>
            <isNull prepend=", " property="extra2">EXTRA2 = NULL</isNull>
            <isNotNull prepend=", " property="extra3">EXTRA3 = #extra3#</isNotNull>
            <isNull prepend=", " property="extra3">EXTRA3 = NULL</isNull>
            <isNotNull prepend=", " property="extra4">EXTRA4 = #extra4#</isNotNull>
            <isNull prepend=", " property="extra4">EXTRA4 = NULL</isNull>
            <isNotNull prepend=", " property="extra5">EXTRA5 = #extra5#</isNotNull>
            <isNull prepend=", " property="extra5">EXTRA5 = NULL</isNull>
            <isNotNull prepend=", " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
            <isNull prepend=", " property="delStatus">DEL_STATUS = NULL</isNull>
            <isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
            <isNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = NULL</isNull>
            <isNotNull prepend=", " property="createDate">CREATE_DATE = #createDate#</isNotNull>
            <isNull prepend=", " property="createDate">CREATE_DATE = NULL</isNull>
            <isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
            <isNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = NULL</isNull>
            <isNotNull prepend=", " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
            <isNull prepend=", " property="updateDate">UPDATE_DATE = NULL</isNull>
            <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
            <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
            <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
            <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
            <isNotNull prepend=", " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
            <isNull prepend=", " property="recordVersion">RECORD_VERSION = NULL</isNull>
        </dynamic>
        WHERE 		DLF_ID=#dlfId# 			</update>

    <update id="updateByC" parameterClass="hashmap">
        UPDATE  ${zzzcSchema}.T_KIZL_DLF
        SET
        <dynamic prepend=" ">
            <isNotNull prepend="," property="dlfId">DLF_ID = #dlfId#</isNotNull>
            <isNotNull prepend="," property="swsId">SWS_ID = #swsId#</isNotNull>
            <isNotNull prepend="," property="swsName">SWS_NAME = #swsName#</isNotNull>
            <isNotNull prepend="," property="gldwCode">GLDW_CODE = #gldwCode#</isNotNull>
            <isNotNull prepend="," property="gldwName">GLDW_NAME = #gldwName#</isNotNull>
            <isNotNull prepend="," property="patentType">PATENT_TYPE = #patentType#</isNotNull>
            <isNotNull prepend="," property="dlfAmount">DLF_AMOUNT = #dlfAmount#</isNotNull>
            <isNotNull prepend="," property="dlfYear">DLF_YEAR = #dlfYear#</isNotNull>
            <isNotNull prepend="," property="location">LOCATION = #location#</isNotNull>
            <isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
            <isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
            <isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
            <isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
            <isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
            <isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
            <isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
            <isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
            <isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
            <isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
            <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
            <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
            <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
            <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
            <isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>

        </dynamic>
        <dynamic prepend=" WHERE ">
            <isNotNull prepend=" AND " property="dlfIdOld">DLF_ID = #dlfIdOld#</isNotNull>
            <isNotNull prepend=" AND " property="swsIdOld">SWS_ID = #swsIdOld#</isNotNull>
            <isNotNull prepend=" AND " property="swsNameOld">SWS_NAME = #swsNameOld#</isNotNull>
            <isNotNull prepend=" AND " property="gldwCodeOld">GLDW_CODE = #gldwCodeOld#</isNotNull>
            <isNotNull prepend=" AND " property="gldwNameOld">GLDW_NAME = #gldwNameOld#</isNotNull>
            <isNotNull prepend=" AND " property="patentTypeOld">PATENT_TYPE = #patentTypeOld#</isNotNull>
            <isNotNull prepend=" AND " property="dlfAmountOld">DLF_AMOUNT = #dlfAmountOld#</isNotNull>
            <isNotNull prepend=" AND " property="dlfYearOld">DLF_YEAR = #dlfYearOld#</isNotNull>
            <isNotNull prepend=" AND " property="locationOld">LOCATION = #locationOld#</isNotNull>
            <isNotNull prepend=" AND " property="extra1Old">EXTRA1 = #extra1Old#</isNotNull>
            <isNotNull prepend=" AND " property="extra2Old">EXTRA2 = #extra2Old#</isNotNull>
            <isNotNull prepend=" AND " property="extra3Old">EXTRA3 = #extra3Old#</isNotNull>
            <isNotNull prepend=" AND " property="extra4Old">EXTRA4 = #extra4Old#</isNotNull>
            <isNotNull prepend=" AND " property="extra5Old">EXTRA5 = #extra5Old#</isNotNull>
            <isNotNull prepend=" AND " property="delStatusOld">DEL_STATUS = #delStatusOld#</isNotNull>
            <isNotNull prepend=" AND " property="createUserLabelOld">CREATE_USER_LABEL = #createUserLabelOld#</isNotNull>
            <isNotNull prepend=" AND " property="createDateOld">CREATE_DATE = #createDateOld#</isNotNull>
            <isNotNull prepend=" AND " property="updateUserLabelOld">UPDATE_USER_LABEL = #updateUserLabelOld#</isNotNull>
            <isNotNull prepend=" AND " property="updateDateOld">UPDATE_DATE = #updateDateOld#</isNotNull>
            <isNotNull prepend=" AND " property="deleteUserLabelOld">DELETE_USER_LABEL = #deleteUserLabelOld#</isNotNull>
            <isNotNull prepend=" AND " property="deleteDateOld">DELETE_DATE = #deleteDateOld#</isNotNull>
            <isNotNull prepend=" AND " property="recordVersionOld">RECORD_VERSION = #recordVersionOld#</isNotNull>
            <isNotNull prepend=" AND " property="dynSqlOld"> $dynSqlOld$ </isNotNull>
        </dynamic>
    </update>

    <update id="updateNull" parameterClass="hashmap">
        UPDATE  ${zzzcSchema}.T_KIZL_DLF
        SET
        <dynamic prepend=" ">
            <isNotNull prepend="," property="dlfId">DLF_ID = #dlfId#</isNotNull>
            <isNotNull prepend="," property="swsId">SWS_ID = #swsId#</isNotNull>
            <isNotNull prepend="," property="swsName">SWS_NAME = #swsName#</isNotNull>
            <isNotNull prepend="," property="gldwCode">GLDW_CODE = #gldwCode#</isNotNull>
            <isNotNull prepend="," property="gldwName">GLDW_NAME = #gldwName#</isNotNull>
            <isNotNull prepend="," property="patentType">PATENT_TYPE = #patentType#</isNotNull>
            <isNotNull prepend="," property="dlfAmount">DLF_AMOUNT = #dlfAmount#</isNotNull>
            <isNotNull prepend="," property="dlfYear">DLF_YEAR = #dlfYear#</isNotNull>
            <isNotNull prepend="," property="location">LOCATION = #location#</isNotNull>
            <isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
            <isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
            <isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
            <isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
            <isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
            <isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
            <isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
            <isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
            <isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
            <isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
            <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
            <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
            <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
            <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
            <isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
        </dynamic>
        WHERE 		DLF_ID=#dlfId# 			</update>
</sqlMap>