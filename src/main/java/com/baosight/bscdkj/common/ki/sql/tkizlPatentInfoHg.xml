<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlPatentInfoHg">
	<typeAlias alias="tKIZLPatentInfoHg" type="com.baosight.bscdkj.common.ki.domain.TkizlPatentInfoHg"/>
	<select id="load" parameterClass="string" resultClass="tKIZLPatentInfoHg">
		SELECT 
				PATENT_ID as "patentId" ,		
				APPLY_ID as "applyId" ,		
				SERIAL_NUM as "serialNum" ,		
				JSBH as "jsbh" ,		
				BGBH as "bgbh" ,		
				APPLY_NAME as "applyName" ,		
				FIRST_DEPT_CODE as "firstDeptCode" ,		
				FIRST_DEPT_NAME as "firstDeptName" ,		
				GLDW_CODE as "gldwCode" ,		
				GLDW_NAME as "gldwName" ,		
				LABEL as "label" ,		
				TECH_AREA as "techArea" ,		
				USE_PROPOSE as "usePropose" ,		
				KNOWLEDGE_CLASS as "knowledgeClass" ,		
				LXR_CODE as "lxrCode" ,		
				LXR_NAME as "lxrName" ,		
				LXR_PHONE as "lxrPhone" ,		
				LXR_EMAIL as "lxrEmail" ,		
				LXR_MOBILE as "lxrMobile" ,		
				FROM_TYPE as "fromType" ,		
				FROM_NO as "fromNo" ,		
				FROM_NAME as "fromName" ,		
				FROM_CONTENT as "fromContent" ,		
				USE_METHOD as "useMethod" ,		
				USE_DEPT as "useDept" ,		
				USE_DEPT_NAME as "useDeptName" ,		
				USE_EXPECTED as "useExpected" ,		
				USE_EXPECTED_NAME as "useExpectedName" ,		
				USE_FIRSTDATE as "useFirstdate" ,		
				REASON_NOUSE as "reasonNouse" ,		
				CONTENT_NOUSE as "contentNouse" ,		
				JPG_FLAG as "jpgFlag" ,		
				JPG_XH as "jpgXh" ,		
				JPG_GW as "jpgGw" ,		
				JPG_TEAM as "jpgTeam" ,		
				ISBIGXM as "isbigxm" ,		
				JFDW_CODE as "jfdwCode" ,		
				FLOW_STATUS as "flowStatus" ,		
				PATENT_STATUS as "patentStatus" ,		
				DJD_PERSON as "djdPerson" ,		
				DJD_DATE as "djdDate" ,		
				JD_PERSON as "jdPerson" ,		
				JD_DATE as "jdDate" ,		
				QS as "qs" ,		
				SLRQ as "slrq" ,		
				PATENT_NO as "patentNo" ,		
				PATENT_TYPE as "patentType" ,		
				YXQR as "yxqr" ,		
				QLYQSL as "qlyqsl" ,		
				SMSYS as "smsys" ,		
				SWS_GUID as "swsGuid" ,		
				SWSDLR as "swsdlr" ,		
				SWSDLR_PHONE as "swsdlrPhone" ,		
				SWSDLR_EMAIL as "swsdlrEmail" ,		
				MONEY_DLF as "moneyDlf" ,		
				MONEY_QF as "moneyQf" ,		
				MONEY_SQF as "moneySqf" ,		
				MONEY_GBYSF as "moneyGbysf" ,		
				MONEY_SMSFJF as "moneySmsfjf" ,		
				MONEY_YHS as "moneyYhs" ,		
				SQTZ_FWDATE as "sqtzFwdate" ,		
				MONEY_FIRST as "moneyFirst" ,		
				ZLH as "zlh" ,		
				SQRQ as "sqrq" ,		
				FLZT as "flzt" ,		
				ISVALID as "isvalid" ,		
				ISGD_ARCHIVE as "isgdArchive" ,		
				ARCHIVE_GUID as "archiveGuid" ,		
				ISGD_DZ as "isgdDz" ,		
				EXTRA1 as "extra1" ,		
				EXTRA2 as "extra2" ,		
				EXTRA3 as "extra3" ,		
				EXTRA4 as "extra4" ,		
				EXTRA5 as "extra5" ,		
				DEL_STATUS as "delStatus" ,		
				CREATE_USER_LABEL as "createUserLabel" ,		
				CREATE_DATE as "createDate" ,		
				UPDATE_USER_LABEL as "updateUserLabel" ,		
				UPDATE_DATE as "updateDate" ,		
				DELETE_USER_LABEL as "deleteUserLabel" ,		
				DELETE_DATE as "deleteDate" ,		
				RECORD_VERSION as "recordVersion" ,		
				FIRST_DEPT_PATH as "firstDeptPath" ,		
				SWS_NO as "swsNo" ,		
				DLR_NO as "dlrNo" ,		
				DJD_PERSON_NAME as "djdPersonName" ,		
				JD_PERSON_NAME as "jdPersonName" ,		
				FML as "fml" ,		
				FMR_CODE as "fmrCode" ,		
				DJR_DATE as "djrDate" ,		
				MONEY_GGYSF as "moneyGgysf" ,		
				ZZRQ as "zzrq" ,		
				ZZBZ as "zzbz" ,		
				ZZTXR as "zztxr" ,		
				ZZTXRQ as "zztxrq" ,		
				ZZBJ as "zzbj" ,		
				FINUSE_SSQK as "finuseSsqk" ,		
				FINUSE_SSDATE as "finuseSsdate" 		
				FROM ${zzzcSchema}.T_KIZL_PATENT_INFO_HG
		WHERE   PATENT_ID=#value# 				
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tKIZLPatentInfoHg">
		SELECT
				PATENT_ID  as "patentId" ,		
				APPLY_ID  as "applyId" ,		
				SERIAL_NUM  as "serialNum" ,		
				JSBH  as "jsbh" ,		
				BGBH  as "bgbh" ,		
				APPLY_NAME  as "applyName" ,		
				FIRST_DEPT_CODE  as "firstDeptCode" ,		
				FIRST_DEPT_NAME  as "firstDeptName" ,		
				GLDW_CODE  as "gldwCode" ,		
				GLDW_NAME  as "gldwName" ,		
				LABEL  as "label" ,		
				TECH_AREA  as "techArea" ,		
				USE_PROPOSE  as "usePropose" ,		
				KNOWLEDGE_CLASS  as "knowledgeClass" ,		
				LXR_CODE  as "lxrCode" ,		
				LXR_NAME  as "lxrName" ,		
				LXR_PHONE  as "lxrPhone" ,		
				LXR_EMAIL  as "lxrEmail" ,		
				LXR_MOBILE  as "lxrMobile" ,		
				FROM_TYPE  as "fromType" ,		
				FROM_NO  as "fromNo" ,		
				FROM_NAME  as "fromName" ,		
				FROM_CONTENT  as "fromContent" ,		
				USE_METHOD  as "useMethod" ,		
				USE_DEPT  as "useDept" ,		
				USE_DEPT_NAME  as "useDeptName" ,		
				USE_EXPECTED  as "useExpected" ,		
				USE_EXPECTED_NAME  as "useExpectedName" ,		
				USE_FIRSTDATE  as "useFirstdate" ,		
				REASON_NOUSE  as "reasonNouse" ,		
				CONTENT_NOUSE  as "contentNouse" ,		
				JPG_FLAG  as "jpgFlag" ,		
				JPG_XH  as "jpgXh" ,		
				JPG_GW  as "jpgGw" ,		
				JPG_TEAM  as "jpgTeam" ,		
				ISBIGXM  as "isbigxm" ,		
				JFDW_CODE  as "jfdwCode" ,		
				FLOW_STATUS  as "flowStatus" ,		
				PATENT_STATUS  as "patentStatus" ,		
				DJD_PERSON  as "djdPerson" ,		
				DJD_DATE  as "djdDate" ,		
				JD_PERSON  as "jdPerson" ,		
				JD_DATE  as "jdDate" ,		
				QS  as "qs" ,		
				SLRQ  as "slrq" ,		
				PATENT_NO  as "patentNo" ,		
				PATENT_TYPE  as "patentType" ,		
				YXQR  as "yxqr" ,		
				QLYQSL  as "qlyqsl" ,		
				SMSYS  as "smsys" ,		
				SWS_GUID  as "swsGuid" ,		
				SWSDLR  as "swsdlr" ,		
				SWSDLR_PHONE  as "swsdlrPhone" ,		
				SWSDLR_EMAIL  as "swsdlrEmail" ,		
				MONEY_DLF  as "moneyDlf" ,		
				MONEY_QF  as "moneyQf" ,		
				MONEY_SQF  as "moneySqf" ,		
				MONEY_GBYSF  as "moneyGbysf" ,		
				MONEY_SMSFJF  as "moneySmsfjf" ,		
				MONEY_YHS  as "moneyYhs" ,		
				SQTZ_FWDATE  as "sqtzFwdate" ,		
				MONEY_FIRST  as "moneyFirst" ,		
				ZLH  as "zlh" ,		
				SQRQ  as "sqrq" ,		
				FLZT  as "flzt" ,		
				ISVALID  as "isvalid" ,		
				ISGD_ARCHIVE  as "isgdArchive" ,		
				ARCHIVE_GUID  as "archiveGuid" ,		
				ISGD_DZ  as "isgdDz" ,		
				EXTRA1  as "extra1" ,		
				EXTRA2  as "extra2" ,		
				EXTRA3  as "extra3" ,		
				EXTRA4  as "extra4" ,		
				EXTRA5  as "extra5" ,		
				DEL_STATUS  as "delStatus" ,		
				CREATE_USER_LABEL  as "createUserLabel" ,		
				CREATE_DATE  as "createDate" ,		
				UPDATE_USER_LABEL  as "updateUserLabel" ,		
				UPDATE_DATE  as "updateDate" ,		
				DELETE_USER_LABEL  as "deleteUserLabel" ,		
				DELETE_DATE  as "deleteDate" ,		
				RECORD_VERSION  as "recordVersion" ,		
				FIRST_DEPT_PATH  as "firstDeptPath" ,		
				SWS_NO  as "swsNo" ,		
				DLR_NO  as "dlrNo" ,		
				DJD_PERSON_NAME  as "djdPersonName" ,		
				JD_PERSON_NAME  as "jdPersonName" ,		
				FML  as "fml" ,		
				FMR_CODE  as "fmrCode" ,		
				DJR_DATE  as "djrDate" ,		
				MONEY_GGYSF  as "moneyGgysf" ,		
				ZZRQ  as "zzrq" ,		
				ZZBZ  as "zzbz" ,		
				ZZTXR  as "zztxr" ,		
				ZZTXRQ  as "zztxrq" ,		
				ZZBJ  as "zzbj" ,		
				FINUSE_SSQK  as "finuseSsqk" ,		
				FINUSE_SSDATE  as "finuseSsdate" 		
				FROM ${zzzcSchema}.T_KIZL_PATENT_INFO_HG
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="serialNum">SERIAL_NUM =  #serialNum#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jsbh">JSBH =  #jsbh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bgbh">BGBH =  #bgbh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="applyName">APPLY_NAME =  #applyName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE =  #firstDeptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME =  #firstDeptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME =  #gldwName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="label">LABEL =  #label#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="techArea">TECH_AREA =  #techArea#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="usePropose">USE_PROPOSE =  #usePropose#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="knowledgeClass">KNOWLEDGE_CLASS =  #knowledgeClass#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrCode">LXR_CODE =  #lxrCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrName">LXR_NAME =  #lxrName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrPhone">LXR_PHONE =  #lxrPhone#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrEmail">LXR_EMAIL =  #lxrEmail#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lxrMobile">LXR_MOBILE =  #lxrMobile#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE =  #fromType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromNo">FROM_NO =  #fromNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromName">FROM_NAME =  #fromName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fromContent">FROM_CONTENT =  #fromContent#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useMethod">USE_METHOD =  #useMethod#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useDept">USE_DEPT =  #useDept#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useDeptName">USE_DEPT_NAME =  #useDeptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useExpected">USE_EXPECTED =  #useExpected#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useExpectedName">USE_EXPECTED_NAME =  #useExpectedName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="useFirstdate">USE_FIRSTDATE =  #useFirstdate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="reasonNouse">REASON_NOUSE =  #reasonNouse#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="contentNouse">CONTENT_NOUSE =  #contentNouse#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgFlag">JPG_FLAG =  #jpgFlag#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgXh">JPG_XH =  #jpgXh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgGw">JPG_GW =  #jpgGw#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jpgTeam">JPG_TEAM =  #jpgTeam#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isbigxm">ISBIGXM =  #isbigxm#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jfdwCode">JFDW_CODE =  #jfdwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="flowStatus">FLOW_STATUS =  #flowStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentStatus">PATENT_STATUS =  #patentStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djdPerson">DJD_PERSON =  #djdPerson#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djdDate">DJD_DATE =  #djdDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jdPerson">JD_PERSON =  #jdPerson#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jdDate">JD_DATE =  #jdDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="qs">QS =  #qs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="slrq">SLRQ =  #slrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentNo">PATENT_NO =  #patentNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="yxqr">YXQR =  #yxqr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="qlyqsl">QLYQSL =  #qlyqsl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="smsys">SMSYS =  #smsys#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsGuid">SWS_GUID =  #swsGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsdlr">SWSDLR =  #swsdlr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsdlrPhone">SWSDLR_PHONE =  #swsdlrPhone#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsdlrEmail">SWSDLR_EMAIL =  #swsdlrEmail#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyDlf">MONEY_DLF =  #moneyDlf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyQf">MONEY_QF =  #moneyQf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneySqf">MONEY_SQF =  #moneySqf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyGbysf">MONEY_GBYSF =  #moneyGbysf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneySmsfjf">MONEY_SMSFJF =  #moneySmsfjf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyYhs">MONEY_YHS =  #moneyYhs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqtzFwdate">SQTZ_FWDATE =  #sqtzFwdate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyFirst">MONEY_FIRST =  #moneyFirst#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zlh">ZLH =  #zlh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sqrq">SQRQ =  #sqrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="flzt">FLZT =  #flzt#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isvalid">ISVALID =  #isvalid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isgdArchive">ISGD_ARCHIVE =  #isgdArchive#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="archiveGuid">ARCHIVE_GUID =  #archiveGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="isgdDz">ISGD_DZ =  #isgdDz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="firstDeptPath">FIRST_DEPT_PATH =  #firstDeptPath#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="swsNo">SWS_NO =  #swsNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dlrNo">DLR_NO =  #dlrNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djdPersonName">DJD_PERSON_NAME =  #djdPersonName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jdPersonName">JD_PERSON_NAME =  #jdPersonName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fml">FML =  #fml#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fmrCode">FMR_CODE =  #fmrCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="djrDate">DJR_DATE =  #djrDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyGgysf">MONEY_GGYSF =  #moneyGgysf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zzrq">ZZRQ =  #zzrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zzbz">ZZBZ =  #zzbz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zztxr">ZZTXR =  #zztxr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zztxrq">ZZTXRQ =  #zztxrq#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zzbj">ZZBJ =  #zzbj#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="finuseSsqk">FINUSE_SSQK =  #finuseSsqk#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="finuseSsdate">FINUSE_SSDATE =  #finuseSsdate#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KIZL_PATENT_INFO_HG 
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="patentId">PATENT_ID =  #patentId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyId">APPLY_ID =  #applyId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="serialNum">SERIAL_NUM =  #serialNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jsbh">JSBH =  #jsbh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="bgbh">BGBH =  #bgbh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyName">APPLY_NAME =  #applyName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE =  #firstDeptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME =  #firstDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gldwName">GLDW_NAME =  #gldwName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="label">LABEL =  #label#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techArea">TECH_AREA =  #techArea#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="usePropose">USE_PROPOSE =  #usePropose#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="knowledgeClass">KNOWLEDGE_CLASS =  #knowledgeClass#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrCode">LXR_CODE =  #lxrCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrName">LXR_NAME =  #lxrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrPhone">LXR_PHONE =  #lxrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrEmail">LXR_EMAIL =  #lxrEmail#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrMobile">LXR_MOBILE =  #lxrMobile#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromType">FROM_TYPE =  #fromType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromNo">FROM_NO =  #fromNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromName">FROM_NAME =  #fromName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fromContent">FROM_CONTENT =  #fromContent#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useMethod">USE_METHOD =  #useMethod#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDept">USE_DEPT =  #useDept#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useDeptName">USE_DEPT_NAME =  #useDeptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useExpected">USE_EXPECTED =  #useExpected#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useExpectedName">USE_EXPECTED_NAME =  #useExpectedName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="useFirstdate">USE_FIRSTDATE =  #useFirstdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="reasonNouse">REASON_NOUSE =  #reasonNouse#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contentNouse">CONTENT_NOUSE =  #contentNouse#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jpgFlag">JPG_FLAG =  #jpgFlag#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jpgXh">JPG_XH =  #jpgXh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jpgGw">JPG_GW =  #jpgGw#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jpgTeam">JPG_TEAM =  #jpgTeam#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isbigxm">ISBIGXM =  #isbigxm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jfdwCode">JFDW_CODE =  #jfdwCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flowStatus">FLOW_STATUS =  #flowStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentStatus">PATENT_STATUS =  #patentStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="djdPerson">DJD_PERSON =  #djdPerson#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="djdDate">DJD_DATE =  #djdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jdPerson">JD_PERSON =  #jdPerson#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jdDate">JD_DATE =  #jdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="qs">QS =  #qs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="slrq">SLRQ =  #slrq#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentNo">PATENT_NO =  #patentNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="yxqr">YXQR =  #yxqr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="qlyqsl">QLYQSL =  #qlyqsl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="smsys">SMSYS =  #smsys#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsGuid">SWS_GUID =  #swsGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsdlr">SWSDLR =  #swsdlr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsdlrPhone">SWSDLR_PHONE =  #swsdlrPhone#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsdlrEmail">SWSDLR_EMAIL =  #swsdlrEmail#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyDlf">MONEY_DLF =  #moneyDlf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyQf">MONEY_QF =  #moneyQf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneySqf">MONEY_SQF =  #moneySqf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyGbysf">MONEY_GBYSF =  #moneyGbysf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneySmsfjf">MONEY_SMSFJF =  #moneySmsfjf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyYhs">MONEY_YHS =  #moneyYhs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqtzFwdate">SQTZ_FWDATE =  #sqtzFwdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyFirst">MONEY_FIRST =  #moneyFirst#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zlh">ZLH =  #zlh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sqrq">SQRQ =  #sqrq#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="flzt">FLZT =  #flzt#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isvalid">ISVALID =  #isvalid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isgdArchive">ISGD_ARCHIVE =  #isgdArchive#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="archiveGuid">ARCHIVE_GUID =  #archiveGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isgdDz">ISGD_DZ =  #isgdDz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="firstDeptPath">FIRST_DEPT_PATH =  #firstDeptPath#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="swsNo">SWS_NO =  #swsNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dlrNo">DLR_NO =  #dlrNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="djdPersonName">DJD_PERSON_NAME =  #djdPersonName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jdPersonName">JD_PERSON_NAME =  #jdPersonName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fml">FML =  #fml#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fmrCode">FMR_CODE =  #fmrCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="djrDate">DJR_DATE =  #djrDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyGgysf">MONEY_GGYSF =  #moneyGgysf#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zzrq">ZZRQ =  #zzrq#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zzbz">ZZBZ =  #zzbz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zztxr">ZZTXR =  #zztxr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zztxrq">ZZTXRQ =  #zztxrq#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zzbj">ZZBJ =  #zzbj#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="finuseSsqk">FINUSE_SSQK =  #finuseSsqk#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="finuseSsdate">FINUSE_SSDATE =  #finuseSsdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tKIZLPatentInfoHg">
		INSERT INTO ${zzzcSchema}.T_KIZL_PATENT_INFO_HG ( 
		<dynamic prepend=" ">
					<isNotNull prepend=", " property="patentId">PATENT_ID </isNotNull>
					<isNotNull prepend=", " property="applyId">APPLY_ID </isNotNull>
					<isNotNull prepend=", " property="serialNum">SERIAL_NUM </isNotNull>
					<isNotNull prepend=", " property="jsbh">JSBH </isNotNull>
					<isNotNull prepend=", " property="bgbh">BGBH </isNotNull>
					<isNotNull prepend=", " property="applyName">APPLY_NAME </isNotNull>
					<isNotNull prepend=", " property="firstDeptCode">FIRST_DEPT_CODE </isNotNull>
					<isNotNull prepend=", " property="firstDeptName">FIRST_DEPT_NAME </isNotNull>
					<isNotNull prepend=", " property="gldwCode">GLDW_CODE </isNotNull>
					<isNotNull prepend=", " property="gldwName">GLDW_NAME </isNotNull>
					<isNotNull prepend=", " property="label">LABEL </isNotNull>
					<isNotNull prepend=", " property="techArea">TECH_AREA </isNotNull>
					<isNotNull prepend=", " property="usePropose">USE_PROPOSE </isNotNull>
					<isNotNull prepend=", " property="knowledgeClass">KNOWLEDGE_CLASS </isNotNull>
					<isNotNull prepend=", " property="lxrCode">LXR_CODE </isNotNull>
					<isNotNull prepend=", " property="lxrName">LXR_NAME </isNotNull>
					<isNotNull prepend=", " property="lxrPhone">LXR_PHONE </isNotNull>
					<isNotNull prepend=", " property="lxrEmail">LXR_EMAIL </isNotNull>
					<isNotNull prepend=", " property="lxrMobile">LXR_MOBILE </isNotNull>
					<isNotNull prepend=", " property="fromType">FROM_TYPE </isNotNull>
					<isNotNull prepend=", " property="fromNo">FROM_NO </isNotNull>
					<isNotNull prepend=", " property="fromName">FROM_NAME </isNotNull>
					<isNotNull prepend=", " property="fromContent">FROM_CONTENT </isNotNull>
					<isNotNull prepend=", " property="useMethod">USE_METHOD </isNotNull>
					<isNotNull prepend=", " property="useDept">USE_DEPT </isNotNull>
					<isNotNull prepend=", " property="useDeptName">USE_DEPT_NAME </isNotNull>
					<isNotNull prepend=", " property="useExpected">USE_EXPECTED </isNotNull>
					<isNotNull prepend=", " property="useExpectedName">USE_EXPECTED_NAME </isNotNull>
					<isNotNull prepend=", " property="useFirstdate">USE_FIRSTDATE </isNotNull>
					<isNotNull prepend=", " property="reasonNouse">REASON_NOUSE </isNotNull>
					<isNotNull prepend=", " property="contentNouse">CONTENT_NOUSE </isNotNull>
					<isNotNull prepend=", " property="jpgFlag">JPG_FLAG </isNotNull>
					<isNotNull prepend=", " property="jpgXh">JPG_XH </isNotNull>
					<isNotNull prepend=", " property="jpgGw">JPG_GW </isNotNull>
					<isNotNull prepend=", " property="jpgTeam">JPG_TEAM </isNotNull>
					<isNotNull prepend=", " property="isbigxm">ISBIGXM </isNotNull>
					<isNotNull prepend=", " property="jfdwCode">JFDW_CODE </isNotNull>
					<isNotNull prepend=", " property="flowStatus">FLOW_STATUS </isNotNull>
					<isNotNull prepend=", " property="patentStatus">PATENT_STATUS </isNotNull>
					<isNotNull prepend=", " property="djdPerson">DJD_PERSON </isNotNull>
					<isNotNull prepend=", " property="djdDate">DJD_DATE </isNotNull>
					<isNotNull prepend=", " property="jdPerson">JD_PERSON </isNotNull>
					<isNotNull prepend=", " property="jdDate">JD_DATE </isNotNull>
					<isNotNull prepend=", " property="qs">QS </isNotNull>
					<isNotNull prepend=", " property="slrq">SLRQ </isNotNull>
					<isNotNull prepend=", " property="patentNo">PATENT_NO </isNotNull>
					<isNotNull prepend=", " property="patentType">PATENT_TYPE </isNotNull>
					<isNotNull prepend=", " property="yxqr">YXQR </isNotNull>
					<isNotNull prepend=", " property="qlyqsl">QLYQSL </isNotNull>
					<isNotNull prepend=", " property="smsys">SMSYS </isNotNull>
					<isNotNull prepend=", " property="swsGuid">SWS_GUID </isNotNull>
					<isNotNull prepend=", " property="swsdlr">SWSDLR </isNotNull>
					<isNotNull prepend=", " property="swsdlrPhone">SWSDLR_PHONE </isNotNull>
					<isNotNull prepend=", " property="swsdlrEmail">SWSDLR_EMAIL </isNotNull>
					<isNotNull prepend=", " property="moneyDlf">MONEY_DLF </isNotNull>
					<isNotNull prepend=", " property="moneyQf">MONEY_QF </isNotNull>
					<isNotNull prepend=", " property="moneySqf">MONEY_SQF </isNotNull>
					<isNotNull prepend=", " property="moneyGbysf">MONEY_GBYSF </isNotNull>
					<isNotNull prepend=", " property="moneySmsfjf">MONEY_SMSFJF </isNotNull>
					<isNotNull prepend=", " property="moneyYhs">MONEY_YHS </isNotNull>
					<isNotNull prepend=", " property="sqtzFwdate">SQTZ_FWDATE </isNotNull>
					<isNotNull prepend=", " property="moneyFirst">MONEY_FIRST </isNotNull>
					<isNotNull prepend=", " property="zlh">ZLH </isNotNull>
					<isNotNull prepend=", " property="sqrq">SQRQ </isNotNull>
					<isNotNull prepend=", " property="flzt">FLZT </isNotNull>
					<isNotNull prepend=", " property="isvalid">ISVALID </isNotNull>
					<isNotNull prepend=", " property="isgdArchive">ISGD_ARCHIVE </isNotNull>
					<isNotNull prepend=", " property="archiveGuid">ARCHIVE_GUID </isNotNull>
					<isNotNull prepend=", " property="isgdDz">ISGD_DZ </isNotNull>
					<isNotNull prepend=", " property="extra1">EXTRA1 </isNotNull>
					<isNotNull prepend=", " property="extra2">EXTRA2 </isNotNull>
					<isNotNull prepend=", " property="extra3">EXTRA3 </isNotNull>
					<isNotNull prepend=", " property="extra4">EXTRA4 </isNotNull>
					<isNotNull prepend=", " property="extra5">EXTRA5 </isNotNull>
					<isNotNull prepend=", " property="delStatus">DEL_STATUS </isNotNull>
					<isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="createDate">CREATE_DATE </isNotNull>
					<isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="updateDate">UPDATE_DATE </isNotNull>
					<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="deleteDate">DELETE_DATE </isNotNull>
					<isNotNull prepend=", " property="recordVersion">RECORD_VERSION </isNotNull>
					<isNotNull prepend=", " property="firstDeptPath">FIRST_DEPT_PATH </isNotNull>
					<isNotNull prepend=", " property="swsNo">SWS_NO </isNotNull>
					<isNotNull prepend=", " property="dlrNo">DLR_NO </isNotNull>
					<isNotNull prepend=", " property="djdPersonName">DJD_PERSON_NAME </isNotNull>
					<isNotNull prepend=", " property="jdPersonName">JD_PERSON_NAME </isNotNull>
					<isNotNull prepend=", " property="fml">FML </isNotNull>
					<isNotNull prepend=", " property="fmrCode">FMR_CODE </isNotNull>
					<isNotNull prepend=", " property="djrDate">DJR_DATE </isNotNull>
					<isNotNull prepend=", " property="moneyGgysf">MONEY_GGYSF </isNotNull>
					<isNotNull prepend=", " property="zzrq">ZZRQ </isNotNull>
					<isNotNull prepend=", " property="zzbz">ZZBZ </isNotNull>
					<isNotNull prepend=", " property="zztxr">ZZTXR </isNotNull>
					<isNotNull prepend=", " property="zztxrq">ZZTXRQ </isNotNull>
					<isNotNull prepend=", " property="zzbj">ZZBJ </isNotNull>
					<isNotNull prepend=", " property="finuseSsqk">FINUSE_SSQK </isNotNull>
					<isNotNull prepend=", " property="finuseSsdate">FINUSE_SSDATE </isNotNull>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotNull prepend=", " property="patentId">#patentId#</isNotNull>
			<isNotNull prepend=", " property="applyId">#applyId#</isNotNull>
			<isNotNull prepend=", " property="serialNum">#serialNum#</isNotNull>
			<isNotNull prepend=", " property="jsbh">#jsbh#</isNotNull>
			<isNotNull prepend=", " property="bgbh">#bgbh#</isNotNull>
			<isNotNull prepend=", " property="applyName">#applyName#</isNotNull>
			<isNotNull prepend=", " property="firstDeptCode">#firstDeptCode#</isNotNull>
			<isNotNull prepend=", " property="firstDeptName">#firstDeptName#</isNotNull>
			<isNotNull prepend=", " property="gldwCode">#gldwCode#</isNotNull>
			<isNotNull prepend=", " property="gldwName">#gldwName#</isNotNull>
			<isNotNull prepend=", " property="label">#label#</isNotNull>
			<isNotNull prepend=", " property="techArea">#techArea#</isNotNull>
			<isNotNull prepend=", " property="usePropose">#usePropose#</isNotNull>
			<isNotNull prepend=", " property="knowledgeClass">#knowledgeClass#</isNotNull>
			<isNotNull prepend=", " property="lxrCode">#lxrCode#</isNotNull>
			<isNotNull prepend=", " property="lxrName">#lxrName#</isNotNull>
			<isNotNull prepend=", " property="lxrPhone">#lxrPhone#</isNotNull>
			<isNotNull prepend=", " property="lxrEmail">#lxrEmail#</isNotNull>
			<isNotNull prepend=", " property="lxrMobile">#lxrMobile#</isNotNull>
			<isNotNull prepend=", " property="fromType">#fromType#</isNotNull>
			<isNotNull prepend=", " property="fromNo">#fromNo#</isNotNull>
			<isNotNull prepend=", " property="fromName">#fromName#</isNotNull>
			<isNotNull prepend=", " property="fromContent">#fromContent#</isNotNull>
			<isNotNull prepend=", " property="useMethod">#useMethod#</isNotNull>
			<isNotNull prepend=", " property="useDept">#useDept#</isNotNull>
			<isNotNull prepend=", " property="useDeptName">#useDeptName#</isNotNull>
			<isNotNull prepend=", " property="useExpected">#useExpected#</isNotNull>
			<isNotNull prepend=", " property="useExpectedName">#useExpectedName#</isNotNull>
			<isNotNull prepend=", " property="useFirstdate">#useFirstdate#</isNotNull>
			<isNotNull prepend=", " property="reasonNouse">#reasonNouse#</isNotNull>
			<isNotNull prepend=", " property="contentNouse">#contentNouse#</isNotNull>
			<isNotNull prepend=", " property="jpgFlag">#jpgFlag#</isNotNull>
			<isNotNull prepend=", " property="jpgXh">#jpgXh#</isNotNull>
			<isNotNull prepend=", " property="jpgGw">#jpgGw#</isNotNull>
			<isNotNull prepend=", " property="jpgTeam">#jpgTeam#</isNotNull>
			<isNotNull prepend=", " property="isbigxm">#isbigxm#</isNotNull>
			<isNotNull prepend=", " property="jfdwCode">#jfdwCode#</isNotNull>
			<isNotNull prepend=", " property="flowStatus">#flowStatus#</isNotNull>
			<isNotNull prepend=", " property="patentStatus">#patentStatus#</isNotNull>
			<isNotNull prepend=", " property="djdPerson">#djdPerson#</isNotNull>
			<isNotNull prepend=", " property="djdDate">#djdDate#</isNotNull>
			<isNotNull prepend=", " property="jdPerson">#jdPerson#</isNotNull>
			<isNotNull prepend=", " property="jdDate">#jdDate#</isNotNull>
			<isNotNull prepend=", " property="qs">#qs#</isNotNull>
			<isNotNull prepend=", " property="slrq">#slrq#</isNotNull>
			<isNotNull prepend=", " property="patentNo">#patentNo#</isNotNull>
			<isNotNull prepend=", " property="patentType">#patentType#</isNotNull>
			<isNotNull prepend=", " property="yxqr">#yxqr#</isNotNull>
			<isNotNull prepend=", " property="qlyqsl">#qlyqsl#</isNotNull>
			<isNotNull prepend=", " property="smsys">#smsys#</isNotNull>
			<isNotNull prepend=", " property="swsGuid">#swsGuid#</isNotNull>
			<isNotNull prepend=", " property="swsdlr">#swsdlr#</isNotNull>
			<isNotNull prepend=", " property="swsdlrPhone">#swsdlrPhone#</isNotNull>
			<isNotNull prepend=", " property="swsdlrEmail">#swsdlrEmail#</isNotNull>
			<isNotNull prepend=", " property="moneyDlf">#moneyDlf#</isNotNull>
			<isNotNull prepend=", " property="moneyQf">#moneyQf#</isNotNull>
			<isNotNull prepend=", " property="moneySqf">#moneySqf#</isNotNull>
			<isNotNull prepend=", " property="moneyGbysf">#moneyGbysf#</isNotNull>
			<isNotNull prepend=", " property="moneySmsfjf">#moneySmsfjf#</isNotNull>
			<isNotNull prepend=", " property="moneyYhs">#moneyYhs#</isNotNull>
			<isNotNull prepend=", " property="sqtzFwdate">#sqtzFwdate#</isNotNull>
			<isNotNull prepend=", " property="moneyFirst">#moneyFirst#</isNotNull>
			<isNotNull prepend=", " property="zlh">#zlh#</isNotNull>
			<isNotNull prepend=", " property="sqrq">#sqrq#</isNotNull>
			<isNotNull prepend=", " property="flzt">#flzt#</isNotNull>
			<isNotNull prepend=", " property="isvalid">#isvalid#</isNotNull>
			<isNotNull prepend=", " property="isgdArchive">#isgdArchive#</isNotNull>
			<isNotNull prepend=", " property="archiveGuid">#archiveGuid#</isNotNull>
			<isNotNull prepend=", " property="isgdDz">#isgdDz#</isNotNull>
			<isNotNull prepend=", " property="extra1">#extra1#</isNotNull>
			<isNotNull prepend=", " property="extra2">#extra2#</isNotNull>
			<isNotNull prepend=", " property="extra3">#extra3#</isNotNull>
			<isNotNull prepend=", " property="extra4">#extra4#</isNotNull>
			<isNotNull prepend=", " property="extra5">#extra5#</isNotNull>
			<isNotNull prepend=", " property="delStatus">#delStatus#</isNotNull>
			<isNotNull prepend=", " property="createUserLabel">#createUserLabel#</isNotNull>
			<isNotNull prepend=", " property="createDate">
			   <isNotEmpty property="createDate">#createDate#</isNotEmpty>
			   <isEmpty property="createDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="updateUserLabel">#updateUserLabel#</isNotNull>
			<isNotNull prepend=", " property="updateDate">
			   <isNotEmpty property="updateDate">#updateDate#</isNotEmpty>
			   <isEmpty property="updateDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">#deleteUserLabel#</isNotNull>
			<isNotNull prepend=", " property="deleteDate">
			   <isNotEmpty property="deleteDate">#deleteDate#</isNotEmpty>
			   <isEmpty property="deleteDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="recordVersion">#recordVersion#</isNotNull>
			<isNotNull prepend=", " property="firstDeptPath">#firstDeptPath#</isNotNull>
			<isNotNull prepend=", " property="swsNo">#swsNo#</isNotNull>
			<isNotNull prepend=", " property="dlrNo">#dlrNo#</isNotNull>
			<isNotNull prepend=", " property="djdPersonName">#djdPersonName#</isNotNull>
			<isNotNull prepend=", " property="jdPersonName">#jdPersonName#</isNotNull>
			<isNotNull prepend=", " property="fml">#fml#</isNotNull>
			<isNotNull prepend=", " property="fmrCode">#fmrCode#</isNotNull>
			<isNotNull prepend=", " property="djrDate">#djrDate#</isNotNull>
			<isNotNull prepend=", " property="moneyGgysf">#moneyGgysf#</isNotNull>
			<isNotNull prepend=", " property="zzrq">#zzrq#</isNotNull>
			<isNotNull prepend=", " property="zzbz">#zzbz#</isNotNull>
			<isNotNull prepend=", " property="zztxr">#zztxr#</isNotNull>
			<isNotNull prepend=", " property="zztxrq">#zztxrq#</isNotNull>
			<isNotNull prepend=", " property="zzbj">#zzbj#</isNotNull>
			<isNotNull prepend=", " property="finuseSsqk">#finuseSsqk#</isNotNull>
			<isNotNull prepend=", " property="finuseSsdate">#finuseSsdate#</isNotNull>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_PATENT_INFO_HG
		WHERE 		PATENT_ID=#value# 	
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_PATENT_INFO_HG
		WHERE 
		<dynamic prepend=" ">
			<isNotNull prepend=" AND " property="patentId">PATENT_ID = #patentId#</isNotNull>
			<isNotNull prepend=" AND " property="applyId">APPLY_ID = #applyId#</isNotNull>
			<isNotNull prepend=" AND " property="serialNum">SERIAL_NUM = #serialNum#</isNotNull>
			<isNotNull prepend=" AND " property="jsbh">JSBH = #jsbh#</isNotNull>
			<isNotNull prepend=" AND " property="bgbh">BGBH = #bgbh#</isNotNull>
			<isNotNull prepend=" AND " property="applyName">APPLY_NAME = #applyName#</isNotNull>
			<isNotNull prepend=" AND " property="firstDeptCode">FIRST_DEPT_CODE = #firstDeptCode#</isNotNull>
			<isNotNull prepend=" AND " property="firstDeptName">FIRST_DEPT_NAME = #firstDeptName#</isNotNull>
			<isNotNull prepend=" AND " property="gldwCode">GLDW_CODE = #gldwCode#</isNotNull>
			<isNotNull prepend=" AND " property="gldwName">GLDW_NAME = #gldwName#</isNotNull>
			<isNotNull prepend=" AND " property="label">LABEL = #label#</isNotNull>
			<isNotNull prepend=" AND " property="techArea">TECH_AREA = #techArea#</isNotNull>
			<isNotNull prepend=" AND " property="usePropose">USE_PROPOSE = #usePropose#</isNotNull>
			<isNotNull prepend=" AND " property="knowledgeClass">KNOWLEDGE_CLASS = #knowledgeClass#</isNotNull>
			<isNotNull prepend=" AND " property="lxrCode">LXR_CODE = #lxrCode#</isNotNull>
			<isNotNull prepend=" AND " property="lxrName">LXR_NAME = #lxrName#</isNotNull>
			<isNotNull prepend=" AND " property="lxrPhone">LXR_PHONE = #lxrPhone#</isNotNull>
			<isNotNull prepend=" AND " property="lxrEmail">LXR_EMAIL = #lxrEmail#</isNotNull>
			<isNotNull prepend=" AND " property="lxrMobile">LXR_MOBILE = #lxrMobile#</isNotNull>
			<isNotNull prepend=" AND " property="fromType">FROM_TYPE = #fromType#</isNotNull>
			<isNotNull prepend=" AND " property="fromNo">FROM_NO = #fromNo#</isNotNull>
			<isNotNull prepend=" AND " property="fromName">FROM_NAME = #fromName#</isNotNull>
			<isNotNull prepend=" AND " property="fromContent">FROM_CONTENT = #fromContent#</isNotNull>
			<isNotNull prepend=" AND " property="useMethod">USE_METHOD = #useMethod#</isNotNull>
			<isNotNull prepend=" AND " property="useDept">USE_DEPT = #useDept#</isNotNull>
			<isNotNull prepend=" AND " property="useDeptName">USE_DEPT_NAME = #useDeptName#</isNotNull>
			<isNotNull prepend=" AND " property="useExpected">USE_EXPECTED = #useExpected#</isNotNull>
			<isNotNull prepend=" AND " property="useExpectedName">USE_EXPECTED_NAME = #useExpectedName#</isNotNull>
			<isNotNull prepend=" AND " property="useFirstdate">USE_FIRSTDATE = #useFirstdate#</isNotNull>
			<isNotNull prepend=" AND " property="reasonNouse">REASON_NOUSE = #reasonNouse#</isNotNull>
			<isNotNull prepend=" AND " property="contentNouse">CONTENT_NOUSE = #contentNouse#</isNotNull>
			<isNotNull prepend=" AND " property="jpgFlag">JPG_FLAG = #jpgFlag#</isNotNull>
			<isNotNull prepend=" AND " property="jpgXh">JPG_XH = #jpgXh#</isNotNull>
			<isNotNull prepend=" AND " property="jpgGw">JPG_GW = #jpgGw#</isNotNull>
			<isNotNull prepend=" AND " property="jpgTeam">JPG_TEAM = #jpgTeam#</isNotNull>
			<isNotNull prepend=" AND " property="isbigxm">ISBIGXM = #isbigxm#</isNotNull>
			<isNotNull prepend=" AND " property="jfdwCode">JFDW_CODE = #jfdwCode#</isNotNull>
			<isNotNull prepend=" AND " property="flowStatus">FLOW_STATUS = #flowStatus#</isNotNull>
			<isNotNull prepend=" AND " property="patentStatus">PATENT_STATUS = #patentStatus#</isNotNull>
			<isNotNull prepend=" AND " property="djdPerson">DJD_PERSON = #djdPerson#</isNotNull>
			<isNotNull prepend=" AND " property="djdDate">DJD_DATE = #djdDate#</isNotNull>
			<isNotNull prepend=" AND " property="jdPerson">JD_PERSON = #jdPerson#</isNotNull>
			<isNotNull prepend=" AND " property="jdDate">JD_DATE = #jdDate#</isNotNull>
			<isNotNull prepend=" AND " property="qs">QS = #qs#</isNotNull>
			<isNotNull prepend=" AND " property="slrq">SLRQ = #slrq#</isNotNull>
			<isNotNull prepend=" AND " property="patentNo">PATENT_NO = #patentNo#</isNotNull>
			<isNotNull prepend=" AND " property="patentType">PATENT_TYPE = #patentType#</isNotNull>
			<isNotNull prepend=" AND " property="yxqr">YXQR = #yxqr#</isNotNull>
			<isNotNull prepend=" AND " property="qlyqsl">QLYQSL = #qlyqsl#</isNotNull>
			<isNotNull prepend=" AND " property="smsys">SMSYS = #smsys#</isNotNull>
			<isNotNull prepend=" AND " property="swsGuid">SWS_GUID = #swsGuid#</isNotNull>
			<isNotNull prepend=" AND " property="swsdlr">SWSDLR = #swsdlr#</isNotNull>
			<isNotNull prepend=" AND " property="swsdlrPhone">SWSDLR_PHONE = #swsdlrPhone#</isNotNull>
			<isNotNull prepend=" AND " property="swsdlrEmail">SWSDLR_EMAIL = #swsdlrEmail#</isNotNull>
			<isNotNull prepend=" AND " property="moneyDlf">MONEY_DLF = #moneyDlf#</isNotNull>
			<isNotNull prepend=" AND " property="moneyQf">MONEY_QF = #moneyQf#</isNotNull>
			<isNotNull prepend=" AND " property="moneySqf">MONEY_SQF = #moneySqf#</isNotNull>
			<isNotNull prepend=" AND " property="moneyGbysf">MONEY_GBYSF = #moneyGbysf#</isNotNull>
			<isNotNull prepend=" AND " property="moneySmsfjf">MONEY_SMSFJF = #moneySmsfjf#</isNotNull>
			<isNotNull prepend=" AND " property="moneyYhs">MONEY_YHS = #moneyYhs#</isNotNull>
			<isNotNull prepend=" AND " property="sqtzFwdate">SQTZ_FWDATE = #sqtzFwdate#</isNotNull>
			<isNotNull prepend=" AND " property="moneyFirst">MONEY_FIRST = #moneyFirst#</isNotNull>
			<isNotNull prepend=" AND " property="zlh">ZLH = #zlh#</isNotNull>
			<isNotNull prepend=" AND " property="sqrq">SQRQ = #sqrq#</isNotNull>
			<isNotNull prepend=" AND " property="flzt">FLZT = #flzt#</isNotNull>
			<isNotNull prepend=" AND " property="isvalid">ISVALID = #isvalid#</isNotNull>
			<isNotNull prepend=" AND " property="isgdArchive">ISGD_ARCHIVE = #isgdArchive#</isNotNull>
			<isNotNull prepend=" AND " property="archiveGuid">ARCHIVE_GUID = #archiveGuid#</isNotNull>
			<isNotNull prepend=" AND " property="isgdDz">ISGD_DZ = #isgdDz#</isNotNull>
			<isNotNull prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
			<isNotNull prepend=" AND " property="firstDeptPath">FIRST_DEPT_PATH = #firstDeptPath#</isNotNull>
			<isNotNull prepend=" AND " property="swsNo">SWS_NO = #swsNo#</isNotNull>
			<isNotNull prepend=" AND " property="dlrNo">DLR_NO = #dlrNo#</isNotNull>
			<isNotNull prepend=" AND " property="djdPersonName">DJD_PERSON_NAME = #djdPersonName#</isNotNull>
			<isNotNull prepend=" AND " property="jdPersonName">JD_PERSON_NAME = #jdPersonName#</isNotNull>
			<isNotNull prepend=" AND " property="fml">FML = #fml#</isNotNull>
			<isNotNull prepend=" AND " property="fmrCode">FMR_CODE = #fmrCode#</isNotNull>
			<isNotNull prepend=" AND " property="djrDate">DJR_DATE = #djrDate#</isNotNull>
			<isNotNull prepend=" AND " property="moneyGgysf">MONEY_GGYSF = #moneyGgysf#</isNotNull>
			<isNotNull prepend=" AND " property="zzrq">ZZRQ = #zzrq#</isNotNull>
			<isNotNull prepend=" AND " property="zzbz">ZZBZ = #zzbz#</isNotNull>
			<isNotNull prepend=" AND " property="zztxr">ZZTXR = #zztxr#</isNotNull>
			<isNotNull prepend=" AND " property="zztxrq">ZZTXRQ = #zztxrq#</isNotNull>
			<isNotNull prepend=" AND " property="zzbj">ZZBJ = #zzbj#</isNotNull>
			<isNotNull prepend=" AND " property="finuseSsqk">FINUSE_SSQK = #finuseSsqk#</isNotNull>
			<isNotNull prepend=" AND " property="finuseSsdate">FINUSE_SSDATE = #finuseSsdate#</isNotNull>
		</dynamic>	
	</delete>

	<update id="update" parameterClass="tKIZLPatentInfoHg">
		UPDATE  ${zzzcSchema}.T_KIZL_PATENT_INFO_HG	
		SET 
		<dynamic prepend=" ">
		<isNotNull prepend="," property="patentId">PATENT_ID = #patentId#</isNotNull>
		<isNotNull prepend="," property="applyId">APPLY_ID = #applyId#</isNotNull>
		<isNotNull prepend="," property="serialNum">SERIAL_NUM = #serialNum#</isNotNull>
		<isNotNull prepend="," property="jsbh">JSBH = #jsbh#</isNotNull>
		<isNotNull prepend="," property="bgbh">BGBH = #bgbh#</isNotNull>
		<isNotNull prepend="," property="applyName">APPLY_NAME = #applyName#</isNotNull>
		<isNotNull prepend="," property="firstDeptCode">FIRST_DEPT_CODE = #firstDeptCode#</isNotNull>
		<isNotNull prepend="," property="firstDeptName">FIRST_DEPT_NAME = #firstDeptName#</isNotNull>
		<isNotNull prepend="," property="gldwCode">GLDW_CODE = #gldwCode#</isNotNull>
		<isNotNull prepend="," property="gldwName">GLDW_NAME = #gldwName#</isNotNull>
		<isNotNull prepend="," property="label">LABEL = #label#</isNotNull>
		<isNotNull prepend="," property="techArea">TECH_AREA = #techArea#</isNotNull>
		<isNotNull prepend="," property="usePropose">USE_PROPOSE = #usePropose#</isNotNull>
		<isNotNull prepend="," property="knowledgeClass">KNOWLEDGE_CLASS = #knowledgeClass#</isNotNull>
		<isNotNull prepend="," property="lxrCode">LXR_CODE = #lxrCode#</isNotNull>
		<isNotNull prepend="," property="lxrName">LXR_NAME = #lxrName#</isNotNull>
		<isNotNull prepend="," property="lxrPhone">LXR_PHONE = #lxrPhone#</isNotNull>
		<isNotNull prepend="," property="lxrEmail">LXR_EMAIL = #lxrEmail#</isNotNull>
		<isNotNull prepend="," property="lxrMobile">LXR_MOBILE = #lxrMobile#</isNotNull>
		<isNotNull prepend="," property="fromType">FROM_TYPE = #fromType#</isNotNull>
		<isNotNull prepend="," property="fromNo">FROM_NO = #fromNo#</isNotNull>
		<isNotNull prepend="," property="fromName">FROM_NAME = #fromName#</isNotNull>
		<isNotNull prepend="," property="fromContent">FROM_CONTENT = #fromContent#</isNotNull>
		<isNotNull prepend="," property="useMethod">USE_METHOD = #useMethod#</isNotNull>
		<isNotNull prepend="," property="useDept">USE_DEPT = #useDept#</isNotNull>
		<isNotNull prepend="," property="useDeptName">USE_DEPT_NAME = #useDeptName#</isNotNull>
		<isNotNull prepend="," property="useExpected">USE_EXPECTED = #useExpected#</isNotNull>
		<isNotNull prepend="," property="useExpectedName">USE_EXPECTED_NAME = #useExpectedName#</isNotNull>
		<isNotNull prepend="," property="useFirstdate">USE_FIRSTDATE = #useFirstdate#</isNotNull>
		<isNotNull prepend="," property="reasonNouse">REASON_NOUSE = #reasonNouse#</isNotNull>
		<isNotNull prepend="," property="contentNouse">CONTENT_NOUSE = #contentNouse#</isNotNull>
		<isNotNull prepend="," property="jpgFlag">JPG_FLAG = #jpgFlag#</isNotNull>
		<isNotNull prepend="," property="jpgXh">JPG_XH = #jpgXh#</isNotNull>
		<isNotNull prepend="," property="jpgGw">JPG_GW = #jpgGw#</isNotNull>
		<isNotNull prepend="," property="jpgTeam">JPG_TEAM = #jpgTeam#</isNotNull>
		<isNotNull prepend="," property="isbigxm">ISBIGXM = #isbigxm#</isNotNull>
		<isNotNull prepend="," property="jfdwCode">JFDW_CODE = #jfdwCode#</isNotNull>
		<isNotNull prepend="," property="flowStatus">FLOW_STATUS = #flowStatus#</isNotNull>
		<isNotNull prepend="," property="patentStatus">PATENT_STATUS = #patentStatus#</isNotNull>
		<isNotNull prepend="," property="djdPerson">DJD_PERSON = #djdPerson#</isNotNull>
		<isNotNull prepend="," property="djdDate">DJD_DATE = #djdDate#</isNotNull>
		<isNotNull prepend="," property="jdPerson">JD_PERSON = #jdPerson#</isNotNull>
		<isNotNull prepend="," property="jdDate">JD_DATE = #jdDate#</isNotNull>
		<isNotNull prepend="," property="qs">QS = #qs#</isNotNull>
		<isNotNull prepend="," property="slrq">SLRQ = #slrq#</isNotNull>
		<isNotNull prepend="," property="patentNo">PATENT_NO = #patentNo#</isNotNull>
		<isNotNull prepend="," property="patentType">PATENT_TYPE = #patentType#</isNotNull>
		<isNotNull prepend="," property="yxqr">YXQR = #yxqr#</isNotNull>
		<isNotNull prepend="," property="qlyqsl">QLYQSL = #qlyqsl#</isNotNull>
		<isNotNull prepend="," property="smsys">SMSYS = #smsys#</isNotNull>
		<isNotNull prepend="," property="swsGuid">SWS_GUID = #swsGuid#</isNotNull>
		<isNotNull prepend="," property="swsdlr">SWSDLR = #swsdlr#</isNotNull>
		<isNotNull prepend="," property="swsdlrPhone">SWSDLR_PHONE = #swsdlrPhone#</isNotNull>
		<isNotNull prepend="," property="swsdlrEmail">SWSDLR_EMAIL = #swsdlrEmail#</isNotNull>
		<isNotNull prepend="," property="moneyDlf">MONEY_DLF = #moneyDlf#</isNotNull>
		<isNotNull prepend="," property="moneyQf">MONEY_QF = #moneyQf#</isNotNull>
		<isNotNull prepend="," property="moneySqf">MONEY_SQF = #moneySqf#</isNotNull>
		<isNotNull prepend="," property="moneyGbysf">MONEY_GBYSF = #moneyGbysf#</isNotNull>
		<isNotNull prepend="," property="moneySmsfjf">MONEY_SMSFJF = #moneySmsfjf#</isNotNull>
		<isNotNull prepend="," property="moneyYhs">MONEY_YHS = #moneyYhs#</isNotNull>
		<isNotNull prepend="," property="sqtzFwdate">SQTZ_FWDATE = #sqtzFwdate#</isNotNull>
		<isNotNull prepend="," property="moneyFirst">MONEY_FIRST = #moneyFirst#</isNotNull>
		<isNotNull prepend="," property="zlh">ZLH = #zlh#</isNotNull>
		<isNotNull prepend="," property="sqrq">SQRQ = #sqrq#</isNotNull>
		<isNotNull prepend="," property="flzt">FLZT = #flzt#</isNotNull>
		<isNotNull prepend="," property="isvalid">ISVALID = #isvalid#</isNotNull>
		<isNotNull prepend="," property="isgdArchive">ISGD_ARCHIVE = #isgdArchive#</isNotNull>
		<isNotNull prepend="," property="archiveGuid">ARCHIVE_GUID = #archiveGuid#</isNotNull>
		<isNotNull prepend="," property="isgdDz">ISGD_DZ = #isgdDz#</isNotNull>
		<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
		<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
		<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
		<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
		<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
		<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
		<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
		<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
		<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
		<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
		<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		<isNotNull prepend="," property="firstDeptPath">FIRST_DEPT_PATH = #firstDeptPath#</isNotNull>
		<isNotNull prepend="," property="swsNo">SWS_NO = #swsNo#</isNotNull>
		<isNotNull prepend="," property="dlrNo">DLR_NO = #dlrNo#</isNotNull>
		<isNotNull prepend="," property="djdPersonName">DJD_PERSON_NAME = #djdPersonName#</isNotNull>
		<isNotNull prepend="," property="jdPersonName">JD_PERSON_NAME = #jdPersonName#</isNotNull>
		<isNotNull prepend="," property="fml">FML = #fml#</isNotNull>
		<isNotNull prepend="," property="fmrCode">FMR_CODE = #fmrCode#</isNotNull>
		<isNotNull prepend="," property="djrDate">DJR_DATE = #djrDate#</isNotNull>
		<isNotNull prepend="," property="moneyGgysf">MONEY_GGYSF = #moneyGgysf#</isNotNull>
		<isNotNull prepend="," property="zzrq">ZZRQ = #zzrq#</isNotNull>
		<isNotNull prepend="," property="zzbz">ZZBZ = #zzbz#</isNotNull>
		<isNotNull prepend="," property="zztxr">ZZTXR = #zztxr#</isNotNull>
		<isNotNull prepend="," property="zztxrq">ZZTXRQ = #zztxrq#</isNotNull>
		<isNotNull prepend="," property="zzbj">ZZBJ = #zzbj#</isNotNull>
		<isNotNull prepend="," property="finuseSsqk">FINUSE_SSQK = #finuseSsqk#</isNotNull>
		<isNotNull prepend="," property="finuseSsdate">FINUSE_SSDATE = #finuseSsdate#</isNotNull>
		</dynamic>
		WHERE 		PATENT_ID=#patentId# 			</update>
	
	<update id="updatewithnull" parameterClass="tKIZLPatentInfoHg">
		UPDATE ${zzzcSchema}.T_KIZL_PATENT_INFO_HG	
		SET 
		<dynamic prepend=" ">
	    <isNotNull prepend=", " property="patentId">PATENT_ID = #patentId#</isNotNull>
	    <isNull prepend=", " property="patentId">PATENT_ID = NULL</isNull>
	    <isNotNull prepend=", " property="applyId">APPLY_ID = #applyId#</isNotNull>
	    <isNull prepend=", " property="applyId">APPLY_ID = NULL</isNull>
	    <isNotNull prepend=", " property="serialNum">SERIAL_NUM = #serialNum#</isNotNull>
	    <isNull prepend=", " property="serialNum">SERIAL_NUM = NULL</isNull>
	    <isNotNull prepend=", " property="jsbh">JSBH = #jsbh#</isNotNull>
	    <isNull prepend=", " property="jsbh">JSBH = NULL</isNull>
	    <isNotNull prepend=", " property="bgbh">BGBH = #bgbh#</isNotNull>
	    <isNull prepend=", " property="bgbh">BGBH = NULL</isNull>
	    <isNotNull prepend=", " property="applyName">APPLY_NAME = #applyName#</isNotNull>
	    <isNull prepend=", " property="applyName">APPLY_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="firstDeptCode">FIRST_DEPT_CODE = #firstDeptCode#</isNotNull>
	    <isNull prepend=", " property="firstDeptCode">FIRST_DEPT_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="firstDeptName">FIRST_DEPT_NAME = #firstDeptName#</isNotNull>
	    <isNull prepend=", " property="firstDeptName">FIRST_DEPT_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="gldwCode">GLDW_CODE = #gldwCode#</isNotNull>
	    <isNull prepend=", " property="gldwCode">GLDW_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="gldwName">GLDW_NAME = #gldwName#</isNotNull>
	    <isNull prepend=", " property="gldwName">GLDW_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="label">LABEL = #label#</isNotNull>
	    <isNull prepend=", " property="label">LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="techArea">TECH_AREA = #techArea#</isNotNull>
	    <isNull prepend=", " property="techArea">TECH_AREA = NULL</isNull>
	    <isNotNull prepend=", " property="usePropose">USE_PROPOSE = #usePropose#</isNotNull>
	    <isNull prepend=", " property="usePropose">USE_PROPOSE = NULL</isNull>
	    <isNotNull prepend=", " property="knowledgeClass">KNOWLEDGE_CLASS = #knowledgeClass#</isNotNull>
	    <isNull prepend=", " property="knowledgeClass">KNOWLEDGE_CLASS = NULL</isNull>
	    <isNotNull prepend=", " property="lxrCode">LXR_CODE = #lxrCode#</isNotNull>
	    <isNull prepend=", " property="lxrCode">LXR_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="lxrName">LXR_NAME = #lxrName#</isNotNull>
	    <isNull prepend=", " property="lxrName">LXR_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="lxrPhone">LXR_PHONE = #lxrPhone#</isNotNull>
	    <isNull prepend=", " property="lxrPhone">LXR_PHONE = NULL</isNull>
	    <isNotNull prepend=", " property="lxrEmail">LXR_EMAIL = #lxrEmail#</isNotNull>
	    <isNull prepend=", " property="lxrEmail">LXR_EMAIL = NULL</isNull>
	    <isNotNull prepend=", " property="lxrMobile">LXR_MOBILE = #lxrMobile#</isNotNull>
	    <isNull prepend=", " property="lxrMobile">LXR_MOBILE = NULL</isNull>
	    <isNotNull prepend=", " property="fromType">FROM_TYPE = #fromType#</isNotNull>
	    <isNull prepend=", " property="fromType">FROM_TYPE = NULL</isNull>
	    <isNotNull prepend=", " property="fromNo">FROM_NO = #fromNo#</isNotNull>
	    <isNull prepend=", " property="fromNo">FROM_NO = NULL</isNull>
	    <isNotNull prepend=", " property="fromName">FROM_NAME = #fromName#</isNotNull>
	    <isNull prepend=", " property="fromName">FROM_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="fromContent">FROM_CONTENT = #fromContent#</isNotNull>
	    <isNull prepend=", " property="fromContent">FROM_CONTENT = NULL</isNull>
	    <isNotNull prepend=", " property="useMethod">USE_METHOD = #useMethod#</isNotNull>
	    <isNull prepend=", " property="useMethod">USE_METHOD = NULL</isNull>
	    <isNotNull prepend=", " property="useDept">USE_DEPT = #useDept#</isNotNull>
	    <isNull prepend=", " property="useDept">USE_DEPT = NULL</isNull>
	    <isNotNull prepend=", " property="useDeptName">USE_DEPT_NAME = #useDeptName#</isNotNull>
	    <isNull prepend=", " property="useDeptName">USE_DEPT_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="useExpected">USE_EXPECTED = #useExpected#</isNotNull>
	    <isNull prepend=", " property="useExpected">USE_EXPECTED = NULL</isNull>
	    <isNotNull prepend=", " property="useExpectedName">USE_EXPECTED_NAME = #useExpectedName#</isNotNull>
	    <isNull prepend=", " property="useExpectedName">USE_EXPECTED_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="useFirstdate">USE_FIRSTDATE = #useFirstdate#</isNotNull>
	    <isNull prepend=", " property="useFirstdate">USE_FIRSTDATE = NULL</isNull>
	    <isNotNull prepend=", " property="reasonNouse">REASON_NOUSE = #reasonNouse#</isNotNull>
	    <isNull prepend=", " property="reasonNouse">REASON_NOUSE = NULL</isNull>
	    <isNotNull prepend=", " property="contentNouse">CONTENT_NOUSE = #contentNouse#</isNotNull>
	    <isNull prepend=", " property="contentNouse">CONTENT_NOUSE = NULL</isNull>
	    <isNotNull prepend=", " property="jpgFlag">JPG_FLAG = #jpgFlag#</isNotNull>
	    <isNull prepend=", " property="jpgFlag">JPG_FLAG = NULL</isNull>
	    <isNotNull prepend=", " property="jpgXh">JPG_XH = #jpgXh#</isNotNull>
	    <isNull prepend=", " property="jpgXh">JPG_XH = NULL</isNull>
	    <isNotNull prepend=", " property="jpgGw">JPG_GW = #jpgGw#</isNotNull>
	    <isNull prepend=", " property="jpgGw">JPG_GW = NULL</isNull>
	    <isNotNull prepend=", " property="jpgTeam">JPG_TEAM = #jpgTeam#</isNotNull>
	    <isNull prepend=", " property="jpgTeam">JPG_TEAM = NULL</isNull>
	    <isNotNull prepend=", " property="isbigxm">ISBIGXM = #isbigxm#</isNotNull>
	    <isNull prepend=", " property="isbigxm">ISBIGXM = NULL</isNull>
	    <isNotNull prepend=", " property="jfdwCode">JFDW_CODE = #jfdwCode#</isNotNull>
	    <isNull prepend=", " property="jfdwCode">JFDW_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="flowStatus">FLOW_STATUS = #flowStatus#</isNotNull>
	    <isNull prepend=", " property="flowStatus">FLOW_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="patentStatus">PATENT_STATUS = #patentStatus#</isNotNull>
	    <isNull prepend=", " property="patentStatus">PATENT_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="djdPerson">DJD_PERSON = #djdPerson#</isNotNull>
	    <isNull prepend=", " property="djdPerson">DJD_PERSON = NULL</isNull>
	    <isNotNull prepend=", " property="djdDate">DJD_DATE = #djdDate#</isNotNull>
	    <isNull prepend=", " property="djdDate">DJD_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="jdPerson">JD_PERSON = #jdPerson#</isNotNull>
	    <isNull prepend=", " property="jdPerson">JD_PERSON = NULL</isNull>
	    <isNotNull prepend=", " property="jdDate">JD_DATE = #jdDate#</isNotNull>
	    <isNull prepend=", " property="jdDate">JD_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="qs">QS = #qs#</isNotNull>
	    <isNull prepend=", " property="qs">QS = NULL</isNull>
	    <isNotNull prepend=", " property="slrq">SLRQ = #slrq#</isNotNull>
	    <isNull prepend=", " property="slrq">SLRQ = NULL</isNull>
	    <isNotNull prepend=", " property="patentNo">PATENT_NO = #patentNo#</isNotNull>
	    <isNull prepend=", " property="patentNo">PATENT_NO = NULL</isNull>
	    <isNotNull prepend=", " property="patentType">PATENT_TYPE = #patentType#</isNotNull>
	    <isNull prepend=", " property="patentType">PATENT_TYPE = NULL</isNull>
	    <isNotNull prepend=", " property="yxqr">YXQR = #yxqr#</isNotNull>
	    <isNull prepend=", " property="yxqr">YXQR = NULL</isNull>
	    <isNotNull prepend=", " property="qlyqsl">QLYQSL = #qlyqsl#</isNotNull>
	    <isNull prepend=", " property="qlyqsl">QLYQSL = NULL</isNull>
	    <isNotNull prepend=", " property="smsys">SMSYS = #smsys#</isNotNull>
	    <isNull prepend=", " property="smsys">SMSYS = NULL</isNull>
	    <isNotNull prepend=", " property="swsGuid">SWS_GUID = #swsGuid#</isNotNull>
	    <isNull prepend=", " property="swsGuid">SWS_GUID = NULL</isNull>
	    <isNotNull prepend=", " property="swsdlr">SWSDLR = #swsdlr#</isNotNull>
	    <isNull prepend=", " property="swsdlr">SWSDLR = NULL</isNull>
	    <isNotNull prepend=", " property="swsdlrPhone">SWSDLR_PHONE = #swsdlrPhone#</isNotNull>
	    <isNull prepend=", " property="swsdlrPhone">SWSDLR_PHONE = NULL</isNull>
	    <isNotNull prepend=", " property="swsdlrEmail">SWSDLR_EMAIL = #swsdlrEmail#</isNotNull>
	    <isNull prepend=", " property="swsdlrEmail">SWSDLR_EMAIL = NULL</isNull>
	    <isNotNull prepend=", " property="moneyDlf">MONEY_DLF = #moneyDlf#</isNotNull>
	    <isNull prepend=", " property="moneyDlf">MONEY_DLF = NULL</isNull>
	    <isNotNull prepend=", " property="moneyQf">MONEY_QF = #moneyQf#</isNotNull>
	    <isNull prepend=", " property="moneyQf">MONEY_QF = NULL</isNull>
	    <isNotNull prepend=", " property="moneySqf">MONEY_SQF = #moneySqf#</isNotNull>
	    <isNull prepend=", " property="moneySqf">MONEY_SQF = NULL</isNull>
	    <isNotNull prepend=", " property="moneyGbysf">MONEY_GBYSF = #moneyGbysf#</isNotNull>
	    <isNull prepend=", " property="moneyGbysf">MONEY_GBYSF = NULL</isNull>
	    <isNotNull prepend=", " property="moneySmsfjf">MONEY_SMSFJF = #moneySmsfjf#</isNotNull>
	    <isNull prepend=", " property="moneySmsfjf">MONEY_SMSFJF = NULL</isNull>
	    <isNotNull prepend=", " property="moneyYhs">MONEY_YHS = #moneyYhs#</isNotNull>
	    <isNull prepend=", " property="moneyYhs">MONEY_YHS = NULL</isNull>
	    <isNotNull prepend=", " property="sqtzFwdate">SQTZ_FWDATE = #sqtzFwdate#</isNotNull>
	    <isNull prepend=", " property="sqtzFwdate">SQTZ_FWDATE = NULL</isNull>
	    <isNotNull prepend=", " property="moneyFirst">MONEY_FIRST = #moneyFirst#</isNotNull>
	    <isNull prepend=", " property="moneyFirst">MONEY_FIRST = NULL</isNull>
	    <isNotNull prepend=", " property="zlh">ZLH = #zlh#</isNotNull>
	    <isNull prepend=", " property="zlh">ZLH = NULL</isNull>
	    <isNotNull prepend=", " property="sqrq">SQRQ = #sqrq#</isNotNull>
	    <isNull prepend=", " property="sqrq">SQRQ = NULL</isNull>
	    <isNotNull prepend=", " property="flzt">FLZT = #flzt#</isNotNull>
	    <isNull prepend=", " property="flzt">FLZT = NULL</isNull>
	    <isNotNull prepend=", " property="isvalid">ISVALID = #isvalid#</isNotNull>
	    <isNull prepend=", " property="isvalid">ISVALID = NULL</isNull>
	    <isNotNull prepend=", " property="isgdArchive">ISGD_ARCHIVE = #isgdArchive#</isNotNull>
	    <isNull prepend=", " property="isgdArchive">ISGD_ARCHIVE = NULL</isNull>
	    <isNotNull prepend=", " property="archiveGuid">ARCHIVE_GUID = #archiveGuid#</isNotNull>
	    <isNull prepend=", " property="archiveGuid">ARCHIVE_GUID = NULL</isNull>
	    <isNotNull prepend=", " property="isgdDz">ISGD_DZ = #isgdDz#</isNotNull>
	    <isNull prepend=", " property="isgdDz">ISGD_DZ = NULL</isNull>
	    <isNotNull prepend=", " property="extra1">EXTRA1 = #extra1#</isNotNull>
	    <isNull prepend=", " property="extra1">EXTRA1 = NULL</isNull>
	    <isNotNull prepend=", " property="extra2">EXTRA2 = #extra2#</isNotNull>
	    <isNull prepend=", " property="extra2">EXTRA2 = NULL</isNull>
	    <isNotNull prepend=", " property="extra3">EXTRA3 = #extra3#</isNotNull>
	    <isNull prepend=", " property="extra3">EXTRA3 = NULL</isNull>
	    <isNotNull prepend=", " property="extra4">EXTRA4 = #extra4#</isNotNull>
	    <isNull prepend=", " property="extra4">EXTRA4 = NULL</isNull>
	    <isNotNull prepend=", " property="extra5">EXTRA5 = #extra5#</isNotNull>
	    <isNull prepend=", " property="extra5">EXTRA5 = NULL</isNull>
	    <isNotNull prepend=", " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
	    <isNull prepend=", " property="delStatus">DEL_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
	    <isNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="createDate">CREATE_DATE = #createDate#</isNotNull>
	    <isNull prepend=", " property="createDate">CREATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
	    <isNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNull prepend=", " property="updateDate">UPDATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
	    <isNull prepend=", " property="recordVersion">RECORD_VERSION = NULL</isNull>
	    <isNotNull prepend=", " property="firstDeptPath">FIRST_DEPT_PATH = #firstDeptPath#</isNotNull>
	    <isNull prepend=", " property="firstDeptPath">FIRST_DEPT_PATH = NULL</isNull>
	    <isNotNull prepend=", " property="swsNo">SWS_NO = #swsNo#</isNotNull>
	    <isNull prepend=", " property="swsNo">SWS_NO = NULL</isNull>
	    <isNotNull prepend=", " property="dlrNo">DLR_NO = #dlrNo#</isNotNull>
	    <isNull prepend=", " property="dlrNo">DLR_NO = NULL</isNull>
	    <isNotNull prepend=", " property="djdPersonName">DJD_PERSON_NAME = #djdPersonName#</isNotNull>
	    <isNull prepend=", " property="djdPersonName">DJD_PERSON_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="jdPersonName">JD_PERSON_NAME = #jdPersonName#</isNotNull>
	    <isNull prepend=", " property="jdPersonName">JD_PERSON_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="fml">FML = #fml#</isNotNull>
	    <isNull prepend=", " property="fml">FML = NULL</isNull>
	    <isNotNull prepend=", " property="fmrCode">FMR_CODE = #fmrCode#</isNotNull>
	    <isNull prepend=", " property="fmrCode">FMR_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="djrDate">DJR_DATE = #djrDate#</isNotNull>
	    <isNull prepend=", " property="djrDate">DJR_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="moneyGgysf">MONEY_GGYSF = #moneyGgysf#</isNotNull>
	    <isNull prepend=", " property="moneyGgysf">MONEY_GGYSF = NULL</isNull>
	    <isNotNull prepend=", " property="zzrq">ZZRQ = #zzrq#</isNotNull>
	    <isNull prepend=", " property="zzrq">ZZRQ = NULL</isNull>
	    <isNotNull prepend=", " property="zzbz">ZZBZ = #zzbz#</isNotNull>
	    <isNull prepend=", " property="zzbz">ZZBZ = NULL</isNull>
	    <isNotNull prepend=", " property="zztxr">ZZTXR = #zztxr#</isNotNull>
	    <isNull prepend=", " property="zztxr">ZZTXR = NULL</isNull>
	    <isNotNull prepend=", " property="zztxrq">ZZTXRQ = #zztxrq#</isNotNull>
	    <isNull prepend=", " property="zztxrq">ZZTXRQ = NULL</isNull>
	    <isNotNull prepend=", " property="zzbj">ZZBJ = #zzbj#</isNotNull>
	    <isNull prepend=", " property="zzbj">ZZBJ = NULL</isNull>
	    <isNotNull prepend=", " property="finuseSsqk">FINUSE_SSQK = #finuseSsqk#</isNotNull>
	    <isNull prepend=", " property="finuseSsqk">FINUSE_SSQK = NULL</isNull>
	    <isNotNull prepend=", " property="finuseSsdate">FINUSE_SSDATE = #finuseSsdate#</isNotNull>
	    <isNull prepend=", " property="finuseSsdate">FINUSE_SSDATE = NULL</isNull>
		</dynamic>
		WHERE 		PATENT_ID=#patentId# 			</update>
	
	<update id="updateByC" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_PATENT_INFO_HG	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="patentId">PATENT_ID = #patentId#</isNotNull>
				<isNotNull prepend="," property="applyId">APPLY_ID = #applyId#</isNotNull>
				<isNotNull prepend="," property="serialNum">SERIAL_NUM = #serialNum#</isNotNull>
				<isNotNull prepend="," property="jsbh">JSBH = #jsbh#</isNotNull>
				<isNotNull prepend="," property="bgbh">BGBH = #bgbh#</isNotNull>
				<isNotNull prepend="," property="applyName">APPLY_NAME = #applyName#</isNotNull>
				<isNotNull prepend="," property="firstDeptCode">FIRST_DEPT_CODE = #firstDeptCode#</isNotNull>
				<isNotNull prepend="," property="firstDeptName">FIRST_DEPT_NAME = #firstDeptName#</isNotNull>
				<isNotNull prepend="," property="gldwCode">GLDW_CODE = #gldwCode#</isNotNull>
				<isNotNull prepend="," property="gldwName">GLDW_NAME = #gldwName#</isNotNull>
				<isNotNull prepend="," property="label">LABEL = #label#</isNotNull>
				<isNotNull prepend="," property="techArea">TECH_AREA = #techArea#</isNotNull>
				<isNotNull prepend="," property="usePropose">USE_PROPOSE = #usePropose#</isNotNull>
				<isNotNull prepend="," property="knowledgeClass">KNOWLEDGE_CLASS = #knowledgeClass#</isNotNull>
				<isNotNull prepend="," property="lxrCode">LXR_CODE = #lxrCode#</isNotNull>
				<isNotNull prepend="," property="lxrName">LXR_NAME = #lxrName#</isNotNull>
				<isNotNull prepend="," property="lxrPhone">LXR_PHONE = #lxrPhone#</isNotNull>
				<isNotNull prepend="," property="lxrEmail">LXR_EMAIL = #lxrEmail#</isNotNull>
				<isNotNull prepend="," property="lxrMobile">LXR_MOBILE = #lxrMobile#</isNotNull>
				<isNotNull prepend="," property="fromType">FROM_TYPE = #fromType#</isNotNull>
				<isNotNull prepend="," property="fromNo">FROM_NO = #fromNo#</isNotNull>
				<isNotNull prepend="," property="fromName">FROM_NAME = #fromName#</isNotNull>
				<isNotNull prepend="," property="fromContent">FROM_CONTENT = #fromContent#</isNotNull>
				<isNotNull prepend="," property="useMethod">USE_METHOD = #useMethod#</isNotNull>
				<isNotNull prepend="," property="useDept">USE_DEPT = #useDept#</isNotNull>
				<isNotNull prepend="," property="useDeptName">USE_DEPT_NAME = #useDeptName#</isNotNull>
				<isNotNull prepend="," property="useExpected">USE_EXPECTED = #useExpected#</isNotNull>
				<isNotNull prepend="," property="useExpectedName">USE_EXPECTED_NAME = #useExpectedName#</isNotNull>
				<isNotNull prepend="," property="useFirstdate">USE_FIRSTDATE = #useFirstdate#</isNotNull>
				<isNotNull prepend="," property="reasonNouse">REASON_NOUSE = #reasonNouse#</isNotNull>
				<isNotNull prepend="," property="contentNouse">CONTENT_NOUSE = #contentNouse#</isNotNull>
				<isNotNull prepend="," property="jpgFlag">JPG_FLAG = #jpgFlag#</isNotNull>
				<isNotNull prepend="," property="jpgXh">JPG_XH = #jpgXh#</isNotNull>
				<isNotNull prepend="," property="jpgGw">JPG_GW = #jpgGw#</isNotNull>
				<isNotNull prepend="," property="jpgTeam">JPG_TEAM = #jpgTeam#</isNotNull>
				<isNotNull prepend="," property="isbigxm">ISBIGXM = #isbigxm#</isNotNull>
				<isNotNull prepend="," property="jfdwCode">JFDW_CODE = #jfdwCode#</isNotNull>
				<isNotNull prepend="," property="flowStatus">FLOW_STATUS = #flowStatus#</isNotNull>
				<isNotNull prepend="," property="patentStatus">PATENT_STATUS = #patentStatus#</isNotNull>
				<isNotNull prepend="," property="djdPerson">DJD_PERSON = #djdPerson#</isNotNull>
				<isNotNull prepend="," property="djdDate">DJD_DATE = #djdDate#</isNotNull>
				<isNotNull prepend="," property="jdPerson">JD_PERSON = #jdPerson#</isNotNull>
				<isNotNull prepend="," property="jdDate">JD_DATE = #jdDate#</isNotNull>
				<isNotNull prepend="," property="qs">QS = #qs#</isNotNull>
				<isNotNull prepend="," property="slrq">SLRQ = #slrq#</isNotNull>
				<isNotNull prepend="," property="patentNo">PATENT_NO = #patentNo#</isNotNull>
				<isNotNull prepend="," property="patentType">PATENT_TYPE = #patentType#</isNotNull>
				<isNotNull prepend="," property="yxqr">YXQR = #yxqr#</isNotNull>
				<isNotNull prepend="," property="qlyqsl">QLYQSL = #qlyqsl#</isNotNull>
				<isNotNull prepend="," property="smsys">SMSYS = #smsys#</isNotNull>
				<isNotNull prepend="," property="swsGuid">SWS_GUID = #swsGuid#</isNotNull>
				<isNotNull prepend="," property="swsdlr">SWSDLR = #swsdlr#</isNotNull>
				<isNotNull prepend="," property="swsdlrPhone">SWSDLR_PHONE = #swsdlrPhone#</isNotNull>
				<isNotNull prepend="," property="swsdlrEmail">SWSDLR_EMAIL = #swsdlrEmail#</isNotNull>
				<isNotNull prepend="," property="moneyDlf">MONEY_DLF = #moneyDlf#</isNotNull>
				<isNotNull prepend="," property="moneyQf">MONEY_QF = #moneyQf#</isNotNull>
				<isNotNull prepend="," property="moneySqf">MONEY_SQF = #moneySqf#</isNotNull>
				<isNotNull prepend="," property="moneyGbysf">MONEY_GBYSF = #moneyGbysf#</isNotNull>
				<isNotNull prepend="," property="moneySmsfjf">MONEY_SMSFJF = #moneySmsfjf#</isNotNull>
				<isNotNull prepend="," property="moneyYhs">MONEY_YHS = #moneyYhs#</isNotNull>
				<isNotNull prepend="," property="sqtzFwdate">SQTZ_FWDATE = #sqtzFwdate#</isNotNull>
				<isNotNull prepend="," property="moneyFirst">MONEY_FIRST = #moneyFirst#</isNotNull>
				<isNotNull prepend="," property="zlh">ZLH = #zlh#</isNotNull>
				<isNotNull prepend="," property="sqrq">SQRQ = #sqrq#</isNotNull>
				<isNotNull prepend="," property="flzt">FLZT = #flzt#</isNotNull>
				<isNotNull prepend="," property="isvalid">ISVALID = #isvalid#</isNotNull>
				<isNotNull prepend="," property="isgdArchive">ISGD_ARCHIVE = #isgdArchive#</isNotNull>
				<isNotNull prepend="," property="archiveGuid">ARCHIVE_GUID = #archiveGuid#</isNotNull>
				<isNotNull prepend="," property="isgdDz">ISGD_DZ = #isgdDz#</isNotNull>
				<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
				<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
				<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
				<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
				<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
				<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
				<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
				<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
				<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
				<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
				<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
				<isNotNull prepend="," property="firstDeptPath">FIRST_DEPT_PATH = #firstDeptPath#</isNotNull>
				<isNotNull prepend="," property="swsNo">SWS_NO = #swsNo#</isNotNull>
				<isNotNull prepend="," property="dlrNo">DLR_NO = #dlrNo#</isNotNull>
				<isNotNull prepend="," property="djdPersonName">DJD_PERSON_NAME = #djdPersonName#</isNotNull>
				<isNotNull prepend="," property="jdPersonName">JD_PERSON_NAME = #jdPersonName#</isNotNull>
				<isNotNull prepend="," property="fml">FML = #fml#</isNotNull>
				<isNotNull prepend="," property="fmrCode">FMR_CODE = #fmrCode#</isNotNull>
				<isNotNull prepend="," property="djrDate">DJR_DATE = #djrDate#</isNotNull>
				<isNotNull prepend="," property="moneyGgysf">MONEY_GGYSF = #moneyGgysf#</isNotNull>
				<isNotNull prepend="," property="zzrq">ZZRQ = #zzrq#</isNotNull>
				<isNotNull prepend="," property="zzbz">ZZBZ = #zzbz#</isNotNull>
				<isNotNull prepend="," property="zztxr">ZZTXR = #zztxr#</isNotNull>
				<isNotNull prepend="," property="zztxrq">ZZTXRQ = #zztxrq#</isNotNull>
				<isNotNull prepend="," property="zzbj">ZZBJ = #zzbj#</isNotNull>
				<isNotNull prepend="," property="finuseSsqk">FINUSE_SSQK = #finuseSsqk#</isNotNull>
				<isNotNull prepend="," property="finuseSsdate">FINUSE_SSDATE = #finuseSsdate#</isNotNull>
			</dynamic>
		<dynamic prepend=" WHERE ">
		 			<isNotNull prepend=" AND " property="patentIdOld">PATENT_ID = #patentIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="applyIdOld">APPLY_ID = #applyIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="serialNumOld">SERIAL_NUM = #serialNumOld#</isNotNull>
			<isNotNull prepend=" AND " property="jsbhOld">JSBH = #jsbhOld#</isNotNull>
			<isNotNull prepend=" AND " property="bgbhOld">BGBH = #bgbhOld#</isNotNull>
			<isNotNull prepend=" AND " property="applyNameOld">APPLY_NAME = #applyNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="firstDeptCodeOld">FIRST_DEPT_CODE = #firstDeptCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="firstDeptNameOld">FIRST_DEPT_NAME = #firstDeptNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="gldwCodeOld">GLDW_CODE = #gldwCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="gldwNameOld">GLDW_NAME = #gldwNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="labelOld">LABEL = #labelOld#</isNotNull>
			<isNotNull prepend=" AND " property="techAreaOld">TECH_AREA = #techAreaOld#</isNotNull>
			<isNotNull prepend=" AND " property="useProposeOld">USE_PROPOSE = #useProposeOld#</isNotNull>
			<isNotNull prepend=" AND " property="knowledgeClassOld">KNOWLEDGE_CLASS = #knowledgeClassOld#</isNotNull>
			<isNotNull prepend=" AND " property="lxrCodeOld">LXR_CODE = #lxrCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="lxrNameOld">LXR_NAME = #lxrNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="lxrPhoneOld">LXR_PHONE = #lxrPhoneOld#</isNotNull>
			<isNotNull prepend=" AND " property="lxrEmailOld">LXR_EMAIL = #lxrEmailOld#</isNotNull>
			<isNotNull prepend=" AND " property="lxrMobileOld">LXR_MOBILE = #lxrMobileOld#</isNotNull>
			<isNotNull prepend=" AND " property="fromTypeOld">FROM_TYPE = #fromTypeOld#</isNotNull>
			<isNotNull prepend=" AND " property="fromNoOld">FROM_NO = #fromNoOld#</isNotNull>
			<isNotNull prepend=" AND " property="fromNameOld">FROM_NAME = #fromNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="fromContentOld">FROM_CONTENT = #fromContentOld#</isNotNull>
			<isNotNull prepend=" AND " property="useMethodOld">USE_METHOD = #useMethodOld#</isNotNull>
			<isNotNull prepend=" AND " property="useDeptOld">USE_DEPT = #useDeptOld#</isNotNull>
			<isNotNull prepend=" AND " property="useDeptNameOld">USE_DEPT_NAME = #useDeptNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="useExpectedOld">USE_EXPECTED = #useExpectedOld#</isNotNull>
			<isNotNull prepend=" AND " property="useExpectedNameOld">USE_EXPECTED_NAME = #useExpectedNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="useFirstdateOld">USE_FIRSTDATE = #useFirstdateOld#</isNotNull>
			<isNotNull prepend=" AND " property="reasonNouseOld">REASON_NOUSE = #reasonNouseOld#</isNotNull>
			<isNotNull prepend=" AND " property="contentNouseOld">CONTENT_NOUSE = #contentNouseOld#</isNotNull>
			<isNotNull prepend=" AND " property="jpgFlagOld">JPG_FLAG = #jpgFlagOld#</isNotNull>
			<isNotNull prepend=" AND " property="jpgXhOld">JPG_XH = #jpgXhOld#</isNotNull>
			<isNotNull prepend=" AND " property="jpgGwOld">JPG_GW = #jpgGwOld#</isNotNull>
			<isNotNull prepend=" AND " property="jpgTeamOld">JPG_TEAM = #jpgTeamOld#</isNotNull>
			<isNotNull prepend=" AND " property="isbigxmOld">ISBIGXM = #isbigxmOld#</isNotNull>
			<isNotNull prepend=" AND " property="jfdwCodeOld">JFDW_CODE = #jfdwCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="flowStatusOld">FLOW_STATUS = #flowStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="patentStatusOld">PATENT_STATUS = #patentStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="djdPersonOld">DJD_PERSON = #djdPersonOld#</isNotNull>
			<isNotNull prepend=" AND " property="djdDateOld">DJD_DATE = #djdDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="jdPersonOld">JD_PERSON = #jdPersonOld#</isNotNull>
			<isNotNull prepend=" AND " property="jdDateOld">JD_DATE = #jdDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="qsOld">QS = #qsOld#</isNotNull>
			<isNotNull prepend=" AND " property="slrqOld">SLRQ = #slrqOld#</isNotNull>
			<isNotNull prepend=" AND " property="patentNoOld">PATENT_NO = #patentNoOld#</isNotNull>
			<isNotNull prepend=" AND " property="patentTypeOld">PATENT_TYPE = #patentTypeOld#</isNotNull>
			<isNotNull prepend=" AND " property="yxqrOld">YXQR = #yxqrOld#</isNotNull>
			<isNotNull prepend=" AND " property="qlyqslOld">QLYQSL = #qlyqslOld#</isNotNull>
			<isNotNull prepend=" AND " property="smsysOld">SMSYS = #smsysOld#</isNotNull>
			<isNotNull prepend=" AND " property="swsGuidOld">SWS_GUID = #swsGuidOld#</isNotNull>
			<isNotNull prepend=" AND " property="swsdlrOld">SWSDLR = #swsdlrOld#</isNotNull>
			<isNotNull prepend=" AND " property="swsdlrPhoneOld">SWSDLR_PHONE = #swsdlrPhoneOld#</isNotNull>
			<isNotNull prepend=" AND " property="swsdlrEmailOld">SWSDLR_EMAIL = #swsdlrEmailOld#</isNotNull>
			<isNotNull prepend=" AND " property="moneyDlfOld">MONEY_DLF = #moneyDlfOld#</isNotNull>
			<isNotNull prepend=" AND " property="moneyQfOld">MONEY_QF = #moneyQfOld#</isNotNull>
			<isNotNull prepend=" AND " property="moneySqfOld">MONEY_SQF = #moneySqfOld#</isNotNull>
			<isNotNull prepend=" AND " property="moneyGbysfOld">MONEY_GBYSF = #moneyGbysfOld#</isNotNull>
			<isNotNull prepend=" AND " property="moneySmsfjfOld">MONEY_SMSFJF = #moneySmsfjfOld#</isNotNull>
			<isNotNull prepend=" AND " property="moneyYhsOld">MONEY_YHS = #moneyYhsOld#</isNotNull>
			<isNotNull prepend=" AND " property="sqtzFwdateOld">SQTZ_FWDATE = #sqtzFwdateOld#</isNotNull>
			<isNotNull prepend=" AND " property="moneyFirstOld">MONEY_FIRST = #moneyFirstOld#</isNotNull>
			<isNotNull prepend=" AND " property="zlhOld">ZLH = #zlhOld#</isNotNull>
			<isNotNull prepend=" AND " property="sqrqOld">SQRQ = #sqrqOld#</isNotNull>
			<isNotNull prepend=" AND " property="flztOld">FLZT = #flztOld#</isNotNull>
			<isNotNull prepend=" AND " property="isvalidOld">ISVALID = #isvalidOld#</isNotNull>
			<isNotNull prepend=" AND " property="isgdArchiveOld">ISGD_ARCHIVE = #isgdArchiveOld#</isNotNull>
			<isNotNull prepend=" AND " property="archiveGuidOld">ARCHIVE_GUID = #archiveGuidOld#</isNotNull>
			<isNotNull prepend=" AND " property="isgdDzOld">ISGD_DZ = #isgdDzOld#</isNotNull>
			<isNotNull prepend=" AND " property="extra1Old">EXTRA1 = #extra1Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra2Old">EXTRA2 = #extra2Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra3Old">EXTRA3 = #extra3Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra4Old">EXTRA4 = #extra4Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra5Old">EXTRA5 = #extra5Old#</isNotNull>
			<isNotNull prepend=" AND " property="delStatusOld">DEL_STATUS = #delStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabelOld">CREATE_USER_LABEL = #createUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="createDateOld">CREATE_DATE = #createDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabelOld">UPDATE_USER_LABEL = #updateUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateDateOld">UPDATE_DATE = #updateDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabelOld">DELETE_USER_LABEL = #deleteUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDateOld">DELETE_DATE = #deleteDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersionOld">RECORD_VERSION = #recordVersionOld#</isNotNull>
			<isNotNull prepend=" AND " property="firstDeptPathOld">FIRST_DEPT_PATH = #firstDeptPathOld#</isNotNull>
			<isNotNull prepend=" AND " property="swsNoOld">SWS_NO = #swsNoOld#</isNotNull>
			<isNotNull prepend=" AND " property="dlrNoOld">DLR_NO = #dlrNoOld#</isNotNull>
			<isNotNull prepend=" AND " property="djdPersonNameOld">DJD_PERSON_NAME = #djdPersonNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="jdPersonNameOld">JD_PERSON_NAME = #jdPersonNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="fmlOld">FML = #fmlOld#</isNotNull>
			<isNotNull prepend=" AND " property="fmrCodeOld">FMR_CODE = #fmrCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="djrDateOld">DJR_DATE = #djrDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="moneyGgysfOld">MONEY_GGYSF = #moneyGgysfOld#</isNotNull>
			<isNotNull prepend=" AND " property="zzrqOld">ZZRQ = #zzrqOld#</isNotNull>
			<isNotNull prepend=" AND " property="zzbzOld">ZZBZ = #zzbzOld#</isNotNull>
			<isNotNull prepend=" AND " property="zztxrOld">ZZTXR = #zztxrOld#</isNotNull>
			<isNotNull prepend=" AND " property="zztxrqOld">ZZTXRQ = #zztxrqOld#</isNotNull>
			<isNotNull prepend=" AND " property="zzbjOld">ZZBJ = #zzbjOld#</isNotNull>
			<isNotNull prepend=" AND " property="finuseSsqkOld">FINUSE_SSQK = #finuseSsqkOld#</isNotNull>
			<isNotNull prepend=" AND " property="finuseSsdateOld">FINUSE_SSDATE = #finuseSsdateOld#</isNotNull>
			<isNotNull prepend=" AND " property="dynSqlOld"> $dynSqlOld$ </isNotNull>
		</dynamic>
	</update>
	
	<update id="updateNull" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_PATENT_INFO_HG	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="patentId">PATENT_ID = #patentId#</isNotNull>
			<isNotNull prepend="," property="applyId">APPLY_ID = #applyId#</isNotNull>
			<isNotNull prepend="," property="serialNum">SERIAL_NUM = #serialNum#</isNotNull>
			<isNotNull prepend="," property="jsbh">JSBH = #jsbh#</isNotNull>
			<isNotNull prepend="," property="bgbh">BGBH = #bgbh#</isNotNull>
			<isNotNull prepend="," property="applyName">APPLY_NAME = #applyName#</isNotNull>
			<isNotNull prepend="," property="firstDeptCode">FIRST_DEPT_CODE = #firstDeptCode#</isNotNull>
			<isNotNull prepend="," property="firstDeptName">FIRST_DEPT_NAME = #firstDeptName#</isNotNull>
			<isNotNull prepend="," property="gldwCode">GLDW_CODE = #gldwCode#</isNotNull>
			<isNotNull prepend="," property="gldwName">GLDW_NAME = #gldwName#</isNotNull>
			<isNotNull prepend="," property="label">LABEL = #label#</isNotNull>
			<isNotNull prepend="," property="techArea">TECH_AREA = #techArea#</isNotNull>
			<isNotNull prepend="," property="usePropose">USE_PROPOSE = #usePropose#</isNotNull>
			<isNotNull prepend="," property="knowledgeClass">KNOWLEDGE_CLASS = #knowledgeClass#</isNotNull>
			<isNotNull prepend="," property="lxrCode">LXR_CODE = #lxrCode#</isNotNull>
			<isNotNull prepend="," property="lxrName">LXR_NAME = #lxrName#</isNotNull>
			<isNotNull prepend="," property="lxrPhone">LXR_PHONE = #lxrPhone#</isNotNull>
			<isNotNull prepend="," property="lxrEmail">LXR_EMAIL = #lxrEmail#</isNotNull>
			<isNotNull prepend="," property="lxrMobile">LXR_MOBILE = #lxrMobile#</isNotNull>
			<isNotNull prepend="," property="fromType">FROM_TYPE = #fromType#</isNotNull>
			<isNotNull prepend="," property="fromNo">FROM_NO = #fromNo#</isNotNull>
			<isNotNull prepend="," property="fromName">FROM_NAME = #fromName#</isNotNull>
			<isNotNull prepend="," property="fromContent">FROM_CONTENT = #fromContent#</isNotNull>
			<isNotNull prepend="," property="useMethod">USE_METHOD = #useMethod#</isNotNull>
			<isNotNull prepend="," property="useDept">USE_DEPT = #useDept#</isNotNull>
			<isNotNull prepend="," property="useDeptName">USE_DEPT_NAME = #useDeptName#</isNotNull>
			<isNotNull prepend="," property="useExpected">USE_EXPECTED = #useExpected#</isNotNull>
			<isNotNull prepend="," property="useExpectedName">USE_EXPECTED_NAME = #useExpectedName#</isNotNull>
			<isNotNull prepend="," property="useFirstdate">USE_FIRSTDATE = #useFirstdate#</isNotNull>
			<isNotNull prepend="," property="reasonNouse">REASON_NOUSE = #reasonNouse#</isNotNull>
			<isNotNull prepend="," property="contentNouse">CONTENT_NOUSE = #contentNouse#</isNotNull>
			<isNotNull prepend="," property="jpgFlag">JPG_FLAG = #jpgFlag#</isNotNull>
			<isNotNull prepend="," property="jpgXh">JPG_XH = #jpgXh#</isNotNull>
			<isNotNull prepend="," property="jpgGw">JPG_GW = #jpgGw#</isNotNull>
			<isNotNull prepend="," property="jpgTeam">JPG_TEAM = #jpgTeam#</isNotNull>
			<isNotNull prepend="," property="isbigxm">ISBIGXM = #isbigxm#</isNotNull>
			<isNotNull prepend="," property="jfdwCode">JFDW_CODE = #jfdwCode#</isNotNull>
			<isNotNull prepend="," property="flowStatus">FLOW_STATUS = #flowStatus#</isNotNull>
			<isNotNull prepend="," property="patentStatus">PATENT_STATUS = #patentStatus#</isNotNull>
			<isNotNull prepend="," property="djdPerson">DJD_PERSON = #djdPerson#</isNotNull>
			<isNotNull prepend="," property="djdDate">DJD_DATE = #djdDate#</isNotNull>
			<isNotNull prepend="," property="jdPerson">JD_PERSON = #jdPerson#</isNotNull>
			<isNotNull prepend="," property="jdDate">JD_DATE = #jdDate#</isNotNull>
			<isNotNull prepend="," property="qs">QS = #qs#</isNotNull>
			<isNotNull prepend="," property="slrq">SLRQ = #slrq#</isNotNull>
			<isNotNull prepend="," property="patentNo">PATENT_NO = #patentNo#</isNotNull>
			<isNotNull prepend="," property="patentType">PATENT_TYPE = #patentType#</isNotNull>
			<isNotNull prepend="," property="yxqr">YXQR = #yxqr#</isNotNull>
			<isNotNull prepend="," property="qlyqsl">QLYQSL = #qlyqsl#</isNotNull>
			<isNotNull prepend="," property="smsys">SMSYS = #smsys#</isNotNull>
			<isNotNull prepend="," property="swsGuid">SWS_GUID = #swsGuid#</isNotNull>
			<isNotNull prepend="," property="swsdlr">SWSDLR = #swsdlr#</isNotNull>
			<isNotNull prepend="," property="swsdlrPhone">SWSDLR_PHONE = #swsdlrPhone#</isNotNull>
			<isNotNull prepend="," property="swsdlrEmail">SWSDLR_EMAIL = #swsdlrEmail#</isNotNull>
			<isNotNull prepend="," property="moneyDlf">MONEY_DLF = #moneyDlf#</isNotNull>
			<isNotNull prepend="," property="moneyQf">MONEY_QF = #moneyQf#</isNotNull>
			<isNotNull prepend="," property="moneySqf">MONEY_SQF = #moneySqf#</isNotNull>
			<isNotNull prepend="," property="moneyGbysf">MONEY_GBYSF = #moneyGbysf#</isNotNull>
			<isNotNull prepend="," property="moneySmsfjf">MONEY_SMSFJF = #moneySmsfjf#</isNotNull>
			<isNotNull prepend="," property="moneyYhs">MONEY_YHS = #moneyYhs#</isNotNull>
			<isNotNull prepend="," property="sqtzFwdate">SQTZ_FWDATE = #sqtzFwdate#</isNotNull>
			<isNotNull prepend="," property="moneyFirst">MONEY_FIRST = #moneyFirst#</isNotNull>
			<isNotNull prepend="," property="zlh">ZLH = #zlh#</isNotNull>
			<isNotNull prepend="," property="sqrq">SQRQ = #sqrq#</isNotNull>
			<isNotNull prepend="," property="flzt">FLZT = #flzt#</isNotNull>
			<isNotNull prepend="," property="isvalid">ISVALID = #isvalid#</isNotNull>
			<isNotNull prepend="," property="isgdArchive">ISGD_ARCHIVE = #isgdArchive#</isNotNull>
			<isNotNull prepend="," property="archiveGuid">ARCHIVE_GUID = #archiveGuid#</isNotNull>
			<isNotNull prepend="," property="isgdDz">ISGD_DZ = #isgdDz#</isNotNull>
			<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			<isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
			<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
			<isNotNull prepend="," property="firstDeptPath">FIRST_DEPT_PATH = #firstDeptPath#</isNotNull>
			<isNotNull prepend="," property="swsNo">SWS_NO = #swsNo#</isNotNull>
			<isNotNull prepend="," property="dlrNo">DLR_NO = #dlrNo#</isNotNull>
			<isNotNull prepend="," property="djdPersonName">DJD_PERSON_NAME = #djdPersonName#</isNotNull>
			<isNotNull prepend="," property="jdPersonName">JD_PERSON_NAME = #jdPersonName#</isNotNull>
			<isNotNull prepend="," property="fml">FML = #fml#</isNotNull>
			<isNotNull prepend="," property="fmrCode">FMR_CODE = #fmrCode#</isNotNull>
			<isNotNull prepend="," property="djrDate">DJR_DATE = #djrDate#</isNotNull>
			<isNotNull prepend="," property="moneyGgysf">MONEY_GGYSF = #moneyGgysf#</isNotNull>
			<isNotNull prepend="," property="zzrq">ZZRQ = #zzrq#</isNotNull>
			<isNotNull prepend="," property="zzbz">ZZBZ = #zzbz#</isNotNull>
			<isNotNull prepend="," property="zztxr">ZZTXR = #zztxr#</isNotNull>
			<isNotNull prepend="," property="zztxrq">ZZTXRQ = #zztxrq#</isNotNull>
			<isNotNull prepend="," property="zzbj">ZZBJ = #zzbj#</isNotNull>
			<isNotNull prepend="," property="finuseSsqk">FINUSE_SSQK = #finuseSsqk#</isNotNull>
			<isNotNull prepend="," property="finuseSsdate">FINUSE_SSDATE = #finuseSsdate#</isNotNull>
		</dynamic>
		WHERE 		PATENT_ID=#patentId# 			</update>	
</sqlMap>