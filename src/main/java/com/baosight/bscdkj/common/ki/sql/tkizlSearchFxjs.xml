<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlSearchFxjs">
	<typeAlias alias="tKIZLSearchFxjs" type="com.baosight.bscdkj.common.ki.domain.TkizlSearchFxjs"/>
	<select id="load" parameterClass="string" resultClass="tKIZLSearchFxjs">
		SELECT 
				FXJS_ID as "fxjsId" ,		
				PROJECT_CODE as "projectCode" ,		
				PROJECT_NAME as "projectName" ,		
				FZR as "fzr" ,		
				FZR_NAME as "fzrName" ,		
				XMZG as "xmzg" ,		
				XMZG_NAME as "xmzgName" ,		
				ZTFX as "ztfx" ,		
				<PERSON><PERSON> as "jsr" ,		
				<PERSON><PERSON><PERSON><PERSON>AM<PERSON> as "jsrName" ,		
				<PERSON><PERSON><PERSON> as "sjk" ,		
				<PERSON><PERSON><PERSON><PERSON><PERSON>AM<PERSON> as "sjkName" ,		
				<PERSON><PERSON><PERSON> as "jscl" ,		
				CORRE<PERSON><PERSON>ON as "correlation" ,		
				UNCORRELATED as "uncorrelated" ,		
				OPINION1 as "opinion1" ,		
				SIGN1 as "sign1" ,		
				SIGN_NAME1 as "signName1" ,		
				SIGN_DATE1 as "signDate1" ,		
				OPINION2 as "opinion2" ,		
				SIGN2 as "sign2" ,		
				SIGN_NAME2 as "signName2" ,		
				SIGN_DATE2 as "signDate2" ,		
				CQZG as "cqzg" ,		
				CQZG_NAME as "cqzgName" ,		
				STATUS as "status" ,		
				EXTRA1 as "extra1" ,		
				EXTRA2 as "extra2" ,		
				EXTRA3 as "extra3" ,		
				EXTRA4 as "extra4" ,		
				EXTRA5 as "extra5" ,		
				DEL_STATUS as "delStatus" ,		
				CREATE_USER_LABEL as "createUserLabel" ,		
				CREATE_DATE as "createDate" ,		
				UPDATE_USER_LABEL as "updateUserLabel" ,		
				UPDATE_DATE as "updateDate" ,		
				DELETE_USER_LABEL as "deleteUserLabel" ,		
				DELETE_DATE as "deleteDate" ,		
				RECORD_VERSION as "recordVersion" 		
				FROM ${zzzcSchema}.T_KIZL_SEARCH_FXJS
		WHERE   FXJS_ID=#value# 				
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tKIZLSearchFxjs">
		SELECT
				FXJS_ID  as "fxjsId" ,		
				PROJECT_CODE  as "projectCode" ,		
				PROJECT_NAME  as "projectName" ,		
				FZR  as "fzr" ,		
				FZR_NAME  as "fzrName" ,		
				XMZG  as "xmzg" ,		
				XMZG_NAME  as "xmzgName" ,		
				ZTFX  as "ztfx" ,		
				JSR  as "jsr" ,		
				JSR_NAME  as "jsrName" ,		
				SJK  as "sjk" ,		
				SJK_NAME  as "sjkName" ,		
				JSCL  as "jscl" ,		
				CORRELATION  as "correlation" ,		
				UNCORRELATED  as "uncorrelated" ,		
				OPINION1  as "opinion1" ,		
				SIGN1  as "sign1" ,		
				SIGN_NAME1  as "signName1" ,		
				SIGN_DATE1  as "signDate1" ,		
				OPINION2  as "opinion2" ,		
				SIGN2  as "sign2" ,		
				SIGN_NAME2  as "signName2" ,		
				SIGN_DATE2  as "signDate2" ,		
				CQZG  as "cqzg" ,		
				CQZG_NAME  as "cqzgName" ,		
				STATUS  as "status" ,		
				EXTRA1  as "extra1" ,		
				EXTRA2  as "extra2" ,		
				EXTRA3  as "extra3" ,		
				EXTRA4  as "extra4" ,		
				EXTRA5  as "extra5" ,		
				DEL_STATUS  as "delStatus" ,		
				CREATE_USER_LABEL  as "createUserLabel" ,		
				CREATE_DATE  as "createDate" ,		
				UPDATE_USER_LABEL  as "updateUserLabel" ,		
				UPDATE_DATE  as "updateDate" ,		
				DELETE_USER_LABEL  as "deleteUserLabel" ,		
				DELETE_DATE  as "deleteDate" ,		
				RECORD_VERSION  as "recordVersion" 		
				FROM ${zzzcSchema}.T_KIZL_SEARCH_FXJS
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="fxjsId">FXJS_ID =  #fxjsId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectCode">PROJECT_CODE =  #projectCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectName">PROJECT_NAME =  #projectName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fzr">FZR =  #fzr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="fzrName">FZR_NAME =  #fzrName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="xmzg">XMZG =  #xmzg#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="xmzgName">XMZG_NAME =  #xmzgName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ztfx">ZTFX =  #ztfx#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jsr">JSR =  #jsr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jsrName">JSR_NAME =  #jsrName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sjk">SJK =  #sjk#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sjkName">SJK_NAME =  #sjkName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jscl">JSCL =  #jscl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="correlation">CORRELATION =  #correlation#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="uncorrelated">UNCORRELATED =  #uncorrelated#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="opinion1">OPINION1 =  #opinion1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sign1">SIGN1 =  #sign1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="signName1">SIGN_NAME1 =  #signName1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="signDate1">SIGN_DATE1 =  #signDate1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="opinion2">OPINION2 =  #opinion2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sign2">SIGN2 =  #sign2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="signName2">SIGN_NAME2 =  #signName2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="signDate2">SIGN_DATE2 =  #signDate2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="cqzg">CQZG =  #cqzg#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="cqzgName">CQZG_NAME =  #cqzgName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="status">STATUS =  #status#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KIZL_SEARCH_FXJS 
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="fxjsId">FXJS_ID =  #fxjsId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCode">PROJECT_CODE =  #projectCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">PROJECT_NAME =  #projectName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzr">FZR =  #fzr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrName">FZR_NAME =  #fzrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="xmzg">XMZG =  #xmzg#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="xmzgName">XMZG_NAME =  #xmzgName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ztfx">ZTFX =  #ztfx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jsr">JSR =  #jsr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jsrName">JSR_NAME =  #jsrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sjk">SJK =  #sjk#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sjkName">SJK_NAME =  #sjkName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jscl">JSCL =  #jscl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="correlation">CORRELATION =  #correlation#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="uncorrelated">UNCORRELATED =  #uncorrelated#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="opinion1">OPINION1 =  #opinion1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sign1">SIGN1 =  #sign1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signName1">SIGN_NAME1 =  #signName1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signDate1">SIGN_DATE1 =  #signDate1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="opinion2">OPINION2 =  #opinion2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sign2">SIGN2 =  #sign2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signName2">SIGN_NAME2 =  #signName2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signDate2">SIGN_DATE2 =  #signDate2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cqzg">CQZG =  #cqzg#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cqzgName">CQZG_NAME =  #cqzgName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="status">STATUS =  #status#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tKIZLSearchFxjs">
		INSERT INTO ${zzzcSchema}.T_KIZL_SEARCH_FXJS ( 
		<dynamic prepend=" ">
					<isNotNull prepend=", " property="fxjsId">FXJS_ID </isNotNull>
					<isNotNull prepend=", " property="projectCode">PROJECT_CODE </isNotNull>
					<isNotNull prepend=", " property="projectName">PROJECT_NAME </isNotNull>
					<isNotNull prepend=", " property="fzr">FZR </isNotNull>
					<isNotNull prepend=", " property="fzrName">FZR_NAME </isNotNull>
					<isNotNull prepend=", " property="xmzg">XMZG </isNotNull>
					<isNotNull prepend=", " property="xmzgName">XMZG_NAME </isNotNull>
					<isNotNull prepend=", " property="ztfx">ZTFX </isNotNull>
					<isNotNull prepend=", " property="jsr">JSR </isNotNull>
					<isNotNull prepend=", " property="jsrName">JSR_NAME </isNotNull>
					<isNotNull prepend=", " property="sjk">SJK </isNotNull>
					<isNotNull prepend=", " property="sjkName">SJK_NAME </isNotNull>
					<isNotNull prepend=", " property="jscl">JSCL </isNotNull>
					<isNotNull prepend=", " property="correlation">CORRELATION </isNotNull>
					<isNotNull prepend=", " property="uncorrelated">UNCORRELATED </isNotNull>
					<isNotNull prepend=", " property="opinion1">OPINION1 </isNotNull>
					<isNotNull prepend=", " property="sign1">SIGN1 </isNotNull>
					<isNotNull prepend=", " property="signName1">SIGN_NAME1 </isNotNull>
					<isNotNull prepend=", " property="signDate1">SIGN_DATE1 </isNotNull>
					<isNotNull prepend=", " property="opinion2">OPINION2 </isNotNull>
					<isNotNull prepend=", " property="sign2">SIGN2 </isNotNull>
					<isNotNull prepend=", " property="signName2">SIGN_NAME2 </isNotNull>
					<isNotNull prepend=", " property="signDate2">SIGN_DATE2 </isNotNull>
					<isNotNull prepend=", " property="cqzg">CQZG </isNotNull>
					<isNotNull prepend=", " property="cqzgName">CQZG_NAME </isNotNull>
					<isNotNull prepend=", " property="status">STATUS </isNotNull>
					<isNotNull prepend=", " property="extra1">EXTRA1 </isNotNull>
					<isNotNull prepend=", " property="extra2">EXTRA2 </isNotNull>
					<isNotNull prepend=", " property="extra3">EXTRA3 </isNotNull>
					<isNotNull prepend=", " property="extra4">EXTRA4 </isNotNull>
					<isNotNull prepend=", " property="extra5">EXTRA5 </isNotNull>
					<isNotNull prepend=", " property="delStatus">DEL_STATUS </isNotNull>
					<isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="createDate">CREATE_DATE </isNotNull>
					<isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="updateDate">UPDATE_DATE </isNotNull>
					<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="deleteDate">DELETE_DATE </isNotNull>
					<isNotNull prepend=", " property="recordVersion">RECORD_VERSION </isNotNull>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotNull prepend=", " property="fxjsId">#fxjsId#</isNotNull>
			<isNotNull prepend=", " property="projectCode">#projectCode#</isNotNull>
			<isNotNull prepend=", " property="projectName">#projectName#</isNotNull>
			<isNotNull prepend=", " property="fzr">#fzr#</isNotNull>
			<isNotNull prepend=", " property="fzrName">#fzrName#</isNotNull>
			<isNotNull prepend=", " property="xmzg">#xmzg#</isNotNull>
			<isNotNull prepend=", " property="xmzgName">#xmzgName#</isNotNull>
			<isNotNull prepend=", " property="ztfx">#ztfx#</isNotNull>
			<isNotNull prepend=", " property="jsr">#jsr#</isNotNull>
			<isNotNull prepend=", " property="jsrName">#jsrName#</isNotNull>
			<isNotNull prepend=", " property="sjk">#sjk#</isNotNull>
			<isNotNull prepend=", " property="sjkName">#sjkName#</isNotNull>
			<isNotNull prepend=", " property="jscl">#jscl#</isNotNull>
			<isNotNull prepend=", " property="correlation">#correlation#</isNotNull>
			<isNotNull prepend=", " property="uncorrelated">#uncorrelated#</isNotNull>
			<isNotNull prepend=", " property="opinion1">#opinion1#</isNotNull>
			<isNotNull prepend=", " property="sign1">#sign1#</isNotNull>
			<isNotNull prepend=", " property="signName1">#signName1#</isNotNull>
			<isNotNull prepend=", " property="signDate1">#signDate1#</isNotNull>
			<isNotNull prepend=", " property="opinion2">#opinion2#</isNotNull>
			<isNotNull prepend=", " property="sign2">#sign2#</isNotNull>
			<isNotNull prepend=", " property="signName2">#signName2#</isNotNull>
			<isNotNull prepend=", " property="signDate2">#signDate2#</isNotNull>
			<isNotNull prepend=", " property="cqzg">#cqzg#</isNotNull>
			<isNotNull prepend=", " property="cqzgName">#cqzgName#</isNotNull>
			<isNotNull prepend=", " property="status">#status#</isNotNull>
			<isNotNull prepend=", " property="extra1">#extra1#</isNotNull>
			<isNotNull prepend=", " property="extra2">#extra2#</isNotNull>
			<isNotNull prepend=", " property="extra3">#extra3#</isNotNull>
			<isNotNull prepend=", " property="extra4">#extra4#</isNotNull>
			<isNotNull prepend=", " property="extra5">#extra5#</isNotNull>
			<isNotNull prepend=", " property="delStatus">#delStatus#</isNotNull>
			<isNotNull prepend=", " property="createUserLabel">#createUserLabel#</isNotNull>
			<isNotNull prepend=", " property="createDate">
			   <isNotEmpty property="createDate">#createDate#</isNotEmpty>
			   <isEmpty property="createDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="updateUserLabel">#updateUserLabel#</isNotNull>
			<isNotNull prepend=", " property="updateDate">
			   <isNotEmpty property="updateDate">#updateDate#</isNotEmpty>
			   <isEmpty property="updateDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">#deleteUserLabel#</isNotNull>
			<isNotNull prepend=", " property="deleteDate">
			   <isNotEmpty property="deleteDate">#deleteDate#</isNotEmpty>
			   <isEmpty property="deleteDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="recordVersion">#recordVersion#</isNotNull>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_SEARCH_FXJS
		WHERE 		FXJS_ID=#value# 	
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_SEARCH_FXJS
		WHERE 
		<dynamic prepend=" ">
			<isNotNull prepend=" AND " property="fxjsId">FXJS_ID = #fxjsId#</isNotNull>
			<isNotNull prepend=" AND " property="projectCode">PROJECT_CODE = #projectCode#</isNotNull>
			<isNotNull prepend=" AND " property="projectName">PROJECT_NAME = #projectName#</isNotNull>
			<isNotNull prepend=" AND " property="fzr">FZR = #fzr#</isNotNull>
			<isNotNull prepend=" AND " property="fzrName">FZR_NAME = #fzrName#</isNotNull>
			<isNotNull prepend=" AND " property="xmzg">XMZG = #xmzg#</isNotNull>
			<isNotNull prepend=" AND " property="xmzgName">XMZG_NAME = #xmzgName#</isNotNull>
			<isNotNull prepend=" AND " property="ztfx">ZTFX = #ztfx#</isNotNull>
			<isNotNull prepend=" AND " property="jsr">JSR = #jsr#</isNotNull>
			<isNotNull prepend=" AND " property="jsrName">JSR_NAME = #jsrName#</isNotNull>
			<isNotNull prepend=" AND " property="sjk">SJK = #sjk#</isNotNull>
			<isNotNull prepend=" AND " property="sjkName">SJK_NAME = #sjkName#</isNotNull>
			<isNotNull prepend=" AND " property="jscl">JSCL = #jscl#</isNotNull>
			<isNotNull prepend=" AND " property="correlation">CORRELATION = #correlation#</isNotNull>
			<isNotNull prepend=" AND " property="uncorrelated">UNCORRELATED = #uncorrelated#</isNotNull>
			<isNotNull prepend=" AND " property="opinion1">OPINION1 = #opinion1#</isNotNull>
			<isNotNull prepend=" AND " property="sign1">SIGN1 = #sign1#</isNotNull>
			<isNotNull prepend=" AND " property="signName1">SIGN_NAME1 = #signName1#</isNotNull>
			<isNotNull prepend=" AND " property="signDate1">SIGN_DATE1 = #signDate1#</isNotNull>
			<isNotNull prepend=" AND " property="opinion2">OPINION2 = #opinion2#</isNotNull>
			<isNotNull prepend=" AND " property="sign2">SIGN2 = #sign2#</isNotNull>
			<isNotNull prepend=" AND " property="signName2">SIGN_NAME2 = #signName2#</isNotNull>
			<isNotNull prepend=" AND " property="signDate2">SIGN_DATE2 = #signDate2#</isNotNull>
			<isNotNull prepend=" AND " property="cqzg">CQZG = #cqzg#</isNotNull>
			<isNotNull prepend=" AND " property="cqzgName">CQZG_NAME = #cqzgName#</isNotNull>
			<isNotNull prepend=" AND " property="status">STATUS = #status#</isNotNull>
			<isNotNull prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>	
	</delete>

	<update id="update" parameterClass="tKIZLSearchFxjs">
		UPDATE  ${zzzcSchema}.T_KIZL_SEARCH_FXJS	
		SET 
		<dynamic prepend=" ">
		<isNotNull prepend="," property="fxjsId">FXJS_ID = #fxjsId#</isNotNull>
		<isNotNull prepend="," property="projectCode">PROJECT_CODE = #projectCode#</isNotNull>
		<isNotNull prepend="," property="projectName">PROJECT_NAME = #projectName#</isNotNull>
		<isNotNull prepend="," property="fzr">FZR = #fzr#</isNotNull>
		<isNotNull prepend="," property="fzrName">FZR_NAME = #fzrName#</isNotNull>
		<isNotNull prepend="," property="xmzg">XMZG = #xmzg#</isNotNull>
		<isNotNull prepend="," property="xmzgName">XMZG_NAME = #xmzgName#</isNotNull>
		<isNotNull prepend="," property="ztfx">ZTFX = #ztfx#</isNotNull>
		<isNotNull prepend="," property="jsr">JSR = #jsr#</isNotNull>
		<isNotNull prepend="," property="jsrName">JSR_NAME = #jsrName#</isNotNull>
		<isNotNull prepend="," property="sjk">SJK = #sjk#</isNotNull>
		<isNotNull prepend="," property="sjkName">SJK_NAME = #sjkName#</isNotNull>
		<isNotNull prepend="," property="jscl">JSCL = #jscl#</isNotNull>
		<isNotNull prepend="," property="correlation">CORRELATION = #correlation#</isNotNull>
		<isNotNull prepend="," property="uncorrelated">UNCORRELATED = #uncorrelated#</isNotNull>
		<isNotNull prepend="," property="opinion1">OPINION1 = #opinion1#</isNotNull>
		<isNotNull prepend="," property="sign1">SIGN1 = #sign1#</isNotNull>
		<isNotNull prepend="," property="signName1">SIGN_NAME1 = #signName1#</isNotNull>
		<isNotNull prepend="," property="signDate1">SIGN_DATE1 = #signDate1#</isNotNull>
		<isNotNull prepend="," property="opinion2">OPINION2 = #opinion2#</isNotNull>
		<isNotNull prepend="," property="sign2">SIGN2 = #sign2#</isNotNull>
		<isNotNull prepend="," property="signName2">SIGN_NAME2 = #signName2#</isNotNull>
		<isNotNull prepend="," property="signDate2">SIGN_DATE2 = #signDate2#</isNotNull>
		<isNotNull prepend="," property="cqzg">CQZG = #cqzg#</isNotNull>
		<isNotNull prepend="," property="cqzgName">CQZG_NAME = #cqzgName#</isNotNull>
		<isNotNull prepend="," property="status">STATUS = #status#</isNotNull>
		<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
		<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
		<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
		<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
		<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
		<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
		<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
		<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
		<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
		<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
		<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		FXJS_ID=#fxjsId# 			</update>
	
	<update id="updatewithnull" parameterClass="tKIZLSearchFxjs">
		UPDATE ${zzzcSchema}.T_KIZL_SEARCH_FXJS	
		SET 
		<dynamic prepend=" ">
	    <isNotNull prepend=", " property="fxjsId">FXJS_ID = #fxjsId#</isNotNull>
	    <isNull prepend=", " property="fxjsId">FXJS_ID = NULL</isNull>
	    <isNotNull prepend=", " property="projectCode">PROJECT_CODE = #projectCode#</isNotNull>
	    <isNull prepend=", " property="projectCode">PROJECT_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="projectName">PROJECT_NAME = #projectName#</isNotNull>
	    <isNull prepend=", " property="projectName">PROJECT_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="fzr">FZR = #fzr#</isNotNull>
	    <isNull prepend=", " property="fzr">FZR = NULL</isNull>
	    <isNotNull prepend=", " property="fzrName">FZR_NAME = #fzrName#</isNotNull>
	    <isNull prepend=", " property="fzrName">FZR_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="xmzg">XMZG = #xmzg#</isNotNull>
	    <isNull prepend=", " property="xmzg">XMZG = NULL</isNull>
	    <isNotNull prepend=", " property="xmzgName">XMZG_NAME = #xmzgName#</isNotNull>
	    <isNull prepend=", " property="xmzgName">XMZG_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="ztfx">ZTFX = #ztfx#</isNotNull>
	    <isNull prepend=", " property="ztfx">ZTFX = NULL</isNull>
	    <isNotNull prepend=", " property="jsr">JSR = #jsr#</isNotNull>
	    <isNull prepend=", " property="jsr">JSR = NULL</isNull>
	    <isNotNull prepend=", " property="jsrName">JSR_NAME = #jsrName#</isNotNull>
	    <isNull prepend=", " property="jsrName">JSR_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="sjk">SJK = #sjk#</isNotNull>
	    <isNull prepend=", " property="sjk">SJK = NULL</isNull>
	    <isNotNull prepend=", " property="sjkName">SJK_NAME = #sjkName#</isNotNull>
	    <isNull prepend=", " property="sjkName">SJK_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="jscl">JSCL = #jscl#</isNotNull>
	    <isNull prepend=", " property="jscl">JSCL = NULL</isNull>
	    <isNotNull prepend=", " property="correlation">CORRELATION = #correlation#</isNotNull>
	    <isNull prepend=", " property="correlation">CORRELATION = NULL</isNull>
	    <isNotNull prepend=", " property="uncorrelated">UNCORRELATED = #uncorrelated#</isNotNull>
	    <isNull prepend=", " property="uncorrelated">UNCORRELATED = NULL</isNull>
	    <isNotNull prepend=", " property="opinion1">OPINION1 = #opinion1#</isNotNull>
	    <isNull prepend=", " property="opinion1">OPINION1 = NULL</isNull>
	    <isNotNull prepend=", " property="sign1">SIGN1 = #sign1#</isNotNull>
	    <isNull prepend=", " property="sign1">SIGN1 = NULL</isNull>
	    <isNotNull prepend=", " property="signName1">SIGN_NAME1 = #signName1#</isNotNull>
	    <isNull prepend=", " property="signName1">SIGN_NAME1 = NULL</isNull>
	    <isNotNull prepend=", " property="signDate1">SIGN_DATE1 = #signDate1#</isNotNull>
	    <isNull prepend=", " property="signDate1">SIGN_DATE1 = NULL</isNull>
	    <isNotNull prepend=", " property="opinion2">OPINION2 = #opinion2#</isNotNull>
	    <isNull prepend=", " property="opinion2">OPINION2 = NULL</isNull>
	    <isNotNull prepend=", " property="sign2">SIGN2 = #sign2#</isNotNull>
	    <isNull prepend=", " property="sign2">SIGN2 = NULL</isNull>
	    <isNotNull prepend=", " property="signName2">SIGN_NAME2 = #signName2#</isNotNull>
	    <isNull prepend=", " property="signName2">SIGN_NAME2 = NULL</isNull>
	    <isNotNull prepend=", " property="signDate2">SIGN_DATE2 = #signDate2#</isNotNull>
	    <isNull prepend=", " property="signDate2">SIGN_DATE2 = NULL</isNull>
	    <isNotNull prepend=", " property="cqzg">CQZG = #cqzg#</isNotNull>
	    <isNull prepend=", " property="cqzg">CQZG = NULL</isNull>
	    <isNotNull prepend=", " property="cqzgName">CQZG_NAME = #cqzgName#</isNotNull>
	    <isNull prepend=", " property="cqzgName">CQZG_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="status">STATUS = #status#</isNotNull>
	    <isNull prepend=", " property="status">STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="extra1">EXTRA1 = #extra1#</isNotNull>
	    <isNull prepend=", " property="extra1">EXTRA1 = NULL</isNull>
	    <isNotNull prepend=", " property="extra2">EXTRA2 = #extra2#</isNotNull>
	    <isNull prepend=", " property="extra2">EXTRA2 = NULL</isNull>
	    <isNotNull prepend=", " property="extra3">EXTRA3 = #extra3#</isNotNull>
	    <isNull prepend=", " property="extra3">EXTRA3 = NULL</isNull>
	    <isNotNull prepend=", " property="extra4">EXTRA4 = #extra4#</isNotNull>
	    <isNull prepend=", " property="extra4">EXTRA4 = NULL</isNull>
	    <isNotNull prepend=", " property="extra5">EXTRA5 = #extra5#</isNotNull>
	    <isNull prepend=", " property="extra5">EXTRA5 = NULL</isNull>
	    <isNotNull prepend=", " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
	    <isNull prepend=", " property="delStatus">DEL_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
	    <isNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="createDate">CREATE_DATE = #createDate#</isNotNull>
	    <isNull prepend=", " property="createDate">CREATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
	    <isNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNull prepend=", " property="updateDate">UPDATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
	    <isNull prepend=", " property="recordVersion">RECORD_VERSION = NULL</isNull>
		</dynamic>
		WHERE 		FXJS_ID=#fxjsId# 			</update>
	
	<update id="updateByC" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_SEARCH_FXJS	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="fxjsId">FXJS_ID = #fxjsId#</isNotNull>
				<isNotNull prepend="," property="projectCode">PROJECT_CODE = #projectCode#</isNotNull>
				<isNotNull prepend="," property="projectName">PROJECT_NAME = #projectName#</isNotNull>
				<isNotNull prepend="," property="fzr">FZR = #fzr#</isNotNull>
				<isNotNull prepend="," property="fzrName">FZR_NAME = #fzrName#</isNotNull>
				<isNotNull prepend="," property="xmzg">XMZG = #xmzg#</isNotNull>
				<isNotNull prepend="," property="xmzgName">XMZG_NAME = #xmzgName#</isNotNull>
				<isNotNull prepend="," property="ztfx">ZTFX = #ztfx#</isNotNull>
				<isNotNull prepend="," property="jsr">JSR = #jsr#</isNotNull>
				<isNotNull prepend="," property="jsrName">JSR_NAME = #jsrName#</isNotNull>
				<isNotNull prepend="," property="sjk">SJK = #sjk#</isNotNull>
				<isNotNull prepend="," property="sjkName">SJK_NAME = #sjkName#</isNotNull>
				<isNotNull prepend="," property="jscl">JSCL = #jscl#</isNotNull>
				<isNotNull prepend="," property="correlation">CORRELATION = #correlation#</isNotNull>
				<isNotNull prepend="," property="uncorrelated">UNCORRELATED = #uncorrelated#</isNotNull>
				<isNotNull prepend="," property="opinion1">OPINION1 = #opinion1#</isNotNull>
				<isNotNull prepend="," property="sign1">SIGN1 = #sign1#</isNotNull>
				<isNotNull prepend="," property="signName1">SIGN_NAME1 = #signName1#</isNotNull>
				<isNotNull prepend="," property="signDate1">SIGN_DATE1 = #signDate1#</isNotNull>
				<isNotNull prepend="," property="opinion2">OPINION2 = #opinion2#</isNotNull>
				<isNotNull prepend="," property="sign2">SIGN2 = #sign2#</isNotNull>
				<isNotNull prepend="," property="signName2">SIGN_NAME2 = #signName2#</isNotNull>
				<isNotNull prepend="," property="signDate2">SIGN_DATE2 = #signDate2#</isNotNull>
				<isNotNull prepend="," property="cqzg">CQZG = #cqzg#</isNotNull>
				<isNotNull prepend="," property="cqzgName">CQZG_NAME = #cqzgName#</isNotNull>
				<isNotNull prepend="," property="status">STATUS = #status#</isNotNull>
				<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
				<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
				<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
				<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
				<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
				<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
				<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
				<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
				<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
				<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
				<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
			</dynamic>
		<dynamic prepend=" WHERE ">
		 			<isNotNull prepend=" AND " property="fxjsIdOld">FXJS_ID = #fxjsIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="projectCodeOld">PROJECT_CODE = #projectCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="projectNameOld">PROJECT_NAME = #projectNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="fzrOld">FZR = #fzrOld#</isNotNull>
			<isNotNull prepend=" AND " property="fzrNameOld">FZR_NAME = #fzrNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="xmzgOld">XMZG = #xmzgOld#</isNotNull>
			<isNotNull prepend=" AND " property="xmzgNameOld">XMZG_NAME = #xmzgNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="ztfxOld">ZTFX = #ztfxOld#</isNotNull>
			<isNotNull prepend=" AND " property="jsrOld">JSR = #jsrOld#</isNotNull>
			<isNotNull prepend=" AND " property="jsrNameOld">JSR_NAME = #jsrNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="sjkOld">SJK = #sjkOld#</isNotNull>
			<isNotNull prepend=" AND " property="sjkNameOld">SJK_NAME = #sjkNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="jsclOld">JSCL = #jsclOld#</isNotNull>
			<isNotNull prepend=" AND " property="correlationOld">CORRELATION = #correlationOld#</isNotNull>
			<isNotNull prepend=" AND " property="uncorrelatedOld">UNCORRELATED = #uncorrelatedOld#</isNotNull>
			<isNotNull prepend=" AND " property="opinion1Old">OPINION1 = #opinion1Old#</isNotNull>
			<isNotNull prepend=" AND " property="sign1Old">SIGN1 = #sign1Old#</isNotNull>
			<isNotNull prepend=" AND " property="signName1Old">SIGN_NAME1 = #signName1Old#</isNotNull>
			<isNotNull prepend=" AND " property="signDate1Old">SIGN_DATE1 = #signDate1Old#</isNotNull>
			<isNotNull prepend=" AND " property="opinion2Old">OPINION2 = #opinion2Old#</isNotNull>
			<isNotNull prepend=" AND " property="sign2Old">SIGN2 = #sign2Old#</isNotNull>
			<isNotNull prepend=" AND " property="signName2Old">SIGN_NAME2 = #signName2Old#</isNotNull>
			<isNotNull prepend=" AND " property="signDate2Old">SIGN_DATE2 = #signDate2Old#</isNotNull>
			<isNotNull prepend=" AND " property="cqzgOld">CQZG = #cqzgOld#</isNotNull>
			<isNotNull prepend=" AND " property="cqzgNameOld">CQZG_NAME = #cqzgNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="statusOld">STATUS = #statusOld#</isNotNull>
			<isNotNull prepend=" AND " property="extra1Old">EXTRA1 = #extra1Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra2Old">EXTRA2 = #extra2Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra3Old">EXTRA3 = #extra3Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra4Old">EXTRA4 = #extra4Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra5Old">EXTRA5 = #extra5Old#</isNotNull>
			<isNotNull prepend=" AND " property="delStatusOld">DEL_STATUS = #delStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabelOld">CREATE_USER_LABEL = #createUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="createDateOld">CREATE_DATE = #createDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabelOld">UPDATE_USER_LABEL = #updateUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateDateOld">UPDATE_DATE = #updateDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabelOld">DELETE_USER_LABEL = #deleteUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDateOld">DELETE_DATE = #deleteDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersionOld">RECORD_VERSION = #recordVersionOld#</isNotNull>
			<isNotNull prepend=" AND " property="dynSqlOld"> $dynSqlOld$ </isNotNull>
		</dynamic>
	</update>
	
	<update id="updateNull" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_SEARCH_FXJS	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="fxjsId">FXJS_ID = #fxjsId#</isNotNull>
			<isNotNull prepend="," property="projectCode">PROJECT_CODE = #projectCode#</isNotNull>
			<isNotNull prepend="," property="projectName">PROJECT_NAME = #projectName#</isNotNull>
			<isNotNull prepend="," property="fzr">FZR = #fzr#</isNotNull>
			<isNotNull prepend="," property="fzrName">FZR_NAME = #fzrName#</isNotNull>
			<isNotNull prepend="," property="xmzg">XMZG = #xmzg#</isNotNull>
			<isNotNull prepend="," property="xmzgName">XMZG_NAME = #xmzgName#</isNotNull>
			<isNotNull prepend="," property="ztfx">ZTFX = #ztfx#</isNotNull>
			<isNotNull prepend="," property="jsr">JSR = #jsr#</isNotNull>
			<isNotNull prepend="," property="jsrName">JSR_NAME = #jsrName#</isNotNull>
			<isNotNull prepend="," property="sjk">SJK = #sjk#</isNotNull>
			<isNotNull prepend="," property="sjkName">SJK_NAME = #sjkName#</isNotNull>
			<isNotNull prepend="," property="jscl">JSCL = #jscl#</isNotNull>
			<isNotNull prepend="," property="correlation">CORRELATION = #correlation#</isNotNull>
			<isNotNull prepend="," property="uncorrelated">UNCORRELATED = #uncorrelated#</isNotNull>
			<isNotNull prepend="," property="opinion1">OPINION1 = #opinion1#</isNotNull>
			<isNotNull prepend="," property="sign1">SIGN1 = #sign1#</isNotNull>
			<isNotNull prepend="," property="signName1">SIGN_NAME1 = #signName1#</isNotNull>
			<isNotNull prepend="," property="signDate1">SIGN_DATE1 = #signDate1#</isNotNull>
			<isNotNull prepend="," property="opinion2">OPINION2 = #opinion2#</isNotNull>
			<isNotNull prepend="," property="sign2">SIGN2 = #sign2#</isNotNull>
			<isNotNull prepend="," property="signName2">SIGN_NAME2 = #signName2#</isNotNull>
			<isNotNull prepend="," property="signDate2">SIGN_DATE2 = #signDate2#</isNotNull>
			<isNotNull prepend="," property="cqzg">CQZG = #cqzg#</isNotNull>
			<isNotNull prepend="," property="cqzgName">CQZG_NAME = #cqzgName#</isNotNull>
			<isNotNull prepend="," property="status">STATUS = #status#</isNotNull>
			<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			<isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
			<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		FXJS_ID=#fxjsId# 			</update>	
</sqlMap>