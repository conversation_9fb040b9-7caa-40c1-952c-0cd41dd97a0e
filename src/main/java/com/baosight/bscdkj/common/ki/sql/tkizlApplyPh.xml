<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlApplyPh">
	<typeAlias alias="tKIZLApplyPh" type="com.baosight.bscdkj.common.ki.domain.TkizlApplyPh"/>
	<select id="load" parameterClass="string" resultClass="tKIZLApplyPh">
		SELECT 
				PH_ID as "phId" ,		
				DATA_FROM as "dataFrom" ,		
				DATAFROM_ID as "datafromId" ,		
				PSR_PZ as "psrPz" ,		
				PSR_BIG as "psrBig" ,		
				SHOPSIGN as "shopsign" ,		
				PH_NAME as "phName" ,		
				PSRCODE_BZ as "psrcodeBz" ,		
				PSRNAME_BZ as "psrnameBz" ,		
				PSRCODE_GZ as "psrcodeGz" ,		
				PSRNAME_GZ as "psrnameGz" ,		
				PSRCODE_CPFLM as "psrcodeCpflm" ,		
				PSRNAME_CPFLM as "psrnameCpflm" ,		
				PSRCODE_DCZL as "psrcodeDczl" ,		
				PSRNAME_DCZL as "psrnameDczl" ,		
				PSRCODE_HCLFS as "psrcodeHclfs" ,		
				PSRNAME_HCLFS as "psrnameHclfs" ,		
				PSRCODE_DHLHZL as "psrcodeDhlhzl" ,		
				PSRNAME_DHLHZL as "psrnameDhlhzl" ,		
				PSRCODE_RCLZL as "psrcodeRclzl" ,		
				PSRNAME_RCLZL as "psrnameRclzl" ,		
				PSRCODE_TLSBM as "psrcodeTlsbm" ,		
				PSRNAME_TLSBM as "psrnameTlsbm" ,		
				PSRCODE_TLXBM as "psrcodeTlxbm" ,		
				PSRNAME_TLXBM as "psrnameTlxbm" ,		
				PSRCODE_WJ as "psrcodeWj" ,		
				PSRNAME_WJ as "psrnameWj" ,		
				PSRCODE_GD as "psrcodeGd" ,		
				PSRNAME_GD as "psrnameGd" ,		
				PSRCODE_BH as "psrcodeBh" ,		
				PSRNAME_BH as "psrnameBh" ,		
				PSRCODE_GJJT as "psrcodeGjjt" ,		
				PSRNAME_GJJT as "psrnameGjjt" ,		
				PSRCODE_GDLWLX as "psrcodeGdlwlx" ,		
				PSRNAME_GDLWLX as "psrnameGdlwlx" ,		
				PSRCODE_RCLFS as "psrcodeRclfs" ,		
				PSRNAME_RCLFS as "psrnameRclfs" ,		
				PSRCODE_LWLX as "psrcodeLwlx" ,		
				PSRNAME_LWLX as "psrnameLwlx" ,		
				PSRCODE_NLWLX as "psrcodeNlwlx" ,		
				PSRNAME_NLWLX as "psrnameNlwlx" ,		
				PSR as "psr" ,		
				EXTRA1 as "extra1" ,		
				EXTRA2 as "extra2" ,		
				EXTRA3 as "extra3" ,		
				EXTRA4 as "extra4" ,		
				EXTRA5 as "extra5" ,		
				DEL_STATUS as "delStatus" ,		
				CREATE_USER_LABEL as "createUserLabel" ,		
				CREATE_DATE as "createDate" ,		
				UPDATE_USER_LABEL as "updateUserLabel" ,		
				UPDATE_DATE as "updateDate" ,		
				DELETE_USER_LABEL as "deleteUserLabel" ,		
				DELETE_DATE as "deleteDate" ,		
				RECORD_VERSION as "recordVersion" 		
				FROM ${zzzcSchema}.T_KIZL_APPLY_PH
		WHERE   PH_ID=#value# 				
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tKIZLApplyPh">
		SELECT
				PH_ID  as "phId" ,		
				DATA_FROM  as "dataFrom" ,		
				DATAFROM_ID  as "datafromId" ,		
				PSR_PZ  as "psrPz" ,		
				PSR_BIG  as "psrBig" ,		
				SHOPSIGN  as "shopsign" ,		
				PH_NAME  as "phName" ,		
				PSRCODE_BZ  as "psrcodeBz" ,		
				PSRNAME_BZ  as "psrnameBz" ,		
				PSRCODE_GZ  as "psrcodeGz" ,		
				PSRNAME_GZ  as "psrnameGz" ,		
				PSRCODE_CPFLM  as "psrcodeCpflm" ,		
				PSRNAME_CPFLM  as "psrnameCpflm" ,		
				PSRCODE_DCZL  as "psrcodeDczl" ,		
				PSRNAME_DCZL  as "psrnameDczl" ,		
				PSRCODE_HCLFS  as "psrcodeHclfs" ,		
				PSRNAME_HCLFS  as "psrnameHclfs" ,		
				PSRCODE_DHLHZL  as "psrcodeDhlhzl" ,		
				PSRNAME_DHLHZL  as "psrnameDhlhzl" ,		
				PSRCODE_RCLZL  as "psrcodeRclzl" ,		
				PSRNAME_RCLZL  as "psrnameRclzl" ,		
				PSRCODE_TLSBM  as "psrcodeTlsbm" ,		
				PSRNAME_TLSBM  as "psrnameTlsbm" ,		
				PSRCODE_TLXBM  as "psrcodeTlxbm" ,		
				PSRNAME_TLXBM  as "psrnameTlxbm" ,		
				PSRCODE_WJ  as "psrcodeWj" ,		
				PSRNAME_WJ  as "psrnameWj" ,		
				PSRCODE_GD  as "psrcodeGd" ,		
				PSRNAME_GD  as "psrnameGd" ,		
				PSRCODE_BH  as "psrcodeBh" ,		
				PSRNAME_BH  as "psrnameBh" ,		
				PSRCODE_GJJT  as "psrcodeGjjt" ,		
				PSRNAME_GJJT  as "psrnameGjjt" ,		
				PSRCODE_GDLWLX  as "psrcodeGdlwlx" ,		
				PSRNAME_GDLWLX  as "psrnameGdlwlx" ,		
				PSRCODE_RCLFS  as "psrcodeRclfs" ,		
				PSRNAME_RCLFS  as "psrnameRclfs" ,		
				PSRCODE_LWLX  as "psrcodeLwlx" ,		
				PSRNAME_LWLX  as "psrnameLwlx" ,		
				PSRCODE_NLWLX  as "psrcodeNlwlx" ,		
				PSRNAME_NLWLX  as "psrnameNlwlx" ,		
				PSR  as "psr" ,		
				EXTRA1  as "extra1" ,		
				EXTRA2  as "extra2" ,		
				EXTRA3  as "extra3" ,		
				EXTRA4  as "extra4" ,		
				EXTRA5  as "extra5" ,		
				DEL_STATUS  as "delStatus" ,		
				CREATE_USER_LABEL  as "createUserLabel" ,		
				CREATE_DATE  as "createDate" ,		
				UPDATE_USER_LABEL  as "updateUserLabel" ,		
				UPDATE_DATE  as "updateDate" ,		
				DELETE_USER_LABEL  as "deleteUserLabel" ,		
				DELETE_DATE  as "deleteDate" ,		
				RECORD_VERSION  as "recordVersion" 		
				FROM ${zzzcSchema}.T_KIZL_APPLY_PH
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="phId">PH_ID =  #phId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dataFrom">DATA_FROM =  #dataFrom#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="datafromId">DATAFROM_ID =  #datafromId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrPz">PSR_PZ =  #psrPz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrBig">PSR_BIG =  #psrBig#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="shopsign">SHOPSIGN =  #shopsign#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="phName">PH_NAME =  #phName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeBz">PSRCODE_BZ =  #psrcodeBz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameBz">PSRNAME_BZ =  #psrnameBz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeGz">PSRCODE_GZ =  #psrcodeGz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameGz">PSRNAME_GZ =  #psrnameGz#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeCpflm">PSRCODE_CPFLM =  #psrcodeCpflm#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameCpflm">PSRNAME_CPFLM =  #psrnameCpflm#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeDczl">PSRCODE_DCZL =  #psrcodeDczl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameDczl">PSRNAME_DCZL =  #psrnameDczl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeHclfs">PSRCODE_HCLFS =  #psrcodeHclfs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameHclfs">PSRNAME_HCLFS =  #psrnameHclfs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeDhlhzl">PSRCODE_DHLHZL =  #psrcodeDhlhzl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameDhlhzl">PSRNAME_DHLHZL =  #psrnameDhlhzl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeRclzl">PSRCODE_RCLZL =  #psrcodeRclzl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameRclzl">PSRNAME_RCLZL =  #psrnameRclzl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeTlsbm">PSRCODE_TLSBM =  #psrcodeTlsbm#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameTlsbm">PSRNAME_TLSBM =  #psrnameTlsbm#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeTlxbm">PSRCODE_TLXBM =  #psrcodeTlxbm#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameTlxbm">PSRNAME_TLXBM =  #psrnameTlxbm#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeWj">PSRCODE_WJ =  #psrcodeWj#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameWj">PSRNAME_WJ =  #psrnameWj#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeGd">PSRCODE_GD =  #psrcodeGd#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameGd">PSRNAME_GD =  #psrnameGd#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeBh">PSRCODE_BH =  #psrcodeBh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameBh">PSRNAME_BH =  #psrnameBh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeGjjt">PSRCODE_GJJT =  #psrcodeGjjt#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameGjjt">PSRNAME_GJJT =  #psrnameGjjt#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeGdlwlx">PSRCODE_GDLWLX =  #psrcodeGdlwlx#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameGdlwlx">PSRNAME_GDLWLX =  #psrnameGdlwlx#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeRclfs">PSRCODE_RCLFS =  #psrcodeRclfs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameRclfs">PSRNAME_RCLFS =  #psrnameRclfs#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeLwlx">PSRCODE_LWLX =  #psrcodeLwlx#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameLwlx">PSRNAME_LWLX =  #psrnameLwlx#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrcodeNlwlx">PSRCODE_NLWLX =  #psrcodeNlwlx#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psrnameNlwlx">PSRNAME_NLWLX =  #psrnameNlwlx#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="psr">PSR =  #psr#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KIZL_APPLY_PH 
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="phId">PH_ID =  #phId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dataFrom">DATA_FROM =  #dataFrom#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="datafromId">DATAFROM_ID =  #datafromId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrPz">PSR_PZ =  #psrPz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrBig">PSR_BIG =  #psrBig#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="shopsign">SHOPSIGN =  #shopsign#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="phName">PH_NAME =  #phName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeBz">PSRCODE_BZ =  #psrcodeBz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameBz">PSRNAME_BZ =  #psrnameBz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeGz">PSRCODE_GZ =  #psrcodeGz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameGz">PSRNAME_GZ =  #psrnameGz#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeCpflm">PSRCODE_CPFLM =  #psrcodeCpflm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameCpflm">PSRNAME_CPFLM =  #psrnameCpflm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeDczl">PSRCODE_DCZL =  #psrcodeDczl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameDczl">PSRNAME_DCZL =  #psrnameDczl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeHclfs">PSRCODE_HCLFS =  #psrcodeHclfs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameHclfs">PSRNAME_HCLFS =  #psrnameHclfs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeDhlhzl">PSRCODE_DHLHZL =  #psrcodeDhlhzl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameDhlhzl">PSRNAME_DHLHZL =  #psrnameDhlhzl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeRclzl">PSRCODE_RCLZL =  #psrcodeRclzl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameRclzl">PSRNAME_RCLZL =  #psrnameRclzl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeTlsbm">PSRCODE_TLSBM =  #psrcodeTlsbm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameTlsbm">PSRNAME_TLSBM =  #psrnameTlsbm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeTlxbm">PSRCODE_TLXBM =  #psrcodeTlxbm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameTlxbm">PSRNAME_TLXBM =  #psrnameTlxbm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeWj">PSRCODE_WJ =  #psrcodeWj#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameWj">PSRNAME_WJ =  #psrnameWj#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeGd">PSRCODE_GD =  #psrcodeGd#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameGd">PSRNAME_GD =  #psrnameGd#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeBh">PSRCODE_BH =  #psrcodeBh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameBh">PSRNAME_BH =  #psrnameBh#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeGjjt">PSRCODE_GJJT =  #psrcodeGjjt#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameGjjt">PSRNAME_GJJT =  #psrnameGjjt#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeGdlwlx">PSRCODE_GDLWLX =  #psrcodeGdlwlx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameGdlwlx">PSRNAME_GDLWLX =  #psrnameGdlwlx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeRclfs">PSRCODE_RCLFS =  #psrcodeRclfs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameRclfs">PSRNAME_RCLFS =  #psrnameRclfs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeLwlx">PSRCODE_LWLX =  #psrcodeLwlx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameLwlx">PSRNAME_LWLX =  #psrnameLwlx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrcodeNlwlx">PSRCODE_NLWLX =  #psrcodeNlwlx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psrnameNlwlx">PSRNAME_NLWLX =  #psrnameNlwlx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="psr">PSR =  #psr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tKIZLApplyPh">
		INSERT INTO ${zzzcSchema}.T_KIZL_APPLY_PH ( 
		<dynamic prepend=" ">
					<isNotNull prepend=", " property="phId">PH_ID </isNotNull>
					<isNotNull prepend=", " property="dataFrom">DATA_FROM </isNotNull>
					<isNotNull prepend=", " property="datafromId">DATAFROM_ID </isNotNull>
					<isNotNull prepend=", " property="psrPz">PSR_PZ </isNotNull>
					<isNotNull prepend=", " property="psrBig">PSR_BIG </isNotNull>
					<isNotNull prepend=", " property="shopsign">SHOPSIGN </isNotNull>
					<isNotNull prepend=", " property="phName">PH_NAME </isNotNull>
					<isNotNull prepend=", " property="psrcodeBz">PSRCODE_BZ </isNotNull>
					<isNotNull prepend=", " property="psrnameBz">PSRNAME_BZ </isNotNull>
					<isNotNull prepend=", " property="psrcodeGz">PSRCODE_GZ </isNotNull>
					<isNotNull prepend=", " property="psrnameGz">PSRNAME_GZ </isNotNull>
					<isNotNull prepend=", " property="psrcodeCpflm">PSRCODE_CPFLM </isNotNull>
					<isNotNull prepend=", " property="psrnameCpflm">PSRNAME_CPFLM </isNotNull>
					<isNotNull prepend=", " property="psrcodeDczl">PSRCODE_DCZL </isNotNull>
					<isNotNull prepend=", " property="psrnameDczl">PSRNAME_DCZL </isNotNull>
					<isNotNull prepend=", " property="psrcodeHclfs">PSRCODE_HCLFS </isNotNull>
					<isNotNull prepend=", " property="psrnameHclfs">PSRNAME_HCLFS </isNotNull>
					<isNotNull prepend=", " property="psrcodeDhlhzl">PSRCODE_DHLHZL </isNotNull>
					<isNotNull prepend=", " property="psrnameDhlhzl">PSRNAME_DHLHZL </isNotNull>
					<isNotNull prepend=", " property="psrcodeRclzl">PSRCODE_RCLZL </isNotNull>
					<isNotNull prepend=", " property="psrnameRclzl">PSRNAME_RCLZL </isNotNull>
					<isNotNull prepend=", " property="psrcodeTlsbm">PSRCODE_TLSBM </isNotNull>
					<isNotNull prepend=", " property="psrnameTlsbm">PSRNAME_TLSBM </isNotNull>
					<isNotNull prepend=", " property="psrcodeTlxbm">PSRCODE_TLXBM </isNotNull>
					<isNotNull prepend=", " property="psrnameTlxbm">PSRNAME_TLXBM </isNotNull>
					<isNotNull prepend=", " property="psrcodeWj">PSRCODE_WJ </isNotNull>
					<isNotNull prepend=", " property="psrnameWj">PSRNAME_WJ </isNotNull>
					<isNotNull prepend=", " property="psrcodeGd">PSRCODE_GD </isNotNull>
					<isNotNull prepend=", " property="psrnameGd">PSRNAME_GD </isNotNull>
					<isNotNull prepend=", " property="psrcodeBh">PSRCODE_BH </isNotNull>
					<isNotNull prepend=", " property="psrnameBh">PSRNAME_BH </isNotNull>
					<isNotNull prepend=", " property="psrcodeGjjt">PSRCODE_GJJT </isNotNull>
					<isNotNull prepend=", " property="psrnameGjjt">PSRNAME_GJJT </isNotNull>
					<isNotNull prepend=", " property="psrcodeGdlwlx">PSRCODE_GDLWLX </isNotNull>
					<isNotNull prepend=", " property="psrnameGdlwlx">PSRNAME_GDLWLX </isNotNull>
					<isNotNull prepend=", " property="psrcodeRclfs">PSRCODE_RCLFS </isNotNull>
					<isNotNull prepend=", " property="psrnameRclfs">PSRNAME_RCLFS </isNotNull>
					<isNotNull prepend=", " property="psrcodeLwlx">PSRCODE_LWLX </isNotNull>
					<isNotNull prepend=", " property="psrnameLwlx">PSRNAME_LWLX </isNotNull>
					<isNotNull prepend=", " property="psrcodeNlwlx">PSRCODE_NLWLX </isNotNull>
					<isNotNull prepend=", " property="psrnameNlwlx">PSRNAME_NLWLX </isNotNull>
					<isNotNull prepend=", " property="psr">PSR </isNotNull>
					<isNotNull prepend=", " property="extra1">EXTRA1 </isNotNull>
					<isNotNull prepend=", " property="extra2">EXTRA2 </isNotNull>
					<isNotNull prepend=", " property="extra3">EXTRA3 </isNotNull>
					<isNotNull prepend=", " property="extra4">EXTRA4 </isNotNull>
					<isNotNull prepend=", " property="extra5">EXTRA5 </isNotNull>
					<isNotNull prepend=", " property="delStatus">DEL_STATUS </isNotNull>
					<isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="createDate">CREATE_DATE </isNotNull>
					<isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="updateDate">UPDATE_DATE </isNotNull>
					<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="deleteDate">DELETE_DATE </isNotNull>
					<isNotNull prepend=", " property="recordVersion">RECORD_VERSION </isNotNull>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotNull prepend=", " property="phId">#phId#</isNotNull>
			<isNotNull prepend=", " property="dataFrom">#dataFrom#</isNotNull>
			<isNotNull prepend=", " property="datafromId">#datafromId#</isNotNull>
			<isNotNull prepend=", " property="psrPz">#psrPz#</isNotNull>
			<isNotNull prepend=", " property="psrBig">#psrBig#</isNotNull>
			<isNotNull prepend=", " property="shopsign">#shopsign#</isNotNull>
			<isNotNull prepend=", " property="phName">#phName#</isNotNull>
			<isNotNull prepend=", " property="psrcodeBz">#psrcodeBz#</isNotNull>
			<isNotNull prepend=", " property="psrnameBz">#psrnameBz#</isNotNull>
			<isNotNull prepend=", " property="psrcodeGz">#psrcodeGz#</isNotNull>
			<isNotNull prepend=", " property="psrnameGz">#psrnameGz#</isNotNull>
			<isNotNull prepend=", " property="psrcodeCpflm">#psrcodeCpflm#</isNotNull>
			<isNotNull prepend=", " property="psrnameCpflm">#psrnameCpflm#</isNotNull>
			<isNotNull prepend=", " property="psrcodeDczl">#psrcodeDczl#</isNotNull>
			<isNotNull prepend=", " property="psrnameDczl">#psrnameDczl#</isNotNull>
			<isNotNull prepend=", " property="psrcodeHclfs">#psrcodeHclfs#</isNotNull>
			<isNotNull prepend=", " property="psrnameHclfs">#psrnameHclfs#</isNotNull>
			<isNotNull prepend=", " property="psrcodeDhlhzl">#psrcodeDhlhzl#</isNotNull>
			<isNotNull prepend=", " property="psrnameDhlhzl">#psrnameDhlhzl#</isNotNull>
			<isNotNull prepend=", " property="psrcodeRclzl">#psrcodeRclzl#</isNotNull>
			<isNotNull prepend=", " property="psrnameRclzl">#psrnameRclzl#</isNotNull>
			<isNotNull prepend=", " property="psrcodeTlsbm">#psrcodeTlsbm#</isNotNull>
			<isNotNull prepend=", " property="psrnameTlsbm">#psrnameTlsbm#</isNotNull>
			<isNotNull prepend=", " property="psrcodeTlxbm">#psrcodeTlxbm#</isNotNull>
			<isNotNull prepend=", " property="psrnameTlxbm">#psrnameTlxbm#</isNotNull>
			<isNotNull prepend=", " property="psrcodeWj">#psrcodeWj#</isNotNull>
			<isNotNull prepend=", " property="psrnameWj">#psrnameWj#</isNotNull>
			<isNotNull prepend=", " property="psrcodeGd">#psrcodeGd#</isNotNull>
			<isNotNull prepend=", " property="psrnameGd">#psrnameGd#</isNotNull>
			<isNotNull prepend=", " property="psrcodeBh">#psrcodeBh#</isNotNull>
			<isNotNull prepend=", " property="psrnameBh">#psrnameBh#</isNotNull>
			<isNotNull prepend=", " property="psrcodeGjjt">#psrcodeGjjt#</isNotNull>
			<isNotNull prepend=", " property="psrnameGjjt">#psrnameGjjt#</isNotNull>
			<isNotNull prepend=", " property="psrcodeGdlwlx">#psrcodeGdlwlx#</isNotNull>
			<isNotNull prepend=", " property="psrnameGdlwlx">#psrnameGdlwlx#</isNotNull>
			<isNotNull prepend=", " property="psrcodeRclfs">#psrcodeRclfs#</isNotNull>
			<isNotNull prepend=", " property="psrnameRclfs">#psrnameRclfs#</isNotNull>
			<isNotNull prepend=", " property="psrcodeLwlx">#psrcodeLwlx#</isNotNull>
			<isNotNull prepend=", " property="psrnameLwlx">#psrnameLwlx#</isNotNull>
			<isNotNull prepend=", " property="psrcodeNlwlx">#psrcodeNlwlx#</isNotNull>
			<isNotNull prepend=", " property="psrnameNlwlx">#psrnameNlwlx#</isNotNull>
			<isNotNull prepend=", " property="psr">#psr#</isNotNull>
			<isNotNull prepend=", " property="extra1">#extra1#</isNotNull>
			<isNotNull prepend=", " property="extra2">#extra2#</isNotNull>
			<isNotNull prepend=", " property="extra3">#extra3#</isNotNull>
			<isNotNull prepend=", " property="extra4">#extra4#</isNotNull>
			<isNotNull prepend=", " property="extra5">#extra5#</isNotNull>
			<isNotNull prepend=", " property="delStatus">#delStatus#</isNotNull>
			<isNotNull prepend=", " property="createUserLabel">#createUserLabel#</isNotNull>
			<isNotNull prepend=", " property="createDate">
			   <isNotEmpty property="createDate">#createDate#</isNotEmpty>
			   <isEmpty property="createDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="updateUserLabel">#updateUserLabel#</isNotNull>
			<isNotNull prepend=", " property="updateDate">
			   <isNotEmpty property="updateDate">#updateDate#</isNotEmpty>
			   <isEmpty property="updateDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">#deleteUserLabel#</isNotNull>
			<isNotNull prepend=", " property="deleteDate">
			   <isNotEmpty property="deleteDate">#deleteDate#</isNotEmpty>
			   <isEmpty property="deleteDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="recordVersion">#recordVersion#</isNotNull>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_PH
		WHERE 		PH_ID=#value# 	
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_APPLY_PH
		WHERE 
		<dynamic prepend=" ">
			<isNotNull prepend=" AND " property="phId">PH_ID = #phId#</isNotNull>
			<isNotNull prepend=" AND " property="dataFrom">DATA_FROM = #dataFrom#</isNotNull>
			<isNotNull prepend=" AND " property="datafromId">DATAFROM_ID = #datafromId#</isNotNull>
			<isNotNull prepend=" AND " property="psrPz">PSR_PZ = #psrPz#</isNotNull>
			<isNotNull prepend=" AND " property="psrBig">PSR_BIG = #psrBig#</isNotNull>
			<isNotNull prepend=" AND " property="shopsign">SHOPSIGN = #shopsign#</isNotNull>
			<isNotNull prepend=" AND " property="phName">PH_NAME = #phName#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeBz">PSRCODE_BZ = #psrcodeBz#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameBz">PSRNAME_BZ = #psrnameBz#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeGz">PSRCODE_GZ = #psrcodeGz#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameGz">PSRNAME_GZ = #psrnameGz#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeCpflm">PSRCODE_CPFLM = #psrcodeCpflm#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameCpflm">PSRNAME_CPFLM = #psrnameCpflm#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeDczl">PSRCODE_DCZL = #psrcodeDczl#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameDczl">PSRNAME_DCZL = #psrnameDczl#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeHclfs">PSRCODE_HCLFS = #psrcodeHclfs#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameHclfs">PSRNAME_HCLFS = #psrnameHclfs#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeDhlhzl">PSRCODE_DHLHZL = #psrcodeDhlhzl#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameDhlhzl">PSRNAME_DHLHZL = #psrnameDhlhzl#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeRclzl">PSRCODE_RCLZL = #psrcodeRclzl#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameRclzl">PSRNAME_RCLZL = #psrnameRclzl#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeTlsbm">PSRCODE_TLSBM = #psrcodeTlsbm#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameTlsbm">PSRNAME_TLSBM = #psrnameTlsbm#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeTlxbm">PSRCODE_TLXBM = #psrcodeTlxbm#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameTlxbm">PSRNAME_TLXBM = #psrnameTlxbm#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeWj">PSRCODE_WJ = #psrcodeWj#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameWj">PSRNAME_WJ = #psrnameWj#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeGd">PSRCODE_GD = #psrcodeGd#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameGd">PSRNAME_GD = #psrnameGd#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeBh">PSRCODE_BH = #psrcodeBh#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameBh">PSRNAME_BH = #psrnameBh#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeGjjt">PSRCODE_GJJT = #psrcodeGjjt#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameGjjt">PSRNAME_GJJT = #psrnameGjjt#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeGdlwlx">PSRCODE_GDLWLX = #psrcodeGdlwlx#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameGdlwlx">PSRNAME_GDLWLX = #psrnameGdlwlx#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeRclfs">PSRCODE_RCLFS = #psrcodeRclfs#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameRclfs">PSRNAME_RCLFS = #psrnameRclfs#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeLwlx">PSRCODE_LWLX = #psrcodeLwlx#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameLwlx">PSRNAME_LWLX = #psrnameLwlx#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeNlwlx">PSRCODE_NLWLX = #psrcodeNlwlx#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameNlwlx">PSRNAME_NLWLX = #psrnameNlwlx#</isNotNull>
			<isNotNull prepend=" AND " property="psr">PSR = #psr#</isNotNull>
			<isNotNull prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>	
	</delete>

	<update id="update" parameterClass="tKIZLApplyPh">
		UPDATE  ${zzzcSchema}.T_KIZL_APPLY_PH	
		SET 
		<dynamic prepend=" ">
		<isNotNull prepend="," property="phId">PH_ID = #phId#</isNotNull>
		<isNotNull prepend="," property="dataFrom">DATA_FROM = #dataFrom#</isNotNull>
		<isNotNull prepend="," property="datafromId">DATAFROM_ID = #datafromId#</isNotNull>
		<isNotNull prepend="," property="psrPz">PSR_PZ = #psrPz#</isNotNull>
		<isNotNull prepend="," property="psrBig">PSR_BIG = #psrBig#</isNotNull>
		<isNotNull prepend="," property="shopsign">SHOPSIGN = #shopsign#</isNotNull>
		<isNotNull prepend="," property="phName">PH_NAME = #phName#</isNotNull>
		<isNotNull prepend="," property="psrcodeBz">PSRCODE_BZ = #psrcodeBz#</isNotNull>
		<isNotNull prepend="," property="psrnameBz">PSRNAME_BZ = #psrnameBz#</isNotNull>
		<isNotNull prepend="," property="psrcodeGz">PSRCODE_GZ = #psrcodeGz#</isNotNull>
		<isNotNull prepend="," property="psrnameGz">PSRNAME_GZ = #psrnameGz#</isNotNull>
		<isNotNull prepend="," property="psrcodeCpflm">PSRCODE_CPFLM = #psrcodeCpflm#</isNotNull>
		<isNotNull prepend="," property="psrnameCpflm">PSRNAME_CPFLM = #psrnameCpflm#</isNotNull>
		<isNotNull prepend="," property="psrcodeDczl">PSRCODE_DCZL = #psrcodeDczl#</isNotNull>
		<isNotNull prepend="," property="psrnameDczl">PSRNAME_DCZL = #psrnameDczl#</isNotNull>
		<isNotNull prepend="," property="psrcodeHclfs">PSRCODE_HCLFS = #psrcodeHclfs#</isNotNull>
		<isNotNull prepend="," property="psrnameHclfs">PSRNAME_HCLFS = #psrnameHclfs#</isNotNull>
		<isNotNull prepend="," property="psrcodeDhlhzl">PSRCODE_DHLHZL = #psrcodeDhlhzl#</isNotNull>
		<isNotNull prepend="," property="psrnameDhlhzl">PSRNAME_DHLHZL = #psrnameDhlhzl#</isNotNull>
		<isNotNull prepend="," property="psrcodeRclzl">PSRCODE_RCLZL = #psrcodeRclzl#</isNotNull>
		<isNotNull prepend="," property="psrnameRclzl">PSRNAME_RCLZL = #psrnameRclzl#</isNotNull>
		<isNotNull prepend="," property="psrcodeTlsbm">PSRCODE_TLSBM = #psrcodeTlsbm#</isNotNull>
		<isNotNull prepend="," property="psrnameTlsbm">PSRNAME_TLSBM = #psrnameTlsbm#</isNotNull>
		<isNotNull prepend="," property="psrcodeTlxbm">PSRCODE_TLXBM = #psrcodeTlxbm#</isNotNull>
		<isNotNull prepend="," property="psrnameTlxbm">PSRNAME_TLXBM = #psrnameTlxbm#</isNotNull>
		<isNotNull prepend="," property="psrcodeWj">PSRCODE_WJ = #psrcodeWj#</isNotNull>
		<isNotNull prepend="," property="psrnameWj">PSRNAME_WJ = #psrnameWj#</isNotNull>
		<isNotNull prepend="," property="psrcodeGd">PSRCODE_GD = #psrcodeGd#</isNotNull>
		<isNotNull prepend="," property="psrnameGd">PSRNAME_GD = #psrnameGd#</isNotNull>
		<isNotNull prepend="," property="psrcodeBh">PSRCODE_BH = #psrcodeBh#</isNotNull>
		<isNotNull prepend="," property="psrnameBh">PSRNAME_BH = #psrnameBh#</isNotNull>
		<isNotNull prepend="," property="psrcodeGjjt">PSRCODE_GJJT = #psrcodeGjjt#</isNotNull>
		<isNotNull prepend="," property="psrnameGjjt">PSRNAME_GJJT = #psrnameGjjt#</isNotNull>
		<isNotNull prepend="," property="psrcodeGdlwlx">PSRCODE_GDLWLX = #psrcodeGdlwlx#</isNotNull>
		<isNotNull prepend="," property="psrnameGdlwlx">PSRNAME_GDLWLX = #psrnameGdlwlx#</isNotNull>
		<isNotNull prepend="," property="psrcodeRclfs">PSRCODE_RCLFS = #psrcodeRclfs#</isNotNull>
		<isNotNull prepend="," property="psrnameRclfs">PSRNAME_RCLFS = #psrnameRclfs#</isNotNull>
		<isNotNull prepend="," property="psrcodeLwlx">PSRCODE_LWLX = #psrcodeLwlx#</isNotNull>
		<isNotNull prepend="," property="psrnameLwlx">PSRNAME_LWLX = #psrnameLwlx#</isNotNull>
		<isNotNull prepend="," property="psrcodeNlwlx">PSRCODE_NLWLX = #psrcodeNlwlx#</isNotNull>
		<isNotNull prepend="," property="psrnameNlwlx">PSRNAME_NLWLX = #psrnameNlwlx#</isNotNull>
		<isNotNull prepend="," property="psr">PSR = #psr#</isNotNull>
		<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
		<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
		<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
		<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
		<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
		<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
		<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
		<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
		<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
		<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
		<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		PH_ID=#phId# 			</update>
	
	<update id="updatewithnull" parameterClass="tKIZLApplyPh">
		UPDATE ${zzzcSchema}.T_KIZL_APPLY_PH	
		SET 
		<dynamic prepend=" ">
	    <isNotNull prepend=", " property="phId">PH_ID = #phId#</isNotNull>
	    <isNull prepend=", " property="phId">PH_ID = NULL</isNull>
	    <isNotNull prepend=", " property="dataFrom">DATA_FROM = #dataFrom#</isNotNull>
	    <isNull prepend=", " property="dataFrom">DATA_FROM = NULL</isNull>
	    <isNotNull prepend=", " property="datafromId">DATAFROM_ID = #datafromId#</isNotNull>
	    <isNull prepend=", " property="datafromId">DATAFROM_ID = NULL</isNull>
	    <isNotNull prepend=", " property="psrPz">PSR_PZ = #psrPz#</isNotNull>
	    <isNull prepend=", " property="psrPz">PSR_PZ = NULL</isNull>
	    <isNotNull prepend=", " property="psrBig">PSR_BIG = #psrBig#</isNotNull>
	    <isNull prepend=", " property="psrBig">PSR_BIG = NULL</isNull>
	    <isNotNull prepend=", " property="shopsign">SHOPSIGN = #shopsign#</isNotNull>
	    <isNull prepend=", " property="shopsign">SHOPSIGN = NULL</isNull>
	    <isNotNull prepend=", " property="phName">PH_NAME = #phName#</isNotNull>
	    <isNull prepend=", " property="phName">PH_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeBz">PSRCODE_BZ = #psrcodeBz#</isNotNull>
	    <isNull prepend=", " property="psrcodeBz">PSRCODE_BZ = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameBz">PSRNAME_BZ = #psrnameBz#</isNotNull>
	    <isNull prepend=", " property="psrnameBz">PSRNAME_BZ = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeGz">PSRCODE_GZ = #psrcodeGz#</isNotNull>
	    <isNull prepend=", " property="psrcodeGz">PSRCODE_GZ = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameGz">PSRNAME_GZ = #psrnameGz#</isNotNull>
	    <isNull prepend=", " property="psrnameGz">PSRNAME_GZ = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeCpflm">PSRCODE_CPFLM = #psrcodeCpflm#</isNotNull>
	    <isNull prepend=", " property="psrcodeCpflm">PSRCODE_CPFLM = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameCpflm">PSRNAME_CPFLM = #psrnameCpflm#</isNotNull>
	    <isNull prepend=", " property="psrnameCpflm">PSRNAME_CPFLM = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeDczl">PSRCODE_DCZL = #psrcodeDczl#</isNotNull>
	    <isNull prepend=", " property="psrcodeDczl">PSRCODE_DCZL = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameDczl">PSRNAME_DCZL = #psrnameDczl#</isNotNull>
	    <isNull prepend=", " property="psrnameDczl">PSRNAME_DCZL = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeHclfs">PSRCODE_HCLFS = #psrcodeHclfs#</isNotNull>
	    <isNull prepend=", " property="psrcodeHclfs">PSRCODE_HCLFS = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameHclfs">PSRNAME_HCLFS = #psrnameHclfs#</isNotNull>
	    <isNull prepend=", " property="psrnameHclfs">PSRNAME_HCLFS = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeDhlhzl">PSRCODE_DHLHZL = #psrcodeDhlhzl#</isNotNull>
	    <isNull prepend=", " property="psrcodeDhlhzl">PSRCODE_DHLHZL = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameDhlhzl">PSRNAME_DHLHZL = #psrnameDhlhzl#</isNotNull>
	    <isNull prepend=", " property="psrnameDhlhzl">PSRNAME_DHLHZL = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeRclzl">PSRCODE_RCLZL = #psrcodeRclzl#</isNotNull>
	    <isNull prepend=", " property="psrcodeRclzl">PSRCODE_RCLZL = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameRclzl">PSRNAME_RCLZL = #psrnameRclzl#</isNotNull>
	    <isNull prepend=", " property="psrnameRclzl">PSRNAME_RCLZL = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeTlsbm">PSRCODE_TLSBM = #psrcodeTlsbm#</isNotNull>
	    <isNull prepend=", " property="psrcodeTlsbm">PSRCODE_TLSBM = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameTlsbm">PSRNAME_TLSBM = #psrnameTlsbm#</isNotNull>
	    <isNull prepend=", " property="psrnameTlsbm">PSRNAME_TLSBM = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeTlxbm">PSRCODE_TLXBM = #psrcodeTlxbm#</isNotNull>
	    <isNull prepend=", " property="psrcodeTlxbm">PSRCODE_TLXBM = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameTlxbm">PSRNAME_TLXBM = #psrnameTlxbm#</isNotNull>
	    <isNull prepend=", " property="psrnameTlxbm">PSRNAME_TLXBM = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeWj">PSRCODE_WJ = #psrcodeWj#</isNotNull>
	    <isNull prepend=", " property="psrcodeWj">PSRCODE_WJ = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameWj">PSRNAME_WJ = #psrnameWj#</isNotNull>
	    <isNull prepend=", " property="psrnameWj">PSRNAME_WJ = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeGd">PSRCODE_GD = #psrcodeGd#</isNotNull>
	    <isNull prepend=", " property="psrcodeGd">PSRCODE_GD = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameGd">PSRNAME_GD = #psrnameGd#</isNotNull>
	    <isNull prepend=", " property="psrnameGd">PSRNAME_GD = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeBh">PSRCODE_BH = #psrcodeBh#</isNotNull>
	    <isNull prepend=", " property="psrcodeBh">PSRCODE_BH = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameBh">PSRNAME_BH = #psrnameBh#</isNotNull>
	    <isNull prepend=", " property="psrnameBh">PSRNAME_BH = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeGjjt">PSRCODE_GJJT = #psrcodeGjjt#</isNotNull>
	    <isNull prepend=", " property="psrcodeGjjt">PSRCODE_GJJT = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameGjjt">PSRNAME_GJJT = #psrnameGjjt#</isNotNull>
	    <isNull prepend=", " property="psrnameGjjt">PSRNAME_GJJT = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeGdlwlx">PSRCODE_GDLWLX = #psrcodeGdlwlx#</isNotNull>
	    <isNull prepend=", " property="psrcodeGdlwlx">PSRCODE_GDLWLX = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameGdlwlx">PSRNAME_GDLWLX = #psrnameGdlwlx#</isNotNull>
	    <isNull prepend=", " property="psrnameGdlwlx">PSRNAME_GDLWLX = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeRclfs">PSRCODE_RCLFS = #psrcodeRclfs#</isNotNull>
	    <isNull prepend=", " property="psrcodeRclfs">PSRCODE_RCLFS = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameRclfs">PSRNAME_RCLFS = #psrnameRclfs#</isNotNull>
	    <isNull prepend=", " property="psrnameRclfs">PSRNAME_RCLFS = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeLwlx">PSRCODE_LWLX = #psrcodeLwlx#</isNotNull>
	    <isNull prepend=", " property="psrcodeLwlx">PSRCODE_LWLX = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameLwlx">PSRNAME_LWLX = #psrnameLwlx#</isNotNull>
	    <isNull prepend=", " property="psrnameLwlx">PSRNAME_LWLX = NULL</isNull>
	    <isNotNull prepend=", " property="psrcodeNlwlx">PSRCODE_NLWLX = #psrcodeNlwlx#</isNotNull>
	    <isNull prepend=", " property="psrcodeNlwlx">PSRCODE_NLWLX = NULL</isNull>
	    <isNotNull prepend=", " property="psrnameNlwlx">PSRNAME_NLWLX = #psrnameNlwlx#</isNotNull>
	    <isNull prepend=", " property="psrnameNlwlx">PSRNAME_NLWLX = NULL</isNull>
	    <isNotNull prepend=", " property="psr">PSR = #psr#</isNotNull>
	    <isNull prepend=", " property="psr">PSR = NULL</isNull>
	    <isNotNull prepend=", " property="extra1">EXTRA1 = #extra1#</isNotNull>
	    <isNull prepend=", " property="extra1">EXTRA1 = NULL</isNull>
	    <isNotNull prepend=", " property="extra2">EXTRA2 = #extra2#</isNotNull>
	    <isNull prepend=", " property="extra2">EXTRA2 = NULL</isNull>
	    <isNotNull prepend=", " property="extra3">EXTRA3 = #extra3#</isNotNull>
	    <isNull prepend=", " property="extra3">EXTRA3 = NULL</isNull>
	    <isNotNull prepend=", " property="extra4">EXTRA4 = #extra4#</isNotNull>
	    <isNull prepend=", " property="extra4">EXTRA4 = NULL</isNull>
	    <isNotNull prepend=", " property="extra5">EXTRA5 = #extra5#</isNotNull>
	    <isNull prepend=", " property="extra5">EXTRA5 = NULL</isNull>
	    <isNotNull prepend=", " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
	    <isNull prepend=", " property="delStatus">DEL_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
	    <isNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="createDate">CREATE_DATE = #createDate#</isNotNull>
	    <isNull prepend=", " property="createDate">CREATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
	    <isNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNull prepend=", " property="updateDate">UPDATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
	    <isNull prepend=", " property="recordVersion">RECORD_VERSION = NULL</isNull>
		</dynamic>
		WHERE 		PH_ID=#phId# 			</update>
	
	<update id="updateByC" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_APPLY_PH	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="phId">PH_ID = #phId#</isNotNull>
				<isNotNull prepend="," property="dataFrom">DATA_FROM = #dataFrom#</isNotNull>
				<isNotNull prepend="," property="datafromId">DATAFROM_ID = #datafromId#</isNotNull>
				<isNotNull prepend="," property="psrPz">PSR_PZ = #psrPz#</isNotNull>
				<isNotNull prepend="," property="psrBig">PSR_BIG = #psrBig#</isNotNull>
				<isNotNull prepend="," property="shopsign">SHOPSIGN = #shopsign#</isNotNull>
				<isNotNull prepend="," property="phName">PH_NAME = #phName#</isNotNull>
				<isNotNull prepend="," property="psrcodeBz">PSRCODE_BZ = #psrcodeBz#</isNotNull>
				<isNotNull prepend="," property="psrnameBz">PSRNAME_BZ = #psrnameBz#</isNotNull>
				<isNotNull prepend="," property="psrcodeGz">PSRCODE_GZ = #psrcodeGz#</isNotNull>
				<isNotNull prepend="," property="psrnameGz">PSRNAME_GZ = #psrnameGz#</isNotNull>
				<isNotNull prepend="," property="psrcodeCpflm">PSRCODE_CPFLM = #psrcodeCpflm#</isNotNull>
				<isNotNull prepend="," property="psrnameCpflm">PSRNAME_CPFLM = #psrnameCpflm#</isNotNull>
				<isNotNull prepend="," property="psrcodeDczl">PSRCODE_DCZL = #psrcodeDczl#</isNotNull>
				<isNotNull prepend="," property="psrnameDczl">PSRNAME_DCZL = #psrnameDczl#</isNotNull>
				<isNotNull prepend="," property="psrcodeHclfs">PSRCODE_HCLFS = #psrcodeHclfs#</isNotNull>
				<isNotNull prepend="," property="psrnameHclfs">PSRNAME_HCLFS = #psrnameHclfs#</isNotNull>
				<isNotNull prepend="," property="psrcodeDhlhzl">PSRCODE_DHLHZL = #psrcodeDhlhzl#</isNotNull>
				<isNotNull prepend="," property="psrnameDhlhzl">PSRNAME_DHLHZL = #psrnameDhlhzl#</isNotNull>
				<isNotNull prepend="," property="psrcodeRclzl">PSRCODE_RCLZL = #psrcodeRclzl#</isNotNull>
				<isNotNull prepend="," property="psrnameRclzl">PSRNAME_RCLZL = #psrnameRclzl#</isNotNull>
				<isNotNull prepend="," property="psrcodeTlsbm">PSRCODE_TLSBM = #psrcodeTlsbm#</isNotNull>
				<isNotNull prepend="," property="psrnameTlsbm">PSRNAME_TLSBM = #psrnameTlsbm#</isNotNull>
				<isNotNull prepend="," property="psrcodeTlxbm">PSRCODE_TLXBM = #psrcodeTlxbm#</isNotNull>
				<isNotNull prepend="," property="psrnameTlxbm">PSRNAME_TLXBM = #psrnameTlxbm#</isNotNull>
				<isNotNull prepend="," property="psrcodeWj">PSRCODE_WJ = #psrcodeWj#</isNotNull>
				<isNotNull prepend="," property="psrnameWj">PSRNAME_WJ = #psrnameWj#</isNotNull>
				<isNotNull prepend="," property="psrcodeGd">PSRCODE_GD = #psrcodeGd#</isNotNull>
				<isNotNull prepend="," property="psrnameGd">PSRNAME_GD = #psrnameGd#</isNotNull>
				<isNotNull prepend="," property="psrcodeBh">PSRCODE_BH = #psrcodeBh#</isNotNull>
				<isNotNull prepend="," property="psrnameBh">PSRNAME_BH = #psrnameBh#</isNotNull>
				<isNotNull prepend="," property="psrcodeGjjt">PSRCODE_GJJT = #psrcodeGjjt#</isNotNull>
				<isNotNull prepend="," property="psrnameGjjt">PSRNAME_GJJT = #psrnameGjjt#</isNotNull>
				<isNotNull prepend="," property="psrcodeGdlwlx">PSRCODE_GDLWLX = #psrcodeGdlwlx#</isNotNull>
				<isNotNull prepend="," property="psrnameGdlwlx">PSRNAME_GDLWLX = #psrnameGdlwlx#</isNotNull>
				<isNotNull prepend="," property="psrcodeRclfs">PSRCODE_RCLFS = #psrcodeRclfs#</isNotNull>
				<isNotNull prepend="," property="psrnameRclfs">PSRNAME_RCLFS = #psrnameRclfs#</isNotNull>
				<isNotNull prepend="," property="psrcodeLwlx">PSRCODE_LWLX = #psrcodeLwlx#</isNotNull>
				<isNotNull prepend="," property="psrnameLwlx">PSRNAME_LWLX = #psrnameLwlx#</isNotNull>
				<isNotNull prepend="," property="psrcodeNlwlx">PSRCODE_NLWLX = #psrcodeNlwlx#</isNotNull>
				<isNotNull prepend="," property="psrnameNlwlx">PSRNAME_NLWLX = #psrnameNlwlx#</isNotNull>
				<isNotNull prepend="," property="psr">PSR = #psr#</isNotNull>
				<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
				<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
				<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
				<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
				<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
				<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
				<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
				<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
				<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
				<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
				<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
			</dynamic>
		<dynamic prepend=" WHERE ">
		 			<isNotNull prepend=" AND " property="phIdOld">PH_ID = #phIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="dataFromOld">DATA_FROM = #dataFromOld#</isNotNull>
			<isNotNull prepend=" AND " property="datafromIdOld">DATAFROM_ID = #datafromIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrPzOld">PSR_PZ = #psrPzOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrBigOld">PSR_BIG = #psrBigOld#</isNotNull>
			<isNotNull prepend=" AND " property="shopsignOld">SHOPSIGN = #shopsignOld#</isNotNull>
			<isNotNull prepend=" AND " property="phNameOld">PH_NAME = #phNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeBzOld">PSRCODE_BZ = #psrcodeBzOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameBzOld">PSRNAME_BZ = #psrnameBzOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeGzOld">PSRCODE_GZ = #psrcodeGzOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameGzOld">PSRNAME_GZ = #psrnameGzOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeCpflmOld">PSRCODE_CPFLM = #psrcodeCpflmOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameCpflmOld">PSRNAME_CPFLM = #psrnameCpflmOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeDczlOld">PSRCODE_DCZL = #psrcodeDczlOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameDczlOld">PSRNAME_DCZL = #psrnameDczlOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeHclfsOld">PSRCODE_HCLFS = #psrcodeHclfsOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameHclfsOld">PSRNAME_HCLFS = #psrnameHclfsOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeDhlhzlOld">PSRCODE_DHLHZL = #psrcodeDhlhzlOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameDhlhzlOld">PSRNAME_DHLHZL = #psrnameDhlhzlOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeRclzlOld">PSRCODE_RCLZL = #psrcodeRclzlOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameRclzlOld">PSRNAME_RCLZL = #psrnameRclzlOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeTlsbmOld">PSRCODE_TLSBM = #psrcodeTlsbmOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameTlsbmOld">PSRNAME_TLSBM = #psrnameTlsbmOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeTlxbmOld">PSRCODE_TLXBM = #psrcodeTlxbmOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameTlxbmOld">PSRNAME_TLXBM = #psrnameTlxbmOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeWjOld">PSRCODE_WJ = #psrcodeWjOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameWjOld">PSRNAME_WJ = #psrnameWjOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeGdOld">PSRCODE_GD = #psrcodeGdOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameGdOld">PSRNAME_GD = #psrnameGdOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeBhOld">PSRCODE_BH = #psrcodeBhOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameBhOld">PSRNAME_BH = #psrnameBhOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeGjjtOld">PSRCODE_GJJT = #psrcodeGjjtOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameGjjtOld">PSRNAME_GJJT = #psrnameGjjtOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeGdlwlxOld">PSRCODE_GDLWLX = #psrcodeGdlwlxOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameGdlwlxOld">PSRNAME_GDLWLX = #psrnameGdlwlxOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeRclfsOld">PSRCODE_RCLFS = #psrcodeRclfsOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameRclfsOld">PSRNAME_RCLFS = #psrnameRclfsOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeLwlxOld">PSRCODE_LWLX = #psrcodeLwlxOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameLwlxOld">PSRNAME_LWLX = #psrnameLwlxOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrcodeNlwlxOld">PSRCODE_NLWLX = #psrcodeNlwlxOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrnameNlwlxOld">PSRNAME_NLWLX = #psrnameNlwlxOld#</isNotNull>
			<isNotNull prepend=" AND " property="psrOld">PSR = #psrOld#</isNotNull>
			<isNotNull prepend=" AND " property="extra1Old">EXTRA1 = #extra1Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra2Old">EXTRA2 = #extra2Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra3Old">EXTRA3 = #extra3Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra4Old">EXTRA4 = #extra4Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra5Old">EXTRA5 = #extra5Old#</isNotNull>
			<isNotNull prepend=" AND " property="delStatusOld">DEL_STATUS = #delStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabelOld">CREATE_USER_LABEL = #createUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="createDateOld">CREATE_DATE = #createDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabelOld">UPDATE_USER_LABEL = #updateUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateDateOld">UPDATE_DATE = #updateDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabelOld">DELETE_USER_LABEL = #deleteUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDateOld">DELETE_DATE = #deleteDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersionOld">RECORD_VERSION = #recordVersionOld#</isNotNull>
			<isNotNull prepend=" AND " property="dynSqlOld"> $dynSqlOld$ </isNotNull>
		</dynamic>
	</update>
	
	<update id="updateNull" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_APPLY_PH	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="phId">PH_ID = #phId#</isNotNull>
			<isNotNull prepend="," property="dataFrom">DATA_FROM = #dataFrom#</isNotNull>
			<isNotNull prepend="," property="datafromId">DATAFROM_ID = #datafromId#</isNotNull>
			<isNotNull prepend="," property="psrPz">PSR_PZ = #psrPz#</isNotNull>
			<isNotNull prepend="," property="psrBig">PSR_BIG = #psrBig#</isNotNull>
			<isNotNull prepend="," property="shopsign">SHOPSIGN = #shopsign#</isNotNull>
			<isNotNull prepend="," property="phName">PH_NAME = #phName#</isNotNull>
			<isNotNull prepend="," property="psrcodeBz">PSRCODE_BZ = #psrcodeBz#</isNotNull>
			<isNotNull prepend="," property="psrnameBz">PSRNAME_BZ = #psrnameBz#</isNotNull>
			<isNotNull prepend="," property="psrcodeGz">PSRCODE_GZ = #psrcodeGz#</isNotNull>
			<isNotNull prepend="," property="psrnameGz">PSRNAME_GZ = #psrnameGz#</isNotNull>
			<isNotNull prepend="," property="psrcodeCpflm">PSRCODE_CPFLM = #psrcodeCpflm#</isNotNull>
			<isNotNull prepend="," property="psrnameCpflm">PSRNAME_CPFLM = #psrnameCpflm#</isNotNull>
			<isNotNull prepend="," property="psrcodeDczl">PSRCODE_DCZL = #psrcodeDczl#</isNotNull>
			<isNotNull prepend="," property="psrnameDczl">PSRNAME_DCZL = #psrnameDczl#</isNotNull>
			<isNotNull prepend="," property="psrcodeHclfs">PSRCODE_HCLFS = #psrcodeHclfs#</isNotNull>
			<isNotNull prepend="," property="psrnameHclfs">PSRNAME_HCLFS = #psrnameHclfs#</isNotNull>
			<isNotNull prepend="," property="psrcodeDhlhzl">PSRCODE_DHLHZL = #psrcodeDhlhzl#</isNotNull>
			<isNotNull prepend="," property="psrnameDhlhzl">PSRNAME_DHLHZL = #psrnameDhlhzl#</isNotNull>
			<isNotNull prepend="," property="psrcodeRclzl">PSRCODE_RCLZL = #psrcodeRclzl#</isNotNull>
			<isNotNull prepend="," property="psrnameRclzl">PSRNAME_RCLZL = #psrnameRclzl#</isNotNull>
			<isNotNull prepend="," property="psrcodeTlsbm">PSRCODE_TLSBM = #psrcodeTlsbm#</isNotNull>
			<isNotNull prepend="," property="psrnameTlsbm">PSRNAME_TLSBM = #psrnameTlsbm#</isNotNull>
			<isNotNull prepend="," property="psrcodeTlxbm">PSRCODE_TLXBM = #psrcodeTlxbm#</isNotNull>
			<isNotNull prepend="," property="psrnameTlxbm">PSRNAME_TLXBM = #psrnameTlxbm#</isNotNull>
			<isNotNull prepend="," property="psrcodeWj">PSRCODE_WJ = #psrcodeWj#</isNotNull>
			<isNotNull prepend="," property="psrnameWj">PSRNAME_WJ = #psrnameWj#</isNotNull>
			<isNotNull prepend="," property="psrcodeGd">PSRCODE_GD = #psrcodeGd#</isNotNull>
			<isNotNull prepend="," property="psrnameGd">PSRNAME_GD = #psrnameGd#</isNotNull>
			<isNotNull prepend="," property="psrcodeBh">PSRCODE_BH = #psrcodeBh#</isNotNull>
			<isNotNull prepend="," property="psrnameBh">PSRNAME_BH = #psrnameBh#</isNotNull>
			<isNotNull prepend="," property="psrcodeGjjt">PSRCODE_GJJT = #psrcodeGjjt#</isNotNull>
			<isNotNull prepend="," property="psrnameGjjt">PSRNAME_GJJT = #psrnameGjjt#</isNotNull>
			<isNotNull prepend="," property="psrcodeGdlwlx">PSRCODE_GDLWLX = #psrcodeGdlwlx#</isNotNull>
			<isNotNull prepend="," property="psrnameGdlwlx">PSRNAME_GDLWLX = #psrnameGdlwlx#</isNotNull>
			<isNotNull prepend="," property="psrcodeRclfs">PSRCODE_RCLFS = #psrcodeRclfs#</isNotNull>
			<isNotNull prepend="," property="psrnameRclfs">PSRNAME_RCLFS = #psrnameRclfs#</isNotNull>
			<isNotNull prepend="," property="psrcodeLwlx">PSRCODE_LWLX = #psrcodeLwlx#</isNotNull>
			<isNotNull prepend="," property="psrnameLwlx">PSRNAME_LWLX = #psrnameLwlx#</isNotNull>
			<isNotNull prepend="," property="psrcodeNlwlx">PSRCODE_NLWLX = #psrcodeNlwlx#</isNotNull>
			<isNotNull prepend="," property="psrnameNlwlx">PSRNAME_NLWLX = #psrnameNlwlx#</isNotNull>
			<isNotNull prepend="," property="psr">PSR = #psr#</isNotNull>
			<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			<isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
			<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		PH_ID=#phId# 			</update>	
</sqlMap>