package com.baosight.bscdkj.common.ki.domain;

import com.baosight.iplat4j.core.ei.EiColumn;
import com.baosight.bscdkj.common.domain.AbstractDomain;
import javax.validation.constraints.Size;

/**
 * 境内专利_其它_答复审查: T_KIZL_OTHER_DFSC
 * 
 * 
 * <AUTHOR>
 */
public class TkizlOtherDfsc  extends AbstractDomain{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	//主键 : DFSC_ID 
	@Size(max = 36,message = "主键最大为36位字符")	
	private String dfscId; 
	
	//专利主键 : PATENT_ID 
	@Size(max = 36,message = "专利主键最大为36位字符")	
	private String patentId; 
	
	//管理员 : GLY 
	@Size(max = 10,message = "管理员最大为10位字符")	
	private String gly; 
	
	//管理员姓名 : GLY_NAME 
	@Size(max = 20,message = "管理员姓名最大为20位字符")	
	private String glyName; 
	
	//管理员电话 : ADMIN_PHONE 
	@Size(max = 20,message = "管理员电话最大为20位字符")	
	private String adminPhone; 
	
	//发明人 : FMR 
	@Size(max = 10,message = "发明人最大为10位字符")	
	private String fmr; 
	
	//发明人姓名 : FMR_NAME 
	@Size(max = 20,message = "发明人姓名最大为20位字符")	
	private String fmrName; 
	
	//发明设计人 : FMSJR_ALL 
	@Size(max = 300,message = "发明设计人最大为300位字符")	
	private String fmsjrAll; 
	
	//发明人电话 : FMR_PHONE 
	@Size(max = 20,message = "发明人电话最大为20位字符")	
	private String fmrPhone; 
	
	//审查意见发文日 : DT_SCYJ 
	@Size(max = 50,message = "审查意见发文日最大为50位字符")	
	private String dtScyj; 
	
	//发明人答复截止日期 : DT_FMRJZ 
	@Size(max = 50,message = "发明人答复截止日期最大为50位字符")	
	private String dtFmrjz; 
	
	//代理机构主键 : SWS_ID 
	@Size(max = 36,message = "代理机构主键最大为36位字符")	
	private String swsId; 
	
	//代理人姓名 : SWSDLR_NAME 
	@Size(max = 20,message = "代理人姓名最大为20位字符")	
	private String swsdlrName; 
	
	//代理人电话 : SWSDLR_PHONE 
	@Size(max = 20,message = "代理人电话最大为20位字符")	
	private String swsdlrPhone; 
	
	//放弃答复理由说明 : CONTENT_FQDF 
	@Size(max = 500,message = "放弃答复理由说明最大为500位字符")	
	private String contentFqdf; 
	
	//递交日 : DT_SUBMIT 
	@Size(max = 50,message = "递交日最大为50位字符")	
	private String dtSubmit; 
	
	//代理人邮箱 : SWSDLR_EMAIL 
	@Size(max = 30,message = "代理人邮箱最大为30位字符")	
	private String swsdlrEmail; 
	
	//发明人最终意见 1-同意答复 0-放弃答复 : FIN_FMRYJ 
	@Size(max = 1,message = "发明人最终意见 1-同意答复 0-放弃答复最大为1位字符")	
	private String finFmryj; 
	
	//主管部门最终意见 1-同意答复 0-放弃答复 : FIN_ZGBMYJ 
	@Size(max = 1,message = "主管部门最终意见 1-同意答复 0-放弃答复最大为1位字符")	
	private String finZgbmyj; 
	
	//draft-草稿 active-审批中 end-结束 : FLOW_STATUS 
	@Size(max = 20,message = "draft-草稿 active-审批中 end-结束最大为20位字符")	
	private String flowStatus; 
	
	//扩展字段1发明人收到日，打开时更新主表 : EXTRA1 
	@Size(max = 30,message = "扩展字段1发明人收到日，打开时更新主表最大为30位字符")	
	private String extra1; 
	
	//扩展字段2 : EXTRA2  序号
	@Size(max = 30,message = "扩展字段2最大为30位字符")	
	private String extra2; 
	
	//扩展字段3 : EXTRA3  
	@Size(max = 200,message = "扩展字段3最大为200位字符")	
	private String extra3; 
	
	//扩展字段4 : EXTRA4 答复次数
	@Size(max = 30,message = "扩展字段4最大为30位字符")	
	private String extra4; 
	
	//扩展字段5 : EXTRA5 
	@Size(max = 30,message = "扩展字段5最大为30位字符")	
	private String extra5; 
	
	//代理人 : DLR_NO 
	@Size(max = 50,message = "代理人最大为50位字符")	
	private String dlrNo; 
	

	/**
	 * 主键 : DFSC_ID
	 * 
	 * @return 
	 */
	public String getDfscId () {
		return dfscId;
	}
	
	/**
	 * 主键 : DFSC_ID
	 * 
	 * @return 
	 */
	public void setDfscId (String dfscId) {
		this.dfscId = dfscId;
	}
	/**
	 * 专利主键 : PATENT_ID
	 * 
	 * @return 
	 */
	public String getPatentId () {
		return patentId;
	}
	
	/**
	 * 专利主键 : PATENT_ID
	 * 
	 * @return 
	 */
	public void setPatentId (String patentId) {
		this.patentId = patentId;
	}
	/**
	 * 管理员 : GLY
	 * 
	 * @return 
	 */
	public String getGly () {
		return gly;
	}
	
	/**
	 * 管理员 : GLY
	 * 
	 * @return 
	 */
	public void setGly (String gly) {
		this.gly = gly;
	}
	/**
	 * 管理员姓名 : GLY_NAME
	 * 
	 * @return 
	 */
	public String getGlyName () {
		return glyName;
	}
	
	/**
	 * 管理员姓名 : GLY_NAME
	 * 
	 * @return 
	 */
	public void setGlyName (String glyName) {
		this.glyName = glyName;
	}
	/**
	 * 管理员电话 : ADMIN_PHONE
	 * 
	 * @return 
	 */
	public String getAdminPhone () {
		return adminPhone;
	}
	
	/**
	 * 管理员电话 : ADMIN_PHONE
	 * 
	 * @return 
	 */
	public void setAdminPhone (String adminPhone) {
		this.adminPhone = adminPhone;
	}
	/**
	 * 发明人 : FMR
	 * 
	 * @return 
	 */
	public String getFmr () {
		return fmr;
	}
	
	/**
	 * 发明人 : FMR
	 * 
	 * @return 
	 */
	public void setFmr (String fmr) {
		this.fmr = fmr;
	}
	/**
	 * 发明人姓名 : FMR_NAME
	 * 
	 * @return 
	 */
	public String getFmrName () {
		return fmrName;
	}
	
	/**
	 * 发明人姓名 : FMR_NAME
	 * 
	 * @return 
	 */
	public void setFmrName (String fmrName) {
		this.fmrName = fmrName;
	}
	/**
	 * 发明设计人 : FMSJR_ALL
	 * 
	 * @return 
	 */
	public String getFmsjrAll () {
		return fmsjrAll;
	}
	
	/**
	 * 发明设计人 : FMSJR_ALL
	 * 
	 * @return 
	 */
	public void setFmsjrAll (String fmsjrAll) {
		this.fmsjrAll = fmsjrAll;
	}
	/**
	 * 发明人电话 : FMR_PHONE
	 * 
	 * @return 
	 */
	public String getFmrPhone () {
		return fmrPhone;
	}
	
	/**
	 * 发明人电话 : FMR_PHONE
	 * 
	 * @return 
	 */
	public void setFmrPhone (String fmrPhone) {
		this.fmrPhone = fmrPhone;
	}
	/**
	 * 审查意见发文日 : DT_SCYJ
	 * 
	 * @return 
	 */
	public String getDtScyj () {
		return dtScyj;
	}
	
	/**
	 * 审查意见发文日 : DT_SCYJ
	 * 
	 * @return 
	 */
	public void setDtScyj (String dtScyj) {
		this.dtScyj = dtScyj;
	}
	/**
	 * 发明人答复截止日期 : DT_FMRJZ
	 * 
	 * @return 
	 */
	public String getDtFmrjz () {
		return dtFmrjz;
	}
	
	/**
	 * 发明人答复截止日期 : DT_FMRJZ
	 * 
	 * @return 
	 */
	public void setDtFmrjz (String dtFmrjz) {
		this.dtFmrjz = dtFmrjz;
	}
	/**
	 * 代理机构主键 : SWS_ID
	 * 
	 * @return 
	 */
	public String getSwsId () {
		return swsId;
	}
	
	/**
	 * 代理机构主键 : SWS_ID
	 * 
	 * @return 
	 */
	public void setSwsId (String swsId) {
		this.swsId = swsId;
	}
	/**
	 * 代理人姓名 : SWSDLR_NAME
	 * 
	 * @return 
	 */
	public String getSwsdlrName () {
		return swsdlrName;
	}
	
	/**
	 * 代理人姓名 : SWSDLR_NAME
	 * 
	 * @return 
	 */
	public void setSwsdlrName (String swsdlrName) {
		this.swsdlrName = swsdlrName;
	}
	/**
	 * 代理人电话 : SWSDLR_PHONE
	 * 
	 * @return 
	 */
	public String getSwsdlrPhone () {
		return swsdlrPhone;
	}
	
	/**
	 * 代理人电话 : SWSDLR_PHONE
	 * 
	 * @return 
	 */
	public void setSwsdlrPhone (String swsdlrPhone) {
		this.swsdlrPhone = swsdlrPhone;
	}
	/**
	 * 放弃答复理由说明 : CONTENT_FQDF
	 * 
	 * @return 
	 */
	public String getContentFqdf () {
		return contentFqdf;
	}
	
	/**
	 * 放弃答复理由说明 : CONTENT_FQDF
	 * 
	 * @return 
	 */
	public void setContentFqdf (String contentFqdf) {
		this.contentFqdf = contentFqdf;
	}
	/**
	 * 递交日 : DT_SUBMIT
	 * 
	 * @return 
	 */
	public String getDtSubmit () {
		return dtSubmit;
	}
	
	/**
	 * 递交日 : DT_SUBMIT
	 * 
	 * @return 
	 */
	public void setDtSubmit (String dtSubmit) {
		this.dtSubmit = dtSubmit;
	}
	/**
	 * 代理人邮箱 : SWSDLR_EMAIL
	 * 
	 * @return 
	 */
	public String getSwsdlrEmail () {
		return swsdlrEmail;
	}
	
	/**
	 * 代理人邮箱 : SWSDLR_EMAIL
	 * 
	 * @return 
	 */
	public void setSwsdlrEmail (String swsdlrEmail) {
		this.swsdlrEmail = swsdlrEmail;
	}
	/**
	 * 发明人最终意见 1-同意答复 0-放弃答复 : FIN_FMRYJ
	 * 
	 * @return 
	 */
	public String getFinFmryj () {
		return finFmryj;
	}
	
	/**
	 * 发明人最终意见 1-同意答复 0-放弃答复 : FIN_FMRYJ
	 * 
	 * @return 
	 */
	public void setFinFmryj (String finFmryj) {
		this.finFmryj = finFmryj;
	}
	/**
	 * 主管部门最终意见 1-同意答复 0-放弃答复 : FIN_ZGBMYJ
	 * 
	 * @return 
	 */
	public String getFinZgbmyj () {
		return finZgbmyj;
	}
	
	/**
	 * 主管部门最终意见 1-同意答复 0-放弃答复 : FIN_ZGBMYJ
	 * 
	 * @return 
	 */
	public void setFinZgbmyj (String finZgbmyj) {
		this.finZgbmyj = finZgbmyj;
	}
	/**
	 * draft-草稿 active-审批中 end-结束 : FLOW_STATUS
	 * 
	 * @return 
	 */
	public String getFlowStatus () {
		return flowStatus;
	}
	
	/**
	 * draft-草稿 active-审批中 end-结束 : FLOW_STATUS
	 * 
	 * @return 
	 */
	public void setFlowStatus (String flowStatus) {
		this.flowStatus = flowStatus;
	}
	/**
	 * 扩展字段1发明人收到日，打开时更新主表 : EXTRA1
	 * 
	 * @return 
	 */
	public String getExtra1 () {
		return extra1;
	}
	
	/**
	 * 扩展字段1发明人收到日，打开时更新主表 : EXTRA1
	 * 
	 * @return 
	 */
	public void setExtra1 (String extra1) {
		this.extra1 = extra1;
	}
	/**
	 * 扩展字段2 : EXTRA2
	 * 
	 * @return 
	 */
	public String getExtra2 () {
		return extra2;
	}
	
	/**
	 * 扩展字段2 : EXTRA2
	 * 
	 * @return 
	 */
	public void setExtra2 (String extra2) {
		this.extra2 = extra2;
	}
	/**
	 * 扩展字段3 : EXTRA3
	 * 
	 * @return 
	 */
	public String getExtra3 () {
		return extra3;
	}
	
	/**
	 * 扩展字段3 : EXTRA3
	 * 
	 * @return 
	 */
	public void setExtra3 (String extra3) {
		this.extra3 = extra3;
	}
	/**
	 * 扩展字段4 : EXTRA4
	 * 
	 * @return 
	 */
	public String getExtra4 () {
		return extra4;
	}
	
	/**
	 * 扩展字段4 : EXTRA4
	 * 
	 * @return 
	 */
	public void setExtra4 (String extra4) {
		this.extra4 = extra4;
	}
	/**
	 * 扩展字段5 : EXTRA5
	 * 
	 * @return 
	 */
	public String getExtra5 () {
		return extra5;
	}
	
	/**
	 * 扩展字段5 : EXTRA5
	 * 
	 * @return 
	 */
	public void setExtra5 (String extra5) {
		this.extra5 = extra5;
	}
	/**
	 * 代理人 : DLR_NO
	 * 
	 * @return 
	 */
	public String getDlrNo () {
		return dlrNo;
	}
	
	/**
	 * 代理人 : DLR_NO
	 * 
	 * @return 
	 */
	public void setDlrNo (String dlrNo) {
		this.dlrNo = dlrNo;
	}


    public void initMetaData(){
    	EiColumn eiColumn;
    	  	
			eiColumn = new EiColumn("dfscId");
       	eiColumn.setDescName("主键");
        eiColumn.setMaxLength(36);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("patentId");
       	eiColumn.setDescName("专利主键");
        eiColumn.setMaxLength(36);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("gly");
       	eiColumn.setDescName("管理员");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("glyName");
       	eiColumn.setDescName("管理员姓名");
        eiColumn.setMaxLength(20);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("adminPhone");
       	eiColumn.setDescName("管理员电话");
        eiColumn.setMaxLength(20);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("fmr");
       	eiColumn.setDescName("发明人");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("fmrName");
       	eiColumn.setDescName("发明人姓名");
        eiColumn.setMaxLength(20);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("fmsjrAll");
       	eiColumn.setDescName("发明设计人");
        eiColumn.setMaxLength(300);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("fmrPhone");
       	eiColumn.setDescName("发明人电话");
        eiColumn.setMaxLength(20);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("dtScyj");
       	eiColumn.setDescName("审查意见发文日");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("dtFmrjz");
       	eiColumn.setDescName("发明人答复截止日期");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("swsId");
       	eiColumn.setDescName("代理机构主键");
        eiColumn.setMaxLength(36);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("swsdlrName");
       	eiColumn.setDescName("代理人姓名");
        eiColumn.setMaxLength(20);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("swsdlrPhone");
       	eiColumn.setDescName("代理人电话");
        eiColumn.setMaxLength(20);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("contentFqdf");
       	eiColumn.setDescName("放弃答复理由说明");
        eiColumn.setMaxLength(500);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("dtSubmit");
       	eiColumn.setDescName("递交日");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("swsdlrEmail");
       	eiColumn.setDescName("代理人邮箱");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("finFmryj");
       	eiColumn.setDescName("发明人最终意见 1-同意答复 0-放弃答复");
        eiColumn.setMaxLength(1);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("finZgbmyj");
       	eiColumn.setDescName("主管部门最终意见 1-同意答复 0-放弃答复");
        eiColumn.setMaxLength(1);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("flowStatus");
       	eiColumn.setDescName("draft-草稿 active-审批中 end-结束");
        eiColumn.setMaxLength(20);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra1");
       	eiColumn.setDescName("扩展字段1发明人收到日，打开时更新主表");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra2");
       	eiColumn.setDescName("扩展字段2");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra3");
       	eiColumn.setDescName("扩展字段3");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra4");
       	eiColumn.setDescName("扩展字段4");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("extra5");
       	eiColumn.setDescName("扩展字段5");
        eiColumn.setMaxLength(30);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("delStatus");
       	eiColumn.setDescName("删除状态");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("createUserLabel");
       	eiColumn.setDescName("创建人");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("createDate");
       	eiColumn.setDescName("创建时间");
        eiColumn.setMaxLength(26);
        eiColumn.setType("date");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("updateUserLabel");
       	eiColumn.setDescName("更新人");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("updateDate");
       	eiColumn.setDescName("更新时间");
        eiColumn.setMaxLength(26);
        eiColumn.setType("date");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("deleteUserLabel");
       	eiColumn.setDescName("删除人");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("deleteDate");
       	eiColumn.setDescName("删除时间");
        eiColumn.setMaxLength(26);
        eiColumn.setType("date");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("recordVersion");
       	eiColumn.setDescName("版本号");
        eiColumn.setMaxLength(10);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiColumn = new EiColumn("dlrNo");
       	eiColumn.setDescName("代理人");
        eiColumn.setMaxLength(50);
        eiColumn.setType("C");
        eiMetadata.addMeta(eiColumn);   
           
		eiMetadata.getMeta("dfscId").setPrimaryKey(true);
	        
    }

    public TkizlOtherDfsc (){
        initMetaData();
    }
    
}
