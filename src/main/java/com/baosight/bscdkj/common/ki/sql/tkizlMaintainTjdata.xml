<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlMaintainTjdata">
	<typeAlias alias="tKIZLMaintainTjdata" type="com.baosight.bscdkj.common.ki.domain.TkizlMaintainTjdata"/>
	<select id="load" parameterClass="string" resultClass="tKIZLMaintainTjdata">
		SELECT 
				TJDATA_ID as "tjdataId" ,		
				DEPT_CODE as "deptCode" ,		
				DEPT_NAME as "deptName" ,		
				PATENT_TYPE as "patentType" ,		
				MONEY_NAME as "moneyName" ,		
				NY as "ny" ,		
				SL as "sl" ,		
				EXTRA1 as "extra1" ,		
				EXTRA2 as "extra2" ,		
				<PERSON>XT<PERSON><PERSON> as "extra3" ,		
				EXTRA<PERSON> as "extra4" ,		
				EXTRA5 as "extra5" ,		
				DEL_STATUS as "delStatus" ,		
				CREATE_USER_LABEL as "createUserLabel" ,		
				CREATE_DATE as "createDate" ,		
				UPDATE_USER_LABEL as "updateUserLabel" ,		
				UPDATE_DATE as "updateDate" ,		
				DELETE_USER_LABEL as "deleteUserLabel" ,		
				DELETE_DATE as "deleteDate" ,		
				RECORD_VERSION as "recordVersion" 		
				FROM ${zzzcSchema}.T_KIZL_MAINTAIN_TJDATA
		WHERE   TJDATA_ID=#value# 				
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tKIZLMaintainTjdata">
		SELECT
				TJDATA_ID  as "tjdataId" ,		
				DEPT_CODE  as "deptCode" ,		
				DEPT_NAME  as "deptName" ,		
				PATENT_TYPE  as "patentType" ,		
				MONEY_NAME  as "moneyName" ,		
				NY  as "ny" ,		
				SL  as "sl" ,		
				EXTRA1  as "extra1" ,		
				EXTRA2  as "extra2" ,		
				EXTRA3  as "extra3" ,		
				EXTRA4  as "extra4" ,		
				EXTRA5  as "extra5" ,		
				DEL_STATUS  as "delStatus" ,		
				CREATE_USER_LABEL  as "createUserLabel" ,		
				CREATE_DATE  as "createDate" ,		
				UPDATE_USER_LABEL  as "updateUserLabel" ,		
				UPDATE_DATE  as "updateDate" ,		
				DELETE_USER_LABEL  as "deleteUserLabel" ,		
				DELETE_DATE  as "deleteDate" ,		
				RECORD_VERSION  as "recordVersion" 		
				FROM ${zzzcSchema}.T_KIZL_MAINTAIN_TJDATA
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="tjdataId">TJDATA_ID =  #tjdataId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE =  #deptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptName">DEPT_NAME =  #deptName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="moneyName">MONEY_NAME =  #moneyName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ny">NY =  #ny#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sl">SL =  #sl#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KIZL_MAINTAIN_TJDATA 
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="tjdataId">TJDATA_ID =  #tjdataId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE =  #deptCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptName">DEPT_NAME =  #deptName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="patentType">PATENT_TYPE =  #patentType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="moneyName">MONEY_NAME =  #moneyName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ny">NY =  #ny#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sl">SL =  #sl#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tKIZLMaintainTjdata">
		INSERT INTO ${zzzcSchema}.T_KIZL_MAINTAIN_TJDATA ( 
		<dynamic prepend=" ">
					<isNotNull prepend=", " property="tjdataId">TJDATA_ID </isNotNull>
					<isNotNull prepend=", " property="deptCode">DEPT_CODE </isNotNull>
					<isNotNull prepend=", " property="deptName">DEPT_NAME </isNotNull>
					<isNotNull prepend=", " property="patentType">PATENT_TYPE </isNotNull>
					<isNotNull prepend=", " property="moneyName">MONEY_NAME </isNotNull>
					<isNotNull prepend=", " property="ny">NY </isNotNull>
					<isNotNull prepend=", " property="sl">SL </isNotNull>
					<isNotNull prepend=", " property="extra1">EXTRA1 </isNotNull>
					<isNotNull prepend=", " property="extra2">EXTRA2 </isNotNull>
					<isNotNull prepend=", " property="extra3">EXTRA3 </isNotNull>
					<isNotNull prepend=", " property="extra4">EXTRA4 </isNotNull>
					<isNotNull prepend=", " property="extra5">EXTRA5 </isNotNull>
					<isNotNull prepend=", " property="delStatus">DEL_STATUS </isNotNull>
					<isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="createDate">CREATE_DATE </isNotNull>
					<isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="updateDate">UPDATE_DATE </isNotNull>
					<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="deleteDate">DELETE_DATE </isNotNull>
					<isNotNull prepend=", " property="recordVersion">RECORD_VERSION </isNotNull>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotNull prepend=", " property="tjdataId">#tjdataId#</isNotNull>
			<isNotNull prepend=", " property="deptCode">#deptCode#</isNotNull>
			<isNotNull prepend=", " property="deptName">#deptName#</isNotNull>
			<isNotNull prepend=", " property="patentType">#patentType#</isNotNull>
			<isNotNull prepend=", " property="moneyName">#moneyName#</isNotNull>
			<isNotNull prepend=", " property="ny">#ny#</isNotNull>
			<isNotNull prepend=", " property="sl">#sl#</isNotNull>
			<isNotNull prepend=", " property="extra1">#extra1#</isNotNull>
			<isNotNull prepend=", " property="extra2">#extra2#</isNotNull>
			<isNotNull prepend=", " property="extra3">#extra3#</isNotNull>
			<isNotNull prepend=", " property="extra4">#extra4#</isNotNull>
			<isNotNull prepend=", " property="extra5">#extra5#</isNotNull>
			<isNotNull prepend=", " property="delStatus">#delStatus#</isNotNull>
			<isNotNull prepend=", " property="createUserLabel">#createUserLabel#</isNotNull>
			<isNotNull prepend=", " property="createDate">
			   <isNotEmpty property="createDate">#createDate#</isNotEmpty>
			   <isEmpty property="createDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="updateUserLabel">#updateUserLabel#</isNotNull>
			<isNotNull prepend=", " property="updateDate">
			   <isNotEmpty property="updateDate">#updateDate#</isNotEmpty>
			   <isEmpty property="updateDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">#deleteUserLabel#</isNotNull>
			<isNotNull prepend=", " property="deleteDate">
			   <isNotEmpty property="deleteDate">#deleteDate#</isNotEmpty>
			   <isEmpty property="deleteDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="recordVersion">#recordVersion#</isNotNull>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_TJDATA
		WHERE 		TJDATA_ID=#value# 	
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_TJDATA
		WHERE 
		<dynamic prepend=" ">
			<isNotNull prepend=" AND " property="tjdataId">TJDATA_ID = #tjdataId#</isNotNull>
			<isNotNull prepend=" AND " property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
			<isNotNull prepend=" AND " property="deptName">DEPT_NAME = #deptName#</isNotNull>
			<isNotNull prepend=" AND " property="patentType">PATENT_TYPE = #patentType#</isNotNull>
			<isNotNull prepend=" AND " property="moneyName">MONEY_NAME = #moneyName#</isNotNull>
			<isNotNull prepend=" AND " property="ny">NY = #ny#</isNotNull>
			<isNotNull prepend=" AND " property="sl">SL = #sl#</isNotNull>
			<isNotNull prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>	
	</delete>

	<update id="update" parameterClass="tKIZLMaintainTjdata">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_TJDATA	
		SET 
		<dynamic prepend=" ">
		<isNotNull prepend="," property="tjdataId">TJDATA_ID = #tjdataId#</isNotNull>
		<isNotNull prepend="," property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
		<isNotNull prepend="," property="deptName">DEPT_NAME = #deptName#</isNotNull>
		<isNotNull prepend="," property="patentType">PATENT_TYPE = #patentType#</isNotNull>
		<isNotNull prepend="," property="moneyName">MONEY_NAME = #moneyName#</isNotNull>
		<isNotNull prepend="," property="ny">NY = #ny#</isNotNull>
		<isNotNull prepend="," property="sl">SL = #sl#</isNotNull>
		<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
		<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
		<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
		<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
		<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
		<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
		<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
		<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
		<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
		<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
		<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		TJDATA_ID=#tjdataId# 			</update>
	
	<update id="updatewithnull" parameterClass="tKIZLMaintainTjdata">
		UPDATE ${zzzcSchema}.T_KIZL_MAINTAIN_TJDATA	
		SET 
		<dynamic prepend=" ">
	    <isNotNull prepend=", " property="tjdataId">TJDATA_ID = #tjdataId#</isNotNull>
	    <isNull prepend=", " property="tjdataId">TJDATA_ID = NULL</isNull>
	    <isNotNull prepend=", " property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
	    <isNull prepend=", " property="deptCode">DEPT_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="deptName">DEPT_NAME = #deptName#</isNotNull>
	    <isNull prepend=", " property="deptName">DEPT_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="patentType">PATENT_TYPE = #patentType#</isNotNull>
	    <isNull prepend=", " property="patentType">PATENT_TYPE = NULL</isNull>
	    <isNotNull prepend=", " property="moneyName">MONEY_NAME = #moneyName#</isNotNull>
	    <isNull prepend=", " property="moneyName">MONEY_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="ny">NY = #ny#</isNotNull>
	    <isNull prepend=", " property="ny">NY = NULL</isNull>
	    <isNotNull prepend=", " property="sl">SL = #sl#</isNotNull>
	    <isNull prepend=", " property="sl">SL = NULL</isNull>
	    <isNotNull prepend=", " property="extra1">EXTRA1 = #extra1#</isNotNull>
	    <isNull prepend=", " property="extra1">EXTRA1 = NULL</isNull>
	    <isNotNull prepend=", " property="extra2">EXTRA2 = #extra2#</isNotNull>
	    <isNull prepend=", " property="extra2">EXTRA2 = NULL</isNull>
	    <isNotNull prepend=", " property="extra3">EXTRA3 = #extra3#</isNotNull>
	    <isNull prepend=", " property="extra3">EXTRA3 = NULL</isNull>
	    <isNotNull prepend=", " property="extra4">EXTRA4 = #extra4#</isNotNull>
	    <isNull prepend=", " property="extra4">EXTRA4 = NULL</isNull>
	    <isNotNull prepend=", " property="extra5">EXTRA5 = #extra5#</isNotNull>
	    <isNull prepend=", " property="extra5">EXTRA5 = NULL</isNull>
	    <isNotNull prepend=", " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
	    <isNull prepend=", " property="delStatus">DEL_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
	    <isNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="createDate">CREATE_DATE = #createDate#</isNotNull>
	    <isNull prepend=", " property="createDate">CREATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
	    <isNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNull prepend=", " property="updateDate">UPDATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
	    <isNull prepend=", " property="recordVersion">RECORD_VERSION = NULL</isNull>
		</dynamic>
		WHERE 		TJDATA_ID=#tjdataId# 			</update>
	
	<update id="updateByC" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_TJDATA	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="tjdataId">TJDATA_ID = #tjdataId#</isNotNull>
				<isNotNull prepend="," property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
				<isNotNull prepend="," property="deptName">DEPT_NAME = #deptName#</isNotNull>
				<isNotNull prepend="," property="patentType">PATENT_TYPE = #patentType#</isNotNull>
				<isNotNull prepend="," property="moneyName">MONEY_NAME = #moneyName#</isNotNull>
				<isNotNull prepend="," property="ny">NY = #ny#</isNotNull>
				<isNotNull prepend="," property="sl">SL = #sl#</isNotNull>
				<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
				<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
				<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
				<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
				<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
				<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
				<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
				<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
				<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
				<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
				<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
			</dynamic>
		<dynamic prepend=" WHERE ">
		 			<isNotNull prepend=" AND " property="tjdataIdOld">TJDATA_ID = #tjdataIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="deptCodeOld">DEPT_CODE = #deptCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="deptNameOld">DEPT_NAME = #deptNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="patentTypeOld">PATENT_TYPE = #patentTypeOld#</isNotNull>
			<isNotNull prepend=" AND " property="moneyNameOld">MONEY_NAME = #moneyNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="nyOld">NY = #nyOld#</isNotNull>
			<isNotNull prepend=" AND " property="slOld">SL = #slOld#</isNotNull>
			<isNotNull prepend=" AND " property="extra1Old">EXTRA1 = #extra1Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra2Old">EXTRA2 = #extra2Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra3Old">EXTRA3 = #extra3Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra4Old">EXTRA4 = #extra4Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra5Old">EXTRA5 = #extra5Old#</isNotNull>
			<isNotNull prepend=" AND " property="delStatusOld">DEL_STATUS = #delStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabelOld">CREATE_USER_LABEL = #createUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="createDateOld">CREATE_DATE = #createDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabelOld">UPDATE_USER_LABEL = #updateUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateDateOld">UPDATE_DATE = #updateDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabelOld">DELETE_USER_LABEL = #deleteUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDateOld">DELETE_DATE = #deleteDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersionOld">RECORD_VERSION = #recordVersionOld#</isNotNull>
			<isNotNull prepend=" AND " property="dynSqlOld"> $dynSqlOld$ </isNotNull>
		</dynamic>
	</update>
	
	<update id="updateNull" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_TJDATA	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="tjdataId">TJDATA_ID = #tjdataId#</isNotNull>
			<isNotNull prepend="," property="deptCode">DEPT_CODE = #deptCode#</isNotNull>
			<isNotNull prepend="," property="deptName">DEPT_NAME = #deptName#</isNotNull>
			<isNotNull prepend="," property="patentType">PATENT_TYPE = #patentType#</isNotNull>
			<isNotNull prepend="," property="moneyName">MONEY_NAME = #moneyName#</isNotNull>
			<isNotNull prepend="," property="ny">NY = #ny#</isNotNull>
			<isNotNull prepend="," property="sl">SL = #sl#</isNotNull>
			<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			<isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
			<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		TJDATA_ID=#tjdataId# 			</update>	
</sqlMap>