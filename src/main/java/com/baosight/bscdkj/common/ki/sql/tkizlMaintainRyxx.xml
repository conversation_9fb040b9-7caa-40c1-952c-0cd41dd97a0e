<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkizlMaintainRyxx">
	<typeAlias alias="tKIZLMaintainRyxx" type="com.baosight.bscdkj.common.ki.domain.TkizlMaintainRyxx"/>
	<select id="load" parameterClass="string" resultClass="tKIZLMaintainRyxx">
		SELECT 
				RYXX_ID as "ryxxId" ,		
				PROJECT_NAME as "projectName" ,		
				JX_NAME as "jxName" ,		
				ZS_CODE as "zsCode" ,		
				JX_LEVEL as "jxLevel" ,		
				START_DATE as "startDate" ,		
				END_DAT<PERSON> as "endDate" ,		
				<PERSON><PERSON><PERSON><PERSON> as "extra1" ,		
				<PERSON><PERSON><PERSON><PERSON> as "extra2" ,		
				<PERSON><PERSON><PERSON><PERSON> as "extra3" ,		
				<PERSON><PERSON><PERSON><PERSON> as "extra4" ,		
				EXT<PERSON><PERSON> as "extra5" ,		
				<PERSON>L_STATUS as "delStatus" ,		
				CREATE_USER_LABEL as "createUserLabel" ,		
				CREATE_DATE as "createDate" ,		
				UPDATE_USER_LABEL as "updateUserLabel" ,		
				UPDATE_DATE as "updateDate" ,		
				DELETE_USER_LABEL as "deleteUserLabel" ,		
				DELETE_DATE as "deleteDate" ,		
				RECORD_VERSION as "recordVersion" 		
				FROM ${zzzcSchema}.T_KIZL_MAINTAIN_RYXX
		WHERE   RYXX_ID=#value# 				
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tKIZLMaintainRyxx">
		SELECT
				RYXX_ID  as "ryxxId" ,		
				PROJECT_NAME  as "projectName" ,		
				JX_NAME  as "jxName" ,		
				ZS_CODE  as "zsCode" ,		
				JX_LEVEL  as "jxLevel" ,		
				START_DATE  as "startDate" ,		
				END_DATE  as "endDate" ,		
				EXTRA1  as "extra1" ,		
				EXTRA2  as "extra2" ,		
				EXTRA3  as "extra3" ,		
				EXTRA4  as "extra4" ,		
				EXTRA5  as "extra5" ,		
				DEL_STATUS  as "delStatus" ,		
				CREATE_USER_LABEL  as "createUserLabel" ,		
				CREATE_DATE  as "createDate" ,		
				UPDATE_USER_LABEL  as "updateUserLabel" ,		
				UPDATE_DATE  as "updateDate" ,		
				DELETE_USER_LABEL  as "deleteUserLabel" ,		
				DELETE_DATE  as "deleteDate" ,		
				RECORD_VERSION  as "recordVersion" 		
				FROM ${zzzcSchema}.T_KIZL_MAINTAIN_RYXX
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="ryxxId">RYXX_ID =  #ryxxId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectName">PROJECT_NAME =  #projectName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jxName">JX_NAME =  #jxName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zsCode">ZS_CODE =  #zsCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="jxLevel">JX_LEVEL =  #jxLevel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="startDate">START_DATE =  #startDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="endDate">END_DATE =  #endDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KIZL_MAINTAIN_RYXX 
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="ryxxId">RYXX_ID =  #ryxxId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">PROJECT_NAME =  #projectName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jxName">JX_NAME =  #jxName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="zsCode">ZS_CODE =  #zsCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jxLevel">JX_LEVEL =  #jxLevel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="startDate">START_DATE =  #startDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="endDate">END_DATE =  #endDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tKIZLMaintainRyxx">
		INSERT INTO ${zzzcSchema}.T_KIZL_MAINTAIN_RYXX ( 
		<dynamic prepend=" ">
					<isNotNull prepend=", " property="ryxxId">RYXX_ID </isNotNull>
					<isNotNull prepend=", " property="projectName">PROJECT_NAME </isNotNull>
					<isNotNull prepend=", " property="jxName">JX_NAME </isNotNull>
					<isNotNull prepend=", " property="zsCode">ZS_CODE </isNotNull>
					<isNotNull prepend=", " property="jxLevel">JX_LEVEL </isNotNull>
					<isNotNull prepend=", " property="startDate">START_DATE </isNotNull>
					<isNotNull prepend=", " property="endDate">END_DATE </isNotNull>
					<isNotNull prepend=", " property="extra1">EXTRA1 </isNotNull>
					<isNotNull prepend=", " property="extra2">EXTRA2 </isNotNull>
					<isNotNull prepend=", " property="extra3">EXTRA3 </isNotNull>
					<isNotNull prepend=", " property="extra4">EXTRA4 </isNotNull>
					<isNotNull prepend=", " property="extra5">EXTRA5 </isNotNull>
					<isNotNull prepend=", " property="delStatus">DEL_STATUS </isNotNull>
					<isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="createDate">CREATE_DATE </isNotNull>
					<isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="updateDate">UPDATE_DATE </isNotNull>
					<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="deleteDate">DELETE_DATE </isNotNull>
					<isNotNull prepend=", " property="recordVersion">RECORD_VERSION </isNotNull>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotNull prepend=", " property="ryxxId">#ryxxId#</isNotNull>
			<isNotNull prepend=", " property="projectName">#projectName#</isNotNull>
			<isNotNull prepend=", " property="jxName">#jxName#</isNotNull>
			<isNotNull prepend=", " property="zsCode">#zsCode#</isNotNull>
			<isNotNull prepend=", " property="jxLevel">#jxLevel#</isNotNull>
			<isNotNull prepend=", " property="startDate">#startDate#</isNotNull>
			<isNotNull prepend=", " property="endDate">#endDate#</isNotNull>
			<isNotNull prepend=", " property="extra1">#extra1#</isNotNull>
			<isNotNull prepend=", " property="extra2">#extra2#</isNotNull>
			<isNotNull prepend=", " property="extra3">#extra3#</isNotNull>
			<isNotNull prepend=", " property="extra4">#extra4#</isNotNull>
			<isNotNull prepend=", " property="extra5">#extra5#</isNotNull>
			<isNotNull prepend=", " property="delStatus">#delStatus#</isNotNull>
			<isNotNull prepend=", " property="createUserLabel">#createUserLabel#</isNotNull>
			<isNotNull prepend=", " property="createDate">
			   <isNotEmpty property="createDate">#createDate#</isNotEmpty>
			   <isEmpty property="createDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="updateUserLabel">#updateUserLabel#</isNotNull>
			<isNotNull prepend=", " property="updateDate">
			   <isNotEmpty property="updateDate">#updateDate#</isNotEmpty>
			   <isEmpty property="updateDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">#deleteUserLabel#</isNotNull>
			<isNotNull prepend=", " property="deleteDate">
			   <isNotEmpty property="deleteDate">#deleteDate#</isNotEmpty>
			   <isEmpty property="deleteDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="recordVersion">#recordVersion#</isNotNull>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_RYXX
		WHERE 		RYXX_ID=#value# 	
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KIZL_MAINTAIN_RYXX
		WHERE 
		<dynamic prepend=" ">
			<isNotNull prepend=" AND " property="ryxxId">RYXX_ID = #ryxxId#</isNotNull>
			<isNotNull prepend=" AND " property="projectName">PROJECT_NAME = #projectName#</isNotNull>
			<isNotNull prepend=" AND " property="jxName">JX_NAME = #jxName#</isNotNull>
			<isNotNull prepend=" AND " property="zsCode">ZS_CODE = #zsCode#</isNotNull>
			<isNotNull prepend=" AND " property="jxLevel">JX_LEVEL = #jxLevel#</isNotNull>
			<isNotNull prepend=" AND " property="startDate">START_DATE = #startDate#</isNotNull>
			<isNotNull prepend=" AND " property="endDate">END_DATE = #endDate#</isNotNull>
			<isNotNull prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>	
	</delete>

	<update id="update" parameterClass="tKIZLMaintainRyxx">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_RYXX	
		SET 
		<dynamic prepend=" ">
		<isNotNull prepend="," property="ryxxId">RYXX_ID = #ryxxId#</isNotNull>
		<isNotNull prepend="," property="projectName">PROJECT_NAME = #projectName#</isNotNull>
		<isNotNull prepend="," property="jxName">JX_NAME = #jxName#</isNotNull>
		<isNotNull prepend="," property="zsCode">ZS_CODE = #zsCode#</isNotNull>
		<isNotNull prepend="," property="jxLevel">JX_LEVEL = #jxLevel#</isNotNull>
		<isNotNull prepend="," property="startDate">START_DATE = #startDate#</isNotNull>
		<isNotNull prepend="," property="endDate">END_DATE = #endDate#</isNotNull>
		<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
		<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
		<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
		<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
		<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
		<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
		<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
		<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
		<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
		<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
		<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		RYXX_ID=#ryxxId# 			</update>
	
	<update id="updatewithnull" parameterClass="tKIZLMaintainRyxx">
		UPDATE ${zzzcSchema}.T_KIZL_MAINTAIN_RYXX	
		SET 
		<dynamic prepend=" ">
	    <isNotNull prepend=", " property="ryxxId">RYXX_ID = #ryxxId#</isNotNull>
	    <isNull prepend=", " property="ryxxId">RYXX_ID = NULL</isNull>
	    <isNotNull prepend=", " property="projectName">PROJECT_NAME = #projectName#</isNotNull>
	    <isNull prepend=", " property="projectName">PROJECT_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="jxName">JX_NAME = #jxName#</isNotNull>
	    <isNull prepend=", " property="jxName">JX_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="zsCode">ZS_CODE = #zsCode#</isNotNull>
	    <isNull prepend=", " property="zsCode">ZS_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="jxLevel">JX_LEVEL = #jxLevel#</isNotNull>
	    <isNull prepend=", " property="jxLevel">JX_LEVEL = NULL</isNull>
	    <isNotNull prepend=", " property="startDate">START_DATE = #startDate#</isNotNull>
	    <isNull prepend=", " property="startDate">START_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="endDate">END_DATE = #endDate#</isNotNull>
	    <isNull prepend=", " property="endDate">END_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="extra1">EXTRA1 = #extra1#</isNotNull>
	    <isNull prepend=", " property="extra1">EXTRA1 = NULL</isNull>
	    <isNotNull prepend=", " property="extra2">EXTRA2 = #extra2#</isNotNull>
	    <isNull prepend=", " property="extra2">EXTRA2 = NULL</isNull>
	    <isNotNull prepend=", " property="extra3">EXTRA3 = #extra3#</isNotNull>
	    <isNull prepend=", " property="extra3">EXTRA3 = NULL</isNull>
	    <isNotNull prepend=", " property="extra4">EXTRA4 = #extra4#</isNotNull>
	    <isNull prepend=", " property="extra4">EXTRA4 = NULL</isNull>
	    <isNotNull prepend=", " property="extra5">EXTRA5 = #extra5#</isNotNull>
	    <isNull prepend=", " property="extra5">EXTRA5 = NULL</isNull>
	    <isNotNull prepend=", " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
	    <isNull prepend=", " property="delStatus">DEL_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
	    <isNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="createDate">CREATE_DATE = #createDate#</isNotNull>
	    <isNull prepend=", " property="createDate">CREATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
	    <isNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNull prepend=", " property="updateDate">UPDATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
	    <isNull prepend=", " property="recordVersion">RECORD_VERSION = NULL</isNull>
		</dynamic>
		WHERE 		RYXX_ID=#ryxxId# 			</update>
	
	<update id="updateByC" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_RYXX	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="ryxxId">RYXX_ID = #ryxxId#</isNotNull>
				<isNotNull prepend="," property="projectName">PROJECT_NAME = #projectName#</isNotNull>
				<isNotNull prepend="," property="jxName">JX_NAME = #jxName#</isNotNull>
				<isNotNull prepend="," property="zsCode">ZS_CODE = #zsCode#</isNotNull>
				<isNotNull prepend="," property="jxLevel">JX_LEVEL = #jxLevel#</isNotNull>
				<isNotNull prepend="," property="startDate">START_DATE = #startDate#</isNotNull>
				<isNotNull prepend="," property="endDate">END_DATE = #endDate#</isNotNull>
				<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
				<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
				<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
				<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
				<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
				<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
				<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
				<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
				<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
				<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
				<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
			</dynamic>
		<dynamic prepend=" WHERE ">
		 			<isNotNull prepend=" AND " property="ryxxIdOld">RYXX_ID = #ryxxIdOld#</isNotNull>
			<isNotNull prepend=" AND " property="projectNameOld">PROJECT_NAME = #projectNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="jxNameOld">JX_NAME = #jxNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="zsCodeOld">ZS_CODE = #zsCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="jxLevelOld">JX_LEVEL = #jxLevelOld#</isNotNull>
			<isNotNull prepend=" AND " property="startDateOld">START_DATE = #startDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="endDateOld">END_DATE = #endDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="extra1Old">EXTRA1 = #extra1Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra2Old">EXTRA2 = #extra2Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra3Old">EXTRA3 = #extra3Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra4Old">EXTRA4 = #extra4Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra5Old">EXTRA5 = #extra5Old#</isNotNull>
			<isNotNull prepend=" AND " property="delStatusOld">DEL_STATUS = #delStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabelOld">CREATE_USER_LABEL = #createUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="createDateOld">CREATE_DATE = #createDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabelOld">UPDATE_USER_LABEL = #updateUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateDateOld">UPDATE_DATE = #updateDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabelOld">DELETE_USER_LABEL = #deleteUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDateOld">DELETE_DATE = #deleteDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersionOld">RECORD_VERSION = #recordVersionOld#</isNotNull>
			<isNotNull prepend=" AND " property="dynSqlOld"> $dynSqlOld$ </isNotNull>
		</dynamic>
	</update>
	
	<update id="updateNull" parameterClass="hashmap">
		UPDATE  ${zzzcSchema}.T_KIZL_MAINTAIN_RYXX	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="ryxxId">RYXX_ID = #ryxxId#</isNotNull>
			<isNotNull prepend="," property="projectName">PROJECT_NAME = #projectName#</isNotNull>
			<isNotNull prepend="," property="jxName">JX_NAME = #jxName#</isNotNull>
			<isNotNull prepend="," property="zsCode">ZS_CODE = #zsCode#</isNotNull>
			<isNotNull prepend="," property="jxLevel">JX_LEVEL = #jxLevel#</isNotNull>
			<isNotNull prepend="," property="startDate">START_DATE = #startDate#</isNotNull>
			<isNotNull prepend="," property="endDate">END_DATE = #endDate#</isNotNull>
			<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			<isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
			<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		RYXX_ID=#ryxxId# 			</update>	
</sqlMap>