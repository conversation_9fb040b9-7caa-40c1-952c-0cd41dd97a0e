<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkyxmDiProjectLog">
	<typeAlias alias="tKYXMDiProjectLog" type="com.baosight.bscdkj.common.ky.domain.TkyxmDiProjectLog"/>
	<select id="load" parameterClass="string" resultClass="tKYXMDiProjectLog">
		SELECT 
				RECORD_GUID as "recordGuid" ,		
				FUNCTION_CODE as "functionCode" ,		
				PROJECT_ITEM as "projectItem" ,		
				PROJECT_NAME as "projectName" ,		
				COMPANY_CODE as "companyCode" ,		
				COST_CENTER as "costCenter" ,		
				CHECK_TYPE as "checkType" ,		
				CHECKTYPE1 as "checktype1" ,		
				CHECKTYPE2 as "checktype2" ,		
				CHECKTYPE3 as "checktype3" ,		
				CHEC<PERSON><PERSON>PE4 as "checktype4" ,		
				CHECK<PERSON>PE5 as "checktype5" ,		
				SEND_STATUS as "sendStatus" ,		
				SEND_TIME as "sendTime" ,		
				SOURCE_LABEL as "sourceLabel" ,		
				EXTRA1 as "extra1" ,		
				EXTRA2 as "extra2" ,		
				EXTRA3 as "extra3" ,		
				EXTRA4 as "extra4" ,		
				EXTRA5 as "extra5" ,		
				EXTRA6 as "extra6" ,		
				EXTRA7 as "extra7" ,		
				EXTRA8 as "extra8" ,		
				EXTRA9 as "extra9" ,		
				EXTRA10 as "extra10" ,		
				DEL_STATUS as "delStatus" ,		
				CREATE_USER_LABEL as "createUserLabel" ,		
				CREATE_DATE as "createDate" ,		
				UPDATE_USER_LABEL as "updateUserLabel" ,		
				UPDATE_DATE as "updateDate" ,		
				DELETE_USER_LABEL as "deleteUserLabel" ,		
				DELETE_DATE as "deleteDate" ,		
				RECORD_VERSION as "recordVersion" 		
				FROM ${kjglSchema}.T_KYXM_DI_PROJECT_LOG
		WHERE   RECORD_GUID=#value# 				
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tKYXMDiProjectLog">
		SELECT
				RECORD_GUID  as "recordGuid" ,		
				FUNCTION_CODE  as "functionCode" ,		
				PROJECT_ITEM  as "projectItem" ,		
				PROJECT_NAME  as "projectName" ,		
				COMPANY_CODE  as "companyCode" ,		
				COST_CENTER  as "costCenter" ,		
				CHECK_TYPE  as "checkType" ,		
				CHECKTYPE1  as "checktype1" ,		
				CHECKTYPE2  as "checktype2" ,		
				CHECKTYPE3  as "checktype3" ,		
				CHECKTYPE4  as "checktype4" ,		
				CHECKTYPE5  as "checktype5" ,		
				SEND_STATUS  as "sendStatus" ,		
				SEND_TIME  as "sendTime" ,		
				SOURCE_LABEL  as "sourceLabel" ,		
				EXTRA1  as "extra1" ,		
				EXTRA2  as "extra2" ,		
				EXTRA3  as "extra3" ,		
				EXTRA4  as "extra4" ,		
				EXTRA5  as "extra5" ,		
				EXTRA6  as "extra6" ,		
				EXTRA7  as "extra7" ,		
				EXTRA8  as "extra8" ,		
				EXTRA9  as "extra9" ,		
				EXTRA10  as "extra10" ,		
				DEL_STATUS  as "delStatus" ,		
				CREATE_USER_LABEL  as "createUserLabel" ,		
				CREATE_DATE  as "createDate" ,		
				UPDATE_USER_LABEL  as "updateUserLabel" ,		
				UPDATE_DATE  as "updateDate" ,		
				DELETE_USER_LABEL  as "deleteUserLabel" ,		
				DELETE_DATE  as "deleteDate" ,		
				RECORD_VERSION  as "recordVersion" 		
				FROM ${kjglSchema}.T_KYXM_DI_PROJECT_LOG
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="recordGuid">RECORD_GUID =  #recordGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="functionCode">FUNCTION_CODE =  #functionCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectItem">PROJECT_ITEM =  #projectItem#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectName">PROJECT_NAME =  #projectName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="companyCode">COMPANY_CODE =  #companyCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="costCenter">COST_CENTER =  #costCenter#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="checkType">CHECK_TYPE =  #checkType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="checktype1">CHECKTYPE1 =  #checktype1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="checktype2">CHECKTYPE2 =  #checktype2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="checktype3">CHECKTYPE3 =  #checktype3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="checktype4">CHECKTYPE4 =  #checktype4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="checktype5">CHECKTYPE5 =  #checktype5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sendStatus">SEND_STATUS =  #sendStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sendTime">SEND_TIME =  #sendTime#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceLabel">SOURCE_LABEL =  #sourceLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra6">EXTRA6 =  #extra6#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra7">EXTRA7 =  #extra7#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra8">EXTRA8 =  #extra8#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra9">EXTRA9 =  #extra9#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra10">EXTRA10 =  #extra10#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${kjglSchema}.T_KYXM_DI_PROJECT_LOG 
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="recordGuid">RECORD_GUID =  #recordGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="functionCode">FUNCTION_CODE =  #functionCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectItem">PROJECT_ITEM =  #projectItem#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">PROJECT_NAME =  #projectName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="companyCode">COMPANY_CODE =  #companyCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="costCenter">COST_CENTER =  #costCenter#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="checkType">CHECK_TYPE =  #checkType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="checktype1">CHECKTYPE1 =  #checktype1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="checktype2">CHECKTYPE2 =  #checktype2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="checktype3">CHECKTYPE3 =  #checktype3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="checktype4">CHECKTYPE4 =  #checktype4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="checktype5">CHECKTYPE5 =  #checktype5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sendStatus">SEND_STATUS =  #sendStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sendTime">SEND_TIME =  #sendTime#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sourceLabel">SOURCE_LABEL =  #sourceLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra6">EXTRA6 =  #extra6#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra7">EXTRA7 =  #extra7#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra8">EXTRA8 =  #extra8#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra9">EXTRA9 =  #extra9#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra10">EXTRA10 =  #extra10#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tKYXMDiProjectLog">
		INSERT INTO ${kjglSchema}.T_KYXM_DI_PROJECT_LOG ( 
		<dynamic prepend=" ">
					<isNotNull prepend=", " property="recordGuid">RECORD_GUID </isNotNull>
					<isNotNull prepend=", " property="functionCode">FUNCTION_CODE </isNotNull>
					<isNotNull prepend=", " property="projectItem">PROJECT_ITEM </isNotNull>
					<isNotNull prepend=", " property="projectName">PROJECT_NAME </isNotNull>
					<isNotNull prepend=", " property="companyCode">COMPANY_CODE </isNotNull>
					<isNotNull prepend=", " property="costCenter">COST_CENTER </isNotNull>
					<isNotNull prepend=", " property="checkType">CHECK_TYPE </isNotNull>
					<isNotNull prepend=", " property="checktype1">CHECKTYPE1 </isNotNull>
					<isNotNull prepend=", " property="checktype2">CHECKTYPE2 </isNotNull>
					<isNotNull prepend=", " property="checktype3">CHECKTYPE3 </isNotNull>
					<isNotNull prepend=", " property="checktype4">CHECKTYPE4 </isNotNull>
					<isNotNull prepend=", " property="checktype5">CHECKTYPE5 </isNotNull>
					<isNotNull prepend=", " property="sendStatus">SEND_STATUS </isNotNull>
					<isNotNull prepend=", " property="sendTime">SEND_TIME </isNotNull>
					<isNotNull prepend=", " property="sourceLabel">SOURCE_LABEL </isNotNull>
					<isNotNull prepend=", " property="extra1">EXTRA1 </isNotNull>
					<isNotNull prepend=", " property="extra2">EXTRA2 </isNotNull>
					<isNotNull prepend=", " property="extra3">EXTRA3 </isNotNull>
					<isNotNull prepend=", " property="extra4">EXTRA4 </isNotNull>
					<isNotNull prepend=", " property="extra5">EXTRA5 </isNotNull>
					<isNotNull prepend=", " property="extra6">EXTRA6 </isNotNull>
					<isNotNull prepend=", " property="extra7">EXTRA7 </isNotNull>
					<isNotNull prepend=", " property="extra8">EXTRA8 </isNotNull>
					<isNotNull prepend=", " property="extra9">EXTRA9 </isNotNull>
					<isNotNull prepend=", " property="extra10">EXTRA10 </isNotNull>
					<isNotNull prepend=", " property="delStatus">DEL_STATUS </isNotNull>
					<isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="createDate">CREATE_DATE </isNotNull>
					<isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="updateDate">UPDATE_DATE </isNotNull>
					<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="deleteDate">DELETE_DATE </isNotNull>
					<isNotNull prepend=", " property="recordVersion">RECORD_VERSION </isNotNull>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotNull prepend=", " property="recordGuid">#recordGuid#</isNotNull>
			<isNotNull prepend=", " property="functionCode">#functionCode#</isNotNull>
			<isNotNull prepend=", " property="projectItem">#projectItem#</isNotNull>
			<isNotNull prepend=", " property="projectName">#projectName#</isNotNull>
			<isNotNull prepend=", " property="companyCode">#companyCode#</isNotNull>
			<isNotNull prepend=", " property="costCenter">#costCenter#</isNotNull>
			<isNotNull prepend=", " property="checkType">#checkType#</isNotNull>
			<isNotNull prepend=", " property="checktype1">#checktype1#</isNotNull>
			<isNotNull prepend=", " property="checktype2">#checktype2#</isNotNull>
			<isNotNull prepend=", " property="checktype3">#checktype3#</isNotNull>
			<isNotNull prepend=", " property="checktype4">#checktype4#</isNotNull>
			<isNotNull prepend=", " property="checktype5">#checktype5#</isNotNull>
			<isNotNull prepend=", " property="sendStatus">#sendStatus#</isNotNull>
			<isNotNull prepend=", " property="sendTime">#sendTime#</isNotNull>
			<isNotNull prepend=", " property="sourceLabel">#sourceLabel#</isNotNull>
			<isNotNull prepend=", " property="extra1">#extra1#</isNotNull>
			<isNotNull prepend=", " property="extra2">#extra2#</isNotNull>
			<isNotNull prepend=", " property="extra3">#extra3#</isNotNull>
			<isNotNull prepend=", " property="extra4">#extra4#</isNotNull>
			<isNotNull prepend=", " property="extra5">#extra5#</isNotNull>
			<isNotNull prepend=", " property="extra6">#extra6#</isNotNull>
			<isNotNull prepend=", " property="extra7">#extra7#</isNotNull>
			<isNotNull prepend=", " property="extra8">#extra8#</isNotNull>
			<isNotNull prepend=", " property="extra9">#extra9#</isNotNull>
			<isNotNull prepend=", " property="extra10">#extra10#</isNotNull>
			<isNotNull prepend=", " property="delStatus">#delStatus#</isNotNull>
			<isNotNull prepend=", " property="createUserLabel">#createUserLabel#</isNotNull>
			<isNotNull prepend=", " property="createDate">
			   <isNotEmpty property="createDate">#createDate#</isNotEmpty>
			   <isEmpty property="createDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="updateUserLabel">#updateUserLabel#</isNotNull>
			<isNotNull prepend=", " property="updateDate">
			   <isNotEmpty property="updateDate">#updateDate#</isNotEmpty>
			   <isEmpty property="updateDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">#deleteUserLabel#</isNotNull>
			<isNotNull prepend=", " property="deleteDate">
			   <isNotEmpty property="deleteDate">#deleteDate#</isNotEmpty>
			   <isEmpty property="deleteDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="recordVersion">#recordVersion#</isNotNull>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${kjglSchema}.T_KYXM_DI_PROJECT_LOG
		WHERE 		RECORD_GUID=#value# 	
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${kjglSchema}.T_KYXM_DI_PROJECT_LOG
		WHERE 
		<dynamic prepend=" ">
			<isNotNull prepend=" AND " property="recordGuid">RECORD_GUID = #recordGuid#</isNotNull>
			<isNotNull prepend=" AND " property="functionCode">FUNCTION_CODE = #functionCode#</isNotNull>
			<isNotNull prepend=" AND " property="projectItem">PROJECT_ITEM = #projectItem#</isNotNull>
			<isNotNull prepend=" AND " property="projectName">PROJECT_NAME = #projectName#</isNotNull>
			<isNotNull prepend=" AND " property="companyCode">COMPANY_CODE = #companyCode#</isNotNull>
			<isNotNull prepend=" AND " property="costCenter">COST_CENTER = #costCenter#</isNotNull>
			<isNotNull prepend=" AND " property="checkType">CHECK_TYPE = #checkType#</isNotNull>
			<isNotNull prepend=" AND " property="checktype1">CHECKTYPE1 = #checktype1#</isNotNull>
			<isNotNull prepend=" AND " property="checktype2">CHECKTYPE2 = #checktype2#</isNotNull>
			<isNotNull prepend=" AND " property="checktype3">CHECKTYPE3 = #checktype3#</isNotNull>
			<isNotNull prepend=" AND " property="checktype4">CHECKTYPE4 = #checktype4#</isNotNull>
			<isNotNull prepend=" AND " property="checktype5">CHECKTYPE5 = #checktype5#</isNotNull>
			<isNotNull prepend=" AND " property="sendStatus">SEND_STATUS = #sendStatus#</isNotNull>
			<isNotNull prepend=" AND " property="sendTime">SEND_TIME = #sendTime#</isNotNull>
			<isNotNull prepend=" AND " property="sourceLabel">SOURCE_LABEL = #sourceLabel#</isNotNull>
			<isNotNull prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend=" AND " property="extra6">EXTRA6 = #extra6#</isNotNull>
			<isNotNull prepend=" AND " property="extra7">EXTRA7 = #extra7#</isNotNull>
			<isNotNull prepend=" AND " property="extra8">EXTRA8 = #extra8#</isNotNull>
			<isNotNull prepend=" AND " property="extra9">EXTRA9 = #extra9#</isNotNull>
			<isNotNull prepend=" AND " property="extra10">EXTRA10 = #extra10#</isNotNull>
			<isNotNull prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>	
	</delete>

	<update id="update" parameterClass="tKYXMDiProjectLog">
		UPDATE  ${kjglSchema}.T_KYXM_DI_PROJECT_LOG	
		SET 
		<dynamic prepend=" ">
		<isNotNull prepend="," property="recordGuid">RECORD_GUID = #recordGuid#</isNotNull>
		<isNotNull prepend="," property="functionCode">FUNCTION_CODE = #functionCode#</isNotNull>
		<isNotNull prepend="," property="projectItem">PROJECT_ITEM = #projectItem#</isNotNull>
		<isNotNull prepend="," property="projectName">PROJECT_NAME = #projectName#</isNotNull>
		<isNotNull prepend="," property="companyCode">COMPANY_CODE = #companyCode#</isNotNull>
		<isNotNull prepend="," property="costCenter">COST_CENTER = #costCenter#</isNotNull>
		<isNotNull prepend="," property="checkType">CHECK_TYPE = #checkType#</isNotNull>
		<isNotNull prepend="," property="checktype1">CHECKTYPE1 = #checktype1#</isNotNull>
		<isNotNull prepend="," property="checktype2">CHECKTYPE2 = #checktype2#</isNotNull>
		<isNotNull prepend="," property="checktype3">CHECKTYPE3 = #checktype3#</isNotNull>
		<isNotNull prepend="," property="checktype4">CHECKTYPE4 = #checktype4#</isNotNull>
		<isNotNull prepend="," property="checktype5">CHECKTYPE5 = #checktype5#</isNotNull>
		<isNotNull prepend="," property="sendStatus">SEND_STATUS = #sendStatus#</isNotNull>
		<isNotNull prepend="," property="sendTime">SEND_TIME = #sendTime#</isNotNull>
		<isNotNull prepend="," property="sourceLabel">SOURCE_LABEL = #sourceLabel#</isNotNull>
		<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
		<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
		<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
		<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
		<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
		<isNotNull prepend="," property="extra6">EXTRA6 = #extra6#</isNotNull>
		<isNotNull prepend="," property="extra7">EXTRA7 = #extra7#</isNotNull>
		<isNotNull prepend="," property="extra8">EXTRA8 = #extra8#</isNotNull>
		<isNotNull prepend="," property="extra9">EXTRA9 = #extra9#</isNotNull>
		<isNotNull prepend="," property="extra10">EXTRA10 = #extra10#</isNotNull>
		<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
		<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
		<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
		<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
		<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
		<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		RECORD_GUID=#recordGuid# 			</update>
	
	<update id="updatewithnull" parameterClass="tKYXMDiProjectLog">
		UPDATE ${kjglSchema}.T_KYXM_DI_PROJECT_LOG	
		SET 
		<dynamic prepend=" ">
	    <isNotNull prepend=", " property="recordGuid">RECORD_GUID = #recordGuid#</isNotNull>
	    <isNull prepend=", " property="recordGuid">RECORD_GUID = NULL</isNull>
	    <isNotNull prepend=", " property="functionCode">FUNCTION_CODE = #functionCode#</isNotNull>
	    <isNull prepend=", " property="functionCode">FUNCTION_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="projectItem">PROJECT_ITEM = #projectItem#</isNotNull>
	    <isNull prepend=", " property="projectItem">PROJECT_ITEM = NULL</isNull>
	    <isNotNull prepend=", " property="projectName">PROJECT_NAME = #projectName#</isNotNull>
	    <isNull prepend=", " property="projectName">PROJECT_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="companyCode">COMPANY_CODE = #companyCode#</isNotNull>
	    <isNull prepend=", " property="companyCode">COMPANY_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="costCenter">COST_CENTER = #costCenter#</isNotNull>
	    <isNull prepend=", " property="costCenter">COST_CENTER = NULL</isNull>
	    <isNotNull prepend=", " property="checkType">CHECK_TYPE = #checkType#</isNotNull>
	    <isNull prepend=", " property="checkType">CHECK_TYPE = NULL</isNull>
	    <isNotNull prepend=", " property="checktype1">CHECKTYPE1 = #checktype1#</isNotNull>
	    <isNull prepend=", " property="checktype1">CHECKTYPE1 = NULL</isNull>
	    <isNotNull prepend=", " property="checktype2">CHECKTYPE2 = #checktype2#</isNotNull>
	    <isNull prepend=", " property="checktype2">CHECKTYPE2 = NULL</isNull>
	    <isNotNull prepend=", " property="checktype3">CHECKTYPE3 = #checktype3#</isNotNull>
	    <isNull prepend=", " property="checktype3">CHECKTYPE3 = NULL</isNull>
	    <isNotNull prepend=", " property="checktype4">CHECKTYPE4 = #checktype4#</isNotNull>
	    <isNull prepend=", " property="checktype4">CHECKTYPE4 = NULL</isNull>
	    <isNotNull prepend=", " property="checktype5">CHECKTYPE5 = #checktype5#</isNotNull>
	    <isNull prepend=", " property="checktype5">CHECKTYPE5 = NULL</isNull>
	    <isNotNull prepend=", " property="sendStatus">SEND_STATUS = #sendStatus#</isNotNull>
	    <isNull prepend=", " property="sendStatus">SEND_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="sendTime">SEND_TIME = #sendTime#</isNotNull>
	    <isNull prepend=", " property="sendTime">SEND_TIME = NULL</isNull>
	    <isNotNull prepend=", " property="sourceLabel">SOURCE_LABEL = #sourceLabel#</isNotNull>
	    <isNull prepend=", " property="sourceLabel">SOURCE_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="extra1">EXTRA1 = #extra1#</isNotNull>
	    <isNull prepend=", " property="extra1">EXTRA1 = NULL</isNull>
	    <isNotNull prepend=", " property="extra2">EXTRA2 = #extra2#</isNotNull>
	    <isNull prepend=", " property="extra2">EXTRA2 = NULL</isNull>
	    <isNotNull prepend=", " property="extra3">EXTRA3 = #extra3#</isNotNull>
	    <isNull prepend=", " property="extra3">EXTRA3 = NULL</isNull>
	    <isNotNull prepend=", " property="extra4">EXTRA4 = #extra4#</isNotNull>
	    <isNull prepend=", " property="extra4">EXTRA4 = NULL</isNull>
	    <isNotNull prepend=", " property="extra5">EXTRA5 = #extra5#</isNotNull>
	    <isNull prepend=", " property="extra5">EXTRA5 = NULL</isNull>
	    <isNotNull prepend=", " property="extra6">EXTRA6 = #extra6#</isNotNull>
	    <isNull prepend=", " property="extra6">EXTRA6 = NULL</isNull>
	    <isNotNull prepend=", " property="extra7">EXTRA7 = #extra7#</isNotNull>
	    <isNull prepend=", " property="extra7">EXTRA7 = NULL</isNull>
	    <isNotNull prepend=", " property="extra8">EXTRA8 = #extra8#</isNotNull>
	    <isNull prepend=", " property="extra8">EXTRA8 = NULL</isNull>
	    <isNotNull prepend=", " property="extra9">EXTRA9 = #extra9#</isNotNull>
	    <isNull prepend=", " property="extra9">EXTRA9 = NULL</isNull>
	    <isNotNull prepend=", " property="extra10">EXTRA10 = #extra10#</isNotNull>
	    <isNull prepend=", " property="extra10">EXTRA10 = NULL</isNull>
	    <isNotNull prepend=", " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
	    <isNull prepend=", " property="delStatus">DEL_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
	    <isNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="createDate">CREATE_DATE = #createDate#</isNotNull>
	    <isNull prepend=", " property="createDate">CREATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
	    <isNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNull prepend=", " property="updateDate">UPDATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
	    <isNull prepend=", " property="recordVersion">RECORD_VERSION = NULL</isNull>
		</dynamic>
		WHERE 		RECORD_GUID=#recordGuid# 			</update>
	
	<update id="updateByC" parameterClass="hashmap">
		UPDATE  ${kjglSchema}.T_KYXM_DI_PROJECT_LOG	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="recordGuid">RECORD_GUID = #recordGuid#</isNotNull>
				<isNotNull prepend="," property="functionCode">FUNCTION_CODE = #functionCode#</isNotNull>
				<isNotNull prepend="," property="projectItem">PROJECT_ITEM = #projectItem#</isNotNull>
				<isNotNull prepend="," property="projectName">PROJECT_NAME = #projectName#</isNotNull>
				<isNotNull prepend="," property="companyCode">COMPANY_CODE = #companyCode#</isNotNull>
				<isNotNull prepend="," property="costCenter">COST_CENTER = #costCenter#</isNotNull>
				<isNotNull prepend="," property="checkType">CHECK_TYPE = #checkType#</isNotNull>
				<isNotNull prepend="," property="checktype1">CHECKTYPE1 = #checktype1#</isNotNull>
				<isNotNull prepend="," property="checktype2">CHECKTYPE2 = #checktype2#</isNotNull>
				<isNotNull prepend="," property="checktype3">CHECKTYPE3 = #checktype3#</isNotNull>
				<isNotNull prepend="," property="checktype4">CHECKTYPE4 = #checktype4#</isNotNull>
				<isNotNull prepend="," property="checktype5">CHECKTYPE5 = #checktype5#</isNotNull>
				<isNotNull prepend="," property="sendStatus">SEND_STATUS = #sendStatus#</isNotNull>
				<isNotNull prepend="," property="sendTime">SEND_TIME = #sendTime#</isNotNull>
				<isNotNull prepend="," property="sourceLabel">SOURCE_LABEL = #sourceLabel#</isNotNull>
				<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
				<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
				<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
				<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
				<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
				<isNotNull prepend="," property="extra6">EXTRA6 = #extra6#</isNotNull>
				<isNotNull prepend="," property="extra7">EXTRA7 = #extra7#</isNotNull>
				<isNotNull prepend="," property="extra8">EXTRA8 = #extra8#</isNotNull>
				<isNotNull prepend="," property="extra9">EXTRA9 = #extra9#</isNotNull>
				<isNotNull prepend="," property="extra10">EXTRA10 = #extra10#</isNotNull>
				<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
				<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
				<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
				<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
				<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
				<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
			</dynamic>
		<dynamic prepend=" WHERE ">
		 			<isNotNull prepend=" AND " property="recordGuidOld">RECORD_GUID = #recordGuidOld#</isNotNull>
			<isNotNull prepend=" AND " property="functionCodeOld">FUNCTION_CODE = #functionCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="projectItemOld">PROJECT_ITEM = #projectItemOld#</isNotNull>
			<isNotNull prepend=" AND " property="projectNameOld">PROJECT_NAME = #projectNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="companyCodeOld">COMPANY_CODE = #companyCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="costCenterOld">COST_CENTER = #costCenterOld#</isNotNull>
			<isNotNull prepend=" AND " property="checkTypeOld">CHECK_TYPE = #checkTypeOld#</isNotNull>
			<isNotNull prepend=" AND " property="checktype1Old">CHECKTYPE1 = #checktype1Old#</isNotNull>
			<isNotNull prepend=" AND " property="checktype2Old">CHECKTYPE2 = #checktype2Old#</isNotNull>
			<isNotNull prepend=" AND " property="checktype3Old">CHECKTYPE3 = #checktype3Old#</isNotNull>
			<isNotNull prepend=" AND " property="checktype4Old">CHECKTYPE4 = #checktype4Old#</isNotNull>
			<isNotNull prepend=" AND " property="checktype5Old">CHECKTYPE5 = #checktype5Old#</isNotNull>
			<isNotNull prepend=" AND " property="sendStatusOld">SEND_STATUS = #sendStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="sendTimeOld">SEND_TIME = #sendTimeOld#</isNotNull>
			<isNotNull prepend=" AND " property="sourceLabelOld">SOURCE_LABEL = #sourceLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="extra1Old">EXTRA1 = #extra1Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra2Old">EXTRA2 = #extra2Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra3Old">EXTRA3 = #extra3Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra4Old">EXTRA4 = #extra4Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra5Old">EXTRA5 = #extra5Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra6Old">EXTRA6 = #extra6Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra7Old">EXTRA7 = #extra7Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra8Old">EXTRA8 = #extra8Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra9Old">EXTRA9 = #extra9Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra10Old">EXTRA10 = #extra10Old#</isNotNull>
			<isNotNull prepend=" AND " property="delStatusOld">DEL_STATUS = #delStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabelOld">CREATE_USER_LABEL = #createUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="createDateOld">CREATE_DATE = #createDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabelOld">UPDATE_USER_LABEL = #updateUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateDateOld">UPDATE_DATE = #updateDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabelOld">DELETE_USER_LABEL = #deleteUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDateOld">DELETE_DATE = #deleteDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersionOld">RECORD_VERSION = #recordVersionOld#</isNotNull>
			<isNotNull prepend=" AND " property="dynSqlOld"> $dynSqlOld$ </isNotNull>
		</dynamic>
	</update>
	
	<update id="updateNull" parameterClass="hashmap">
		UPDATE  ${kjglSchema}.T_KYXM_DI_PROJECT_LOG	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="recordGuid">RECORD_GUID = #recordGuid#</isNotNull>
			<isNotNull prepend="," property="functionCode">FUNCTION_CODE = #functionCode#</isNotNull>
			<isNotNull prepend="," property="projectItem">PROJECT_ITEM = #projectItem#</isNotNull>
			<isNotNull prepend="," property="projectName">PROJECT_NAME = #projectName#</isNotNull>
			<isNotNull prepend="," property="companyCode">COMPANY_CODE = #companyCode#</isNotNull>
			<isNotNull prepend="," property="costCenter">COST_CENTER = #costCenter#</isNotNull>
			<isNotNull prepend="," property="checkType">CHECK_TYPE = #checkType#</isNotNull>
			<isNotNull prepend="," property="checktype1">CHECKTYPE1 = #checktype1#</isNotNull>
			<isNotNull prepend="," property="checktype2">CHECKTYPE2 = #checktype2#</isNotNull>
			<isNotNull prepend="," property="checktype3">CHECKTYPE3 = #checktype3#</isNotNull>
			<isNotNull prepend="," property="checktype4">CHECKTYPE4 = #checktype4#</isNotNull>
			<isNotNull prepend="," property="checktype5">CHECKTYPE5 = #checktype5#</isNotNull>
			<isNotNull prepend="," property="sendStatus">SEND_STATUS = #sendStatus#</isNotNull>
			<isNotNull prepend="," property="sendTime">SEND_TIME = #sendTime#</isNotNull>
			<isNotNull prepend="," property="sourceLabel">SOURCE_LABEL = #sourceLabel#</isNotNull>
			<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend="," property="extra6">EXTRA6 = #extra6#</isNotNull>
			<isNotNull prepend="," property="extra7">EXTRA7 = #extra7#</isNotNull>
			<isNotNull prepend="," property="extra8">EXTRA8 = #extra8#</isNotNull>
			<isNotNull prepend="," property="extra9">EXTRA9 = #extra9#</isNotNull>
			<isNotNull prepend="," property="extra10">EXTRA10 = #extra10#</isNotNull>
			<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			<isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
			<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		RECORD_GUID=#recordGuid# 			</update>	
</sqlMap>