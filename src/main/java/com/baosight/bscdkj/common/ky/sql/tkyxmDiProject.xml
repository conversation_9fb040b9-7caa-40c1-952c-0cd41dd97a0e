<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkyxmDiProject">
	<typeAlias alias="tKYXMDiProject" type="com.baosight.bscdkj.common.ky.domain.TkyxmDiProject"/>
	<select id="load" parameterClass="string" resultClass="tKYXMDiProject">
		SELECT 
				RECORD_GUID as "recordGuid" ,		
				PROJECT_GUID as "projectGuid" ,		
				PROJECT_CODE as "projectCode" ,		
				PROJECT_NAME as "projectName" ,		
				PROJECT_STATUS as "projectStatus" ,		
				LAST_SEND_TIME as "lastSendTime" ,		
				LAST_SEND_TYPE as "lastSendType" ,		
				SEND_COUNT as "sendCount" ,		
				ZT_ZRZX as "ztZrzx" ,		
				ZT_ZRZX_HIS as "ztZrzxHis" ,		
				<PERSON><PERSON><PERSON><PERSON> as "extra1" ,		
				EXTRA2 as "extra2" ,		
				EXTRA3 as "extra3" ,		
				EXTRA4 as "extra4" ,		
				EXTRA5 as "extra5" ,		
				EXTRA6 as "extra6" ,		
				EXTRA7 as "extra7" ,		
				EXTRA8 as "extra8" ,		
				EXTRA9 as "extra9" ,		
				EXTRA10 as "extra10" ,		
				DEL_STATUS as "delStatus" ,		
				CREATE_USER_LABEL as "createUserLabel" ,		
				CREATE_DATE as "createDate" ,		
				UPDATE_USER_LABEL as "updateUserLabel" ,		
				UPDATE_DATE as "updateDate" ,		
				DELETE_USER_LABEL as "deleteUserLabel" ,		
				DELETE_DATE as "deleteDate" ,		
				RECORD_VERSION as "recordVersion" 		
				FROM ${kjglSchema}.T_KYXM_DI_PROJECT
		WHERE   RECORD_GUID=#value# 				
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tKYXMDiProject">
		SELECT
				RECORD_GUID  as "recordGuid" ,		
				PROJECT_GUID  as "projectGuid" ,		
				PROJECT_CODE  as "projectCode" ,		
				PROJECT_NAME  as "projectName" ,		
				PROJECT_STATUS  as "projectStatus" ,		
				LAST_SEND_TIME  as "lastSendTime" ,		
				LAST_SEND_TYPE  as "lastSendType" ,		
				SEND_COUNT  as "sendCount" ,		
				ZT_ZRZX  as "ztZrzx" ,		
				ZT_ZRZX_HIS  as "ztZrzxHis" ,		
				EXTRA1  as "extra1" ,		
				EXTRA2  as "extra2" ,		
				EXTRA3  as "extra3" ,		
				EXTRA4  as "extra4" ,		
				EXTRA5  as "extra5" ,		
				EXTRA6  as "extra6" ,		
				EXTRA7  as "extra7" ,		
				EXTRA8  as "extra8" ,		
				EXTRA9  as "extra9" ,		
				EXTRA10  as "extra10" ,		
				DEL_STATUS  as "delStatus" ,		
				CREATE_USER_LABEL  as "createUserLabel" ,		
				CREATE_DATE  as "createDate" ,		
				UPDATE_USER_LABEL  as "updateUserLabel" ,		
				UPDATE_DATE  as "updateDate" ,		
				DELETE_USER_LABEL  as "deleteUserLabel" ,		
				DELETE_DATE  as "deleteDate" ,		
				RECORD_VERSION  as "recordVersion" 		
				FROM ${kjglSchema}.T_KYXM_DI_PROJECT
			<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="recordGuid">RECORD_GUID =  #recordGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordGuids"> 
					RECORD_GUID IN
					<iterate property="recordGuids" conjunction="," open="(" close=")">#recordGuids[]#</iterate>
				</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectGuid">PROJECT_GUID =  #projectGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectCode">PROJECT_CODE =  #projectCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectCodeLike">project_code like '%$projectCodeLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectName">PROJECT_NAME =  #projectName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectNameLike">PROJECT_NAME like  '%$projectNameLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectStatus">PROJECT_STATUS =  #projectStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lastSendTime">LAST_SEND_TIME =  #lastSendTime#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="lastSendType">LAST_SEND_TYPE =  #lastSendType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sendCount">SEND_COUNT =  #sendCount#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ztZrzx">ZT_ZRZX =  #ztZrzx#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ztLike">ZT_ZRZX like '%$ztLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="zrzxLike">ZT_ZRZX like '%$zrzxLike$%'</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ztZrzxHis">ZT_ZRZX_HIS =  #ztZrzxHis#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra6">EXTRA6 =  #extra6#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra7">EXTRA7 =  #extra7#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra8">EXTRA8 =  #extra8#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra9">EXTRA9 =  #extra9#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra10">EXTRA10 =  #extra10#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
				<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
			</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${kjglSchema}.T_KYXM_DI_PROJECT 
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="recordGuid">RECORD_GUID =  #recordGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectGuid">PROJECT_GUID =  #projectGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCode">PROJECT_CODE =  #projectCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">PROJECT_NAME =  #projectName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStatus">PROJECT_STATUS =  #projectStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lastSendTime">LAST_SEND_TIME =  #lastSendTime#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lastSendType">LAST_SEND_TYPE =  #lastSendType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sendCount">SEND_COUNT =  #sendCount#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ztZrzx">ZT_ZRZX =  #ztZrzx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ztZrzxHis">ZT_ZRZX_HIS =  #ztZrzxHis#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra6">EXTRA6 =  #extra6#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra7">EXTRA7 =  #extra7#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra8">EXTRA8 =  #extra8#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra9">EXTRA9 =  #extra9#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra10">EXTRA10 =  #extra10#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">DEL_STATUS = '0'</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tKYXMDiProject">
		INSERT INTO ${kjglSchema}.T_KYXM_DI_PROJECT ( 
		<dynamic prepend=" ">
					<isNotNull prepend=", " property="recordGuid">RECORD_GUID </isNotNull>
					<isNotNull prepend=", " property="projectGuid">PROJECT_GUID </isNotNull>
					<isNotNull prepend=", " property="projectCode">PROJECT_CODE </isNotNull>
					<isNotNull prepend=", " property="projectName">PROJECT_NAME </isNotNull>
					<isNotNull prepend=", " property="projectStatus">PROJECT_STATUS </isNotNull>
					<isNotNull prepend=", " property="lastSendTime">LAST_SEND_TIME </isNotNull>
					<isNotNull prepend=", " property="lastSendType">LAST_SEND_TYPE </isNotNull>
					<isNotNull prepend=", " property="sendCount">SEND_COUNT </isNotNull>
					<isNotNull prepend=", " property="ztZrzx">ZT_ZRZX </isNotNull>
					<isNotNull prepend=", " property="ztZrzxHis">ZT_ZRZX_HIS </isNotNull>
					<isNotNull prepend=", " property="extra1">EXTRA1 </isNotNull>
					<isNotNull prepend=", " property="extra2">EXTRA2 </isNotNull>
					<isNotNull prepend=", " property="extra3">EXTRA3 </isNotNull>
					<isNotNull prepend=", " property="extra4">EXTRA4 </isNotNull>
					<isNotNull prepend=", " property="extra5">EXTRA5 </isNotNull>
					<isNotNull prepend=", " property="extra6">EXTRA6 </isNotNull>
					<isNotNull prepend=", " property="extra7">EXTRA7 </isNotNull>
					<isNotNull prepend=", " property="extra8">EXTRA8 </isNotNull>
					<isNotNull prepend=", " property="extra9">EXTRA9 </isNotNull>
					<isNotNull prepend=", " property="extra10">EXTRA10 </isNotNull>
					<isNotNull prepend=", " property="delStatus">DEL_STATUS </isNotNull>
					<isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="createDate">CREATE_DATE </isNotNull>
					<isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="updateDate">UPDATE_DATE </isNotNull>
					<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL </isNotNull>
					<isNotNull prepend=", " property="deleteDate">DELETE_DATE </isNotNull>
					<isNotNull prepend=", " property="recordVersion">RECORD_VERSION </isNotNull>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotNull prepend=", " property="recordGuid">#recordGuid#</isNotNull>
			<isNotNull prepend=", " property="projectGuid">#projectGuid#</isNotNull>
			<isNotNull prepend=", " property="projectCode">#projectCode#</isNotNull>
			<isNotNull prepend=", " property="projectName">#projectName#</isNotNull>
			<isNotNull prepend=", " property="projectStatus">#projectStatus#</isNotNull>
			<isNotNull prepend=", " property="lastSendTime">#lastSendTime#</isNotNull>
			<isNotNull prepend=", " property="lastSendType">#lastSendType#</isNotNull>
			<isNotNull prepend=", " property="sendCount">#sendCount#</isNotNull>
			<isNotNull prepend=", " property="ztZrzx">#ztZrzx#</isNotNull>
			<isNotNull prepend=", " property="ztZrzxHis">#ztZrzxHis#</isNotNull>
			<isNotNull prepend=", " property="extra1">#extra1#</isNotNull>
			<isNotNull prepend=", " property="extra2">#extra2#</isNotNull>
			<isNotNull prepend=", " property="extra3">#extra3#</isNotNull>
			<isNotNull prepend=", " property="extra4">#extra4#</isNotNull>
			<isNotNull prepend=", " property="extra5">#extra5#</isNotNull>
			<isNotNull prepend=", " property="extra6">#extra6#</isNotNull>
			<isNotNull prepend=", " property="extra7">#extra7#</isNotNull>
			<isNotNull prepend=", " property="extra8">#extra8#</isNotNull>
			<isNotNull prepend=", " property="extra9">#extra9#</isNotNull>
			<isNotNull prepend=", " property="extra10">#extra10#</isNotNull>
			<isNotNull prepend=", " property="delStatus">#delStatus#</isNotNull>
			<isNotNull prepend=", " property="createUserLabel">#createUserLabel#</isNotNull>
			<isNotNull prepend=", " property="createDate">
			   <isNotEmpty property="createDate">#createDate#</isNotEmpty>
			   <isEmpty property="createDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="updateUserLabel">#updateUserLabel#</isNotNull>
			<isNotNull prepend=", " property="updateDate">
			   <isNotEmpty property="updateDate">#updateDate#</isNotEmpty>
			   <isEmpty property="updateDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">#deleteUserLabel#</isNotNull>
			<isNotNull prepend=", " property="deleteDate">
			   <isNotEmpty property="deleteDate">#deleteDate#</isNotEmpty>
			   <isEmpty property="deleteDate">NULL</isEmpty>
			</isNotNull>
			<isNotNull prepend=", " property="recordVersion">#recordVersion#</isNotNull>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${kjglSchema}.T_KYXM_DI_PROJECT
		WHERE 		RECORD_GUID=#value# 	
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${kjglSchema}.T_KYXM_DI_PROJECT
		WHERE 
		<dynamic prepend=" ">
			<isNotNull prepend=" AND " property="recordGuid">RECORD_GUID = #recordGuid#</isNotNull>
			<isNotNull prepend=" AND " property="projectGuid">PROJECT_GUID = #projectGuid#</isNotNull>
			<isNotNull prepend=" AND " property="projectCode">PROJECT_CODE = #projectCode#</isNotNull>
			<isNotNull prepend=" AND " property="projectName">PROJECT_NAME = #projectName#</isNotNull>
			<isNotNull prepend=" AND " property="projectStatus">PROJECT_STATUS = #projectStatus#</isNotNull>
			<isNotNull prepend=" AND " property="lastSendTime">LAST_SEND_TIME = #lastSendTime#</isNotNull>
			<isNotNull prepend=" AND " property="lastSendType">LAST_SEND_TYPE = #lastSendType#</isNotNull>
			<isNotNull prepend=" AND " property="sendCount">SEND_COUNT = #sendCount#</isNotNull>
			<isNotNull prepend=" AND " property="ztZrzx">ZT_ZRZX = #ztZrzx#</isNotNull>
			<isNotNull prepend=" AND " property="ztZrzxHis">ZT_ZRZX_HIS = #ztZrzxHis#</isNotNull>
			<isNotNull prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend=" AND " property="extra6">EXTRA6 = #extra6#</isNotNull>
			<isNotNull prepend=" AND " property="extra7">EXTRA7 = #extra7#</isNotNull>
			<isNotNull prepend=" AND " property="extra8">EXTRA8 = #extra8#</isNotNull>
			<isNotNull prepend=" AND " property="extra9">EXTRA9 = #extra9#</isNotNull>
			<isNotNull prepend=" AND " property="extra10">EXTRA10 = #extra10#</isNotNull>
			<isNotNull prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>	
	</delete>

	<update id="update" parameterClass="tKYXMDiProject">
		UPDATE  ${kjglSchema}.T_KYXM_DI_PROJECT	
		SET 
		<dynamic prepend=" ">
		<isNotNull prepend="," property="recordGuid">RECORD_GUID = #recordGuid#</isNotNull>
		<isNotNull prepend="," property="projectGuid">PROJECT_GUID = #projectGuid#</isNotNull>
		<isNotNull prepend="," property="projectCode">PROJECT_CODE = #projectCode#</isNotNull>
		<isNotNull prepend="," property="projectName">PROJECT_NAME = #projectName#</isNotNull>
		<isNotNull prepend="," property="projectStatus">PROJECT_STATUS = #projectStatus#</isNotNull>
		<isNotNull prepend="," property="lastSendTime">LAST_SEND_TIME = #lastSendTime#</isNotNull>
		<isNotNull prepend="," property="lastSendType">LAST_SEND_TYPE = #lastSendType#</isNotNull>
		<isNotNull prepend="," property="sendCount">SEND_COUNT = #sendCount#</isNotNull>
		<isNotNull prepend="," property="ztZrzx">ZT_ZRZX = #ztZrzx#</isNotNull>
		<isNotNull prepend="," property="ztZrzxHis">ZT_ZRZX_HIS = #ztZrzxHis#</isNotNull>
		<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
		<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
		<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
		<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
		<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
		<isNotNull prepend="," property="extra6">EXTRA6 = #extra6#</isNotNull>
		<isNotNull prepend="," property="extra7">EXTRA7 = #extra7#</isNotNull>
		<isNotNull prepend="," property="extra8">EXTRA8 = #extra8#</isNotNull>
		<isNotNull prepend="," property="extra9">EXTRA9 = #extra9#</isNotNull>
		<isNotNull prepend="," property="extra10">EXTRA10 = #extra10#</isNotNull>
		<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
		<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
		<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
		<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
		<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
		<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		RECORD_GUID=#recordGuid# 			</update>
	
	<update id="updatewithnull" parameterClass="tKYXMDiProject">
		UPDATE ${kjglSchema}.T_KYXM_DI_PROJECT	
		SET 
		<dynamic prepend=" ">
	    <isNotNull prepend=", " property="recordGuid">RECORD_GUID = #recordGuid#</isNotNull>
	    <isNull prepend=", " property="recordGuid">RECORD_GUID = NULL</isNull>
	    <isNotNull prepend=", " property="projectGuid">PROJECT_GUID = #projectGuid#</isNotNull>
	    <isNull prepend=", " property="projectGuid">PROJECT_GUID = NULL</isNull>
	    <isNotNull prepend=", " property="projectCode">PROJECT_CODE = #projectCode#</isNotNull>
	    <isNull prepend=", " property="projectCode">PROJECT_CODE = NULL</isNull>
	    <isNotNull prepend=", " property="projectName">PROJECT_NAME = #projectName#</isNotNull>
	    <isNull prepend=", " property="projectName">PROJECT_NAME = NULL</isNull>
	    <isNotNull prepend=", " property="projectStatus">PROJECT_STATUS = #projectStatus#</isNotNull>
	    <isNull prepend=", " property="projectStatus">PROJECT_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="lastSendTime">LAST_SEND_TIME = #lastSendTime#</isNotNull>
	    <isNull prepend=", " property="lastSendTime">LAST_SEND_TIME = NULL</isNull>
	    <isNotNull prepend=", " property="lastSendType">LAST_SEND_TYPE = #lastSendType#</isNotNull>
	    <isNull prepend=", " property="lastSendType">LAST_SEND_TYPE = NULL</isNull>
	    <isNotNull prepend=", " property="sendCount">SEND_COUNT = #sendCount#</isNotNull>
	    <isNull prepend=", " property="sendCount">SEND_COUNT = NULL</isNull>
	    <isNotNull prepend=", " property="ztZrzx">ZT_ZRZX = #ztZrzx#</isNotNull>
	    <isNull prepend=", " property="ztZrzx">ZT_ZRZX = NULL</isNull>
	    <isNotNull prepend=", " property="ztZrzxHis">ZT_ZRZX_HIS = #ztZrzxHis#</isNotNull>
	    <isNull prepend=", " property="ztZrzxHis">ZT_ZRZX_HIS = NULL</isNull>
	    <isNotNull prepend=", " property="extra1">EXTRA1 = #extra1#</isNotNull>
	    <isNull prepend=", " property="extra1">EXTRA1 = NULL</isNull>
	    <isNotNull prepend=", " property="extra2">EXTRA2 = #extra2#</isNotNull>
	    <isNull prepend=", " property="extra2">EXTRA2 = NULL</isNull>
	    <isNotNull prepend=", " property="extra3">EXTRA3 = #extra3#</isNotNull>
	    <isNull prepend=", " property="extra3">EXTRA3 = NULL</isNull>
	    <isNotNull prepend=", " property="extra4">EXTRA4 = #extra4#</isNotNull>
	    <isNull prepend=", " property="extra4">EXTRA4 = NULL</isNull>
	    <isNotNull prepend=", " property="extra5">EXTRA5 = #extra5#</isNotNull>
	    <isNull prepend=", " property="extra5">EXTRA5 = NULL</isNull>
	    <isNotNull prepend=", " property="extra6">EXTRA6 = #extra6#</isNotNull>
	    <isNull prepend=", " property="extra6">EXTRA6 = NULL</isNull>
	    <isNotNull prepend=", " property="extra7">EXTRA7 = #extra7#</isNotNull>
	    <isNull prepend=", " property="extra7">EXTRA7 = NULL</isNull>
	    <isNotNull prepend=", " property="extra8">EXTRA8 = #extra8#</isNotNull>
	    <isNull prepend=", " property="extra8">EXTRA8 = NULL</isNull>
	    <isNotNull prepend=", " property="extra9">EXTRA9 = #extra9#</isNotNull>
	    <isNull prepend=", " property="extra9">EXTRA9 = NULL</isNull>
	    <isNotNull prepend=", " property="extra10">EXTRA10 = #extra10#</isNotNull>
	    <isNull prepend=", " property="extra10">EXTRA10 = NULL</isNull>
	    <isNotNull prepend=", " property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
	    <isNull prepend=", " property="delStatus">DEL_STATUS = NULL</isNull>
	    <isNotNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
	    <isNull prepend=", " property="createUserLabel">CREATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="createDate">CREATE_DATE = #createDate#</isNotNull>
	    <isNull prepend=", " property="createDate">CREATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
	    <isNull prepend=", " property="updateUserLabel">UPDATE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
	    <isNull prepend=", " property="updateDate">UPDATE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
	    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
	    <isNotNull prepend=", " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
	    <isNull prepend=", " property="recordVersion">RECORD_VERSION = NULL</isNull>
		</dynamic>
		WHERE 		RECORD_GUID=#recordGuid# 			</update>
	
	<update id="updateByC" parameterClass="hashmap">
		UPDATE  ${kjglSchema}.T_KYXM_DI_PROJECT	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="recordGuid">RECORD_GUID = #recordGuid#</isNotNull>
				<isNotNull prepend="," property="projectGuid">PROJECT_GUID = #projectGuid#</isNotNull>
				<isNotNull prepend="," property="projectCode">PROJECT_CODE = #projectCode#</isNotNull>
				<isNotNull prepend="," property="projectName">PROJECT_NAME = #projectName#</isNotNull>
				<isNotNull prepend="," property="projectStatus">PROJECT_STATUS = #projectStatus#</isNotNull>
				<isNotNull prepend="," property="lastSendTime">LAST_SEND_TIME = #lastSendTime#</isNotNull>
				<isNotNull prepend="," property="lastSendType">LAST_SEND_TYPE = #lastSendType#</isNotNull>
				<isNotNull prepend="," property="sendCount">SEND_COUNT = #sendCount#</isNotNull>
				<isNotNull prepend="," property="ztZrzx">ZT_ZRZX = #ztZrzx#</isNotNull>
				<isNotNull prepend="," property="ztZrzxHis">ZT_ZRZX_HIS = #ztZrzxHis#</isNotNull>
				<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
				<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
				<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
				<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
				<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
				<isNotNull prepend="," property="extra6">EXTRA6 = #extra6#</isNotNull>
				<isNotNull prepend="," property="extra7">EXTRA7 = #extra7#</isNotNull>
				<isNotNull prepend="," property="extra8">EXTRA8 = #extra8#</isNotNull>
				<isNotNull prepend="," property="extra9">EXTRA9 = #extra9#</isNotNull>
				<isNotNull prepend="," property="extra10">EXTRA10 = #extra10#</isNotNull>
				<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
				<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
				<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
				<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
				<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			    <isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
	    <isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			    <isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
	    <isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
				<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
			</dynamic>
		<dynamic prepend=" WHERE ">
		 			<isNotNull prepend=" AND " property="recordGuidOld">RECORD_GUID = #recordGuidOld#</isNotNull>
			<isNotNull prepend=" AND " property="projectGuidOld">PROJECT_GUID = #projectGuidOld#</isNotNull>
			<isNotNull prepend=" AND " property="projectCodeOld">PROJECT_CODE = #projectCodeOld#</isNotNull>
			<isNotNull prepend=" AND " property="projectNameOld">PROJECT_NAME = #projectNameOld#</isNotNull>
			<isNotNull prepend=" AND " property="projectStatusOld">PROJECT_STATUS = #projectStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="lastSendTimeOld">LAST_SEND_TIME = #lastSendTimeOld#</isNotNull>
			<isNotNull prepend=" AND " property="lastSendTypeOld">LAST_SEND_TYPE = #lastSendTypeOld#</isNotNull>
			<isNotNull prepend=" AND " property="sendCountOld">SEND_COUNT = #sendCountOld#</isNotNull>
			<isNotNull prepend=" AND " property="ztZrzxOld">ZT_ZRZX = #ztZrzxOld#</isNotNull>
			<isNotNull prepend=" AND " property="ztZrzxHisOld">ZT_ZRZX_HIS = #ztZrzxHisOld#</isNotNull>
			<isNotNull prepend=" AND " property="extra1Old">EXTRA1 = #extra1Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra2Old">EXTRA2 = #extra2Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra3Old">EXTRA3 = #extra3Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra4Old">EXTRA4 = #extra4Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra5Old">EXTRA5 = #extra5Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra6Old">EXTRA6 = #extra6Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra7Old">EXTRA7 = #extra7Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra8Old">EXTRA8 = #extra8Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra9Old">EXTRA9 = #extra9Old#</isNotNull>
			<isNotNull prepend=" AND " property="extra10Old">EXTRA10 = #extra10Old#</isNotNull>
			<isNotNull prepend=" AND " property="delStatusOld">DEL_STATUS = #delStatusOld#</isNotNull>
			<isNotNull prepend=" AND " property="createUserLabelOld">CREATE_USER_LABEL = #createUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="createDateOld">CREATE_DATE = #createDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateUserLabelOld">UPDATE_USER_LABEL = #updateUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="updateDateOld">UPDATE_DATE = #updateDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteUserLabelOld">DELETE_USER_LABEL = #deleteUserLabelOld#</isNotNull>
			<isNotNull prepend=" AND " property="deleteDateOld">DELETE_DATE = #deleteDateOld#</isNotNull>
			<isNotNull prepend=" AND " property="recordVersionOld">RECORD_VERSION = #recordVersionOld#</isNotNull>
			<isNotNull prepend=" AND " property="dynSqlOld"> $dynSqlOld$ </isNotNull>
		</dynamic>
	</update>
	
	<update id="updateNull" parameterClass="hashmap">
		UPDATE  ${kjglSchema}.T_KYXM_DI_PROJECT	
		SET 
		<dynamic prepend=" ">
			<isNotNull prepend="," property="recordGuid">RECORD_GUID = #recordGuid#</isNotNull>
			<isNotNull prepend="," property="projectGuid">PROJECT_GUID = #projectGuid#</isNotNull>
			<isNotNull prepend="," property="projectCode">PROJECT_CODE = #projectCode#</isNotNull>
			<isNotNull prepend="," property="projectName">PROJECT_NAME = #projectName#</isNotNull>
			<isNotNull prepend="," property="projectStatus">PROJECT_STATUS = #projectStatus#</isNotNull>
			<isNotNull prepend="," property="lastSendTime">LAST_SEND_TIME = #lastSendTime#</isNotNull>
			<isNotNull prepend="," property="lastSendType">LAST_SEND_TYPE = #lastSendType#</isNotNull>
			<isNotNull prepend="," property="sendCount">SEND_COUNT = #sendCount#</isNotNull>
			<isNotNull prepend="," property="ztZrzx">ZT_ZRZX = #ztZrzx#</isNotNull>
			<isNotNull prepend="," property="ztZrzxHis">ZT_ZRZX_HIS = #ztZrzxHis#</isNotNull>
			<isNotNull prepend="," property="extra1">EXTRA1 = #extra1#</isNotNull>
			<isNotNull prepend="," property="extra2">EXTRA2 = #extra2#</isNotNull>
			<isNotNull prepend="," property="extra3">EXTRA3 = #extra3#</isNotNull>
			<isNotNull prepend="," property="extra4">EXTRA4 = #extra4#</isNotNull>
			<isNotNull prepend="," property="extra5">EXTRA5 = #extra5#</isNotNull>
			<isNotNull prepend="," property="extra6">EXTRA6 = #extra6#</isNotNull>
			<isNotNull prepend="," property="extra7">EXTRA7 = #extra7#</isNotNull>
			<isNotNull prepend="," property="extra8">EXTRA8 = #extra8#</isNotNull>
			<isNotNull prepend="," property="extra9">EXTRA9 = #extra9#</isNotNull>
			<isNotNull prepend="," property="extra10">EXTRA10 = #extra10#</isNotNull>
			<isNotNull prepend="," property="delStatus">DEL_STATUS = #delStatus#</isNotNull>
			<isNotNull prepend="," property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotNull>
			<isNotNull prepend="," property="createDate">CREATE_DATE = #createDate#</isNotNull>
			<isNotNull prepend="," property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotNull>
			<isNotNull prepend="," property="updateDate">UPDATE_DATE = #updateDate#</isNotNull>
			<isNotNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotNull>
			<isNull prepend=", " property="deleteUserLabel">DELETE_USER_LABEL = NULL</isNull>
			<isNotNull prepend=", " property="deleteDate">DELETE_DATE = #deleteDate#</isNotNull>
			<isNull prepend=", " property="deleteDate">DELETE_DATE = NULL</isNull>
			<isNotNull prepend="," property="recordVersion">RECORD_VERSION = #recordVersion#</isNotNull>
		</dynamic>
		WHERE 		RECORD_GUID=#recordGuid# 			</update>	
</sqlMap>