<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkyxmProject">
	<typeAlias alias="tkyxmProjectResult" type="com.baosight.bscdkj.common.ky.domain.TkyxmProject"/>
	<select id="load" parameterClass="string" resultClass="tkyxmProjectResult">
		SELECT 
				record_guid as "recordGuid" ,
				serial_no as "serialNo" ,
				project_code as "projectCode" ,
				project_name as "projectName" ,
				fzr as "fzr" ,
				fzr_name as "fzrName" ,
				fzr_contact_tel as "fzrContactTel" ,
				lxr as "lxr" ,
				lxr_name as "lxrName" ,
				lxr_contact_tel as "lxrContactTel" ,
				project_zg as "projectZg" ,
				project_zg_name as "projectZgName" ,
				project_start_date as "projectStartDate" ,
				project_end_date as "projectEndDate" ,
				project_type as "projectType" ,
				project_level as "projectLevel" ,	
				ghjsfx as "ghjsfx" ,
				tech_domain as "techDomain" ,
				techsx as "techsx" ,
				project_status as "projectStatus" ,
				sign_date as "signDate" ,
				economy_benefit as "economyBenefit" ,
				economy_benefit_pf_date as "economyBenefitPfDate" ,
				actual_complete_date as "actualCompleteDate" ,
				main_project_guid as "mainProjectGuid" ,
				source_label as "sourceLabel" ,
				nr_oldpro_source_guid as "nrOldproSourceGuid" ,
				gd_date as "gdDate" ,
				jt_date as "jtDate" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				extra6 as "extra6" ,
				extra7 as "extra7" ,
				extra8 as "extra8" ,
				extra9 as "extra9" ,
				extra10 as "extra10" ,
				extra11 as "extra11" ,
				extra12 as "extra12" ,
				extra13 as "extra13" ,
				extra14 as "extra14" ,
				extra15 as "extra15" ,
				extra16 as "extra16" ,
				extra17 as "extra17" ,
				extra18 as "extra18" ,
				extra19 as "extra19" ,
				extra20 as "extra20" ,
				extra22 as "extra22" ,
				extra21 as "extra21" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" ,
				extra23 as "extra23" ,
				extra24 as "extra24" ,
				extra25 as "extra25" ,
				extra26 as "extra26" ,
				extra27 as "extra27" ,
				extra28 as "extra28" ,
				yan_shou_date as "yanShouDate" ,
				extra30 as "extra30" ,
				extra31 as "extra31" ,
				extra32 as "extra32" ,
				extra33 as "extra33" ,
				extra34 as "extra34" ,
				extra35 as "extra35" ,
				extra36 as "extra36" ,
				extra37 as "extra37" ,
				extra38 as "extra38" ,
				extra39 as "extra39" ,
				extra40 as "extra40" ,
				extra41 as "extra41" ,
				extra42 as "extra42" ,
				extra43 as "extra43" ,
				extra44 as "extra44" ,
				extra45 as "extra45" ,
				extra46 as "extra46" ,
				extra47 as "extra47" ,
				extra48 as "extra48" ,
				extra49 as "extra49" , 
				extra50 as "extra50" , 
				extra51 as "extra51" , 
				extra52 as "extra52" , 
				extra53 as "extra53" , 
				extra54 as "extra54" , 
				extra55 as "extra55" , 
				extra56 as "extra56" , 
				extra57 as "extra57" , 
				extra58 as "extra58" , 
				extra59 as "extra59" , 
				extra60 as "extra60" , 
				extra61 as "extra61" , 
				extra62 as "extra62" , 
				extra63 as "extra63" , 
				extra64 as "extra64" , 
				extra65 as "extra65" , 
				extra66 as "extra66" , 
				extra67 as "extra67" , 
				extra68 as "extra68" , 
				extra69 as "extra69" , 
				extra70 as "extra70" , 
				extra71 as "extra71" , 
				extra72 as "extra72" , 
				extra73 as "extra73" , 
				extra74 as "extra74" , 
				extra75 as "extra75" , 
				extra76 as "extra76" , 
				extra77 as "extra77" , 
				extra78 as "extra78" , 
				extra79 as "extra79" , 
				extra80 as "extra80" ,
				project_sx as "projectSx",
				cybm as "cybm",
				cybm_name as "cybmName",
				ssfs as "ssfs"
				FROM ${srmsSchema}.t_kyxm_project
		WHERE
				record_guid = #recordGuid#
		
	</select>

	<select id="query" parameterClass="hashmap" resultClass="tkyxmProjectResult" remapResults="true">
		SELECT
			record_guid as "recordGuid" ,
			serial_no as "serialNo" ,
			project_code as "projectCode" ,
			project_name as "projectName" ,
			fzr as "fzr" ,
			fzr_name as "fzrName" ,
			fzr_contact_tel as "fzrContactTel" ,
			lxr as "lxr" ,
			lxr_name as "lxrName" ,
			lxr_contact_tel as "lxrContactTel" ,
			project_zg as "projectZg" ,
			project_zg_name as "projectZgName" ,
			project_start_date as "projectStartDate" ,
			project_end_date as "projectEndDate" ,
			project_type as "projectType" ,
			project_level as "projectLevel" ,
			ghjsfx as "ghjsfx" ,
			tech_domain as "techDomain" ,
			techsx as "techsx" ,
			project_status as "projectStatus" ,
			sign_date as "signDate" ,
			economy_benefit as "economyBenefit" ,
			economy_benefit_pf_date as "economyBenefitPfDate" ,
			actual_complete_date as "actualCompleteDate" ,
			main_project_guid as "mainProjectGuid" ,
			source_label as "sourceLabel" ,
			nr_oldpro_source_guid as "nrOldproSourceGuid" ,
			gd_date as "gdDate" ,
			jt_date as "jtDate" ,
			extra1 as "extra1" ,
			extra2 as "extra2" ,
			extra3 as "extra3" ,
			extra4 as "extra4" ,
			extra5 as "extra5" ,
			extra6 as "extra6" ,
			extra7 as "extra7" ,
			extra8 as "extra8" ,
			extra9 as "extra9" ,
			extra10 as "extra10" ,
			extra11 as "extra11" ,
			extra12 as "extra12" ,
			extra13 as "extra13" ,
			extra14 as "extra14" ,
			extra15 as "extra15" ,
			extra16 as "extra16" ,
			extra17 as "extra17" ,
			extra18 as "extra18" ,
			extra19 as "extra19" ,
			extra20 as "extra20" ,
			extra22 as "extra22" ,
			extra21 as "extra21" ,
			del_status as "delStatus" ,
			create_user_label as "createUserLabel" ,
			create_date as "createDate" ,
			update_user_label as "updateUserLabel" ,
			update_date as "updateDate" ,
			delete_user_label as "deleteUserLabel" ,
			delete_date as "deleteDate" ,
			record_version as "recordVersion" ,
			extra23 as "extra23" ,
			extra24 as "extra24" ,
			extra25 as "extra25" ,
			extra26 as "extra26" ,
			extra27 as "extra27" ,
			extra28 as "extra28" ,
			yan_shou_date as "yanShouDate" ,
			extra30 as "extra30",
			extra31 as "extra31" ,
			extra32 as "extra32" ,
			extra33 as "extra33" ,
			extra34 as "extra34" ,
			extra35 as "extra35" ,
			extra36 as "extra36" ,
			extra37 as "extra37" ,
			extra38 as "extra38" ,
			extra39 as "extra39" ,
			extra40 as "extra40" ,
			extra41 as "extra41" ,
			extra42 as "extra42" ,
			extra43 as "extra43" ,
			extra44 as "extra44" ,
			extra45 as "extra45" ,
			extra46 as "extra46" ,
			extra47 as "extra47" ,
			extra48 as "extra48" ,
			extra49 as "extra49" , 
			extra50 as "extra50" , 
			extra51 as "extra51" , 
			extra52 as "extra52" , 
			extra53 as "extra53" , 
			extra54 as "extra54" , 
			extra55 as "extra55" , 
			extra56 as "extra56" , 
			extra57 as "extra57" , 
			extra58 as "extra58" , 
			extra59 as "extra59" , 
			extra60 as "extra60" , 
			extra61 as "extra61" , 
			extra62 as "extra62" , 
			extra63 as "extra63" , 
			extra64 as "extra64" , 
			extra65 as "extra65" , 
			extra66 as "extra66" , 
			extra67 as "extra67" , 
			extra68 as "extra68" , 
			extra69 as "extra69" , 
			extra70 as "extra70" , 
			extra71 as "extra71" , 
			extra72 as "extra72" , 
			extra73 as "extra73" , 
			extra74 as "extra74" , 
			extra75 as "extra75" , 
			extra76 as "extra76" , 
			extra77 as "extra77" , 
			extra78 as "extra78" , 
			extra79 as "extra79" , 
			extra80 as "extra80" ,
			project_sx as "projectSx",
			cybm as "cybm",
			cybm_name as "cybmName",
			ssfs as "ssfs"
		FROM ${srmsSchema}.t_kyxm_project
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="recordGuid">record_guid = #recordGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectGuids"> 
				record_guid IN
				<iterate property="projectGuids" conjunction="," open="(" close=")">#projectGuids[]#</iterate>
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="serialNo">serial_no = #serialNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCode">project_code = #projectCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCodeLike">project_code like '%$projectCodeLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCodes"> 
				project_code IN
				<iterate property="projectCodes" conjunction="," open="(" close=")">#projectCodes[]#</iterate>
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">project_name = #projectName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectNameLike">project_name like '%$projectNameLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzr">fzr = #fzr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrSub">fzr like '%$fzrSub$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrLike">fzr like '%$fzrLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrName">fzr_name = #fzrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrNameLike">fzr_name  like '%$fzrNameLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrContactTel">fzr_contact_tel = #fzrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxr">lxr = #lxr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrName">lxr_name = #lxrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrContactTel">lxr_contact_tel = #lxrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectZg">project_zg = #projectZg#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectZgName">project_zg_name = #projectZgName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrOrProjectZg">
				(project_zg = #fzrOrProjectZg# or fzr = #fzrOrProjectZg#)
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStartDate">project_start_date = #projectStartDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectEndDate">project_end_date = #projectEndDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectEndTime">JT_DATE = #projectEndTime# </isNotEmpty>
			<isNotEmpty prepend=" AND " property="startHpg">PROJECT_LEVEL in ('r','k') and EXTRA11 not in ('d','a')</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectType">project_type =#projectType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectTypeNotIn">
				project_type NOT IN
				<iterate property="projectTypeNotIn" conjunction="," open="(" close=")">#projectTypeNotIn[]#</iterate>
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectLevel">project_level = #projectLevel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ghjsfx">ghjsfx = #ghjsfx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techDomain">tech_domain = #techDomain#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techsx">techsx = #techsx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStatus">project_status = #projectStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStatuss">
				project_status IN
				<iterate property="projectStatuss" conjunction="," open="(" close=")">#projectStatuss[]#</iterate>
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signDate">sign_date = #signDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="economyBenefit">economy_benefit = #economyBenefit#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="economyBenefitNeEmpty">(economy_benefit is not null AND economy_benefit > 0)</isNotEmpty>
			<isNotEmpty prepend=" AND " property="economyBenefitPfDate">economy_benefit_pf_date =
				#economyBenefitPfDate#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="actualCompleteDate">actual_complete_date = #actualCompleteDate#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="mainProjectGuid">main_project_guid = #mainProjectGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="mainProjectGuids">((EXTRA16 IS NULL OR EXTRA16 = '' OR EXTRA16 = '0') OR  ( EXTRA16 = '1' and  MAIN_PROJECT_GUID IS not NULL )) </isNotEmpty>
			<isNotEmpty prepend=" AND " property="sourceLabel">source_label = #sourceLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="nrOldproSourceGuid">nr_oldpro_source_guid = #nrOldproSourceGuid#
			</isNotEmpty>
			
			<isNotEmpty prepend=" AND " property="gdLevel"> project_level != #gdLevel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gdDate">gd_date = #gdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jtDate">jt_date = #jtDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">extra1 = #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">extra2 = #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">extra3 = #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">extra4 = #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">extra5 = #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra6">extra6 = #extra6#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra7">extra7 = #extra7#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra8">extra8 = #extra8#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra9">extra9 = #extra9#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra10">extra10 = #extra10#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11">extra11 = #extra11#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11s"> 
				extra11 IN
				<iterate property="extra11s" conjunction="," open="(" close=")">#extra11s[]#</iterate>
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11NotIn"> 
				extra11 NOT IN
				<iterate property="extra11NotIn" conjunction="," open="(" close=")">#extra11NotIn[]#</iterate>
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra12">extra12 = #extra12#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra13">extra13 = #extra13#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra14">extra14 = #extra14#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra15">extra15 = #extra15#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra16">extra16 = #extra16#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra17">extra17 = #extra17#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra17Like">extra17 like '%$extra17Like$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra18">extra18 = #extra18#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra19">extra19 = #extra19#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra20">extra20 = #extra20#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra22">extra22 = #extra22#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra21">extra21 = #extra21#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">del_status = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">(del_status='0' or del_status is null or del_status='')</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label = #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">create_date = #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label = #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">update_date = #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label = #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">delete_date = #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">record_version = #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra23">extra23 = #extra23#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra24">extra24 = #extra24#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra25">extra25 = #extra25#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra26">extra26 = #extra26#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra27">extra27 = #extra27#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra28">extra28 = #extra28#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="yanShouDate">yan_shou_date = #yanShouDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra30">extra30 = #extra30#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra30Like">extra30 like '%$extra30Like$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra31">extra31 = #extra31#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra32">extra32 = #extra32#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra33">extra33 = #extra33#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra34">extra34 = #extra34#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra35">extra35 = #extra35#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra36">extra36 = #extra36#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra37">extra37 = #extra37#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra38">extra38 = #extra38#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra39">extra39 = #extra39#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra40">extra40 = #extra40#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra41">extra41 = #extra41#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra42">extra42 = #extra42#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra43">extra43 = #extra43#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra44">extra44 = #extra44#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra45">extra45 = #extra45#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra46">extra46 = #extra46#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra47">extra47 = #extra47#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra48">extra48 = #extra48#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra49">extra49 = #extra49#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra50">extra50 = #extra50#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra51">extra51 = #extra51#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra52">extra52 = #extra52#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra53">extra53 = #extra53#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra54">extra54 = #extra54#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra55">extra55 = #extra55#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra56">extra56 = #extra56#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra57">extra57 = #extra57#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra58">extra58 = #extra58#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra59">extra59 = #extra59#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra60">extra60 = #extra60#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra61">extra61 = #extra61#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra62">extra62 = #extra62#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra63">extra63 = #extra63#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra64">extra64 = #extra64#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra65">extra65 = #extra65#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra66">extra66 = #extra66#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra67">extra67 = #extra67#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra68">extra68 = #extra68#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra69">extra69 = #extra69#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra70">extra70 = #extra70#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra71">extra71 = #extra71#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra72">extra72 = #extra72#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra73">extra73 = #extra73#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra74">extra74 = #extra74#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra75">extra75 = #extra75#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra76">extra76 = #extra76#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra77">extra77 = #extra77#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra78">extra78 = #extra78#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra79">extra79 = #extra79#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra80">extra80 = #extra80#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectSx">project_sx = #projectSx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cybm">cybm = #cybm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cybmName">cybm_name = #cybmName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ssfs">ssfs = #ssfs#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="z">extra80 is null </isNotEmpty>

			<isNotEmpty prepend=" AND " property="allQdJttz">
				record_guid not in (select PROJECT_GUID from ${srmsSchema}.T_KYXM_PROJECT_END_NOTICE where DEL_STATUS = '0' and EXTRA1 in ('10','20'))
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jttzDate">PROJECT_END_DATE <![CDATA[<]]> #jttzDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="curDate_1y">
			(
				record_guid in (SELECT RECORD_GUID FROM ${srmsSchema}.T_KYXM_PROJECT WHERE RECORD_GUID NOT IN (SELECT project_guid FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE (del_status='0' or del_status is null or del_status='')) AND PROJECT_START_DATE<![CDATA[<=]]>#curDate_1y#) 
				OR
				record_guid in (SELECT project_guid FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE (del_status='0' or del_status is null or del_status='') AND BZJZRQ<![CDATA[<=]]>#curDate_1y# )
			)
			</isNotEmpty>
			<!-- 接口：立项完成待抛送的项目 -->
			<isNotEmpty prepend=" AND " property="lx_end">
				    record_guid NOT IN (SELECT PROJECT_GUID FROM ${srmsSchema}.T_KYXM_DI_PROJECT WHERE PROJECT_GUID IS not null)
				AND PROJECT_CODE NOT IN (SELECT PROJECT_CODE FROM ${srmsSchema}.T_KYXM_DI_PROJECT WHERE PROJECT_CODE IS not null)
				and project_status in ('02','03')
			</isNotEmpty>
			<!-- 接口：结题完成待删除的项目 -->
			<isNotEmpty prepend=" AND " property="jt_end">
				record_guid in (select PROJECT_GUID FROM ${srmsSchema}.T_KYXM_DI_PROJECT where PROJECT_STATUS='02')
				and extra36='1'
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ndzj_jpg">
			record_guid not in (SELECT PROJECT_GUID FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE (del_status='0' or del_status is null or del_status='') and status<![CDATA[<>]]>'40' GROUP BY PROJECT_GUID HAVING COUNT(PROJECT_GUID)>=3)
			and 
			(
				record_guid in (SELECT RECORD_GUID FROM ${srmsSchema}.T_KYXM_PROJECT WHERE RECORD_GUID NOT IN (SELECT project_guid FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE (del_status='0' or del_status is null or del_status='') and status<![CDATA[<>]]>'40' )) 
				<isNotEmpty prepend=" OR " property="xyStartDate">
				record_guid in (SELECT project_guid  FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE PROJECT_GUID||EXTRA10 IN (SELECT PROJECT_GUID||MAX(EXTRA10)  FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE (del_status='0' or del_status is null or del_status='') and status<![CDATA[<>]]>'40' GROUP BY PROJECT_GUID) and (del_status='0' or del_status is null or del_status='') and status<![CDATA[<>]]>'40' AND EXTRA10<![CDATA[<]]>#xyStartDate# )
				</isNotEmpty>
				<isNotEmpty prepend=" OR " property="xyEndDate">
				record_guid in (SELECT project_guid  FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE PROJECT_GUID||EXTRA9 IN (SELECT PROJECT_GUID||MIN(EXTRA9)  FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE (del_status='0' or del_status is null or del_status='') and status<![CDATA[<>]]>'40' GROUP BY PROJECT_GUID) and (del_status='0' or del_status is null or del_status='') and status<![CDATA[<>]]>'40' AND EXTRA9<![CDATA[>]]>#xyEndDate# )
				</isNotEmpty> 
			)
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ndzj_d">
			(
				record_guid in (SELECT RECORD_GUID FROM ${srmsSchema}.T_KYXM_PROJECT WHERE RECORD_GUID NOT IN (SELECT project_guid FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE (del_status='0' or del_status is null or del_status='') and status<![CDATA[<>]]>'40')) 
				<isNotEmpty prepend=" OR " property="xyStartDate">
				record_guid in (SELECT project_guid  FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE PROJECT_GUID||EXTRA10 IN (SELECT PROJECT_GUID||MAX(EXTRA10)  FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE (del_status='0' or del_status is null or del_status='') and status<![CDATA[<>]]>'40' GROUP BY PROJECT_GUID) and (del_status='0' or del_status is null or del_status='') and status<![CDATA[<>]]>'40' AND EXTRA10<![CDATA[<]]>#xyStartDate# )
				</isNotEmpty>
				<isNotEmpty prepend=" OR " property="xyEndDate">
				record_guid in (SELECT project_guid  FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE PROJECT_GUID||EXTRA9 IN (SELECT PROJECT_GUID||MIN(EXTRA9)  FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE (del_status='0' or del_status is null or del_status='') and status<![CDATA[<>]]>'40' GROUP BY PROJECT_GUID) and (del_status='0' or del_status is null or del_status='') and status<![CDATA[<>]]>'40' AND EXTRA9<![CDATA[>]]>#xyEndDate# )
				</isNotEmpty>
			)
			</isNotEmpty>
			
			<isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
			<isNotEmpty prepend=" or " property="orDynSql">$orDynSql$</isNotEmpty>

		</dynamic>
		<isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*)
		FROM ${srmsSchema}.t_kyxm_project
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="recordGuid">record_guid = #recordGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="serialNo">serial_no = #serialNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCode">project_code = #projectCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">project_name = #projectName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzr">fzr = #fzr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrName">fzr_name = #fzrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrContactTel">fzr_contact_tel = #fzrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxr">lxr = #lxr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrName">lxr_name = #lxrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrContactTel">lxr_contact_tel = #lxrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectZg">project_zg = #projectZg#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectZgName">project_zg_name = #projectZgName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStartDate">project_start_date = #projectStartDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectEndDate">project_end_date = #projectEndDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectType">project_type = #projectType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectLevel">project_level = #projectLevel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ghjsfx">ghjsfx = #ghjsfx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techDomain">tech_domain = #techDomain#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techsx">techsx = #techsx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStatus">project_status = #projectStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signDate">sign_date = #signDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="economyBenefit">economy_benefit = #economyBenefit#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="economyBenefitPfDate">economy_benefit_pf_date =
				#economyBenefitPfDate#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="actualCompleteDate">actual_complete_date = #actualCompleteDate#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="mainProjectGuid">main_project_guid = #mainProjectGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sourceLabel">source_label = #sourceLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="nrOldproSourceGuid">nr_oldpro_source_guid = #nrOldproSourceGuid#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gdDate">gd_date = #gdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jtDate">jt_date = #jtDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">extra1 = #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">extra2 = #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">extra3 = #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">extra4 = #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">extra5 = #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra6">extra6 = #extra6#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra7">extra7 = #extra7#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra8">extra8 = #extra8#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra9">extra9 = #extra9#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra10">extra10 = #extra10#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11">extra11 = #extra11#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra12">extra12 = #extra12#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra13">extra13 = #extra13#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra14">extra14 = #extra14#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra15">extra15 = #extra15#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra16">extra16 = #extra16#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra17">extra17 = #extra17#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra18">extra18 = #extra18#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra19">extra19 = #extra19#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra20">extra20 = #extra20#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra22">extra22 = #extra22#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra21">extra21 = #extra21#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">del_status = #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label = #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">create_date = #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label = #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">update_date = #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label = #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">delete_date = #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">record_version = #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra23">extra23 = #extra23#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra24">extra24 = #extra24#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra25">extra25 = #extra25#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra26">extra26 = #extra26#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra27">extra27 = #extra27#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra28">extra28 = #extra28#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="yanShouDate">yan_shou_date = #yanShouDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra30">extra30 = #extra30#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra31">extra31 = #extra31#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra32">extra32 = #extra32#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra33">extra33 = #extra33#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra34">extra34 = #extra34#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra35">extra35 = #extra35#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra36">extra36 = #extra36#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra37">extra37 = #extra37#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra38">extra38 = #extra38#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra39">extra39 = #extra39#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra40">extra40 = #extra40#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra41">extra41 = #extra41#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra42">extra42 = #extra42#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra43">extra43 = #extra43#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra44">extra44 = #extra44#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra45">extra45 = #extra45#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra46">extra46 = #extra46#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra47">extra47 = #extra47#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra48">extra48 = #extra48#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra49">extra49 = #extra49#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra50">extra50 = #extra50#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra51">extra51 = #extra51#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra52">extra52 = #extra52#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra53">extra53 = #extra53#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra54">extra54 = #extra54#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra55">extra55 = #extra55#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra56">extra56 = #extra56#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra57">extra57 = #extra57#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra58">extra58 = #extra58#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra59">extra59 = #extra59#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra60">extra60 = #extra60#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra61">extra61 = #extra61#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra62">extra62 = #extra62#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra63">extra63 = #extra63#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra64">extra64 = #extra64#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra65">extra65 = #extra65#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra66">extra66 = #extra66#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra67">extra67 = #extra67#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra68">extra68 = #extra68#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra69">extra69 = #extra69#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra70">extra70 = #extra70#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra71">extra71 = #extra71#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra72">extra72 = #extra72#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra73">extra73 = #extra73#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra74">extra74 = #extra74#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra75">extra75 = #extra75#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra76">extra76 = #extra76#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra77">extra77 = #extra77#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra78">extra78 = #extra78#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra79">extra79 = #extra79#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra80">extra80 = #extra80#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectSx">project_sx = #projectSx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cybm">cybm = #cybm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cybmName">cybm_name = #cybmName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ssfs">ssfs = #ssfs#</isNotEmpty>

			<isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
		</dynamic>
	</select>

	<select id="querySjbl" parameterClass="hashmap" resultClass="tkyxmProjectResult" >
		SELECT
		record_guid as "recordGuid" ,
		serial_no as "serialNo" ,
		project_code as "projectCode" ,
		project_name as "projectName" ,
		fzr as "fzr" ,
		fzr_name as "fzrName" ,
		fzr_contact_tel as "fzrContactTel" ,
		lxr as "lxr" ,
		lxr_name as "lxrName" ,
		lxr_contact_tel as "lxrContactTel" ,
		project_zg as "projectZg" ,
		project_zg_name as "projectZgName" ,
		project_start_date as "projectStartDate" ,
		project_end_date as "projectEndDate" ,
		project_type as "projectType" ,
		project_level as "projectLevel" ,
		ghjsfx as "ghjsfx" ,
		tech_domain as "techDomain" ,
		techsx as "techsx" ,
		project_status as "projectStatus" ,
		sign_date as "signDate" ,
		economy_benefit as "economyBenefit" ,
		economy_benefit_pf_date as "economyBenefitPfDate" ,
		actual_complete_date as "actualCompleteDate" ,
		main_project_guid as "mainProjectGuid" ,
		source_label as "sourceLabel" ,
		nr_oldpro_source_guid as "nrOldproSourceGuid" ,
		gd_date as "gdDate" ,
		jt_date as "jtDate" ,
		extra1 as "extra1" ,
		extra2 as "extra2" ,
		extra3 as "extra3" ,
		extra4 as "extra4" ,
		extra5 as "extra5" ,
		extra6 as "extra6" ,
		extra7 as "extra7" ,
		extra8 as "extra8" ,
		extra9 as "extra9" ,
		extra10 as "extra10" ,
		extra11 as "extra11" ,
		extra12 as "extra12" ,
		extra13 as "extra13" ,
		extra14 as "extra14" ,
		extra15 as "extra15" ,
		extra16 as "extra16" ,
		extra17 as "extra17" ,
		extra18 as "extra18" ,
		extra19 as "extra19" ,
		extra20 as "extra20" ,
		extra22 as "extra22" ,
		extra21 as "extra21" ,
		del_status as "delStatus" ,
		create_user_label as "createUserLabel" ,
		create_date as "createDate" ,
		update_user_label as "updateUserLabel" ,
		update_date as "updateDate" ,
		delete_user_label as "deleteUserLabel" ,
		delete_date as "deleteDate" ,
		record_version as "recordVersion" ,
		extra23 as "extra23" ,
		extra24 as "extra24" ,
		extra25 as "extra25" ,
		extra26 as "extra26" ,
		extra27 as "extra27" ,
		extra28 as "extra28" ,
		yan_shou_date as "yanShouDate" ,
		extra30 as "extra30",
		extra31 as "extra31" ,
		extra32 as "extra32" ,
		extra33 as "extra33" ,
		extra34 as "extra34" ,
		extra35 as "extra35" ,
		extra36 as "extra36" ,
		extra37 as "extra37" ,
		extra38 as "extra38" ,
		extra39 as "extra39" ,
		extra40 as "extra40" ,
		extra41 as "extra41" ,
		extra42 as "extra42" ,
		extra43 as "extra43" ,
		extra44 as "extra44" ,
		extra45 as "extra45" ,
		extra46 as "extra46" ,
		extra47 as "extra47" ,
		extra48 as "extra48" ,
		extra49 as "extra49" ,
		extra50 as "extra50" ,
		extra51 as "extra51" ,
		extra52 as "extra52" ,
		extra53 as "extra53" ,
		extra54 as "extra54" ,
		extra55 as "extra55" ,
		extra56 as "extra56" ,
		extra57 as "extra57" ,
		extra58 as "extra58" ,
		extra59 as "extra59" ,
		extra60 as "extra60" ,
		extra61 as "extra61" ,
		extra62 as "extra62" ,
		extra63 as "extra63" ,
		extra64 as "extra64" ,
		extra65 as "extra65" ,
		extra66 as "extra66" ,
		extra67 as "extra67" ,
		extra68 as "extra68" ,
		extra69 as "extra69" ,
		extra70 as "extra70" ,
		extra71 as "extra71" ,
		extra72 as "extra72" ,
		extra73 as "extra73" ,
		extra74 as "extra74" ,
		extra75 as "extra75" ,
		extra76 as "extra76" ,
		extra77 as "extra77" ,
		extra78 as "extra78" ,
		extra79 as "extra79" ,
		extra80 as "extra80" ,
		project_sx as "projectSx",
		cybm as "cybm",
		cybm_name as "cybmName",
		ssfs as "ssfs"
		FROM ${srmsSchema}.t_kyxm_project
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="recordGuid">record_guid = #recordGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="serialNo">serial_no = #serialNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCode">project_code = #projectCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCodeLike">project_code like '%$projectCodeLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">project_name = #projectName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectNameLike">project_name like '%$projectNameLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzr">fzr = #fzr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrName">fzr_name = #fzrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrContactTel">fzr_contact_tel = #fzrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxr">lxr = #lxr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrName">lxr_name = #lxrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrContactTel">lxr_contact_tel = #lxrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectZg">project_zg = #projectZg#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectZgName">project_zg_name = #projectZgName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStartDate">project_start_date = #projectStartDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectEndDate">project_end_date = #projectEndDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectType">project_type = #projectType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectLevel">project_level = #projectLevel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ghjsfx">ghjsfx = #ghjsfx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techDomain">tech_domain = #techDomain#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techsx">techsx = #techsx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStatus">project_status = #projectStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signDate">sign_date = #signDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="economyBenefit">economy_benefit = #economyBenefit#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="economyBenefitPfDate">economy_benefit_pf_date =
				#economyBenefitPfDate#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="actualCompleteDate">actual_complete_date = #actualCompleteDate#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="mainProjectGuid">main_project_guid = #mainProjectGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sourceLabel">source_label = #sourceLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="nrOldproSourceGuid">nr_oldpro_source_guid = #nrOldproSourceGuid#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gdDate">gd_date = #gdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jtDate">jt_date = #jtDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">extra1 = #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">extra2 = #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">extra3 = #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">extra4 = #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">extra5 = #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra6">extra6 = #extra6#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra7">extra7 = #extra7#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra8">extra8 = #extra8#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra9">extra9 = #extra9#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra10">extra10 = #extra10#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11">extra11 = #extra11#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra12">extra12 = #extra12#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra13">extra13 = #extra13#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra14">extra14 = #extra14#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra15">extra15 = #extra15#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra16">extra16 = #extra16#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra17">extra17 = #extra17#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra18">extra18 = #extra18#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra19">extra19 = #extra19#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzDeptLike">extra19 like '%$fzDeptLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra20">extra20 = #extra20#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra22">extra22 = #extra22#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra21">extra21 = #extra21#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">del_status = #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label = #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">create_date = #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label = #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">update_date = #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label = #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">delete_date = #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">record_version = #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra23">extra23 = #extra23#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra24">extra24 = #extra24#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra25">extra25 = #extra25#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra26">extra26 = #extra26#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra27">extra27 = #extra27#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra28">extra28 = #extra28#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="yanShouDate">yan_shou_date = #yanShouDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra30">extra30 = #extra30#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra31">extra31 = #extra31#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra32">extra32 = #extra32#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra33">extra33 = #extra33#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra34">extra34 = #extra34#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra35">extra35 = #extra35#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra36">extra36 = #extra36#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra37">extra37 = #extra37#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra38">extra38 = #extra38#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra39">extra39 = #extra39#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra40">extra40 = #extra40#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra41">extra41 = #extra41#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra42">extra42 = #extra42#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra43">extra43 = #extra43#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra44">extra44 = #extra44#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra45">extra45 = #extra45#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra46">extra46 = #extra46#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra47">extra47 = #extra47#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra48">extra48 = #extra48#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra49">extra49 = #extra49#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra50">extra50 = #extra50#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra51">extra51 = #extra51#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra52">extra52 = #extra52#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra53">extra53 = #extra53#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra54">extra54 = #extra54#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra55">extra55 = #extra55#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra56">extra56 = #extra56#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra57">extra57 = #extra57#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra58">extra58 = #extra58#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra59">extra59 = #extra59#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra60">extra60 = #extra60#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra61">extra61 = #extra61#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra62">extra62 = #extra62#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra63">extra63 = #extra63#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra64">extra64 = #extra64#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra65">extra65 = #extra65#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra66">extra66 = #extra66#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra67">extra67 = #extra67#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra68">extra68 = #extra68#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra69">extra69 = #extra69#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra70">extra70 = #extra70#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra71">extra71 = #extra71#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra72">extra72 = #extra72#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra73">extra73 = #extra73#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra74">extra74 = #extra74#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra75">extra75 = #extra75#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra76">extra76 = #extra76#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra77">extra77 = #extra77#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra78">extra78 = #extra78#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra79">extra79 = #extra79#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra80">extra80 = #extra80#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectSx">project_sx = #projectSx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cybm">cybm = #cybm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cybmName">cybm_name = #cybmName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ssfs">ssfs = #ssfs#</isNotEmpty>

			<isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
		</dynamic>
	</select>

	<insert id="insert" parameterClass="tkyxmProjectResult">
		INSERT INTO ${srmsSchema}.t_kyxm_project (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="recordGuid">record_guid</isNotEmpty>
			<isNotEmpty prepend=" , " property="serialNo">serial_no</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectCode">project_code</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectName">project_name</isNotEmpty>
			<isNotEmpty prepend=" , " property="fzr">fzr</isNotEmpty>
			<isNotEmpty prepend=" , " property="fzrName">fzr_name</isNotEmpty>
			<isNotEmpty prepend=" , " property="fzrContactTel">fzr_contact_tel</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxr">lxr</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrName">lxr_name</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrContactTel">lxr_contact_tel</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectZg">project_zg</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectZgName">project_zg_name</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectStartDate">project_start_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectEndDate">project_end_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectType">project_type</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectLevel">project_level</isNotEmpty>
			<isNotEmpty prepend=" , " property="ghjsfx">ghjsfx</isNotEmpty>
			<isNotEmpty prepend=" , " property="techDomain">tech_domain</isNotEmpty>
			<isNotEmpty prepend=" , " property="techsx">techsx</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectStatus">project_status</isNotEmpty>
			<isNotEmpty prepend=" , " property="signDate">sign_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="economyBenefit">economy_benefit</isNotEmpty>
			<isNotEmpty prepend=" , " property="economyBenefitPfDate">economy_benefit_pf_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="actualCompleteDate">actual_complete_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="mainProjectGuid">main_project_guid</isNotEmpty>
			<isNotEmpty prepend=" , " property="sourceLabel">source_label</isNotEmpty>
			<isNotEmpty prepend=" , " property="nrOldproSourceGuid">nr_oldpro_source_guid</isNotEmpty>
			<isNotEmpty prepend=" , " property="gdDate">gd_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="jtDate">jt_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">extra1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">extra2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">extra3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">extra4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">extra5</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra6">extra6</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra7">extra7</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra8">extra8</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra9">extra9</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra10">extra10</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra11">extra11</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra12">extra12</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra13">extra13</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra14">extra14</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra15">extra15</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra16">extra16</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra17">extra17</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra18">extra18</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra19">extra19</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra20">extra20</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra22">extra22</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra21">extra21</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">del_status</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">create_user_label</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">create_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">update_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">delete_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">record_version</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra23">extra23</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra24">extra24</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra25">extra25</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra26">extra26</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra27">extra27</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra28">extra28</isNotEmpty>
			<isNotEmpty prepend=" , " property="yanShouDate">yan_shou_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra30">extra30</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra31">extra31</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra32">extra32</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra33">extra33</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra34">extra34</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra35">extra35</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra36">extra36</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra37">extra37</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra38">extra38</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra39">extra39</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra40">extra40</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra41">extra41</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra42">extra42</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra43">extra43</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra44">extra44</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra45">extra45</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra46">extra46</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra47">extra47</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra48">extra48</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra49">extra49</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra50">extra50</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra51">extra51</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra52">extra52</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra53">extra53</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra54">extra54</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra55">extra55</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra56">extra56</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra57">extra57</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra58">extra58</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra59">extra59</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra60">extra60</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra61">extra61</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra62">extra62</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra63">extra63</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra64">extra64</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra65">extra65</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra66">extra66</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra67">extra67</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra68">extra68</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra69">extra69</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra70">extra70</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra71">extra71</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra72">extra72</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra73">extra73</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra74">extra74</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra75">extra75</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra76">extra76</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra77">extra77</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra78">extra78</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra79">extra79</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra80">extra80</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectSx">project_sx</isNotEmpty>
			<isNotEmpty prepend=" , " property="cybm">cybm</isNotEmpty>
			<isNotEmpty prepend=" , " property="cybmName">cybm_name</isNotEmpty>
			<isNotEmpty prepend=" , " property="ssfs">ssfs</isNotEmpty>


		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="recordGuid">#recordGuid#</isNotEmpty>
			<isNotEmpty prepend=" , " property="serialNo">#serialNo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectCode">#projectCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectName">#projectName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fzr">#fzr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fzrName">#fzrName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fzrContactTel">#fzrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxr">#lxr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrName">#lxrName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrContactTel">#lxrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectZg">#projectZg#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectZgName">#projectZgName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectStartDate">#projectStartDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectEndDate">#projectEndDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectType">#projectType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectLevel">#projectLevel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="ghjsfx">#ghjsfx#</isNotEmpty>
			<isNotEmpty prepend=" , " property="techDomain">#techDomain#</isNotEmpty>
			<isNotEmpty prepend=" , " property="techsx">#techsx#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectStatus">#projectStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="signDate">#signDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="economyBenefit">#economyBenefit#</isNotEmpty>
			<isNotEmpty prepend=" , " property="economyBenefitPfDate">#economyBenefitPfDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="actualCompleteDate">#actualCompleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="mainProjectGuid">#mainProjectGuid#</isNotEmpty>
			<isNotEmpty prepend=" , " property="sourceLabel">#sourceLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="nrOldproSourceGuid">#nrOldproSourceGuid#</isNotEmpty>
			<isNotEmpty prepend=" , " property="gdDate">#gdDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="jtDate">#jtDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra6">#extra6#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra7">#extra7#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra8">#extra8#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra9">#extra9#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra10">#extra10#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra11">#extra11#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra12">#extra12#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra13">#extra13#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra14">#extra14#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra15">#extra15#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra16">#extra16#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra17">#extra17#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra18">#extra18#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra19">#extra19#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra20">#extra20#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra22">#extra22#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra21">#extra21#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra23">#extra23#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra24">#extra24#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra25">#extra25#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra26">#extra26#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra27">#extra27#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra28">#extra28#</isNotEmpty>
			<isNotEmpty prepend=" , " property="yanShouDate">#yanShouDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra30">#extra30#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra31">#extra31#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra32">#extra32#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra33">#extra33#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra34">#extra34#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra35">#extra35#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra36">#extra36#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra37">#extra37#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra38">#extra38#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra39">#extra39#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra40">#extra40#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra41">#extra41#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra42">#extra42#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra43">#extra43#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra44">#extra44#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra45">#extra45#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra46">#extra46#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra47">#extra47#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra48">#extra48#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra49">#extra49#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra50">#extra50#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra51">#extra51#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra52">#extra52#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra53">#extra53#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra54">#extra54#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra55">#extra55#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra56">#extra56#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra57">#extra57#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra58">#extra58#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra59">#extra59#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra60">#extra60#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra61">#extra61#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra62">#extra62#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra63">#extra63#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra64">#extra64#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra65">#extra65#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra66">#extra66#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra67">#extra67#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra68">#extra68#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra69">#extra69#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra70">#extra70#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra71">#extra71#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra72">#extra72#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra73">#extra73#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra74">#extra74#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra75">#extra75#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra76">#extra76#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra77">#extra77#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra78">#extra78#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra79">#extra79#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra80">#extra80#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectSx">#projectSx#</isNotEmpty>
			<isNotEmpty prepend=" , " property="cybm">#cybm#</isNotEmpty>
			<isNotEmpty prepend=" , " property="cybmName">#cybmName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="ssfs">#ssfs#</isNotEmpty>

		</dynamic>
		)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${srmsSchema}.t_kyxm_project
		WHERE 
		    record_guid = #value#

	</delete>

	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM ${srmsSchema}.t_kyxm_project
		WHERE
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="recordGuid">record_guid=#recordGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="serialNo">serial_no=#serialNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCode">project_code=#projectCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">project_name=#projectName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzr">fzr=#fzr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrName">fzr_name=#fzrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrContactTel">fzr_contact_tel=#fzrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxr">lxr=#lxr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrName">lxr_name=#lxrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrContactTel">lxr_contact_tel=#lxrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectZg">project_zg=#projectZg#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectZgName">project_zg_name=#projectZgName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStartDate">project_start_date=#projectStartDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectEndDate">project_end_date=#projectEndDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectType">project_type=#projectType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectLevel">project_level=#projectLevel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ghjsfx">ghjsfx=#ghjsfx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techDomain">tech_domain=#techDomain#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techsx">techsx=#techsx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStatus">project_status=#projectStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signDate">sign_date=#signDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="economyBenefit">economy_benefit=#economyBenefit#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="economyBenefitPfDate">economy_benefit_pf_date=#economyBenefitPfDate#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="actualCompleteDate">actual_complete_date=#actualCompleteDate#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="mainProjectGuid">main_project_guid=#mainProjectGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sourceLabel">source_label=#sourceLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="nrOldproSourceGuid">nr_oldpro_source_guid=#nrOldproSourceGuid#
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gdDate">gd_date=#gdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jtDate">jt_date=#jtDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">extra1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">extra2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">extra3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">extra4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">extra5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra6">extra6=#extra6#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra7">extra7=#extra7#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra8">extra8=#extra8#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra9">extra9=#extra9#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra10">extra10=#extra10#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11">extra11=#extra11#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra12">extra12=#extra12#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra13">extra13=#extra13#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra14">extra14=#extra14#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra15">extra15=#extra15#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra16">extra16=#extra16#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra17">extra17=#extra17#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra18">extra18=#extra18#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra19">extra19=#extra19#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra20">extra20=#extra20#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra22">extra22=#extra22#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra21">extra21=#extra21#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">del_status=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">create_date=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">update_date=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra23">extra23=#extra23#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra24">extra24=#extra24#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra25">extra25=#extra25#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra26">extra26=#extra26#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra27">extra27=#extra27#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra28">extra28=#extra28#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="yanShouDate">yan_shou_date=#yanShouDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra30">extra30=#extra30#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra31">extra31 = #extra31#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra32">extra32 = #extra32#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra33">extra33 = #extra33#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra34">extra34 = #extra34#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra35">extra35 = #extra35#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra36">extra36 = #extra36#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra37">extra37 = #extra37#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra38">extra38 = #extra38#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra39">extra39 = #extra39#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra40">extra40 = #extra40#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra41">extra41 = #extra41#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra42">extra42 = #extra42#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra43">extra43 = #extra43#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra44">extra44 = #extra44#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra45">extra45 = #extra45#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra46">extra46 = #extra46#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra47">extra47 = #extra47#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra48">extra48 = #extra48#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra49">extra49 = #extra49#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra50">extra50 = #extra50#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra51">extra51 = #extra51#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra52">extra52 = #extra52#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra53">extra53 = #extra53#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra54">extra54 = #extra54#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra55">extra55 = #extra55#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra56">extra56 = #extra56#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra57">extra57 = #extra57#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra58">extra58 = #extra58#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra59">extra59 = #extra59#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra60">extra60 = #extra60#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra61">extra61 = #extra61#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra62">extra62 = #extra62#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra63">extra63 = #extra63#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra64">extra64 = #extra64#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra65">extra65 = #extra65#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra66">extra66 = #extra66#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra67">extra67 = #extra67#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra68">extra68 = #extra68#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra69">extra69 = #extra69#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra70">extra70 = #extra70#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra71">extra71 = #extra71#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra72">extra72 = #extra72#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra73">extra73 = #extra73#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra74">extra74 = #extra74#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra75">extra75 = #extra75#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra76">extra76 = #extra76#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra77">extra77 = #extra77#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra78">extra78 = #extra78#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra79">extra79 = #extra79#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra80">extra80 = #extra80#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectSx">project_sx = #projectSx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cybm">cybm = #cybm#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cybmName">cybm_name = #cybmName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ssfs">ssfs = #ssfs#</isNotEmpty>

		</dynamic>
	</delete>

	<update id="update" parameterClass="tkyxmProjectResult">
		UPDATE ${srmsSchema}.t_kyxm_project
		SET
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="serialNo">serial_no=#serialNo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectCode">project_code=#projectCode#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectName">project_name=#projectName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fzr">fzr=#fzr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fzrName">fzr_name=#fzrName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fzrContactTel">fzr_contact_tel=#fzrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxr">lxr=#lxr#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrName">lxr_name=#lxrName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="lxrContactTel">lxr_contact_tel=#lxrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectZg">project_zg=#projectZg#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectZgName">project_zg_name=#projectZgName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectStartDate">project_start_date=#projectStartDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectEndDate">project_end_date=#projectEndDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectType">project_type=#projectType#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectLevel">project_level=#projectLevel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="ghjsfx">ghjsfx=#ghjsfx#</isNotEmpty>
			<isNotEmpty prepend=" , " property="techDomain">tech_domain=#techDomain#</isNotEmpty>
			<isNotEmpty prepend=" , " property="techsx">techsx=#techsx#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectStatus">project_status=#projectStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="signDate">sign_date=#signDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="economyBenefit">economy_benefit=#economyBenefit#</isNotEmpty>
			<isNotEmpty prepend=" , " property="economyBenefitPfDate">economy_benefit_pf_date=#economyBenefitPfDate#
			</isNotEmpty>
			<isNotEmpty prepend=" , " property="actualCompleteDate">actual_complete_date=#actualCompleteDate#
			</isNotEmpty>
			<isNotEmpty prepend=" , " property="mainProjectGuid">main_project_guid=#mainProjectGuid#</isNotEmpty>
			<isNotEmpty prepend=" , " property="sourceLabel">source_label=#sourceLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="nrOldproSourceGuid">nr_oldpro_source_guid=#nrOldproSourceGuid#
			</isNotEmpty>
			<isNotEmpty prepend=" , " property="gdDate">gd_date=#gdDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="jtDate">jt_date=#jtDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">extra1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">extra2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">extra3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">extra4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">extra5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra6">extra6=#extra6#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra7">extra7=#extra7#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra8">extra8=#extra8#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra9">extra9=#extra9#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra10">extra10=#extra10#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra11">extra11=#extra11#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra12">extra12=#extra12#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra13">extra13=#extra13#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra14">extra14=#extra14#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra15">extra15=#extra15#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra16">extra16=#extra16#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra17">extra17=#extra17#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra18">extra18=#extra18#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra19">extra19=#extra19#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra20">extra20=#extra20#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra22">extra22=#extra22#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra21">extra21=#extra21#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">del_status=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">create_date=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">update_date=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra23">extra23=#extra23#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra24">extra24=#extra24#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra25">extra25=#extra25#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra26">extra26=#extra26#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra27">extra27=#extra27#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra28">extra28=#extra28#</isNotEmpty>
			<isNotEmpty prepend=" , " property="yanShouDate">yan_shou_date=#yanShouDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra30">extra30=#extra30#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra31">extra31=#extra31#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra32">extra32=#extra32#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra33">extra33=#extra33#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra34">extra34=#extra34#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra35">extra35=#extra35#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra36">extra36=#extra36#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra37">extra37=#extra37#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra38">extra38=#extra38#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra39">extra39=#extra39#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra40">extra40=#extra40#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra41">extra41=#extra41#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra42">extra42=#extra42#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra43">extra43=#extra43#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra44">extra44=#extra44#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra45">extra45=#extra45#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra46">extra46=#extra46#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra47">extra47=#extra47#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra48">extra48=#extra48#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra49">extra49=#extra49#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra50">extra50=#extra50#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra51">extra51=#extra51#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra52">extra52=#extra52#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra53">extra53=#extra53#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra54">extra54=#extra54#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra55">extra55=#extra55#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra56">extra56=#extra56#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra57">extra57=#extra57#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra58">extra58=#extra58#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra59">extra59=#extra59#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra60">extra60=#extra60#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra61">extra61=#extra61#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra62">extra62=#extra62#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra63">extra63=#extra63#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra64">extra64=#extra64#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra65">extra65=#extra65#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra66">extra66=#extra66#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra67">extra67=#extra67#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra68">extra68=#extra68#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra69">extra69=#extra69#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra70">extra70=#extra70#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra71">extra71=#extra71#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra72">extra72=#extra72#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra73">extra73=#extra73#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra74">extra74=#extra74#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra75">extra75=#extra75#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra76">extra76=#extra76#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra77">extra77=#extra77#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra78">extra78=#extra78#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra79">extra79=#extra79#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra80">extra80=#extra80#</isNotEmpty>
			<isNotEmpty prepend=" , " property="projectSx">project_sx=#projectSx#</isNotEmpty>
			<isNotEmpty prepend=" , " property="cybm">cybm=#cybm#</isNotEmpty>
			<isNotEmpty prepend=" , " property="cybmName">cybm_name=#cybmName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="ssfs">ssfs=#ssfs#</isNotEmpty>


		</dynamic>                                              
		WHERE                                                   
		record_guid =#recordGuid#                               
	</update>                                                   
                                                                
	<!-- 综合查询 -->
	<select id="queryCompre" parameterClass="hashmap" resultClass="hashmap" remapResults="true">
		SELECT
			p.record_guid as "recordGuid" ,
			p.serial_no as "serialNo" ,
			p.project_code as "projectCode" ,
			p.project_name as "projectName" ,
			p.fzr as "fzr" ,
			p.fzr_name as "fzrName" ,
			p.fzr_contact_tel as "fzrContactTel" ,
			p.lxr as "lxr" ,
			p.lxr_name as "lxrName" ,
			p.lxr_contact_tel as "lxrContactTel" ,
			p.project_zg as "projectZg" ,
			p.project_zg_name as "projectZgName" ,
			p.project_start_date as "projectStartDate" ,
			p.project_end_date as "projectEndDate" ,
			p.project_type as "projectType" ,
			p.project_level as "projectLevel" ,
			p.ghjsfx as "ghjsfx" ,
			p.tech_domain as "techDomain" ,
			p.techsx as "techsx" ,
			p.project_status as "projectStatus" ,
			p.sign_date as "signDate" ,
			p.economy_benefit as "economyBenefit" ,
			p.economy_benefit_pf_date as "economyBenefitPfDate" ,
			p.actual_complete_date as "actualCompleteDate" ,
			p.main_project_guid as "mainProjectGuid" ,
			p.source_label as "sourceLabel" ,
			p.nr_oldpro_source_guid as "nrOldproSourceGuid" ,
			p.gd_date as "gdDate" ,
			p.jt_date as "jtDate" ,
			p.extra1 as "extra1" ,
			p.extra2 as "extra2" ,
			p.extra3 as "extra3" ,
			p.extra4 as "extra4" ,
			p.extra5 as "extra5" ,
			p.extra6 as "extra6" ,
			p.extra7 as "extra7" ,
			p.extra8 as "extra8" ,
			p.extra9 as "extra9" ,
			p.extra10 as "extra10" ,
			p.extra11 as "extra11" ,
			p.extra12 as "extra12" ,
			p.extra13 as "extra13" ,
			p.extra14 as "extra14" ,
			p.extra15 as "extra15" ,
			p.extra16 as "extra16" ,
			p.extra17 as "extra17" ,
			p.extra18 as "extra18" ,
			p.extra19 as "extra19" ,
			p.extra20 as "extra20" ,
			p.extra22 as "extra22" ,
			p.extra21 as "extra21" ,
			p.del_status as "delStatus" ,
			p.create_user_label as "createUserLabel" ,
			p.create_date as "createDate" ,
			p.update_user_label as "updateUserLabel" ,
			p.update_date as "updateDate" ,
			p.delete_user_label as "deleteUserLabel" ,
			p.delete_date as "deleteDate" ,
			p.extra23 as "extra23" ,
			p.extra24 as "extra24" ,
			p.extra25 as "extra25" ,
			p.extra26 as "extra26" ,
			p.extra27 as "extra27" ,
			p.extra28 as "extra28" ,
			p.yan_shou_date as "yanShouDate" ,
			p.extra30 as "extra30" ,
			p.extra31 as "extra31" ,
			p.extra32 as "extra32" ,
			p.extra33 as "extra33" ,
			p.extra34 as "extra34" ,
			p.extra35 as "extra35" ,
			p.extra36 as "extra36" ,
			p.extra37 as "extra37" ,
			p.extra38 as "extra38" ,
			p.extra39 as "extra39" ,
			p.extra40 as "extra40" ,
			p.extra41 as "extra41" ,
			p.extra42 as "extra42" ,
			p.extra43 as "extra43" ,
			p.extra44 as "extra44" ,
			p.extra45 as "extra45" ,
			p.extra46 as "extra46" ,
			p.extra47 as "extra47" ,
			p.extra48 as "extra48" ,
			p.extra49 as "extra49" ,
			p.extra50 as "extra50" ,
			p.extra51 as "extra51" ,
			p.extra52 as "extra52" ,
			p.extra53 as "extra53" ,
			p.extra54 as "extra54" ,
			p.extra55 as "extra55" ,
			p.extra56 as "extra56" ,
			p.extra57 as "extra57" ,
			p.extra58 as "extra58" ,
			p.extra59 as "extra59" ,
			p.extra60 as "extra60" ,
			p.extra61 as "extra61" ,
			p.extra62 as "extra62" ,
			p.extra63 as "extra63" ,
			p.extra64 as "extra64" ,
			p.extra65 as "extra65" ,
			p.extra66 as "extra66" ,
			p.extra67 as "extra67" ,
			p.extra68 as "extra68" ,
			p.extra69 as "extra69" ,
			p.extra70 as "extra70" ,
			p.extra71 as "extra71" ,
			p.extra72 as "extra72" ,
			p.extra73 as "extra73" ,
			p.extra74 as "extra74" ,
			p.extra75 as "extra75" ,
			p.extra76 as "extra76" ,
			p.extra77 as "extra77" ,
			p.extra78 as "extra78" ,
			p.extra79 as "extra79" ,
			p.extra80 as "extra80" ,
			p.project_sx as "projectSx",
			p.cybm as "cybm",
			p.cybm_name as "cybmName",
			p.ssfs as "ssfs",
			d1.DICT_NAME as "projectTypeName",
			d2.DICT_NAME as "projectLevelName",
			d3.DICT_NAME as "extra11Name",
			d4.DICT_NAME as "projectStatusName",
			vpl.CURRENT_ACTIVITY_NAME AS "currentActivityNamePro",
			<isNotEmpty prepend="" property="jb">
			j.RECORD_GUID as "jguid",
			j.FYEAR  as "fyear" ,
			j.QUARTER  as "quarter" ,
			j.EXTRA2 as "extra2j",
			j.zjdjs as "zjdjs",
			j.edit_User as "editUser",
			j.edit_Date as "editDate",
			w.CURRENT_ACTIVITY_NAME AS "currentActivityNamej",
			</isNotEmpty>
			<isNotEmpty prepend="" property="zj">
			zj.RECORD_GUID as "nguid",
			zj.EVALUATE_DATE as "evaluateDate",
			w.CURRENT_ACTIVITY_NAME AS "currentActivityNamen",
			w.CURRENT_OPERATOR AS "currentOpertorzjr",
			</isNotEmpty>
			<isNotEmpty prepend="" property="bg">
			c.RECORD_GUID as "cguid",
			c.EXTRA6 as "extra6c",
			w.CURRENT_ACTIVITY_NAME AS "currentActivityNameb",
			w.CURRENT_OPERATOR AS "currentOpertorbg",
			</isNotEmpty>
			<isNotEmpty prepend="" property="gyx">
			g.RECORD_GUID as "gguid",
			g.EXTRA1 as "extra1g",
			g.PH  as "ph",
			w.CURRENT_ACTIVITY_NAME AS "currentActivityNamegyyz",
			w.CURRENT_OPERATOR AS "currentOpertorgyyz",
			</isNotEmpty>
			<isNotEmpty prepend="" property="bzx">
			b.RECORD_GUID as "bzxGuid" ,
			b.STANDAER_NAME as "standaerName",
			w.CURRENT_ACTIVITY_NAME AS "currentActivityNamebzx",
			w.CURRENT_OPERATOR AS "currentOpertorbzx",
			</isNotEmpty>
			<isNotEmpty prepend="" property="hpg">
			h.RECORD_GUID as "hguid",
			h.EXTRA1 as "extra1h",
			w.CURRENT_ACTIVITY_NAME AS "currentActivityNamehpg",
			w.CURRENT_OPERATOR AS "currentOpertorhpg",
			</isNotEmpty>
			<isNotEmpty prepend="" property="sj">
			sj.RECORD_GUID as "sjguid",
			w.CURRENT_ACTIVITY_NAME AS "currentActivityNamesj",
			w.CURRENT_OPERATOR AS "currentOpertorsj",
			</isNotEmpty>
			<isNotEmpty prepend="" property="nd">
			ndzj.RECORD_GUID as "ndzjguid",
			ndzj.STATUS as "ndstatus",
			ndzj.BZJZRQ as "bzjzrq",
			w.CURRENT_ACTIVITY_NAME AS "ndcurrentActivityName",
			</isNotEmpty>
			<isNotEmpty prepend="" property="ys">
			p.jt_date as "jtDate",
			pen.record_guid as "penGuid",
			jtbg.extra2 as "ysextra2",
			</isNotEmpty>
			<isNotEmpty prepend="" property="ndjh">
			yp.year_plan_no as "yearPlanNo",
			yp.PLAN_YEAR as "planYear",
			</isNotEmpty>
			<isNotEmpty prepend="" property="export">
			case
				when (wjt.current_Operator is not null and wjt.current_Operator<![CDATA[<>]]>'')  then wjt.current_Operator
				else wp.current_Operator
			end as "currentOperator",
			NVL2(jb.project_guid,'有','无')  AS "hasJb",
			</isNotEmpty>
			vpl.CURRENT_OPERATOR  AS "currentOperator",
			p.record_version as "recordVersion"
		FROM ${srmsSchema}.t_kyxm_project p
		left join ${ggmkSchema}.T_MPTY_DICT d1 on d1.PARENT_DICT_ID = (SELECT  DICT_ID FROM ${ggmkSchema}.T_MPTY_DICT  WHERE BUSINESS_TYPE = 'KYXM' AND DICT_VALUE ='project_type') AND d1.DICT_VALUE = p.project_type
		left join ${ggmkSchema}.T_MPTY_DICT d2 on d2.PARENT_DICT_ID = (SELECT  DICT_ID FROM ${ggmkSchema}.T_MPTY_DICT  WHERE BUSINESS_TYPE = 'KYXM' AND DICT_VALUE ='project_level') AND d2.DICT_VALUE = p.project_level
		left join ${ggmkSchema}.T_MPTY_DICT d3 on d3.PARENT_DICT_ID = (SELECT  DICT_ID FROM ${ggmkSchema}.T_MPTY_DICT  WHERE BUSINESS_TYPE = 'KYXM' AND DICT_VALUE ='project_execute_way') AND d3.DICT_VALUE = p.extra11
		left join ${ggmkSchema}.T_MPTY_DICT d4 on d4.PARENT_DICT_ID = (SELECT  DICT_ID FROM ${ggmkSchema}.T_MPTY_DICT  WHERE BUSINESS_TYPE = 'KYXM' AND DICT_VALUE ='project_status') AND d4.DICT_VALUE = p.project_status
		<isNotEmpty prepend="" property="jb">
			inner join ${srmsSchema}.T_KYXM_XMJB j on j.PROJECT_GUID = p.record_guid and j.DEL_STATUS = '0'
			inner join ${ggmkSchema}.T_MPWF_FLOW_INFO w on j.RECORD_GUID = w.BUSINESS_ID
		</isNotEmpty>
		<isNotEmpty prepend="" property="zj">
			inner join ${srmsSchema}.T_KYXM_MIDDLE_EVALUATE_NOTICE zj  on zj.PROJECT_GUID = p.record_guid and zj.DEL_STATUS = '0'
			inner join ${ggmkSchema}.T_MPWF_FLOW_INFO w on zj.RECORD_GUID = w.BUSINESS_ID
		</isNotEmpty>
		<isNotEmpty prepend="" property="bg">
			inner join ${srmsSchema}.T_KYXM_PLAN_TASK_CHANGE   c  on c.PROJECT_GUID = p.record_guid and c.DEL_STATUS = '0'
			inner join ${ggmkSchema}.T_MPWF_FLOW_INFO w on c.RECORD_GUID = w.BUSINESS_ID
		</isNotEmpty>
		<isNotEmpty prepend="" property="gyx">
			inner join ${srmsSchema}.T_KYXM_GYYZSQ g  on g.PROJECT_GUID = p.record_guid and g.DEL_STATUS = '0'
			inner join ${ggmkSchema}.T_MPWF_FLOW_INFO w on g.RECORD_GUID = w.BUSINESS_ID
		</isNotEmpty>
		<isNotEmpty prepend="" property="bzx">
			inner join ${srmsSchema}.T_KYXM_BZXJSSQ   b  on b.PROJECT_GUID = p.record_guid and b.DEL_STATUS = '0'
			inner join ${ggmkSchema}.T_MPWF_FLOW_INFO w on b.RECORD_GUID = w.BUSINESS_ID
		</isNotEmpty>
		<isNotEmpty prepend="" property="hpg">
			inner join ${srmsSchema}.T_KYXM_XMHPG   h  on h.PROJECT_GUID = p.record_guid and h.DEL_STATUS = '0'
			inner join ${ggmkSchema}.T_MPWF_FLOW_INFO w on h.RECORD_GUID = w.BUSINESS_ID
		</isNotEmpty>
		<isNotEmpty prepend="" property="sj">
			inner join ${srmsSchema}.T_KYXM_BMZGXMSJ sj  on sj.PROJECT_GUID = p.record_guid and sj.DEL_STATUS = '0'
			inner join ${ggmkSchema}.T_MPWF_FLOW_INFO w on sj.RECORD_GUID = w.BUSINESS_ID
		</isNotEmpty>
		<isNotEmpty prepend="" property="nd">
			inner join ${srmsSchema}.T_KYXM_JPGNDZJ ndzj  on ndzj.PROJECT_GUID = p.record_guid and ndzj.DEL_STATUS = '0'
			inner join ${ggmkSchema}.T_MPWF_FLOW_INFO w on ndzj.RECORD_GUID = w.BUSINESS_ID
		</isNotEmpty>
		<isNotEmpty prepend="" property="ys">
		    inner join ${srmsSchema}.T_KYXM_XMJTBG jtbg on jtbg.PROJECT_GUID = p.record_guid and jtbg.DEL_STATUS = '0'
			inner join ${srmsSchema}.T_KYXM_PROJECT_END_NOTICE pen  on pen.PROJECT_GUID = p.record_guid and pen.DEL_STATUS = '0'
			inner join ${ggmkSchema}.T_MPWF_FLOW_INFO w on pen.RECORD_GUID = w.BUSINESS_ID
		</isNotEmpty>
		<isNotEmpty prepend="" property="export">
			left join ${ggmkSchema}.T_MPWF_FLOW_INFO wp on wp.BUSINESS_ID = p.RECORD_GUID and wp.FLOW_STATE = 'active'
			left join ${srmsSchema}.T_KYXM_XMJTBG jt on jt.PROJECT_GUID = p.RECORD_GUID and jt.DEL_STATUS = '0'
			left join ${ggmkSchema}.T_MPWF_FLOW_INFO wjt on wjt.BUSINESS_ID = jt.RECORD_GUID and wjt.FLOW_STATE = 'active'
			left join (SELECT PROJECT_GUID  FROM ${srmsSchema}.T_KYXM_XMJB GROUP BY PROJECT_GUID) jb ON jb.PROJECT_GUID = p.RECORD_GUID
		</isNotEmpty>
		<isNotEmpty prepend="" property="ndjh"><!-- 年度计划两种表 -->
		inner join (SELECT PROJECT_ID ,PLAN_YEAR,YEAR_PLAN_NO FROM ${srmsSchema}.T_KYXM_NDJHSB UNION ALL SELECT PROJECT_guid AS PROJECT_ID,PLAN_YEAR,YEAR_PLAN_NO FROM ${srmsSchema}.T_KYXM_NDJHXCP) as yp on yp.PROJECT_ID=p.record_guid <isNotEmpty prepend=" AND " property="planYear">yp.PLAN_YEAR = #planYear#</isNotEmpty>
		</isNotEmpty>
			left join ${srmsSchema}.V_KYXM_PRO_LINE vpl on vpl.PROJECT_GUID = p.RECORD_GUID and vpl.FLOW_STATE ='active' and vpl.flow_code not in ('KYXM_JT_SUB1')
		<isNotEmpty prepend="" property="projectXmsx">
			    LEFT JOIN kjgl.T_KYXM_NDJHSB nd ON
		nd.PROJECT_ID = p.RECORD_GUID
		</isNotEmpty>
			    <dynamic prepend="WHERE">
					<isNotEmpty prepend=" AND " property="projectXmsx">nd.PROJECT_XMSX  = #projectXmsx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectSx">p.project_sx = #projectSx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordGuid">p.record_guid = #recordGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="serialNo">p.serial_no = #serialNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="serialNoLike">p.serial_no like '%$serialNoLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCode">p.project_code = #projectCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectCodeLike">p.project_code like '%$projectCodeLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectName">p.project_name = #projectName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectNameLike">p.project_name like '%$projectNameLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzr">p.fzr = #fzr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrLike">p.fzr like '%$fzrLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrNameLike">p.fzr_name like '%$fzrNameLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzrContactTel">p.fzr_contact_tel = #fzrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxr">p.lxr = #lxr#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrName">p.lxr_name = #lxrName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="lxrContactTel">p.lxr_contact_tel = #lxrContactTel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectZg">p.project_zg = #projectZg#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectZgLike">p.project_zg like '%$projectZgLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectZgName">p.project_zg_name = #projectZgName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStartDate">p.project_start_date = #projectStartDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStartDateMin">p.project_start_date >= #projectStartDateMin#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStartDateMax">p.project_start_date <![CDATA[<=]]> #projectStartDateMax#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectEndDate">p.project_end_date = #projectEndDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectEndDateMin">p.project_end_date >= #projectEndDateMin#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectEndDateMax">p.project_end_date <![CDATA[<=]]> #projectEndDateMax#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectType">p.project_type = #projectType#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectLevel">p.project_level = #projectLevel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ghjsfx">p.ghjsfx = #ghjsfx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techDomain">p.tech_domain = #techDomain#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="techsx">p.techsx = #techsx#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStatus">p.project_status = #projectStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signDate">p.sign_date = #signDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signDateMin">p.sign_date >= #signDateMin#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="signDateMax">p.sign_date <![CDATA[<=]]> #signDateMax#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="economyBenefit">p.economy_benefit = #economyBenefit#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="economyBenefitPfDate">p.economy_benefit_pf_date =#economyBenefitPfDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="actualCompleteDate">p.actual_complete_date = #actualCompleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="mainProjectGuid">p.main_project_guid = #mainProjectGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="mainProjectGuids"> p.MAIN_PROJECT_GUID IS not NULL</isNotEmpty>
			<isNotEmpty prepend=" AND " property="sourceLabel">p.source_label = #sourceLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="nrOldproSourceGuid">p.nr_oldpro_source_guid = #nrOldproSourceGuid#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gdDate">p.gd_date = #gdDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jtDate">p.jt_date = #jtDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jtDateMin">p.jt_date >= #jtDateMin#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jtDateMax">p.jt_date <![CDATA[<=]]> #jtDateMax#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">p.extra1 = #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">p.extra2 = #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">p.extra3 = #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">p.extra4 = #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">p.extra5 = #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra6">p.extra6 = #extra6#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra7">p.extra7 = #extra7#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra8">p.extra8 = #extra8#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="glDeptLike">p.extra20 like '%$glDeptLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fzDeptLike">p.extra19 like '%$fzDeptLike$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra9">p.extra9 = #extra9#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra10">p.extra10 = #extra10#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11">p.extra11 = #extra11#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra12">p.extra12 = #extra12#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra13">p.extra13 = #extra13#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra14">p.extra14 = #extra14#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra15">p.extra15 = #extra15#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra16">p.extra16 = #extra16#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra17">p.extra17 = #extra17#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra18">p.extra18 = #extra18#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra18Min">p.extra18 >= #extra18Min#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra18Max">p.extra18 <![CDATA[<=]]> #extra18Max#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra19">p.extra19 = #extra19#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra20">p.extra20 = #extra20#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra22">p.extra22 = #extra22#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra21">p.extra21 = #extra21#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">p.del_status = #delStatus#</isNotEmpty>
			<isEmpty prepend=" AND " property="delStatus">(p.del_status='0' or p.del_status is null or p.del_status='')</isEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">p.create_user_label = #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">p.create_date = #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">p.update_user_label = #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">p.update_date = #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">p.delete_user_label = #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">p.delete_date = #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">p.record_version = #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra23">p.extra23 = #extra23#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra24">p.extra24 = #extra24#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra25">p.extra25 = #extra25#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra26">p.extra26 = #extra26#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra27">p.extra27 = #extra27#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra28">p.extra28 = #extra28#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="yanShouDate">p.yan_shou_date = #yanShouDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra30">p.extra30 = #extra30#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra31">p.extra31 = #extra31#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra32">p.extra32 = #extra32#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra33">p.extra33 = #extra33#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra34">p.extra34 = #extra34#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra35">p.extra35 = #extra35#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra36">p.extra36 = #extra36#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra37">p.extra37 = #extra37#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra38">p.extra38 = #extra38#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra39">p.extra39 = #extra39#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra39_0">(p.extra39 is null or p.extra39='' or p.extra39='0') </isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra40">p.extra40 = #extra40#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra41">p.extra41 = #extra41#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra42">p.extra42 = #extra42#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra43">p.extra43 = #extra43#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra44">p.extra44 = #extra44#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra45">p.extra45 = #extra45#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra46">p.extra46 = #extra46#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra47">p.extra47 = #extra47#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra48">p.extra48 = #extra48#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra49">p.extra49 = #extra49#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra50">p.extra50 = #extra50#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra51">p.extra51 = #extra51#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra52">p.extra52 = #extra52#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra53">p.extra53 = #extra53#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra54">p.extra54 = #extra54#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra55">p.extra55 = #extra55#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra56">p.extra56 = #extra56#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra57">p.extra57 = #extra57#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra58">p.extra58 = #extra58#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra59">p.extra59 = #extra59#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra60">p.extra60 = #extra60#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra61">p.extra61 = #extra61#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra62">p.extra62 = #extra62#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra63">p.extra63 = #extra63#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra64">p.extra64 = #extra64#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra65">p.extra65 = #extra65#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra66">p.extra66 = #extra66#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra67">p.extra67 = #extra67#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra68">p.extra68 = #extra68#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra69">p.extra69 = #extra69#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra70">p.extra70 = #extra70#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra71">p.extra71 = #extra71#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra72">p.extra72 = #extra72#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra73">p.extra73 = #extra73#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra74">p.extra74 = #extra74#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra75">p.extra75 = #extra75#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra76">p.extra76 = #extra76#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra77">p.extra77 = #extra77#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra78">p.extra78 = #extra78#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra79">p.extra79 = #extra79#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra80">p.extra80 = #extra80#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="ssfs">p.ssfs = #ssfs#</isNotEmpty>

			<isNotEmpty prepend=" AND " property="hpgStatus">w.CURRENT_ACTIVITY = #hpgStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="bzxStatus">w.CURRENT_ACTIVITY = #bzxStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gyyzStatus">w.CURRENT_ACTIVITY = #gyyzStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="bmzgStatus">w.CURRENT_ACTIVITY = #bmzgStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jbLcStatus">w.CURRENT_ACTIVITY = #jbLcStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="statusZj">w.CURRENT_ACTIVITY = #statusZj#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="statusBg">w.CURRENT_ACTIVITY = #statusBg#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jttzDate">p.PROJECT_END_DATE <![CDATA[<]]> #jttzDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cjr">
				p.record_guid in
				(select PROJECT_GUID from ${srmsSchema}.T_KYXM_PROJECT_GROUP_MEMBER where IS_FZR_CYR='c' and member_code = #cjr#)
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="xmzysMin">
				p.record_guid in
				(select PROJECT_GUID  ${srmsSchema}.T_KYXM_YEAR_BUDGET where FYEAR ='9999' and yb.BUDGET_AMOUNT >= #xmzysMin#)
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="xmzysMax">
				p.record_guid in
				(select PROJECT_GUID  ${srmsSchema}.T_KYXM_YEAR_BUDGET where FYEAR ='9999' and yb.BUDGET_AMOUNT <![CDATA[<=]]> #xmzysMax#)
			</isNotEmpty>
			<!-- 我处理过的 -->
			<isNotEmpty prepend=" AND " property="completerId">
				p.record_guid in (SELECT BUSINESS_GUID FROM ${srmsSchema}.v_kyxm_myhandle WHERE COMPLETER_ID = #completerId#)
			</isNotEmpty>
			<!-- 我相关的 -->
			<isNotEmpty prepend=" AND " property="relevanterId">
				(
				<!-- 我负责的 -->
				p.fzr like '%$relevanterId$%'
				<!-- 我管理的(单主管) -->
				OR (p.project_zg = #relevanterId#)
				<!-- 我参加的 -->
				OR (p.record_guid in (select PROJECT_GUID from ${srmsSchema}.T_KYXM_PROJECT_GROUP_MEMBER where IS_FZR_CYR='c' and member_code = #relevanterId#))
				<!-- 我处理的 -->
				OR (p.record_guid in (SELECT BUSINESS_GUID FROM ${srmsSchema}.v_kyxm_myhandle WHERE COMPLETER_ID = #relevanterId#))
				<isNotEmpty prepend=" OR " property="dynSql1">$dynSql1$</isNotEmpty>
				<isNotEmpty prepend=" OR " property="dynSql2">$dynSql2$</isNotEmpty>
				<isNotEmpty prepend=" " property="dynSql3">
				OR (p.record_guid in (select PROJECT_GUID FROM ${srmsSchema}.T_KYXM_PROJECT_GROUP_MEMBER where $dynSql3$))
				</isNotEmpty>
				<isNotEmpty prepend=" OR " property="dynSql4">$dynSql4$</isNotEmpty>
				<isNotEmpty prepend=" OR " property="dynSql5">$dynSql5$</isNotEmpty>
				)
			</isNotEmpty>
			<!-- 运行结题中的项目 -->
			<isNotEmpty prepend=" AND " property="statusList">
				p.project_status in ('02','03')
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="search">
					(p.record_Guid like '%$search$%'
					or p.project_code like '%$search$%'
					or p.serial_no like '%$search$%'
					or p.project_name LIKE '%$search$%'
					OR p.fzr LIKE '%$search$%'
					OR p.fzr_name LIKE '%$search$%'
					OR p.fzr_contact_tel LIKE '%$search$%'
					OR p.project_zg LIKE '%$search$%'
					OR p.project_zg_name LIKE '%$search$%'
					OR p.project_start_date LIKE '%$search$%'
					OR p.project_end_date LIKE '%$search$%'
					OR p.project_type LIKE '%$search$%'
					OR p.project_level LIKE '%$search$%'
					OR p.ghjsfx LIKE '%$search$%'
					OR p.tech_domain LIKE '%$search$%'
					OR p.techsx LIKE '%$search$%'
					OR p.project_status LIKE '%$search$%'
					OR p.sign_date LIKE '%$search$%'
					OR p.economy_benefit LIKE '%$search$%'
					OR p.economy_benefit_pf_date LIKE '%$search$%'
					OR p.actual_complete_date LIKE '%$search$%'
					OR p.main_project_guid LIKE '%$search$%'
					OR p.SOURCE_LABEL LIKE '%$search$%'
					OR p.nr_oldpro_source_guid LIKE '%$search$%'
					OR p.gd_date LIKE '%$search$%'
					OR p.jt_date LIKE '%$search$%'
					OR p.extra1 LIKE '%$search$%'
					OR p.extra2 LIKE '%$search$%'
					OR p.extra3 LIKE '%$search$%'
					OR p.extra4 LIKE '%$search$%'
					OR p.extra5 LIKE '%$search$%'
					OR p.extra6 LIKE '%$search$%'
					OR p.extra7 LIKE '%$search$%'
					OR p.extra8 LIKE '%$search$%'
					OR p.extra9 LIKE '%$search$%'
					OR p.extra10 LIKE '%$search$%'
					OR p.extra11 LIKE '%$search$%'
					OR p.extra12 LIKE '%$search$%'
					OR p.extra13 LIKE '%$search$%'
					OR p.extra14 LIKE '%$search$%'
					OR p.extra15 LIKE '%$search$%'
					OR p.extra16 LIKE '%$search$%'
					OR p.extra17 LIKE '%$search$%'
					OR p.extra18 LIKE '%$search$%'
					OR p.extra19 LIKE '%$search$%'
					OR p.extra20 LIKE '%$search$%'
					OR p.extra21 LIKE '%$search$%'
					OR p.extra22 LIKE '%$search$%'
					OR p.extra23 LIKE '%$search$%'
					OR p.extra24 LIKE '%$search$%'
					OR p.extra25 LIKE '%$search$%'
					OR p.extra26 LIKE '%$search$%'
					OR p.extra27 LIKE '%$search$%'
					OR p.extra28 LIKE '%$search$%'
					OR p.yan_shou_date LIKE '%$search$%'
					OR p.extra30 LIKE '%$search$%'
					OR p.extra31 LIKE '%$search$%'
					OR p.extra32 LIKE '%$search$%'
					OR p.extra33 LIKE '%$search$%'
					OR p.extra34 LIKE '%$search$%'
					OR p.extra35 LIKE '%$search$%'
					OR p.extra36 LIKE '%$search$%'
					OR p.extra37 LIKE '%$search$%'
					OR p.extra38 LIKE '%$search$%'
					OR p.extra39 LIKE '%$search$%'
					OR p.extra40 LIKE '%$search$%'
					OR p.extra41 LIKE '%$search$%'
					OR p.extra42 LIKE '%$search$%'
					OR p.extra43 LIKE '%$search$%'
					OR p.extra44 LIKE '%$search$%'
					OR p.extra45 LIKE '%$search$%'
					OR p.extra46 LIKE '%$search$%'
					OR p.extra47 LIKE '%$search$%'
					OR p.extra48 LIKE '%$search$%'
					OR p.extra49 LIKE '%$search$%'
					OR p.extra50 LIKE '%$search$%'
					OR p.extra51 LIKE '%$search$%'
					OR p.extra52 LIKE '%$search$%'
					OR p.extra53 LIKE '%$search$%'
					OR p.extra54 LIKE '%$search$%'
					OR p.extra55 LIKE '%$search$%'
					OR p.extra56 LIKE '%$search$%'
					OR p.extra57 LIKE '%$search$%'
					OR p.extra58 LIKE '%$search$%'
					OR p.extra59 LIKE '%$search$%'
					OR p.extra60 LIKE '%$search$%'
					OR p.extra61 LIKE '%$search$%'
					OR p.extra62 LIKE '%$search$%'
					OR p.extra63 LIKE '%$search$%'
					OR p.extra64 LIKE '%$search$%'
					OR p.extra65 LIKE '%$search$%'
					OR p.extra66 LIKE '%$search$%'
					OR p.extra67 LIKE '%$search$%'
					OR p.extra68 LIKE '%$search$%'
					OR p.extra69 LIKE '%$search$%'
					OR p.extra70 LIKE '%$search$%'
					OR p.extra71 LIKE '%$search$%'
					OR p.extra72 LIKE '%$search$%'
					OR p.extra73 LIKE '%$search$%'
					OR p.extra74 LIKE '%$search$%'
					OR p.extra75 LIKE '%$search$%'
					OR p.extra76 LIKE '%$search$%'
					OR p.extra77 LIKE '%$search$%'
					OR p.extra78 LIKE '%$search$%'
					OR p.extra79 LIKE '%$search$%'
					OR p.extra80 LIKE '%$search$%'
					OR p.ssfs LIKE '%$search$%'
					)
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="search1">$search1$</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
		</dynamic>
		<isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
	</select>

	<!-- 科技效益接口 -->
	<select id="queryXy" parameterClass="hashmap" resultClass="hashmap" remapResults="true">
		SELECT
			p.record_guid as "recordGuid" ,
			p.serial_no as "serialNo" ,
			p.project_code as "projectCode" ,
			p.project_name as "projectName" ,
			p.fzr as "fzr" ,
			p.fzr_name as "fzrName" ,
			pgm.MEMBER_NAME  as "memberName" ,		
			pgm.MEMBER_CODE  as "memberCode" ,		
			pgm.DW  as "dw" ,		
			pgm.DW_NAME  as "dwName" ,		
			pgm.DEPT_NAME  as "deptName" ,		
			pgm.DEPT  as "dept" ,		
			pgm.IS_FZR_CYR  as "isFzrCyr" ,	
			jtpsjg.PRO_ECONOMY_BENEFIT  as "proEconomyBenefit" 
		FROM ${srmsSchema}.t_kyxm_project p
		inner join ${srmsSchema}.T_KYXM_PLAN_TASK pt on pt.PROJECT_GUID=p.record_guid
		inner join ${srmsSchema}.T_KYXM_PROJECT_GROUP_MEMBER pgm on pgm.BUSINESS=pt.record_guid
		inner join ${srmsSchema}.T_KYXM_JTPSJG jtpsjg on jtpsjg.PROJECT_GUID=p.record_guid
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="year">jtpsjg.EXTRA8 like '%$year$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="memberCode">pgm.MEMBER_CODE =  #memberCode#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="memberCodes"> 
				pgm.MEMBER_CODE IN
				<iterate property="memberCodes" conjunction="," open="(" close=")">#memberCodes[]#</iterate>
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1_jtpsjg">jtpsjg.EXTRA1 =  #extra1_jtpsjg#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="projectStatus">p.project_status = #projectStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra11sNo">
				p.extra11 not IN
				<iterate property="extra11sNo" conjunction="," open="(" close=")">#extra11sNo[]#</iterate>
			</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
			
		</dynamic>
		<isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
	</select>
	
	<!--查询专职报支人员 -->
	<select id="queryZzbzry" parameterClass="hashmap" resultClass="hashmap" remapResults="true">
		SELECT 
			user_code as "userCode" ,
			user_name as "userName" 
		FROM ${srmsSchema}.T_KYXM_ZZBZRY
	</select>
	<!--excel通用导出 -->
	<select id="export" parameterClass="hashmap" resultClass="hashmap" remapResults="true">
		$dynSql$
	</select>

	
	<!-- 年度总结项目列表 -->
	<select id="queryNdzj" parameterClass="hashmap" resultClass="tkyxmProjectResult" remapResults="true">
		SELECT
			p.record_guid as "recordGuid" ,
			serial_no as "serialNo" ,
			project_code as "projectCode" ,
			project_name as "projectName" ,
			fzr as "fzr" ,
			fzr_name as "fzrName" ,
			project_zg as "projectZg" ,
			project_zg_name as "projectZgName" ,
			project_start_date as "projectStartDate" ,
			project_end_date as "projectEndDate" ,
			project_type as "projectType" ,
			project_level as "projectLevel" ,
			economy_benefit as "economyBenefit" ,
			economy_benefit_pf_date as "economyBenefitPfDate" ,
			actual_complete_date as "actualCompleteDate",
			j.extra9 as "xyStartDate",
			j.extra10 as "xyEndDate" 
		FROM ${srmsSchema}.t_kyxm_project p
		left join (SELECT * FROM ${srmsSchema}.T_KYXM_JPGNDZJ WHERE PROJECT_GUID||CREATE_DATE IN (SELECT PROJECT_GUID||MAX(CREATE_DATE) FROM ${srmsSchema}.T_KYXM_JPGNDZJ GROUP BY PROJECT_GUID)) j on j.PROJECT_GUID=p.record_guid
		where (p.main_project_guid is null or p.main_project_guid='')
		<isNotEmpty prepend=" AND " property="projectStatus">p.project_status = #projectStatus#</isNotEmpty>
	    <isNotEmpty prepend=" AND " property="extra11">p.extra11 = #extra11#</isNotEmpty>
		<isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
		<isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
	</select>
	
	<!-- 未转产纳入已有项目 -->
	<select id="queryNRYYXMPro" parameterClass="hashmap" resultClass="hashmap" remapResults="true">
		SELECT 
			record_guid as "recordGuid" ,
			serial_no as "serialNo" ,
			project_code as "projectCode" ,
			project_name as "projectName" ,
			fzr as "fzr" ,
			fzr_name as "fzrName" ,
			fzr_contact_tel as "fzrContactTel" ,
			lxr as "lxr" ,
			lxr_name as "lxrName" ,
			lxr_contact_tel as "lxrContactTel" ,
			project_zg as "projectZg" ,
			project_zg_name as "projectZgName" ,
			project_start_date as "projectStartDate" ,
			project_end_date as "projectEndDate" ,
			project_type as "projectType" ,
			project_level as "projectLevel" ,
			ghjsfx as "ghjsfx" ,
			tech_domain as "techDomain" ,
			techsx as "techsx" ,
			project_status as "projectStatus" ,
			sign_date as "signDate" ,
			main_project_guid as "mainProjectGuid" ,
			source_label as "sourceLabel" ,
			gd_date as "gdDate" ,
			jt_date as "jtDate" ,
			extra1 as "extra1" ,
			extra2 as "extra2" ,
			extra3 as "extra3" ,
			extra4 as "extra4" ,
			extra5 as "extra5" ,
			extra6 as "extra6" ,
			extra7 as "extra7" ,
			extra8 as "extra8" ,
			extra9 as "extra9" ,
			extra10 as "extra10" ,
			extra11 as "extra11" ,
			yan_shou_date as "yanShouDate" 
		FROM KJGL.T_KYXM_PROJECT WHERE PROJECT_STATUS ='02' AND PROJECT_TYPE ='x' 
		<isNotEmpty prepend=" AND " property="xmssjds">
			<iterate property="xmssjds" conjunction="and " open="(" close=")">EXTRA10 LIKE '%$xmssjds[]$%'</iterate>
		</isNotEmpty>
		AND RECORD_GUID NOT IN (
			SELECT p.RECORD_GUID FROM KJGL.T_KYXM_NPGDXQ_CP cp,KJGL.T_KYXM_PLAN_TASK pt,KJGL.T_KYXM_PROJECT p
			WHERE cp.NPGDXQ_GUID = pt.RECORD_GUID AND pt.PROJECT_GUID = p.RECORD_GUID 
			<isNotEmpty prepend=" AND " property="cpmcs">
				PRODUCT_NAME NOT IN
				<iterate property="cpmcs" conjunction="," open="(" close=")">#cpmcs[]#</iterate>
			</isNotEmpty>
			and p.PROJECT_STATUS ='02' AND p.PROJECT_TYPE ='x'
			<isNotEmpty prepend=" AND " property="xmssjds">
				<iterate property="xmssjds" conjunction="and " open="(" close=")">p.EXTRA10 LIKE '%$xmssjds[]$%'</iterate>
			</isNotEmpty>
		)
	</select>
	
</sqlMap>