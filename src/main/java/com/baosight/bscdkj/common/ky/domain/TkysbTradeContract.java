package com.baosight.bscdkj.common.ky.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bscdkj.common.domain.AbstractDomain;
import javax.validation.constraints.Size;


/**
 * 【请填写功能名称】对象 t_kysb_trade_contract
 * 
 * <AUTHOR>
 * @date 2022-09-01
 */
@Getter
@Setter
@ToString
public class TkysbTradeContract extends AbstractDomain{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String tradeContractId;

    /** 商标主键 */
    @Size(max = 36,message = "商标主键最大为36位字符")
    private String tradeRegistId;

    /** 许可主键 */
    @Size(max = 36,message = "许可主键最大为36位字符")
    private String tradePermId;

    /** 贸易合同号 */
    @Size(max = 36,message = "贸易合同号最大为36位字符")
    private String tradeContractNo;

    /** 贸易对象 */
    @Size(max = 50,message = "贸易对象最大为50位字符")
    private String tradePartnes;

    /** 状态 */
    @Size(max = 10,message = "状态最大为10位字符")
    private String status;

    /** 扩展字段1 */
    @Size(max = 50,message = "扩展字段1最大为50位字符")
    private String extra1;

    /** 扩展字段2 */
    @Size(max = 50,message = "扩展字段2最大为50位字符")
    private String extra2;

    /** 扩展字段3 */
    @Size(max = 100,message = "扩展字段3最大为100位字符")
    private String extra3;

    /** 扩展字段4 */
    @Size(max = 100,message = "扩展字段4最大为100位字符")
    private String extra4;

}
