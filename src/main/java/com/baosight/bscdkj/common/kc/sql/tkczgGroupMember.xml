<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkczgGroupMember">
	<typeAlias alias="tkczgGroupMemberResult" type="com.baosight.bscdkj.common.kc.domain.TkczgGroupMember"/>
	<select id="load" parameterClass="string" resultClass="tkczgGroupMemberResult">
		SELECT
				member_id as "memberId" ,
				plan_id as "planId" ,
				stage_year as "stageYear" ,
				order_no as "orderNo" ,
				role as "role" ,
				emp_name as "empName" ,
				emp_code as "empCode" ,
				dept_code as "deptCode" ,
				dept_name as "deptName" ,
				task as "task" ,
				risk_detail_amt as "riskDetailAmt" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" 
				FROM ${zzzcSchema}.t_kczg_group_member
		WHERE
				member_id = #memberId#

	</select>

	<select id="query"  parameterClass="hashmap" resultClass="tkczgGroupMemberResult">
		SELECT
				member_id as "memberId" ,
				plan_id as "planId" ,
				stage_year as "stageYear" ,
				order_no as "orderNo" ,
				role as "role" ,
				emp_name as "empName" ,
				emp_code as "empCode" ,
				dept_code as "deptCode" ,
				dept_name as "deptName" ,
				task as "task" ,
				risk_detail_amt as "riskDetailAmt" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" 
				FROM ${zzzcSchema}.t_kczg_group_member
			<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="memberId">member_id =  #memberId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="planId">plan_id =  #planId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="stageYear">stage_year =  #stageYear#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="orderNo">order_no =  #orderNo#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="role">role =  #role#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="empName">emp_name =  #empName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="empCode">emp_code =  #empCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptCode">dept_code =  #deptCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptName">dept_name =  #deptName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="task">task =  #task#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="riskDetailAmt">risk_detail_amt =  #riskDetailAmt#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
		
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*)
		FROM ${zzzcSchema}.t_kczg_group_member
		<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="memberId">member_id =  #memberId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="planId">plan_id =  #planId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="stageYear">stage_year =  #stageYear#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="orderNo">order_no =  #orderNo#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="role">role =  #role#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="empName">emp_name =  #empName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="empCode">emp_code =  #empCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptCode">dept_code =  #deptCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptName">dept_name =  #deptName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="task">task =  #task#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="riskDetailAmt">risk_detail_amt =  #riskDetailAmt#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
					<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>

	<insert id="insert" parameterClass="tkczgGroupMemberResult">
		INSERT INTO ${zzzcSchema}.t_kczg_group_member (
		<dynamic prepend=" ">
						<isNotEmpty prepend=" , " property="memberId">member_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="planId">plan_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="stageYear">stage_year</isNotEmpty>
						<isNotEmpty prepend=" , " property="orderNo">order_no</isNotEmpty>
						<isNotEmpty prepend=" , " property="role">role</isNotEmpty>
						<isNotEmpty prepend=" , " property="empName">emp_name</isNotEmpty>
						<isNotEmpty prepend=" , " property="empCode">emp_code</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptCode">dept_code</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptName">dept_name</isNotEmpty>
						<isNotEmpty prepend=" , " property="task">task</isNotEmpty>
						<isNotEmpty prepend=" , " property="riskDetailAmt">risk_detail_amt</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version</isNotEmpty>
				</dynamic>
		) VALUES (
    <dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="memberId">#memberId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="planId">#planId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="stageYear">#stageYear#</isNotEmpty>
						<isNotEmpty prepend=" , " property="orderNo">#orderNo#</isNotEmpty>
						<isNotEmpty prepend=" , " property="role">#role#</isNotEmpty>
						<isNotEmpty prepend=" , " property="empName">#empName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="empCode">#empCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptCode">#deptCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptName">#deptName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="task">#task#</isNotEmpty>
						<isNotEmpty prepend=" , " property="riskDetailAmt">#riskDetailAmt#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.t_kczg_group_member
		WHERE
			member_id = #value#

	</delete>

	<delete id="deleteByCon" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.t_kczg_group_member
		WHERE
			$dynSql$
	</delete>

	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.t_kczg_group_member
		WHERE
		<dynamic prepend=" ">
						<isNotEmpty prepend=" AND " property="memberId">member_id=#memberId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="planId">plan_id=#planId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="stageYear">stage_year=#stageYear#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="orderNo">order_no=#orderNo#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="role">role=#role#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="empName">emp_name=#empName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="empCode">emp_code=#empCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptCode">dept_code=#deptCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptName">dept_name=#deptName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="task">task=#task#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="riskDetailAmt">risk_detail_amt=#riskDetailAmt#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>
	</delete>

	<update id="update" parameterClass="tkczgGroupMemberResult">
		UPDATE  ${zzzcSchema}.t_kczg_group_member
		SET
		<dynamic prepend=" ">
					<isNotEmpty prepend=" , " property="memberId">member_id=#memberId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="planId">plan_id=#planId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="stageYear">stage_year=#stageYear#</isNotEmpty>
						<isNotEmpty prepend=" , " property="orderNo">order_no=#orderNo#</isNotEmpty>
						<isNotEmpty prepend=" , " property="role">role=#role#</isNotEmpty>
						<isNotEmpty prepend=" , " property="empName">emp_name=#empName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="empCode">emp_code=#empCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptCode">dept_code=#deptCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptName">dept_name=#deptName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="task">task=#task#</isNotEmpty>
						<isNotEmpty prepend=" , " property="riskDetailAmt">risk_detail_amt=#riskDetailAmt#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>
		WHERE
		 member_id =#memberId#
	</update>



</sqlMap>
