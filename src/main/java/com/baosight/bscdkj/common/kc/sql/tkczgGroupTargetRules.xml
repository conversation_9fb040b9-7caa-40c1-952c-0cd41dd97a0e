<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkczgGroupTargetRules">
	<typeAlias alias="tkczgGroupTargetRulesResult" type="com.baosight.bscdkj.common.kc.domain.TkczgGroupTargetRules"/>
	<select id="load" parameterClass="string" resultClass="tkczgGroupTargetRulesResult">
		SELECT
				target_rules_id as "targetRulesId" ,
				plan_id as "planId" ,
				stage_year as "stageYear" ,
				jlxz_x1 as "jlxzX1" ,
				jlxz_x2 as "jlxzX2" ,
				jlxz_x3 as "jlxzX3" ,
				jlxz_x4 as "jlxzX4" ,
				jlxz_x5 as "jlxzX5" ,
				x1_desc as "x1Desc" ,
				x2_desc as "x2Desc" ,
				x3_desc as "x3Desc" ,
				x4_desc as "x4Desc" ,
				x5_desc as "x5Desc" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion"
				FROM ${zzzcSchema}.t_kczg_group_target_rules
		WHERE
				target_rules_id = #targetRulesId#

	</select>

	<select id="query"  parameterClass="hashmap" resultClass="tkczgGroupTargetRulesResult">
		SELECT
				target_rules_id as "targetRulesId" ,
				plan_id as "planId" ,
				stage_year as "stageYear" ,
				jlxz_x1 as "jlxzX1" ,
				jlxz_x2 as "jlxzX2" ,
				jlxz_x3 as "jlxzX3" ,
				jlxz_x4 as "jlxzX4" ,
				jlxz_x5 as "jlxzX5" ,
				x1_desc as "x1Desc" ,
				x2_desc as "x2Desc" ,
				x3_desc as "x3Desc" ,
				x4_desc as "x4Desc" ,
				x5_desc as "x5Desc" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion"
				FROM ${zzzcSchema}.t_kczg_group_target_rules
			<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="targetRulesId">target_rules_id =  #targetRulesId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="planId">plan_id =  #planId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="stageYear">stage_year =  #stageYear#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX1">jlxz_x1 =  #jlxzX1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX2">jlxz_x2 =  #jlxzX2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX3">jlxz_x3 =  #jlxzX3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX4">jlxz_x4 =  #jlxzX4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX5">jlxz_x5 =  #jlxzX5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x1Desc">x1_desc =  #x1Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x2Desc">x2_desc =  #x2Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x3Desc">x3_desc =  #x3Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x4Desc">x4_desc =  #x4Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x5Desc">x5_desc =  #x5Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>

				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*)
		FROM ${zzzcSchema}.t_kczg_group_target_rules
		<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="targetRulesId">target_rules_id =  #targetRulesId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="planId">plan_id =  #planId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="stageYear">stage_year =  #stageYear#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX1">jlxz_x1 =  #jlxzX1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX2">jlxz_x2 =  #jlxzX2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX3">jlxz_x3 =  #jlxzX3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX4">jlxz_x4 =  #jlxzX4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX5">jlxz_x5 =  #jlxzX5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x1Desc">x1_desc =  #x1Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x2Desc">x2_desc =  #x2Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x3Desc">x3_desc =  #x3Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x4Desc">x4_desc =  #x4Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x5Desc">x5_desc =  #x5Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
					<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>

	<insert id="insert" parameterClass="tkczgGroupTargetRulesResult">
		INSERT INTO ${zzzcSchema}.t_kczg_group_target_rules (
		<dynamic prepend=" ">
						<isNotEmpty prepend=" , " property="targetRulesId">target_rules_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="planId">plan_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="stageYear">stage_year</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX1">jlxz_x1</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX2">jlxz_x2</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX3">jlxz_x3</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX4">jlxz_x4</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX5">jlxz_x5</isNotEmpty>
						<isNotEmpty prepend=" , " property="x1Desc">x1_desc</isNotEmpty>
						<isNotEmpty prepend=" , " property="x2Desc">x2_desc</isNotEmpty>
						<isNotEmpty prepend=" , " property="x3Desc">x3_desc</isNotEmpty>
						<isNotEmpty prepend=" , " property="x4Desc">x4_desc</isNotEmpty>
						<isNotEmpty prepend=" , " property="x5Desc">x5_desc</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version</isNotEmpty>
				</dynamic>
		) VALUES (
    <dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="targetRulesId">#targetRulesId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="planId">#planId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="stageYear">#stageYear#</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX1">#jlxzX1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX2">#jlxzX2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX3">#jlxzX3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX4">#jlxzX4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX5">#jlxzX5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="x1Desc">#x1Desc#</isNotEmpty>
						<isNotEmpty prepend=" , " property="x2Desc">#x2Desc#</isNotEmpty>
						<isNotEmpty prepend=" , " property="x3Desc">#x3Desc#</isNotEmpty>
						<isNotEmpty prepend=" , " property="x4Desc">#x4Desc#</isNotEmpty>
						<isNotEmpty prepend=" , " property="x5Desc">#x5Desc#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.t_kczg_group_target_rules
		WHERE
		    target_rules_id = #value#

	</delete>

	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.t_kczg_group_target_rules
		WHERE
		<dynamic prepend=" ">
						<isNotEmpty prepend=" AND " property="targetRulesId">target_rules_id=#targetRulesId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="planId">plan_id=#planId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="stageYear">stage_year=#stageYear#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX1">jlxz_x1=#jlxzX1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX2">jlxz_x2=#jlxzX2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX3">jlxz_x3=#jlxzX3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX4">jlxz_x4=#jlxzX4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="jlxzX5">jlxz_x5=#jlxzX5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x1Desc">x1_desc=#x1Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x2Desc">x2_desc=#x2Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x3Desc">x3_desc=#x3Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x4Desc">x4_desc=#x4Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="x5Desc">x5_desc=#x5Desc#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>
	</delete>

	<update id="update" parameterClass="tkczgGroupTargetRulesResult">
		UPDATE  ${zzzcSchema}.t_kczg_group_target_rules
		SET
		<dynamic prepend=" ">
					<isNotEmpty prepend=" , " property="targetRulesId">target_rules_id=#targetRulesId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="planId">plan_id=#planId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="stageYear">stage_year=#stageYear#</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX1">jlxz_x1=#jlxzX1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX2">jlxz_x2=#jlxzX2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX3">jlxz_x3=#jlxzX3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX4">jlxz_x4=#jlxzX4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="jlxzX5">jlxz_x5=#jlxzX5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="x1Desc">x1_desc=#x1Desc#</isNotEmpty>
						<isNotEmpty prepend=" , " property="x2Desc">x2_desc=#x2Desc#</isNotEmpty>
						<isNotEmpty prepend=" , " property="x3Desc">x3_desc=#x3Desc#</isNotEmpty>
						<isNotEmpty prepend=" , " property="x4Desc">x4_desc=#x4Desc#</isNotEmpty>
						<isNotEmpty prepend=" , " property="x5Desc">x5_desc=#x5Desc#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>
		WHERE
		 target_rules_id =#targetRulesId#
	</update>



</sqlMap>
