<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tksysLaboratory">
    <typeAlias alias="tksysLaboratoryResult" type="com.baosight.bscdkj.common.ks.domain.TksysLaboratory"/>
    <select id="load" parameterClass="string" resultClass="tksysLaboratoryResult">
        SELECT
        RECORD_ID as "recordId" , <!-- 主键 -->
        SERIAL_NO as "serialNo" , <!-- 流水号 -->
        LAB_NAME as "labName" , <!-- 实验室名称 -->
        HZ_DEPT as "hzDept" , <!-- 拟合作单位 -->
        TC_DEPT_CODE as "tcDeptCode" , <!-- 项目提出部门 -->
        TC_DEPT_NAME as "tcDeptName" , <!-- 项目提出部门 -->
        TC_DEPT_CODE_PATH as "tcDeptCodePath" , <!-- 项目提出部门path -->
        TCR_CODE as "tcrCode" , <!-- 项目负责人工号 -->
        TCR_NAME as "tcrName" , <!-- 项目提出名称 -->
        TCR_MOBILE_PHONE as "tcrMobilePhone" , <!-- 项目负责人联系手机 -->
        CATOGERY as "catogery" , <!-- 属性 -->
        TECH_DOMAIN as "techDomain" , <!-- 所属领域 -->
        FZR_CODE as "fzrCode" , <!-- 项目负责人 -->
        FZR_NAME as "fzrName" , <!-- 项目负责人 -->
        XMZG_CODE as "xmzgCode" , <!-- 项目主管 -->
        XMZG_NAME as "xmzgName" , <!-- 项目主管 -->
        CYCLE as "cycle" , <!-- 协议周期：3年、长期 -->
        START_DATE as "startDate" , <!-- 协议生效日期 -->
        END_DATE as "endDate" , <!-- 协议到期日期 -->
        RENEW_DATE as "renewDate" , <!-- 协议续签日期 -->
        STATUS as "status" , <!-- 状态 -->
        EXTRA1 as "extra1" , <!-- 扩展字段1 -->
        EXTRA2 as "extra2" , <!-- 扩展字段2 -->
        EXTRA3 as "extra3" , <!-- 扩展字段3 -->
        EXTRA4 as "extra4" , <!-- 扩展字段4 -->
        EXTRA5 as "extra5" , <!-- 扩展字段5 -->
        DEL_STATUS as "delStatus" , <!-- 删除状态 -->
        CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
        CREATE_DATE as "createDate" , <!-- 创建时间 -->
        UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
        UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
        DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
        DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
        RECORD_VERSION as "recordVersion" , <!-- 版本号 -->
        TECH_CLASSIFICATION as "techClassification"  <!-- 技术分类 -->
        FROM ${zzzcSchema}.T_KSYS_LABORATORY
        WHERE
        RECORD_ID = #recordId#

    </select>

    <select id="query" parameterClass="hashmap" resultClass="tksysLaboratoryResult">
        SELECT
        RECORD_ID as "recordId" ,
        SERIAL_NO as "serialNo" ,
        LAB_NAME as "labName" ,
        HZ_DEPT as "hzDept" ,
        TC_DEPT_CODE as "tcDeptCode" ,
        TC_DEPT_NAME as "tcDeptName" ,
        TC_DEPT_CODE_PATH as "tcDeptCodePath" ,
        TCR_CODE as "tcrCode" ,
        TCR_NAME as "tcrName" ,
        TCR_MOBILE_PHONE as "tcrMobilePhone" ,
        CATOGERY as "catogery" ,
        TECH_DOMAIN as "techDomain" ,
        FZR_CODE as "fzrCode" ,
        FZR_NAME as "fzrName" ,
        XMZG_CODE as "xmzgCode" ,
        XMZG_NAME as "xmzgName" ,
        CYCLE as "cycle" ,
        START_DATE as "startDate" ,
        END_DATE as "endDate" ,
        RENEW_DATE as "renewDate" ,
        STATUS as "status" ,
        EXTRA1 as "extra1" ,
        EXTRA2 as "extra2" ,
        EXTRA3 as "extra3" ,
        EXTRA4 as "extra4" ,
        EXTRA5 as "extra5" ,
        DEL_STATUS as "delStatus" ,
        CREATE_USER_LABEL as "createUserLabel" ,
        CREATE_DATE as "createDate" ,
        UPDATE_USER_LABEL as "updateUserLabel" ,
        UPDATE_DATE as "updateDate" ,
        DELETE_USER_LABEL as "deleteUserLabel" ,
        DELETE_DATE as "deleteDate" ,
        RECORD_VERSION as "recordVersion" ,
        TECH_CLASSIFICATION as "techClassification"
        FROM ${zzzcSchema}.T_KSYS_LABORATORY
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="recordId">RECORD_ID = #recordId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="serialNo">SERIAL_NO = #serialNo#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="labName">LAB_NAME = #labName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="hzDept">HZ_DEPT = #hzDept#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcDeptCode">TC_DEPT_CODE = #tcDeptCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcDeptName">TC_DEPT_NAME = #tcDeptName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcDeptCodePath">TC_DEPT_CODE_PATH = #tcDeptCodePath#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcrCode">TCR_CODE = #tcrCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcrName">TCR_NAME = #tcrName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcrMobilePhone">TCR_MOBILE_PHONE = #tcrMobilePhone#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="catogery">CATOGERY = #catogery#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="techDomain">TECH_DOMAIN = #techDomain#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="fzrCode">FZR_CODE = #fzrCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="fzrName">FZR_NAME = #fzrName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="xmzgCode">XMZG_CODE = #xmzgCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="xmzgName">XMZG_NAME = #xmzgName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="cycle">CYCLE = #cycle#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="startDate">START_DATE = #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">END_DATE = #endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="renewDate">RENEW_DATE = #renewDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="status">STATUS = #status#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="techClassification">TECH_CLASSIFICATION = #techClassification#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="orgCode">TC_DEPT_CODE_PATH like '%$orgCode$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <select id="count" parameterClass="hashmap" resultClass="integer">
        SELECT count(*)
        FROM ${zzzcSchema}.T_KSYS_LABORATORY
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="recordId">RECORD_ID = #recordId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="serialNo">SERIAL_NO = #serialNo#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="labName">LAB_NAME = #labName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="hzDept">HZ_DEPT = #hzDept#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcDeptCode">TC_DEPT_CODE = #tcDeptCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcDeptName">TC_DEPT_NAME = #tcDeptName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcDeptCodePath">TC_DEPT_CODE_PATH = #tcDeptCodePath#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcrCode">TCR_CODE = #tcrCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcrName">TCR_NAME = #tcrName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcrMobilePhone">TCR_MOBILE_PHONE = #tcrMobilePhone#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="catogery">CATOGERY = #catogery#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="techDomain">TECH_DOMAIN = #techDomain#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="fzrCode">FZR_CODE = #fzrCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="fzrName">FZR_NAME = #fzrName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="xmzgCode">XMZG_CODE = #xmzgCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="xmzgName">XMZG_NAME = #xmzgName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="cycle">CYCLE = #cycle#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="startDate">START_DATE = #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">END_DATE = #endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="renewDate">RENEW_DATE = #renewDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="status">STATUS = #status#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra1">EXTRA1 = #extra1#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra2">EXTRA2 = #extra2#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra3">EXTRA3 = #extra3#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra4">EXTRA4 = #extra4#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra5">EXTRA5 = #extra5#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS = #delStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL = #createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createDate">CREATE_DATE = #createDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL = #updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE = #updateDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL = #deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE = #deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION = #recordVersion#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="techClassification">TECH_CLASSIFICATION = #techClassification#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
    </select>

    <insert id="insert" parameterClass="tksysLaboratoryResult">
        INSERT INTO ${zzzcSchema}.T_KSYS_LABORATORY (
        <dynamic prepend=" ">
            <isNotEmpty prepend=" , " property="recordId">RECORD_ID</isNotEmpty>
            <isNotEmpty prepend=" , " property="serialNo">SERIAL_NO</isNotEmpty>
            <isNotEmpty prepend=" , " property="labName">LAB_NAME</isNotEmpty>
            <isNotEmpty prepend=" , " property="hzDept">HZ_DEPT</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcDeptCode">TC_DEPT_CODE</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcDeptName">TC_DEPT_NAME</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcDeptCodePath">TC_DEPT_CODE_PATH</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcrCode">TCR_CODE</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcrName">TCR_NAME</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcrMobilePhone">TCR_MOBILE_PHONE</isNotEmpty>
            <isNotEmpty prepend=" , " property="catogery">CATOGERY</isNotEmpty>
            <isNotEmpty prepend=" , " property="techDomain">TECH_DOMAIN</isNotEmpty>
            <isNotEmpty prepend=" , " property="fzrCode">FZR_CODE</isNotEmpty>
            <isNotEmpty prepend=" , " property="fzrName">FZR_NAME</isNotEmpty>
            <isNotEmpty prepend=" , " property="xmzgCode">XMZG_CODE</isNotEmpty>
            <isNotEmpty prepend=" , " property="xmzgName">XMZG_NAME</isNotEmpty>
            <isNotEmpty prepend=" , " property="cycle">CYCLE</isNotEmpty>
            <isNotEmpty prepend=" , " property="startDate">START_DATE</isNotEmpty>
            <isNotEmpty prepend=" , " property="endDate">END_DATE</isNotEmpty>
            <isNotEmpty prepend=" , " property="renewDate">RENEW_DATE</isNotEmpty>
            <isNotEmpty prepend=" , " property="status">STATUS</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
            <isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
            <isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
            <isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
            <isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
            <isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
            <isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
            <isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
            <isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
            <isNotEmpty prepend=" , " property="techClassification">TECH_CLASSIFICATION</isNotEmpty>
        </dynamic>
        ) VALUES (
        <dynamic prepend=" ">
            <isNotEmpty prepend=" , " property="recordId">#recordId#</isNotEmpty>
            <isNotEmpty prepend=" , " property="serialNo">#serialNo#</isNotEmpty>
            <isNotEmpty prepend=" , " property="labName">#labName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="hzDept">#hzDept#</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcDeptCode">#tcDeptCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcDeptName">#tcDeptName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcDeptCodePath">#tcDeptCodePath#</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcrCode">#tcrCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcrName">#tcrName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcrMobilePhone">#tcrMobilePhone#</isNotEmpty>
            <isNotEmpty prepend=" , " property="catogery">#catogery#</isNotEmpty>
            <isNotEmpty prepend=" , " property="techDomain">#techDomain#</isNotEmpty>
            <isNotEmpty prepend=" , " property="fzrCode">#fzrCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="fzrName">#fzrName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="xmzgCode">#xmzgCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="xmzgName">#xmzgName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="cycle">#cycle#</isNotEmpty>
            <isNotEmpty prepend=" , " property="startDate">#startDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="endDate">#endDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="renewDate">#renewDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="status">#status#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
            <isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
            <isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
            <isNotEmpty prepend=" , " property="techClassification">#techClassification#</isNotEmpty>
        </dynamic>
        )
    </insert>

    <delete id="delete" parameterClass="string">
        DELETE
        FROM ${zzzcSchema}.T_KSYS_LABORATORY
        WHERE RECORD_ID = #value#
    </delete>

    <delete id="deleteByC" parameterClass="hashmap">
        DELETE FROM ${zzzcSchema}.T_KSYS_LABORATORY
        WHERE
        <dynamic prepend=" ">
            <isNotEmpty prepend=" AND " property="recordId">RECORD_ID=#recordId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="serialNo">SERIAL_NO=#serialNo#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="labName">LAB_NAME=#labName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="hzDept">HZ_DEPT=#hzDept#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcDeptCode">TC_DEPT_CODE=#tcDeptCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcDeptName">TC_DEPT_NAME=#tcDeptName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcDeptCodePath">TC_DEPT_CODE_PATH=#tcDeptCodePath#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcrCode">TCR_CODE=#tcrCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcrName">TCR_NAME=#tcrName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcrMobilePhone">TCR_MOBILE_PHONE=#tcrMobilePhone#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="catogery">CATOGERY=#catogery#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="techDomain">TECH_DOMAIN=#techDomain#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="fzrCode">FZR_CODE=#fzrCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="fzrName">FZR_NAME=#fzrName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="xmzgCode">XMZG_CODE=#xmzgCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="xmzgName">XMZG_NAME=#xmzgName#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="cycle">CYCLE=#cycle#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="startDate">START_DATE=#startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">END_DATE=#endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="renewDate">RENEW_DATE=#renewDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="status">STATUS=#status#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="techClassification">TECH_CLASSIFICATION=#techClassification#
            </isNotEmpty>
        </dynamic>
    </delete>

    <update id="update" parameterClass="tksysLaboratoryResult">
        UPDATE ${zzzcSchema}.T_KSYS_LABORATORY
        SET
        <dynamic prepend=" ">
            <isNotEmpty prepend=" , " property="recordId">RECORD_ID=#recordId#</isNotEmpty>
            <isNotEmpty prepend=" , " property="serialNo">SERIAL_NO=#serialNo#</isNotEmpty>
            <isNotEmpty prepend=" , " property="labName">LAB_NAME=#labName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="hzDept">HZ_DEPT=#hzDept#</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcDeptCode">TC_DEPT_CODE=#tcDeptCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcDeptName">TC_DEPT_NAME=#tcDeptName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcDeptCodePath">TC_DEPT_CODE_PATH=#tcDeptCodePath#</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcrCode">TCR_CODE=#tcrCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcrName">TCR_NAME=#tcrName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="tcrMobilePhone">TCR_MOBILE_PHONE=#tcrMobilePhone#</isNotEmpty>
            <isNotEmpty prepend=" , " property="catogery">CATOGERY=#catogery#</isNotEmpty>
            <isNotEmpty prepend=" , " property="techDomain">TECH_DOMAIN=#techDomain#</isNotEmpty>
            <isNotEmpty prepend=" , " property="fzrCode">FZR_CODE=#fzrCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="fzrName">FZR_NAME=#fzrName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="xmzgCode">XMZG_CODE=#xmzgCode#</isNotEmpty>
            <isNotEmpty prepend=" , " property="xmzgName">XMZG_NAME=#xmzgName#</isNotEmpty>
            <isNotEmpty prepend=" , " property="cycle">CYCLE=#cycle#</isNotEmpty>
            <isNotEmpty prepend=" , " property="startDate">START_DATE=#startDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="endDate">END_DATE=#endDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="renewDate">RENEW_DATE=#renewDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="status">STATUS=#status#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
            <isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
            <isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
            <isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
            <isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
            <isNotEmpty prepend=" , " property="techClassification">TECH_CLASSIFICATION=#techClassification#
            </isNotEmpty>
        </dynamic>
        WHERE
        RECORD_ID =#recordId#
    </update>

    <!--综合查询联合实验室-->
    <select id="ZHCXlaboratory" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        distinct
        A.RECORD_ID as "recordId" ,
        A.SERIAL_NO as "serialNo" ,
        A. LAB_NAME as "labName" ,
        A. HZ_DEPT as "hzDept" ,
        A. TC_DEPT_CODE as "tcDeptCode" ,
        A. TC_DEPT_NAME as "tcDeptName" ,
        A.TC_DEPT_CODE_PATH as "tcDeptCodePath" ,
        A.TCR_CODE as "tcrCode" ,
        A.TCR_NAME as "tcrName" ,
        A.CATOGERY as "catogery" ,
        A.TECH_DOMAIN as "techDomain" ,
        A.FZR_CODE as "fzrCode" ,
        A.FZR_NAME as "fzrName" ,
        A.XMZG_CODE as "xmzgCode" ,
        A.XMZG_NAME as "xmzgName" ,
        A.CYCLE as "cycle" ,
        A.START_DATE as "startDate" ,
        A.END_DATE as "endDate" ,
        A.RENEW_DATE as "renewDate" ,
        A.STATUS as "status" ,
        A.TECH_CLASSIFICATION as "techClassification",
        B.LAB_ID as "labId"
        FROM
        ${zzzcSchema}.T_KSYS_LABORATORY A left join ${zzzcSchema}.T_KSYS_LABORATORY_APPLY B on A.RECORD_ID=B.LAB_ID
        left join ${ggmkSchema}.V_MPWF_YB C on C.BUSINESS_ID = B.RECORD_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="loginName">
                (
                (C.ASSIGNEE_ID = #loginName# OR A.TCR_CODE=#loginName# OR A.FZR_CODE=#loginName# OR
                A.XMZG_CODE=#loginName#)
                <isNotEmpty prepend=" OR " property="dynSql">$dynSql$</isNotEmpty>
                )
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="serialNo">A.SERIAL_NO like '%$serialNo$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="labName">A.LAB_NAME like '%$labName$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcrName">A.TCR_NAME like '%$tcrName$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="tcDeptName">A.TC_DEPT_NAME like '%$tcDeptName$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="fzrName">A.FZR_NAME like '%$fzrName$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="xmzgName">A.XMZG_NAME like '%$xmzgName$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="hzDept">A.HZ_DEPT like '%$hzDept$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="catogery">A.CATOGERY = #catogery#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="techDomain">A.TECH_DOMAIN = #techDomain#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="status">A.STATUS = #status#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="startDate">A.START_DATE = #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">A.END_DATE = #endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="applyStatus">B.STATUS != #applyStatus#</isNotEmpty>
            <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
        </dynamic>
    </select>


</sqlMap>