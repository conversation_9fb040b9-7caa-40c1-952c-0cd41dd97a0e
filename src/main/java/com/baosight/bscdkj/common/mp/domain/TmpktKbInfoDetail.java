package com.baosight.bscdkj.common.mp.domain;


import cn.afterturn.easypoi.excel.annotation.Excel;
import com.baosight.bscdkj.common.domain.AbstractDomain;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import javax.validation.constraints.Size;
import java.math.BigDecimal;


/**
 * 26.0_01.01开票明细信息对象 t_mpkt_kb_info_detail
 * 
 * <AUTHOR>
 * @date 2021-12-28
 */
@Getter
@Setter
@ToString
public class TmpktKbInfoDetail extends AbstractDomain{
    private static final long serialVersionUID = 1L;

    /** 开票明细信息主键 */
    private String detailId;

    /** 开票信息主键 */
    @Excel(name = "开票信息主键")
    @Size(max = 36,message = "开票信息主键最大为36位字符")
    private String kbinfoId;

    /** 品名 */
    @Excel(name = "品名")
    @Size(max = 100,message = "品名最大为100位字符")
    private String goods;

    /** 规格型号 */
    @Excel(name = "规格型号")
    @Size(max = 50,message = "规格型号最大为50位字符")
    private String model;

    /** 计量单位 */
    @Excel(name = "计量单位")
    private String kbinfoUnit;

    /** 单价（元） */
    @Excel(name = "单价")
    private BigDecimal kbinfoPrice;

    /** 数量 */
    @Excel(name = "数量")
    private BigDecimal kbinfoCount;

    /** 税率 */
    @Excel(name = "税率")
    private BigDecimal rate;

    /** 不含税金额（元） */
    @Excel(name = "不含税金额")
    private BigDecimal invoiceNoTax;

    /** 发票税额（元） */
    @Excel(name = "发票税额")
    private BigDecimal invoiceTax;

    /** 备注 */
    @Excel(name = "备注")
    @Size(max = 500,message = "备注最大为500位字符")
    private String kbinfoRemark;

    /** 扩展字段1 */
    @Excel(name = "扩展字段1")
    @Size(max = 30,message = "扩展字段1最大为30位字符")
    private String extra1;

    /** 扩展字段2 */
    @Excel(name = "扩展字段2")
    @Size(max = 30,message = "扩展字段2最大为30位字符")
    private String extra2;

    /** 扩展字段3 */
    @Excel(name = "扩展字段3")
    @Size(max = 30,message = "扩展字段3最大为30位字符")
    private String extra3;

    /** 扩展字段4 */
    @Excel(name = "扩展字段4")
    @Size(max = 30,message = "扩展字段4最大为30位字符")
    private String extra4;

    /** 扩展字段5 */
    @Excel(name = "扩展字段5")
    @Size(max = 30,message = "扩展字段5最大为30位字符")
    private String extra5;

    /** 删除状态 */
    @Excel(name = "删除状态")
    @Size(max = 10,message = "删除状态最大为10位字符")
    private String delStatus;






   

}
