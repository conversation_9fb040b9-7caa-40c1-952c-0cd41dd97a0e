<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkjxjDeptQuota">
	<typeAlias alias="tkjxjDeptQuotaResult" type="com.baosight.bscdkj.common.kj.domain.TkjxjDeptQuota"/>
	<select id="load" parameterClass="string" resultClass="tkjxjDeptQuotaResult">
		SELECT
				guid as "guid" ,
        pid as "pid" ,
				plan_guid as "planGuid" ,
				year as "year" ,
				order_no as "orderNo" ,
				dept_code as "deptCode" ,
				yxtu_num as "yxtuNum" ,
				kjxx_num as "kjxxNum" ,
				kjmx_num as "kjmxNum" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion"
				FROM ${zzzcSchema}.t_kjxj_dept_quota
		WHERE
				guid = #guid#

	</select>

	<select id="query"  parameterClass="hashmap" resultClass="tkjxjDeptQuotaResult">
		SELECT
				guid as "guid" ,
        pid as "pid" ,
				plan_guid as "planGuid" ,
				year as "year" ,
				order_no as "orderNo" ,
				dept_code as "deptCode" ,
				yxtu_num as "yxtuNum" ,
				kjxx_num as "kjxxNum" ,
				kjmx_num as "kjmxNum" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion"
				FROM ${zzzcSchema}.t_kjxj_dept_quota
			<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="guid">guid =  #guid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="pid">pid =  #pid#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="planGuid">plan_guid =  #planGuid#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="year">year =  #year#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="orderNo">order_no =  #orderNo#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptCode">dept_code =  #deptCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="yxtuNum">yxtu_num =  #yxtuNum#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="kjxxNum">kjxx_num =  #kjxxNum#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="kjmxNum">kjmx_num =  #kjmxNum#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>

				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>


  <select id="queryVote"  parameterClass="hashmap" resultClass="tkjxjDeptQuotaResult">
    SELECT
    guid as "guid" ,
    pid as "pid" ,
    plan_guid as "planGuid" ,
    year as "year" ,
    order_no as "orderNo" ,
    dept_code as "deptCode" ,
    (SELECT count(*) FROM
    (SELECT * FROM
    ( SELECT r.PLAN_ID , r.APPLY_GUID, sum(r.VOTING_RESULT) AS voteNum
    FROM zzzc.T_KJXJ_VOTING_RESULT r
    WHERE r.VOTING_RESULT = '1' AND r.Apply_type = 'YXTD'
    GROUP BY r.PLAN_ID, r.APPLY_GUID )
    ORDER BY voteNum DESC LIMIT 3 ) w,
    ZZZC.T_KJXJ_APPLY a
    WHERE w.apply_guid = a.guid
    AND w.plan_id = t.PLAN_GUID
    AND a.DEPT_CODE_PATH LIKE '%' || t.DEPT_CODE || '%'
    ) AS "yxtuNum" ,
    (SELECT count(*) FROM
    (SELECT * FROM
    ( SELECT r.PLAN_ID , r.APPLY_GUID, sum(r.VOTING_RESULT) AS voteNum
    FROM zzzc.T_KJXJ_VOTING_RESULT r
    WHERE r.VOTING_RESULT = '1' AND r.Apply_type = 'KJXX'
    GROUP BY r.PLAN_ID, r.APPLY_GUID )
    ORDER BY voteNum DESC LIMIT 3 ) w,
    ZZZC.T_KJXJ_APPLY a
    WHERE w.apply_guid = a.guid
    AND w.plan_id = t.PLAN_GUID
    AND a.DEPT_CODE_PATH LIKE '%' || t.DEPT_CODE || '%'
    ) AS "kjxxNum" ,
    (SELECT count(*) FROM
    (SELECT * FROM
    ( SELECT r.PLAN_ID , r.APPLY_GUID, sum(r.VOTING_RESULT) AS voteNum
    FROM zzzc.T_KJXJ_VOTING_RESULT r
    WHERE r.VOTING_RESULT = '1' AND r.Apply_type = 'KJMX'
    GROUP BY r.PLAN_ID, r.APPLY_GUID )
    ORDER BY voteNum DESC LIMIT 3 ) w,
    ZZZC.T_KJXJ_APPLY a
    WHERE w.apply_guid = a.guid
    AND w.plan_id = t.PLAN_GUID
    AND a.DEPT_CODE_PATH LIKE '%' || t.DEPT_CODE || '%'
    ) AS "kjmxNum" ,
    extra1 as "extra1" ,
    extra2 as "extra2" ,
    extra3 as "extra3" ,
    extra4 as "extra4" ,
    extra5 as "extra5" ,
    del_status as "delStatus" ,
    create_user_label as "createUserLabel" ,
    create_date as "createDate" ,
    update_user_label as "updateUserLabel" ,
    update_date as "updateDate" ,
    delete_user_label as "deleteUserLabel" ,
    delete_date as "deleteDate" ,
    record_version as "recordVersion"
    FROM ${zzzcSchema}.t_kjxj_dept_quota t
    <dynamic prepend="WHERE">
      <isNotEmpty prepend=" AND " property="guid">guid =  #guid#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="pid">pid =  #pid#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="planGuid">plan_guid =  #planGuid#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="year">year =  #year#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="orderNo">order_no =  #orderNo#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="deptCode">dept_code =  #deptCode#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="yxtuNum">yxtu_num =  #yxtuNum#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="kjxxNum">kjxx_num =  #kjxxNum#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="kjmxNum">kjmx_num =  #kjmxNum#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
      <isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>

      <isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
    </dynamic>
    <isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
  </select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*)
		FROM ${zzzcSchema}.t_kjxj_dept_quota
		<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="guid">guid =  #guid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="pid">pid =  #pid#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="planGuid">plan_guid =  #planGuid#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="year">year =  #year#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="orderNo">order_no =  #orderNo#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptCode">dept_code =  #deptCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="yxtuNum">yxtu_num =  #yxtuNum#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="kjxxNum">kjxx_num =  #kjxxNum#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="kjmxNum">kjmx_num =  #kjmxNum#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
					<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>

	<insert id="insert" parameterClass="tkjxjDeptQuotaResult">
		INSERT INTO ${zzzcSchema}.t_kjxj_dept_quota (
		<dynamic prepend=" ">
						<isNotEmpty prepend=" , " property="guid">guid</isNotEmpty>
            <isNotEmpty prepend=" , " property="pid">pid</isNotEmpty>
						<isNotEmpty prepend=" , " property="planGuid">plan_guid</isNotEmpty>
						<isNotEmpty prepend=" , " property="year">year</isNotEmpty>
						<isNotEmpty prepend=" , " property="orderNo">order_no</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptCode">dept_code</isNotEmpty>
						<isNotEmpty prepend=" , " property="yxtuNum">yxtu_num</isNotEmpty>
						<isNotEmpty prepend=" , " property="kjxxNum">kjxx_num</isNotEmpty>
						<isNotEmpty prepend=" , " property="kjmxNum">kjmx_num</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version</isNotEmpty>
				</dynamic>
		) VALUES (
    <dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="guid">#guid#</isNotEmpty>
      <isNotEmpty prepend=" , " property="pid">#pid#</isNotEmpty>
						<isNotEmpty prepend=" , " property="planGuid">#planGuid#</isNotEmpty>
						<isNotEmpty prepend=" , " property="year">#year#</isNotEmpty>
						<isNotEmpty prepend=" , " property="orderNo">#orderNo#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptCode">#deptCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="yxtuNum">#yxtuNum#</isNotEmpty>
						<isNotEmpty prepend=" , " property="kjxxNum">#kjxxNum#</isNotEmpty>
						<isNotEmpty prepend=" , " property="kjmxNum">#kjmxNum#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.t_kjxj_dept_quota
		WHERE
		    guid = #value#

	</delete>

	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.t_kjxj_dept_quota
		WHERE
		<dynamic prepend=" ">
						<isNotEmpty prepend=" AND " property="guid">guid=#guid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="pid">pid=#pid#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="planGuid">plan_guid=#planGuid#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="year">year=#year#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="orderNo">order_no=#orderNo#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptCode">dept_code=#deptCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="yxtuNum">yxtu_num=#yxtuNum#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="kjxxNum">kjxx_num=#kjxxNum#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="kjmxNum">kjmx_num=#kjmxNum#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>
	</delete>

	<update id="update" parameterClass="tkjxjDeptQuotaResult">
		UPDATE  ${zzzcSchema}.t_kjxj_dept_quota
		SET
		<dynamic prepend=" ">
					<isNotEmpty prepend=" , " property="guid">guid=#guid#</isNotEmpty>
            <isNotEmpty prepend=" , " property="pid">pid=#pid#</isNotEmpty>
						<isNotEmpty prepend=" , " property="planGuid">plan_guid=#planGuid#</isNotEmpty>
						<isNotEmpty prepend=" , " property="year">year=#year#</isNotEmpty>
						<isNotEmpty prepend=" , " property="orderNo">order_no=#orderNo#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptCode">dept_code=#deptCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="yxtuNum">yxtu_num=#yxtuNum#</isNotEmpty>
						<isNotEmpty prepend=" , " property="kjxxNum">kjxx_num=#kjxxNum#</isNotEmpty>
						<isNotEmpty prepend=" , " property="kjmxNum">kjmx_num=#kjmxNum#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>
		WHERE
		 guid =#guid#
	</update>



</sqlMap>
