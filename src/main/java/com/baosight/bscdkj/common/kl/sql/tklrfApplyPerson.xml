<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tklrfApplyPerson">
	<typeAlias alias="tklrfApplyPersonResult" type="com.baosight.bscdkj.common.kl.domain.TklrfApplyPerson"/>
	<select id="load" parameterClass="string" resultClass="tklrfApplyPersonResult">
		SELECT 
				lrfxry_id as "lrfxryId" ,
				lrfx_id as "lrfxId" ,
				xh as "xh" ,
				emp_id as "empId" ,
				emp_name as "empName" ,
				title as "title" ,
				job as "job" ,
				education as "education" ,
				dept_code as "deptCode" ,
				dept_name as "deptName" ,
				role as "role" ,
				gxxs as "gxxs" ,
				isdjry as "isdjry" ,
				post_level as "postLevel" ,
				post_name as "postName" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" 
				FROM ${zzzcSchema}.t_klrf_apply_person
		WHERE
				lrfxry_id = #lrfxryId#
		
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tklrfApplyPersonResult">
		SELECT
				lrfxry_id as "lrfxryId" ,
				lrfx_id as "lrfxId" ,
				xh as "xh" ,
				emp_id as "empId" ,
				emp_name as "empName" ,
				title as "title" ,
				job as "job" ,
				education as "education" ,
				dept_code as "deptCode" ,
				dept_name as "deptName" ,
				role as "role" ,
				gxxs as "gxxs" ,
				isdjry as "isdjry" ,
				post_level as "postLevel" ,
				post_name as "postName" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" 
				FROM ${zzzcSchema}.t_klrf_apply_person
			<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="lrfxryId">lrfxry_id =  #lrfxryId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="lrfxId">lrfx_id =  #lrfxId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="xh">xh =  #xh#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="empId">emp_id =  #empId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="empName">emp_name =  #empName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="title">title =  #title#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="job">job =  #job#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="education">education =  #education#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptCode">dept_code =  #deptCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptName">dept_name =  #deptName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="role">role =  #role#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="gxxs">gxxs =  #gxxs#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="isdjry">isdjry =  #isdjry#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="postLevel">post_level =  #postLevel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="postName">post_name =  #postName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.t_klrf_apply_person
		<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="lrfxryId">lrfxry_id =  #lrfxryId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="lrfxId">lrfx_id =  #lrfxId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="xh">xh =  #xh#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="empId">emp_id =  #empId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="empName">emp_name =  #empName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="title">title =  #title#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="job">job =  #job#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="education">education =  #education#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptCode">dept_code =  #deptCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptName">dept_name =  #deptName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="role">role =  #role#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="gxxs">gxxs =  #gxxs#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="isdjry">isdjry =  #isdjry#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="postLevel">post_level =  #postLevel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="postName">post_name =  #postName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
					<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tklrfApplyPersonResult">
		INSERT INTO ${zzzcSchema}.t_klrf_apply_person (
		<dynamic prepend=" ">
						<isNotEmpty prepend=" , " property="lrfxryId">lrfxry_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="lrfxId">lrfx_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="xh">xh</isNotEmpty>
						<isNotEmpty prepend=" , " property="empId">emp_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="empName">emp_name</isNotEmpty>
						<isNotEmpty prepend=" , " property="title">title</isNotEmpty>
						<isNotEmpty prepend=" , " property="job">job</isNotEmpty>
						<isNotEmpty prepend=" , " property="education">education</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptCode">dept_code</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptName">dept_name</isNotEmpty>
						<isNotEmpty prepend=" , " property="role">role</isNotEmpty>
						<isNotEmpty prepend=" , " property="gxxs">gxxs</isNotEmpty>
						<isNotEmpty prepend=" , " property="isdjry">isdjry</isNotEmpty>
						<isNotEmpty prepend=" , " property="postLevel">post_level</isNotEmpty>
						<isNotEmpty prepend=" , " property="postName">post_name</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version</isNotEmpty>
				</dynamic>
		) VALUES (
    <dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="lrfxryId">#lrfxryId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="lrfxId">#lrfxId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="xh">#xh#</isNotEmpty>
						<isNotEmpty prepend=" , " property="empId">#empId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="empName">#empName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="title">#title#</isNotEmpty>
						<isNotEmpty prepend=" , " property="job">#job#</isNotEmpty>
						<isNotEmpty prepend=" , " property="education">#education#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptCode">#deptCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptName">#deptName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="role">#role#</isNotEmpty>
						<isNotEmpty prepend=" , " property="gxxs">#gxxs#</isNotEmpty>
						<isNotEmpty prepend=" , " property="isdjry">#isdjry#</isNotEmpty>
						<isNotEmpty prepend=" , " property="postLevel">#postLevel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="postName">#postName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.t_klrf_apply_person
		WHERE 
		    lrfxry_id = #value#

	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.t_klrf_apply_person
		WHERE 
		<dynamic prepend=" ">
						<isNotEmpty prepend=" AND " property="lrfxryId">lrfxry_id=#lrfxryId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="lrfxId">lrfx_id=#lrfxId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="xh">xh=#xh#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="empId">emp_id=#empId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="empName">emp_name=#empName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="title">title=#title#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="job">job=#job#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="education">education=#education#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptCode">dept_code=#deptCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deptName">dept_name=#deptName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="role">role=#role#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="gxxs">gxxs=#gxxs#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="isdjry">isdjry=#isdjry#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="postLevel">post_level=#postLevel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="postName">post_name=#postName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>	
	</delete>

	<update id="update" parameterClass="tklrfApplyPersonResult">
		UPDATE  ${zzzcSchema}.t_klrf_apply_person
		SET 
		<dynamic prepend=" ">
					<isNotEmpty prepend=" , " property="lrfxryId">lrfxry_id=#lrfxryId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="lrfxId">lrfx_id=#lrfxId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="xh">xh=#xh#</isNotEmpty>
						<isNotEmpty prepend=" , " property="empId">emp_id=#empId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="empName">emp_name=#empName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="title">title=#title#</isNotEmpty>
						<isNotEmpty prepend=" , " property="job">job=#job#</isNotEmpty>
						<isNotEmpty prepend=" , " property="education">education=#education#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptCode">dept_code=#deptCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deptName">dept_name=#deptName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="role">role=#role#</isNotEmpty>
						<isNotEmpty prepend=" , " property="gxxs">gxxs=#gxxs#</isNotEmpty>
						<isNotEmpty prepend=" , " property="isdjry">isdjry=#isdjry#</isNotEmpty>
						<isNotEmpty prepend=" , " property="postLevel">post_level=#postLevel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="postName">post_name=#postName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>
		WHERE
		 lrfxry_id =#lrfxryId#
	</update>
	
	

</sqlMap>