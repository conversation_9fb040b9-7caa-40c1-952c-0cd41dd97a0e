<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tktcgPlanNeed">
	<typeAlias alias="tktcgPlanNeedResult" type="com.baosight.bscdkj.common.kt.domain.TktcgPlanNeed"/>
	<select id="load" parameterClass="string" resultClass="tktcgPlanNeedResult">
		SELECT 
						NEED_ID as "needId" , <!-- 需求表ID -->
						PLAN_ID as "planId" , <!-- 年度计划id -->
						SERIAL_NO as "serialNo" , <!-- 流水号 -->
						YEAR_PLAN_NO as "yearPlanNo" , <!-- 年度计划号 -->
						PROJECT_NAME as "projectName" , <!-- 项目名称 -->
						PROJECT_NUM as "projectNum" , <!-- 需求第一步提交生成 -->
						PLAN_YEAR as "planYear" , <!-- 计划年度 -->
						DEPT_CODE as "deptCode" , <!-- 所在单位部门 -->
						GLDW_CODE as "gldwCode" , <!-- 项目管理单位 -->
						TCR_USER_CODE as "tcrUserCode" , <!-- 提出人工号 -->
						TCR_CONTACT_TEL as "tcrContactTel" , <!-- 提出人联系电话 -->
						PLAN_TYPE as "planType" , <!-- 采购类别 -->
						XMCJDEPT_CODE as "xmcjdeptCode" , <!-- 项目参加部门 -->
						PLAN_FINISH_DATE as "planFinishDate" , <!-- 建议完成时间 -->
						PLAN_YJF as "planYjf" , <!-- 拟引进方 -->
						XMZG_USER_CODE as "xmzgUserCode" , <!-- 项目主管工号 -->
						EXTRA1 as "extra1" , <!-- 扩展字段1 -->
						EXTRA2 as "extra2" , <!-- 扩展字段2 -->
						EXTRA3 as "extra3" , <!-- 扩展字段3 -->
						EXTRA4 as "extra4" , <!-- 扩展字段4 -->
						EXTRA5 as "extra5" , <!-- 扩展字段5 -->
						DEL_STATUS as "delStatus" , <!-- 删除状态 -->
						CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
						CREATE_DATE as "createDate" , <!-- 创建时间 -->
						UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
						UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
						DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
						DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
						RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
					FROM ${zzzcSchema}.T_KTCG_PLAN_NEED
		WHERE
			NEED_ID = #needId#
		
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tktcgPlanNeedResult">
		SELECT
						NEED_ID as "needId" ,			
						PLAN_ID as "planId" ,			
						SERIAL_NO as "serialNo" ,			
						YEAR_PLAN_NO as "yearPlanNo" ,			
						PROJECT_NAME as "projectName" ,			
						PROJECT_NUM as "projectNum" ,			
						PLAN_YEAR as "planYear" ,			
						DEPT_CODE as "deptCode" ,			
						GLDW_CODE as "gldwCode" ,			
						TCR_USER_CODE as "tcrUserCode" ,			
						TCR_CONTACT_TEL as "tcrContactTel" ,			
						PLAN_TYPE as "planType" ,			
						XMCJDEPT_CODE as "xmcjdeptCode" ,			
						PLAN_FINISH_DATE as "planFinishDate" ,			
						PLAN_YJF as "planYjf" ,			
						XMZG_USER_CODE as "xmzgUserCode" ,			
						EXTRA1 as "extra1" ,			
						EXTRA2 as "extra2" ,			
						EXTRA3 as "extra3" ,			
						EXTRA4 as "extra4" ,			
						EXTRA5 as "extra5" ,			
						DEL_STATUS as "delStatus" ,			
						CREATE_USER_LABEL as "createUserLabel" ,			
						CREATE_DATE as "createDate" ,			
						UPDATE_USER_LABEL as "updateUserLabel" ,			
						UPDATE_DATE as "updateDate" ,			
						DELETE_USER_LABEL as "deleteUserLabel" ,			
						DELETE_DATE as "deleteDate" ,			
						RECORD_VERSION as "recordVersion" 			
					FROM ${zzzcSchema}.T_KTCG_PLAN_NEED
		<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="needId">NEED_ID =  #needId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planId">PLAN_ID =  #planId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="serialNo">SERIAL_NO =  #serialNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="yearPlanNo">YEAR_PLAN_NO =  #yearPlanNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectName">PROJECT_NAME =  #projectName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectNum">PROJECT_NUM =  #projectNum#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planYear">PLAN_YEAR =  #planYear#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE =  #deptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="tcrUserCode">TCR_USER_CODE =  #tcrUserCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="tcrContactTel">TCR_CONTACT_TEL =  #tcrContactTel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planType">PLAN_TYPE =  #planType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="xmcjdeptCode">XMCJDEPT_CODE =  #xmcjdeptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planFinishDate">PLAN_FINISH_DATE =  #planFinishDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planYjf">PLAN_YJF =  #planYjf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="xmzgUserCode">XMZG_USER_CODE =  #xmzgUserCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KTCG_PLAN_NEED
		<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="needId">NEED_ID =  #needId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planId">PLAN_ID =  #planId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="serialNo">SERIAL_NO =  #serialNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="yearPlanNo">YEAR_PLAN_NO =  #yearPlanNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectName">PROJECT_NAME =  #projectName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectNum">PROJECT_NUM =  #projectNum#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planYear">PLAN_YEAR =  #planYear#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE =  #deptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE =  #gldwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="tcrUserCode">TCR_USER_CODE =  #tcrUserCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="tcrContactTel">TCR_CONTACT_TEL =  #tcrContactTel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planType">PLAN_TYPE =  #planType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="xmcjdeptCode">XMCJDEPT_CODE =  #xmcjdeptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planFinishDate">PLAN_FINISH_DATE =  #planFinishDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planYjf">PLAN_YJF =  #planYjf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="xmzgUserCode">XMZG_USER_CODE =  #xmzgUserCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tktcgPlanNeedResult">
		INSERT INTO ${zzzcSchema}.T_KTCG_PLAN_NEED ( 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="needId">NEED_ID</isNotEmpty>
				<isNotEmpty prepend=" , " property="planId">PLAN_ID</isNotEmpty>
				<isNotEmpty prepend=" , " property="serialNo">SERIAL_NO</isNotEmpty>
				<isNotEmpty prepend=" , " property="yearPlanNo">YEAR_PLAN_NO</isNotEmpty>
				<isNotEmpty prepend=" , " property="projectName">PROJECT_NAME</isNotEmpty>
				<isNotEmpty prepend=" , " property="projectNum">PROJECT_NUM</isNotEmpty>
				<isNotEmpty prepend=" , " property="planYear">PLAN_YEAR</isNotEmpty>
				<isNotEmpty prepend=" , " property="deptCode">DEPT_CODE</isNotEmpty>
				<isNotEmpty prepend=" , " property="gldwCode">GLDW_CODE</isNotEmpty>
				<isNotEmpty prepend=" , " property="tcrUserCode">TCR_USER_CODE</isNotEmpty>
				<isNotEmpty prepend=" , " property="tcrContactTel">TCR_CONTACT_TEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="planType">PLAN_TYPE</isNotEmpty>
				<isNotEmpty prepend=" , " property="xmcjdeptCode">XMCJDEPT_CODE</isNotEmpty>
				<isNotEmpty prepend=" , " property="planFinishDate">PLAN_FINISH_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="planYjf">PLAN_YJF</isNotEmpty>
				<isNotEmpty prepend=" , " property="xmzgUserCode">XMZG_USER_CODE</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="needId">#needId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="planId">#planId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="serialNo">#serialNo#</isNotEmpty>
				<isNotEmpty prepend=" , " property="yearPlanNo">#yearPlanNo#</isNotEmpty>
				<isNotEmpty prepend=" , " property="projectName">#projectName#</isNotEmpty>
				<isNotEmpty prepend=" , " property="projectNum">#projectNum#</isNotEmpty>
				<isNotEmpty prepend=" , " property="planYear">#planYear#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deptCode">#deptCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="gldwCode">#gldwCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="tcrUserCode">#tcrUserCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="tcrContactTel">#tcrContactTel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="planType">#planType#</isNotEmpty>
				<isNotEmpty prepend=" , " property="xmcjdeptCode">#xmcjdeptCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="planFinishDate">#planFinishDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="planYjf">#planYjf#</isNotEmpty>
				<isNotEmpty prepend=" , " property="xmzgUserCode">#xmzgUserCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KTCG_PLAN_NEED
		WHERE 
		    NEED_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KTCG_PLAN_NEED
		WHERE 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" AND " property="needId">NEED_ID=#needId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planId">PLAN_ID=#planId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="serialNo">SERIAL_NO=#serialNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="yearPlanNo">YEAR_PLAN_NO=#yearPlanNo#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectName">PROJECT_NAME=#projectName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectNum">PROJECT_NUM=#projectNum#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planYear">PLAN_YEAR=#planYear#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deptCode">DEPT_CODE=#deptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="gldwCode">GLDW_CODE=#gldwCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="tcrUserCode">TCR_USER_CODE=#tcrUserCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="tcrContactTel">TCR_CONTACT_TEL=#tcrContactTel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planType">PLAN_TYPE=#planType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="xmcjdeptCode">XMCJDEPT_CODE=#xmcjdeptCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planFinishDate">PLAN_FINISH_DATE=#planFinishDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="planYjf">PLAN_YJF=#planYjf#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="xmzgUserCode">XMZG_USER_CODE=#xmzgUserCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
				</dynamic>	
	</delete>

	<update id="update" parameterClass="tktcgPlanNeedResult">
		UPDATE  ${zzzcSchema}.T_KTCG_PLAN_NEED	
		SET 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="needId">NEED_ID=#needId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="planId">PLAN_ID=#planId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="serialNo">SERIAL_NO=#serialNo#</isNotEmpty>
				<isNotEmpty prepend=" , " property="yearPlanNo">YEAR_PLAN_NO=#yearPlanNo#</isNotEmpty>
				<isNotEmpty prepend=" , " property="projectName">PROJECT_NAME=#projectName#</isNotEmpty>
				<isNotEmpty prepend=" , " property="projectNum">PROJECT_NUM=#projectNum#</isNotEmpty>
				<isNotEmpty prepend=" , " property="planYear">PLAN_YEAR=#planYear#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deptCode">DEPT_CODE=#deptCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="gldwCode">GLDW_CODE=#gldwCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="tcrUserCode">TCR_USER_CODE=#tcrUserCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="tcrContactTel">TCR_CONTACT_TEL=#tcrContactTel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="planType">PLAN_TYPE=#planType#</isNotEmpty>
				<isNotEmpty prepend=" , " property="xmcjdeptCode">XMCJDEPT_CODE=#xmcjdeptCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="planFinishDate">PLAN_FINISH_DATE=#planFinishDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="planYjf">PLAN_YJF=#planYjf#</isNotEmpty>
				<isNotEmpty prepend=" , " property="xmzgUserCode">XMZG_USER_CODE=#xmzgUserCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
				</dynamic>
		WHERE
		NEED_ID =#needId#
	</update>

</sqlMap>