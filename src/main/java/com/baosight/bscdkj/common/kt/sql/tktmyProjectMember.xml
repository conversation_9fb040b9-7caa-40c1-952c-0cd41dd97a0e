<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tktmyProjectMember">
	<typeAlias alias="tktmyProjectMemberResult" type="com.baosight.bscdkj.common.kt.domain.TktmyProjectMember"/>
	<select id="load" parameterClass="string" resultClass="tktmyProjectMemberResult">
		SELECT 
						ROW_ID as "rowId" , <!-- 记录ID -->
						SOURCE_ID as "sourceId" , <!-- 来源ID -->
						SOURCE_TYPE as "sourceType" , <!-- 来源类型 -->
						SOURCE_CODE as "sourceCode" , <!-- 来源编码 -->
						SOURCE_NAME as "sourceName" , <!-- 来源名称 -->
						SOURCE_COMMENT as "sourceComment" , <!-- 来源意见 -->
						SOURCE_TIME as "sourceTime" , <!-- 来源时间 -->
						SOURCE_STATUS as "sourceStatus" , <!-- 来源状态 -->
						EXTRA1 as "extra1" , <!-- 扩展字段1 -->
						EXTRA2 as "extra2" , <!-- 扩展字段2 -->
						EXTRA3 as "extra3" , <!-- 扩展字段3 -->
						DEL_STATUS as "delStatus" , <!-- 删除状态 -->
						CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
						CREATE_DATE as "createDate" , <!-- 创建时间 -->
						UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
						UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
						DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
						DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
						RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
					FROM ${zzzcSchema}.T_KTMY_PROJECT_MEMBER
		WHERE
			ROW_ID = #rowId#
		
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tktmyProjectMemberResult">
		SELECT
						ROW_ID as "rowId" ,			
						SOURCE_ID as "sourceId" ,			
						SOURCE_TYPE as "sourceType" ,			
						SOURCE_CODE as "sourceCode" ,			
						SOURCE_NAME as "sourceName" ,			
						SOURCE_COMMENT as "sourceComment" ,			
						SOURCE_TIME as "sourceTime" ,			
						SOURCE_STATUS as "sourceStatus" ,			
						EXTRA1 as "extra1" ,			
						EXTRA2 as "extra2" ,			
						EXTRA3 as "extra3" ,			
						DEL_STATUS as "delStatus" ,			
						CREATE_USER_LABEL as "createUserLabel" ,			
						CREATE_DATE as "createDate" ,			
						UPDATE_USER_LABEL as "updateUserLabel" ,			
						UPDATE_DATE as "updateDate" ,			
						DELETE_USER_LABEL as "deleteUserLabel" ,			
						DELETE_DATE as "deleteDate" ,			
						RECORD_VERSION as "recordVersion" 			
					FROM ${zzzcSchema}.T_KTMY_PROJECT_MEMBER
		<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="rowId">ROW_ID =  #rowId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceId">SOURCE_ID =  #sourceId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceType">SOURCE_TYPE =  #sourceType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceCode">SOURCE_CODE =  #sourceCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceName">SOURCE_NAME =  #sourceName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceComment">SOURCE_COMMENT =  #sourceComment#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceTime">SOURCE_TIME =  #sourceTime#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceStatus">SOURCE_STATUS =  #sourceStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KTMY_PROJECT_MEMBER
		<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="rowId">ROW_ID =  #rowId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceId">SOURCE_ID =  #sourceId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceType">SOURCE_TYPE =  #sourceType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceCode">SOURCE_CODE =  #sourceCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceName">SOURCE_NAME =  #sourceName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceComment">SOURCE_COMMENT =  #sourceComment#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceTime">SOURCE_TIME =  #sourceTime#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceStatus">SOURCE_STATUS =  #sourceStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tktmyProjectMemberResult">
		INSERT INTO ${zzzcSchema}.T_KTMY_PROJECT_MEMBER ( 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="rowId">ROW_ID</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceId">SOURCE_ID</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceType">SOURCE_TYPE</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceCode">SOURCE_CODE</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceName">SOURCE_NAME</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceComment">SOURCE_COMMENT</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceTime">SOURCE_TIME</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceStatus">SOURCE_STATUS</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="rowId">#rowId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceId">#sourceId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceType">#sourceType#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceCode">#sourceCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceName">#sourceName#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceComment">#sourceComment#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceTime">#sourceTime#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceStatus">#sourceStatus#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KTMY_PROJECT_MEMBER
		WHERE 
		    ROW_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KTMY_PROJECT_MEMBER
		WHERE 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" AND " property="rowId">ROW_ID=#rowId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceId">SOURCE_ID=#sourceId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceType">SOURCE_TYPE=#sourceType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceCode">SOURCE_CODE=#sourceCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceName">SOURCE_NAME=#sourceName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceComment">SOURCE_COMMENT=#sourceComment#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceTime">SOURCE_TIME=#sourceTime#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="sourceStatus">SOURCE_STATUS=#sourceStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
				</dynamic>	
	</delete>

	<update id="update" parameterClass="tktmyProjectMemberResult">
		UPDATE  ${zzzcSchema}.T_KTMY_PROJECT_MEMBER	
		SET 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="rowId">ROW_ID=#rowId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceId">SOURCE_ID=#sourceId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceType">SOURCE_TYPE=#sourceType#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceCode">SOURCE_CODE=#sourceCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceName">SOURCE_NAME=#sourceName#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceComment">SOURCE_COMMENT=#sourceComment#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceTime">SOURCE_TIME=#sourceTime#</isNotEmpty>
				<isNotEmpty prepend=" , " property="sourceStatus">SOURCE_STATUS=#sourceStatus#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
				</dynamic>
		WHERE
		ROW_ID =#rowId#
	</update>

</sqlMap>