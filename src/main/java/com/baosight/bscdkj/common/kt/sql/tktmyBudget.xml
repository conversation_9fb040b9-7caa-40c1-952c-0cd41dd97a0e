<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tktmyBudget">
	<typeAlias alias="tktmyBudgetResult" type="com.baosight.bscdkj.common.kt.domain.TktmyBudget"/>
	<select id="load" parameterClass="string" resultClass="tktmyBudgetResult">
		SELECT 
						YS_ID as "ysId" , <!-- 经费预算ID -->
						HT_ID as "htId" , <!-- 合同ID -->
						YS_CODE as "ysCode" , <!-- 经费预算代码 -->
						YS_TOTAL1 as "ysTotal1" , <!-- 节点1 -->
						YS_TOTAL2 as "ysTotal2" , <!-- 节点2 -->
						YS_TOTAL3 as "ysTotal3" , <!-- 节点3 -->
						YS_TOTAL4 as "ysTotal4" , <!-- 节点4 -->
						YS_TOTAL5 as "ysTotal5" , <!-- 节点5 -->
						YS_TOTAL as "ysTotal" , <!-- 经费预算 -->
						EXTRA1 as "extra1" , <!-- 扩展字段1 -->
						EXTRA2 as "extra2" , <!-- 扩展字段2 -->
						EXTRA3 as "extra3" , <!-- 扩展字段3 -->
						EXTRA4 as "extra4" , <!-- 扩展字段4 -->
						EXTRA5 as "extra5" , <!-- 扩展字段5 -->
						DEL_STATUS as "delStatus" , <!-- 删除状态 -->
						CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
						CREATE_DATE as "createDate" , <!-- 创建时间 -->
						UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
						UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
						DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
						DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
						RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
					FROM ${zzzcSchema}.T_KTMY_BUDGET
		WHERE
			YS_ID = #ysId#
		
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tktmyBudgetResult">
		SELECT
						YS_ID as "ysId" ,			
						HT_ID as "htId" ,			
						YS_CODE as "ysCode" ,			
						YS_TOTAL1 as "ysTotal1" ,			
						YS_TOTAL2 as "ysTotal2" ,			
						YS_TOTAL3 as "ysTotal3" ,			
						YS_TOTAL4 as "ysTotal4" ,			
						YS_TOTAL5 as "ysTotal5" ,			
						YS_TOTAL as "ysTotal" ,			
						EXTRA1 as "extra1" ,			
						EXTRA2 as "extra2" ,			
						EXTRA3 as "extra3" ,			
						EXTRA4 as "extra4" ,			
						EXTRA5 as "extra5" ,			
						DEL_STATUS as "delStatus" ,			
						CREATE_USER_LABEL as "createUserLabel" ,			
						CREATE_DATE as "createDate" ,			
						UPDATE_USER_LABEL as "updateUserLabel" ,			
						UPDATE_DATE as "updateDate" ,			
						DELETE_USER_LABEL as "deleteUserLabel" ,			
						DELETE_DATE as "deleteDate" ,			
						RECORD_VERSION as "recordVersion" 			
					FROM ${zzzcSchema}.T_KTMY_BUDGET
		<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="ysId">YS_ID =  #ysId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="htId">HT_ID =  #htId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysCode">YS_CODE =  #ysCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal1">YS_TOTAL1 =  #ysTotal1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal2">YS_TOTAL2 =  #ysTotal2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal3">YS_TOTAL3 =  #ysTotal3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal4">YS_TOTAL4 =  #ysTotal4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal5">YS_TOTAL5 =  #ysTotal5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal">YS_TOTAL =  #ysTotal#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KTMY_BUDGET
		<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="ysId">YS_ID =  #ysId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="htId">HT_ID =  #htId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysCode">YS_CODE =  #ysCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal1">YS_TOTAL1 =  #ysTotal1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal2">YS_TOTAL2 =  #ysTotal2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal3">YS_TOTAL3 =  #ysTotal3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal4">YS_TOTAL4 =  #ysTotal4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal5">YS_TOTAL5 =  #ysTotal5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal">YS_TOTAL =  #ysTotal#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tktmyBudgetResult">
		INSERT INTO ${zzzcSchema}.T_KTMY_BUDGET ( 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="ysId">YS_ID</isNotEmpty>
				<isNotEmpty prepend=" , " property="htId">HT_ID</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysCode">YS_CODE</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal1">YS_TOTAL1</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal2">YS_TOTAL2</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal3">YS_TOTAL3</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal4">YS_TOTAL4</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal5">YS_TOTAL5</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal">YS_TOTAL</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="ysId">#ysId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="htId">#htId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysCode">#ysCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal1">#ysTotal1#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal2">#ysTotal2#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal3">#ysTotal3#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal4">#ysTotal4#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal5">#ysTotal5#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal">#ysTotal#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KTMY_BUDGET
		WHERE 
		    YS_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KTMY_BUDGET
		WHERE 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" AND " property="ysId">YS_ID=#ysId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="htId">HT_ID=#htId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysCode">YS_CODE=#ysCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal1">YS_TOTAL1=#ysTotal1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal2">YS_TOTAL2=#ysTotal2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal3">YS_TOTAL3=#ysTotal3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal4">YS_TOTAL4=#ysTotal4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal5">YS_TOTAL5=#ysTotal5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="ysTotal">YS_TOTAL=#ysTotal#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
				</dynamic>	
	</delete>

	<update id="update" parameterClass="tktmyBudgetResult">
		UPDATE  ${zzzcSchema}.T_KTMY_BUDGET	
		SET 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="ysId">YS_ID=#ysId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="htId">HT_ID=#htId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysCode">YS_CODE=#ysCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal1">YS_TOTAL1=#ysTotal1#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal2">YS_TOTAL2=#ysTotal2#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal3">YS_TOTAL3=#ysTotal3#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal4">YS_TOTAL4=#ysTotal4#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal5">YS_TOTAL5=#ysTotal5#</isNotEmpty>
				<isNotEmpty prepend=" , " property="ysTotal">YS_TOTAL=#ysTotal#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
				</dynamic>
		WHERE
		YS_ID =#ysId#
	</update>

</sqlMap>