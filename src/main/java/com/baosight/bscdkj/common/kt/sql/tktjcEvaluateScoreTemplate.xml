<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tktjcEvaluateScoreTemplate">
	<typeAlias alias="tktjcEvaluateScoreTemplateResult" type="com.baosight.bscdkj.common.kt.domain.TktjcEvaluateScoreTemplate"/>
	<select id="load" parameterClass="string" resultClass="tktjcEvaluateScoreTemplateResult">
		SELECT 
						RECORD_GUID as "recordGuid" , <!-- 主键 -->
						PROJECT_GUID as "projectGuid" , <!-- 项目主键 -->
						CATEGORY as "category" , <!-- 类型 -->
						TYPE as "type" , <!-- 类型项中的分类 -->
						ITEM_CODE as "itemCode" , <!-- 评分项编码 -->
						ITEM as "item" , <!-- 评分项 -->
						SCORE_RANGE as "scoreRange" , <!-- 分数范围 -->
						XH as "xh" , <!-- 序号 -->
						TEMPLATE_TYPE as "templateType" , <!-- 模板类型 -->
						EXTRA1 as "extra1" , <!-- 扩展字段1 -->
						EXTRA2 as "extra2" , <!-- 扩展字段2 -->
						EXTRA3 as "extra3" , <!-- 扩展字段3 -->
						EXTRA4 as "extra4" , <!-- 扩展字段4 -->
						EXTRA5 as "extra5" , <!-- 扩展字段5 -->
						CREATE_DATE as "createDate" , <!-- 记录创建日期 -->
						UPDATE_DATE as "updateDate" , <!-- 记录修改日期 -->
						DELETE_DATE as "deleteDate" , <!-- 记录删除日期 -->
						CREATE_USER_LABEL as "createUserLabel" , <!-- 记录创建人 -->
						UPDATE_USER_LABEL as "updateUserLabel" , <!-- 记录修改人 -->
						DELETE_USER_LABEL as "deleteUserLabel" , <!-- 记录删除人 -->
						RECORD_VERSION as "recordVersion" , <!-- 记录版本号 -->
						DEL_STATUS as "delStatus"  <!-- 删除状态 -->
					FROM ${zzzcSchema}.T_KTJC_EVALUATE_SCORE_TEMPLATE
		WHERE
			RECORD_GUID = #recordGuid#
		
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tktjcEvaluateScoreTemplateResult">
		SELECT
						RECORD_GUID as "recordGuid" ,			
						PROJECT_GUID as "projectGuid" ,			
						CATEGORY as "category" ,			
						TYPE as "type" ,			
						ITEM_CODE as "itemCode" ,			
						ITEM as "item" ,			
						SCORE_RANGE as "scoreRange" ,			
						XH as "xh" ,			
						TEMPLATE_TYPE as "templateType" ,			
						EXTRA1 as "extra1" ,			
						EXTRA2 as "extra2" ,			
						EXTRA3 as "extra3" ,			
						EXTRA4 as "extra4" ,			
						EXTRA5 as "extra5" ,			
						CREATE_DATE as "createDate" ,			
						UPDATE_DATE as "updateDate" ,			
						DELETE_DATE as "deleteDate" ,			
						CREATE_USER_LABEL as "createUserLabel" ,			
						UPDATE_USER_LABEL as "updateUserLabel" ,			
						DELETE_USER_LABEL as "deleteUserLabel" ,			
						RECORD_VERSION as "recordVersion" ,			
						DEL_STATUS as "delStatus" 			
					FROM ${zzzcSchema}.T_KTJC_EVALUATE_SCORE_TEMPLATE
		<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="recordGuid">RECORD_GUID =  #recordGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectGuid">PROJECT_GUID =  #projectGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="category">CATEGORY =  #category#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="type">TYPE =  #type#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="itemCode">ITEM_CODE =  #itemCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="item">ITEM =  #item#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="scoreRange">SCORE_RANGE =  #scoreRange#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="xh">XH =  #xh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="templateType">TEMPLATE_TYPE =  #templateType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KTJC_EVALUATE_SCORE_TEMPLATE
		<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="recordGuid">RECORD_GUID =  #recordGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectGuid">PROJECT_GUID =  #projectGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="category">CATEGORY =  #category#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="type">TYPE =  #type#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="itemCode">ITEM_CODE =  #itemCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="item">ITEM =  #item#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="scoreRange">SCORE_RANGE =  #scoreRange#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="xh">XH =  #xh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="templateType">TEMPLATE_TYPE =  #templateType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4 =  #extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5 =  #extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tktjcEvaluateScoreTemplateResult">
		INSERT INTO ${zzzcSchema}.T_KTJC_EVALUATE_SCORE_TEMPLATE ( 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="recordGuid">RECORD_GUID</isNotEmpty>
				<isNotEmpty prepend=" , " property="projectGuid">PROJECT_GUID</isNotEmpty>
				<isNotEmpty prepend=" , " property="category">CATEGORY</isNotEmpty>
				<isNotEmpty prepend=" , " property="type">TYPE</isNotEmpty>
				<isNotEmpty prepend=" , " property="itemCode">ITEM_CODE</isNotEmpty>
				<isNotEmpty prepend=" , " property="item">ITEM</isNotEmpty>
				<isNotEmpty prepend=" , " property="scoreRange">SCORE_RANGE</isNotEmpty>
				<isNotEmpty prepend=" , " property="xh">XH</isNotEmpty>
				<isNotEmpty prepend=" , " property="templateType">TEMPLATE_TYPE</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra4">EXTRA4</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra5">EXTRA5</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="recordGuid">#recordGuid#</isNotEmpty>
				<isNotEmpty prepend=" , " property="projectGuid">#projectGuid#</isNotEmpty>
				<isNotEmpty prepend=" , " property="category">#category#</isNotEmpty>
				<isNotEmpty prepend=" , " property="type">#type#</isNotEmpty>
				<isNotEmpty prepend=" , " property="itemCode">#itemCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="item">#item#</isNotEmpty>
				<isNotEmpty prepend=" , " property="scoreRange">#scoreRange#</isNotEmpty>
				<isNotEmpty prepend=" , " property="xh">#xh#</isNotEmpty>
				<isNotEmpty prepend=" , " property="templateType">#templateType#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KTJC_EVALUATE_SCORE_TEMPLATE
		WHERE 
		    RECORD_GUID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KTJC_EVALUATE_SCORE_TEMPLATE
		WHERE 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" AND " property="recordGuid">RECORD_GUID=#recordGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="projectGuid">PROJECT_GUID=#projectGuid#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="category">CATEGORY=#category#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="type">TYPE=#type#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="itemCode">ITEM_CODE=#itemCode#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="item">ITEM=#item#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="scoreRange">SCORE_RANGE=#scoreRange#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="xh">XH=#xh#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="templateType">TEMPLATE_TYPE=#templateType#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra4">EXTRA4=#extra4#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra5">EXTRA5=#extra5#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
				</dynamic>	
	</delete>

	<update id="update" parameterClass="tktjcEvaluateScoreTemplateResult">
		UPDATE  ${zzzcSchema}.T_KTJC_EVALUATE_SCORE_TEMPLATE	
		SET 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="recordGuid">RECORD_GUID=#recordGuid#</isNotEmpty>
				<isNotEmpty prepend=" , " property="projectGuid">PROJECT_GUID=#projectGuid#</isNotEmpty>
				<isNotEmpty prepend=" , " property="category">CATEGORY=#category#</isNotEmpty>
				<isNotEmpty prepend=" , " property="type">TYPE=#type#</isNotEmpty>
				<isNotEmpty prepend=" , " property="itemCode">ITEM_CODE=#itemCode#</isNotEmpty>
				<isNotEmpty prepend=" , " property="item">ITEM=#item#</isNotEmpty>
				<isNotEmpty prepend=" , " property="scoreRange">SCORE_RANGE=#scoreRange#</isNotEmpty>
				<isNotEmpty prepend=" , " property="xh">XH=#xh#</isNotEmpty>
				<isNotEmpty prepend=" , " property="templateType">TEMPLATE_TYPE=#templateType#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra4">EXTRA4=#extra4#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra5">EXTRA5=#extra5#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
				</dynamic>
		WHERE
		RECORD_GUID =#recordGuid#
	</update>

</sqlMap>