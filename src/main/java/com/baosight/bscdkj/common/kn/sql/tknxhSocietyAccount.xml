<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tknxhSocietyAccount">
	<typeAlias alias="tknxhSocietyAccountResult" type="com.baosight.bscdkj.common.kn.domain.TknxhSocietyAccount"/>
	<select id="load" parameterClass="string" resultClass="tknxhSocietyAccountResult">
		SELECT 
						ACCOUNT_ID as "accountId" , <!-- 账户ID -->
						SOCIETY_ID as "societyId" , <!-- 学会ID -->
						BANK_NUMBER as "bankNumber" , <!-- 账户 -->
						BANK_NAME as "bankName" , <!-- 开户行 -->
						EXTRA1 as "extra1" , <!-- 扩展字段1 -->
						EXTRA2 as "extra2" , <!-- 扩展字段2 -->
						EXTRA3 as "extra3" , <!-- 扩展字段3 -->
						DEL_STATUS as "delStatus" , <!-- 删除状态 -->
						CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
						CREATE_DATE as "createDate" , <!-- 创建时间 -->
						UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
						UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
						DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
						DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
						RECORD_VERSION as "recordVersion"  <!-- 版本号 -->
					FROM ${zzzcSchema}.T_KNXH_SOCIETY_ACCOUNT
		WHERE
			ACCOUNT_ID = #accountId#
		
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tknxhSocietyAccountResult">
		SELECT
						ACCOUNT_ID as "accountId" ,			
						SOCIETY_ID as "societyId" ,			
						BANK_NUMBER as "bankNumber" ,			
						BANK_NAME as "bankName" ,			
						EXTRA1 as "extra1" ,			
						EXTRA2 as "extra2" ,			
						EXTRA3 as "extra3" ,			
						DEL_STATUS as "delStatus" ,			
						CREATE_USER_LABEL as "createUserLabel" ,			
						CREATE_DATE as "createDate" ,			
						UPDATE_USER_LABEL as "updateUserLabel" ,			
						UPDATE_DATE as "updateDate" ,			
						DELETE_USER_LABEL as "deleteUserLabel" ,			
						DELETE_DATE as "deleteDate" ,			
						RECORD_VERSION as "recordVersion" 			
					FROM ${zzzcSchema}.T_KNXH_SOCIETY_ACCOUNT
		<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="accountId">ACCOUNT_ID =  #accountId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="societyId">SOCIETY_ID =  #societyId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bankNumber">BANK_NUMBER =  #bankNumber#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bankName">BANK_NAME =  #bankName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.T_KNXH_SOCIETY_ACCOUNT
		<dynamic prepend="WHERE">
				<isNotEmpty prepend=" AND " property="accountId">ACCOUNT_ID =  #accountId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="societyId">SOCIETY_ID =  #societyId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bankNumber">BANK_NUMBER =  #bankNumber#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bankName">BANK_NAME =  #bankName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1 =  #extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2 =  #extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3 =  #extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS =  #delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL =  #createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE =  #createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL =  #updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE =  #updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL =  #deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE =  #deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION =  #recordVersion#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tknxhSocietyAccountResult">
		INSERT INTO ${zzzcSchema}.T_KNXH_SOCIETY_ACCOUNT ( 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="accountId">ACCOUNT_ID</isNotEmpty>
				<isNotEmpty prepend=" , " property="societyId">SOCIETY_ID</isNotEmpty>
				<isNotEmpty prepend=" , " property="bankNumber">BANK_NUMBER</isNotEmpty>
				<isNotEmpty prepend=" , " property="bankName">BANK_NAME</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">EXTRA1</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">EXTRA2</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">EXTRA3</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">CREATE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION</isNotEmpty>
				</dynamic>
		) VALUES (
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="accountId">#accountId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="societyId">#societyId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="bankNumber">#bankNumber#</isNotEmpty>
				<isNotEmpty prepend=" , " property="bankName">#bankName#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.T_KNXH_SOCIETY_ACCOUNT
		WHERE 
		    ACCOUNT_ID = #value#
	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.T_KNXH_SOCIETY_ACCOUNT
		WHERE 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" AND " property="accountId">ACCOUNT_ID=#accountId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="societyId">SOCIETY_ID=#societyId#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bankNumber">BANK_NUMBER=#bankNumber#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="bankName">BANK_NAME=#bankName#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra1">EXTRA1=#extra1#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra2">EXTRA2=#extra2#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="extra3">EXTRA3=#extra3#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" AND " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
				</dynamic>	
	</delete>

	<update id="update" parameterClass="tknxhSocietyAccountResult">
		UPDATE  ${zzzcSchema}.T_KNXH_SOCIETY_ACCOUNT	
		SET 
		<dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="accountId">ACCOUNT_ID=#accountId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="societyId">SOCIETY_ID=#societyId#</isNotEmpty>
				<isNotEmpty prepend=" , " property="bankNumber">BANK_NUMBER=#bankNumber#</isNotEmpty>
				<isNotEmpty prepend=" , " property="bankName">BANK_NAME=#bankName#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra1">EXTRA1=#extra1#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra2">EXTRA2=#extra2#</isNotEmpty>
				<isNotEmpty prepend=" , " property="extra3">EXTRA3=#extra3#</isNotEmpty>
				<isNotEmpty prepend=" , " property="delStatus">DEL_STATUS=#delStatus#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createUserLabel">CREATE_USER_LABEL=#createUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="createDate">CREATE_DATE=#createDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateUserLabel">UPDATE_USER_LABEL=#updateUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="updateDate">UPDATE_DATE=#updateDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteUserLabel">DELETE_USER_LABEL=#deleteUserLabel#</isNotEmpty>
				<isNotEmpty prepend=" , " property="deleteDate">DELETE_DATE=#deleteDate#</isNotEmpty>
				<isNotEmpty prepend=" , " property="recordVersion">RECORD_VERSION=#recordVersion#</isNotEmpty>
				</dynamic>
		WHERE
		ACCOUNT_ID =#accountId#
	</update>

</sqlMap>