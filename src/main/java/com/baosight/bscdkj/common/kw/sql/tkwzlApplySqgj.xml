<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkwzlApplySqgj">
	<typeAlias alias="tkwzlApplySqgjResult" type="com.baosight.bscdkj.common.kw.domain.TkwzlApplySqgj"/>
	<select id="load" parameterClass="string" resultClass="tkwzlApplySqgjResult">
		SELECT
				sqgj_id as "sqgjId" ,
				gj_name as "gjName" ,
				gj_code as "gjCode" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" 
				FROM ${zzzcSchema}.t_kwzl_apply_sqgj
		WHERE
				sqgj_id = #sqgjId#
		
	</select>
	
	<select id="query"  parameterClass="hashmap" resultClass="tkwzlApplySqgjResult">
		SELECT
				sqgj_id as "sqgjId" ,
				gj_name as "gjName" ,
				gj_code as "gjCode" ,
				extra1 as "extra1" ,
				extra2 as "extra2" ,
				extra3 as "extra3" ,
				extra4 as "extra4" ,
				extra5 as "extra5" ,
				del_status as "delStatus" ,
				create_user_label as "createUserLabel" ,
				create_date as "createDate" ,
				update_user_label as "updateUserLabel" ,
				update_date as "updateDate" ,
				delete_user_label as "deleteUserLabel" ,
				delete_date as "deleteDate" ,
				record_version as "recordVersion" 
				FROM ${zzzcSchema}.t_kwzl_apply_sqgj
			<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="sqgjId">sqgj_id =  #sqgjId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="gjName">gj_name =  #gjName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="gjCode">gj_code =  #gjCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
				
				<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>	
				<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*) 
		FROM ${zzzcSchema}.t_kwzl_apply_sqgj
		<dynamic prepend="WHERE">
						<isNotEmpty prepend=" AND " property="sqgjId">sqgj_id =  #sqgjId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="gjName">gj_name =  #gjName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="gjCode">gj_code =  #gjCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
					<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>
		
	<insert id="insert" parameterClass="tkwzlApplySqgjResult">
		INSERT INTO ${zzzcSchema}.t_kwzl_apply_sqgj ( 
		<dynamic prepend=" ">
						<isNotEmpty prepend=" , " property="sqgjId">sqgj_id</isNotEmpty>
						<isNotEmpty prepend=" , " property="gjName">gj_name</isNotEmpty>
						<isNotEmpty prepend=" , " property="gjCode">gj_code</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version</isNotEmpty>
				</dynamic>
		) VALUES (
    <dynamic prepend=" ">
				<isNotEmpty prepend=" , " property="sqgjId">#sqgjId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="gjName">#gjName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="gjCode">#gjCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
				</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.t_kwzl_apply_sqgj
		WHERE 
		    sqgj_id = #value#

	</delete>
	
	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.t_kwzl_apply_sqgj
		WHERE 
		<dynamic prepend=" ">
						<isNotEmpty prepend=" AND " property="sqgjId">sqgj_id=#sqgjId#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="gjName">gj_name=#gjName#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="gjCode">gj_code=#gjCode#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" AND " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>	
	</delete>

	<update id="update" parameterClass="tkwzlApplySqgjResult">
		UPDATE  ${zzzcSchema}.t_kwzl_apply_sqgj	
		SET 
		<dynamic prepend=" ">
					<isNotEmpty prepend=" , " property="sqgjId">sqgj_id=#sqgjId#</isNotEmpty>
						<isNotEmpty prepend=" , " property="gjName">gj_name=#gjName#</isNotEmpty>
						<isNotEmpty prepend=" , " property="gjCode">gj_code=#gjCode#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra1">extra1=#extra1#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra2">extra2=#extra2#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra3">extra3=#extra3#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra4">extra4=#extra4#</isNotEmpty>
						<isNotEmpty prepend=" , " property="extra5">extra5=#extra5#</isNotEmpty>
						<isNotEmpty prepend=" , " property="delStatus">del_status=#delStatus#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="createDate">create_date=#createDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="updateDate">update_date=#updateDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
						<isNotEmpty prepend=" , " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
						<isNotEmpty prepend=" , " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
				</dynamic>
		WHERE
		 sqgj_id =#sqgjId#
	</update>


	<select id="getCountry"  parameterClass="hashmap" resultClass="tkwzlApplySqgjResult">
		SELECT
		sqgj_id as "sqgjId" ,
		gj_name as "gjName" ,
		gj_code as "gjCode" ,
		extra1 as "extra1" ,
		extra2 as "extra2" ,
		extra3 as "extra3" ,
		extra4 as "extra4" ,
		extra5 as "extra5" ,
		del_status as "delStatus" ,
		create_user_label as "createUserLabel" ,
		create_date as "createDate" ,
		update_user_label as "updateUserLabel" ,
		update_date as "updateDate" ,
		delete_user_label as "deleteUserLabel" ,
		delete_date as "deleteDate" ,
		record_version as "recordVersion"
		FROM ${zzzcSchema}.t_kwzl_apply_sqgj
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="sqgjId">sqgj_id =  #sqgjId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gjName">gj_name LIKE  '%$gjName$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="gjCode">gj_code LIKE  '%$gjCode$%'</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>
</sqlMap>