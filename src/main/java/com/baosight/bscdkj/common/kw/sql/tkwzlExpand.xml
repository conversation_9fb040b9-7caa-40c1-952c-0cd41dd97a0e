<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkwzlExpand">

    <select id="sbQuery" parameterClass="hashmap" resultClass="com.baosight.bscdkj.common.kw.domain.TkwzlApplyBaseinfo">
        SELECT A.JWSQ_ID AS "jwsqId",A.SBBM_CODE AS "sbbmCode",A.SBBM_NAME AS "sbbmName",A.IN_SQH AS "inSqh",A.IN_ZLMC
        AS "inZlmc",A.IN_ZLTYPE AS "inZltype",B.CURRENT_ACTIVITY_NAME AS "flowStatus"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO B ON A.JWSQ_ID = B.BUSINESS_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="jwsqId">A.JWSQ_ID = #jwsqId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="inZlmc">A.IN_ZLMC LIKE '%$inZlmc$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="inSqh">A.IN_SQH = #inSqh#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="inZltype">A.IN_ZLTYPE = #inZltype#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="paStatus">A.PA_STATUS = #paStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="flowStatus">A.FLOW_STATUS = #flowStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <select id="getAuthCountryInfo" parameterClass="hashmap" resultClass="hashmap">
        SELECT tkab.GJSQRQ AS     "gjsqrq",
               tkgb.STATE_NAME AS "stateName",
               tkgb.STATE_CODE AS "stateCode",
               tkgb.STATE_SQRQ AS "stateSqrq"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO tkab
                 LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO tkgb ON tkab.JWSQ_ID = tkgb.JWSQ_ID
        WHERE tkab.isvalid = '1'
          AND tkgb.isvalid = '1'
          AND tkgb.STATE_SQRQ IS NOT null
          AND tkab.IN_SQH = #patentNo#
    </select>

    <!--后评估规则启动-->
    <select id="selHpgStart" parameterClass="hashmap" resultClass="hashmap">
        SELECT tkab.jwsq_id AS "jwsqId",
        tkab.sbbm_code AS "sbbmCode",
        tkab.sbbm_name AS "sbbmName",
        tkab.in_zlmc AS "inZlmc",
        tkab.gw_sqh as "gwSqh",
        tkgb.jwzl_id AS "jwzlId",
        tkgb.gj_sqh as "gjSqh" ,
        tkgb.state_name as "stateName" ,
        tkgb.state_sqh as "stateSqh" ,
        tkgb.state_sqrq as "stateSqrq"
        FROM ${zzzcSchema}.t_kwzl_apply_baseinfo tkab LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO tkgb ON tkab.jwsq_id
        = tkgb.jwsq_id
        WHERE tkab.isvalid = '1' and tkgb.isvalid = '1'
        <isNotEmpty prepend=" AND " property="patentNo">tkab.in_sqh IN
            <iterate property="patentNo" conjunction="," open="(" close=")">
                #patentNo[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sqCountry">tkgb.state_code IN
            <iterate property="sqCountry" conjunction="," open="(" close=")">
                #sqCountry[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sqYear">SUBSTRING(tkgb.state_sqrq,0,5) = #sqYear#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <select id="zhcxSlQuery" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        tkab.JWSQ_ID as "jwsqId" ,
        tkab.DWHZ_ID as "dwhzId" ,
        tkab.IN_BGBH as "inBgbh" ,
        tkab.IN_SQH as "inSqh" ,
        tkab.IN_ZLMC as "inZlmc" ,
        tkab.IN_SLRQ as "inSlrq" ,
        tkab.IN_ZLTYPE as "inZltype" ,
        tkab.SBBM_CODE as "sbbmCode" ,
        tkab.SBBM_NAME as "sbbmName" ,
        tkab.FROM_TYPE as "fromType" ,
        tkab.ISJPG as "isjpg" ,
        tkab.JPG_TEAM as "jpgTeam" ,
        tkab.PRIORITY_DATE as "priorityDate" ,
        tkab.ALL_PERSON as "allPerson" ,
        tkab.BZR as "bzr" ,
        tkab.BZR_NAME as "bzrName" ,
        tkab.START_REASON as "startReason" ,
        tkab.FIRST_PERSON as "firstPerson" ,
        tkab.FIRST_PERSON_NAME as "firstPersonName" ,
        tkab.FIRST_PHONE as "firstPhone" ,
        tkab.FIRST_MOBILE as "firstMobile" ,
        tkab.SECOND_PERSON as "secondPerson" ,
        tkab.SECOND_PERSON_NAME as "secondPersonName" ,
        tkab.SECOND_PHONE as "secondPhone" ,
        tkab.SECOND_MOBILE as "secondMobile" ,
        tkab.SOLVE_PROBLEM as "solveProblem" ,
        tkab.LEAD_WHERE as "leadWhere" ,
        tkab.FOREIGN_PROSPECT as "foreignProspect" ,
        tkab.SQGJ_TCR as "sqgjTcr" ,
        tkab.SQGJ_EXPERT as "sqgjExpert" ,
        tkab.SQGJ_FINAL as "sqgjFinal" ,
        tkab.ISQQSF as "isqqsf" ,
        tkab.ZXH as "zxh" ,
        tkab.GW_SQH as "gwSqh" ,
        tkab.SWS_GUID as "swsGuid" ,
        tkab.SWS_LXR as "swsLxr" ,
        tkab.SWS_DLRXM as "swsDlrxm" ,
        tkab.SWS_DLREMAIL as "swsDlremail" ,
        tkab.SWS_DLRPHONE as "swsDlrphone" ,
        tkab.GJSQRQ as "gjsqrq" ,
        tkab.PCTSQH as "pctsqh" ,
        tkab.APPLY_REASON as "applyReason" ,
        tkab.PA_STATUS as "paStatus" ,
        tkab.ISZZSQ as "iszzsq" ,
        tkab.ZZSQ_REASON as "zzsqReason" ,
        tkab.FLOW_STATUS as "flowStatus" ,
        tkab.ISVALID as "isvalid" ,
        tkab.EXTRA1 as "extra1" ,
        tkab.EXTRA2 as "extra2" ,
        tkab.EXTRA3 as "extra3" ,
        tkab.EXTRA4 as "extra4" ,
        tkab.EXTRA5 as "extra5" ,
        tkab.SWS_NO as "swsNo" ,
        tkab.DLR_NO as "dlrNo" ,
        tkab.IS_GJCS as "isGjcs" ,
        tkab.SQR as "sqr" ,
        tkab.JPG_XH as "jpgXh" ,
        tkab.JRGJCSRQ as "jrgjcsrq" ,
        tkab.JRGJJDRQ as "jrgjjdrq" ,
        tkab.NOGJJDRQ as "nogjjdrq" ,
        tkab.FIRST_DEPT_PATH as "firstDeptPath" ,
        tkab.NOGJJDSM as "nogjjdsm" ,
        tkab.FMR_CODE as "fmrCode" ,
        tkab.GLDW_CODE as "gldwCode" ,
        tkab.GLDW_NAME as "gldwName"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO tkab
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="inBgbh">tkab.IN_BGBH LIKE '%$inBgbh$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="inSqh">tkab.IN_SQH LIKE '%$inSqh$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sbbmCode">tkab.FIRST_DEPT_PATH LIKE '%$sbbmCode$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="inZlmc">tkab.IN_ZLMC LIKE '%$inZlmc$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="zxh">tkab.ZXH = #zxh#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isvalid">tkab.ISVALID = #isvalid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="paStatus">tkab.PA_STATUS = #paStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gwSqh">tkab.GW_SQH LIKE '%$gwSqh$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isGwSqh">tkab.GW_SQH IS NOT NULL AND tkab.GW_SQH != ''</isNotEmpty>
            <isNotEmpty prepend=" AND " property="zlmc">tkab.IN_ZLMC LIKE '%zlmc%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqr">tkab.SQR LIKE '%$sqr$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="bgSbbmName">tkab.SBBM_NAME LIKE '%$bgSbbmName$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="fromType">tkab.FROM_TYPE = #fromType#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isqqsf">tkab.ISQQSF = #isqqsf#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isjpg">tkab.ISJPG = #isjpg#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jpgXm">tkab.JPG_XM = #jpgXm#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="teamName">tkab.JPG_TEAM LIKE '%$teamName$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jpgXmName">tkab.JPG_TEAM LIKE '%$jpgXmName$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="priorityDateMin">tkab.PRIORITY_DATE <![CDATA[ >= ]]>
                #priorityDateMin#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="priorityDateMax">tkab.PRIORITY_DATE <![CDATA[ <= ]]>
                #priorityDateMax#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="gjsqrqMin">tkab.GJSQRQ <![CDATA[ >= ]]> #gjsqrqMin#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gjsqrqMax">tkab.GJSQRQ <![CDATA[ <= ]]> #gjsqrqMax#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jrgjcsrqMin">tkab.JRGJCSRQ <![CDATA[ >= ]]> #jrgjcsrqMin#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jrgjcsrqMax">tkab.JRGJCSRQ <![CDATA[ <= ]]> #jrgjcsrqMax#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="pctsqh">tkab.PCTSQH LIKE '%$pctsqh$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isGjcs">tkab.IS_GJCS = #isGjcs#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra2">tkab.EXTRA2 = #extra2#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqgjTcr">tkab.SQGJ_TCR LIKE '%$sqgjTcr$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqgjFinal">tkab.SQGJ_FINAL LIKE '%$sqgjFinal$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="firstPerson">tkab.FIRST_PERSON = #firstPerson#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="firstMobile">tkab.FIRST_MOBILE = #firstMobile#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="firstPhone">tkab.FIRST_PHONE = #firstPhone#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="secondPerson">tkab.SECOND_PERSON = #secondPerson#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="secondMobile">tkab.SECOND_MOBILE = #secondMobile#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="secondPhone">tkab.SECOND_PHONE = #secondPhone#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsGuid">tkab.SWS_GUID = #swsGuid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsLxr">tkab.SWS_LXR = #swsLxr#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsDlrxm">tkab.SWS_DLRXM = #swsDlrxm#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsDlrphone">tkab.SWS_DLRPHONE = #swsDlrphone#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jrgjjdrqMin">tkab.JRGJJDRQ <![CDATA[ >= ]]> #jrgjjdrqMin#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jrgjjdrqMax">tkab.JRGJJDRQ <![CDATA[ <= ]]> #jrgjjdrqMax#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">tkab.create_user_label = #createUserLabel#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="bzr">tkab.BZR = #bzr#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isGwrq">tkab.JRGJJDRQ IS NOT null</isNotEmpty>
            <isNotEmpty prepend=" AND " property="fmrCode">tkab.FMR_CODE LIKE '%$fmrCode$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gjjdYear">YEAR(tkab.JRGJJDRQ) = #gjjdYear#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql2">$dynSql2$</isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
    </select>

    <select id="zhcxQuery" parameterClass="hashmap" resultClass="hashmap">
        SELECT DISTINCT
        tkab.GW_SQH AS "gwSqh"
        ,tkab.jwsq_id as "jwsqId"
        ,tkab.SQR AS "sqr"
        ,tkab.PRIORITY_DATE AS "priorityDate"
        ,tkab.IN_SQH AS "inSqh"
        ,tkab.GJSQRQ AS "gjsqrq"
        ,tkab.IN_ZLMC AS "inZlmc"
        ,tkab.SBBM_CODE AS "sbbmCode"
        ,tkab.FLOW_STATUS AS "flowStatus"
        ,tkab.PCTSQH AS "pctsqh"
        ,tkgb.GJ_SQH AS "gjSqh"
        ,tkgb.STATE_NAME AS "stateName"
        ,tkgb.STATE_CODE AS "stateCode"
        ,tkgb.STATE_SQH AS "stateSqh"
        ,tkgb.STATE_SQRQ AS "stateSqrq"
        ,tkgb.STATE_ZLH AS "stateZlh"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO tkab
        LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO tkgb ON tkab.JWSQ_ID = tkgb.JWSQ_ID
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_JPGINFO tkaj ON tkab.JWSQ_ID = tkaj.JWSQ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="inBgbh">tkab.IN_BGBH LIKE '%$inBgbh$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="inSqh">tkab.IN_SQH LIKE '%$inSqh$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sbbmCode">tkab.SBBM_CODE = #sbbmCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="inZlmc">tkab.IN_ZLMC LIKE '%$inZlmc$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="zxh">tkab.ZXH = #zxh#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isvalid">tkab.ISVALID = #isvalid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="paStatus">tkab.PA_STATUS = #paStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gwSqh">tkab.GW_SQH LIKE '%$gwSqh$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="zlmc">tkab.IN_ZLMC LIKE '%zlmc%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqr">tkab.SQR LIKE '%$sqr$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="bgSbbmName">tkab.SBBM_CODE LIKE '%$bgSbbmName$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="fromType">tkab.FROM_TYPE = #fromType#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isqqsf">tkab.ISQQSF = #isqqsf#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isjpg">tkab.ISJPG = #isjpg#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jpgXm">tkab.JPG_XM = #jpgXm#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="teamName">tkab.JPG_TEAM LIKE '%$teamName$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jpgXmName">tkab.JPG_TEAM LIKE '%$jpgXmName$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="priorityDateMin">tkab.PRIORITY_DATE <![CDATA[ >= ]]>
                #priorityDateMin#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="priorityDateMax">tkab.PRIORITY_DATE <![CDATA[ <= ]]>
                #priorityDateMax#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="gjsqrqMin">tkab.GJSQRQ <![CDATA[ >= ]]> #gjsqrqMin#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gjsqrqMax">tkab.GJSQRQ <![CDATA[ <= ]]> #gjsqrqMax#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jrgjcsrqMin">tkab.JRGJCSRQ <![CDATA[ >= ]]> #jrgjcsrqMin#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jrgjcsrqMax">tkab.JRGJCSRQ <![CDATA[ <= ]]> #jrgjcsrqMax#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="pctsqh">tkab.PCTSQH LIKE '%$pctsqh$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isGjcs">tkab.IS_GJCS = #isGjcs#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="extra2">tkab.EXTRA2 = #extra2#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqgjTcr">tkab.SQGJ_TCR LIKE '%$sqgjTcr$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqgjFinal">tkab.SQGJ_FINAL LIKE '%$sqgjFinal$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="firstPerson">tkab.FIRST_PERSON = #firstPerson#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="firstMobile">tkab.FIRST_MOBILE = #firstMobile#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="firstPhone">tkab.FIRST_PHONE = #firstPhone#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="secondPerson">tkab.SECOND_PERSON = #secondPerson#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="secondMobile">tkab.SECOND_MOBILE = #secondMobile#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="secondPhone">tkab.SECOND_PHONE = #secondPhone#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsGuid">tkab.SWS_GUID = #swsGuid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsLxr">tkab.SWS_LXR = #swsLxr#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsDlrxm">tkab.SWS_DLRXM = #swsDlrxm#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsDlrphone">tkab.SWS_DLRPHONE = #swsDlrphone#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jrgjjdrqMin">tkab.JRGJJDRQ <![CDATA[ >= ]]> #jrgjjdrqMin#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jrgjjdrqMax">tkab.JRGJJDRQ <![CDATA[ <= ]]> #jrgjjdrqMax#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="createUserLabel">tkab.create_user_label = #createUserLabel#
            <isNotEmpty prepend=" AND " property="bzr">tkab.BZR = #bzr#</isNotEmpty>
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="stateCode">tkgb.STATE_CODE IN
                <iterate property="stateCode" conjunction="," open="(" close=")">
                    #stateCode[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="gjSqh">tkgb.GJ_SQH LIKE '%$gjSqh$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gjIsvalid">tkgb.ISVALID = #gjIsvalid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="nameEng">tkgb.NAME_ENG LIKE '%$nameEng$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="stateSqh">tkgb.STATE_SQH LIKE '%$stateSqh$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="stateZlh">tkgb.STATE_ZLH LIKE '%$stateZlh$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="stateSqrqMin">tkgb.STATE_SQRQ <![CDATA[ >= ]]>#stateSqrqMin#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="stateSqrqMax">tkgb.STATE_SQRQ <![CDATA[ <= ]]>#stateSqrqMax#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="zrzzrqMin">tkgb.ZRZZRQ <![CDATA[ >= ]]> #zrzzrqMin#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="zrzzrqMax">tkgb.ZRZZRQ <![CDATA[ <= ]]> #zrzzrqMax#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="docCode">tkgb.DOC_CODE = #docCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="iszdfq">tkgb.ISZDFQ = #iszdfq#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isGwrq">tkgb.STATE_SQRQ IS NOT NULL AND tkgb.STATE_SQRQ != ''</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isSqrq">tkab.STATE_SQH IS NOT NULL AND tkab.STATE_SQH != ''</isNotEmpty>
            <isNotEmpty prepend=" AND " property="fmrCode">tkab.FMR_CODE LIKE '%$fmrCode$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql2">$dynSql2$</isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <select id="queryYB" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        DISTINCT
        A.FLOW_CODE AS "processCode" ,
        A.FLOW_ID AS "flowId" ,
        A.CURRENT_ACTIVITY AS "currentActivity" ,
        A.CURRENT_ACTIVITY_NAME AS "currentActivityName",
        A.BUSINESS_ID AS "businessId" ,
        A.BUSINESS_NAME AS "businessName" ,
        A.BUSINESS_TYPE AS "businessType" ,
        A.CURRENT_OPERATOR AS "currentOperator" ,
        A.PROCESS_INSTANCE_ID AS "processInstanceId",
        A.LAST_TIME AS "lastTime"
        FROM
        ${ggmkSchema}.V_MPWF_YB A
        WHERE A.ASSIGNEE_ID = #userLabel#
        <isNotEmpty prepend=" AND " property="businessType">A.BUSINESS_TYPE = #businessType#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="flowCode">A.FLOW_CODE IN
            <iterate property="flowCode" conjunction="," open="(" close=")">
                #flowCode[]#
            </iterate>
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="currentActivity">A.CURRENT_ACTIVITY = #currentActivity#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="businessNameLike">A.BUSINESS_NAME like '%$businessNameLike$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <select id="queryGRCountry" parameterClass="hashmap" resultClass="hashmap">
        SELECT TKAGS.USER_CODE AS "userCode",TKAGS.USER_NAME AS "userName",TKAGS.SC_TYPE AS "scType",TKAG.GJ_CODE AS
        "gjCode",TKAG.GJ_NAME AS "gjName"
        FROM ${zzzcSchema}.T_KWZL_APPLY_GJZD_SC TKAGS LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_GJZD TKAG ON TKAGS.SQGJSC_ID
        = TKAG.SQGJSC_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="userCode">TKAGS.USER_CODE = #userCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gjName">TKAG.GJ_NAME LIKE '%$gjName$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gjCode">TKAG.GJ_CODE LIKE '%$gjCode$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <select id="queryHpgDataIled" parameterClass="hashmap" resultClass="hashmap">
        SELECT DISTINCT
        A.jwsq_id as "jwsqId" ,
        B.PATENT_ID AS "patentId",
        A.in_bgbh as "inBgbh" ,
        A.gw_sqh as "gwSqh" ,
        A.in_zlmc as "inZlmc"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KIZL_PATENT_INFO B ON A.EXTRA1 = B.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO C ON A.JWSQ_ID = C.JWSQ_ID
        LEFT JOIN ${zzzcSchema}.T_KTMY_PROJECT JSMY ON A.EXTRA4 = JSMY.PROJECT_NUM
        LEFT JOIN ${kjglSchema}.T_KTTG_MAIN TGYZ ON A.EXTRA4 = TGYZ.PROJECT_NUM
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="startDate">A.JRGJCSRQ <![CDATA[ >= ]]> #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">A.JRGJCSRQ <![CDATA[ <= ]]> #endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isValid">A.ISVALID = #isValid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="paStatus">A.PA_STATUS = #paStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isOmr">C.STATE_CODE IN ('US','JP','EP') AND C.STATE_SQRQ IS NOT NULL AND C.STATE_SQRQ != ''</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isNotOmr">C.STATE_CODE NOT IN ('US','JP','EP')</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isBh">B.FLZT = '06'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isNotBh">B.FLZT != '06'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isMy">JSMY.PROJECT_ID IS NOT NULL</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isNotMy">JSMY.PROJECT_ID IS NULL</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isTg">TGYZ.MAIN_ID IS NOT NULL</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isNotTg">TGYZ.MAIN_ID IS NULL</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>


    <select id="querySLDataIled" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        jwsq_id as "jwsqId" ,
        dwhz_id as "dwhzId" ,
        in_bgbh as "inBgbh" ,
        in_sqh as "inSqh" ,
        in_zlmc as "inZlmc" ,
        in_slrq as "inSlrq" ,
        in_zltype as "inZltype" ,
        sbbm_code as "sbbmCode" ,
        sbbm_name as "sbbmName" ,
        from_type as "fromType" ,
        isjpg as "isjpg" ,
        jpg_team as "jpgTeam" ,
        priority_date as "priorityDate" ,
        all_person as "allPerson" ,
        bzr as "bzr" ,
        bzr_name as "bzrName" ,
        start_reason as "startReason" ,
        first_person as "firstPerson" ,
        first_person_name as "firstPersonName" ,
        first_phone as "firstPhone" ,
        first_mobile as "firstMobile" ,
        second_person as "secondPerson" ,
        second_person_name as "secondPersonName" ,
        second_phone as "secondPhone" ,
        second_mobile as "secondMobile" ,
        solve_problem as "solveProblem" ,
        lead_where as "leadWhere" ,
        foreign_prospect as "foreignProspect" ,
        sqgj_tcr as "sqgjTcr" ,
        sqgj_expert as "sqgjExpert" ,
        sqgj_final as "sqgjFinal" ,
        isqqsf as "isqqsf" ,
        zxh as "zxh" ,
        gw_sqh as "gwSqh" ,
        sws_guid as "swsGuid" ,
        sws_lxr as "swsLxr" ,
        sws_dlrxm as "swsDlrxm" ,
        sws_dlremail as "swsDlremail" ,
        sws_dlrphone as "swsDlrphone" ,
        gjsqrq as "gjsqrq" ,
        pctsqh as "pctsqh" ,
        apply_reason as "applyReason" ,
        pa_status as "paStatus" ,
        iszzsq as "iszzsq" ,
        zzsq_reason as "zzsqReason" ,
        flow_status as "flowStatus" ,
        isvalid as "isvalid" ,
        extra1 as "extra1" ,
        extra2 as "extra2" ,
        extra3 as "extra3" ,
        extra4 as "extra4" ,
        extra5 as "extra5" ,
        del_status as "delStatus" ,
        create_user_label as "createUserLabel" ,
        create_date as "createDate" ,
        update_user_label as "updateUserLabel" ,
        update_date as "updateDate" ,
        delete_user_label as "deleteUserLabel" ,
        delete_date as "deleteDate" ,
        record_version as "recordVersion" ,
        sws_no as "swsNo" ,
        dlr_no as "dlrNo" ,
        is_gjcs as "isGjcs" ,
        sqr as "sqr" ,
        jpg_xh as "jpgXh" ,
        jrgjcsrq as "jrgjcsrq" ,
        jrgjjdrq as "jrgjjdrq" ,
        nogjjdrq as "nogjjdrq"
        FROM ${zzzcSchema}.t_kwzl_apply_baseinfo
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="startDate">JRGJJDRQ <![CDATA[ >= ]]> #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">JRGJJDRQ <![CDATA[ <= ]]> #endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isValid">ISVALID = #isValid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="paStatus">PA_STATUS = #paStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="issqOmr">EXTRA2 = #issqOmr#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <select id="queryIntAndCyGroup" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        A.GW_SQH as "gwSqh"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="startDate">B.STATE_SQRQ <![CDATA[ >= ]]> #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">B.STATE_SQRQ <![CDATA[ <= ]]> #endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isvalid">B.ISVALID = #isvalid#</isNotEmpty>
            <!--国家阶段查询条件-->
            <isNotEmpty prepend=" AND " property="gwSqh">A.GW_SQH = #gwSqh#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gjSqh">B.GJ_SQH = #gjSqh#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="allPerson">A.ALL_PERSON LIKE '%$allPerson$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="firstPersonName">A.FIRST_PERSON_NAME LIKE '%$firstPersonName$%'
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="inZlmc">A.IN_ZLMC LIKE '%$inZlmc$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="pctsqh">A.PCTSQH = #pctsqh#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isStateSqh">B.state_sqh IS NOT NULL AND trim(state_sqh) != ''
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="paStatus">A.PA_STATUS = #paStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isvalid">A.ISVALID = #isvalid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jwsqId">A.JWSQ_ID = #jwsqId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
        GROUP BY A.GW_SQH
        ORDER BY A.GW_SQH DESC
    </select>


    <select id="querySQDataIled" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        A.GW_SQH as "gwSqh",
        A.JWSQ_ID as "jwsqId",
        A.in_zlmc as "inZlmc" ,
        A.second_person as "secondPerson" ,
        A.second_person_name as "secondPersonName" ,
        A.first_person as "firstPerson" ,
        A.first_person_name as "firstPersonName" ,
        A.gjsqrq as "gjsqrq" ,
        B.jwzl_id as "jwzlId" ,
        B.gj_sqh as "gjSqh" ,
        B.state_name as "stateName" ,
        B.state_code as "stateCode" ,
        B.state_sqh as "stateSqh" ,
        B.state_sqrq as "stateSqrq" ,
        B.state_zlh as "stateZlh" ,
        B.zrzzrq as "zrzzrq" ,
        B.name_eng as "nameEng" ,
        B.doc_code as "docCode" ,
        B.iszzsq as "iszzsq" ,
        B.zzsq_reason as "zzsqReason" ,
        B.iszdfq as "iszdfq" ,
        B.isvalid as "isvalid" ,
        B.zdfq_reason as "zdfqReason" ,
        B.after_fqrq as "afterFqrq" ,
        B.isreject as "isreject" ,
        B.reject_reason as "rejectReason"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="startDate">B.STATE_SQRQ <![CDATA[ >= ]]> #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">B.STATE_SQRQ <![CDATA[ <= ]]> #endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isvalid">B.ISVALID = #isvalid#</isNotEmpty>
            <!--国家阶段查询条件-->
            <isNotEmpty prepend=" AND " property="swsNo">A.SWS_NO = #swsNo#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gwSqh">A.GW_SQH = #gwSqh#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gjSqh">B.GJ_SQH = #gjSqh#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="allPerson">A.ALL_PERSON LIKE '%$allPerson$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="firstPersonName">A.FIRST_PERSON_NAME LIKE '%$firstPersonName$%'
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="inZlmc">A.IN_ZLMC LIKE '%$inZlmc$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="pctsqh">A.PCTSQH = #pctsqh#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isStateSqh">B.state_sqh IS NOT NULL AND trim(state_sqh) != ''
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="paStatus">A.PA_STATUS = #paStatus#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isvalid">A.ISVALID = #isvalid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jwsqId">A.JWSQ_ID = #jwsqId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <select id="getGwSqh" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        A.GW_SQH as "gwSqh"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO B ON A.JWSQ_ID =
        B.JWSQ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="startDate">B.STATE_SQRQ <![CDATA[ >= ]]> #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">B.STATE_SQRQ <![CDATA[ <= ]]> #endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isvalid">B.isvalid = #isvalid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder">GROUP BY $displayOrder$</isNotEmpty>
    </select>

    <!-- S_KI_ZL_009根据年度，发明人工号获取（已受理，已授权）专利 -->
    <select id="querySlSqzl" parameterClass="hashmap" resultClass="hashmap">
        SELECT distinct
        t.JWSQ_ID as "jwsqId" ,
        t.GW_SQH as "projectCode" ,
        t.IN_ZLMC as "projectName" ,
        ryxx.EMP_ID as "memberId" ,
        ryxx.DEPT_CODE as "deptCode" ,
        ryxx.GXXS as "ratio" ,
        t.IN_ZLTYPE as "patentType" ,
        'KW' as "source" ,
        t.GJSQRQ as "applyDate" ,
        t.GJSQRQ as "authDate" ,
        '受理' as "status"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO t LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_WCR ryxx
        ON t.JWSQ_ID = ryxx.JWSQ_ID
        WHERE t.isvalid = '1' AND t.PA_STATUS = 'int' AND t.FLOW_STATUS = 'end' AND t.GJSQRQ IS NOT NULL
        <isNotEmpty prepend=" AND " property="year">YEAR(t.GJSQRQ) = #year#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="empIdList">ryxx.EMP_ID IN
            <iterate property="empIdList" conjunction="," open="(" close=")">
                #empIdList[]#
            </iterate>
        </isNotEmpty>
        UNION
        SELECT distinct
        t.JWSQ_ID as "jwsqId" ,
        t.GW_SQH as "projectCode" ,
        t.IN_ZLMC as "projectName" ,
        ryxx.EMP_ID as "memberId" ,
        ryxx.DEPT_CODE as "deptCode" ,
        ryxx.GXXS as "ratio" ,
        t.IN_ZLTYPE as "patentType" ,
        'KW' as "source" ,
        t.GJSQRQ as "applyDate" ,
        g.STATE_SQRQ as "authDate" ,
        '授权' as "status"
        FROM ${zzzcSchema}.T_KWZL_GJJD_BASEINFO g LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO t
        ON g.JWSQ_ID = t.JWSQ_ID
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_WCR ryxx ON g.JWSQ_ID = ryxx.JWSQ_ID
        WHERE t.isvalid = '1' AND t.PA_STATUS = 'cy' AND t.FLOW_STATUS = 'end' AND t.GJSQRQ IS NOT NULL AND g.STATE_SQRQ
        IS NOT NULL AND g.ISVALID = '1'
        <isNotEmpty prepend=" AND " property="year">YEAR(g.STATE_SQRQ) = #year#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="empIdList">ryxx.EMP_ID IN
            <iterate property="empIdList" conjunction="," open="(" close=")">
                #empIdList[]#
            </iterate>
        </isNotEmpty>
    </select>

    <select id="querySqSqxx" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        CASE SYSIBM.REGEXP_SUBSTR(min(t.CODE_PATH), #regexp#)
        WHEN ''
        THEN #deptPath#
        ELSE SYSIBM.REGEXP_SUBSTR(min(t.CODE_PATH), #regexp#)
        END AS "deptCode",
        MAX(t.DWMCPX) AS "deptName",
        SUM(CASE WHEN
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 1</isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 1</isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 1</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jan",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 2</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 2</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 2</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "feb",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 3</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 3</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 3</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "mar",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 4</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 4</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 4</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "apr",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 5</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 5</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 5</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "may",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 6</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 6</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 6</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jun",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 7</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 7</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 7</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jul",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 8</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 8</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 8</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "aug",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 9</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 9</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 9</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "sep",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 10</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 10</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 10</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "oct",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 11</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 11</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 11</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "nov",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 12</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 12</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 12</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "dec",
        SUM(t.EXTRA2) * 0.01 AS "tongji",
        COUNT(b.JWSQ_ID) AS "zongshu"
        FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO b ON b.EXTRA1 = t.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO d ON t.APPLY_ID = d.APPLY_ID
        <isNotEmpty property="gjsq">
            LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO c ON c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        <isNotEmpty property="cysq">
            LEFT JOIN (
            SELECT
            JWSQ_ID,
            max(STATE_SQRQ) AS STATE_SQRQ,
            max(STATE_NAME) AS STATE_NAME
            FROM
            ${zzzcSchema}.T_KWZL_GJJD_BASEINFO
            WHERE
            STATE_SQRQ IS NOT NULL
            AND STATE_SQRQ != ''
            GROUP BY
            JWSQ_ID ) c ON
            c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        WHERE t.EXTRA1 = '1'
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty prepend=" AND " property="intDate">b.JRGJCSRQ <![CDATA[ >= ]]>  #intDate# AND b.JRGJCSRQ
            <![CDATA[ <= ]]> #intDateEd#
        </isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty prepend=" AND " property="applyDate">b.GJSQRQ <![CDATA[ >= ]]>  #applyDate# AND b.GJSQRQ
            <![CDATA[ <= ]]> #applyDateEd#
        </isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty prepend=" AND " property="cyDate">c.STATE_SQRQ <![CDATA[ >= ]]>  #cyDate# AND c.STATE_SQRQ
            <![CDATA[ <= ]]> #cyDateEd#
        </isNotEmpty>
        <!-- 专利类型：发明  实用新型  外观专利 -->
        <isNotEmpty prepend=" AND " property="zllx">b.IN_ZLTYPE = #zllx#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">t.CODE_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">t.CODE_PATH NOT LIKE '%BSZG%' AND t.CODE_PATH NOT LIKE '%BGTM%' AND t.CODE_PATH
            NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isvalid">b.isvalid = #isvalid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dwCode">t.DEPTCBBM = #dwCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isGtcp">d.EXTRA4 = '1'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCodePath">t.CODE_PATH LIKE '$unitCodePath$%'</isNotEmpty>
        GROUP BY SYSIBM.REGEXP_SUBSTR(t.CODE_PATH, #regexp#)
    </select>

    <select id="queryCBSqSqxx" parameterClass="hashmap" resultClass="hashmap">
        SELECT t.DEPTCBBM AS "deptCode",
        MAX(t.DWMCPX) AS "deptName",
        SUM(CASE WHEN
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 1</isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 1</isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 1</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jan",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 2</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 2</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 2</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "feb",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 3</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 3</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 3</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "mar",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 4</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 4</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 4</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "apr",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 5</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 5</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 5</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "may",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 6</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 6</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 6</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jun",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 7</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 7</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 7</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jul",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 8</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 8</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 8</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "aug",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 9</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 9</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 9</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "sep",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 10</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 10</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 10</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "oct",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 11</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 11</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 11</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "nov",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 12</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 12</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 12</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "dec",
        SUM(t.EXTRA2) * 0.01 AS "tongji",
        COUNT(b.JWSQ_ID) AS "zongshu"
        FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO b ON b.EXTRA1 = t.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO d ON t.APPLY_ID = d.APPLY_ID
        <isNotEmpty property="cysq">
            LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO c ON c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        <isNotEmpty property="gjsq">
            LEFT JOIN (
            SELECT
            JWSQ_ID,
            max(STATE_SQRQ) AS STATE_SQRQ,
            max(STATE_NAME) AS STATE_NAME
            FROM
            ${zzzcSchema}.T_KWZL_GJJD_BASEINFO
            WHERE
            STATE_SQRQ IS NOT NULL
            AND STATE_SQRQ != ''
            GROUP BY
            JWSQ_ID ) c ON
            c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        WHERE t.EXTRA1 = '1'
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty prepend=" AND " property="intDate">b.JRGJCSRQ <![CDATA[ >= ]]>  #intDate# AND b.JRGJCSRQ
            <![CDATA[ <= ]]> #intDateEd#
        </isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty prepend=" AND " property="applyDate">b.GJSQRQ <![CDATA[ >= ]]>  #applyDate# AND b.GJSQRQ
            <![CDATA[ <= ]]> #applyDateEd#
        </isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty prepend=" AND " property="cyDate">c.STATE_SQRQ <![CDATA[ >= ]]>  #cyDate# AND c.STATE_SQRQ
            <![CDATA[ <= ]]> #cyDateEd#
        </isNotEmpty>
        <!-- 专利类型：发明  实用新型  外观专利 -->
        <isNotEmpty prepend=" AND " property="zllx">b.IN_ZLTYPE = #zllx#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">t.CODE_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">t.CODE_PATH NOT LIKE '%BSZG%' AND t.CODE_PATH NOT LIKE '%BGTM%' AND t.CODE_PATH
            NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isvalid">b.isvalid = #isvalid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dwCode">t.DEPTCBBM = #dwCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCodePath">t.CODE_PATH LIKE '$unitCodePath$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isCbbm">t.DEPTCBBM IS NOT NULL AND t.DEPTCBBM != ''</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isGtcp">d.EXTRA4 = '1'</isNotEmpty>
        GROUP BY t.DEPTCBBM
    </select>

    <select id="querySqSqxxMonth" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        '月申请量' AS "deptName",
        SUM(CASE WHEN
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 1</isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 1</isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 1</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jan",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 2</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 2</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 2</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "feb",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 3</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 3</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 3</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "mar",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 4</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 4</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 4</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "apr",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 5</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 5</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 5</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "may",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 6</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 6</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 6</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jun",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 7</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 7</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 7</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jul",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 8</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 8</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 8</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "aug",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 9</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 9</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 9</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "sep",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 10</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 10</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 10</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "oct",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 11</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 11</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 11</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "nov",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 12</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 12</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 12</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "dec",
        SUM(t.EXTRA2) * 0.01 AS "tongji",
        COUNT(b.JWSQ_ID) AS "zongshu"
        FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO b ON b.EXTRA1 = t.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO d ON t.APPLY_ID = d.APPLY_ID
        <isNotEmpty property="cysq">
            LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO c ON c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        <isNotEmpty property="gjsq">
            LEFT JOIN (
            SELECT
            JWSQ_ID,
            max(STATE_SQRQ) AS STATE_SQRQ,
            max(STATE_NAME) AS STATE_NAME
            FROM
            ${zzzcSchema}.T_KWZL_GJJD_BASEINFO
            WHERE
            STATE_SQRQ IS NOT NULL
            AND STATE_SQRQ != ''
            GROUP BY
            JWSQ_ID ) c ON
            c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        WHERE t.EXTRA1 = '1'
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty prepend=" AND " property="intDate">b.JRGJCSRQ <![CDATA[ >= ]]>  #intDate# AND b.JRGJCSRQ
            <![CDATA[ <= ]]> #intDateEd#
        </isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty prepend=" AND " property="applyDate">b.GJSQRQ <![CDATA[ >= ]]>  #applyDate# AND b.GJSQRQ
            <![CDATA[ <= ]]> #applyDateEd#
        </isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty prepend=" AND " property="cyDate">c.STATE_SQRQ <![CDATA[ >= ]]>  #cyDate# AND c.STATE_SQRQ
            <![CDATA[ <= ]]> #cyDateEd#
        </isNotEmpty>
        <!-- 专利类型：发明  实用新型  外观专利 -->
        <isNotEmpty prepend=" AND " property="zllx">b.IN_ZLTYPE = #zllx#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">t.CODE_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">t.CODE_PATH NOT LIKE '%BSZG%' AND t.CODE_PATH NOT LIKE '%BGTM%' AND t.CODE_PATH
            NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isvalid">b.isvalid = #isvalid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dwCode">t.DEPTCBBM = #dwCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCodePath">t.CODE_PATH LIKE '$unitCodePath$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isGtcp">d.EXTRA4 = '1'</isNotEmpty>
    </select>


    <select id="querySqSqxxQuarter" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        '季度申请量' AS "deptName",
        SUM(CASE WHEN
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) IN (1,2,3)</isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) IN (1,2,3)</isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) IN (1,2,3)</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "feb",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) IN (4,5,6)</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) IN (4,5,6)</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) IN (4,5,6)</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "may",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) IN (7,8,9)</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) IN (7,8,9)</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) IN (7,8,9)</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "aug",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) IN (10,11,12)</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) IN (10,11,12)</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) IN (10,11,12)</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "nov",
        SUM(t.EXTRA2) * 0.01 AS "tongji",
        COUNT(b.JWSQ_ID) AS "zongshu"
        FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO b ON b.EXTRA1 = t.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO d ON t.APPLY_ID = d.APPLY_ID
        <isNotEmpty property="cysq">
            LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO c ON c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        <isNotEmpty property="gjsq">
            LEFT JOIN (
            SELECT
            JWSQ_ID,
            max(STATE_SQRQ) AS STATE_SQRQ,
            max(STATE_NAME) AS STATE_NAME
            FROM
            ${zzzcSchema}.T_KWZL_GJJD_BASEINFO
            WHERE
            STATE_SQRQ IS NOT NULL
            AND STATE_SQRQ != ''
            GROUP BY
            JWSQ_ID ) c ON
            c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        WHERE t.EXTRA1 = '1'
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty prepend=" AND " property="intDate">b.JRGJCSRQ <![CDATA[ >= ]]>  #intDate# AND b.JRGJCSRQ
            <![CDATA[ <= ]]> #intDateEd#
        </isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty prepend=" AND " property="applyDate">b.GJSQRQ <![CDATA[ >= ]]>  #applyDate# AND b.GJSQRQ
            <![CDATA[ <= ]]> #applyDateEd#
        </isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty prepend=" AND " property="cyDate">c.STATE_SQRQ <![CDATA[ >= ]]>  #cyDate# AND c.STATE_SQRQ
            <![CDATA[ <= ]]> #cyDateEd#
        </isNotEmpty>
        <!-- 专利类型：发明  实用新型  外观专利 -->
        <isNotEmpty prepend=" AND " property="zllx">b.IN_ZLTYPE = #zllx#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">t.CODE_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">t.CODE_PATH NOT LIKE '%BSZG%' AND t.CODE_PATH NOT LIKE '%BGTM%' AND t.CODE_PATH
            NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isvalid">b.isvalid = #isvalid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dwCode">t.DEPTCBBM = #dwCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="unitCodePath">t.CODE_PATH LIKE '$unitCodePath$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isGtcp">d.EXTRA4 = '1'</isNotEmpty>
    </select>

    <select id="querySqSqxxJpg" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        s.RECORD_GUID AS "guid",
        MAX(s.MAIN_PROJECT_GUID) AS "mainProjectGuid",
        MAX(s.PROJECT_CODE) AS "projectCode",
        MAX(s.PROJECT_NAME) AS "projectName",
        MAX(s.FZR) AS "fzr",
        MAX(s.FZR_NAME) AS "fzrName",
        SUM(CASE WHEN
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 1</isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 1</isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 1</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jan",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 2</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 2</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 2</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "feb",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 3</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 3</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 3</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "mar",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 4</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 4</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 4</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "apr",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 5</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 5</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 5</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "may",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 6</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 6</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 6</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jun",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 7</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 7</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 7</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jul",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 8</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 8</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 8</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "aug",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 9</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 9</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 9</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "sep",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 10</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 10</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 10</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "oct",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 11</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 11</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 11</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "nov",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 12</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 12</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 12</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "dec",
        SUM(t.EXTRA2) * 0.01 AS "tongji"
        FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO b ON b.EXTRA1 = t.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO d ON t.APPLY_ID = d.APPLY_ID
        <isNotEmpty property="cysq">
            LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO c ON c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        <isNotEmpty property="gjsq">
            LEFT JOIN (
            SELECT
            JWSQ_ID,
            max(STATE_SQRQ) AS STATE_SQRQ,
            max(STATE_NAME) AS STATE_NAME
            FROM
            ${zzzcSchema}.T_KWZL_GJJD_BASEINFO
            WHERE
            STATE_SQRQ IS NOT NULL
            AND STATE_SQRQ != ''
            GROUP BY
            JWSQ_ID ) c ON
            c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        INNER JOIN ${kjglSchema}.T_KYXM_PROJECT s ON s.PROJECT_CODE = b.EXTRA4 AND s.EXTRA11 = 'a'
        WHERE t.EXTRA1 = '1'
        <isNotEmpty prepend=" AND " property="deptCode">t.EXTRA3 like '%$deptCode$%'</isNotEmpty>
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty prepend=" AND " property="intDate">b.JRGJCSRQ <![CDATA[ >= ]]>  #intDate# AND b.JRGJCSRQ
            <![CDATA[ <= ]]> #intDateEd#
        </isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty prepend=" AND " property="applyDate">b.GJSQRQ <![CDATA[ >= ]]>  #applyDate# AND b.GJSQRQ
            <![CDATA[ <= ]]> #applyDateEd#
        </isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty prepend=" AND " property="cyDate">c.STATE_SQRQ <![CDATA[ >= ]]>  #cyDate# AND c.STATE_SQRQ
            <![CDATA[ <= ]]> #cyDateEd#
        </isNotEmpty>
        <!-- 专利类型：发明  实用新型  外观专利 -->
        <isNotEmpty prepend=" AND " property="zllx">b.IN_ZLTYPE = #zllx#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">t.CODE_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">t.CODE_PATH NOT LIKE '%BSZG%' AND t.CODE_PATH NOT LIKE '%BGTM%' AND t.CODE_PATH
            NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isvalid">b.isvalid = #isvalid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="sclx">SUBSTRING(s.PROJECT_CODE,2,2) = #sclx#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isGtcp">d.EXTRA4 = '1'</isNotEmpty>
        GROUP BY s.RECORD_GUID
    </select>

    <select id="querySqSqxxJpgMonth" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        '月申请量' AS "projectName",
        SUM(CASE WHEN
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 1</isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 1</isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 1</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jan",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 2</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 2</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 2</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "feb",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 3</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 3</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 3</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "mar",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 4</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 4</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 4</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "apr",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 5</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 5</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 5</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "may",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 6</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 6</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 6</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jun",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 7</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 7</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 7</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "jul",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 8</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 8</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 8</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "aug",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 9</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 9</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 9</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "sep",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 10</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 10</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 10</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "oct",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 11</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 11</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 11</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "nov",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) = 12</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) = 12</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) = 12</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "dec",
        SUM(t.EXTRA2) * 0.01 AS "tongji"
        FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO b ON b.EXTRA1 = t.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO d ON t.APPLY_ID = d.APPLY_ID
        <isNotEmpty property="cysq">
            LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO c ON c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        <isNotEmpty property="gjsq">
            LEFT JOIN (
            SELECT
            JWSQ_ID,
            max(STATE_SQRQ) AS STATE_SQRQ,
            max(STATE_NAME) AS STATE_NAME
            FROM
            ${zzzcSchema}.T_KWZL_GJJD_BASEINFO
            WHERE
            STATE_SQRQ IS NOT NULL
            AND STATE_SQRQ != ''
            GROUP BY
            JWSQ_ID ) c ON
            c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        INNER JOIN ${kjglSchema}.T_KYXM_PROJECT s ON s.PROJECT_CODE = b.EXTRA4 AND s.EXTRA11 = 'a'
        WHERE t.EXTRA1 = '1'
        <isNotEmpty prepend=" AND " property="deptCode">t.EXTRA3 like '%$deptCode$%'</isNotEmpty>
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty prepend=" AND " property="intDate">b.JRGJCSRQ <![CDATA[ >= ]]>  #intDate# AND b.JRGJCSRQ
            <![CDATA[ <= ]]> #intDateEd#
        </isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty prepend=" AND " property="applyDate">b.GJSQRQ <![CDATA[ >= ]]>  #applyDate# AND b.GJSQRQ
            <![CDATA[ <= ]]> #applyDateEd#
        </isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty prepend=" AND " property="cyDate">c.STATE_SQRQ <![CDATA[ >= ]]>  #cyDate# AND c.STATE_SQRQ
            <![CDATA[ <= ]]> #cyDateEd#
        </isNotEmpty>
        <!-- 专利类型：发明  实用新型  外观专利 -->
        <isNotEmpty prepend=" AND " property="zllx">b.IN_ZLTYPE = #zllx#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">t.CODE_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">t.CODE_PATH NOT LIKE '%BSZG%' AND t.CODE_PATH NOT LIKE '%BGTM%' AND t.CODE_PATH
            NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isvalid">b.isvalid = #isvalid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="sclx">SUBSTRING(s.PROJECT_CODE,2,2) = #sclx#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isGtcp">d.EXTRA4 = '1'</isNotEmpty>
    </select>

    <select id="querySqSqxxJpgQuarter" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        '季度申请量' AS "projectName",
        SUM(CASE WHEN
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) IN (1,2,3)</isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) IN (1,2,3)</isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) IN (1,2,3)</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "feb",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) IN (4,5,6)</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) IN (4,5,6)</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) IN (4,5,6)</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "may",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) IN (7,8,9)</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) IN (7,8,9)</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) IN (7,8,9)</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "aug",
        SUM(CASE WHEN
        <isNotEmpty property="intDate">MONTH(b.JRGJCSRQ) IN (10,11,12)</isNotEmpty>
        <isNotEmpty property="applyDate">MONTH(b.GJSQRQ) IN (10,11,12)</isNotEmpty>
        <isNotEmpty property="cyDate">MONTH(c.STATE_SQRQ) IN (10,11,12)</isNotEmpty>
        THEN t.EXTRA2 ELSE 0 END) * 0.01 AS "nov",
        SUM(t.EXTRA2) * 0.01 AS "tongji"
        FROM ${zzzcSchema}.T_KIZL_APPLY_RYXX t
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO b ON b.EXTRA1 = t.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO d ON t.APPLY_ID = d.APPLY_ID
        <isNotEmpty property="cysq">
            LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO c ON c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        <isNotEmpty property="gjsq">
            LEFT JOIN (
            SELECT
            JWSQ_ID,
            max(STATE_SQRQ) AS STATE_SQRQ,
            max(STATE_NAME) AS STATE_NAME
            FROM
            ${zzzcSchema}.T_KWZL_GJJD_BASEINFO
            WHERE
            STATE_SQRQ IS NOT NULL
            AND STATE_SQRQ != ''
            GROUP BY
            JWSQ_ID ) c ON
            c.JWSQ_ID = b.JWSQ_ID
        </isNotEmpty>
        INNER JOIN ${kjglSchema}.T_KYXM_PROJECT s ON s.PROJECT_CODE = b.EXTRA4 AND s.EXTRA11 = 'a'
        WHERE t.EXTRA1 = '1'
        <isNotEmpty prepend=" AND " property="deptCode">t.EXTRA3 like '%$deptCode$%'</isNotEmpty>
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty prepend=" AND " property="intDate">b.JRGJCSRQ <![CDATA[ >= ]]>  #intDate# AND b.JRGJCSRQ
            <![CDATA[ <= ]]> #intDateEd#
        </isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty prepend=" AND " property="applyDate">b.GJSQRQ <![CDATA[ >= ]]>  #applyDate# AND b.GJSQRQ
            <![CDATA[ <= ]]> #applyDateEd#
        </isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty prepend=" AND " property="cyDate">c.STATE_SQRQ <![CDATA[ >= ]]>  #cyDate# AND c.STATE_SQRQ
            <![CDATA[ <= ]]> #cyDateEd#
        </isNotEmpty>
        <!-- 专利类型：发明  实用新型  外观专利 -->
        <isNotEmpty prepend=" AND " property="zllx">b.IN_ZLTYPE = #zllx#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">t.CODE_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">t.CODE_PATH NOT LIKE '%BSZG%' AND t.CODE_PATH NOT LIKE '%BGTM%' AND t.CODE_PATH
            NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isvalid">b.isvalid = #isvalid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="sclx">SUBSTRING(s.PROJECT_CODE,2,2) = #sclx#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isGtcp">d.EXTRA4 = '1'</isNotEmpty>
    </select>

    <select id="queryGjsqrq" parameterClass="hashmap" resultClass="hashmap">
        SELECT tmp.jwsqId AS "jwsqId",
        tmp.gwSqh AS "gwSqh",
        tmp.bgbh AS "bgbh",
        tmp.sqh AS "sqh",
        tmp.zlmc AS "zlmc",
        tmp.sqrq AS "sqrq",
        tmp.sqr AS "sqr",
        tmp.priorityDate AS "priorityDate",
        tmp.gjsqrq AS "gjsqrq",
        tmp.pctSqh AS "pctSqh",
        <isNotEmpty property="isSt">
            tmp.stateName as "stateName",
        </isNotEmpty>
        tmp.gxxs AS "gxxs",
        tmp.empName as "empName"
        FROM (SELECT A.JWSQ_ID AS jwsqId,
        MAX(A.GW_SQH) AS gwSqh,
        MAX(A.IN_BGBH) AS bgbh,
        MAX(A.IN_SQH) AS sqh,
        MAX(A.IN_ZLMC) AS zlmc,
        MAX(A.JRGJJDRQ) AS sqrq,
        MAX(A.SQR) AS sqr,
        MAX(A.PRIORITY_DATE) AS priorityDate,
        MAX(A.GJSQRQ) AS gjsqrq,
        MAX(A.PCTSQH) AS pctSqh,
        <isNotEmpty property="isSt">
            MAX(C.STATE_NAME) AS stateName,
        </isNotEmpty>
        SUM(B.EXTRA2) * 0.01 AS gxxs,
        MAX(A.ALL_PERSON) as empName
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX B ON A.EXTRA1 = B.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO d ON B.APPLY_ID = d.APPLY_ID
        <isNotEmpty property="cysq">
            LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO C ON C.JWSQ_ID = A.JWSQ_ID
        </isNotEmpty>
        <isNotEmpty property="gjsq">
            LEFT JOIN (
            SELECT
            JWSQ_ID,
            max(STATE_SQRQ) AS STATE_SQRQ,
            max(STATE_NAME) AS STATE_NAME
            FROM
            ${zzzcSchema}.T_KWZL_GJJD_BASEINFO
            WHERE
            STATE_SQRQ IS NOT NULL
            AND STATE_SQRQ != ''
            GROUP BY
            JWSQ_ID ) C ON
            C.JWSQ_ID = A.JWSQ_ID
        </isNotEmpty>
        WHERE B.EXTRA1 = '1'
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty prepend=" AND " property="intDate">A.JRGJCSRQ <![CDATA[ >= ]]>  #intDate# AND A.JRGJCSRQ
            <![CDATA[ <= ]]> #intDateEd#
        </isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty prepend=" AND " property="applyDate">A.GJSQRQ <![CDATA[ >= ]]>  #applyDate# AND A.GJSQRQ
            <![CDATA[ <= ]]> #applyDateEd#
        </isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty prepend=" AND " property="cyDate">C.STATE_SQRQ <![CDATA[ >= ]]>  #cyDate# AND C.STATE_SQRQ
            <![CDATA[ <= ]]> #cyDateEd#
        </isNotEmpty>
        <isNotEmpty property="yf">
            <!-- 统计类型：进入国际阶段日期 -->
            <isNotEmpty prepend=" AND " property="intDate">MONTH(A.JRGJCSRQ) = #yf#</isNotEmpty>
            <!-- 统计类型：申请日期 -->
            <isNotEmpty prepend=" AND " property="applyDate">MONTH(A.GJSQRQ) = #yf#</isNotEmpty>
            <!-- 统计类型：国家授权日期 -->
            <isNotEmpty prepend=" AND " property="cyDate">MONTH(C.STATE_SQRQ) = #yf#</isNotEmpty>
        </isNotEmpty>
        <!-- 专利类型：发明  实用新型  外观专利 -->
        <isNotEmpty prepend=" AND " property="zllx">A.IN_ZLTYPE = #zllx#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">B.CODE_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">B.CODE_PATH NOT LIKE '%BSZG%' AND B.CODE_PATH NOT LIKE '%BGTM%' AND B.CODE_PATH
            NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isvalid">A.isvalid = #isvalid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dwCode">B.DEPTCBBM = #dwCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="deptPath">B.CODE_PATH LIKE '%$deptPath$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="deptCode">B.CODE_PATH = #deptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isGtcp">d.EXTRA4 = '1'</isNotEmpty>
        GROUP BY A.JWSQ_ID) TMP
        ORDER BY TMP.gwSqh
    </select>

    <select id="queryGjsqrqCt" parameterClass="hashmap" resultClass="hashmap">
        SELECT SUM(B.EXTRA2) * 0.01 AS "gxxs"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX B ON A.EXTRA1 = B.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO d ON B.APPLY_ID = d.APPLY_ID
        <isNotEmpty property="cysq">
            LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO C ON C.JWSQ_ID = A.JWSQ_ID
        </isNotEmpty>
        <isNotEmpty property="gjsq">
            LEFT JOIN (
            SELECT
            JWSQ_ID,
            max(STATE_SQRQ) AS STATE_SQRQ,
            max(STATE_NAME) AS STATE_NAME
            FROM
            ${zzzcSchema}.T_KWZL_GJJD_BASEINFO
            WHERE
            STATE_SQRQ IS NOT NULL
            AND STATE_SQRQ != ''
            GROUP BY
            JWSQ_ID ) C ON
            C.JWSQ_ID = A.JWSQ_ID
        </isNotEmpty>
        WHERE B.EXTRA1 = '1'
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty prepend=" AND " property="intDate">A.JRGJCSRQ <![CDATA[ >= ]]>  #intDate# AND A.JRGJCSRQ
            <![CDATA[ <= ]]> #intDateEd#
        </isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty prepend=" AND " property="applyDate">A.GJSQRQ <![CDATA[ >= ]]>  #applyDate# AND A.GJSQRQ
            <![CDATA[ <= ]]> #applyDateEd#
        </isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty prepend=" AND " property="cyDate">C.STATE_SQRQ <![CDATA[ >= ]]>  #cyDate# AND C.STATE_SQRQ
            <![CDATA[ <= ]]> #cyDateEd#
        </isNotEmpty>
        <isNotEmpty property="yf">
            <!-- 统计类型：进入国际阶段日期 -->
            <isNotEmpty prepend=" AND " property="intDate">MONTH(A.JRGJCSRQ) = #yf#</isNotEmpty>
            <!-- 统计类型：申请日期 -->
            <isNotEmpty prepend=" AND " property="applyDate">MONTH(A.GJSQRQ) = #yf#</isNotEmpty>
            <!-- 统计类型：国家授权日期 -->
            <isNotEmpty prepend=" AND " property="cyDate">MONTH(C.STATE_SQRQ) = #yf#</isNotEmpty>
        </isNotEmpty>
        <!-- 专利类型：发明  实用新型  外观专利 -->
        <isNotEmpty prepend=" AND " property="zllx">A.IN_ZLTYPE = #zllx#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">B.CODE_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">B.CODE_PATH NOT LIKE '%BSZG%' AND B.CODE_PATH NOT LIKE '%BGTM%' AND B.CODE_PATH
            NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isvalid">A.isvalid = #isvalid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dwCode">B.DEPTCBBM = #dwCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="deptPath">B.CODE_PATH LIKE '%$deptPath$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="deptCode">B.CODE_PATH = #deptCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isGtcp">d.EXTRA4 = '1'</isNotEmpty>
    </select>

    <select id="queryGjsqrqJpg" parameterClass="hashmap" resultClass="hashmap">
        SELECT tmp.jwsqId AS "jwsqId",tmp.gwSqh AS "gwSqh",tmp.bgbh AS "bgbh",tmp.sqh AS "sqh",tmp.zlmc AS
        "zlmc",tmp.sqrq AS "sqrq",tmp.gjrq AS "gjrq",tmp.gxxs AS "gxxs",tmp.projectCode AS "projectCode",tmp.projectName
        AS "projectName"
        FROM (SELECT A.JWSQ_ID AS jwsqId,
        MAX(A.GW_SQH) AS gwSqh,
        MAX(A.IN_BGBH) AS bgbh,
        MAX(A.IN_SQH) AS sqh,
        MAX(A.IN_ZLMC) AS zlmc,
        MAX(A.JRGJJDRQ) AS sqrq,
        MAX(A.JRGJCSRQ) AS gjrq,
        SUM(B.EXTRA2) * 0.01 AS gxxs,
        MAX(s.PROJECT_CODE) AS projectCode,
        MAX(s.PROJECT_NAME) AS projectName
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX B ON A.EXTRA1 = B.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO d ON B.APPLY_ID = d.APPLY_ID
        <isNotEmpty property="cysq">
            LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO C ON C.JWSQ_ID = A.JWSQ_ID
        </isNotEmpty>
        <isNotEmpty property="gjsq">
            LEFT JOIN (
            SELECT
            JWSQ_ID,
            max(STATE_SQRQ) AS STATE_SQRQ,
            max(STATE_NAME) AS STATE_NAME
            FROM
            ${zzzcSchema}.T_KWZL_GJJD_BASEINFO
            WHERE
            STATE_SQRQ IS NOT NULL
            AND STATE_SQRQ != ''
            GROUP BY
            JWSQ_ID ) C ON
            C.JWSQ_ID = A.JWSQ_ID
        </isNotEmpty>
        INNER JOIN ${kjglSchema}.T_KYXM_PROJECT s ON s.PROJECT_CODE = b.EXTRA4 AND s.EXTRA11 = 'a'
        WHERE B.EXTRA1 = '1'
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty prepend=" AND " property="intDate">A.JRGJCSRQ <![CDATA[ >= ]]>  #intDate# AND A.JRGJCSRQ
            <![CDATA[ <= ]]> #intDateEd#
        </isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty prepend=" AND " property="applyDate">A.GJSQRQ <![CDATA[ >= ]]>  #applyDate# AND A.GJSQRQ
            <![CDATA[ <= ]]> #applyDateEd#
        </isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty prepend=" AND " property="cyDate">C.STATE_SQRQ <![CDATA[ >= ]]>  #cyDate# AND C.STATE_SQRQ
            <![CDATA[ <= ]]> #cyDateEd#
        </isNotEmpty>
        <isNotEmpty property="yf">
            <!-- 统计类型：进入国际阶段日期 -->
            <isNotEmpty prepend=" AND " property="intDate">MONTH(A.JRGJCSRQ) = #yf#</isNotEmpty>
            <!-- 统计类型：申请日期 -->
            <isNotEmpty prepend=" AND " property="applyDate">MONTH(A.GJSQRQ) = #yf#</isNotEmpty>
            <!-- 统计类型：国家授权日期 -->
            <isNotEmpty prepend=" AND " property="cyDate">MONTH(C.STATE_SQRQ) = #yf#</isNotEmpty>
        </isNotEmpty>
        <!-- 专利类型：发明  实用新型  外观专利 -->
        <isNotEmpty prepend=" AND " property="zllx">A.IN_ZLTYPE = #zllx#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">B.CODE_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">B.CODE_PATH NOT LIKE '%BSZG%' AND B.CODE_PATH NOT LIKE '%BGTM%' AND B.CODE_PATH
            NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isvalid">A.isvalid = #isvalid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="projectCode">b.EXTRA4 = #projectCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="sclx">SUBSTRING(s.PROJECT_CODE,2,2) = #sclx#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dwCode">B.DEPTCBBM = #dwCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="deptPath">B.CODE_PATH LIKE '%$deptPath$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isGtcp">d.EXTRA4 = '1'</isNotEmpty>
        GROUP BY A.JWSQ_ID) tmp
        ORDER BY tmp.gwSqh
    </select>

    <select id="queryGjsqrqJpgCt" parameterClass="hashmap" resultClass="hashmap">
        SELECT SUM(B.EXTRA2) * 0.01 AS "gxxs"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_RYXX B ON A.EXTRA1 = B.APPLY_ID
        LEFT JOIN ${zzzcSchema}.T_KIZL_APPLY_BASEINFO d ON B.APPLY_ID = d.APPLY_ID
        <isNotEmpty property="cysq">
            LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO C ON C.JWSQ_ID = A.JWSQ_ID
        </isNotEmpty>
        <isNotEmpty property="gjsq">
            LEFT JOIN (
            SELECT
            JWSQ_ID,
            max(STATE_SQRQ) AS STATE_SQRQ,
            max(STATE_NAME) AS STATE_NAME
            FROM
            ${zzzcSchema}.T_KWZL_GJJD_BASEINFO
            WHERE
            STATE_SQRQ IS NOT NULL
            AND STATE_SQRQ != ''
            GROUP BY
            JWSQ_ID ) C ON
            C.JWSQ_ID = A.JWSQ_ID
        </isNotEmpty>
        INNER JOIN ${kjglSchema}.T_KYXM_PROJECT s ON s.PROJECT_CODE = b.EXTRA4 AND s.EXTRA11 = 'a'
        WHERE B.EXTRA1 = '1'
        <!-- 统计类型：进入国际阶段日期 -->
        <isNotEmpty prepend=" AND " property="intDate">A.JRGJCSRQ <![CDATA[ >= ]]>  #intDate# AND A.JRGJCSRQ
            <![CDATA[ <= ]]> #intDateEd#
        </isNotEmpty>
        <!-- 统计类型：申请日期 -->
        <isNotEmpty prepend=" AND " property="applyDate">A.GJSQRQ <![CDATA[ >= ]]>  #applyDate# AND A.GJSQRQ
            <![CDATA[ <= ]]> #applyDateEd#
        </isNotEmpty>
        <!-- 统计类型：国家授权日期 -->
        <isNotEmpty prepend=" AND " property="cyDate">C.STATE_SQRQ <![CDATA[ >= ]]>  #cyDate# AND C.STATE_SQRQ
            <![CDATA[ <= ]]> #cyDateEd#
        </isNotEmpty>
        <isNotEmpty property="yf">
            <!-- 统计类型：进入国际阶段日期 -->
            <isNotEmpty prepend=" AND " property="intDate">MONTH(A.JRGJCSRQ) = #yf#</isNotEmpty>
            <!-- 统计类型：申请日期 -->
            <isNotEmpty prepend=" AND " property="applyDate">MONTH(A.GJSQRQ) = #yf#</isNotEmpty>
            <!-- 统计类型：国家授权日期 -->
            <isNotEmpty prepend=" AND " property="cyDate">MONTH(C.STATE_SQRQ) = #yf#</isNotEmpty>
        </isNotEmpty>
        <!-- 专利类型：发明  实用新型  外观专利 -->
        <isNotEmpty prepend=" AND " property="zllx">A.IN_ZLTYPE = #zllx#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">B.CODE_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">B.CODE_PATH NOT LIKE '%BSZG%' AND B.CODE_PATH NOT LIKE '%BGTM%' AND B.CODE_PATH
            NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="isvalid">A.isvalid = #isvalid#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="projectCode">b.EXTRA4 = #projectCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="sclx">SUBSTRING(s.PROJECT_CODE,2,2) = #sclx#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="dwCode">B.DEPTCBBM = #dwCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="deptPath">B.CODE_PATH LIKE '%$deptPath$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isGtcp">d.EXTRA4 = '1'</isNotEmpty>
    </select>

    <select id="queryHpgExpInfo" parameterClass="hashmap"
            resultClass="com.baosight.bscdkj.common.kw.domain.TkwzlHpgExport">
        SELECT
        A.RECORD_ID AS "recordId",
        B.JWSQ_ID AS "jwsqId",
        B.IN_SQH AS "patentNo",
        B.IN_BGBH AS "bgbh",
        B.IN_ZLMC AS "applyName",
        B.IN_ZLTYPE AS "patentType",
        B.GJSQRQ AS "slrq",
        B.GLDW_NAME AS "gldwName",
        CASE WHEN A.ISYY = '1' THEN '是' ELSE '否' END AS "isYy",
        A.CONTENT_GSWMY AS "contentGswmy",
        A.REASON_CWYY AS "reasonCwyy",
        A.IMPORTANT_PJ AS "importantPj",
        A.YJXY_JE AS "yjxyJe",
        CASE WHEN A.ISNRXGBZ = '1' THEN '是' ELSE '否' END AS "isNrxgbz",
        A.CONTENT_GFTG AS "contentGftg",
        A.YQSM_GFTG AS "yqsmGftg",
        A.YQSM_JTYZ AS "yqsmYtyz",
        A.YQSM_DWMY AS "yqsmDwmy",
        B.PA_STATUS AS "zlzt"
        FROM ${zzzcSchema}.T_KWZL_APPLY_HPGINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.APPLY_ID = B.JWSQ_ID
        WHERE A.FLOW_STATUS = 'end'
        <isNotEmpty prepend=" AND " property="year">A.YEAR = #year#</isNotEmpty>
    </select>

    <select id="queryGjhjtj" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        B.JWSQ_ID AS "jwsqId",
        B.GJSQRQ AS "gjsqrq",
        B.GW_SQH AS "gwSqh",
        B.IN_ZLMC AS "zlmc",
        A.STATE_NAME AS "stateName",
        B.JRGJJDRQ AS "jrgjjdrq",
        A.GJ_SQH AS "gjSqh"
        FROM ${zzzcSchema}.T_KWZL_GJJD_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        WHERE B.JRGJJDRQ IS NOT NULL
        <isNotEmpty prepend=" AND " property="startDate">B.JRGJJDRQ <![CDATA[ >= ]]>  #startDate# AND B.JRGJJDRQ
            <![CDATA[ <= ]]> #endDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="year">YEAR(B.JRGJJDRQ) = #year#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT LIKE
            'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>


    <select id="queryNoGjhjtj" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        JWSQ_ID AS "jwsqId",
        GW_SQH AS "gwSqh",
        IN_ZLMC AS "zlmc",
        NOGJJDRQ AS "nogjjdrq"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO
        WHERE NOGJJDRQ IS NOT NULL
        <isNotEmpty prepend=" AND " property="startDate">NOGJJDRQ <![CDATA[ >= ]]>  #startDate# AND NOGJJDRQ
            <![CDATA[ <= ]]> #endDate#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="year">YEAR(NOGJJDRQ) = #year#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND FIRST_DEPT_PATH NOT LIKE
            'BGTM%' AND FIRST_DEPT_PATH NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>

    <select id="queryLnNoGjhjtj" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        COUNT(JWSQ_ID) AS "count",
        YEAR(
        <isNotEmpty property="cyDate">JRGJJDRQ</isNotEmpty>
        <isNotEmpty property="noCyDate">NOGJJDRQ</isNotEmpty>) AS "year"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="cyDate">JRGJJDRQ <![CDATA[ >= ]]>  #cyDate# AND JRGJJDRQ
                <![CDATA[ <= ]]>
                #cyDateEnd#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="noCyDate">NOGJJDRQ <![CDATA[ >= ]]>  #noCyDate# AND NOGJJDRQ
                <![CDATA[ <= ]]> #noCyDateEnd#
            </isNotEmpty>
            <!-- 基地-->
            <isNotEmpty prepend=" AND " property="jd">FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
            <!-- 宝山基地-->
            <isNotEmpty prepend=" AND " property="bsJd">FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND FIRST_DEPT_PATH NOT LIKE
                'BGTM%' AND FIRST_DEPT_PATH NOT LIKE '%BGBW%'
            </isNotEmpty>
        </dynamic>
        GROUP BY
        YEAR(
        <isNotEmpty property="cyDate">JRGJJDRQ</isNotEmpty>
        <isNotEmpty property="noCyDate">NOGJJDRQ</isNotEmpty>)
    </select>


    <select id="queryLnGjhjtj" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        COUNT(B.JWSQ_ID) AS "count",
        YEAR(
        <isNotEmpty property="cyDate">B.JRGJJDRQ</isNotEmpty>
        <isNotEmpty property="noCyDate">B.NOGJJDRQ</isNotEmpty>) AS "year"
        FROM
        ${zzzcSchema}.T_KWZL_GJJD_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON
        A.JWSQ_ID = B.JWSQ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="cyDate">B.JRGJJDRQ <![CDATA[ >= ]]>  #cyDate# AND B.JRGJJDRQ
                <![CDATA[ <= ]]> #cyDateEnd#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="noCyDate">B.NOGJJDRQ <![CDATA[ >= ]]>  #noCyDate# AND B.NOGJJDRQ
                <![CDATA[ <= ]]> #noCyDateEnd#
            </isNotEmpty>
            <!-- 基地-->
            <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
            <!-- 宝山基地-->
            <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT
                LIKE
                'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
            </isNotEmpty>
        </dynamic>
        GROUP BY
        YEAR(
        <isNotEmpty property="cyDate">B.JRGJJDRQ</isNotEmpty>
        <isNotEmpty property="noCyDate">B.NOGJJDRQ</isNotEmpty>)
    </select>

    <select id="queryMgZlSqTj" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        B.GJSQRQ AS "gjsqrq",
        B.PCTSQH AS "pctSqh",
        B.priority_date AS "priorityDate" ,
        B.GW_SQH AS "gwSqh",
        B.sqh AS "sqh" ,
        B.in_zlmc AS "inZlmc" ,
        A.name_eng AS "nameEng" ,
        A.state_sqh AS "stateSqh" ,
        A.state_zlh AS "stateZlh" ,
        B.sbbm_name AS "sbbmName" ,
        B.all_person AS "allPerson" ,
        A.state_sqrq AS "stateSqrq" ,
        A.zrzzrq AS "zrzzrq" ,
        A.after_fqrq AS "afterFqrq" ,
        A.zdfq_reason AS "zdfqReason",
        A.GJ_SQH AS "gjSqh"
        FROM ${zzzcSchema}.T_KWZL_GJJD_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="sqDate">B.JRGJJDRQ <![CDATA[ >= ]]>  #sqDate# AND B.JRGJJDRQ
                <![CDATA[ <= ]]> #sqDateEnd#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="stateDate">A.STATE_SQRQ <![CDATA[ >= ]]>  #stateDate# AND A.STATE_SQRQ
                <![CDATA[ <= ]]> #stateDateEnd#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="stateCode">A.STATE_CODE = #stateCode#</isNotEmpty>
            <!-- 基地-->
            <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
            <!-- 宝山基地-->
            <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT
                LIKE
                'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="querySqzl" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        SUM(CASE WHEN A.FIRST_DEPT_PATH LIKE '%BGTA00%' THEN 1 ELSE 0 END ) AS "BGTA",
        SUM(CASE WHEN A.FIRST_DEPT_PATH LIKE '%BGBW%' THEN 1 ELSE 0 END ) AS "BGBW",
        SUM(CASE WHEN A.FIRST_DEPT_PATH LIKE '%BSZG%' THEN 1 ELSE 0 END ) AS "BSZG",
        SUM(CASE WHEN A.FIRST_DEPT_PATH LIKE '%BGTM%' THEN 1 ELSE 0 END ) AS "BGTM",
        SUM(CASE WHEN A.FIRST_DEPT_PATH LIKE '%BGTA00%' THEN 1
        WHEN A.FIRST_DEPT_PATH LIKE '%BGBW%' THEN 1
        WHEN A.FIRST_DEPT_PATH LIKE '%BSZG%' THEN 1
        WHEN A.FIRST_DEPT_PATH LIKE '%BGTM%' THEN 1 ELSE 0 END ) AS "OTHER"
        FROM
        ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        WHERE B.ISVALID = '1' AND A.ISVALID = '1'
        AND B.STATE_SQRQ IS NOT NULL
        <isNotEmpty prepend=" AND " property="startDate">A.GJSQRQ <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">A.GJSQRQ <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
    </select>

    <select id="queryzlsqsCount" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        'CN' AS "type",
        COUNT(B.JWSQ_ID) AS "count"
        FROM
        ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B
        WHERE (B.DWHZ_ID IS NULL OR B.DWHZ_ID = '')
        <isNotEmpty prepend=" AND " property="cyDate">B.JRGJCSRQ <![CDATA[ >= ]]>  #cyDate# AND B.JRGJCSRQ
            <![CDATA[ <= ]]> #cyDateEnd#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sqDate">B.GJSQRQ <![CDATA[ >= ]]>  #sqDate# AND B.GJSQRQ
            <![CDATA[ <= ]]> #sqDateEnd#
        </isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT LIKE
            'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noCy">B.NOGJJDRQ IS NOT NULL AND B.NOGJJDRQ != ''</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isqqsf">B.ISQQSF = #isqqsf#</isNotEmpty>
        UNION ALL
        SELECT
        'AU' AS "type",
        COUNT(B.JWSQ_ID) AS "count"
        FROM
        ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B
        WHERE B.DWHZ_ID IS NOT NULL AND B.DWHZ_ID != ''
        <isNotEmpty prepend=" AND " property="cyDate">B.GJSQRQ <![CDATA[ >= ]]>  #cyDate# AND B.GJSQRQ
            <![CDATA[ <= ]]> #cyDateEnd#
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="sqDate">B.GJSQRQ <![CDATA[ >= ]]>  #sqDate# AND B.GJSQRQ
            <![CDATA[ <= ]]> #sqDateEnd#
        </isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT LIKE
            'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
        </isNotEmpty>
        <isNotEmpty prepend=" AND " property="noCy">B.NOGJJDRQ IS NOT NULL AND B.NOGJJDRQ != ''</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isqqsf">B.ISQQSF = #isqqsf#</isNotEmpty>
    </select>

    <select id="queryzlsqsCountGroupBydw" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        B.GLDW_CODE AS "sbbmCode",
        MAX(B.GLDW_NAME) AS "sbbmName" ,
        COUNT(B.JWSQ_ID) AS "count"
        FROM
        ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="cyDate">B.JRGJCSRQ <![CDATA[ >= ]]>  #cyDate# AND B.JRGJCSRQ
                <![CDATA[ <= ]]> #cyDateEnd#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqDate">B.GJSQRQ <![CDATA[ >= ]]>  #sqDate# AND B.GJSQRQ
                <![CDATA[ <= ]]> #sqDateEnd#
            </isNotEmpty>
            <!-- 基地-->
            <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
            <!-- 宝山基地-->
            <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT
                LIKE
                'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="noCy">B.NOGJJDRQ IS NOT NULL AND B.NOGJJDRQ != ''</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isqqsf">B.ISQQSF = #isqqsf#</isNotEmpty>
        </dynamic>
        GROUP BY B.GLDW_CODE
    </select>

    <select id="queryGjsqList" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        A.jwsq_id as "jwsqId" ,
        A.dwhz_id as "dwhzId" ,
        A.in_bgbh as "inBgbh" ,
        A.sqh as "sqh" ,
        A.in_zlmc as "inZlmc" ,
        A.in_slrq as "inSlrq" ,
        A.in_zltype as "inZltype" ,
        A.sbbm_code as "sbbmCode" ,
        A.sbbm_name as "sbbmName" ,
        A.from_type as "fromType" ,
        A.isjpg as "isjpg" ,
        A.jpg_team as "jpgTeam" ,
        A.priority_date as "priorityDate" ,
        A.all_person as "allPerson" ,
        A.bzr as "bzr" ,
        A.bzr_name as "bzrName" ,
        A.start_reason as "startReason" ,
        A.first_person as "firstPerson" ,
        A.first_person_name as "firstPersonName" ,
        A.first_phone as "firstPhone" ,
        A.first_mobile as "firstMobile" ,
        A.second_person as "secondPerson" ,
        A.second_person_name as "secondPersonName" ,
        A.second_phone as "secondPhone" ,
        A.second_mobile as "secondMobile" ,
        A.solve_problem as "solveProblem" ,
        A.lead_where as "leadWhere" ,
        A.foreign_prospect as "foreignProspect" ,
        A.sqgj_tcr as "sqgjTcr" ,
        A.sqgj_expert as "sqgjExpert" ,
        A.sqgj_final as "sqgjFinal" ,
        A.isqqsf as "isqqsf" ,
        A.zxh as "zxh" ,
        A.gw_sqh as "gwSqh" ,
        A.sws_guid as "swsGuid" ,
        A.sws_lxr as "swsLxr" ,
        A.sws_dlrxm as "swsDlrxm" ,
        A.sws_dlremail as "swsDlremail" ,
        A.sws_dlrphone as "swsDlrphone" ,
        A.gjsqrq as "gjsqrq" ,
        A.pctsqh as "pctsqh" ,
        A.apply_reason as "applyReason" ,
        A.pa_status as "paStatus" ,
        A.iszzsq as "iszzsq" ,
        A.zzsq_reason as "zzsqReason" ,
        A.flow_status as "flowStatus" ,
        A.isvalid as "isvalid" ,
        A.sws_no as "swsNo" ,
        A.dlr_no as "dlrNo" ,
        A.is_gjcs as "isGjcs" ,
        A.sqr as "sqr" ,
        A.jpg_xh as "jpgXh" ,
        A.jrgjcsrq as "jrgjcsrq" ,
        A.jrgjjdrq as "jrgjjdrq" ,
        A.nogjjdrq as "nogjjdrq",
        A.first_dept_path as "firstDeptPath"
        FROM
        ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="cyDate">A.JRGJCSRQ <![CDATA[ >= ]]>  #cyDate# AND A.JRGJCSRQ
                <![CDATA[ <= ]]> #cyDateEnd#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqDate">A.GJSQRQ <![CDATA[ >= ]]>  #sqDate# AND A.GJSQRQ
                <![CDATA[ <= ]]> #sqDateEnd#
            </isNotEmpty>
            <!-- 基地-->
            <isNotEmpty prepend=" AND " property="jd">A.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
            <!-- 宝山基地-->
            <isNotEmpty prepend=" AND " property="bsJd">A.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND A.FIRST_DEPT_PATH NOT
                LIKE
                'BGTM%' AND A.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="noCy">A.NOGJJDRQ IS NOT NULL AND A.NOGJJDRQ != ''</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isqqsf">A.ISQQSF = #isqqsf#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sbbmCode">A.GLDW_CODE = #sbbmCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isAu">A.DWHZ_ID IS NOT NULL AND A.DWHZ_ID != ''</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isNotAu">(A.DWHZ_ID IS NULL OR A.DWHZ_ID = '')</isNotEmpty>
            <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
        </dynamic>
    </select>

    <select id="queryGwSqsCount" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        'A' AS "name" ,
        COUNT(A.JWZL_ID) AS "count"
        FROM
        ${zzzcSchema}.T_KWZL_GJJD_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        <dynamic prepend="WHERE">
        <isNotEmpty prepend=" AND " property="startDate">A.STATE_SQRQ <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">A.STATE_SQRQ <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="iszdfq">A.ISZDFQ = #iszdfq#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT LIKE
            'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
        </isNotEmpty>
        </dynamic>
        UNION
        SELECT
        'B' AS "name" ,
        COUNT(A.JWZL_ID) AS "count"
        FROM
        ${zzzcSchema}.T_KWZL_GJJD_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        WHERE A.STATE_CODE NOT IN('EP')
        <isNotEmpty prepend=" AND " property="startDate">A.STATE_SQRQ <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">A.STATE_SQRQ <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="iszdfq">A.ISZDFQ = #iszdfq#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT LIKE
            'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
        </isNotEmpty>

    </select>

    <select id="queryzlsqsGroupBy" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        B.GLDW_CODE AS "sbbmCode",
        MAX(B.GLDW_NAME) AS "sbbmName",
        COUNT(A.JWZL_ID) AS "sq",
        SUM(CASE WHEN A.STATE_CODE NOT IN('EP') THEN 1 ELSE 0 END ) AS "nosq"
        FROM
        ${zzzcSchema}.T_KWZL_GJJD_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        <dynamic prepend="WHERE">
        <isNotEmpty prepend=" AND " property="startDate">A.STATE_SQRQ <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">A.STATE_SQRQ <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="iszdfq">A.ISZDFQ = #iszdfq#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT LIKE
            'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
        </isNotEmpty>
        </dynamic>
        GROUP BY B.GLDW_CODE
    </select>

    <select id="queryGwSqsList" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        B.SQH AS "sqh",
        B.in_bgbh as "inBgbh" ,
        B.in_zlmc as "inZlmc" ,
        A.NAME_ENG AS "nameEng" ,
        A.STATE_ZLH AS "stateZlh" ,
        B.SBBM_NAME AS "sbbmName",
        B.ALL_PERSON AS "allPerson" ,
        A.STATE_NAME AS "stateName" ,
        A.STATE_SQRQ AS "stateSqrq" ,
        A.ZRZZRQ AS "zrzzrq" ,
        A.AFTER_FQRQ AS "afterFqrq" ,
        A.STATE_SQH AS "stateSqh" ,
        B.SQR AS "sqr",
        B.GJSQRQ AS "gjsqrq",
        B.PRIORITY_DATE AS "priorityDate" ,
        B.PCTSQH AS "pctsqh",
        A.GJ_SQH AS "gjSqh"
        FROM
        ${zzzcSchema}.T_KWZL_GJJD_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="startDate">A.STATE_SQRQ <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">A.STATE_SQRQ <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="iszdfq">A.ISZDFQ = #iszdfq#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isEpo">A.STATE_CODE != 'EP'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sbbmCode">B.FIRST_DEPT_PATH LIKE '%$sbbmCode$%'</isNotEmpty>
            <!-- 基地-->
            <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
            <!-- 宝山基地-->
            <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT
                LIKE
                'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
            </isNotEmpty>
        </dynamic>
    </select>


    <select id="querySqgjByJwsqId" parameterClass="hashmap" resultClass="hashmap">
        SELECT LISTAGG(STATE_NAME, ',') AS "stateName"
        FROM ${zzzcSchema}.T_KWZL_GJJD_BASEINFO
        WHERE STATE_SQRQ IS NOT NULL
          AND JWSQ_ID = #jwsqId#
    </select>

    <select id="queryJfTjList" parameterClass="hashmap" resultClass="hashmap">
        SELECT C.GW_SQH AS "gwSqh",C.IN_ZLMC AS "inZlmc",B.STATE_NAME AS "stateName",A.PAY_NUM AS "payNum",A.PAY_MONEY
        AS "payMoney"
        FROM ${zzzcSchema}.T_KWZL_MONEY_PAINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO C ON A.GJJD_ID = C.JWSQ_ID
        LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO B ON C.JWSQ_ID = B.JWSQ_ID
        WHERE A.PA_TYPE = '01'
        <isNotEmpty prepend=" AND " property="startDate">A.PAY_DATE <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">A.PAY_DATE <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="gwSqh">A.GW_SQH = #gwSqh#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">C.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">C.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND C.FIRST_DEPT_PATH NOT LIKE
            'BGTM%' AND C.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
        </isNotEmpty>
        UNION ALL
        SELECT C.GW_SQH AS "gwSqh",C.IN_ZLMC AS "inZlmc",B.STATE_NAME AS "stateName",A.PAY_NUM AS "payNum",A.PAY_MONEY
        AS "payMoney"
        FROM
        ${zzzcSchema}.T_KWZL_MONEY_PAINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO B ON
        A.GJJD_ID = B.JWZL_ID
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO C ON
        C.JWSQ_ID = B.JWSQ_ID
        WHERE A.PA_TYPE = '02'
        <isNotEmpty prepend=" AND " property="startDate">A.PAY_DATE <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">A.PAY_DATE <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="gwSqh">A.GW_SQH = #gwSqh#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="gwSqh"></isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">C.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">C.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND C.FIRST_DEPT_PATH NOT
            LIKE
            'BGTM%' AND C.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
        </isNotEmpty>
    </select>

    <select id="queryJfTjSum" parameterClass="hashmap" resultClass="hashmap">
        SELECT SUM(tmp.payMoney) AS "payMoney"
        FROM (SELECT SUM(A.PAY_MONEY) AS payMoney
        FROM ${zzzcSchema}.T_KWZL_MONEY_PAINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO C ON A.GJJD_ID = C.JWSQ_ID
        LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO B ON C.JWSQ_ID = B.JWSQ_ID
        WHERE A.PA_TYPE = '01'
        <isNotEmpty prepend=" AND " property="startDate">A.PAY_DATE <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">A.PAY_DATE <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="gwSqh">A.GW_SQH = #gwSqh#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">C.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">C.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND C.FIRST_DEPT_PATH NOT LIKE
            'BGTM%' AND C.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
        </isNotEmpty>
        UNION ALL
        SELECT SUM(A.PAY_MONEY) AS payMoney
        FROM
        ${zzzcSchema}.T_KWZL_MONEY_PAINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO B ON
        A.GJJD_ID = B.JWZL_ID
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO C ON
        C.JWSQ_ID = B.JWSQ_ID
        WHERE A.PA_TYPE = '02'
        <isNotEmpty prepend=" AND " property="startDate">A.PAY_DATE <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">A.PAY_DATE <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="gwSqh">A.GW_SQH = #gwSqh#</isNotEmpty>
        <!-- 基地-->
        <isNotEmpty prepend=" AND " property="jd">C.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
        <!-- 宝山基地-->
        <isNotEmpty prepend=" AND " property="bsJd">C.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND C.FIRST_DEPT_PATH NOT LIKE
            'BGTM%' AND C.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
        </isNotEmpty>) tmp
    </select>

    <select id="queryMROtjList" parameterClass="hashmap" resultClass="hashmap">
        SELECT B.SQH AS "sqh",B.in_zlmc as "inZlmc" ,A.NAME_ENG AS "nameEng" , A.STATE_ZLH AS "stateZlh"
        ,B.SBBM_CODE AS "sbbmCode",B.SBBM_NAME AS "sbbmName",B.ALL_PERSON AS "allPerson" ,A.STATE_CODE AS "stateCode" ,A.STATE_NAME AS "stateName" ,
        A.STATE_SQRQ AS "stateSqrq" ,A.ZRZZRQ AS "zrzzrq" ,
        A.AFTER_FQRQ AS "afterFqrq" ,A.ZDFQ_REASON AS "zdfqReason",A.STATE_SQH AS "stateSqh" ,
        B.SQR AS "sqr",
        B.GJSQRQ AS "gjsqrq",
        B.PRIORITY_DATE AS "priorityDate" ,
        B.PCTSQH AS "pctsqh",
        B.JWSQ_ID AS "jwsqId",
        B.GW_SQH AS "gwSqh",
        A.GJ_SQH AS "gjSqh"
        FROM
        ${zzzcSchema}.T_KWZL_GJJD_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="isMro">A.STATE_CODE IN('US','JP','EP')</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isNotMro">A.STATE_CODE = 'JP' AND A.STATE_SQRQ IS NOT NULL AND A.STATE_CODE = 'EP' AND A.STATE_SQRQ IS NOT NULL AND A.STATE_CODE = 'US' AND A.STATE_SQRQ IS NOT NULL</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqStartDate">A.STATE_SQRQ <![CDATA[ >= ]]>  #sqStartDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqEndDate">A.STATE_SQRQ <![CDATA[ <= ]]>  #sqEndDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="slStartDate">B.JRGJJDRQ <![CDATA[ >= ]]>  #slStartDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="slEndDate">B.JRGJJDRQ <![CDATA[ <= ]]>  #slEndDate#</isNotEmpty>
            <!-- 基地-->
            <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
            <!-- 宝山基地-->
            <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT
                LIKE
                'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="queryAUtjCount" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        SUM(CASE WHEN A.STATE_CODE = 'AU' THEN 1 ELSE 0 END) AS "sqs" ,
        COUNT(A.JWZL_ID) AS "bhoz",
        SUM(CASE WHEN A.STATE_CODE NOT IN('EP') THEN 1
        ELSE 0 END) AS "bbhoz"
        FROM ${zzzcSchema}.T_KWZL_GJJD_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        WHERE SUBSTRING(A.GJ_SQH ,5,2) = 'AU'
            <isNotEmpty prepend=" AND " property="jrStartDate">B.JRGJCSRQ <![CDATA[ >= ]]>  #jrStartDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jrEndDate">B.JRGJCSRQ <![CDATA[ <= ]]>  #jrEndDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="slStartDate">B.GJSQRQ <![CDATA[ >= ]]>  #slStartDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="slEndDate">B.GJSQRQ <![CDATA[ <= ]]>  #slEndDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqStartDate">A.STATE_SQRQ <![CDATA[ >= ]]>  #sqStartDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqEndDate">A.STATE_SQRQ <![CDATA[ <= ]]>  #sqEndDate#</isNotEmpty>
            <!-- 基地-->
            <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
            <!-- 宝山基地-->
            <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT
                LIKE
                'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
            </isNotEmpty>
    </select>

    <select id="queryAUtjList" parameterClass="hashmap" resultClass="hashmap">
        SELECT B.SQH AS "sqh",C.PA_NAME as "inZlmc" ,A.NAME_ENG AS "nameEng" , A.STATE_ZLH AS "stateZlh"
        ,B.SBBM_NAME AS "sbbmName",B.ALL_PERSON AS "allPerson" ,A.STATE_NAME AS "stateName" ,
        A.STATE_SQRQ AS "stateSqrq" ,A.ZRZZRQ AS "zrzzrq" ,
        A.AFTER_FQRQ AS "afterFqrq" ,A.ZDFQ_REASON AS "zdfqReason",A.STATE_SQH AS "stateSqh" ,
        B.SQR AS "sqr",
        B.GJSQRQ AS "gjsqrq",
        B.PRIORITY_DATE AS "priorityDate" ,
        B.PCTSQH AS "pctsqh",
        A.GJ_SQH AS "gjSqh",
        B.GW_SQH AS "gwSqh"
        FROM ${zzzcSchema}.T_KWZL_GJJD_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        LEFT JOIN ${zzzcSchema}.T_KWZL_DWHZ_BASEINFO C ON B.DWHZ_ID = C.DWHZ_ID
        WHERE SUBSTRING(A.GJ_SQH ,5,2) = 'AU'
            <isNotEmpty prepend=" AND " property="jrStartDate">B.JRGJCSRQ <![CDATA[ >= ]]>  #jrStartDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jrEndDate">B.JRGJCSRQ <![CDATA[ <= ]]>  #jrEndDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="slStartDate">B.GJSQRQ <![CDATA[ >= ]]>  #slStartDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="slEndDate">B.GJSQRQ <![CDATA[ <= ]]>  #slEndDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqStartDate">A.STATE_SQRQ <![CDATA[ >= ]]>  #sqStartDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="sqEndDate">A.STATE_SQRQ <![CDATA[ <= ]]>  #sqEndDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isAu">A.STATE_CODE = 'AU'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isNotOz">A.STATE_CODE NOT IN('EP')
            </isNotEmpty>
            <!-- 基地-->
            <isNotEmpty prepend=" AND " property="jd">B.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
            <!-- 宝山基地-->
            <isNotEmpty prepend=" AND " property="bsJd">B.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND B.FIRST_DEPT_PATH NOT
                LIKE
                'BGTM%' AND B.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
            </isNotEmpty>
    </select>

    <select id="queryZjpsCount" parameterClass="hashmap" resultClass="hashmap">
        SELECT B.GROUP_MEMBERS AS "zjCode",MAX(B.GROUP_MEMBERS_NAME) AS "zjName",SUM(B.APPROVE_NUM) AS "pscs"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ B ON A.JWSQ_ID = B.BIZ_ID
        LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_INFO C ON B.REVIEWREQ_ID = C.REVIEWREQ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="startDate">C.REVIEW_DATE <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">C.REVIEW_DATE <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
            <!-- 基地-->
            <isNotEmpty prepend=" AND " property="jd">A.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
            <!-- 宝山基地-->
            <isNotEmpty prepend=" AND " property="bsJd">A.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND A.FIRST_DEPT_PATH NOT
                LIKE
                'BGTM%' AND A.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
            </isNotEmpty>
        </dynamic>
        GROUP BY B.GROUP_MEMBERS
    </select>

    <select id="queryZjpsList" parameterClass="hashmap" resultClass="hashmap">
        SELECT A.IN_ZLMC AS "inZlmc",A.SBBM_NAME AS "sbbmName",B.GROUP_MEMBERS || B.GROUP_MEMBERS_NAME AS
        "zjName",B.APPROVE_NUM AS "pscs",C.REVIEW_DATE AS "psrq",C.REVIEW_COMMENT AS "psyj"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ B ON A.JWSQ_ID = B.BIZ_ID
        LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_INFO C ON B.REVIEWREQ_ID = C.REVIEWREQ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="startDate">C.REVIEW_DATE <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">C.REVIEW_DATE <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="zjCode">B.GROUP_MEMBERS = #zjCode#</isNotEmpty>
            <!-- 基地-->
            <isNotEmpty prepend=" AND " property="jd">A.FIRST_DEPT_PATH LIKE '%$jd$%'</isNotEmpty>
            <!-- 宝山基地-->
            <isNotEmpty prepend=" AND " property="bsJd">A.FIRST_DEPT_PATH NOT LIKE '%BSZG%' AND A.FIRST_DEPT_PATH NOT
                LIKE
                'BGTM%' AND A.FIRST_DEPT_PATH NOT LIKE '%BGBW%'
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="queryZzSqzl" parameterClass="hashmap" resultClass="hashmap">
        SELECT A.JWSQ_ID AS "jwsqId",
        MAX(A.IN_ZLMC) AS "inZlmc",
        MAX(A.IN_SQH) AS "inSqh",
        MAX(A.IN_SLRQ) AS "inSlrq",
        MAX(A.IN_BGBH) AS "inBgbh",
        MAX(A.PCTSQH) AS "pctSqh",
        MAX(A.SQR) AS "sqr",
        MAX(A.GJSQRQ) AS "gjsqrq",
        MAX(A.SWS_GUID) AS "swsGuid",
        LISTAGG(B.STATE_NAME, ',') AS "stateName",
        LISTAGG(B.JWZL_ID,',') AS "jwzlId"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_GJJD_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="isValid">B.ISVALID = #isValid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="startDate">B.STATE_SQRQ <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">B.STATE_SQRQ <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="jwsqId">A.JWSQ_ID = #jwsqId#</isNotEmpty>
        </dynamic>
        GROUP BY A.JWSQ_ID
    </select>

    <select id="countHpgYear" parameterClass="hashmap" resultClass="hashmap">
        SELECT A.YEAR AS "name"
        FROM ${zzzcSchema}.T_KWZL_APPLY_HPGINFO A
        GROUP BY A.YEAR
    </select>

    <select id="queryHpgGZCount" parameterClass="hashmap" resultClass="hashmap">
        SELECT B.GLDW_CODE AS "deptCode",
        MAX(B.GLDW_NAME) AS "deptName",
        SUM(CASE WHEN C.CURRENT_ACTIVITY IN('Manual1', 'Manual2') THEN 1 ELSE 0 END) AS "fmrwpg",
        SUM(CASE WHEN R.MODULE_CODE = 'kwzl_hpg' AND R.IS_END = '0' THEN 1 ELSE 0 END) AS "zjps",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual3' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "bmgly",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual4' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "bmld",
        SUM(CASE WHEN A.EXTRA4 = '1' THEN 1 ELSE 0 END) AS "bmfq",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual5' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "dwgly",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual6' THEN 1 ELSE 0 END) AS "dwld",
        SUM(CASE WHEN A.EXTRA5 = '1' THEN 1 ELSE 0 END) AS "dwfq",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual8' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "zgbmgly",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual9' THEN 1 ELSE 0 END) AS "zgbmzg",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual11' THEN 1 ELSE 0 END) AS "zgbmbz",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual12' THEN 1 ELSE 0 END) AS "zgbmld",
        SUM(CASE WHEN A.FLOW_STATUS = 'end' THEN 1 ELSE 0 END) AS "pgjs",
        SUM(CASE WHEN A.FLOW_STATUS != 'draft' THEN 1 ELSE 0 END) AS "zs",
        SUM(CASE WHEN A.FIN_ISFQ = '1' THEN 1 ELSE 0 END) AS "fq"
        FROM ${zzzcSchema}.T_KWZL_APPLY_HPGINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.APPLY_ID = B.JWSQ_ID
        LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO C ON A.RECORD_ID = C.BUSINESS_ID
        LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ R ON A.RECORD_ID = R.BIZ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="year">A.YEAR = #year#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gldwCode">B.GLDW_CODE IN
                <iterate property="gldwCode" conjunction="," open="(" close=")">
                    #gldwCode[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="dept">B.FIRST_DEPT_PATH LIKE '%$dept$%'</isNotEmpty>
        </dynamic>
        GROUP BY B.GLDW_CODE
    </select>

    <select id="queryHpgGZTotal" parameterClass="hashmap" resultClass="hashmap">
        SELECT DISTINCT
        '合计' AS "deptName",
        SUM(CASE WHEN C.CURRENT_ACTIVITY IN('Manual1', 'Manual2') THEN 1 ELSE 0 END) AS "fmrwpg",
        SUM(CASE WHEN R.MODULE_CODE = 'kwzl_hpg' AND R.IS_END = '0' THEN 1 ELSE 0 END) AS "zjps",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual3' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "bmgly",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual4' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "bmld",
        SUM(CASE WHEN A.EXTRA4 = '1' THEN 1 ELSE 0 END) AS "bmfq",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual5' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "dwgly",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual6' THEN 1 ELSE 0 END) AS "dwld",
        SUM(CASE WHEN A.EXTRA5 = '1' THEN 1 ELSE 0 END) AS "dwfq",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual8' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "zgbmgly",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual9' THEN 1 ELSE 0 END) AS "zgbmzg",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual11' THEN 1 ELSE 0 END) AS "zgbmbz",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual12' THEN 1 ELSE 0 END) AS "zgbmld",
        SUM(CASE WHEN A.FLOW_STATUS = 'end' THEN 1 ELSE 0 END) AS "pgjs",
        SUM(CASE WHEN A.FLOW_STATUS != 'draft' THEN 1 ELSE 0 END) AS "zs",
        SUM(CASE WHEN A.FIN_ISFQ = '1' THEN 1 ELSE 0 END) AS "fq"
        FROM ${zzzcSchema}.T_KWZL_APPLY_HPGINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.APPLY_ID = B.JWSQ_ID
        LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO C ON A.RECORD_ID = C.BUSINESS_ID
        LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ R ON A.RECORD_ID = R.BIZ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="year">A.YEAR = #year#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gldwCode">B.GLDW_CODE IN
                <iterate property="gldwCode" conjunction="," open="(" close=")">
                    #gldwCode[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="dept">B.FIRST_DEPT_PATH LIKE '%$dept$%'</isNotEmpty>
        </dynamic>
    </select>

    <select id="queryHpgGZList" parameterClass="hashmap" resultClass="hashmap">
        SELECT DISTINCT
        A.RECORD_ID AS "recordId",
        B.JWSQ_ID AS "jwsqId",
        B.IN_BGBH AS "bgbh",
        B.IN_ZLMC AS "applyName",
        B.SBBM_CODE AS "deptCode",
        B.SBBM_NAME AS "deptName",
        B.IN_SQH AS "zlh",
        B.ALL_PERSON AS "fmr",
        B.FIRST_PERSON AS "lxr" ,
        B.FIRST_PERSON_NAME AS "lxrName" ,
        C.CURRENT_OPERATOR AS "currentOperator",
        C.CURRENT_ACTIVITY_NAME AS "currentActivityName"
        FROM ${zzzcSchema}.T_KWZL_APPLY_HPGINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.APPLY_ID = B.JWSQ_ID
        LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO C ON A.RECORD_ID = C.BUSINESS_ID
        LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ R ON A.RECORD_ID = R.BIZ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="year">A.YEAR = #year#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="lx">C.CURRENT_ACTIVITY IN
                <iterate property="lx" conjunction="," open="(" close=")">
                    #lx[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="status">A.FLOW_STATUS = #status#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isQb">A.FLOW_STATUS != 'draft'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isFq">A.FIN_ISFQ = #isFq#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="bmfq">A.EXTRA4 = #bmfq#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dwfq">A.EXTRA5 = #dwfq#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="zjps">R.IS_END = '0'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isNoZj">R.REVIEWREQ_ID IS NULL</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gldwCode">B.GLDW_CODE IN
                <iterate property="gldwCode" conjunction="," open="(" close=")">
                    #gldwCode[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="dept">B.FIRST_DEPT_PATH LIKE '%$dept$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="deptCode">B.FIRST_DEPT_PATH LIKE '%$deptCode$%'</isNotEmpty>
        </dynamic>
    </select>


    <select id="queryHpgGZXZCount" parameterClass="hashmap" resultClass="hashmap">
        SELECT SYSIBM.REGEXP_SUBSTR(min(B.FIRST_DEPT_PATH), #regexp#) as "deptCode",
        MAX(B.SBBM_NAME) AS "deptName",
        SUM(CASE WHEN C.CURRENT_ACTIVITY IN('Manual1', 'Manual2') THEN 1 ELSE 0 END) AS "fmrwpg",
        SUM(CASE WHEN R.MODULE_CODE = 'kwzl_hpg' AND R.IS_END = '0' THEN 1 ELSE 0 END) AS "zjps",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual3' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "bmgly",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual4' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "bmld",
        SUM(CASE WHEN A.EXTRA4 = '1' THEN 1 ELSE 0 END) AS "bmfq",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual5' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "dwgly",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual6' THEN 1 ELSE 0 END) AS "dwld",
        SUM(CASE WHEN A.EXTRA5 = '1' THEN 1 ELSE 0 END) AS "dwfq",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual8' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "zgbmgly",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual9' THEN 1 ELSE 0 END) AS "zgbmzg",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual11' THEN 1 ELSE 0 END) AS "zgbmbz",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual12' THEN 1 ELSE 0 END) AS "zgbmld",
        SUM(CASE WHEN A.FLOW_STATUS = 'end' THEN 1 ELSE 0 END) AS "pgjs",
        SUM(CASE WHEN A.FLOW_STATUS != 'draft' THEN 1 ELSE 0 END) AS "zs",
        SUM(CASE WHEN A.FIN_ISFQ = '1' THEN 1 ELSE 0 END) AS "fq"
        FROM ${zzzcSchema}.T_KWZL_APPLY_HPGINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.APPLY_ID = B.JWSQ_ID
        LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO C ON A.RECORD_ID = C.BUSINESS_ID
        LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ R ON A.RECORD_ID = R.BIZ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="year">A.YEAR = #year#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gldwCode">B.GLDW_CODE = #gldwCode#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="unitCodePath">B.FIRST_DEPT_PATH LIKE '$unitCodePath$%'</isNotEmpty>
        </dynamic>
        GROUP BY SYSIBM.REGEXP_SUBSTR(B.FIRST_DEPT_PATH, #regexp#)
    </select>

    <select id="queryHpgGZXZTotal" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        '合计' AS "deptName",
        SUM(CASE WHEN C.CURRENT_ACTIVITY IN('Manual1', 'Manual2') THEN 1 ELSE 0 END) AS "fmrwpg",
        SUM(CASE WHEN R.MODULE_CODE = 'kwzl_hpg' AND R.IS_END = '0' THEN 1 ELSE 0 END) AS "zjps",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual3' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "bmgly",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual4' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "bmld",
        SUM(CASE WHEN A.EXTRA4 = '1' THEN 1 ELSE 0 END) AS "bmfq",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual5' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "dwgly",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual6' THEN 1 ELSE 0 END) AS "dwld",
        SUM(CASE WHEN A.EXTRA5 = '1' THEN 1 ELSE 0 END) AS "dwfq",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual8' AND R.REVIEWREQ_ID IS NULL THEN 1 ELSE 0 END) AS "zgbmgly",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual9' THEN 1 ELSE 0 END) AS "zgbmzg",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual11' THEN 1 ELSE 0 END) AS "zgbmbz",
        SUM(CASE WHEN C.CURRENT_ACTIVITY = 'Manual12' THEN 1 ELSE 0 END) AS "zgbmld",
        SUM(CASE WHEN A.FLOW_STATUS = 'end' THEN 1 ELSE 0 END) AS "pgjs",
        SUM(CASE WHEN A.FLOW_STATUS != 'draft' THEN 1 ELSE 0 END) AS "zs",
        SUM(CASE WHEN A.FIN_ISFQ = '1' THEN 1 ELSE 0 END) AS "fq"
        FROM ${zzzcSchema}.T_KWZL_APPLY_HPGINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO B ON A.APPLY_ID = B.JWSQ_ID
        LEFT JOIN ${ggmkSchema}.T_MPWF_FLOW_INFO C ON A.RECORD_ID = C.BUSINESS_ID
        LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_REQ R ON A.RECORD_ID = R.BIZ_ID
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="year">A.YEAR = #year#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gldwCode">B.GLDW_CODE = #gldwCode#
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="unitCodePath">B.FIRST_DEPT_PATH LIKE '$unitCodePath$%'</isNotEmpty>
        </dynamic>
    </select>

    <select id="queryHpgZjpsyj" parameterClass="hashmap" resultClass="hashmap">
        SELECT CASE WHEN A.EXTRA5 = 'Manual3' THEN B.REVIEW_COMMENT ELSE '' END AS "bmZjps",
               CASE WHEN A.EXTRA5 = 'Manual6' THEN B.REVIEW_COMMENT ELSE '' END AS "sybZjps"
        FROM ${ggmkSchema}.T_MPPS_REVIEW_REQ A
                 LEFT JOIN ${ggmkSchema}.T_MPPS_REVIEW_RESULT B ON A.REVIEWREQ_ID = B.REVIEWREQ_ID
        WHERE A.BIZ_ID = #recordId#
        ORDER BY A.CREATE_DATE LIMIT 1
    </select>

    <select id="queryHpgJGCount" parameterClass="hashmap" resultClass="hashmap">
        SELECT A.ASSIGNEE_ID AS "userCode",
        MAX(A.ASSIGNEE_FULLNAME) AS "userName",
        SUM(CASE WHEN B.FIN_ISFQ = '0' THEN 1 ELSE 0 END) AS "ps",
        SUM(CASE WHEN B.FIN_ISFQ = '1' THEN 1 ELSE 0 END) AS "fq"
        FROM ${ggmkSchema}.V_MPWF_YB A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_HPGINFO B ON A.BUSINESS_ID = B.RECORD_ID
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO C ON B.APPLY_ID = C.JWSQ_ID
        WHERE A.BUSINESS_TYPE = 'KWZL'
        AND A.FLOW_CODE = 'kwzl_Hpg'
        AND B.FLOW_STATUS = 'end'
        <isNotEmpty prepend=" AND " property="deptCode">C.FIRST_DEPT_PATH LIKE '%$deptCode$%'</isNotEmpty>
        <isNotEmpty prepend=" AND " property="startDate">B.YEAR <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">B.YEAR <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
        GROUP BY A.ASSIGNEE_ID
    </select>

    <select id="queryHpgJGList" parameterClass="hashmap" resultClass="hashmap">
        SELECT DISTINCT
        C.JWSQ_ID AS "jwsqId",
        C.IN_BGBH AS "bgbh",
        C.IN_BGBH AS "applyName",
        C.SBBM_NAME AS "firstDeptName",
        A.LAST_TIME AS "psDate",
        CASE WHEN B.FIN_ISFQ = '1' THEN '建议放弃' ELSE '不放弃' END AS "comment"
        FROM
        ${ggmkSchema}.V_MPWF_YB A
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_HPGINFO B ON A.BUSINESS_ID = B.RECORD_ID
        LEFT JOIN ${zzzcSchema}.T_KWZL_APPLY_BASEINFO C ON B.APPLY_ID = C.JWSQ_ID
        WHERE
        A.BUSINESS_TYPE = 'KWZL'
        AND A.FLOW_CODE = 'kwzl_Hpg'
        AND B.FLOW_STATUS = 'end'
        <isNotEmpty prepend=" AND " property="userCode">A.ASSIGNEE_ID = #userCode#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="isFq">B.ISFQ = #isFq#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="startDate">B.YEAR <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="endDate">B.YEAR <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
    </select>

    <select id="quertJfGwSqhPage" parameterClass="hashmap" resultClass="com.baosight.bscdkj.common.kw.domain.TkwzlMoneyPainfo">
        SELECT TMP.gwSqh AS "gwSqh" ,TMP.inZlmc AS "inZlmc" ,TMP.payNum AS "payNum" ,TMP.payMoney AS "payMoney"
        ,TMP.payDate AS "payDate" ,TMP.swsId AS "swsId",EXTRA1 AS "extra1"  ,TMP.paType AS "paType",TMP.gjjdId AS "gjjdId"
        FROM (SELECT
        EXTRA1 AS extra1  ,
        MAX(GW_SQH) AS gwSqh,
        MAX(GJJD_ID) AS gjjdId,
        MAX(PA_TYPE) AS paType,
        MAX(in_zlmc) AS inZlmc ,
        MAX(pay_num) AS payNum ,
        MAX(pay_money) AS payMoney ,
        MAX(pay_date) AS payDate ,
        MAX(sws_id) AS swsId
        FROM
        ${zzzcSchema}.t_kwzl_money_painfo
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="pamemoId">pamemo_id = #pamemoId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="paType">pa_type = #paType#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gjjdId">gjjd_id = #gjjdId#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="gwSqh">gw_sqh LIKE '%$gwSqh$%'</isNotEmpty>
            <isNotEmpty prepend=" AND " property="inZlmc">in_zlmc = #inZlmc#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="payNum">pay_num = #payNum#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="payMoney">pay_money = #payMoney#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="payDate">pay_date = #payDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="swsId">sws_id = #swsId#</isNotEmpty>
        </dynamic>
        GROUP BY EXTRA1
        ) TMP
        ORDER BY TMP.gwSqh DESC
    </select>

    <select id="getJpgXmInfo" parameterClass="hashmap" resultClass="hashmap">
        SELECT
               RECORD_GUID AS "recordGuid",
               SUBSTRING(PROJECT_CODE,2,2) AS "projectCode",
               PROJECT_NAME AS "projectName",
               EXTRA9 AS "jpgGw",
               MAIN_PROJECT_GUID AS "mainProjectGuid"
        FROM KJGL.T_KYXM_PROJECT
        <dynamic prepend="WHERE">
            <isNotEmpty prepend=" AND " property="recordGuid">RECORD_GUID = #recordGuid#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="projectCode">PROJECT_CODE = #projectCode#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="isZx">MAIN_PROJECT_GUID IS NOT NULL </isNotEmpty>
            <isNotEmpty prepend=" AND " property="isNotZx">MAIN_PROJECT_GUID IS NULL </isNotEmpty>
        </dynamic>
    </select>

    <select id="queryAUSqtjCount" parameterClass="hashmap" resultClass="hashmap">
        SELECT COUNT(JWSQ_ID) AS "sqs"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO
        WHERE SUBSTRING(GW_SQH ,5,2) = 'AU'
        <isNotEmpty prepend=" AND " property="jrStartDate">JRGJCSRQ <![CDATA[ >= ]]>  #jrStartDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="jrEndDate">JRGJCSRQ <![CDATA[ <= ]]>  #jrEndDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="slStartDate">GJSQRQ <![CDATA[ >= ]]>  #slStartDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="slEndDate">GJSQRQ <![CDATA[ <= ]]>  #slEndDate#</isNotEmpty>
    </select>

    <select id="queryAUSqtjList" parameterClass="hashmap" resultClass="hashmap">
        SELECT A.GW_SQH AS "gwSqh",B.PA_NAME AS "inZlmc",A.GJSQRQ AS "gjsqrq",A.ALL_PERSON AS "allPerson"
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        LEFT JOIN ${zzzcSchema}.T_KWZL_DWHZ_BASEINFO B ON A.DWHZ_ID = B.DWHZ_ID
        WHERE SUBSTRING(A.GW_SQH ,5,2) = 'AU'
        <isNotEmpty prepend=" AND " property="jrStartDate">A.JRGJCSRQ <![CDATA[ >= ]]>  #jrStartDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="jrEndDate">A.JRGJCSRQ <![CDATA[ <= ]]>  #jrEndDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="slStartDate">A.GJSQRQ <![CDATA[ >= ]]>  #slStartDate#</isNotEmpty>
        <isNotEmpty prepend=" AND " property="slEndDate">A.GJSQRQ <![CDATA[ <= ]]>  #slEndDate#</isNotEmpty>
    </select>


    <select id="querySlApi" parameterClass="hashmap" resultClass="hashmap">
        select
            count(JWSQ_ID) as "count",
            SUM(CASE WHEN IN_ZLTYPE = 'FM' THEN 1 ELSE 0 END) AS "fmCount",
            SUM(CASE WHEN PCTSQH IS NOT NULL AND PCTSQH != '' THEN 1 ELSE 0 END) AS "pctCount"
        from ${zzzcSchema}.T_KWZL_APPLY_BASEINFO
        <dynamic prepend=" where ">
            <isEqual prepend=" and " property="rangeField" compareValue="slrq">
                GJSQRQ between #startDate# and #endDate#
            </isEqual>
            <isEqual prepend=" and " property="rangeField" compareValue="gjrq">
                JRGJCSRQ between #startDate# and #endDate#
            </isEqual>
            <isNotEmpty prepend=" and " property="gldwCode">
                FIRST_DEPT_PATH LIKE '%$gldwCode$%'
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="querySqApi" parameterClass="hashmap" resultClass="hashmap">
        select
        count(A.JWSQ_ID) as "count",
        SUM(CASE WHEN IN_ZLTYPE = 'FM' THEN 1 ELSE 0 END) AS "fmCount"
        from ${zzzcSchema}.T_KWZL_APPLY_BASEINFO A
        LEFT JOIN zzzc.T_KWZL_GJJD_BASEINFO B ON A.JWSQ_ID = B.JWSQ_ID
        <dynamic prepend=" where ">
            <isEqual prepend=" and " property="rangeField" compareValue="sqrq">
                B.STATE_SQRQ between #startDate# and #endDate#
            </isEqual>
            <isNotEmpty prepend=" and " property="isValid">
                A.ISVALID = '1' AND B.ISVALID = '1'
            </isNotEmpty>
            <isNotEmpty prepend=" and " property="gldwCode">
                FIRST_DEPT_PATH LIKE '%$gldwCode$%'
            </isNotEmpty>
        </dynamic>
    </select>

    <select id="queryKwzlByFromNo" parameterClass="hashmap" resultClass="hashmap">
        SELECT
        JWSQ_ID as "jwsqId" , <!-- 主键 -->
        DWHZ_ID as "dwhzId" , <!-- 对外合作专利主键 -->
        IN_BGBH as "inBgbh" , <!-- 国内清能编号 -->
        IN_SQH as "inSqh" , <!-- 国内申请号 -->
        IN_ZLMC as "inZlmc" , <!-- 国内专利名称 -->
        IN_SLRQ as "inSlrq" , <!-- 国内专利受理日期 -->
        IN_ZLTYPE as "inZltype" , <!-- 国内专利专利类型 01-发明 02-实用新型 03-外观设计 -->
        SBBM_CODE as "sbbmCode" , <!-- 申报部门 -->
        SBBM_NAME as "sbbmName" , <!-- 申报部门名称 -->
        FROM_TYPE as "fromType" , <!-- 来源类型 -->
        ISJPG as "isjpg" , <!-- 是否金苹果项目 -->
        JPG_TEAM as "jpgTeam" , <!-- 金苹果团队 -->
        PRIORITY_DATE as "priorityDate" , <!-- 优先权日 -->
        ALL_PERSON as "allPerson" , <!-- 发明人 -->
        BZR as "bzr" , <!-- 编制人 -->
        BZR_NAME as "bzrName" , <!-- 编制人姓名 -->
        START_REASON as "startReason" , <!-- 启动境外专利申请理由 -->
        FIRST_PERSON as "firstPerson" , <!-- 第一联系人 -->
        FIRST_PERSON_NAME as "firstPersonName" , <!-- 第一联系人姓名 -->
        FIRST_PHONE as "firstPhone" , <!-- 第一联系人电话 -->
        FIRST_MOBILE as "firstMobile" , <!-- 第一联系人手机 -->
        SECOND_PERSON as "secondPerson" , <!-- 第二联系人 -->
        SECOND_PERSON_NAME as "secondPersonName" , <!-- 第二联系人姓名 -->
        SECOND_PHONE as "secondPhone" , <!-- 第二联系人电话 -->
        SECOND_MOBILE as "secondMobile" , <!-- 第二联系人手机 -->
        SOLVE_PROBLEM as "solveProblem" , <!-- 该技术解决的问题 -->
        LEAD_WHERE as "leadWhere" , <!-- 技术领先在何处 -->
        FOREIGN_PROSPECT as "foreignProspect" , <!-- 该项技术国外市场前景 -->
        SQGJ_TCR as "sqgjTcr" , <!-- 建议申请国家 -->
        SQGJ_EXPERT as "sqgjExpert" , <!-- 专家建议申请国家 -->
        SQGJ_FINAL as "sqgjFinal" , <!-- 申请国家 -->
        ISQQSF as "isqqsf" , <!-- 是否属于全球首发 1-是 0-否 -->
        ZXH as "zxh" , <!-- 总序号 -->
        GW_SQH as "gwSqh" , <!-- 国际专利申请编号 -->
        SWS_GUID as "swsGuid" , <!-- 代理事务所 -->
        SWS_LXR as "swsLxr" , <!-- 事务所联系人 -->
        SWS_DLRXM as "swsDlrxm" , <!-- 代理人姓名 -->
        SWS_DLREMAIL as "swsDlremail" , <!-- 代理人邮箱 -->
        SWS_DLRPHONE as "swsDlrphone" , <!-- 代理人电话 -->
        GJSQRQ as "gjsqrq" , <!-- 国际申请日期 -->
        PCTSQH as "pctsqh" , <!-- PCT申请号 -->
        APPLY_REASON as "applyReason" , <!-- 申请理由 -->
        PA_STATUS as "paStatus" , <!-- 法律状态 -->
        ISZZSQ as "iszzsq" , <!-- 是否终止申请 1-是 0-否 -->
        ZZSQ_REASON as "zzsqReason" , <!-- 终止申请说明 -->
        FLOW_STATUS as "flowStatus" , <!-- draft-草稿 active-审批中 end-结束 -->
        ISVALID as "isvalid" , <!-- 是否有效 1-是 0-否 -->
        EXTRA1 as "extra1" , <!-- 境内专利applyId -->
        EXTRA2 as "extra2" , <!-- 是否进入欧洲专利局 0-否 1-是 -->
        EXTRA3 as "extra3" , <!-- 是否快速录入 0-否 1-是 -->
        EXTRA4 as "extra4" , <!-- 来源编号 -->
        EXTRA5 as "extra5" , <!-- 专家评审意见 -->
        DEL_STATUS as "delStatus" , <!-- 删除状态 -->
        CREATE_USER_LABEL as "createUserLabel" , <!-- 创建人 -->
        CREATE_DATE as "createDate" , <!-- 创建时间 -->
        UPDATE_USER_LABEL as "updateUserLabel" , <!-- 更新人 -->
        UPDATE_DATE as "updateDate" , <!-- 更新时间 -->
        DELETE_USER_LABEL as "deleteUserLabel" , <!-- 删除人 -->
        DELETE_DATE as "deleteDate" , <!-- 删除时间 -->
        RECORD_VERSION as "recordVersion" , <!-- 版本号 -->
        SWS_NO as "swsNo" , <!-- 代理事务所账号 -->
        DLR_NO as "dlrNo" , <!-- 代理人账号 -->
        IS_GJCS as "isGjcs" , <!-- 是否要求国际初审 -->
        SQR as "sqr" , <!-- 申请人 -->
        JPG_XH as "jpgXh" , <!-- 金苹果轮次 -->
        JRGJCSRQ as "jrgjcsrq" , <!-- 进入国际阶段日期 -->
        JRGJJDRQ as "jrgjjdrq" , <!-- 进入国家阶段日期 -->
        NOGJJDRQ as "nogjjdrq" , <!-- 不进入国家阶段日期 -->
        FIRST_DEPT_PATH as "firstDeptPath" , <!-- 申报部门路径 -->
        NOGJJDSM as "nogjjdsm" , <!-- 不进入国家阶段说明 -->
        FMR_CODE as "fmrCode" , <!-- 发明人工号 -->
        GLDW_CODE as "gldwCode" , <!-- 管理单位Code -->
        GLDW_NAME as "gldwName" , <!-- 管理单位名称 -->
        SQH as "sqh" , <!-- 申请号 -->
        JPG_GW as "jpgGw"  <!-- 金苹果果王 -->
        FROM ${zzzcSchema}.T_KWZL_APPLY_BASEINFO
        <dynamic prepend=" where ">
            <isNotEmpty prepend=" AND " property="projectCodes">EXTRA4 IN
                <iterate property="projectCodes" conjunction="," open="(" close=")">
                    #projectCodes[]#
                </iterate>
            </isNotEmpty>
            <isNotEmpty prepend=" AND " property="year">YEAR(GJSQRQ) = #year#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="startDate">GJSQRQ <![CDATA[ >= ]]>  #startDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="endDate">GJSQRQ <![CDATA[ <= ]]>  #endDate#</isNotEmpty>
            <isNotEmpty prepend=" AND " property="dynSql">$dynSql$</isNotEmpty>
        </dynamic>
        <isNotEmpty prepend=" " property="displayOrder">ORDER BY $displayOrder$</isNotEmpty>
    </select>


</sqlMap>