<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE sqlMap  PUBLIC "-//ibatis.apache.org//DTD SQL Map 2.0//EN" "http://ibatis.apache.org/dtd/sql-map-2.dtd">
<sqlMap namespace="tkwhtPayApplication">
	<typeAlias alias="tkwhtPayApplicationResult" type="com.baosight.bscdkj.common.kw.domain.TkwhtPayApplication"/>
	<select id="load" parameterClass="string" resultClass="tkwhtPayApplicationResult">
		SELECT
		fksq_id as "fksqId" ,
		dwhz_id as "dwhzId" ,
		contract_id as "contractId" ,
		charge_num as "chargeNum" ,
		label_name as "labelName" ,
		isend as "isend" ,
		isapplyover as "isapplyover" ,
		content_over as "contentOver" ,
		apply_je as "applyJe" ,
		tax_rate as "taxRate" ,
		tax_amount as "taxAmount" ,
		apply_person as "applyPerson" ,
		apply_date as "applyDate" ,
		dept_sk as "deptSk" ,
		duty_paragraph as "dutyParagraph" ,
		extra1 as "extra1" ,
		extra2 as "extra2" ,
		extra3 as "extra3" ,
		extra4 as "extra4" ,
		extra5 as "extra5" ,
		del_status as "delStatus" ,
		create_user_label as "createUserLabel" ,
		create_date as "createDate" ,
		update_user_label as "updateUserLabel" ,
		update_date as "updateDate" ,
		delete_user_label as "deleteUserLabel" ,
		delete_date as "deleteDate" ,
		record_version as "recordVersion" ,
		pay_no as "payNo" ,
		bz_no as "bzNo" ,
		cw_status as "cwStatus" ,
		shdate as "shdate" ,
		fkdate as "fkdate" ,
		jj_no as "jjNo"
		FROM ${zzzcSchema}.t_kwht_pay_application
		WHERE
		fksq_id = #fksqId#

	</select>

	<select id="query"  parameterClass="hashmap" resultClass="tkwhtPayApplicationResult">
		SELECT
		fksq_id as "fksqId" ,
		dwhz_id as "dwhzId" ,
		contract_id as "contractId" ,
		charge_num as "chargeNum" ,
		label_name as "labelName" ,
		isend as "isend" ,
		isapplyover as "isapplyover" ,
		content_over as "contentOver" ,
		apply_je as "applyJe" ,
		tax_rate as "taxRate" ,
		tax_amount as "taxAmount" ,
		apply_person as "applyPerson" ,
		apply_date as "applyDate" ,
		dept_sk as "deptSk" ,
		duty_paragraph as "dutyParagraph" ,
		extra1 as "extra1" ,
		extra2 as "extra2" ,
		extra3 as "extra3" ,
		extra4 as "extra4" ,
		extra5 as "extra5" ,
		del_status as "delStatus" ,
		create_user_label as "createUserLabel" ,
		create_date as "createDate" ,
		update_user_label as "updateUserLabel" ,
		update_date as "updateDate" ,
		delete_user_label as "deleteUserLabel" ,
		delete_date as "deleteDate" ,
		record_version as "recordVersion" ,
		pay_no as "payNo" ,
		bz_no as "bzNo" ,
		cw_status as "cwStatus" ,
		shdate as "shdate" ,
		fkdate as "fkdate" ,
		jj_no as "jjNo"
		FROM ${zzzcSchema}.t_kwht_pay_application
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="fksqId">fksq_id =  #fksqId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dwhzId">dwhz_id =  #dwhzId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contractId">contract_id =  #contractId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="chargeNum">charge_num =  #chargeNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="labelName">label_name =  #labelName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isend">isend =  #isend#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isapplyover">isapplyover =  #isapplyover#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contentOver">content_over =  #contentOver#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyJe">apply_je =  #applyJe#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="taxRate">tax_rate =  #taxRate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="taxAmount">tax_amount =  #taxAmount#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyPerson">apply_person =  #applyPerson#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyDate">apply_date =  #applyDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptSk">dept_sk =  #deptSk#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dutyParagraph">duty_paragraph =  #dutyParagraph#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="payNo">pay_no =  #payNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="bzNo">bz_no =  #bzNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cwStatus">cw_status =  #cwStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="shdate">shdate =  #shdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fkdate">fkdate =  #fkdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jjNo">jj_no =  #jjNo#</isNotEmpty>

			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
		<isNotEmpty prepend=" " property="displayOrder"> ORDER BY $displayOrder$ </isNotEmpty>
	</select>

	<select id="count"  parameterClass="hashmap" resultClass="integer">
		SELECT count(*)
		FROM ${zzzcSchema}.t_kwht_pay_application
		<dynamic prepend="WHERE">
			<isNotEmpty prepend=" AND " property="fksqId">fksq_id =  #fksqId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dwhzId">dwhz_id =  #dwhzId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contractId">contract_id =  #contractId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="chargeNum">charge_num =  #chargeNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="labelName">label_name =  #labelName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isend">isend =  #isend#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isapplyover">isapplyover =  #isapplyover#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contentOver">content_over =  #contentOver#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyJe">apply_je =  #applyJe#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="taxRate">tax_rate =  #taxRate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="taxAmount">tax_amount =  #taxAmount#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyPerson">apply_person =  #applyPerson#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyDate">apply_date =  #applyDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptSk">dept_sk =  #deptSk#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dutyParagraph">duty_paragraph =  #dutyParagraph#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">extra1 =  #extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">extra2 =  #extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">extra3 =  #extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">extra4 =  #extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">extra5 =  #extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">del_status =  #delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label =  #createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">create_date =  #createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label =  #updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">update_date =  #updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label =  #deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">delete_date =  #deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">record_version =  #recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="payNo">pay_no =  #payNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="bzNo">bz_no =  #bzNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cwStatus">cw_status =  #cwStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="shdate">shdate =  #shdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fkdate">fkdate =  #fkdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jjNo">jj_no =  #jjNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dynSql"> $dynSql$ </isNotEmpty>
		</dynamic>
	</select>

	<insert id="insert" parameterClass="tkwhtPayApplicationResult">
		INSERT INTO ${zzzcSchema}.t_kwht_pay_application (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="fksqId">fksq_id</isNotEmpty>
			<isNotEmpty prepend=" , " property="dwhzId">dwhz_id</isNotEmpty>
			<isNotEmpty prepend=" , " property="contractId">contract_id</isNotEmpty>
			<isNotEmpty prepend=" , " property="chargeNum">charge_num</isNotEmpty>
			<isNotEmpty prepend=" , " property="labelName">label_name</isNotEmpty>
			<isNotEmpty prepend=" , " property="isend">isend</isNotEmpty>
			<isNotEmpty prepend=" , " property="isapplyover">isapplyover</isNotEmpty>
			<isNotEmpty prepend=" , " property="contentOver">content_over</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyJe">apply_je</isNotEmpty>
			<isNotEmpty prepend=" , " property="taxRate">tax_rate</isNotEmpty>
			<isNotEmpty prepend=" , " property="taxAmount">tax_amount</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyPerson">apply_person</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyDate">apply_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptSk">dept_sk</isNotEmpty>
			<isNotEmpty prepend=" , " property="dutyParagraph">duty_paragraph</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">extra1</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">extra2</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">extra3</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">extra4</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">extra5</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">del_status</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">create_user_label</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">create_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">update_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">delete_date</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">record_version</isNotEmpty>
			<isNotEmpty prepend=" , " property="payNo">pay_no</isNotEmpty>
			<isNotEmpty prepend=" , " property="bzNo">bz_no</isNotEmpty>
			<isNotEmpty prepend=" , " property="cwStatus">cw_status</isNotEmpty>
			<isNotEmpty prepend=" , " property="shdate">shdate</isNotEmpty>
			<isNotEmpty prepend=" , " property="fkdate">fkdate</isNotEmpty>
			<isNotEmpty prepend=" , " property="jjNo">jj_no</isNotEmpty>
		</dynamic>
		) VALUES (
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="fksqId">#fksqId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="dwhzId">#dwhzId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contractId">#contractId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="chargeNum">#chargeNum#</isNotEmpty>
			<isNotEmpty prepend=" , " property="labelName">#labelName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="isend">#isend#</isNotEmpty>
			<isNotEmpty prepend=" , " property="isapplyover">#isapplyover#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contentOver">#contentOver#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyJe">#applyJe#</isNotEmpty>
			<isNotEmpty prepend=" , " property="taxRate">#taxRate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="taxAmount">#taxAmount#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyPerson">#applyPerson#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyDate">#applyDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptSk">#deptSk#</isNotEmpty>
			<isNotEmpty prepend=" , " property="dutyParagraph">#dutyParagraph#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="payNo">#payNo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="bzNo">#bzNo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="cwStatus">#cwStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="shdate">#shdate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fkdate">#fkdate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="jjNo">#jjNo#</isNotEmpty>
		</dynamic>)
	</insert>

	<delete id="delete" parameterClass="string">
		DELETE FROM  ${zzzcSchema}.t_kwht_pay_application
		WHERE
		fksq_id = #value#

	</delete>

	<delete id="deleteByC" parameterClass="hashmap">
		DELETE FROM  ${zzzcSchema}.t_kwht_pay_application
		WHERE
		<dynamic prepend=" ">
			<isNotEmpty prepend=" AND " property="fksqId">fksq_id=#fksqId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dwhzId">dwhz_id=#dwhzId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contractId">contract_id=#contractId#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="chargeNum">charge_num=#chargeNum#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="labelName">label_name=#labelName#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isend">isend=#isend#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="isapplyover">isapplyover=#isapplyover#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="contentOver">content_over=#contentOver#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyJe">apply_je=#applyJe#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="taxRate">tax_rate=#taxRate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="taxAmount">tax_amount=#taxAmount#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyPerson">apply_person=#applyPerson#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="applyDate">apply_date=#applyDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deptSk">dept_sk=#deptSk#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="dutyParagraph">duty_paragraph=#dutyParagraph#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra1">extra1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra2">extra2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra3">extra3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra4">extra4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="extra5">extra5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="delStatus">del_status=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="createDate">create_date=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="updateDate">update_date=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="payNo">pay_no=#payNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="bzNo">bz_no=#bzNo#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="cwStatus">cw_status=#cwStatus#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="shdate">shdate=#shdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="fkdate">fkdate=#fkdate#</isNotEmpty>
			<isNotEmpty prepend=" AND " property="jjNo">jj_no=#jjNo#</isNotEmpty>
		</dynamic>
	</delete>

	<update id="update" parameterClass="tkwhtPayApplicationResult">
		UPDATE  ${zzzcSchema}.t_kwht_pay_application
		SET
		<dynamic prepend=" ">
			<isNotEmpty prepend=" , " property="fksqId">fksq_id=#fksqId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="dwhzId">dwhz_id=#dwhzId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contractId">contract_id=#contractId#</isNotEmpty>
			<isNotEmpty prepend=" , " property="chargeNum">charge_num=#chargeNum#</isNotEmpty>
			<isNotEmpty prepend=" , " property="labelName">label_name=#labelName#</isNotEmpty>
			<isNotEmpty prepend=" , " property="isend">isend=#isend#</isNotEmpty>
			<isNotEmpty prepend=" , " property="isapplyover">isapplyover=#isapplyover#</isNotEmpty>
			<isNotEmpty prepend=" , " property="contentOver">content_over=#contentOver#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyJe">apply_je=#applyJe#</isNotEmpty>
			<isNotEmpty prepend=" , " property="taxRate">tax_rate=#taxRate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="taxAmount">tax_amount=#taxAmount#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyPerson">apply_person=#applyPerson#</isNotEmpty>
			<isNotEmpty prepend=" , " property="applyDate">apply_date=#applyDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deptSk">dept_sk=#deptSk#</isNotEmpty>
			<isNotEmpty prepend=" , " property="dutyParagraph">duty_paragraph=#dutyParagraph#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra1">extra1=#extra1#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra2">extra2=#extra2#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra3">extra3=#extra3#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra4">extra4=#extra4#</isNotEmpty>
			<isNotEmpty prepend=" , " property="extra5">extra5=#extra5#</isNotEmpty>
			<isNotEmpty prepend=" , " property="delStatus">del_status=#delStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createUserLabel">create_user_label=#createUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="createDate">create_date=#createDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateUserLabel">update_user_label=#updateUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="updateDate">update_date=#updateDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteUserLabel">delete_user_label=#deleteUserLabel#</isNotEmpty>
			<isNotEmpty prepend=" , " property="deleteDate">delete_date=#deleteDate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="recordVersion">record_version=#recordVersion#</isNotEmpty>
			<isNotEmpty prepend=" , " property="payNo">pay_no=#payNo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="bzNo">bz_no=#bzNo#</isNotEmpty>
			<isNotEmpty prepend=" , " property="cwStatus">cw_status=#cwStatus#</isNotEmpty>
			<isNotEmpty prepend=" , " property="shdate">shdate=#shdate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="fkdate">fkdate=#fkdate#</isNotEmpty>
			<isNotEmpty prepend=" , " property="jjNo">jj_no=#jjNo#</isNotEmpty>
		</dynamic>
		WHERE
		fksq_id =#fksqId#
	</update>



</sqlMap>