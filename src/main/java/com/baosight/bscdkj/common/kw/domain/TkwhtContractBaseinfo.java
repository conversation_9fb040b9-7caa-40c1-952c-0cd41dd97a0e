package com.baosight.bscdkj.common.kw.domain;


import lombok.Getter;
import lombok.Setter;
import lombok.ToString;

import com.baosight.bscdkj.common.domain.AbstractDomain;
import javax.validation.constraints.Size;

import java.math.BigDecimal;

/**
 * 对外合作_申请_合同信息对象 t_kwht_contract_baseinfo
 * 
 * <AUTHOR>
 * @date 2022-02-23
 */
@Getter
@Setter
@ToString
public class TkwhtContractBaseinfo extends AbstractDomain{
    private static final long serialVersionUID = 1L;

    /** 主键 */
    private String contractId;

    /** 申请主键 */
    @Size(max = 36,message = "申请主键最大为36位字符")
    private String dwhzId;

    /** 合同名称 */
    @Size(max = 100,message = "合同名称最大为100位字符")
    private String contractName;

    /** 合同编号 */
    @Size(max = 20,message = "合同编号最大为20位字符")
    private String contractNum;

    /** 合同开始时间 */
    @Size(max = 10,message = "合同开始时间最大为10位字符")
    private String contractStart;

    /** 合同结束时间 */
    @Size(max = 10,message = "合同结束时间最大为10位字符")
    private String contractEnd;

    /** 合同类型 */
    @Size(max = 2,message = "合同类型最大为2位字符")
    private String contractType;

    /** 合同金额 */
    private BigDecimal contractJe;

    /** 签订日期 */
    @Size(max = 10,message = "签订日期最大为10位字符")
    private String signDate;

    /** 开口合同付款申请主键 */
    @Size(max = 30,message = "扩展字段1最大为30位字符")
    private String extra1;

    /** 扩展字段2 */
    @Size(max = 30,message = "扩展字段2最大为30位字符")
    private String extra2;

    /** 扩展字段3 */
    @Size(max = 30,message = "扩展字段3最大为30位字符")
    private String extra3;

    /** 扩展字段4 */
    @Size(max = 30,message = "扩展字段4最大为30位字符")
    private String extra4;

    /** 扩展字段5 */
    @Size(max = 30,message = "扩展字段5最大为30位字符")
    private String extra5;


    /** 合同备案类型 */
    private String contractRecordType;

    /** 是否期初切换 */
    private String ifBeginningSwitch;

    /** 备案公司代码 */
    private String companyCode;

    /** 签订部门代码 */
    private String department;

    /** 签订部门名称 */
    private String departmentCname;

    /** 签约客商代码 */
    private String purUserCode;

    /** 签订人工号 */
    private String purUserName;

    /** 签订人工号 */
    private String signUserId;

    /** 签订人名称 */
    private String signUserName;

    /** 合同起草人工号 */
    private String billPeople;

    /** 合同起草人名称 */
    private String billPeopleName;

    /** 合同末级审核人工号 */
    private String lastCheckPeople;

    /** 合同末级审核人姓名 */
    private String lastCheckPeopleName;

    /** 合同标的 */
    private String contractSubjectMatter;

    /** 合同大类 */
    private String bpoTypeS;

    /** 合同细类 */
    private String bpoTypeT;

    /** 是否法审格式合同 */
    private String ifLegalCheckFormat;

    /** 是否经过法审 */
    private String ifLegalCheck;

    /** 是否多次结算 */
    private String ifPayMultiple;

    /** 是否招投标 */
    private String ifBidding;

    /** 是否阳光采购 */
    private String ifSunPurchase;

    /** 是否集团外贸易 */
    private String ifForeignTrade;

    /** 合同状态 */
    private String contractStatus;

    /** 结束理由 */
    private String zzReason;

}
