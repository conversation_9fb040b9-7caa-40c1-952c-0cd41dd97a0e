package com.baosight.bscdkj.ky.mm.mmutlis;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.alibaba.fastjson.JSONObject;
import com.baosight.bscdkj.common.domain.AjaxResult;
import com.baosight.bscdkj.common.exception.BusinessException;
import com.baosight.bscdkj.common.ki.domain.*;
import com.baosight.bscdkj.common.kt.domain.TktmyProject;
import com.baosight.bscdkj.common.ky.domain.*;
import com.baosight.bscdkj.common.mp.domain.TmppsReviewMemo;
import com.baosight.bscdkj.ki.zl.business.*;
import com.baosight.bscdkj.ki.zl.utils.PatentUtil;
import com.baosight.bscdkj.ky.mm.business.*;
import com.baosight.bscdkj.ky.mm.common.JMConstans;
import com.baosight.bscdkj.ky.mm.common.MMConstans;
import com.baosight.bscdkj.ky.mm.domain.TkymmTechImplEx;
import com.baosight.bscdkj.ky.mm.domain.TkymmTechnologyEx;
import com.baosight.bscdkj.ky.mm.enums.EnumKYMMAccessType;
import com.baosight.bscdkj.mp.ad.dto.ADOrg;
import com.baosight.bscdkj.mp.ad.utils.OrgUtil;
import com.baosight.bscdkj.mp.ad.utils.RoleUtil;
import com.baosight.bscdkj.mp.ad.utils.UserUtil;
import com.baosight.bscdkj.mp.ps.business.BusinessReviewMemoPatent;
import com.baosight.bscdkj.mp.ty.utils.ClobUtil;
import com.baosight.bscdkj.mp.ty.utils.SAttachmentUtil;
import com.baosight.bscdkj.mp.ty.utils.SDictUtil;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.bscdkj.utils.LevelAwardUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XLocalManager;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;

/****
 * 技术秘密通用工具类
 */
public class CommonUtli {

    private static BusinessTechnology businessTechnology = SpringUtil.getBean ( BusinessTechnology.class );
    private static BusinessPatentInfoPatent businessPatentInfo = SpringUtil.getBean ( BusinessPatentInfoPatent.class );
    private static BusinessOrganization businessOrganization = SpringUtil.getBean ( BusinessOrganization.class );
    private static BusinessTechImpl businessTechImpl  = SpringUtil.getBean(BusinessTechImpl.class);
    private static BusinessKYMMTechAccess businessKYMMTechAccess  = SpringUtil.getBean(BusinessKYMMTechAccess.class);

    private static BusinessTechMember businessTechMember = SpringUtil.getBean(BusinessTechMember.class);

    private static BusinessMaintainTechareaPatent businessMaintainTecharea = SpringUtil.getBean(BusinessMaintainTechareaPatent.class);

    private static BusinessPsflogPatent businessPsf = SpringUtil.getBean(BusinessPsflogPatent.class);
    private static BusinessReviewMemoPatent businessReviewMemo = SpringUtil.getBean(BusinessReviewMemoPatent.class);
    private static BusinessApplyBaseinfoPatent businessApplyBaseinfoPatent = SpringUtil.getBean(BusinessApplyBaseinfoPatent.class);

    //产权室人员配置
    private static BusinessMaintainCqsryPatent businessMaintainCqsryPatent = SpringUtil.getBean(BusinessMaintainCqsryPatent.class);
    /****
     * 结束流程
     * @param params
     */
    public static void endProcess(Map<String, Object> params) {
        String loginName = UserSession.getLoginName ();
        //移动端
        if (StringUtils.isBlank(loginName)) {
            loginName = (String) params.get("operator");
        }
        String businessId = (String) params.get ( "businessId" );
        String approvecomment = (String) params.get ( "approvecomment" );
        
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(loginName);
        workFlow.setBusinessId(businessId);
        workFlow.setComment(approvecomment);
        SWorkFlowUtil.endProecess(loginName, workFlow);
        //SWorkFlowUtil.endProecess ( loginName, businessId, approvecomment );
    }

    /****
     * 认定 结束流程
     * @param params
     */
    public static String endProcessForRd(Map<String, Object> params) {
        String loginName = "";
        if (StringUtils.isBlank((String) params.get("operator"))) {
            loginName = UserSession.getLoginName ();
        }else {
            loginName = (String) params.get("operator");
        }

        String taskId = (String) params.get("taskId");

        //添加认定状态
        TkymmTechnology tkymmTechnologyRd = new TkymmTechnology ();
        tkymmTechnologyRd.setTechnologyId ( (String) params.get ( "businessId" ) );
        tkymmTechnologyRd.setStatus ( MMConstans.aprove_status_identified );
        //认定号生成
        String confirmNum = (String)params.get("confirmNum");
        if (StringUtils.isBlank(confirmNum)) {
//            tkymmTechnologyRd.setConfirmNum ( SequenceGenerator.getNextSequence ( MMConstans.KYMM_RD_SERIALNO ) );
            tkymmTechnologyRd.setConfirmNum (RDMMutli.getConfirmNum((String) params.get("businessId")));
        }else{
            tkymmTechnologyRd.setConfirmNum (confirmNum);
        }
        //认定时间取专家评审同意的最后一个

        tkymmTechnologyRd.setConfirmTime(DateUtil.formatDate(new Date()));
        TkymmExpertReview tkymmExpertReview = RDMMutli.getFistExpert ( (String) params.get ( "businessId" ), MMConstans.aprove_status_agree );
        Optional.ofNullable (tkymmExpertReview ).ifPresent ( d->{
            tkymmTechnologyRd.setConfirmTime ( d.getIdentificationTime ());
        } );

        TkymmTechnology tkymmTechnology = businessTechnology.query((String) params.get("businessId"));
        String gldw = tkymmTechnology.getExtra5();
        //四大基地认定时间为领导审批时间
        if ("BGTM00".equals(gldw) || "BGBW".equals(gldw) || "BSTM".equals(gldw) || "BSZG".equals(gldw)) {
            tkymmTechnologyRd.setConfirmTime(DateUtil.formatDate(new Date()));
        }
        //判断是否重复操作
        if (MMConstans.aprove_status_identified.equals(tkymmTechnology.getStatus())) {
            throw new PlatException("业务已认定,请勿重复操作");
        }
        //update操作放发奖之前，避免误操作导致重复发奖
        //业务状态更新
        businessTechnology.update ( loginName, tkymmTechnologyRd );
        /**
         * 认定后发起认定奖
         *     武钢有限  不进行发奖
         */
        if (!"BGBW".equals(tkymmTechnology.getExtra5())) {
            EiInfo eiInfo = new EiInfo();
            eiInfo.set(EiConstant.serviceName, "KYMMJiangli");
            eiInfo.set(EiConstant.methodName, "rdjJlfp");
            eiInfo.set("operator", loginName);
            eiInfo.set("technologyId", tkymmTechnologyRd.getTechnologyId());
            EiInfo out = XLocalManager.call(eiInfo);
            if(out.getStatus()<0) {
                throw new PlatException(out.getMsg());
            }
        }

        //认定结束
        WorkFlow workFlow = SWorkFlowUtil.getWorkFlowByTaskId(taskId);
        if(null!=workFlow) {
            workFlow.setComment("认定");
//            return SWorkFlowUtil.endProecess ( loginName, workFlow );
            return SWorkFlowUtil.finshProcess ( loginName, workFlow );
        }
        return null;
    }

    /***
     * 根据来源类型与编号获取来源信息
     * @param sourceType
     * @param sourceId
     * @return
     */
    public static Map<String, Object> getSource(String sourceType, String sourceId) {
        Map<String, Object> result = new HashMap<> ();
        //同来源专利
        String  samesourcePatent="";
        //来源名称
        String  sourceName="";
        //同来源技术秘密
        String  samesourceTech="";
        switch (sourceType) {
            case "01":
                Map<String, Object> projectMap = getServiceDataXm(sourceId);

                //项目名称
                if (ObjectUtil.isNotEmpty(projectMap)){
                    sourceName = (String)projectMap.get("projectName");
                }

                result.put ( "sourceName", sourceName );

                break;
//            case "KYZL":
//                List<Map<String, Object>> zlList = getServiceDataZl("fromNo", sourceId);
//
//                if (ObjectUtil.isNotEmpty(zlList)){
//                	samesourcePatent = zlList.stream().map(m -> (String) m.get("applyName")).collect(Collectors.joining(","));
//                }
//                result.put("sourceName",samesourcePatent);
//                break;

            case "03":
                Map<String, Object> serviceDataJSTG = getServiceDataJSTG(sourceId);
                if (ObjectUtil.isNotEmpty(serviceDataJSTG)){
                    sourceName=(String) serviceDataJSTG.get("projectName");
                }
                result.put("sourceName",sourceName);
                break;
//            case "KYMM":
//                TkymmTechnology tkymmTechnology = businessTechnology.queryByConfirmNum(sourceId);
//                if (ObjectUtil.isNotEmpty(tkymmTechnology)){
//                    sourceName=tkymmTechnology.getTechnologyName();
//                }
//                result.put("sourceName",sourceName);
//                break;
            case "02" :
                TktmyProject serviceDataJSMY = (TktmyProject) getServiceDataJSMY(sourceId);
                if (ObjectUtil.isNotEmpty(serviceDataJSMY)){
                    sourceName= serviceDataJSMY.getProjectName();
                }
                result.put("sourceName",sourceName);
                break;
            case "04":
                String serviceDataHLHJY = getServiceDataHLHJY(sourceId);
                if (StringUtils.isNotBlank(serviceDataHLHJY)){
                    sourceName = serviceDataHLHJY;
                }
                result.put("sourceName",sourceName);
                break;
            case "05":
                String serviceDataSIX = getServiceDataSIX(sourceId);
                if (StringUtils.isNotBlank(serviceDataSIX)){
                    sourceName = serviceDataSIX;
                }
                result.put("sourceName",sourceName);
                break;
            case "06":
                result.put("sourceName", "");
                break;
            case "07":
                result.put("sourceName","");
                break;
        }

        if(ObjectUtil.isEmpty(result.get("sourceName"))){
            result.put("sourceName","");
        }
        //同来源专利
//        List<Map<String, Object>> zlList = getServiceDataZl("fromNo", sourceId);
        List<Map<String, Object>> zlList = getZLBySourceNo(sourceId,sourceType);
        if (ObjectUtil.isNotEmpty(zlList)){
            samesourcePatent = zlList.stream().map(m -> (String) m.get("applyName")).collect(Collectors.joining(";"));
        }
        result.put ( "samesourcePatent",samesourcePatent );
        //添加专利最新状态
        for (Map<String, Object> zlMap : zlList) {
            zlMap.put("lastStatus",SDictUtil.getDictName("KIZL", "KI_FLZT", (String) zlMap.get("flzt")));
        }
        result.put ( "samesourcePatentList",zlList );
        //同来源秘密
        samesourceTech = getMMNameBySourceNo(sourceId);
        result.put ( "samesourceTech",samesourceTech);
        List<Map<String, Object>> mmList = getMMBySourceNo(sourceId);

        //添加秘密最新状态
        for (Map<String, Object> mmMap : mmList) {
            String status = (String) mmMap.get("status");
            switch (status) {
                case "agree":
                    mmMap.put("lastStatus", "同意");
                    break;
                case "noagree":
                    mmMap.put("lastStatus", "不同意");
                    break;
                case "negative":
                    mmMap.put("lastStatus", "否定");
                    break;
                case "identified":
                    mmMap.put("lastStatus", "认定");
                    break;
                default:
                    mmMap.put("lastStatus", "");
            }
        }
        result.put ( "samesourceTechList",mmList);

        return result;
    }

    /****
     * 通用流程提交下一步
     * @param param
     * @param operator
     * @return
     */
    public static String commonSubmit(Map<String, Object> param,String operator){
        if(param==null){
            throw  new BusinessException ("流程参数不能为空");
        }
        operator = Optional.ofNullable ( operator ).orElse ( UserSession.getLoginName () );

        //提交流程
        WorkFlow workFlow = new WorkFlow();
        workFlow.setOperator(operator);
        workFlow.setBusinessId ( (String) param.get ( "businessId" ) );
        workFlow.setTaskId ( (String) param.get ( "taskId" ) );
        workFlow.setComment ( (String) param.get ( "approvecomment" ) );
        workFlow.setTransitionKey ( (String) param.get ( "transitionKey" ) );
        if(null!=param.get("variable")) {
            Map variable = (Map)param.get("variable");
            workFlow.setVariable(variable);
        }
        
        
        //认定流程
        // 预审节点进入子流程
        if (param.get("processCode").equals(MMConstans.process_rd)
                && MMConstans.PRELIMINARY_EXAMINAT.equals(param.get("activityCode"))) {
            //判断是否为提交退回人
            if (!(((String) param.get("transitionKey")).indexOf("jumpReturnNode@@") == 0)) {
                //评审费插入
                CommonUtli.psfLog(operator,(String) param.get ( "businessId" ),MMConstans.Review_KYMM_RD
                        ,"1",(String) param.get ( "processCode"),(String) param.get ( "activityCode"));
                return RDMMutli.rdSubProcess((String) param.get ( "taskId" ) ,(String) param.get("businessId"));
            }
        }
        //实施流程到单位主管管理员前改变主管部门
        //改为非人工操作，移至子流程全部结束时候处理
//        if (!("".equals(param.get("variables"))) && param.get("processCode").equals(MMConstans.kymmreward) && param.get("activityCode").equals(MMConstans.Manual1)) {
//            workFlow.setVariable((Map<String, Object>) param.get("variables"));
//        }
        //实施奖流程特殊处理
        if (param.get("processCode").equals(MMConstans.kymmreward)) {

            //编辑节点根据是否为提交退回人，判断是否进入子流程（这里没用了，在实施控制器做了处理）
            if (MMConstans.KYMM_START.equals(param.get("activityCode")) &&
                    !(((String) param.get("transitionKey")).indexOf("jumpReturnNode@@") == 0)) {
                return ImplementationAwardUtli.subProcess((String) param.get("businessId"), (String) param.get("taskId"));
            }
            if("KYMM_ZGDEPT_ZG".equals(param.get("activityCode"))) {
                //主管部门打分
                TkymmTechAccess techAccess = BeanUtil.toBean(param, TkymmTechAccess.class);
                if(null!=techAccess&&StringUtils.isNotEmpty(techAccess.getSourceId())) {
                    businessKYMMTechAccess.saveByType(UserSession.getLoginName(), techAccess.getSourceId(), EnumKYMMAccessType.ZGBM_ZG, techAccess);
                }
            }
            //等待效益节点
            if (MMConstans.Manual1.equals(param.get("activityCode"))) {
                //非系统管理员不能进行提交
                if (!RoleUtil.isAdmin(UserSession.getLoginName())) {
                    throw new PlatException("该节点非人工操作,请联系系统管理员");
                }
            }
        }

        //判断是否为提交退回人
        if (MMConstans.kymmreward.equals(param.get("processCode")) && MMConstans.Project_Leader.equals(param.get("activityCode"))) {
            if (!(((String) param.get("transitionKey")).indexOf("jumpReturnNode@@") == 0)) {
                return ImplementationAwardUtli.subProcess((String) param.get("businessId"), (String) param.get("taskId"));
            }
        }

        if (MMConstans.kymmsondeptps.equals(param.get("processCode"))) {
            //分厂管理员
            if (MMConstans.KYMM_FC_ADMIN_QR.equals(param.get("activityCode"))) {
                if ("Transition9".equals(param.get("transitionKey"))) {
                    workFlow.setUserLabelM((String) param.get("applyUserGh"));
                }
            }
            //部门管理员节点
            if (MMConstans.KYMM_DEPT_ADMIN_QE.equals(param.get("activityCode"))) {
                if ("Transition4".equals(param.get("transitionKey"))) {
                    workFlow.setUserLabelM((String) param.get("applyUserGh"));
                }
            }
        }
        return  SWorkFlowUtil.submit ( operator, workFlow );
    }

    /**
     * 判断组织有没有权限
     * @param deptCode
     * @return
     */
    public static  Boolean justOrganizationCode(String deptCode){

        if (StringUtils.isEmpty(deptCode)){
            throw new BusinessException("部门组织不能为空");
        }

//        ADOrg adOrg = OrgUtil.getLevelCompany(deptCode, 3);
        ADOrg adOrg = OrgUtil.getLevelOrg(deptCode, 3);
        HashMap<String, Object> hashMap = new HashMap<>();
        hashMap.put("organizationCode",adOrg.getOrgCode());
        List<TkymmOrganization> list = businessOrganization.queryList(hashMap);
        //判读是否查询到组织
        if (list!=null && list.size()>0) {
            TkymmOrganization tkymmOrganization = list.get(0);
            Long jurisdiction = tkymmOrganization.getJurisdiction();
            //权限字段为1 表示有认定权限
            if (jurisdiction == 1) {
                return true;
            } else {
                return false;
            }
        }else{
            return false;
//            hashMap.remove("organizationCode");
//            hashMap.put("fullPathLike",deptCode);
//             list = businessOrganization.queryList(hashMap);
//             if (list!=null){
//                 TkymmOrganization lists = list.get(0);
//                 String fullPath = lists.getFullPath();
//                 boolean b = StringUtils.hasText(fullPath);
//                 if (b==true){
//                     return true;
//                 }
//             }
        }
//        throw  new BusinessException("没有配置权限");
    }


    /****
     * 根据参数返回服务数据
     * @param parmValue
     * @return
     */
    public static Map<String, Object> getServiceDataXm(String parmValue) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set ( EiConstant.serviceId, "S_KY_XM_001" );
        eiInfo.set ( "projectCode", parmValue );
        EiInfo outInfo = XServiceManager.call ( eiInfo );
        int status = outInfo.getStatus();
        if(-1==status){
//            throw new BusinessException("请求异常:" + outInfo.getMsg());
        }
        String pro = outInfo.getString("pro");
        Map<String, Object> parse = (Map<String, Object>)JSONObject.parse(pro);
        return parse;
    }

    /****
     * 根据参数返回服务数据(专利)
     * @param parmValue
     * @return
     */
    public static List<Map<String, Object>> getServiceDataZl(String parmName, String parmValue) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set ( EiConstant.serviceId, "S_KI_ZL_001" );
        eiInfo.set ( parmName, parmValue );
        EiInfo outInfo = XServiceManager.call ( eiInfo );
        int status = outInfo.getStatus();
        if(-1==status){
//            throw  new BusinessException("请求异常:"+outInfo.getMsg());
        }
        List<Map<String, Object>> list = (ArrayList<Map<String, Object>>)outInfo.get("list");
        return list;
    }

    /****
     * 获取同来源技术秘密(来源类型+来源名称)
     * @param mmMap
     * @return
     */
//    public static List<Map<String, Object>> getServiceDataMm(Map<String,Object> mmMap) {
//        EiInfo eiInfo = new EiInfo();
//        eiInfo.set(EiConstant.serviceId, "S_ZZ_MM_04");
//        Set<String> keys = mmMap.keySet();
//        for (String key : keys) {
//            eiInfo.set(key,mmMap.get(key));
//        }
//        EiInfo outInfo = XServiceManager.call(eiInfo);
//        int status = outInfo.getStatus();
//        if (-1 == status) {
//            throw new BusinessException("请求异常");
//        }
//        List<Map<String,Object>> list = (List<Map<String, Object>>) outInfo.get("result");
//        return list;
//    }


    /****
     * 根据参数返回服务数据(技术推广)
     * @param parmValue
     * @return
     */
    public static Map<String, Object> getServiceDataJSTG(String parmValue) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set ( EiConstant.serviceId, "S_ZZ_LX_001" );
        eiInfo.set ( "projectNum", parmValue );
        EiInfo outInfo = XServiceManager.call ( eiInfo );
        int status = outInfo.getStatus();
        if(-1==status){
//            throw new BusinessException("请求异常:" + outInfo.getMsg());
        }
        Map<String, Object> result = (Map<String, Object>)outInfo.get("result");
        return result;
    }

    /****
     * 根据参数返回服务数据(技术贸易)
     * @param sourceId
     * @return
     */
    public static Object getServiceDataJSMY( String sourceId) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set ( EiConstant.serviceId, "S_KT_MY_01" );
        eiInfo.set ( "projectNum", sourceId );
        EiInfo outInfo = XServiceManager.call ( eiInfo );
        int status = outInfo.getStatus();
        if(-1==status){
//            throw  new BusinessException("请求异常:"+outInfo.getMsg());
        }

        Object result = outInfo.get("project");
        return result;
    }

    /****
     * 根据参数返回服务数据(六西格玛)
     * @param sourceId
     * @return
     */
    public static String getServiceDataSIX( String sourceId) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set ( EiConstant.serviceId, "S_KL_XG_003" );
        eiInfo.set ( "projectCode", sourceId );
        EiInfo outInfo = XServiceManager.call ( eiInfo );
        int status = outInfo.getStatus();
        if(-1==status){
//            throw  new BusinessException("请求异常:"+outInfo.getMsg());
        }

        String result = (String) outInfo.get("lxProName");
        return result;
    }

    /****
     * 根据参数返回服务数据(合理化建议)
     * @param sourceId
     * @return
     */
    public static String getServiceDataHLHJY( String sourceId) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set ( EiConstant.serviceId, "S_KH_JY_001" );
        eiInfo.set ( "projectCode", sourceId );
        EiInfo outInfo = XServiceManager.call ( eiInfo );
        int status = outInfo.getStatus();
        if(-1==status){
//            throw  new BusinessException("请求异常:"+outInfo.getMsg());
        }
        String result = (String) outInfo.get("suggestName");
        return result;
    }

    /****
     * 通过来源号获取技术秘密
     * @param sourceNo
     * @return
     */
    public static List<Map<String, Object>> getMMBySourceNo(String sourceNo){
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        if(StrUtil.isEmpty(sourceNo)){
            return maps;
        }
        HashMap<String, Object> parm = new HashMap<>();
        parm.put("sourceNum",sourceNo);
//        parm.put("status", "identified");//已认定
        parm.put("dynSql", "confirm_num is not null and confirm_num !=''");//认定号不为空
        List<TkymmTechnology> tkymmTechnologies = businessTechnology.queryList(parm);
        if(ObjectUtil.isNotEmpty(tkymmTechnologies)){
            for (TkymmTechnology tkymmTechnology : tkymmTechnologies) {
                maps.add(tkymmTechnology.toMap());
            }
        }
        return maps;
    }

    /****
     * 通过来源号获取专利信息
     * @param sourceNo
     * @return
     */
    public static List<Map<String, Object>> getZLBySourceNo(String sourceNo,String sourceType){
        ArrayList<Map<String, Object>> maps = new ArrayList<>();
        if(StrUtil.isEmpty(sourceNo)){
            return maps;
        }
        HashMap<String, Object> param = new HashMap<>();
        param.put("fromNo",sourceNo);
        param.put("fromType",sourceType);
//        param.put("patentStatus", "06");//已授权
        param.put("dynSql", "SLRQ is not null and SLRQ !=''");//受理日期不为空

        List<TkizlPatentInfo> patentList = businessPatentInfo.queryList(param);
        for (TkizlPatentInfo baseZl : patentList) {
            if (ObjectUtil.isNotEmpty(baseZl)) {
                Map<String,Object> zlMap = baseZl.toMap();
                TkizlApplyBaseinfo load = businessApplyBaseinfoPatent.load(baseZl.getApplyId());
                if (ObjectUtil.isNotEmpty(load)) {
                    //添加基础表数据
                    //占比数据
                    zlMap.put("extra10", load.getExtra10());
                }
                maps.add(zlMap);
            }
        }
        return maps;
    }

    /***
     * 通过来源编号获取技术秘密名称
     * @param sourceNo
     * @return
     */
    public static String getMMNameBySourceNo(String sourceNo){
        if(ObjectUtil.isEmpty(sourceNo)){
            return null;
        }

        List<Map<String, Object>> mmBySourceNo = getMMBySourceNo(sourceNo);
        String technologyName = mmBySourceNo.stream().map(m -> (String) m.get("technologyName")).collect(Collectors.joining(";"));
        return technologyName;
    }


    /***
     * 流程中暂存
     * @param parms
     * @return
     */
    public static AjaxResult axiosSave(Map<String,Object> parms){
        if(ObjectUtil.isEmpty(parms)){
            return AjaxResult.error("暂存失败");
        }
        if(!parms.containsKey("businessId")){
            return AjaxResult.error("暂存失败");
        }
        if(!parms.containsKey("processCode")){
            return AjaxResult.error("暂存失败");
        }
        String processCode = (String)parms.get("processCode");
        String activityCode = (String)parms.get("activityCode");
        if(StrUtil.isEmpty(processCode)){
            return AjaxResult.error("暂存失败");
        }

        String loginName = UserSession.getLoginName();
        if(MMConstans.process_rd.equals(processCode)){
            TkymmTechnologyEx tkymmTechnologyEx = BeanUtil.toBean(parms, TkymmTechnologyEx.class);
            businessTechnology.update(loginName, tkymmTechnologyEx);
        }else if(MMConstans.kymmreward.equals(processCode)){
            if (MMConstans.Allot_Ratio.equals(activityCode)) {
                //TkymmTechImplEx tkymmTechImplEx = BeanUtil.toBean(parms, TkymmTechImplEx.class);
            } else if(MMConstans.KYMM_DEPT_ZG.equals(activityCode)) {
                TkymmTechImplEx tkymmTechImplEx = BeanUtil.toBean(parms, TkymmTechImplEx.class);
                businessTechImpl.update(loginName, tkymmTechImplEx);
            }
        }else if(MMConstans.kymmsondeptps.equals(processCode)){

            if (MMConstans.Manual_FC.equals(activityCode) || MMConstans.Manual_BM.equals(activityCode)) {
                TkymmTechImplEx tkymmTechImplEx = BeanUtil.toBean(parms, TkymmTechImplEx.class);
                BusinessTechImpl businessTechImpl = SpringUtil.getBean(BusinessTechImpl.class);
                businessTechImpl.update(loginName, tkymmTechImplEx);

                //处理大字段
                HashMap<String, Object> tkymmTechImplExmap = new HashMap<>();
                tkymmTechImplExmap.put("implementationSummaryText", tkymmTechImplEx.getImplementationSummaryText());
                tkymmTechImplExmap.put("proofImplementationText", tkymmTechImplEx.getProofImplementationText());
                ClobUtil.addOrUpdateadd(tkymmTechImplEx.toMap(), tkymmTechImplEx.getTechImplId(),
                        TkymmTechImplEx.class.toString(), MMConstans.business_type, UserSession.getLoginName());

                //处理附件
                if (ObjectUtil.isNotEmpty(tkymmTechImplEx)) {

                    if (StrUtil.isNotEmpty(tkymmTechImplEx.getImplementationSummary())) {
                        // 实施小结相关附件
                        SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(tkymmTechImplEx.getTechImplId(),
                                tkymmTechImplEx.getImplementationSummary(), MMConstans.business_type,
                                MMConstans.IMPLEMENTATION_SUMMARY, null, null);
                    }

                    if (StrUtil.isNotEmpty(tkymmTechImplEx.getProofImplementation())) {
                        // 实施情况证明相关附件
                        SAttachmentUtil.addAttachmentMapsBySourceGuidAndSourceLabel(tkymmTechImplEx.getTechImplId(),
                                tkymmTechImplEx.getProofImplementation(), MMConstans.business_type, MMConstans.PROOF_IMPLEMENTATION,
                                null, null);
                    }
                }
            }

            if("KYMM_DEPT_LEADER".equals(activityCode)) {
                //实施部门打分
                TkymmTechAccess techAccess = BeanUtil.toBean(parms, TkymmTechAccess.class);
                if(null!=techAccess&&StringUtils.isNotEmpty(techAccess.getSourceId())) {
                    businessKYMMTechAccess.saveByType(UserSession.getLoginName(), techAccess.getSourceId(), EnumKYMMAccessType.SSBM_LD, techAccess);
                }
            }
        } else if(JMConstans.process_rd.equals(processCode)){//解密流程

        }
        return AjaxResult.success("暂存成功");
    }


    public static BigDecimal calculationTemplate(){
        return  null;
    }


    //判断技术领域是否包含钢铁产品类
    public static Boolean getIron(String treeId,String treeName) {
        boolean flag = false;
        String[] ids = treeId.split(",");
        StringBuffer strSql = new StringBuffer();
        strSql.append("AREA_ID in (");
        for (int i = 0; i < ids.length; i++) {
            if (i == ids.length - 1) {
                strSql.append("'"+ids[i]+"'");
            } else {
                strSql.append("'"+ids[i]+"',");
            }
        }
        strSql.append(")");
        Map map = new HashMap();
        map.put("dynSql",strSql);

        List<TkizlMaintainTecharea> queryList = businessMaintainTecharea.queryList(map);
        for (TkizlMaintainTecharea tkizlMaintainTecharea : queryList) {
            if (tkizlMaintainTecharea.getFullId().split("/")[1].equals("2D8D2ECC648E727048257B19001C8897")) {
                flag = true;
                break;
            }
        }

        return flag;
    }


    //根据id和申请类型获取当前申报次数和最大申报次数
    public static Map<String,Object> getAwardNum(String technologyId,String applyType) {
        TkymmTechnology tkymmTechnology = businessTechnology.query(technologyId);
        Optional.ofNullable(tkymmTechnology).orElseThrow(() -> new PlatException("认定数据不能为空"));
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("projectCode", tkymmTechnology.getSourceNum());
        eiInfo.set(EiConstant.serviceId, "S_KY_XM_025");
        EiInfo call = XServiceManager.call(eiInfo);
        //效益金额
        BigDecimal xyje = new BigDecimal("0");
        if (call.getStatus() != -1) {
            String economyBenefit = (String) call.get("economyBenefit");
            if (StringUtils.isNotBlank(economyBenefit)) {
                xyje = new BigDecimal(economyBenefit);
            }
        }

        Map<String, Object> numMap = new HashMap<>();
        HashMap<String, Object> params = new HashMap<>();
        params.put("technologyId", technologyId);
//        params.put("dynSql", "IMPL_STATUS !='" + MMConstans.proccss_status_draft + "'");
        //效益奖
        params.put("applyType", "benefit_award");
        List<TkymmTechImpl> tkymmTeches = businessTechImpl.queryList(params);
        tkymmTeches = tkymmTeches.stream()
                .filter(f->!MMConstans.proccss_status_no.equals(f.getImplStatus())
                        && !MMConstans.proccss_status_cancel.equals(f.getImplStatus())
                        && !MMConstans.proccss_status_stop.equals(f.getImplStatus())
                        && !MMConstans.proccss_status_draft.equals(f.getImplStatus())
                )
                .collect(Collectors.toList());
        numMap.put("benefitNum", Convert.toLong(tkymmTeches.size() + 1));
        //无来源为5次，否则4次（条件内）
        if (MMConstans.NO_SOURCE.equals(tkymmTechnology.getSourceType())) {
            numMap.put("benefitNumMax", 5L);
        } else {
            List<Map<String, Object>> zlList = getZLBySourceNo(tkymmTechnology.getSourceNum(),tkymmTechnology.getSourceType());
            //来源于科研的部门项目，效益小于100万元并且没有专利的
            if ("KYXM".equals(tkymmTechnology.getSourceType())//来源类型
                    && tkymmTechnology.getSourceNum().indexOf('Z') == 0//来源编号首字母'Z'
                    && zlList.size() == 0 //无专利
                    && new BigDecimal("100").compareTo(xyje) == 1) {//效益小于100万元

                numMap.put("benefitNumMax", 5L);
            } else if ("KYXM".equals(tkymmTechnology.getSourceType())) {
                EiInfo shareData = getShareData(tkymmTechnology.getSourceNum());
                if (shareData.getStatus() != -1) {
                    //lrfxStatus : np 未参与 active 申报中 end 已结束
                    String lrfxStatus = (String) shareData.get("lrfxStatus");
                    if (lrfxStatus == "np") {//未参与利润分享
                        numMap.put("benefitNumMax", 4L);
                    } else {
                        numMap.put("benefitNumMax", 1L);
                    }
                } else {
                    throw new PlatException(shareData.getMsg());
                }
            } else {//非科研项目、非无来源
                numMap.put("benefitNumMax", 4L);
            }
        }

        //水平奖
        params.put("applyType", "level_award");
        numMap.put("levelNum", 1L);
        numMap.put("levelNumMax", 1L);
        return numMap;
    }

    /**
     * 根据来源编号 返回利润分享数据
     * @param sourceNum
     * @return
     */
    public static EiInfo getShareData(String sourceNum) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("projectCode", sourceNum);
        eiInfo.set(EiConstant.serviceId, "S_KL_RF_002");
        EiInfo call = XServiceManager.call(eiInfo);
        if (call.getStatus() == -1) {
            throw new PlatException(call.getMsg());
        }
        return call;
    }

    /**
     * 根据来源编号 返回实施奖 科研项目 结题、经济效益数据
     * @param sourceNum
     * @return
     */
    public static EiInfo getJtXyData(String sourceNum) {
        //kyxm相关参数计算
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("projectCode", sourceNum);
        eiInfo.set(EiConstant.serviceId, "S_KY_XM_025");
        EiInfo call = XServiceManager.call(eiInfo);
        if (call.getStatus() == -1) {
            throw new PlatException(call.getMsg());
        }
        return call;
    }

    //根据业务id和评审模块判断是否已完成评审
    public static Boolean getOverPs(String businessId,String moduleCode) {
        boolean flag = false;
        EiInfo einfo = new EiInfo();
        einfo.set("bizId",businessId);
        einfo.set("moduleCode",moduleCode);
        einfo.set(EiConstant.serviceId, "S_MP_PS_02");
        EiInfo outInfo = XServiceManager.call(einfo);
        List<Map<String,Object>> list = (List<Map<String, Object>>) outInfo.get("list");
        if (list != null && !list.isEmpty()) {
            Map<String, Object> psMap = list.get(0);
            Object isEnd = psMap.get("isEnd");
            if ("1".equals(isEnd)) {
//                inInfo.set("psEnd",true);
                flag = true;
            }
        } else {
            throw new BusinessException("尚未启动专家评审");
        }

        return flag;
    }

    //根据业务id和模块code获取专家评审状态
    public static String getPsStatusByBusinessId(String businessId,String moduleCode) {
        EiInfo einfo = new EiInfo();
        einfo.set("bizId",businessId);
        einfo.set("moduleCode",moduleCode);
        einfo.set(EiConstant.serviceId, "S_MP_PS_02");
        EiInfo outInfo = XServiceManager.call(einfo);
        List<Map<String,Object>> list = (List<Map<String, Object>>) outInfo.get("list");
        if (list != null && list.size() > 0) {
            Map<String, Object> psMap = list.get(0);
            Object isEnd = psMap.get("isEnd");
            if ("1".equals(isEnd)) {
                return MMConstans.Complete_Review;
            }else {
                return MMConstans.In_Review;
            }
        }
        return MMConstans.Not_Review;
    }

    //获取确认贡献系数管理单位
    public static List<TkizlMaintainGxxsqr> getContributeGL() {
        BusinessMaintainGxxsqrPatent gxxsqr = SpringUtil.getBean(BusinessMaintainGxxsqrPatent.class);
        List<TkizlMaintainGxxsqr> glDept = gxxsqr.queryList(null);
        return glDept;
    }


    /**
     * 获取全额和审批额
     * @param techImplId 实施奖ID
     * @param benefitAwardCoefficient 奖励系数
     * @param scoreallCompAdmin 总打分
     * @param remain 留成比例
     * @return
     */
    public static Map<String,Object> getAward(String techImplId, String benefitAwardCoefficient, String scoreallCompAdmin, String remain) {
        Map<String, Object> rtn = new HashMap<>();

        try {
            BusinessTechImpl businessTechImpl = SpringUtil.getBean(BusinessTechImpl.class);
            TkymmTechImpl techImpl = businessTechImpl.query(techImplId);
            if(null==techImpl) {
                throw new PlatException("无对应实施奖");
            }
            BigDecimal awardAll = new BigDecimal(0);//奖励全额 = 计奖效益（万元）* 奖励系数
            BigDecimal awardEndorse = new BigDecimal(0);//审批额 = 奖励全额 * 去除本公司外发明人贡献额度后的奖励额
            BigDecimal award = new BigDecimal(0);//实发额 审批额*（100-留成）

            BigDecimal remain_dec;
            try {
                remain_dec = new BigDecimal(remain);
            } catch (Exception e) {
                throw new PlatException("留成比例不支持"+remain);
            }
            if("benefit_award".equals(techImpl.getApplyType())) {//效益奖
                BigDecimal benefitAwardCoefficient_dec;
                try {
                    benefitAwardCoefficient_dec = new BigDecimal(benefitAwardCoefficient);
                } catch (Exception e) {
                    throw new PlatException("奖励系数不支持"+benefitAwardCoefficient);
                }
                BigDecimal benefitAward = techImpl.getBenefitAward();//计奖效益单位万元
                if(benefitAward!=null) {
                    //总额
                    awardAll = benefitAward.multiply(new BigDecimal("10000"))
                            .multiply(benefitAwardCoefficient_dec)
                            .multiply(new BigDecimal("0.01"));
                    awardAll = getAward(awardAll);
                    //审批额
                    BusinessTechMember businessTechMember = SpringUtil.getBean(BusinessTechMember.class);
                    BigDecimal gx = businessTechMember.getGX(techImpl.getTechnologyId());//贡献系数
                    awardEndorse = awardAll.multiply(gx).multiply(new BigDecimal(0.01));
                    awardEndorse = getAward(awardEndorse);
                }
            }else if("level_award".equals(techImpl.getApplyType())) {//水平奖
                //总额
                int levelAward = LevelAwardUtil.getLevelAward(Integer.parseInt(scoreallCompAdmin), "LEVEL_AWARD_JS");//技术秘密
                awardAll = new BigDecimal(levelAward);
                //审批额
                BusinessTechMember businessTechMember = SpringUtil.getBean(BusinessTechMember.class);
                BigDecimal gx = businessTechMember.getGX(techImpl.getTechnologyId());//贡献系数
                awardEndorse = awardAll.multiply(gx).multiply(new BigDecimal(0.01));
            }else {
                throw new PlatException("实施奖类型不支持");
            }

            //实发额
            award = new BigDecimal(100).subtract(remain_dec).multiply(awardEndorse).multiply(new BigDecimal(0.01));
            award = getAward(award);

            DecimalFormat df = new DecimalFormat("0");
            rtn.put("awardAll", df.format(awardAll));
            rtn.put("awardEndorse", df.format(awardEndorse));
            rtn.put("award", df.format(award));
            rtn.put(EiConstant.status, 1);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            rtn.put("msg", e.getMessage());
            rtn.put(EiConstant.status, -1);
        }
        return rtn;
    }
    
    /**
     * 实施奖：计算全额、审批额、实发额都应该是按整百数取整（根据十位数四舍五入），奖金数字都是整百以上的，十位数和个位数都是0
     * @param bd
     * @return
     */
    public static BigDecimal getAward(BigDecimal bd) {
        BigDecimal ss= bd.divide(new BigDecimal(100));
        DecimalFormat df = new DecimalFormat("0");
        df.setRoundingMode(RoundingMode.HALF_UP);
        return new BigDecimal(df.format(ss)).multiply(new BigDecimal(100));
    }
    
    /**
     * 万元
     * 精确到小数后6位
     * @param bd
     * @return
     */
    public static BigDecimal getWan(BigDecimal bd) {
        BigDecimal ss= bd.multiply(new BigDecimal(1000000));
        DecimalFormat df = new DecimalFormat("0");
        df.setRoundingMode(RoundingMode.HALF_UP);
        return new BigDecimal(df.format(ss)).divide(new BigDecimal(1000000));
    }
    
    /**
     * 精确到小数后2位
     * @param bd
     * @return
     */
    public static BigDecimal getPoint2(BigDecimal bd) {
        BigDecimal ss= bd.multiply(new BigDecimal(100));
        DecimalFormat df = new DecimalFormat("0");
        df.setRoundingMode(RoundingMode.HALF_UP);
        return new BigDecimal(df.format(ss)).divide(new BigDecimal(100));
    }


    /**
     * 更新提出人表extra6、extra1(股份内份额)
     * @param operator
     * @param technologyId
     */
    public static void updateRatio(String operator, String technologyId) {

        BusinessTechMember businessTechMember = SpringUtil.getBean(BusinessTechMember.class);
        List<TkymmTechMember> members = businessTechMember.queryByBusinessId(technologyId);
        if (members != null && members.size() > 0) {

            Long sumGf = members.stream().filter(f -> StrUtil.isNotEmpty(f.getMemberGh()) && PatentUtil.isGf(f.getDeptCode()))
                    .mapToLong(t -> t.getContribution().longValue()).sum();
//            BigDecimal sumGf = new BigDecimal(0);
//            for (TkymmTechMember member : members) {
//                if(StrUtil.isNotEmpty(member.getMemberGh()) && PatentUtil.isGf(member.getDeptCode())){
//                    sumGf.add(member.getContribution());
//                }
//            }

            //发明人所属更新(1-股份内,2-股份外,3-外单位)
            for (TkymmTechMember member : members) {
                if (org.apache.commons.lang.StringUtils.isNotBlank(member.getDeptCode())) {

                    //保留两位小数
                    java.text.DecimalFormat df = new java.text.DecimalFormat("#.##");

                    if (PatentUtil.isGf(member.getDeptCode())) {
                        //股份内份额
                        member.setExtra1(new BigDecimal(df.format(member.getContribution().doubleValue() / (double) sumGf))
                                .multiply(new BigDecimal(100)).toString());
                        member.setExtra6(MMConstans.iscompany_1);
                    } else {
                        member.setExtra6(MMConstans.iscompany_2);
                    }
                } else {
                    member.setExtra6(MMConstans.iscompany_3);
                }
                businessTechMember.update(operator, member);
            }

        }
    }

    /**
     * 审批日志数据持久化（评审费）
     *    操作人、业务主键、评审类型(RD-认定,SS-实施,JM-解密)、评审结果(1-同意,0-否定)、流程code、当前活动code
     */
    public static boolean psfLog(String operator, String businessId, String reviewType, String reviewResults, String processCode, String activityCode) {

        if (StringUtils.isBlank(processCode)) {
//            throw new PlatException("流程code不能为空");
            return false;
        }
        if (StringUtils.isBlank(activityCode)) {
//            throw new PlatException("节点code不能为空");
            return false;
        }
        if (StringUtils.isBlank(businessId)) {
//            throw new PlatException("业务id不能为空");
            return false;
        }
        //流程过滤   目前只记录  认定流程  和 实施奖部门打分流程
        if (!MMConstans.process_rd.equals(processCode)//认定流程
                && !MMConstans.kymmsondeptps.equals(processCode)) {//部门打分流程
            return false;
        }
        //认定节点过滤  只记录预审和初审节点
        if (MMConstans.process_rd.equals(processCode)) {
            if (!MMConstans.PRELIMINARY_EXAMINAT.equals(activityCode) //预审
                    && !MMConstans.PRELIMINARY_REVIEW.equals(activityCode)) {//初审
                return false;
            }

        }
        //实施节点过滤
//        if (MMConstans.kymmreward.equals(processCode)) {
//            if ("KYMM_START".equals(activityCode)
//                    || "KYMM_START".equals(activityCode)
//                    || "Allot_Ratio".equals(activityCode)
//                    || "Manual1".equals(activityCode)) {
//                return false;
//            }
//        }
        //实施打分节点过滤  只记录分厂管理员和部门管理员节点
        if (MMConstans.kymmsondeptps.equals(processCode)) {
            if (!MMConstans.KYMM_FC_ADMIN_QR.equals(activityCode) //预审
                    && !MMConstans.KYMM_DEPT_ADMIN_QE.equals(activityCode)) {//初审
                return false;
            }

        }

        TkymmTechnology technology = Optional.ofNullable(businessTechnology.query(businessId)).orElse(new TkymmTechnology());
        TkymmTechImpl techImpl = Optional.ofNullable(businessTechImpl.query(businessId)).orElse(new TkymmTechImpl());

        String flowName = null;
        String lsh = null;
        String ssjType = null;
        String ssjCs = null;
        String orgMc = null;
        //主流程
        switch (processCode) {
            case MMConstans.process_rd:
                flowName = "技术秘密认定申请";
                lsh = technology.getConfirmNum();
                orgMc = OrgUtil.getOrgPathName(technology.getFirstdeptCode());
                break;
            case MMConstans.kymmreward:
                flowName = "技术秘密实施奖申报";
                ssjType = techImpl.getApplyType();
                ssjCs = String.valueOf(techImpl.getApplyTimes());
                break;
            case MMConstans.kymmsondeptps:
                flowName = "技术秘密实施奖申报";
                ssjType = techImpl.getApplyType();
                ssjCs = String.valueOf(techImpl.getApplyTimes());
                break;
            case MMConstans.process_jm:
                flowName = "技术秘密解密申请";
                break;
            default:
                flowName = "技术秘密流程";
        }


        String logGh = operator;
        String logXm = UserUtil.getUserName(logGh);
        String logDate = DateUtil.formatDate(new Date());
        String logIdent = getRoleName(processCode, activityCode);
        String logYj = reviewResults;

        //评审费明细表GGMK.T_MPPS_REVIEW_MEMO
        TmppsReviewMemo reviewMemo = new TmppsReviewMemo();
        reviewMemo.setEmpId(operator);
        reviewMemo.setEmpName(UserUtil.getUserName(operator));
        //获取部门  获取单位
        ADOrg adorg = OrgUtil.getMainOrgByUserCode(operator);
        if (adorg != null) {
            if (adorg.getOrgCode() != null) {
                reviewMemo.setDeptCode(adorg.getOrgCode());
            }
            if (adorg.getOrgPathName() != null) {
                reviewMemo.setDeptName(adorg.getOrgPathName());
            }
        }
        reviewMemo.setReviewType(reviewType);
        reviewMemo.setExtra1(businessId);
        reviewMemo.setExtra2(getRoleName(processCode, activityCode));
        reviewMemo.setExtra3(logYj);
        reviewMemo.setExtra4(activityCode);
        reviewMemo.setExtra5("0");//扩展字段5已启评审流程1是0否

        HashMap param = new HashMap();
        param.put("reviewType", reviewType);
        param.put("extra1", businessId);
        param.put("empId", operator);
        List<TmppsReviewMemo> list = businessReviewMemo.queryList(param);
        if (list != null && list.size() > 0) {
            //更新
            String psmemoId = list.get(0).getPsmemoId();
            reviewMemo.setPsmemoId(psmemoId);
            businessReviewMemo.update(operator, reviewMemo);
        } else {
            //插入
            businessReviewMemo.insert(operator, reviewMemo);
        }


        //评审费主表ZZZC.T_KIZL_PSFLOG
        TkizlPsflog psflog = new TkizlPsflog();
        psflog.setLsh(lsh);
        psflog.setSsjtype(ssjType);
        psflog.setSsjcs(ssjCs);
        psflog.setDocId(businessId);
        psflog.setOrgmc(orgMc);
        psflog.setLoggh(logGh);
        psflog.setLogxm(logXm);
        psflog.setLogdate(logDate);
        psflog.setLogIdent(logIdent);
        psflog.setLogYj(logYj);
        psflog.setFlowName(flowName);
        psflog.setExtra1("100");
        psflog.setExtra2(MMConstans.business_type);

        HashMap psmap = new HashMap();
        psmap.put("docId", businessId);
        psmap.put("loggh", operator);
        psmap.put("flowName", flowName);
        List<TkizlPsflog> pslist = businessPsf.queryList(psmap);
        if (pslist != null && pslist.size() > 0) {
            String logId = pslist.get(0).getLogId();
            psflog.setLogId(logId);
            businessPsf.update(operator, psflog);
        } else {
            businessPsf.insert(logGh, psflog);
        }

        return true;
    }

    public static String getRoleName(String processCode, String activityCode) {

        if (StringUtils.isAnyBlank(processCode, activityCode)) {
            throw new PlatException("流程code或节点code为空");
        }
        String roleName = null;
        switch (processCode) {
            case MMConstans.process_rd:
                switch (activityCode) {
                    case "APPLE_KING_REVIEW":
                        roleName = "技术秘密认定_苹果王";
                        break;
                    case "PRELIMINARY_EXAMINAT":
                        roleName = "技术秘密认定_分厂管理员";
                        break;
                    case "PRELIMINARY_REVIEW":
                        roleName = "技术秘密认定_部门管理员";
                        break;
                    case "LEADER_APPROVAL":
                        roleName = "技术秘密认定_部门领导";
                        break;
                    case "KYMM_ZG_DEPT_ADMIN":
                        roleName = "技术秘密认定_单位主管部门管理员";
                        break;
                    case "KYMM_ZG_DEPT_ZG":
                        roleName = "技术秘密认定_单位主管部门主管";
                        break;
                    case "KYMM_DEPT_ZG_LEADER":
                        roleName = "技术秘密认定_单位主管部门领导";
                        break;
                    default:
                        //获取节点配置的角色
                        String roleCode = SWorkFlowUtil.getRoleValue(processCode, activityCode);
                        //根据角色获取名称
                        roleName = "技术秘密认定_" + StrUtil.nullToEmpty(RoleUtil.getRoleName(roleCode));
                }
                break;
            case MMConstans.kymmreward:
                switch (activityCode) {
                    case "Project_Leader":
                        roleName = "技术秘密实施_科研项目负责人";
                        break;
                    case "KYMM_DEPT_ZG":
                        roleName = "技术秘密实施_主管部门管理员";
                        break;
                    case "KYMM_ZGDEPT_ZG":
                        roleName = "技术秘密实施_主管部门主管";
                        break;
                    case "KYMM_ZGDEPT_LEADER":
                        roleName = "技术秘密实施_主管部门领导";
                        break;
                    case "KYMM_GS_FG_LEADER":
                        roleName = "技术秘密实施_公司分管领导";
                        break;
                    default:
                        //获取节点配置的角色
                        String roleCode = SWorkFlowUtil.getRoleValue(processCode, activityCode);
                        //根据角色获取名称
                        roleName = "技术秘密实施_" + StrUtil.nullToEmpty(RoleUtil.getRoleName(roleCode));
                }
                break;
            case MMConstans.kymmsondeptps:
                switch (activityCode) {
                    case "KYMM_FC_ADMIN_QR":
                        roleName = "技术秘密实施_分厂管理员";
                        break;
                    case "KYMM_DEPT_ADMIN_QE":
                        roleName = "技术秘密实施_部门管理员";
                        break;
                    default:
                        //获取节点配置的角色
                        String roleCode = SWorkFlowUtil.getRoleValue(processCode, activityCode);
                        //根据角色获取名称
                        roleName = "技术秘密实施_" + StrUtil.nullToEmpty(RoleUtil.getRoleName(roleCode));
                }
                break;
            case MMConstans.process_jm:
                break;
        }

        return roleName;
    }
    
    /**
     * 获取效益奖奖励系数
     * @param scoreallCompAdmin
     * @return
     */
    public static String getBenefitXS(String scoreallCompAdmin) {
        double n = Double.parseDouble(scoreallCompAdmin);
        double xs;
        //n=总分 xs=系数
        if(n<30) {//总分<30 xs=0
            xs = 0;
        } else if(n>=30&n<60) {// xs=(0.5-0.1)/(60-30)*(n-30)+0.1
            xs = 0.4000/30*(n-30)+0.1;
        } else if(n>=60&n<=100) {//xs=(1.5-0.5)/(100-60)*(n-60)+0.5
            double ss = n-60;
            xs = 1.0000/40*ss+0.5;
        } else {
            return "";
        }
        BigDecimal b = new BigDecimal(xs*2);//最终系数*0.02  百分比要乘100
        xs = b.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
        return String.valueOf(xs);
    }

    /**
     * 根据系数获取总分 系数默认1~3
     * @param benefitXs
     * @return
     */
    public static String getScoreAll(String benefitXs) {
        double xs = Double.parseDouble(benefitXs) / 2;  //0-1.5
        double scoreAll = 0.0;
        //scoreAll=总分 xs=系数
        if (xs < 0.1) {
            //默认位0
        } else if (xs >= 0.1 && xs < 0.5) {
            scoreAll = (xs - 0.1) * 30 / 0.4000 + 30;
        } else if (xs >= 0.5 && xs <= 1.5) {//n=(xs-0.5)/(1.0000/(100-60))+60
            scoreAll = (xs - 0.5) / (0.025) + 60;
        }
        BigDecimal bd = new BigDecimal(scoreAll);
        scoreAll = bd.setScale(0, BigDecimal.ROUND_HALF_UP).doubleValue();
        return String.valueOf(scoreAll);
    }

    /**
     * 分配分数
     * @param benefitXs
     * @return
     */
    public static Map<String, Object> getScores(String benefitXs) {
        Map<String, Object> scoresMap = new HashMap<>();
        //根据系数 取总分
        String all = getScoreAll(benefitXs);
        BigDecimal bdAll = new BigDecimal(all);

        BigDecimal scoreAll = new BigDecimal(all);
        BigDecimal score1 = scoreAll.multiply(new BigDecimal(30))
                .divide(new BigDecimal(100))
                .setScale(0, BigDecimal.ROUND_DOWN);
        BigDecimal score2 = scoreAll.multiply(new BigDecimal(35))
                .divide(new BigDecimal(100))
                .setScale(0, BigDecimal.ROUND_DOWN);
        BigDecimal score3 = scoreAll.multiply(new BigDecimal(35))
                .divide(new BigDecimal(100))
                .setScale(0, BigDecimal.ROUND_DOWN);
        BigDecimal subScore = bdAll.subtract(score1).subtract(score2).subtract(score3);
        if (bdAll.compareTo(new BigDecimal(0)) == 1 && subScore.compareTo(new BigDecimal(0)) == 1) {
            score1 = score1.add(subScore).setScale(0,BigDecimal.ROUND_DOWN);
        }

        scoresMap.put("score1", String.valueOf(score1));
        scoresMap.put("score2", String.valueOf(score2));
        scoresMap.put("score3", String.valueOf(score3));
        scoresMap.put("scoreAll", String.valueOf(scoreAll));
        return scoresMap;
    }

    /**
     * 判断是否为国际组织
     * @param orgCode
     * @return
     */
    public static boolean isGj(String orgCode) {
        if (orgCode.equals(MMConstans.gjzz)) {
            return true;
        }

        ADOrg[] allParentOrg = OrgUtil.getAllParentOrg(orgCode);
        if (allParentOrg == null) {
            throw new PlatException(orgCode + "不存在该申报组织");
        }
        for (ADOrg adOrg : allParentOrg) {
            if (MMConstans.gjzz.equals(adOrg.getOrgCode())) {
                return true;
            }
        }
        return false;
    }

    /**
     * 根据组织code与管理单位 判断是否为当前管理单位下组织
     * @param orgCode
     * @param gldw
     * @return
     */
    public static boolean isSubOrg(String orgCode,String gldw) {
        if (StringUtils.isAnyBlank(orgCode, gldw)) {
            throw new PlatException("组织代码或管理单位不能空");
        }

        if (orgCode.equals(gldw)) {
            return true;
        }

        ADOrg[] allParentOrg = OrgUtil.getAllParentOrg(orgCode);
        if (allParentOrg == null) {
            throw new PlatException(orgCode + "组织不存在");
        }
        for (ADOrg adOrg : allParentOrg) {
            if (gldw.equals(adOrg.getOrgCode())) {
                return true;
            }
        }
        return false;
    }

    public static List<String> getCqOrgbyLogin(String loginName) {
        Map<String, Object> queryMap = new HashMap<>();
        queryMap.put("empId", loginName);
        List<String> collect = null;

        List<TkizlMaintainCqsry> cqsryList = businessMaintainCqsryPatent.queryList(queryMap);
        if (null != cqsryList && cqsryList.size() > 0) {
            collect = cqsryList.stream().map(m -> {
                ADOrg orgByOrgCode = OrgUtil.getOrgByOrgCode(m.getDeptCode());
                return orgByOrgCode.getOrgPathCode();
            }).collect(Collectors.toList());
        }
        return collect;

    }

    /**
     * 根据业务id和流程code获取 发明人列表
     * @param businessId
     * @param processCode
     * @return
     */
    public static List<String> getFmrList(String businessId, String processCode) {
        List<String> paramList = new ArrayList<>();
        TkymmTechnology technology = null;
        switch (processCode) {
            case MMConstans.process_rd:
                technology = businessTechnology.query(businessId);
                break;
            case MMConstans.kymmreward:
                technology = businessTechImpl.getTechByTechImplId(businessId);
                break;
        }
        if (null == technology) {
            throw new PlatException("数据未找到,请联系系统管理员");
        }
        paramList.add(technology.getContactPersonGh());
        List<TkymmTechMember> memberList = businessTechMember.queryByBusinessId(technology.getTechnologyId());
        if (null != memberList && !memberList.isEmpty()) {
            for (TkymmTechMember member : memberList) {
                paramList.add(member.getMemberGh());
            }
        }
         paramList = paramList.stream().filter(f -> StringUtils.isNotBlank(f)).distinct().collect(Collectors.toList());
        return paramList;
    }

}
