package com.baosight.bscdkj.ky.mm.mmutlis;

import cn.hutool.core.codec.Base64;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSON;
import cn.hutool.json.JSONUtil;
import com.baosight.bscdkj.common.exception.BusinessException;
import com.baosight.bscdkj.common.ki.domain.TkizlApplyBaseinfo;
import com.baosight.bscdkj.common.ki.domain.TkizlPatentInfo;
import com.baosight.bscdkj.common.ky.domain.TkymmTechImpl;
import com.baosight.bscdkj.common.ky.domain.TkymmTechnology;
import com.baosight.bscdkj.ki.zl.business.BusinessApplyBaseinfoPatent;
import com.baosight.bscdkj.ki.zl.business.BusinessPatentInfoPatent;
import com.baosight.bscdkj.ki.zl.utils.PatentUtil;
import com.baosight.bscdkj.ky.mm.business.BusinessTechImpl;
import com.baosight.bscdkj.ky.mm.business.BusinessTechImplDept;
import com.baosight.bscdkj.ky.mm.business.BusinessTechnology;
import com.baosight.bscdkj.ky.mm.common.MMConstans;
import com.baosight.bscdkj.ky.mm.domain.TkymmTechImplEx;
import com.baosight.bscdkj.ky.mm.domain.TkymmTechnologyEx;
import com.baosight.bscdkj.mp.ad.dto.ADOrg;
import com.baosight.bscdkj.mp.ad.utils.OrgUtil;
import com.baosight.bscdkj.mp.ad.utils.RoleUtil;
import com.baosight.bscdkj.mp.ty.utils.SDictUtil;
import com.baosight.bscdkj.mp.wf.dto.SubProcessParam;
import com.baosight.bscdkj.mp.wf.dto.WorkFlow;
import com.baosight.bscdkj.mp.wf.utils.SWorkFlowUtil;
import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import com.baosight.iplat4j.core.exception.PlatException;
import com.baosight.iplat4j.core.service.soa.XServiceManager;
import com.baosight.iplat4j.core.web.threadlocal.UserSession;
import org.apache.commons.lang3.StringUtils;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/****
 * 实施奖工具类
 */
public class ImplementationAwardUtli {
    //实施部门
    private static  final   BusinessTechImplDept businessTechImplDept = SpringUtil.getBean(BusinessTechImplDept.class);

    //实施奖
    private static final BusinessTechImpl businessTechimpl = SpringUtil.getBean(BusinessTechImpl.class);

    //认定奖
    private static final BusinessTechnology businessTechnology = SpringUtil.getBean(BusinessTechnology.class);

    //专利
    private static BusinessPatentInfoPatent businessPatentInfo = SpringUtil.getBean(BusinessPatentInfoPatent.class);
    private static BusinessApplyBaseinfoPatent businessApplyBaseinfo = SpringUtil.getBean(BusinessApplyBaseinfoPatent.class);

    /****
     * 根据认定id 获取申报次数
     * @param technologyId
     * @return
     */
    public static Long countNumberAwards(String technologyId){
        if(StrUtil.isEmpty ( technologyId )){
            throw new BusinessException ( "认定主键不能为空" );
        }
        BusinessTechImpl bean = SpringUtil.getBean ( BusinessTechImpl.class );
        HashMap<String, Object> params = new HashMap<> ();
        params.put ( "technologyId" ,technologyId);
        List<TkymmTechImpl> tkymmTeches = bean.queryList ( params );
        if(ObjectUtil.isNotEmpty ( tkymmTeches )){
            return Convert.toLong ( tkymmTeches.size () + 1 );
        }

        return 1L;
    }

    /***
     *启动实施奖流程
     * @param tkymmTechImplEx
     * @return
     */
    public static String startProcess(TkymmTechImplEx tkymmTechImplEx){
        //启动流程
        String loginName = UserSession.getLoginName();
        HashMap<String, Object> variable = new HashMap<>();
        variable.put("orgParamter",tkymmTechImplEx.getFirstdeptCode());
        //判断是否已绑定流程
        WorkFlow workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId ( tkymmTechImplEx.getTechImplId () );
        if (ObjectUtil.isEmpty(workFlow)) {
//            SWorkFlowUtil.startProcess(loginName,tkymmTechImplEx.getTechImplId(),tkymmTechImplEx.getTechnologyName(), MMConstans.business_type,MMConstans.kymmreward,null,variable,null);
            workFlow = new WorkFlow(loginName, MMConstans.kymmreward, MMConstans.business_type, tkymmTechImplEx.getTechImplId());
        } else {
            //退回提出人
            if (StrUtil.isNotEmpty(tkymmTechImplEx.getImplTransitionKey())) {
                workFlow.setTransitionKey(tkymmTechImplEx.getImplTransitionKey());
                return SWorkFlowUtil.submit(loginName, workFlow);
            }
        }
        //添加流程参数
        workFlow.setVariable(variable);
        workFlow.setBusinessName(tkymmTechImplEx.getTechnologyName());
        //如果申请类型是效益奖则启动效益评审-->移至子流程部门领导

        //判断流程转移
        HashMap<String, Object> queryParam = new HashMap<>();
        queryParam.put("technologyId",tkymmTechImplEx.getTechnologyId());
        List<TkymmTechImpl> tkymmTeches = businessTechimpl.queryList(queryParam);
        TkymmTechnology technology = businessTechnology.query(tkymmTechImplEx.getTechnologyId());

        //第一次报奖、来源为KYXM、未分配专利秘密占比
        if (!PatentUtil.isZb(technology.getSourceNum()) && "KYXM".equals(technology.getSourceType())) {
            workFlow.setTransitionKey("Transition6");
        }else{
            String deptJson = businessTechImplDept.getDeptJson(tkymmTechImplEx.getTechImplId());
            if(StrUtil.isNotEmpty(deptJson)) {
                //创建数组
                ArrayList<SubProcessParam> subProcessParamArrayList = new ArrayList<SubProcessParam>();
                String chice = "";
                ADOrg chiceOrg = null;
                String tKymmBmAdmin = null;
                for (String deptCode : deptJson.split(",")) {
                    SubProcessParam subProcessParam = new SubProcessParam();
                    //会签组织编码
                    subProcessParam.setDepartmentNo(deptCode);
//                    String tKymmBmAdmin = RoleUtil.getUserLabel("KYMM_BM_ADMIN", deptCode);
//                    subProcessParam.setUsers(tKymmBmAdmin);
//                    subVariable.put("submit", 0);
                    HashMap<String, Object> subVariable = new HashMap<>();
                    subVariable.put("orgParamter", deptCode);
                    if (StrUtil.isEmpty(deptCode)) {
                        continue;
                    }
                    //如果存在分厂管理员那么就去分厂管理员
                    List<Map<String, String>> tKymmFcAdmin = RoleUtil.getRoleList(deptCode, "KYMM_FC_ADMIN");
                    if (ObjectUtil.isNotEmpty(tKymmFcAdmin)) {
                        subVariable.put("submit", 1);
                        tKymmBmAdmin = RoleUtil.getUserLabel("KYMM_FC_ADMIN", deptCode);
                        subProcessParam.setUsers(tKymmBmAdmin);
                        chiceOrg = RoleUtil.getOrgByChildOrg(deptCode, "KYMM_FC_ADMIN");
                    }else{
                        subVariable.put("submit", 0);
                        tKymmBmAdmin = RoleUtil.getUserLabel("KYMM_BM_ADMIN", deptCode);
                        subProcessParam.setUsers(tKymmBmAdmin);
                        chiceOrg = RoleUtil.getOrgByChildOrg(deptCode, "KYMM_BM_ADMIN");
                    }
                    //去重
                    if (null != chiceOrg) {
                        if (chice.indexOf(chiceOrg.getOrgCode()) >= 0) {
                            continue;
                        }
                        chice = chice + chiceOrg.getOrgCode() + ";";
                    }
                    subProcessParam.setSubVariables(subVariable);
                    //如果有实施部门需要启动自流程
                    if (1 == (Integer) subProcessParam.getSubVariables().get("submit")) {
                        if (StrUtil.isEmpty(subProcessParam.getUsers())) {
                            String orgName = OrgUtil.getOrgName(deptCode);
                            throw new BusinessException("实施部门:" + orgName + "未配置:分厂管理员(KYMM_FC_ADMIN)");
                        }
                    } else if (0 == (Integer) subProcessParam.getSubVariables().get("submit")) {
                        if (StrUtil.isEmpty(subProcessParam.getUsers())) {
                            String orgName = OrgUtil.getOrgName(deptCode);
                            throw new BusinessException("实施部门:" + orgName + "未配置:部门管理员(KYMM_BM_ADMIN)");
                        }
                    }

                    subProcessParamArrayList.add(subProcessParam);
                }
                //塞入
                workFlow.setSubProcessParamS(Convert.convert(SubProcessParam[].class, subProcessParamArrayList));
                //流转路径
                workFlow.setTransitionKey("Transition2");
            }
        }
        if (StrUtil.isNotEmpty(workFlow.getProcessInstanceId())) {
            return SWorkFlowUtil.submit(loginName, workFlow);
        }
        return SWorkFlowUtil.startProcessAndSubmit(loginName, workFlow);
    }


    /****
     * 启动效益评审
     * @param bizGuid
     * @param jbxx
     * @param xytxns
     * @param xytxne
     * @param sjxs
     */
    public static void startBenefit(String bizGuid,String jbxx,String xytxns,String xytxne,String sjxs,Map<String, Object>  lcMap){
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("bizGuid",bizGuid);
        eiInfo.set("businesCode","bscdkj-benefits");
        eiInfo.set("jbxx",jbxx);
        eiInfo.set("xytxns",xytxns);
        eiInfo.set("xytxne",xytxne);
        eiInfo.set("sjxs",sjxs);
        //新增条件
        eiInfo.set("isjjxy",true);
        eiInfo.set("lcMap",lcMap);
        eiInfo.set("operator",UserSession.getLoginName());
        eiInfo.set ( EiConstant.serviceId, "S_KJ_XY_01" );
        String s = eiInfo.toJSONString();
        EiInfo outInfo = XServiceManager.call ( eiInfo );
        if(-1==outInfo.getStatus()){
            throw new BusinessException("经济效益启动异常:"+outInfo.getMsg());
        }

    }

    /****
     * 提交实施部门子流程
     * @param techImplId
     * @return
     */
    public static String subProcess(String techImplId, String taskId) {
        //提交第一步
//        WorkFlow workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(techImplId);
        WorkFlow workFlow = SWorkFlowUtil.getWorkFlowByTaskId ( taskId );
        if (ObjectUtil.isNotEmpty(workFlow)) {
            //获取实施部门
            String deptJson = businessTechImplDept.getDeptJson(techImplId);
            if (StrUtil.isNotEmpty(deptJson)) {
                //创建数组
                ArrayList<SubProcessParam> subProcessParamArrayList = new ArrayList<SubProcessParam>();
                String chice = "";
                ADOrg chiceOrg = null;
                String tKymmBmAdmin = null;

                for (String deptCode : deptJson.split(",")) {
                    SubProcessParam subProcessParam = new SubProcessParam();
                    //会签组织编码
                    subProcessParam.setDepartmentNo(deptCode);
//                    String tKymmBmAdmin = RoleUtil.getUserLabel("KYMM_BM_ADMIN", deptCode);
//                    subProcessParam.setUsers(tKymmBmAdmin);
                    HashMap<String, Object> subVariable = new HashMap<>();
//                    subVariable.put("submit",0);
                    subVariable.put("orgParamter", deptCode);
                    if (StrUtil.isEmpty(deptCode)) {
                        continue;
                    }
                    //如果存在分厂管理员那么就去分厂管理员
                    List<Map<String, String>> tKymmFcAdmin = RoleUtil.getRoleList(deptCode, "KYMM_FC_ADMIN");
                    if (ObjectUtil.isNotEmpty(tKymmFcAdmin)) {
                        subVariable.put("submit", 1);
                        tKymmBmAdmin = RoleUtil.getUserLabel("KYMM_FC_ADMIN", deptCode);
                        subProcessParam.setUsers(tKymmBmAdmin);
                        chiceOrg = RoleUtil.getOrgByChildOrg(deptCode, "KYMM_FC_ADMIN");
                    } else {
                        subVariable.put("submit", 0);
                        tKymmBmAdmin = RoleUtil.getUserLabel("KYMM_BM_ADMIN", deptCode);
                        subProcessParam.setUsers(tKymmBmAdmin);
                        chiceOrg = RoleUtil.getOrgByChildOrg(deptCode, "KYMM_BM_ADMIN");
                    }
                    //去重
                    if (null != chiceOrg) {
                        if (chice.indexOf(chiceOrg.getOrgCode()) >= 0) {
                            continue;
                        }
                        chice = chice + chiceOrg.getOrgCode() + ";";
                    }

                    subProcessParam.setSubVariables(subVariable);
                    //如果有实施部门需要启动自流程
                    if (1 == (Integer) subProcessParam.getSubVariables().get("submit")) {
                        if (StrUtil.isEmpty(subProcessParam.getUsers())) {
                            String orgName = OrgUtil.getOrgName(deptCode);
                            throw new BusinessException("实施部门:" + orgName + "未配置:分厂管理员(KYMM_FC_ADMIN)");
                        }
                    } else if (0 == (Integer) subProcessParam.getSubVariables().get("submit")) {
                        if (StrUtil.isEmpty(subProcessParam.getUsers())) {
                            String orgName = OrgUtil.getOrgName(deptCode);
                            throw new BusinessException("实施部门:" + orgName + "未配置:部门管理员(KYMM_BM_ADMIN)");
                        }
                    }

                    subProcessParamArrayList.add(subProcessParam);
                }
                //塞入
                workFlow.setSubProcessParamS(Convert.convert(SubProcessParam[].class, subProcessParamArrayList));
            }
        }
        //提交下一步
        String nextUser = SWorkFlowUtil.submit(UserSession.getLoginName(), workFlow);
        return nextUser;
    }


    /****
     * 获取来源详细链接
     * @param tkymmTechnologyEx
     */
    public static  void getSourceUrl(TkymmTechnologyEx tkymmTechnologyEx){
        String sourceType = tkymmTechnologyEx.getSourceType();
        if(MMConstans.NO_SOURCE.equals(sourceType)||MMConstans.OTHER_SOURCE.equals(sourceType)){

        }else{
            List<Map<String, Object>> dictList = SDictUtil.getDictList("MPTY", "BUSINESS_DETAIL");
            if(ObjectUtil.isNotEmpty(dictList)){
                Map<String, Object> map = dictList.stream().filter(f -> (sourceType + "_DETAIL").equals(f.get("dictCode"))).findFirst().get();
                if(ObjectUtil.isNotEmpty(map)){
                        String url= (String)map.get("dictValue");
                        tkymmTechnologyEx.setSourceName(  " <a href='"+url+"' style=\"color:blue;\" >"+tkymmTechnologyEx.getSourceName()+"</a>");
                }
            }
            //如果后期需要获取同来源的信息照着上面写就好

        }
    }

    /**
     * 计算衰减系数(按10%衰减)
     * @return
     */
    public static String calculateSjxs(String techImplId) {
        double sjxs = 0.9;
        TkymmTechImpl tkymmTech = businessTechimpl.query(techImplId);
        HashMap<String, Object> param = new HashMap<>();
        param.put("technologyId", tkymmTech.getTechnologyId());
        List<TkymmTechImpl> tkymmTeches = businessTechimpl.queryList(param);
        sjxs = sjxs - (0.1 * (tkymmTeches.size() - 1));
        return String.valueOf(sjxs);
    }

    /**
     * 根据来源编号获取专利秘密占比信息
     * @param sourceNum
     * @return
     */
    public static List<Map<String,Object>> getZLMMRatioInfo(String sourceNum) {

        //获取同来源专利与技术秘密信息
        List<Map<String, Object>> zlmmList = new ArrayList<>();
        if (StrUtil.isEmpty(sourceNum)) {
//            throw new BusinessException("来源编号不能为空");
            return zlmmList;
        }

        //同来源专利
        List<Map<String, Object>> zlList = CommonUtli.getZLBySourceNo(sourceNum,"");
        //同来源秘密
        List<Map<String, Object>> mmList = CommonUtli.getMMBySourceNo(sourceNum);
        Map<String, Object> param = null;
        int index = 1;
        //专利
        for (Map<String, Object> zl : zlList) {
            param = new HashMap<>();
            param.put("projectId", zl.get("applyId"));
            param.put("projectXh", index);
            param.put("projectName", zl.get("applyName"));
            param.put("projectDept", zl.get("firstDeptName"));
            param.put("projectType", "专利");
            param.put("ratio", zl.get("extra10"));
            param.put("projectNum", zl.get("jsbh"));
            param.put("projectStatus", SDictUtil.getDictName("KIZL", "KI_FLZT", (String) zl.get("flzt")));
            zlmmList.add(param);
            index++;
        }
        //技术秘密
        for (Map<String, Object> mm : mmList) {
            param = new HashMap<>();
            param.put("projectId", mm.get("technologyId"));
            param.put("projectXh", index);
            param.put("projectName", mm.get("technologyName"));
            param.put("projectDept", mm.get("firstdeptName"));
            param.put("projectType", "技术秘密");
            param.put("ratio", mm.get("extra6"));
            param.put("projectNum", mm.get("confirmNum"));
            switch ((String) mm.get("status")) {
                case "agree":
                    param.put("projectStatus", "同意");
                    break;
                case "noagree":
                    param.put("projectStatus", "不同意");
                    break;
                case "negative":
                    param.put("projectStatus", "否定");
                    break;
                case "identified":
                    param.put("projectStatus", "认定");
                    break;
                default:
                    param.put("projectStatus", "");
            }
            zlmmList.add(param);
            index++;
        }
        return zlmmList;
    }

    //根据来源编号判断是否已进行专利秘密占比分配
    public static boolean isSaveRatio(String sourceNum) {

        if (!"KYXM".equals(sourceNum)) {
            return true;
        }

        Map<String, Object> zlParam = new HashMap<>();
        zlParam.put("fromNo", sourceNum);
        zlParam.put("patentStatus", "06");//已授权
        List<TkizlPatentInfo> zlList = businessPatentInfo.queryList(zlParam);
        Map mmParam = new HashMap();
        mmParam.put("sourceNum", sourceNum);
        mmParam.put("status", "identified");//已认定
        List<TkymmTechnology> mmList = businessTechnology.queryList(mmParam);
        //专利
        if (zlList != null && zlList.size() > 0) {
            for (TkizlPatentInfo zl : zlList) {
                TkizlApplyBaseinfo zlBase = businessApplyBaseinfo.load(zl.getApplyId());
                if (ObjectUtil.isNotEmpty(zlBase.getExtra10()) && !zlBase.getExtra10().equals("")) {
                    return true;
                }
            }
        }
        //技术秘密
        if (mmList != null && mmList.size() > 0) {
            for (TkymmTechnology mm : mmList) {
                if (StrUtil.isNotEmpty(mm.getExtra6())) {
                    return true;
                }
            }
        }
        return false;
    }


    /****
     * 实施奖 专家评审 基本信息加密
     * @param
     * @return
     */
    public static String base64BaseApplyInfo(String techImplId, String technologyId,String techImplDeptId) {
        //返回结果
        Map<String, Object> result = new HashMap<>();
        if (StrUtil.isNotEmpty(techImplId)  && StrUtil.isNotEmpty(technologyId)) {
            BusinessTechImpl businessTech = SpringUtil.getBean(BusinessTechImpl.class);
            BusinessTechnology businessTechnology = SpringUtil.getBean(BusinessTechnology.class);
            TkymmTechImpl techImpl = businessTech.query(techImplId);
            TkymmTechnology technology = businessTechnology.query(technologyId);

            if (techImpl != null && technology != null) {
                createBase64(techImpl, technology, techImplDeptId, result);
            }
            JSON parse = JSONUtil.parse(result);
            String s = parse.toString();
            String encodeUrlSafe = Base64.encodeUrlSafe(parse.toString());
            return encodeUrlSafe;
        }
        return null;
    }

    /****
     * 生成baseinfo
     * @param
     * @param baseMap
     */
    private static void createBase64(TkymmTechImpl tkymmTechImpl, TkymmTechnology tkymmTechnology, String techImplDeptId, Map<String, Object> baseMap) {
        if (tkymmTechImpl != null) {
            ArrayList<Map<String, String>> objects = new ArrayList<>();
            HashMap<String, String> baseInfoMap = new HashMap<>();
            baseInfoMap.put("name", "技术秘密名称");
            baseInfoMap.put("value", tkymmTechnology.getTechnologyName());
            objects.add(baseInfoMap);


            HashMap<String, String> baseInfoMap2 = new HashMap<>();
            baseInfoMap2.put("name", "申请类型");
            baseInfoMap2.put("value", SDictUtil.getDictName(MMConstans.business_type, "applyType", tkymmTechImpl.getApplyType()));
            objects.add(baseInfoMap2);

            HashMap<String, String> baseInfoMap3 = new HashMap<>();
            baseInfoMap3.put("name", "提出单位");
            String applyGh = tkymmTechImpl.getApplyUserGh();
            ADOrg userOrg = OrgUtil.getMainOrgByUserCode(applyGh);
            if (ObjectUtil.isNotEmpty(userOrg)) {
                baseInfoMap3.put("value", userOrg.getOrgPathName());
            } else {
                baseInfoMap3.put("value", tkymmTechnology.getFirstdeptName());
            }
            objects.add(baseInfoMap3);

            //新增显示流程名称
//            baseMap.put(tkymmTechImpl.getTechImplId() + "Name", "技术秘密实施奖申请");
//            baseMap.put(tkymmTechImpl.getTechImplId(), objects);
            if (StringUtils.isNotBlank(techImplDeptId)) {//实施部门管理员
                baseMap.put(techImplDeptId + "Name", tkymmTechnology.getTechnologyName());
                baseMap.put(techImplDeptId, objects);
            } else {//主管部门管理员
                baseMap.put(tkymmTechImpl.getTechImplId() + "Name", tkymmTechnology.getTechnologyName());
                baseMap.put(tkymmTechImpl.getTechImplId(), objects);
            }


        }
    }

    /**
     * 效益体现年加一
     * @return str
     */
    public static String yearAddOne(String strTime) {
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = simpleDateFormat.parse(strTime);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        //获取时间加一年或加一月或加一天
        Calendar cal = Calendar.getInstance();
        cal.setTime(date); //设置起时间
        cal.add(Calendar.YEAR, 1); //增加一年
        //cal.add(Calendar.DATE, n); //增加一天
        //cal.add(Calendar.DATE, -10); //减10天
        //cal.add(Calendar.MONTH, n); //增加一个月
        return simpleDateFormat.format(cal.getTime());
    }

    /**
     * 时间计算 timeStr 格式yyyy-MM-dd
     * @return
     */
    public static String dateCalculate(String timeStr,String calType,int num) {
        if (StringUtils.isBlank(timeStr)) {
            return "";
        }
        DateFormat format = new SimpleDateFormat("yyyy-MM-dd");//定义日期格式化的格式
        Date classDate = null;//把字符串转化成指定格式的日期
        try {
            classDate = format.parse(timeStr);
        } catch (ParseException e) {
            e.printStackTrace();
        }

        Calendar calendar = Calendar.getInstance(); //使用Calendar日历类对日期进行加减
        calendar.setTime(classDate);

        if ("yearAdd".equals(calType)) {//年份加num
            calendar.add(Calendar.YEAR, num);
        } else if ("monthAdd".equals(calType)) {//月份加num
            calendar.add(Calendar.MONTH, num);
        } else if ("dayAdd".equals(calType)) {//日期加num
            calendar.add(Calendar.DATE, num);
        }
        classDate = calendar.getTime();//获取加减以后的Date类型日期
        //返回字符串类型
        return format.format(classDate);
    }

    /**
     * 根据业务id和模块code判断是否发起过效益评审流程
     * @return
     */
    public static Boolean isPassXy(String businessId,String moduleCode) {

        if (StrUtil.isBlank(businessId) || StrUtil.isBlank(moduleCode)) {
            throw new BusinessException("业务id或效益模块code不能为空");
        }

        EiInfo eiInfo = new EiInfo();
        eiInfo.set("ywId", businessId);
        eiInfo.set("extra2", moduleCode);

        eiInfo.set("serviceId","S_KJ_XY_03");
        EiInfo call = XServiceManager.call(eiInfo);

        if (EiConstant.STATUS_FAILURE == call.getStatus()) {
            throw new PlatException(call.getMsg());
        }

        List<Map<String, Object>> list = (List<Map<String, Object>>) call.get("list");
        if (list != null && list.size() > 0) {
            return true;
        }
        return false;
    }

    /**
     * 根据来源类型和来源编号及报奖类型判断实施奖是否进行申报
     *
     * @return
     */
    public static boolean isFinishJt(String sourceType, String sourceNum,String applyType) {
        boolean flag = false;
        Map<String, Object> result = new HashMap<>();
        EiInfo eiInfo = null;
        if (StringUtils.isAllBlank(sourceType)) {
            throw new PlatException("来源类型不能为空！");
        }

        switch(sourceType){
            //☆科研项目接口S_KY_XM_025-根据项目号获取效益接口 isJT 1已结题 0未结题 isJJXY 1有经济效益 0无经济效益
            case "KYXM":
                if (StringUtils.isBlank(applyType)) {
                    throw new PlatException("申报类型不能为空！");
                }
                if (StringUtils.isNotBlank(sourceNum)) {
                    eiInfo = new EiInfo();
                    eiInfo.set("projectCode", sourceNum);
                    eiInfo.set(EiConstant.serviceId, "S_KY_XM_025");
                    EiInfo call = XServiceManager.call(eiInfo);

                    String kyType = "";
                    String mSourceNum = "";
                    if (null != call) {
                        kyType = (String) call.get("extra11");//科研项目类型
                        mSourceNum = (String) call.get("mainProjectCode");//母项编号
                    }else {
                        throw new PlatException("该来源编号无法获取科研项目信息,请联系系统管理员！");
                    }
                    if (StringUtils.isNotBlank(kyType)
                            && (MMConstans.PROJECT_TYPE_A.equals(kyType)
                            || MMConstans.PROJECT_TYPE_D.equals(kyType))) {//金苹果或大项目
                        //通过母项编号取值
                        if (StringUtils.isNotBlank(mSourceNum)) {
                            eiInfo.set("projectCode", mSourceNum);
                            call = XServiceManager.call(eiInfo);
                        } else {
                            throw new PlatException("未获取到母项科研项目编号,请联系系统管理员！");
                        }
                    }

                    int status = call.getStatus();
                    if (status == -1) {
                        throw new PlatException("科研项目接口-请求异常" + call.getMsg());
                    }
                    String isJT = call.getString("isJT");
                    String isJJXY = call.getString("isJJXY");
                    if (isJT!=null&&isJT.equals("1")) {
                        if (isJJXY != null) {
                            if (isJJXY.equals("1") && !applyType.equals("benefit_award")) {
                                throw new PlatException("本技术秘密来源科研项目只能申报效益奖！");
                            } else if (isJJXY.equals("0") && !applyType.equals("level_award")) {
                                throw new PlatException("本技术秘密来源科研项目只能申报水平奖！");
                            }
                            //OK

                        } else {
                            throw new PlatException("科研项目接口-获取经济效益信息异常！");
                        }
                        //效益奖判断是否能获取效益体现年
                        //判断能否取到效益体现年 只作用于第一次(实际每次都会判断)
//                        if (applyType.equals("benefit_award")) {
//                            String xyDate = (String) call.get("xyDate");
//                            if (xyDate == null || "".equals(xyDate)) {
//                                BusinessSsjBaseinfoPatent xyBean = SpringUtil.getBean(BusinessSsjBaseinfoPatent.class);
//                                Map<String, Object> paramMap = new HashMap<>();
//                                paramMap.put("projectCode", sourceNum);
//                                List<Map> mapList = xyBean.queryTbsprq(paramMap);
//                                if (mapList != null && mapList.size() > 0) {
//                                    Map mp = mapList.get(0);
//                                    if (mp.get("tbsprq") == null || mp.get("tbsprq").equals("")) {
//                                        throw new PlatException("未获取到经济效益结束日期，请联系系统管理员！");
//                                    }
//                                } else {
//                                    throw new PlatException("未获取到经济效益结束日期，请联系系统管理员！");
//                                }
//                            }
//                        }
                    }else{
                        throw new PlatException("本技术秘密来源科研项目未结题不能申报实施奖！");
                    }
                } else {
                    throw new PlatException("股份科研-来源编号不能为空！");
                }
                flag = true;
                break;
            case "JSTG":
                flag = true;
                break;
            case "JSMY":
                flag = true;
                break;
            case "HLHJY":
                flag = true;
                break;
            case "SIX":
                flag = true;
                break;
            default:
                flag = true;
        }
        return flag;
    }


    /**
     * 根据认定主键查询实施奖历史记录
     * @param technologyId
     * @return
     */
    public static List<Map<String,Object>> getSsjHistory(String technologyId) {

        return businessTechimpl.getSsjHistoryToMap(technologyId);
    }

    /**
     * 管理员手动启动效益评审
     * @return
     */
    public static void startXyProcess(String businessId) {

        WorkFlow workFlow = SWorkFlowUtil.getMainFlowInfoByBusinessId(businessId);
        //判断是否进入等待效益节点
        if (workFlow.getProcessCode().equals(MMConstans.kymmreward) && workFlow.getCurrentActivity().equals(MMConstans.Manual1)) {
            BusinessTechImpl businessTechImpl = SpringUtil.getBean(BusinessTechImpl.class);
            BusinessTechnology businessTechnology = SpringUtil.getBean(BusinessTechnology.class);
            HashMap<String, Object> paramQuery = new HashMap<>();
            paramQuery.put("techImplId", businessId);
            TkymmTechImpl impl = businessTechImpl.query(businessId);
            //申报人管理单位
            String zlOrgCode = "";
            if (ObjectUtil.isNotEmpty(impl)) {
                String applyUserGh = impl.getApplyUserGh();
                ADOrg mainOrgByUserCode = OrgUtil.getMainOrgByUserCode(applyUserGh);
                if (ObjectUtil.isNotEmpty(mainOrgByUserCode)) {
                    ADOrg levelCompany = OrgUtil.getLevelCompany(mainOrgByUserCode.getOrgCode(), 2);
                    if (ObjectUtil.isNotEmpty(levelCompany)) {
                        zlOrgCode = levelCompany.getOrgCode();
                    }
                }
            }
            List<TkymmTechImpl> tkymmTeches = businessTechImpl.queryList(paramQuery);
            if (impl != null) {
                TkymmTechnology technology = businessTechnology.query(impl.getTechnologyId());

                //子u流程结束，启动效益评审
                TkymmTechImpl tkymmTech = impl;
                String applyType = tkymmTech.getApplyType();
                if ("benefit_award".equals(applyType) && !ImplementationAwardUtli.isPassXy(businessId, MMConstans.impl_XY_Module_Code)) {
                    String xytxns = "";
                    String xytxne = "";
                    HashMap<String, Object> parms = new HashMap<>();
                    //基本信息
                    ArrayList<Map<String, Object>> jbxxList = new ArrayList<>();
                    HashMap<String, Object> model = new HashMap<>();
                    model.put("name", "模块");
                    model.put("value", "技术秘密实施奖");
                    jbxxList.add(model);

                    HashMap<String, Object> type = new HashMap<>();
                    type.put("name", "类型");
                    type.put("value", "效益奖");
                    jbxxList.add(type);
                    parms.put(businessId, jbxxList);
                    parms.put("projectGuid", businessId);
                    parms.put("name", technology.getTechnologyName());
                    JSON jbxxJson = JSONUtil.parse(parms);

                    //参数
                    HashMap<String, Object> lcMap = new HashMap<>();
//                    lcMap.put("orgParamter", technology.getFirstdeptCode());
                    lcMap.put("orgParamter", tkymmTech.getExtra4().split(",")[0]);
                    lcMap.put("orgParamter2", tkymmTech.getExtra4().split(",")[0]);
                    lcMap.put("userParamter", tkymmTech.getApplyUserGh());
                    lcMap.put("roleParamter", MMConstans.KYMM_DW_ZGBMGLY);
                    //管理单位
                    lcMap.put("zlOrgCode", zlOrgCode);
                    //传认定日期
                    lcMap.put("zlslrq", technology.getConfirmTime());
                    //是否第一次效益奖
                    if (String.valueOf(impl.getApplyTimes()).equals("1")) {
                        lcMap.put("zlsfdyc", true);
                    } else {
                        lcMap.put("zlsfdyc", false);
                        Map<String, Object> par = new HashMap<>();
                        par.put("technologyId", impl.getTechnologyId());
                        par.put("applyTimes", impl.getApplyTimes() - 1);
                        List<TkymmTechImpl> tkymmTecBeforeList = businessTechImpl.queryList(par);
                        TkymmTechImpl tkymmTecBefore = tkymmTecBeforeList.get(0);
                        xytxns = ImplementationAwardUtli.yearAddOne(tkymmTecBefore.getBenefitYearIndiStart());
                        xytxne = ImplementationAwardUtli.yearAddOne(tkymmTecBefore.getBenefitYearIndiEnd());
                    }

                    //衰减系数计算
                    double sjxs = 1.0 - 0.1 * impl.getApplyTimes();
                    if ("BGTM00".equals(technology.getExtra5()) || "BSTM".equals(technology.getExtra5())) {
                        sjxs = 1.0;
                    }
                    //科研项目 利润分享数据
                    EiInfo shareData = null;
                    EiInfo jtXyData = null;
                    if ("KYXM".equals(technology.getSourceType()) && StringUtils.isNotBlank(technology.getSourceNum())) {
                        shareData = CommonUtli.getShareData(technology.getSourceNum());
                        jtXyData = CommonUtli.getJtXyData(technology.getSourceNum());
                    }
                    //科研项目 结题效益数据
                    //利润分享状态 lrfxStatus : np 未参与 active 申报中 end 已结束；
                    if ("KYXM".equals(technology.getSourceType())) {//科研项目
                        if (null != shareData && shareData.getStatus() != -1 && "end".equals(shareData.get("lrfxStatus"))) {//利润分享已结束
                            sjxs = 0.6;
                            //利润分享结束时间
                            String shareEnd = (String) shareData.get("shareEnd");
                            xytxns = ImplementationAwardUtli.dateCalculate(shareEnd, "dayAdd", 1);
                            xytxne = ImplementationAwardUtli.dateCalculate(xytxns, "yearAdd", 1);
                        } else if (null != jtXyData && String.valueOf(impl.getApplyTimes()).equals("1")//第一次效益奖 且 未参与利润分享
                                && "1".equals(jtXyData.get("isJT"))
                                && "1".equals(jtXyData.get("isJJXY"))) {
                            //科研项目 效益体现年结束日期
                            String xyDate = (String) jtXyData.get("xyDate");
//                            if (xyDate == null || "".equals(xyDate)) {
//                                BusinessSsjBaseinfoPatent bean = SpringUtil.getBean(BusinessSsjBaseinfoPatent.class);
//                                Map<String, Object> paramMap = new HashMap<>();
//                                paramMap.put("projectCode", technology.getSourceNum());
//                                List<Map> mapList = bean.queryTbsprq(paramMap);
//                                if (mapList != null && mapList.size() > 0) {
//                                    Map mp = mapList.get(0);
//                                    if (mp.get("tbsprq") != null && !mp.get("tbsprq").equals("")) {
//                                        xyDate = mp.get("tbsprq") + "";//老数据科研没有日期-去取科研效益财务领导审批日期
//                                    }
//                                }
//                            }
                            xytxns = ImplementationAwardUtli.dateCalculate(xyDate, "dayAdd", 1);
                            xytxne = ImplementationAwardUtli.dateCalculate(xytxns, "yearAdd", 1);
                        }
                    }
                    //启动效益
                    ImplementationAwardUtli.startBenefit(businessId, jbxxJson.toString(), xytxns, xytxne, String.valueOf(sjxs), lcMap);

                } else {
                    //水平奖
                    if ("level_award".equals(applyType)) {
                        throw new PlatException("水平奖不能启动效益评审");
                    }
                    throw new PlatException("已启动效益评审");
                }
            }
        } else {
            throw new PlatException("当前节点不能启动效益评审");
        }
    }
}
