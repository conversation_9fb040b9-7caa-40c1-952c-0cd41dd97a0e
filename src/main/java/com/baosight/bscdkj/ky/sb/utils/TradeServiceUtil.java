package com.baosight.bscdkj.ky.sb.utils;

import cn.hutool.core.util.StrUtil;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.common.domain.Ztree;
import com.baosight.bscdkj.common.exception.BusinessException;
import com.baosight.bscdkj.common.ky.domain.TkysbTradeRegist;
import com.baosight.bscdkj.common.ky.domain.TkysbTradeService;
import com.baosight.bscdkj.common.ky.domain.TkysbTradeState;
import com.baosight.bscdkj.ky.sb.business.BusinessKYSBTradeRegist;
import com.baosight.bscdkj.ky.sb.business.BusinessKYSBTradeService;
import com.baosight.bscdkj.ky.sb.business.BusinessKYSBTradeState;
import com.baosight.iplat4j.core.data.dao.DaoFactory;
import com.baosight.iplat4j.core.data.ibatis.dao.Dao;
import com.baosight.iplat4j.core.util.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
@Controller
@RequestMapping("/insert1")
public class TradeServiceUtil {


    public static String data = "257691\t宝光＋图\t1986年7月30日\t2026年7月29日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  257717\t宝光＋图\t1986年7月30日\t2026年7月29日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  258730\t宝光＋图\t1986年8月10日\t2026年8月9日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  503029\t宝光＋图\t1989年11月10日\t2029年11月9日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  1372511\t宝光＋图\t2000年3月14日\t2030年3月13日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  1153872\tBG图形\t1998年2月21日\t2028年2月20日\t有效\t37\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1155970\tBG图形\t1998年2月28日\t2028年2月27日\t有效\t39\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1165964\tBG图形\t1998年4月7日\t2028年4月6日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1188969\tBG图形\t1998年7月7日\t2028年7月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1507812\tBG图形\t2001年1月14日\t2021年1月13日\t有效\t37\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1555906\tBG图形\t2001年4月14日\t2021年4月13日\t有效\t39\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  3619608\tBG图形\t2005年5月21日\t2025年5月20日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1153873\t宝钢\t1998年2月21日\t2028年2月20日\t有效\t37\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1155980\t宝钢\t1998年2月28日\t2028年2月27日\t有效\t39\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1165965\t宝钢\t1998年4月7日\t2028年4月6日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1171126\t宝钢\t1998年4月28日\t2028年4月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1507811\t宝钢\t2001年1月14日\t2021年1月13日\t有效\t37\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1555903\t宝钢\t2001年4月14日\t2021年4月13日\t有效\t39\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1656116\t宝钢\t2001年10月28日\t2021年10月27日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  3619254\t宝钢\t2009年1月28日\t2029年1月27日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1153884\tBAOSTEEL\t1998年2月21日\t2028年2月20日\t有效\t37\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1155981\tBAOSTEEL\t1998年2月28日\t2028年2月27日\t有效\t39\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1180015\tBAOSTEEL\t1998年6月7日\t2028年6月6日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1507813\tBAOSTEEL\t2001/1/14\t2021年1月13日\t有效\t37\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1555904\tBAOSTEEL\t2001年4月14日\t2021年4月13日\t有效\t39\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1656115\tBAOSTEEL\t2001年10月28日\t2021年10月27日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  3619253\tBAOSTEEL\t2009年1月28日\t2029年1月27日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1511865\tBG图形+宝钢\t2001/1/21\t2021年1月20日\t有效\t37\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1555905\tBG图形+宝钢\t2001年4月14日\t2021年4月13日\t有效\t39\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  3619607\tBG图形+宝钢\t2009年1月28日\t2029年1月27日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  6842439\t宝BG钢\t2010年4月28日\t2030年4月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t主商标\n" +
            ",  1157231\t宝日\t1998年3月7日\t2028年3月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  4248009\t宝日图形\t2007年2月7日\t2027年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  4254941\t宝日彩色图形\t2007年2月14日\t2027年2月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  4254940\t宝日图形及BNA\t2008年4月14日\t2028年4月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  4254938\t宝日图形及宝钢新日铁汽车板有限公司\t2009年5月28日\t2029年5月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  6726808\t宝日图形＋宝日汽车板\t2010年3月28日\t2030年3月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  27674352\t图形+BNA BAOSTEEL & NSC\t2018年11月14日\t2028年11月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  5576478\t宝钢汽车板\t2009年6月28日\t2029年6月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  7673730\t宝钢彩涂板\t2010年11月28日\t2030年11月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  9208987\t宝钢镀锡板\t2012年3月21日\t2022年3月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  9208995\t宝钢冷轧板\t2012年3月21日\t2022年3月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  9209001\t宝钢镀锌板\t2012年3月21日\t2022年3月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241155\tBQB\t2010年2月21日\t2030年2月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  6241158\tBQB\t2010年3月28日\t2030年3月27日\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  6241161\tBQB图形\t2011年4月7日\t2021年4月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  6241164\tBQB图形\t2010年3月28日\t2030年3月27日\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  6241165\tEco-P\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241166\tEco-C\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241167\tE-Coating\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241168\tEco-Coating\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241170\tG-Coating\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241171\tGreen-Coating\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241173\tSUSAZ\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241174\tSUS-AZ\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241175\tSustainable AZ\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241176\tSUPAZ\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241177\tSuper-AZ\t2010年2月21日\t2030年2月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241178\tSuper AZ\t2010年2月21日\t2030年2月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241179\tExcl-AZ\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241180\tExclusive-AZ\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  10103033\tEAZ\t2012年12月21日\t2022年12月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241181\tSGO\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241182\tBao-Core\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241183\t宝科\t2010年2月21日\t2030年2月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241184\tSebon\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241186\t森邦\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241187\tChromfre\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241188\tHex-Crfre\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241189\tCr6+-Free\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241190\tHex-Chrom-Fre\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241191\t科慕斐\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241192\t科慕蜚\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241400\t科慕菲\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6241401\t科拇斐\t2010年2月7日\t2030年2月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6355465\tBSSF\t2010年2月28日\t2030年2月27日\t有效\t7\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t装备设备\n" +
            ",  6355464\tBSSF\t2011年4月7日\t2021年4月6日\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  6355463\tBAOSTRIP\t2010年2月28日\t2030年2月27日\t有效\t7\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t装备设备\n" +
            ",  6355462\tBAOSTRIP\t2010年3月28日\t2030年3月27日\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  6355461\tBSP\t2010年2月28日\t2030年2月27日\t有效\t7\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t装备设备\n" +
            ",  6355460\tBSP\t2011年1月28日\t2021年1月27日\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  6920483\t宝赫迪\t2010年5月14日\t2030年5月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6920484\t宝拓迪\t2010年5月14日\t2030年5月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6920485\t宝铠迪\t2010年5月14日\t2030年5月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6920486\t宝甲迪\t2010年5月14日\t2030年5月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6920487\t宝威迪\t2010年5月14日\t2030年5月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6924451\tWELDY\t2010年5月21日\t2030年5月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6924452\tARMOY\t2010年5月21日\t2030年5月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6924453\tTOOLY\t2010年5月21日\t2030年5月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6924454\tBWELDY\t2010年5月21日\t2030年5月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6924455\tBARMOY\t2010年5月21日\t2030年5月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6924456\tBTOOLY\t2010年5月21日\t2030年5月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  6924457\tBHARDY\t2010年5月21日\t2030年5月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  7673712\tantimicroB\t2010年11月28日\t2030年11月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  7673691\tKB图形\t2010年11月28日\t2030年11月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  8090432\tBaoVision\t2011年3月28日\t2021年3月27日\t有效\t9\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t装备设备\n" +
            ",  8090447\tBaoVision\t2011年10月21日\t2021年10月20日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  7673635\t宝钢不锈钢\t2012年5月28日\t2022年5月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  7673684\t抗菌宝\t2012年6月14日\t2022年6月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  8360804\tNSGO\t2011年6月14日\t2031年6月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  8360814\tBG图形+NSGO\t2011年6月14日\t2031年6月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  8760528\t倍凯\t2011年10月28日\t2021年10月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  8760556\t倍凯\t2011年10月28日\t2021年10月27日\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  8760589\t倍凯\t2011年10月28日\t2021年10月27日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  8760537\tFle-Form\t2011年10月28日\t2021年10月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  8760548\tFle-Form\t2011年10月28日\t2021年10月27日\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  8760652\tFle-Form\t2011年10月28日\t2021年10月27日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  10662207\tBaoBlank\t2013年6月14日\t2023年6月13日\t有效\t7\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t装备设备\n" +
            ",  10662711\tBaoBlank\t2013年7月7日\t2023年7月6日\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  10662750\tBaoLaser\t2013年7月7日\t2023年7月6日\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  10662231\tBaoLaser\t2013年6月14日\t2023年6月13日\t有效\t7\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t装备设备\n" +
            ",  10662272\t宝络\t2013年5月14日\t2023年5月13日\t有效\t7\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t装备设备\n" +
            ",  10662677\t宝络\t2013年7月7日\t2023年7月6日\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  10662506\t雷卡\t2013年7月7日\t2023年7月6日\t有效\t7\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t装备设备\n" +
            ",  10662546\t雷卡\t2013年7月7日\t2023年7月6日\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  12449716\tBQHT\t2014年9月21日\t2024年9月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  12450075\tBQHT\t2014年9月21日\t2024年9月20日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  12449727\t宝库\t2014年9月21日\t2024年9月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  12450063\tBUFC\t2014年9月21日\t2024年9月20日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  12984981\tVRB\t2015年2月14日\t2025年2月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  12985016\tVRB\t2015年1月28日\t2025年1月27日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  13374531\tBQP\t2015年1月14日\t2025年1月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  13131040\tB-DRC\t2015年1月14日\t2025年1月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  13131108\tB-DRC\t2014年12月28日\t2024年12月27日\t有效\t7\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t装备设备\n" +
            ",  14066841\t宝钢构筑共享价值\t2015年9月7日\t2025年9月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t企业文化\n" +
            ",  14066842\t宝钢构筑共享价值\t2015年9月7日\t2025年9月6日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t企业文化\n" +
            ",  14066839\t宝钢构筑共享价值\t2015年4月21日\t2025年4月20日\t有效\t39\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t企业文化\n" +
            ",  14066840\t宝钢构筑共享价值\t2015年8月7日\t2025年8月6日\t有效\t37\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t企业文化\n" +
            ",  14066838\tBAOSTEEL Make you life more valuable\t2015年9月7日\t2025年9月6日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t企业文化\n" +
            ",  14066837\tBAOSTEEL Make you life more valuable\t2015年8月7日\t2025年8月6日\t有效\t37\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t企业文化\n" +
            ",  14066947\tBAOSTEEL Make you life more valuable\t2015年4月21日\t2025年4月20日\t有效\t39\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t企业文化\n" +
            ",  14066845\t宝钢真诚友爱创造力\t2015年9月7日\t2025年9月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t企业文化\n" +
            ",  14066946\t宝钢真诚友爱创造力\t2015年9月7日\t2025年9月6日\t有效\t1\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t企业文化\n" +
            ",  14066843\t宝钢真诚友爱创造力\t2015年4月21日\t2025年4月20日\t有效\t39\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t企业文化\n" +
            ",  14066844\t宝钢真诚友爱创造力\t2015年8月7日\t2025年8月6日\t有效\t37\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t企业文化\n" +
            ",  15998709\t宝钢家电板\t2016年2月21日\t2026年2月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  15998704\t宝钢电工钢\t2016年2月21日\t2026年2月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  15998652\t宝钢镀锡板\t2016年2月21日\t2026年2月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  13009366\t博美达\t2016年10月21日\t2026年10月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  22311136\t易磁加\t2018年1月28日\t2028年1月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  22311486\t易磁加\t2018年1月28日\t2028年1月27日\t有效\t7\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t装备设备\n" +
            ",  22311748\t易磁加\t2018年1月28日\t2028年1月27日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  22311189\tBAO EMS+\t2018年1月28日\t2028年1月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  22311569\tBAO EMS+\t2018年1月28日\t2028年1月27日\t有效\t7\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t装备设备\n" +
            ",  22311730\tBAO EMS+\t2018年1月28日\t2028年1月27日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  22311242\tBAO EMS Plus\t2018年1月28日\t2028年1月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  22311451\tBAO EMS Plus\t2018年1月28日\t2028年1月27日\t有效\t7\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t装备设备\n" +
            ",  22311784\tBAO EMS Plus\t2018年1月28日\t2028年1月27日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  24909838\t宝特赛\t2018年7月7日\t2028年7月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  24924829\t宝特赛\t2018年7月7日\t2028年7月6日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  24912032\tBAOTEX\t2018年10月14日\t2028年10月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  34263138\t小钢宝\t2019年7月21日\t2029年7月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  34275299\t钢小宝\t2019年6月28日\t2029年6月27日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  37046169\tBAOTEX\t2020年1月14日\t2030年1月13日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  41160162\t吉帕钢\t2020年5月21日\t2030年5月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  41136078\t吉帕钢\t2020年5月21日\t2030年5月20日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  41142765\tX-GPa\t2020年5月21日\t2030年5月20日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  41147534\tX-GPa\t2020年5月21日\t2030年5月20日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  41156644\tBaoQP\t2020年5月14日\t2030年5月13日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  41140931\tBaoQP\t2020年5月14日\t2030年5月13日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  41151832\tGALVALUMAG\t2020年5月21日\t2030年5月20日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  41399190\tBaoZM\t2020年6月7日\t2030年6月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  41405906\tBaoZM\t2020年6月7日\t2030年6月6日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  41385109\tBaoXM\t2020年6月7日\t2030年6月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  41378659\tBaoXM\t2020年6月7日\t2030年6月6日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  41389304\tBaoAM\t2020年6月7日\t2030年6月6日\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  41394044\tBaoAM\t2020年6月7日\t2030年6月6日\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  45853161\t数字钢\t2021/2/14\t2031/2/13\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  45834327\t数字钢\t2020/12/14\t2030/12/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  50968863\t数据钢\t2021/7/28\t2031/7/27\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  50957225\tBaoD.Coil\t2021/7/7\t2031/7/6\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  50942886\tBaoD.Coil\t2021/8/14\t2031/8/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  50409358\tHiBri\t2021/9/7\t2031/9/6\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  50424166\tHiBri\t2021/6/14\t2031/6/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  50404927\tRcorBri\t2021/6/21\t2031/6/20\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  50412797\tRcorBri\t2021/6/14\t2031/6/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  50409371\tOcorBri\t2021/6/14\t2031/6/13\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  50406471\tOcorBri\t2021/6/14\t2031/6/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  50423830\tCladBri\t2021/6/14\t2031/6/13\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  50395992\tCladBri\t2021/6/14\t2031/6/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  50423835\tIMECBri\t2021/6/14\t2031/6/13\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  50402788\tIMECBri\t2021/6/14\t2031/6/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  50406436\tCableBri\t2021/6/14\t2031/6/13\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  50424195\tCableBri\t2021/6/14\t2031/6/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  50416924\tBriWeld\t2021/6/14\t2031/6/13\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  50424200\tBriWeld\t 2021/9/7\t2031/9/6\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  53897675\tBeCOREs\t2021/9/14\t2031/9/13\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  53900375\tBeCOREs\t2021/12/14\t2031/12/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  53909720\tBeyondECO\t2021/12/14\t2031/12/13\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  55462737\t洁白\t2022/2/7\t2032/2/6\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  55452241\t洁白\t2021/11/28\t2031/11/27\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  55484532\tHygiSteel\t2021/11/28\t2031/11/27\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  55452251\tHygiSteel\t2021/11/21\t2031/11/20\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  62156022\t丹霞钢\t2022/9/28\t2032/9/27\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  62160184\t丹霞钢\t2022/7/14\t2032/7/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  62161744\t宝友爱\t2022/7/14\t2032/7/13\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  62167732\t宝友爱\t2022/7/14\t2032/7/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  62161330\tBeKIND\t2022/9/28\t2032/9/27\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  62148843\tBeKIND\t2022/9/28\t2032/9/27\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  62155037\tDanXia\t2022/9/28\t2032/9/27\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  62167770\tHiCON\t2022/9/28\t2032/9/27\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  62308117\tiM-EVI\t2022/7/14\t2032/7/13\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  62303692\tiM-EVI\t2022/7/14\t2032/7/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  62325739\tBCB EV\t2022/7/21\t2032/7/20\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  62309713\tBCB EV\t2022/7/14\t2032/7/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  62308126\tG-EVI\t2022/9/21\t2032/9/20\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  62319287\tG-EVI\t2022/7/21\t2032/7/20\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  62309391\tB-in META\t2022/10/14\t2032/10/13\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  62504669\tBao-HiS\t2022/7/28\t2032/7/27\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  62512374\tBao-HiS\t2022/8/14\t2032/8/13\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  62521342\tAnti-SRB\t2022/7/28\t2032/7/27\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  62504709\tAnti-SRB\t2022/7/28\t2032/7/27\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  62506123\tPremCoN\t2022/7/28\t2032/7/27\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  62509439\tPremCoN\t2022/7/28\t2032/7/27\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  62513885\t吉帕管\t2022/7/28\t2032/7/27\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t钢铁产品\n" +
            ",  62514268\t吉帕管\t2022/7/28\t2032/7/27\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国\t\n" +
            ",  01294561\t宝钢\t2008/1/1\t2027/12/31\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国台湾\t主商标\n" +
            ",  01286372\t宝钢\t2007/11/1\t2027/10/31\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国台湾\t主商标\n" +
            ",  01294560\t寶鋼\t2008/1/1\t2027/12/31\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国台湾\t主商标\n" +
            ",  01286371\t寶鋼\t2007/11/1\t2027/10/31\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国台湾\t主商标\n" +
            ",  01273778\tBAOSTEEL\t2007/8/1\t2027/7/31\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国台湾\t主商标\n" +
            ",  01273778\tBAOSTEEL\t2007/8/1\t2027/7/31\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国台湾\t主商标\n" +
            ",  01273779\tBG图形\t2007/8/1\t2027/7/31\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国台湾\t主商标\n" +
            ",  01273779\tBG图形\t2007/8/1\t2027/7/31\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t中国台湾\t主商标\n" +
            ",  018346550\tBAOSTEEL\t2021/3/17\t2031/3/16\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t欧盟\t主商标\n" +
            ",  018346550\tBAOSTEEL\t2021/3/17\t2031/3/16\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t欧盟\t主商标\n" +
            ",  018346550\tBAOSTEEL\t2021/3/17\t2031/3/16\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t欧盟\t主商标\n" +
            ",  018346551\tBG图形\t2021/3/17\t2031/3/16\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t欧盟\t主商标\n" +
            ",  018346551\tBG图形\t2021/3/17\t2031/3/16\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t欧盟\t主商标\n" +
            ",  018346551\tBG图形\t2021/3/17\t2031/3/16\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t欧盟\t主商标\n" +
            ",  018346552\t宝钢\t2021/3/17\t2031/3/16\t有效\t6\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t欧盟\t主商标\n" +
            ",  018346552\t宝钢\t2021/3/17\t2031/3/16\t有效\t40\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t欧盟\t主商标\n" +
            ",  018346552\t宝钢\t2021/3/17\t2031/3/16\t有效\t42\t宝山钢铁股份有限公司\t上海市宝山区富锦路885号\t欧盟\t主商标\n";


    @Autowired
    BusinessKYSBTradeService businessKYSBTradeService;
    @Autowired
    BusinessKYSBTradeRegist businessKYSBTradeRegist;
    @Autowired
    BusinessKYSBTradeState businessKYSBTradeState;
    /**
     * 查询法人信息列表 /insert1/loadRegist
     */
    @PostMapping("/loadRegist")
    @ResponseBody
    public TableDataInfo list() throws ParseException {
        String [] hangs = data.split(",");
        for (int i =0;i<hangs.length;i++){
            String hang1= hangs[i];
            String [] hang = hang1.split("\t");
            TkysbTradeRegist regist = new TkysbTradeRegist();
            regist.setStatus("end");
            regist.setDelStatus("0");
            regist.setDeptAddress(hang[7]);
            regist.setOwnership(hang[6]);
            regist.setCountryRegion("CHN");
            regist.setTrademarkName(hang[1]);
            Map<String, Object> hashMap = new HashMap<>();
            String name  = hang[5];
            if(name.length()==1) name = "0"+name;
            hashMap.put("dynSql", "SERVICE_NAME LIKE '%" + name + "%'" );
            hashMap.put("serviceLevel",1);
            List<TkysbTradeService> list = businessKYSBTradeService.queryList(hashMap);
            regist.setTrademarkRegistType(list.get(0).getTradeServiceId());
            String lable = "";
            String lavleName="";
            if(hang.length==10){
                lavleName=hang[9];
                if("主商标\n".equals(lavleName)){
                    lable="ZSB";
                }
                if("工艺技术\n".equals(lavleName)){
                    lable="GYJS";
                }
                if("钢铁产品\n".equals(lavleName)){
                    lable="GTCP";
                }
                if("装备设备\n".equals(lavleName)){
                    lable="ZBSB";
                }
                if("企业文化\n".equals(lavleName)){
                    lable="QYWH";
                }
                if("其他\n".equals(lavleName)){
                    lable="OTHER";
                }
            }
            regist.setTrademarkLabel(lable);
            regist.setApplyDeptPh("26649289");
            TkysbTradeRegist regist1 = businessKYSBTradeRegist.insert("admin",regist);

            TkysbTradeState tradeState = new TkysbTradeState();
            tradeState.setTradeRegistId(regist1.getTradeRegistId());
            tradeState.setRegigterNo(hang[0].trim());
            tradeState.setDelStatus("0");

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String resterDate = hang[2];
            String star =resterDate.trim()
                    .replace("/","-")
                    .replace("年","-")
                    .replace("月","-")
                    .replace("日","");
            Date date1111 = sdf.parse(star);
            tradeState.setInitRegisterDate(sdf.format (date1111));
            String endDATE = hang[3].trim();
            String enddate = endDATE.replace("/","-")
                    .replace("年","-")
                    .replace("月","-")
                    .replace("日","");
            Calendar cal = Calendar.getInstance();
            Date date = sdf.parse(enddate);
            cal.setTime(date);;
            cal.add(Calendar.YEAR,-10);
            String strTime2 = sdf.format (cal.getTime()) ;
            tradeState.setRegisterDate(strTime2);
            tradeState.setEndDate(sdf.format (date));
            tradeState.setLawStatus("valid");
            businessKYSBTradeState.insert("admin",tradeState);
            System.out.println(list.get(0).getTradeServiceId());
            System.out.println(hang[1]);
        }

        return null;
    }

    /**
     * 获取附件树
     * @return
     */
    public static List<Ztree> getServiceAllZtree(String parentCode, Integer level, String values) {
        List<Map<String, Object>> query = queryFileTree( level,parentCode);
        List<Ztree> ztrees = new ArrayList<Ztree>();
        for (Map<String, Object> map : query) {
            Ztree ztree = new Ztree();
            ztree.setId((String) map.get("orgId"));
            ztree.setpId((String) map.get("parentOrgId"));
            ztree.setCode((String) map.get("orgCode"));
            ztree.setOpen(false);
            ztree.setCode((String) map.get("orgCode"));
            Integer num = (Integer) map.get("num");
            if(num!=null && num>0 ){
                ztree.setIsParent(true);
            }
            if (StrUtil.isNotBlank(values)) {
                List<String> asList = Arrays.asList(values.split(","));
                if (asList.contains(ztree.getId())) {
                    ztree.setChecked(true);
                }
            }
            Integer levelorg = (Integer) map.get("orgLevel");
            if(!levelorg.equals(3)){
                ztree.setNocheck(true);
            }
            ztree.setName((String) map.get("orgName"));
            ztree.setTitle((String) map.get("orgName"));
            ztrees.add(ztree);
        }
        return ztrees;
    }
    /**
     * 查詢附件樹
     * @return
     */
    private static List<Map<String,Object>> queryFileTree(Integer level,String parentCode){
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        Map<String,Object> map = new HashMap<>(3);
        map.put("level", level);
        map.put("parentCode", parentCode);
        return platSqlDao.query("tkysbTreeFile.queryFileTree", map);
    }

    public static String getServiceName(String orgCode){
        String separator = ",";
        if(!StringUtils.isNotEmpty(orgCode)) return "";
        String[] split = orgCode.split(",");
        StringBuffer sb = new StringBuffer("");
        for (String string : split) {
          //  ADOrg adOrg = OrgUtil.getOrgByOrgCode(string);
            Dao platSqlDao = DaoFactory.getPlatSqlDao();
            Map<String,Object> map = new HashMap<>();
            map.put("tradeServiceId", string);
            List<Map<String,Object>> map1= platSqlDao.query("tkysbTradeService.queryByTradeServiceId", map);
            if(map1==null||map1.size()<=0){
                return "";
            }
            Map<String,Object> adOrg =  map1.get(0);
            if(adOrg!=null) {
                if(sb.length()>0)
                    sb.append(separator);
                String orgPathName = (String) adOrg.get("serviceName");
                if(StringUtils.isNotEmpty(orgPathName) && orgPathName.trim().startsWith("/"))
                    orgPathName = orgPathName.trim().substring(1);
                sb.append(orgPathName);
            }
        }
        return sb.toString();
    }



    /**
     * 获取组织树
     * @param parentCode 父组织
     * @param level 组织树最多层级
     * @param values 回显值 ，隔开

     * @return
     */
    public static List<Ztree> getTecAllZtree(String parentCode, Integer level, String values,String type) {
        List<Map<String, Object>> query = queryTecChildByOrgId( level,parentCode,type);
        List<Ztree> ztrees = new ArrayList<Ztree>();
        for (Map<String, Object> map : query) {
            Ztree ztree = new Ztree();
            ztree.setId((String) map.get("orgId"));
            ztree.setpId((String) map.get("parentOrgId"));
            ztree.setCode((String) map.get("orgCode"));
            ztree.setOpen(false);
            ztree.setCode((String) map.get("orgCode"));
            Integer num = (Integer) map.get("num");
            if(num!=null && num>0 ){
                ztree.setIsParent(true);
            }
            if (StrUtil.isNotBlank(values)) {
                List<String> asList = Arrays.asList(values.split(","));
                if (asList.contains(ztree.getId())) {
                    ztree.setChecked(true);
                }
            }
            Integer levelorg = (Integer) map.get("orgLevel");
            if(!levelorg.equals(2)){
                ztree.setNocheck(true);
            }
            ztree.setName((String) map.get("orgName"));
            ztree.setTitle((String) map.get("orgName"));
            ztrees.add(ztree);
        }
        return ztrees;
    }
    /**
     * 根据组织id 查询所有子节点
     * @param level 最大层级
     * @param parentCode 父组织
     * @return
     */
    private static List<Map<String,Object>> queryTecChildByOrgId(Integer level,String parentCode,String type){
        Dao platSqlDao = DaoFactory.getPlatSqlDao();
        Map<String,Object> map = new HashMap<>(3);
        map.put("level", level);
        map.put("parentCode", parentCode);
        if(type!=null && type.equals("1")){
            map.put("type", type);
        }
        return  platSqlDao.query("tkysbTradeTechnical.queryTecChildByorgCode", map);
    }

    public static String getTechnicalName(String orgCode){
        String separator = ",";
        if(!StringUtils.isNotEmpty(orgCode)) return "";
        String[] split = orgCode.split(",");
        StringBuffer sb = new StringBuffer("");
        for (String string : split) {
            //  ADOrg adOrg = OrgUtil.getOrgByOrgCode(string);
            Dao platSqlDao = DaoFactory.getPlatSqlDao();
            Map<String,Object> map = new HashMap<>();
            map.put("tradeTechnicalId", string);
            List<Map<String,Object>> map1= platSqlDao.query("tkysbTradeTechnical.queryByTradeTecId", map);
            if(map1==null||map1.size()<=0){
                throw new BusinessException("技术类别不存在");
            }
            Map<String,Object> adOrg =  map1.get(0);
            if(adOrg!=null) {
                if(sb.length()>0)
                    sb.append(separator);
                String orgPathName = (String) adOrg.get("technicalName");
                if(StringUtils.isNotEmpty(orgPathName) && orgPathName.trim().startsWith("/"))
                    orgPathName = orgPathName.trim().substring(1);
                sb.append(orgPathName);
            }
        }
        return sb.toString();
    }

}
