package com.baosight.bscdkj.ky.sb.utils;


import java.awt.Color;
import java.awt.Font;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.Transparency;
import java.awt.image.BufferedImage;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.baosight.bscdkj.common.exception.BusinessException;
import com.baosight.bscdkj.common.ky.domain.TkysbTradeContinue;
import com.baosight.bscdkj.common.ky.domain.TkysbTradeShow;
import com.baosight.bscdkj.common.ky.domain.TkysbTradeState;
import com.baosight.bscdkj.ky.sb.business.BusinessKYSBTradeContinue;
import com.baosight.bscdkj.ky.sb.business.BusinessKYSBTradeShow;
import com.baosight.bscdkj.ky.sb.business.BusinessKYSBTradeState;
import com.baosight.bscdkj.mp.ty.dto.AttachmentMap;
import com.baosight.bscdkj.mp.ty.utils.SAttachmentUtil;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.PdfContentByte;
import com.itextpdf.text.pdf.PdfGState;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfStamper;
import org.apache.commons.lang.StringUtils;
import org.apache.pdfbox.io.MemoryUsageSetting;
import org.apache.pdfbox.multipdf.PDFMergerUtility;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @Date 2021/8/23 19:04
 */
@Controller
@RequestMapping("/kysb/tradepdf")
public class DownLoadTradePdfUtil {

    @Autowired
    BusinessKYSBTradeShow businessKYSBTradeShow;
    @Autowired
    BusinessKYSBTradeState businessKYSBTradeState;
    @Autowired
    BusinessKYSBTradeContinue businessKYSBTradeContinue;

    @ResponseBody
    @GetMapping("/download/{permId}")
    public void getPDF(HttpServletResponse response, HttpServletRequest request,
                       @PathVariable("permId") String permId) throws Exception {
        String imaPath ="/87ftp/tradePermIn/imgFile.png";
        String baseOutUrl ="/87ftp/tradePermIn";
        String resPath="/87ftp/tradePermIn/out.pdf";
        File filePhoto = new File(imaPath);
        if(!filePhoto.getParentFile().exists()){
            filePhoto.getParentFile().mkdirs();
        }
        //查询水印信息
        TkysbTradeShow tradeShow = businessKYSBTradeShow.query(permId);
        String yongtu = "用途:"+tradeShow.getPurpose();
        DateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
        String date = dateFormat.format(tradeShow.getCreateDate());
        String startTime = "开始日期:"+date;
        String mart = "有效期:"+tradeShow.getShowTerm()+"天";
        BufferedImage bi = createWaterMark(yongtu,startTime,mart);
        ImageIO.write(bi, "png", new File(imaPath)); //写入文件
        fileIn(permId,imaPath);
        response.setContentType("application/x-download");//告知浏览器下载文件，而不是直接打开，浏览器默认为打开
        File file = fileToOne(baseOutUrl,resPath);
        if(file==null) throw new BusinessException("没有可下载的文件");
        response.addHeader("Content-Disposition" ,"attachment;filename=\"" +"tradeShow.pdf"+ "\"");//下载文件的名称
        InputStream in = null;
        OutputStream out = null;
        try {
            //获取要下载的文件输入流
            in = new FileInputStream(file);
            int len = 0;
            //创建数据缓冲区
            byte[] buffer = new byte[1024];
            //通过response对象获取outputStream流
            out = response.getOutputStream();
            //将FileInputStream流写入到buffer缓冲区
            while((len = in.read(buffer)) > 0) {
                //使用OutputStream将缓冲区的数据输出到浏览器
                out.write(buffer,0,len);
            }
            out.flush();
            in.close();
            File fileAll = new File("/87ftp/tradePermIn");
            deleteFile(fileAll);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        } finally{
            if (out != null) {
                try {
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }

    }

    /**
     * 生成背景透明的文字水印图片，文字位于中央，且倾斜
     * @param content 水印文字
     * @return
     */
    public static BufferedImage createWaterMark(String content,String content1,String content2) {
        //生成图片宽度
        int width = 650;
        //生成图片高度
        int heigth = 550;
        //获取bufferedImage对象
        BufferedImage image = new BufferedImage(width, heigth, BufferedImage.TYPE_INT_RGB);
        //得到画笔对象
        Graphics2D g2d = image.createGraphics();
        //使得背景透明
        image = g2d.getDeviceConfiguration().createCompatibleImage(width, heigth, Transparency.TRANSLUCENT);
        g2d.dispose();
        g2d = image.createGraphics();
        g2d.setRenderingHint(RenderingHints.KEY_ANTIALIASING,RenderingHints.VALUE_ANTIALIAS_ON);
        //设置对线段的锯齿状边缘处理
       // g2d.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
        //设置水印旋转，倾斜度
        g2d.rotate(-0.5, (double) image.getWidth()/2, (double) image.getHeight()/2);
        //设置颜色，这是黑色，第4个参数是透明度
        g2d.setColor(Color.red);
        //设置字体
        Font font = new Font("宋体", Font.ROMAN_BASELINE, 22);
        g2d.setFont(font);
        float fontSize = font.getSize();
        //计算绘图偏移x、y，使得使得水印文字在图片中居中
        float x = 0.5f * fontSize;
        float y = 0.5f * heigth + x;
        //取绘制的字串宽度、高度中间点进行偏移，使得文字在图片坐标中居中
        g2d.drawString(content, x-30, y);
        g2d.drawString(content1, x-30, y+30);
        g2d.drawString(content2, x-30, y+60);
        //释放资源
        g2d.dispose();
        return image;
    }

    public void genFile(String id,String module,String label1,String imgPath) throws MalformedURLException {
        String filePath="/87ftp/tradePermIn/yuan.pdf";
        int i=(int)(Math.random()*900)+100;
        String baseOutUrl ="/87ftp/tradePermIn/"+label1+i+".pdf";
        List<AttachmentMap> list =  SAttachmentUtil.getAttachmentBySourceIdAndSourceLabel(id
                ,module,
                label1, null, null);
        if(list==null || list.size()==0) return;
        list.stream().forEach(attachmentMap -> {
            try {
                Map<String,Object> attachment = SAttachmentUtil.downAttachment("admin", attachmentMap.getAttachmentId());
                String type = (String) attachment.get("attachmentType");
                downloadNet((String)attachment.get("docUrl"),filePath);
                if(!type.toUpperCase().equals("PDF")) throw new BusinessException("文件类型错误不为PDF");
                addPDFImageWaterMark(filePath,baseOutUrl,imgPath);

            } catch (DocumentException e) {
                e.printStackTrace();
            } catch (IOException e) {
                e.printStackTrace();
            } catch (Exception e) {
                e.printStackTrace();
            }
        });
        File file = new File(filePath);
        if(file.exists()){
            Boolean delStu = file.delete();
            System.out.println(delStu);
        }
    }

    public void fileIn(String permId,String imgPath) throws MalformedURLException {
        TkysbTradeShow show =businessKYSBTradeShow.query(permId);
        if(StringUtils.isEmpty(show.getTradeRegistId())){
            throw new BusinessException("商标编号不能为空");
        }
        if(StringUtils.isEmpty(show.getStatus()) ){
            if(!show.getStatus().equals("end"))
            throw new BusinessException("对外出示尚未审批结束不能下载");
        }
        String[] ids  = show.getTradeRegistId().split(",");
        for(String regId :ids){
            genFile(regId,"KYSB","tradeSbzczFile",imgPath);
            genFile(regId,"KYSB","tradeStateUpdateFile",imgPath);
            TkysbTradeState query = businessKYSBTradeState.queryByRegId(regId);
            if(query!=null){
                TkysbTradeContinue tradeContinue=null;
                if(StringUtils.isNotBlank(query.getTradeContinueId())){
                    tradeContinue = businessKYSBTradeContinue.queryByRegistId(regId);
                }else{
                    tradeContinue = businessKYSBTradeContinue.query(query.getTradeContinueId());
                }
                if(tradeContinue!=null)
                    genFile(tradeContinue.getTradeContinueId(),"KYSB","continueFile",imgPath);
            }
        }
        File file = new File(imgPath);
        if(file.exists()){
            file.delete();
        }
    }
//    public static void main(String[] args) {
//        File file = new File("/87ftp/tradePermIn");
//        deleteFile(file);
//    }

    public static void deleteFile(File file){
        if (file == null || !file.exists()){
            return;
        }
        File[] files = file.listFiles();
        for (File f: files){
            String name = file.getName();
            System.out.println(name);
            if (f.isDirectory()){
                deleteFile(f);
            }else {
                f.delete();
            }
        }
        file.delete();
    }
    public File fileToOne(String filePath , String resPath){
        List<File> files = new ArrayList();
        File file = new File(filePath);
        File[] tempList = file.listFiles();
        if(tempList==null || tempList.length<=0) return null;
        //获取该文件夹下的文件（文件都是PDF）
        for (int i = 0; i < tempList.length; i++) {
            if (tempList[i].isFile()) {
                files.add(tempList[i]);
            }
        }
        try {
            PDFMergerUtility mergePdf = new PDFMergerUtility();
            for (File f : files) {
                if(f.exists() && f.isFile()){
                    // 循环添加要合并的pdf
                    mergePdf.addSource(f);
                }
            }
            // 设置合并生成pdf文件名称
            mergePdf.setDestinationFileName(resPath);
            // 合并pdf
            mergePdf.mergeDocuments(MemoryUsageSetting.setupMainMemoryOnly());
            return new File(resPath);
        } catch (Exception e){
            e.printStackTrace();
        }
        return null;
    }


    public static void addPDFImageWaterMark(String srcPath, String destPath, String imagePath)
            throws Exception {
        PdfReader reader = new PdfReader(srcPath);

        PdfReader.unethicalreading = true;
        PdfStamper stamper = new PdfStamper(reader, new FileOutputStream(destPath));
//        stamper.setEncryption(null,
//                "123".getBytes(), 2052, false);

        //加载图片
        File img = new File(imagePath);
        Image image = Image.getInstance(img.getPath());
        PdfGState gs = new PdfGState();
        //gs.setFillOpacity(0.2f);//图片水印透明度
        //gs.setStrokeOpacity(0.4f);//设置笔触字体不透明度
        PdfContentByte content = null;
        int total = reader.getNumberOfPages();//pdf文件页数
        for (int i=0; i<total; i++) {
            float x = reader.getPageSize(i+1).getWidth();//页宽度
            float y = reader.getPageSize(i+1).getHeight();//页高度
            content = stamper.getOverContent(i+1);
            stamper.getUnderContent(3);
            content.setGState(gs);
            content.beginText();//开始写入
            //每页7行，一行3个
            for (int j=1; j<2; j++) {
                for (int k=1; k<7; k++) {
                    if(k==2 || k==4 || k==6 )continue;
                    //setAbsolutePosition 方法的参数（输出水印X轴位置，Y轴位置）
                    image.setAbsolutePosition(x/3*j-30, y/7*k-20);
                    content.addImage(image);
                }
            }
            content.endText();//结束写入
            stamper.setFormFlattening(true);
        }

        //关闭流
        stamper.close();
        reader.close();
    }
    public void downloadNet(String ursl,String outFilePath) throws MalformedURLException {
        // 下载网络文件
        int bytesum = 0;
        int byteread = 0;

        URL url = new URL(ursl);

        try {
            URLConnection conn = url.openConnection();
            InputStream inStream = conn.getInputStream();
            FileOutputStream fs = new FileOutputStream(outFilePath);

            byte[] buffer = new byte[1204];
            int length;
            while ((byteread = inStream.read(buffer)) != -1) {
                bytesum += byteread;
                System.out.println(bytesum);
                fs.write(buffer, 0, byteread);
            }
            fs.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }
}