package com.baosight.bscdkj.ky.sb.controller;

import java.util.List;
import java.util.Map;

import com.baosight.bscdkj.ky.sb.vo.TkysbTradeRechWF;
import com.baosight.bscdkj.common.controller.BaseController;
import com.baosight.bscdkj.common.domain.AjaxResult;
import com.baosight.bscdkj.common.domain.TableDataInfo;
import com.baosight.bscdkj.utils.excel.ExcelUtil;
import com.baosight.bscdkj.common.ky.domain.TkysbTradeRech;
import com.baosight.bscdkj.utils.iplat.ServiceUtil;

import com.baosight.iplat4j.core.ei.EiConstant;
import com.baosight.iplat4j.core.ei.EiInfo;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Controller;
import org.springframework.ui.ModelMap;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.validation.annotation.Validated;

/**
 * 商标注册-驳回复审（子）Controller
 * 
 * <AUTHOR>
 * @date 2022-02-25
 */
@Controller
@RequestMapping("/kysb/tradeRech")
public class ControllerKYSBTradeRech extends BaseController{
    private String prefix = "/kysb/tradeRech";

    @GetMapping("KYSBTRA11")
    public String KYSBTRA11(){
        return prefix + "/KYSBTRA11";
    }

     /**
     * 查询商标注册-驳回复审（子）列表
     */
  	@PostMapping("/list")
	@ResponseBody
	public TableDataInfo list(@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKYSBTradeRech", "pageDraft");
		return getDataTable(query);
	}
    /**
     * 已处理
     * @return
     */
    @GetMapping("/KYSBTRC11")
    public String KYSBTRC11(){
        return prefix + "/KYSBTRC11";
    }
    /**
     * 新增商标注册-驳回复审（子）
     */
    @GetMapping("/KYSBTRA12")
    public String add(){
        return prefix + "/KYSBTRA12";
    }



    @GetMapping("/newTradeRech/{pageNo}/{regId}")
    public String newTradeRegist(@PathVariable("pageNo") String pageNo,@PathVariable("regId") String regId, ModelMap model) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("tradeRegistId",regId);
        model.put("tradeRech", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKYSBTradeRech", "newTradeRech", "tradeRech"));
        return prefix + "/" + pageNo;
    }
    /**
     * 暂存
     */
    @PostMapping("/save")
    @ResponseBody
    public AjaxResult save(@Validated TkysbTradeRechWF tradeRech) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("tradeRech", tradeRech);//页面对应的VO不能放到eiInfo的block块里
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKYSBTradeRech", "save");
    }

    /**
     * 提交（包含启动流程）
     *
     */
    @PostMapping("/doSubmit")
    @ResponseBody
    public AjaxResult doSubmit(@Validated TkysbTradeRechWF tradeRech) {

        EiInfo eiInfo = new EiInfo();
        eiInfo.set("tradeRech", tradeRech);//页面对应的VO不能放到eiInfo的block块里
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKYSBTradeRech", "doSubmit");
    }
    /**
     * 新增保存商标注册申请
     */
    @PostMapping("/add")
    @ResponseBody
    public AjaxResult addSave(@Validated TkysbTradeRech tradeRech) {
        EiInfo eiInfo = getEiInfo("i", tradeRech);
        return  ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKYSBTradeRech", "c");
    }

    /**
     * 修改商标注册申请
     */
    @GetMapping("/edit/{tradeRechId}")
    public String queryDBDetail( @PathVariable("tradeRechId") String tradeRechId,
                                 @PathVariable(value="taskId",required = false) String taskId, ModelMap model) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("businessId", tradeRechId);
        eiInfo.set("taskId", taskId);//
        model.put("tradeRech", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKYSBTradeRech", "toEditPage","tradeRech"));

        return prefix + "KYSBTRB13";
    }
    /**
     * 查询待办列表
     *
     * @param map
     * @return
     */
    @PostMapping("/DaiBanList")
    @ResponseBody
    public TableDataInfo DaiBanList(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKYSBTradeRech", "DaiBanList");
        return getDataTable(query);
    }
    /**
     * 到处理页面（处理按钮）
     */
    @GetMapping(value={"/toHandlePage/{pageNo}/{tradeRechId}/{taskId}"})
    public String toHandlePage( @PathVariable("pageNo") String pageNo,@PathVariable("tradeRechId") String tradeRechId,
                                       @PathVariable(value="taskId",required = false) String taskId, ModelMap model) {

        EiInfo eiInfo = new EiInfo();
        eiInfo.set("businessId", tradeRechId);
        eiInfo.set("taskId", taskId);
        model.put("tradeRech", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKYSBTradeRech", "toHandlePage","tradeRech"));
        TkysbTradeRechWF rechWF = (TkysbTradeRechWF) model.get("tradeRech");
        if(StringUtils.isBlank(rechWF.getPageNo())){
            rechWF.setPageNo(pageNo);
        }
        return prefix +"/"+ rechWF.getPageNo();
    }

    /**
     * 已处理列表
     * @param map
     * @return
     */
    @PostMapping("/pageYB")
    @ResponseBody
    public TableDataInfo pageYB(@RequestBody Map<String, Object> map) {
        EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
        EiInfo query = ServiceUtil.xLocalManager(eiInfo, "ServiceKYSBTradeRech", "pageYB");
        return getDataTable(query);
    }
    /**
     * 流程退回
     */
    @PostMapping("/doReturn")
    @ResponseBody
    public AjaxResult doReturn(@Validated TkysbTradeRechWF tkysbTradeRechWF) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("tkysbTradeRechWF", tkysbTradeRechWF);//页面对应的VO不能放到eiInfo的block块里
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKYSBTradeRech", "doReturn");
    }
    /**
     * 已办详细
     * @param pageNo
     * @param businessId
     * @param processInstanceId
     * @param model
     * @return
     */
    @GetMapping("/queryYBDetail/{pageNo}/{businessId}/{processInstanceId}")
    public String queryYBDetail(@PathVariable("pageNo") String pageNo, @PathVariable("businessId") String businessId,
                                @PathVariable("processInstanceId") String processInstanceId, ModelMap model) {
        EiInfo eiInfo = new EiInfo();
        eiInfo.set("tradeRechId", businessId);
        model.put("tradeRech", ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKYSBTradeRech", "queryDetail", "tradeRech"));
        model.put("processInstanceId", processInstanceId);
        return prefix + "/" + pageNo;
    }


    /**
     * 修改保存商标注册-驳回复审（子）
     */
    @PostMapping("/edit")
    @ResponseBody
    public AjaxResult editSave(@Validated TkysbTradeRech tradeRech){
        EiInfo eiInfo = getEiInfo("i", tradeRech);
        return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKYSBTradeRech", "update");
    }
    
    /**
     * 删除商标注册-驳回复审（子）
     */
	@PostMapping("/remove")
	@ResponseBody
	public AjaxResult remove(String ids) {
		EiInfo eiInfo = new EiInfo();
		eiInfo.set("tradeRechId", ids);
		return ServiceUtil.xLocalManagerToAjaxResult(eiInfo, "ServiceKYSBTradeRech", "remove");
	}

    /**
     * 导出商标注册-驳回复审（子）列表
     */
    @PostMapping("/export")
    @ResponseBody
    public AjaxResult export(@RequestBody Map<String, Object> map) {
		EiInfo eiInfo = getEiInfo(EiConstant.queryBlock, map);
		List<TkysbTradeRech> list = (List<TkysbTradeRech>)ServiceUtil.xLocalManagerToData(eiInfo, "ServiceKYSBTradeRech", "query","list");
		ExcelUtil<TkysbTradeRech> util = new ExcelUtil<>(TkysbTradeRech.class);
		util.setSheet("商标注册-驳回复审（子）");
		return util.exportExcel(list);
    }
}
