<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('结算方式')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-dict-add" th:object="${dict}">
        <input type="hidden" name="parentDictId" value="settlement">
        <input type="hidden" name="businessType" value="settlement">
        <input type="hidden" th:field="*{dictId}">

        <div aria-multiselectable="true" class="panel-group" id="accordion"
             role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed"
                           data-parent="#version" data-toggle="collapse" href="#jbxx">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i aria-hidden="true"
                                                        class="fa fa-chevron-down"></i></span>
                        </a>
                    </h4>
                </div>
                <div aria-expanded="false" class="panel-collapse collapse in"
                     id="jbxx">
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">结算方式：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" th:field="*{dictName}" autocomplete="off" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">结算方式代码：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" th:field="*{dictCode}" autocomplete="off" required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzzc/dict"

    $("#form-dict-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            if(!$.common.isEmpty([[${dict.dictId}]])){
                $.operate.save(prefix + "/edit", $('#form-dict-add').serialize());
            }
            $.operate.save(prefix + "/add", $('#form-dict-add').serialize());
        }
    }

</script>
</body>
</html>