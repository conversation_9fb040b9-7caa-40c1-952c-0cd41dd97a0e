<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增岗位类型')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-dict-add" th:object="${dict}">
        <input name="parentDictId" type="hidden" value="postTypePrice">
        <input name="businessType" type="hidden" value="postType">
        <input name="dictValue" type="hidden" value="">
        <input th:field="*{dictId}" type="hidden">

        <div aria-multiselectable="true" class="panel-group" id="accordion"
             role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed"
                           data-parent="#version" data-toggle="collapse" href="#jbxx">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i aria-hidden="true"
                                                        class="fa fa-chevron-down"></i></span>
                        </a>
                    </h4>
                </div>
                <div aria-expanded="false" class="panel-collapse collapse in" id="jbxx">
                    <div class="panel-body">

                        <div class="form-group">
                            <label class="col-sm-3 control-label">业务方：</label>
                            <div class="col-sm-8" th:include="/component/selectOrg :: init(orgCodeId='dictCode',orgNameId='dictName',selectType='S',value=*{dictCode},isrequired=true)"></div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">岗位类型：</label>
                            <div class="col-sm-8">
                                <select class="form-control" id="postType" name="postType" th:with="dictData=${@maintain.getPostType()}">
                                    <option value="">请选择</option>
                                    <option th:each="data : ${dictData}" th:selected="${dict.dictValue eq data.dictCode}" th:text="${data.dictName}"
                                            th:value="${data.dictCode}"></option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">岗位价格：</label>
                            <div class="col-sm-8">
                                <input autocomplete="off" class="form-control width100" required th:field="*{extra1}" type="text">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzzc/dict"
    $("#form-dict-add").validate({
        focusCleanup: true
    });
    $('#postType').on('select2:select', function (e) {
        $("[name='dictValue']").val(e.params.data.id)
    });

    function submitHandler() {
        if ($.validate.form()) {
            if (!$.common.isEmpty([[${dict.dictId}]])) {
                $.operate.save(prefix + "/edit", $('#form-dict-add').serialize());
            }
            $.operate.save(prefix + "/add", $('#form-dict-add').serialize());
        }
    }

</script>
</body>
</html>