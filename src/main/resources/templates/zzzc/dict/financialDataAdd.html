<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增财务数据')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-dict-add" th:object="${dict}">
        <input type="hidden" name="parentDictId" value="financialData">
        <input type="hidden" name="businessType" value="financialData">
        <input type="hidden" th:field="*{dictId}">

        <div aria-multiselectable="true" class="panel-group" id="accordion"
             role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed"
                           data-parent="#version" data-toggle="collapse" href="#jbxx">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i aria-hidden="true"
                                                        class="fa fa-chevron-down"></i></span>
                        </a>
                    </h4>
                </div>
                <div aria-expanded="false" class="panel-collapse collapse in" id="jbxx">
                    <div class="panel-body">

                        <div class="form-group">
                            <label class="col-sm-3 control-label">业务方：</label>
                            <div class="col-sm-8" th:include="/component/selectOrg :: init(orgCodeId='dictCode',orgNameId='dictName',selectType='S',value=*{dictCode},isrequired=true)"></div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">客户代码：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra1}" autocomplete="off" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">开票帐套：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra2}" autocomplete="off" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">开票责任中心：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra3}" autocomplete="off" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">推广方报支帐套：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra4}" autocomplete="off" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">推广方报支责任中心：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra5}" autocomplete="off" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">受让方报支帐套：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra6}" autocomplete="off">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label">受让方报支责任中心：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra7}" autocomplete="off">
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">财务责任中心：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra8}" autocomplete="off" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">开票单位名称：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra9}" autocomplete="off" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">开票单位税号：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra10}" autocomplete="off" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">开票地址电话：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra11}" autocomplete="off" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">开票银行名称：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra12}" autocomplete="off" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">开票银行帐号：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra13}" autocomplete="off" required>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">联系地址：</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control width100" th:field="*{extra14}" autocomplete="off" required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzzc/dict"
    $("#form-dict-add").validate({
        focusCleanup: true
    });
    $('#postType').on('select2:select', function (e) {
        console.log(e)
        $("[name='businessType']").val(e.params.data.id)
    });
    function submitHandler() {
        if ($.validate.form()) {
            if(!$.common.isEmpty([[${dict.dictId}]])){
                $.operate.save(prefix + "/edit", $('#form-dict-add').serialize());
            }
            $.operate.save(prefix + "/add", $('#form-dict-add').serialize());
        }
    }

</script>
</body>
</html>