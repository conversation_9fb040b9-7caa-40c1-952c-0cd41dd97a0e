<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('岗位类型维护')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="row form-group">
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label is-required">岗位类型编码：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="dictCode" required type="text">
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label is-required">岗位类型名称：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="dictName" required type="text">
                        </div>
                    </div>
                </div>
                <div class="select-list">
                    <ul>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzzc/dict";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/postTypeAdd",
            updateUrl: prefix + "/postTypeEdit/{id}",
            removeUrl: prefix + "/deleteLogin",
            modalName: "岗位类型维护",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.parentDictId = 'postType';
                return search;
            },
            uniqueId: 'dictId',
            columns:
                [
                    {
                        checkbox: true
                    },
                    {
                        field: 'dictId',
                        visible: false
                    },
                    {
                        field: 'dictCode',
                        title: '岗位类型编码',
                        align: 'center',
                    },
                    {
                        field: 'dictName',
                        title: '岗位类型名称',
                        align: 'center',
                    },
                ]

        };
        $.table.init(options);
    });
</script>
</body>
</html>