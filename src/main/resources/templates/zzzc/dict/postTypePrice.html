<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('岗位类型维护定价维护')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="row form-group">
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label is-required">业务方：</label>
                        <div class="col-sm-8" th:include="/component/selectOrg :: init(orgCodeId='dictCode',orgNameId='dictName',selectType='S',value=*{dictCode},isrequired=true)"></div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label is-required">岗位类型：</label>
                        <div class="col-sm-8">
                            <select class="form-control" id="dictValue" name="dictValue" th:with="dictData=${@maintain.getPostType()}">
                                <option value="">请选择</option>
                                <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictCode}"></option>
                            </select>
                        </div>
                    </div>
                </div>
                <div class="select-list">
                    <ul>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzzc/dict";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/postTypePriceAdd",
            updateUrl: prefix + "/postTypePriceEdit/{id}",
            removeUrl: prefix + "/deleteLogin",
            modalName: "岗位类型维护",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.parentDictId = 'postTypePrice';
                search.businessType = 'postType';
                return search;
            },
            uniqueId: 'dictId',
            columns:
                [
                    {
                        checkbox: true
                    },
                    {
                        field: 'dictId',
                        visible: false
                    },
                    {
                        field: 'dictName',
                        title: '业务方',
                        align: 'center',
                    },
                    {
                        field: 'dictValue',
                        title: '岗位类型',
                        align: 'center',
                        formatter: function (value, row, index) {
                            return $("#dictValue option[value='"+value+"']").text();
                        }
                    },
                    {
                        field: 'extra1',
                        title: '岗位价格',
                        align: 'center',
                    }
                ]

        };
        $.table.init(options);
    });
</script>
</body>
</html>