<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('推广移植配置列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-3 control-label">业务方：</label>
                        <div class="col-sm-8" th:include="/component/selectOrg :: init(orgCodeId='dictCode',orgNameId='dictName',selectType='S')"></div>
                    </div>
                </div>
                <div class="select-list">
                    <ul>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.edit()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzzc/dict";

    $(function () {
        var options = {
            url: prefix + "/approvalLimitList",
            createUrl: prefix + "/approvalLimitAdd",
            updateUrl: prefix + "/approvalLimitAdd?dictCode={id}",
            removeUrl: prefix + "/approvalLimitRemove",
            modalName: "业务方授权审批额度配置",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.parentDictId = 'approvalLimit';
                return search;
            },
            uniqueId: 'dictCode',
            columns: [
                [
                    {
                        checkbox: true,
                        rowspan: 2
                    },
                    {
                        field: 'dictCode',
                        visible:false,
                        rowspan: 2
                    },
                    {
                        field: 'dictName',
                        title: '业务方',
                        align: 'left',
                        halign: 'center',
                        rowspan: 2
                    },
                    {
                        title: '业务主管审批额',
                        align: 'center',
                        colspan: 2
                    },
                    {
                        title: '主管部门领导审批额',
                        align: 'center',
                        colspan: 2
                    }
                ],
                [
                    {
                        field: 'priceExtra1',
                        title: '定价额度(万元)',
                        align: 'center'
                    },
                    {
                        field: 'rewardExtra1',
                        title: '奖励额度(万元)',
                        align: 'center'
                    },
                    {
                        field: 'priceExtra2',
                        title: '定价额度(万元)',
                        align: 'center'
                    },
                    {
                        field: 'rewardExtra2',
                        title: '奖励额度(万元)',
                        align: 'center'
                    }

                ]
            ]

        };
        $.table.init(options);
    });
</script>
</body>
</html>