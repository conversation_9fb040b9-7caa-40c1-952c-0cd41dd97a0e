<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('业务方授权审批额度配置')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-dict-add" th:object="${dictEx}">
        <div aria-multiselectable="true" class="panel-group" id="accordion"
             role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed"
                           data-parent="#version" data-toggle="collapse" href="#jbxx">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i aria-hidden="true" class="fa fa-chevron-down"></i></span>
                        </a>
                    </h4>
                </div>
                <div aria-expanded="false" class="panel-collapse collapse in" id="jbxx">
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">业务方：</label>
                            <div class="col-sm-8" th:include="/component/selectOrg :: init(orgCodeId='dictCode',orgNameId='dictName',selectType='S',value=*{dictCode},isrequired=true)"></div>
                        </div>

                        <div class="mnote-editor-title text-center">业务主管审批额</div>

                        <div class="mnote-editor-box">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">定价额度：</label>
                                <div class="col-sm-2">
                                    <input autocomplete="off" class="form-control width100" name="price.extra1" required th:value="*{priceExtra1}" type="text">
                                </div>
                                <span class="txt-impt" style="margin-top: 7px;float: left;">(万)</span>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">奖励额度：</label>
                                <div class="col-sm-2">
                                    <input autocomplete="off" class="form-control width100" name="reward.extra1" required th:value="*{rewardExtra1}" type="text">
                                </div>
                                <span class="txt-impt" style="margin-top: 7px;float: left;">(万)</span>
                            </div>
                        </div>

                        <div class="mnote-editor-title text-center">主管部门领导审批额</div>

                        <div class="mnote-editor-box">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">定价额度：</label>
                                <div class="col-sm-2">
                                    <input autocomplete="off" class="form-control width100" name="price.extra2" required th:value="*{priceExtra2}" type="text">
                                </div>
                                <span class="txt-impt" style="margin-top: 7px;float: left;">(万)</span>
                            </div>

                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">奖励额度：</label>
                                <div class="col-sm-2">
                                    <input autocomplete="off" class="form-control width100" name="reward.extra2" required th:value="*{rewardExtra2}" type="text">
                                </div>
                                <span class="txt-impt" style="margin-top: 7px;float: left;">(万)</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>


<script th:inline="javascript">
    var prefix = ctx + "zzzc/dict"

    $("#form-dict-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.save(prefix + "/approvalLimitAdd", $('#form-dict-add').serialize());
        }
    }

</script>
</body>
</html>