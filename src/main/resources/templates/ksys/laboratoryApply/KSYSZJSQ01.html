<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <!--Manual2 部门/所领导审批 -->
    <!--Manual3 研究院院长审批 -->
    <!--Manual4 单位策划主管审批 -->
    <!--Manual5 指定项目主管 -->
    <th:block th:include="include :: header('联合实验室组建申请')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-js"/>
    <th:block th:include="include :: summernote-css"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-laboratoryApply-submit" th:object="${laboratoryApplyEx}">
        <input name="recordId" id="recordId" type="hidden" th:field="*{recordId}">
        <input name="labId" id="labId" type="hidden" th:field="*{labId}">
        <!--基础信息start-->
        <div class="panel-group" id="accordion1" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jb01" aria-expanded="false" class="collapsed">基本信息<span class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jb01" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">流水号：</label>
                                <label class="col-sm-8 form-control-static" th:utext="*{serialNo}"></label>

                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">联合实验室名称：</label>
                                <label class="col-sm-8 form-control-static" th:utext="*{labName}"></label>
                            </div>
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">属性：</label>
                                <div class="col-sm-8"
                                     th:include="/component/checkbox :: init(id='catogery',name='catogery', businessType='KSYS',dictCode='catogery',see=true,value=*{catogery})"></div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">提出人：</label>
                                <label class="col-sm-8 form-control-static" th:utext="*{tcrName}"></label>
                            </div>
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">联系电话：</label>
                                <label class="col-sm-8 form-control-static" th:utext="*{tcrMobilePhone}"></label>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">申报单位：</label>
                                <label class="col-sm-8 form-control-static" th:utext="*{tcDeptName}"></label>
                            </div>
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">拟合作单位：</label>
                                <label class="col-sm-8 form-control-static" th:utext="*{hzDept}"></label>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">所属领域：</label>
                                <div class="col-sm-8"
                                     th:include="/component/select :: init(id='techDomain', name='techDomain',businessType='KYND', dictCode='jsly',see=true,value=*{techDomain})"></div>
                            </div>
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">技术分类：</label>
                                <div class="col-sm-8"
                                     th:include="/component/select :: init(id='techClassification', name='techClassification',businessType='KYND', dictCode='jsfl',see=true,value=*{techClassification})"></div>
                            </div>
                        </div>

                        <div class="row form-group" th:if="${laboratoryApplyEx.workFlow.currentActivity} eq 'Manual5'">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label is-required">项目主管：</label>
                                <div class="col-sm-8"
                                     th:include="component/selectUser :: init(userCodeId='xmzgCode',userNameId='xmzgName',selectType='S',isrequired='true')"></th:block>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--基础信息end-->

        <!--合作方背景（战略、重要用户；国内外重点院所研究机构）start-->
        <div class="panel-group" id="accordion2" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jb02" aria-expanded="false" class="collapsed">一、合作方背景（战略、重要用户；国内外重点院所研究机构）<span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jb02" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row form-group">
                            <div class="mnote-editor-title" style="margin: 5px"><span style="color: red">*</span>
                                1.同行业中地位：
                            </div>
                            <div class="col-sm-12">
                                <div class="research_content textarea-readonly" th:utext="*{place}"></div>
                            </div>
                        </div>
                        <!--属性为止为用户 1 的时候显示 带院所不显示包括 0和0,1-->
                        <div class="row form-group"
                             th:if="${laboratoryApplyEx.catogery=='1'}">
                            <div class="mnote-editor-title" style="margin: 5px"><span style="color: red">*</span>
                                2.使用宝钢产品情况：
                            </div>
                            <div class="col-sm-12">
                                <div class="research_content textarea-readonly" th:utext="*{useProduct}"></div>
                            </div>
                        </div>
                        <!--属性院所 序号为2 否则为3-->
                        <div class="row form-group" th:if="${laboratoryApplyEx.catogery=='1'}">
                            <div class="mnote-editor-title" style="margin: 5px">3.历史合作情况：</div>
                            <div class="col-sm-12">
                                <div class="research_content textarea-readonly" th:utext="*{coopHistory}"></div>
                            </div>
                        </div>
                        <div class="row form-group"
                             th:if="${laboratoryApplyEx.catogery=='0'||laboratoryApplyEx.catogery=='0,1'}">
                            <div class="mnote-editor-title" style="margin: 5px">2.历史合作情况：</div>
                            <div class="col-sm-12">
                                <div class="research_content textarea-readonly" th:utext="*{coopHistory}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--合作方背景（战略、重要用户；国内外重点院所研究机构）end-->

        <!--联合实验室目标及定位：start-->
        <div class="panel-group" id="accordion3" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jb03" aria-expanded="false" class="collapsed"><span style="color:red;">*</span>二、联合实验室目标及定位<span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jb03" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row form-group">
                            <div class="col-sm-12">
                                <div class="research_content textarea-readonly" th:utext="*{goal}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--联合实验室目标及定位：end-->

        <!--主要研究方向：start-->
        <div class="panel-group" id="accordion4" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jb04" aria-expanded="false" class="collapsed"><span style="color:red;">*</span>三、主要研究方向<span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jb04" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row form-group">
                            <div class="col-sm-12">
                                <div class="research_content textarea-readonly" th:utext="*{research}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--主要研究方向：end-->

        <!--本年度工作内容及目标：start-->
        <div class="panel-group" id="accordion5" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jb05" aria-expanded="false" class="collapsed"><span style="color:red;">*</span>四、本年度工作内容及目标<span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jb05" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row form-group">
                            <div class="col-sm-12">
                                <div class="research_content textarea-readonly" th:utext="*{target}"></div>
                            </div>
                        </div>
                        <div class="row form-group">
                            <div class="mnote-editor-title" style="margin: 5px"><span style="color: red">*</span>指标结构化：
                            </div>
                            <div class="col-sm-12">
                                <table class="table table-bordered table-hover table-striped"
                                       id="laboratoryTargetTable">
                                    <thead>
                                    <tr>
                                        <th style="text-align:center">序号</th>
                                        <th style="text-align:center">指标名称</th>
                                        <th style="text-align:center">指标目标值</th>
                                        <th style="text-align:center">行业最优指标</th>
                                        <th style="text-align:center">近一年实绩</th>
                                    </tr>
                                    </thead>
                                    <tbody id="laboratoryTargetList">
                                    <tr th:data-index='${laboratoryTargetListStat.index}'
                                        th:each="laboratoryTargetList:${laboratoryApplyEx.laboratoryTargetList}">
                                        <td style='text-align: center;'
                                            th:utext="${laboratoryTargetListStat.index+1}"></td>
                                        <td style='text-align: center;'
                                            th:utext="${laboratoryTargetList.targetName}"></td>
                                        <td style='text-align: center;'
                                            th:utext="${laboratoryTargetList.targetNum}"></td>
                                        <td style='text-align: center;'
                                            th:utext="${laboratoryTargetList.targetZynum}"></td>
                                        <td style='text-align: center;'
                                            th:utext="${laboratoryTargetList.targetJynnum}"></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--本年度工作内容及目标：end-->

        <!--运行制度：：start-->
        <div class="panel-group" id="accordion6" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jb06" aria-expanded="false" class="collapsed"><span
                                style="color:red;">*</span>五、运行制度<span class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jb06" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row form-group">
                            <div class="col-sm-12">
                                <div th:include="/component/richText :: init(name='operationSystem',id='operationSystem',see='true',value=*{operationSystem})"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--运行制度：end-->

        <!--组织构成：start-->
        <div class="panel-group" id="accordion7" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jb07" aria-expanded="false" class="collapsed"><span
                                style="color:red;">*</span>六：组织构成<span class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jb07" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row form-group">
                            <div class="col-sm-12">
                                <table class="table table-bordered table-hover table-striped"
                                       id="laboratoryUnitTable">
                                    <thead>
                                    <tr>
                                        <th style="text-align:center">序号</th>
                                        <th style="text-align:center">组织名称</th>
                                        <th style="text-align:center">联系人</th>
                                    </tr>
                                    </thead>
                                    <tbody id="laboratoryUnitList">
                                    <tr th:data-index='${laboratoryUnitListStat.index}'
                                        th:each="laboratoryUnitList:${laboratoryApplyEx.laboratoryUnitList}">
                                        <td style='text-align: center;'
                                            th:utext="${laboratoryUnitListStat.index+1}"></td>
                                        <td style='text-align: center;'
                                            th:utext="${laboratoryUnitList.unitName}"></td>
                                        <td style='text-align: center;'
                                            th:utext="${laboratoryUnitList.linkman}"></td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--组织构成：end-->

        <!--协议初稿上传start-->
        <div class="panel-group" id="accordion8" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jb08" aria-expanded="false" class="collapsed">协议初稿<span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jb08" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row form-group">
                            <div class="col-sm-12">
                                <label class="col-sm-2 control-label">附件：</label>
                                <div class="col-sm-10">
                                    <div th:include="/component/attachment :: init(display='none',name='agreementDraft',id='agreementDraft',sourceModule='KSYS',sourceLabel1='applyFile',see=true,sourceId=*{recordId})">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--协议初稿上传end-->

        <!--审批意见start-->
        <div class="panel-group" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" href="#spyj" aria-expanded="false" class="collapsed">
                            审批意见
                            <span class="pull-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
								</span>
                        </a>
                    </h4>
                </div>
                <!-- name必须为 workFlow.comment -->
                <div id="spyj" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="form-group col-sm-12">
                            <textarea class="form-control" id="workFlow_comment" name="workFlow.comment"
                                      style="height: 200px; width: 100%;"></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--审批意见end-->

        <!-- 流程相关信息 -->
        <th:block th:include="component/wfWorkFlow :: init(workFlow=${laboratoryApplyEx.workFlow})"/>
        <!--审批履历-->
        <th:block th:include="component/wfCommentList3 :: init(businessId=${laboratoryApplyEx.recordId})"/>
    </form>

    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <!-- 审批历史 -->
        <th:block
                th:include="component/wfCommentList :: init(processInstanceId=${laboratoryApplyEx.workFlow.processInstanceId})"/>
        <!-- 提交 -->
        <th:block
                th:include="component/wfSubmitOne:: init(taskId=${laboratoryApplyEx.workFlow.taskId},callback=doSubmit)"/>
        <!-- 退回 -->
        <th:block
                th:include="component/wfReturn :: init(taskId=${laboratoryApplyEx.workFlow.taskId},callback=doReturn)"/>
<!--        &lt;!&ndash;流程跟踪图&ndash;&gt;-->
<!--        <button type="button" class="btn  btn-primary"-->
<!--                th:onclick="openProcessTrack([[${laboratoryApplyEx.workFlow.processInstanceId}]])">-->
<!--            <i class="fa fa-eye"></i>流程跟踪图-->
<!--        </button>-->
        <button type="button" class="btn  btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
    <!--按钮区end-->

</div>
<th:block th:include="include :: datetimepicker-js"/>
<script th:inline="javascript">
    var prefix = ctx + "ksys/laboratoryWorkFlow";

    $("#form-laboratoryApply-submit").validate({
        focusCleanup: true
    });

    //提交流程
    function doSubmit(transitionKey) {
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function () {
                $.operate.saveTabAlert(prefix + "/doSubmit", $('#form-laboratoryApply-submit').serialize());
            });
        }
    }

    //退回流程
    function doReturn(returnActivityKey) {
        $.modal.confirm("确认退回吗？", function () {
            $.operate.saveTabAlert(prefix + "/doReturn", $('#form-laboratoryApply-submit').serialize());
        });
    }

    //流程跟踪图
    function openProcessTrack(processInstanceId) {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processInstanceId);
    }
</script>
</body>
</html>