<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <!--Manual2 项目主管审批-->
    <th:block th:include="include :: header('项目主管审批')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-laboratoryRenew-add">
        <input name="labId" id="labId" type="hidden" th:value="${laboratory.recordId}">
        <!--联合实验室基础信息start-->
        <div class="panel-group" id="accordion1" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jb01" aria-expanded="false" class="collapsed">基本信息<span class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jb01" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">流水号：</label>
                                <label class="col-sm-8 form-control-static" th:utext="${laboratory.serialNo}"></label>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">联合实验室名称：</label>
                                <label class="col-sm-8 form-control-static" th:utext="${laboratory.labName}"></label>
                            </div>
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">属性：</label>
                                <div class="col-sm-8"
                                     th:include="/component/checkbox :: init(id='catogery',name='catogery', businessType='KSYS',dictCode='catogery',see=true,value=${laboratory.catogery})"></div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">提出人：</label>
                                <label class="col-sm-8 form-control-static" th:utext="${laboratory.tcrName}"></label>
                            </div>
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">联系电话：</label>
                                <label class="col-sm-8 form-control-static"
                                       th:utext="${laboratory.tcrMobilePhone}"></label>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">申报单位：</label>
                                <label class="col-sm-8 form-control-static" th:utext="${laboratory.tcDeptName}"></label>
                            </div>
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">拟合作单位：</label>
                                <label class="col-sm-8 form-control-static" th:utext="${laboratory.hzDept}"></label>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">所属领域：</label>
                                <div class="col-sm-8"
                                     th:include="/component/select :: init(id='techDomain', name='techDomain',businessType='KYND', dictCode='jsly',see=true,value=${laboratory.techDomain})"></div>
                            </div>
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">技术分类：</label>
                                <div class="col-sm-8"
                                     th:include="/component/select :: init(id='techClassification', name='techClassification',businessType='KYND', dictCode='jsfl',see=true,value=${laboratory.techClassification})"></div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">项目主管：</label>
                                <label class="col-sm-8 form-control-static" th:utext="${laboratory.xmzgName}"></label>
                            </div>

                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label ">项目负责人：</label>
                                <label class="col-sm-8 form-control-static" th:utext="${laboratory.fzrName}"></label>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">协议生效日期：</label>
                                <label class="col-sm-8 form-control-static" th:utext="${laboratory.startDate}"></label>
                            </div>
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">协议到期日期：</label>
                                <label class="col-sm-8 form-control-static" th:utext="${laboratory.endDate}"></label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--联合实验室基础信息end-->

        <!--续签情况start-->
        <div class="panel-group" id="accordion2" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jb02" aria-expanded="false" class="collapsed">续签情况<span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jb02" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">是否续签：</label>
                                <div class="col-sm-8"
                                     th:include="/component/radio :: init(id='isRenew', name='isRenew',businessType='MPTY', dictCode='is_yes_no',value=${laboratoryRenewEx.isRenew},see=true)"></div>
                            </div>

                            <div class="col-sm-5">
                                <label class="col-sm-4 control-label">协议有效期：</label>
                                <div class="col-sm-8"
                                     th:include="/component/select :: init(id='cycle', name='cycle',businessType='KSYS', dictCode='cycle',value=${laboratoryRenewEx.cycle},see=true)"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--续签情况end-->

        <!--相关附件start-->
        <div class="panel-group" id="accordion5" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jb05" aria-expanded="false" class="collapsed">附件<span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jb05" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row form-group">
                            <div class="col-sm-12">
                                <label class="col-sm-2 control-label is-required">协议正式稿：</label>
                                <div class="col-sm-10">
                                    <div th:include="/component/attachment :: init(display='none',name='renewalAgreement',id='renewalAgreement',sourceModule='KSYS',sourceLabel1='renewalAgreement',sourceId=${laboratoryRenewEx.recordId},see=true)">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-12">
                                <label class="col-sm-2 control-label">其他附件：</label>
                                <div class="col-sm-10">
                                    <div th:include="/component/attachment :: init(display='none',name='otherAgreement',id='otherAgreement',sourceModule='KSYS',sourceLabel1='otherAgreement',sourceId=${laboratoryRenewEx.recordId},see=true)">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--相关附件end-->

        <!-- 流程相关信息 -->
        <th:block th:include="component/wfWorkFlow :: init(workFlow=${laboratoryRenewEx.workFlow})"/>
        <!--审批履历-->
        <th:block th:include="component/wfCommentList3 :: init(businessId=${laboratoryRenewEx.recordId})"/>
    </form>

    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <!-- 审批历史 -->
        <th:block
                th:include="component/wfCommentList :: init(processInstanceId=${laboratoryRenewEx.workFlow.processInstanceId})"/>
        <!-- 提交 -->
        <th:block
                th:include="component/wfSubmitOne:: init(taskId=${laboratoryRenewEx.workFlow.taskId},callback=doSubmit)"/>
<!--        &lt;!&ndash;流程跟踪图&ndash;&gt;-->
<!--        <button type="button" class="btn  btn-primary"-->
<!--                th:onclick="openProcessTrack([[${laboratoryRenewEx.workFlow.processInstanceId}]])">-->
<!--            <i class="fa fa-eye"></i>流程跟踪图-->
<!--        </button>-->
        <button type="button" class="btn  btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
    <!--按钮区end-->

</div>
<th:block th:include="include :: datetimepicker-js"/>
<script th:inline="javascript">
    var prefix = ctx + "ksys/laboratoryWorkFlow";

    $("#form-laboratoryReport-add").validate({
        focusCleanup: true
    });

    //提交流程
    function doSubmit(transitionKey) {
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function () {
                $.operate.saveTabAlert(prefix + "/doSubmitXYXQ", $('#form-laboratoryRenew-add').serialize());
            });
        }
    }

    //流程跟踪图
    function openProcessTrack(processInstanceId) {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processInstanceId);
    }
</script>
</body>
</html>