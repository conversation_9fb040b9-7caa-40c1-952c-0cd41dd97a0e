<!DOCTYPE HTML>
<html  lang="zh" xmlns:th="http://www.thymeleaf.org">
<meta charset="utf-8">
<head >
    <th:block th:include="include :: header('商标申请')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId" >

                <div class="select-list">
                    <ul>
                        <li>
                            <label >商标名称：</label>
                            <input type="text"   name="trademarkName" placeholder="支持模糊查询"/>
                        </li>
                        <li>
                            <label >注册号：</label>
                            <input type="text"   name="regigterNo" placeholder="支持模糊查询"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<script th:inline="javascript">
    var prefix = ctx + "kysb/tradeRegist";
    var techClass = [[${@dict.getDictList('KYSB','techClass')}]];
    var trademarkRegistType = [[${@dict.getDictList('KYSB','trademarkRegistType')}]];
    var ownership = [[${@dict.getDictList('KYSB','ownership')}]];
    // var trademarkLabel = [[${@dict.getDictList('KYSB','trademarkLabel')}]];


    $(function() {
        var options = {
            url: prefix + "/ENDTRADEDXCOUNTINUE",
            modalName: "商标注册申请",
            pageSize: 1000,
            columns: [{
                checkbox: true
            },
            {
                field: 'tradeRegistId',
                title: '主键',
                visible: false
            },
                {
                    field: 'imgurl',
                    title: '商标图样',
                    formatter: function (value, row, index) {
                        var s = '<img style="width:300;height:40px;"  src="'+value+'" />';
                        return s;
                    }
                },
                {
                    field: 'trademarkName',
                    title: '商标名称'
                },
                {
                    field: 'regigterNo',
                    title: '注册号'
                },
                {
                    field: 'initRegisterDate',
                    title: '注册日期'
                },
                {
                    field: 'endTime',
                    title: '有效期限',
                    sortable:"true"
                },
                {
                    field: 'trademarkRegistTypeValue',
                    title: '商标注册类别'
                },
                // {
                //     field: 'trademarkLabel',
                //     title: '商标标签',
                //     formatter:function (value,row,index) {
                //         return $.table.selectDictLabel(trademarkLabel, value);
                //     }
                //
                // },
           ]
        };
        $.table.init(options);
    });
    function submitHandler() {
        var row = $('#bootstrap-table').bootstrapTable('getSelections');
        var arr = new Array();
        for(var i=0;i<row.length;i++){
            arr[i] = row[i].tradeRegistId;
        }
        console.log(arr)
        parent.opendTab(arr);
        var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        parent.layer.close(index);
    }

    // function submitHandler() {
    //     var row = $('#bootstrap-table').bootstrapTable('getSelections');
    //     var tradeRegistId = row[0].tradeRegistId;
    //     console.log(tradeRegistId)
    //     parent.opendTab(tradeRegistId);
    //     var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
    //     parent.layer.close(index);
    // }

    </script>
</body>
</html>