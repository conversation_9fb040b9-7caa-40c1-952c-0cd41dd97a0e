<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('专家评审页')" />
	<th:block th:include="kysb/countyr :: selectCountry"/>
<th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
	<form class="form-horizontal m" id="form-tradeRegist-add" >
		<div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#jbxx" aria-expanded="false" class="collapsed">
							基本信息
							<span class="pull-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
								</span>
						</a>
					</h4>
				</div>
				<div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="form-group" hidden>
								<label class="col-sm-4 control-label">申请单位编码：</label>
								<div class="col-sm-8">
									<input name="tradeRegistId" th:field="*{tradeRegist.tradeRegistId}" >
								</div>
							</div>
							<div class="form-group">
								<div class="form-group">
									<div class="col-sm-12">
										<label class="col-sm-2 control-label is-required">流水号：</label>
										<div class="col-sm-10">
											<input name="tradeNum" th:field="*{tradeRegist.tradeNum}" class="form-control" type="text">
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<div class="form-group">
									<div class="col-sm-12">
										<label class="col-sm-2 control-label">申请单位：</label>
										<div class="col-sm-10"
											 th:include="/component/selectOrg::init(isrequired=true,see='true',orgCodeId='applyDeptCode',orgNameId='applyDeptName',value=${tradeRegist.applyDeptCode},selectType='M')">
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">

								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">单位电话：</label>
										<div class="col-sm-8">
											<input name="applyDeptPh" class="form-control" th:field="*{tradeRegist.applyDeptPh}" type="text">
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">提出人（经办人）：</label>
										<div class="col-sm-8">
											<input name="applyPersonName" class="form-control" th:field="*{tradeRegist.applyPersonName}" type="text">
										</div>
									</div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label">工号：</label>
										<div class="col-sm-8">
											<input name="applyPersonGh" class="form-control" th:field="*{tradeRegist.applyPersonGh}" type="text"  readonly>
										</div>
									</div>
								</div>
							</div>

							<!--            <div class="form-group">    -->
							<!--                <label class="col-sm-4 control-label">工号：</label>-->
							<!--                <div class="col-sm-8">-->
							<!--                    <input name="applyPersonGh" class="form-control" type="text">-->
							<!--                </div>-->
							<!--            </div>-->


							<!--            <div class="form-group">    -->
							<!--                <label class="col-sm-4 control-label">申请人：</label>-->
							<!--                <div class="col-sm-8">-->
							<!--                    <input name="applyPersonName" class="form-control" type="text">-->
							<!--                </div>-->
							<!--            </div>-->
							<div class="form-group">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label">EMAIL：</label>
										<div class="col-sm-8">
											<input name="applyPersonEmail" class="form-control" type="text" th:field="*{tradeRegist.applyPersonEmail}">
										</div>
									</div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">手机号码：</label>
										<div class="col-sm-8">
											<input name="applyPersonPh" class="form-control" th:field="*{tradeRegist.applyPersonPh}" type="tel">
										</div>
									</div>
								</div>
							</div>
						<div class="form-group">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label">商标法律状态：</label>
									<div class="col-sm-8">
										<input name="" class="form-control" type="text" value="注册后 自动生成" readonly>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label ">注册号：</label>
									<div class="col-sm-8">
										<input name="" class="form-control" type="tel" value="注册后 自动生成" readonly>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label">注册日期：</label>
									<div class="col-sm-8">
										<input name="" class="form-control" type="text" value="注册后 自动生成" readonly>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label ">有效截止日：</label>
									<div class="col-sm-8">
										<input name="" class="form-control" type="tel" value="状态为终止时显示 自动带出" readonly>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="panel panel-default panel-group">
			<div class="panel-heading">
				<h4 class="panel-title">
					<a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#6">商标名称
						<span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
					</a>
				</h4>
			</div>
			<!--折叠区域-->
			<div aria-expanded="false" class="panel-collapse collapse in" id="6">
				<div class="panel-body">
					<div class="row form-group">
						<button class="btn btn-white btn-sm" onclick="addColumn('H')" type="button"><i
								class="fa fa-plus"> 增加</i></button>
						<button class="btn btn-white btn-sm" onclick="sub.delColumn()" type="button"><i
								class="fa fa-minus"> 删除</i></button>
						<div class="col-sm-12 select-table table-striped">
							<table id="bootstrap-tableH"></table>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="panel-group" id="accordion2" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#sbxx" class="collapsed">
							商标信息
							<span class="pull-right">
									<i class="fa fa-chevron-down"></i>
								</span>
						</a>
					</h4>
				</div>
				<div id="sbxx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
<!--						<div class="form-group">-->
<!--							<label class="col-sm-2 control-label is-required">商标中/英文名称（首选）：</label>-->
<!--							<div class="col-sm-10">-->
<!--								<input name="trademarkName" class="form-control"  th:field="*{tradeRegist.trademarkName}" type="text">-->
<!--							</div>-->
<!--						</div>-->
<!--						<div class="form-group">-->
<!--							<label class="col-sm-2 control-label ">商标中/英文名称（备选）：</label>-->
<!--							<div class="col-sm-10">-->
<!--								<input name="trademarkName2" class="form-control"  th:field="*{tradeRegist.trademarkName2}" type="text">-->
<!--							</div>-->
<!--						</div>-->
						<div class="form-group">
							<label class="col-sm-2 control-label ">商标样式及策划方案：</label>
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='tradeFile',name='tradeFile',
									 sourceId=*{tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeFile',see='true')">
							</div>
						</div>

						<div class="form-group">
							<label class="col-sm-2 control-label is-required">商标含义：</label>
							<div class="col-sm-10">
									<textarea name="trademarkMeaning" th:field="*{tradeRegist.trademarkMeaning}" rows="7" cols="150" class="form-control" rangelength="2,500"  required="required" readonly>
                                    </textarea>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label  is-required">技术分类：</label>
									<div class="col-sm-8"
										 th:include="/kysb/tradeRegist/selectTechnicalx::init(isrequired=true,orgCodeId='techClass',selectType='M',type='2',orgNameId='techClassName',see='true',value=*{tradeRegist.techClass})">
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label">文档属性：</label>
									<div class="col-sm-8">
										<input name="docProperty" class="form-control" type="text" th:value="商标" readonly>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">商标注册类别：</label>
							<div class="col-sm-10">
								<input name="trademarkRegistType" class="form-control" type="hidden"  th:field="*{tradeRegist.trademarkRegistType}" >

								<input  class="form-control" th:value="*{tradeRegist.trademarkRegistTypeValue}"type="text" >
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">商标形式：</label>
							<div class="col-sm-4">
								<th:block th:include="component/select :: init(id='layout', see='true',name='layout' ,businessType='KYSB', dictCode='layout',value=*{tradeRegist.layout})">
								</th:block>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">商标标签：</label>
							<div class="col-sm-10">
								<th:block th:include="component/select :: init(id='trademarkLabel',multimultiple='true',see='true',
									 name='trademarkLabel' ,businessType='KYSB', dictCode='trademarkLabel',value=*{tradeRegist.trademarkLabel})">
								</th:block>
							</div>
						</div>

						<div class="form-group">
							<label class="col-sm-2 control-label is-required">权利人：</label>
							<div class="col-sm-10">
								<input name="ownership" class="form-control" type="text"  th:field="*{tradeRegist.ownership}" >
<!--								<input  class="form-control" th:value="*{tradeRegist.ownershipValue}"type="text" >-->
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">法定地址：</label>
							<div class="col-sm-10">
								<input name="deptAddress" class="form-control" th:field="*{tradeRegist.deptAddress}" type="text">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label  is-required">申请说明：</label>
							<div class="col-sm-10">
								<textarea name="applyRepresent" th:field="*{tradeRegist.applyRepresent}" rows="7" cols="150" class="form-control" rangelength="2,500"  required="required" readonly>
								</textarea>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">申请/展出国家/地区：</label>
							<div  th:include="kysb/countyr :: choiceCountry(divClass='col-sm-10',userCode='trade',userCodeId='countryRegion',see='true',
                                userNameId='countryRegionName',selectType='M',value=*{tradeRegist.countryRegion},isrequired=true)">
							</div>
						</div>
						<input type="hidden" name="countryRegion" th:value="${tradeRegist.countryRegion}">
						<div th:if='${tradeRegist.workFlow.currentActivity  ne "KY_SB_ZSZG_START" and (tradeRegist.status eq "activedCs" or tradeRegist.status eq "activeBlwt" or tradeRegist.status eq "activedTzs")}'>
<!--							<div class="form-group">-->
<!--								<label class="col-sm-2 control-label is-required">是否报公司：</label>-->
<!--								<div class="col-sm-4">-->
<!--									<th:block th:include="component/select :: init(id='istogs', name='istogs' ,businessType='KYSB', dictCode='istogs',see='true',value=*{tradeRegist.istogs})">-->
<!--									</th:block>-->
<!--								</div>-->
<!--							</div>-->
							<div class="form-group">
								<label class="col-sm-2 control-label">商标检索分析报告：</label>
								<div class="col-sm-10">
									<div  th:include="/component/attachment :: init(id='tradeJsfxFile',name='tradeJsfxFile',see='true',
                                 sourceId=*{tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeJsfxFile')"></div>
								</div>
							</div>
						</div>
						<div th:if='${tradeRegist.workFlow.currentActivity  eq "Manual2"}'>
							<div class="form-group">
								<label class="col-sm-2 control-label">注册委托材料：</label>
								<div class="col-sm-10">
									<div  th:include="/component/attachment :: init(id='tradeZcwtFile',name='tradeZcwtFile',
									 sourceId=*{tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeZcwtFile')"></div>
								</div>
							</div>
<!--							<div class="form-group">-->
<!--								<label class="col-sm-2 control-label is-required">代理所：</label>-->
<!--								<div class="col-sm-10">-->
<!--									<th:block th:include="component/select :: init(id='dls', name='dls' ,businessType='KYSB', dictCode='dls')">-->
<!--									</th:block>-->
<!--								</div>-->
<!--							</div>-->
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">供应商：</label>
								<div class="col-sm-10">
									<th:block th:include="component/select :: init(id='supName',see='true', name='supName' ,businessType='KYSB', dictCode='dls',value=*{tradeRegist.supName})">
									</th:block>
								</div>
							</div>
						</div>
						<div th:if='${tradeRegist.workFlow.currentActivity  ne "Manual2" and (tradeRegist.status eq "activeBlwt" or tradeRegist.status eq "activedTzs")}'>
							<div class="form-group">
								<label class="col-sm-2 control-label">注册委托材料：</label>
								<div class="col-sm-10">
									<div  th:include="/component/attachment :: init(id='tradeZcwtFile',name='tradeZcwtFile',
									 sourceId=*{tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeZcwtFile')"></div>
								</div>
							</div>
<!--							<div class="form-group">-->
<!--								<label class="col-sm-2 control-label is-required">代理所：</label>-->
<!--								<div class="col-sm-10">-->
<!--									<th:block th:include="component/select :: init(id='dls', name='dls' ,businessType='KYSB', dictCode='dls',see='true',value=*{tradeRegist.dls})">-->
<!--									</th:block>-->
<!--								</div>-->
<!--							</div>-->
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">供应商：</label>
								<div class="col-sm-10">
									<th:block th:include="component/select :: init(id='supName',see='true', name='supName' ,businessType='KYSB', dictCode='dls')">
									</th:block>
								</div>
							</div>
						</div>
						<!--在受理通知书节点增加商标图样栏位以附件形式，图片直接显示在页面上-->
						<div th:if='${tradeRegist.workFlow.currentActivity  eq "Manual4"}'>
							<div class="form-group">
								<label class="col-sm-2 control-label">受理相关附件：</label>
								<div class="col-sm-10">
									<div  th:include="/component/attachment :: init(id='tradeSlxgFile',name='tradeSlxgFile',
									 sourceId=*{tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeSlxgFile')"></div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">初审相关附件：</label>
								<div class="col-sm-10">
									<div  th:include="/component/attachment :: init(id='tradeCsxgFile',name='tradeCsxgFile',
									 sourceId=*{tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeCsxgFile')"></div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">注册相关附件：</label>
								<div class="col-sm-10">
									<div  th:include="/component/attachment :: init(id='tradeZcxgFile',name='tradeZcxgFile',
									 sourceId=*{tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeZcxgFile')"></div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">申请日期：</label>
								<div class="col-sm-10">
									<th:block th:include="/component/date::init(name='applysqDate',id='applysqDate',strValue=*{tradeRegist.applysqDate},isrequired=true)"/>
								</div>
							</div>

							<div class="form-group">
								<label class="col-sm-2 control-label">商标图样：</label>
								<div class="col-sm-10">
									<input type="hidden" name="imgurl" id="imgurl">
									<div class="layui-upload-list">
										<img class="layui-upload-img" th:src="${tradeRegist.imgurl}" width="508" height="278" id="imgFile">
										<p id="demoText"></p>
									</div>
									<button type="button" class="layui-btn" id="shangchuangTp">上传图片</button>
								</div>
							</div>
						</div>
						<div th:if='${tradeRegist.workFlow.currentActivity  ne "Manual4" and tradeRegist.status eq "activedTzs"}'>
							<div class="form-group">
								<label class="col-sm-2 control-label">受理相关附件：</label>
								<div class="col-sm-10">
									<div  th:include="/component/attachment :: init(id='tradeSlxgFile',name='tradeSlxgFile',see='true',
									 sourceId=*{tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeSlxgFile')"></div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">初审相关附件：</label>
								<div class="col-sm-10">
									<div  th:include="/component/attachment :: init(id='tradeCsxgFile',name='tradeCsxgFile',see='true',
									 sourceId=*{tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeCsxgFile')"></div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">注册相关附件：</label>
								<div class="col-sm-10">
									<div  th:include="/component/attachment :: init(id='tradeZcxgFile',name='tradeZcxgFile',see='true',
									 sourceId=*{tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeZcxgFile')"></div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">申请日期：</label>
								<div class="col-sm-10">
									<th:block th:include="/component/date::init(name='applysqDate',id='applysqDate',strValue=*{tradeRegist.applysqDate},isrequired=true,see='true')"/>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">商标图样：</label>
								<div class="col-sm-10">
									<input type="hidden" name="imgurl" id="imgurl">
									<div class="layui-upload-list">
										<img class="layui-upload-img" th:src="${tradeRegist.imgurl}" width="508" height="278" id="imgFile">
										<p id="demoText"></p>
									</div>
<!--									<button type="button" class="layui-btn" id="shangchuangTp">上传图片</button>-->
								</div>
							</div>
						</div>
						<div th:if='${tradeRegist.workFlow.currentActivity  eq "Manual6"}'>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">初审日期：</label>
								<div class="col-sm-10">
									<th:block th:include="/component/date::init(name='initialDate',id='initialDate',strValue=*{tradeRegist.initialDate},isrequired=true)"/>
								</div>
							</div>
						</div>
						<div th:if='${tradeRegist.workFlow.currentActivity  eq "Manual1"}'>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">初审日期：</label>
								<div class="col-sm-10">
									<th:block th:include="/component/date::init(name='initialDate',id='initialDate',strValue=*{tradeRegist.initialDate},isrequired=true,see='true')"/>
								</div>
							</div>
						</div>

					</div>
				</div>
			</div>
		</div>

		<div class="panel-group" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#spyj" aria-expanded="false" class="collapsed">
							审批意见
							<span class="pull-right">
								<i class="fa fa-chevron-down" aria-hidden="true"></i>
							</span>
						</a>
					</h4>
				</div>
				<!-- name必须为 workFlow.comment -->
				<div id="spyj" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="form-group col-sm-12">
							<textarea class="form-control" id="workFlow_comment" name="workFlow.comment" style="height: 200px; width: 100%;"
									  th:utext="${tradeRegist.workFlow.comment}"></textarea>
						</div>
					</div>
				</div>
			</div>
		</div>
		<th:block th:include="component/wfCommentList3 :: init(businessId=${tradeRegist.workFlow.businessId})" />

		<!--流程相关信息-->
		<th:block th:include="component/wfWorkFlow :: init(workFlow=${tradeRegist.workFlow})" />
		<!--流程相关信息-->
		</form>
		<!--按钮区-->
		<th:block th:include="kysb/tradeRegist/wfDetailButton :: init(workFlow=${tradeRegist.workFlow})" />
		<!--按钮区end-->
	</div>
	<script th:inline="javascript">
		$("#form-tradeRegist-add input").attr("readOnly",true);
		$("#supName").attr("readOnly",false);
		$("#supCode").attr("readOnly",false);
		$(document).ready(function(){
			var jsIsChooseZj = [[${tradeRegist.jsIsChooseZj}]];//是否专家评审
			if(!jsIsChooseZj || jsIsChooseZj == 'noNeed'){
				$('.ifJsIsChooseZj').addClass('hide');
			}
		});
		function ifJsIsChooseZj(item){
			if (item == 'need') {
				$('.ifJsIsChooseZj').removeClass('hide');
			} else {
				$('.ifJsIsChooseZj').addClass('hide');
			}
		}

		//常规使用 - 普通图片上传
		layui.use(['upload', 'element', 'layer'], function(){
			var $ = layui.jquery
					,upload = layui.upload
					,element = layui.element
					,layer = layui.layer;

			//常规使用 - 普通图片上传
			var uploadInst = upload.render({
				elem: '#shangchuangTp'
				,url: ctx+"attachment/upload"
				,before: function(obj){
					//预读本地文件示例，不支持ie8
					obj.preview(function(index, file, result){
						$('#imgFile').attr('src', result); //图片链接（base64）
					});
				}
				,done: function(res){
					//如果上传失败
					if(res.code > 0){
						return layer.msg('上传失败');
					}
					console.log(res)
					$("#imgurl").val(res.docUrl);
					//上传成功的一些操作
					//……
				}
				,error: function(){
					layer.msg('上传失败');
				}
			});
		})

		var prefix = ctx + "kysb/tradeRegist";
		function doSubmit(transitionKey) {
			if ($.validate.form()) {
				$.modal.confirm("确认提交吗？", function () {
					//$("#workFlow_transitionKey").val(transitionKey); 这个js在提交组件里加了，没有用提交组件的需要这行
					$.operate.saveTabAlert(prefix + "/doSubmit", $('#form-tradeRegist-add').serialize());
				});		
			}
		}
		var tradeRegistId =$("#tradeRegistId").val();
		initSub();
		function initSub(){
			var optionsH = { // 推广移植后经济指标、产品、质量等情况
				id: "bootstrap-tableH",
				url: ctx+"kysb/tradeRegistNamesub/list/"+tradeRegistId,
				modalName: "子项",
				pagination: false,
				showSearch: false,
				showRefresh: false,
				showToggle: false,
				showColumns: false,
				showFooter: true,
				sidePagination: "client",
				columns: [{
					checkbox: true
				},
					{
						field: 'index',
						align: 'center',
						title: "序号",
						formatter: function (value, row, index) {
							var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
							return columnIndex + $.table.serialNumber(index);
						}
					},
					{
						field: 'trademarkName',
						align: 'center',
						title: '商标名称(首选)',
						formatter: function (value, row, index) {
							var html = $.common.sprintf("<input class='form-control' type='text' name='namesubs[%s].trademarkName' value='%s' readonly>", index, value);
							var html1 = $.common.sprintf("<input class='form-control' type='hidden' name='namesubs[%s].tradeUseSubId' value='%s'>" +
									"", index, row.tradeUseSubId);
							return html+html1;
						}
					},
					{
						field: 'trademarkName2',
						align: 'center',
						title: '商标名称(备选1)',
						formatter: function (value, row, index) {
							var html = $.common.sprintf("<input class='form-control' type='text' name='namesubs[%s].trademarkName2' value='%s' readonly>", index, value);
							return html;
						}
					},
					{
						field: 'trademarkName3',
						align: 'center',
						title: '商标名称(备选2)',
						formatter: function (value, row, index) {
							var html = $.common.sprintf("<input class='form-control' type='text' name='namesubs[%s].trademarkName3' value='%s' readonly>", index, value);
							return html;
						}
					},
					{
						field: 'trademarkName4',
						align: 'center',
						title: '商标名称(备选3)',
						formatter: function (value, row, index) {
							var html = $.common.sprintf("<input class='form-control' type='text' name='namesubs[%s].trademarkName4' value='%s' readonly>", index, value);
							return html;
						}
					},
				]
			};
			$.table.init(optionsH);

		}
		function addColumn(tableId) {
			var row = {
				trademarkName:"",
				trademarkName2:"",
				trademarkName3:"",
				trademarkName4:""
			};
			sub.addColumn(row, "bootstrap-table" + "H");
		}
	</script>
</body>
</html>