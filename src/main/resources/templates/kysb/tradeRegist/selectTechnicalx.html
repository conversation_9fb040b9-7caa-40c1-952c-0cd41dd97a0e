<!-- 选择组织封装

labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认附件
divClass:指定附件的div 的class 默认col-sm-8
orgCodeId:orgCode name id
orgNameId:orgName name id
selectType:S 单选 M 多选 默认S
level:组织层级 -1只显示公司
showLevel:显示组织层级
callback: 回调函数
isrequired:是否必填 默认false
value: 当前值
see:是否查看

-->

<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	orgCodeId=${orgCodeId==null?'orgCodeId':orgCodeId},
               	 	orgNameId=${orgNameId==null?'orgNameId':orgNameId},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择技术分类：':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
                    selectType=${selectType==null?'S':selectType},
                    type=${type==null?'1':type}
               	 	">

        <th:block th:if="${!see}">
            <div th:class="'input-group'+' '+${divClass}" th:onclick="choiceTechnical([[${orgCodeId}]],[[${orgNameId}]],[[${selectType}]],[[${level}]],[[${showLevel}]],[[${callback}]],[[${type}]])">
                <input th:name="${orgCodeId}" th:id="${orgCodeId}" type="hidden" th:value="${value}" /> <input
                    th:name="${orgNameId}" th:id="${orgNameId}" th:value="${T(com.baosight.bscdkj.ky.sb.utils.TradeServiceUtil).getTechnicalName(value)}" class="form-control  detailOrgOrUser" type="text"
                    th:required="${isrequired!=null && isrequired}"  readonly>
                <span class="input-group-addon"  ><i
                        class="fa fa-search " ></i></span>
            </div>
        </th:block>
        <th:block th:unless="${!see}">
            <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.ky.sb.utils.TradeServiceUtil).getTechnicalName(value)}"></div>
            <input th:name="${orgCodeId}" th:id="${orgCodeId}" type="hidden" th:value="${value}" /> <input
                th:name="${orgNameId}" th:id="${orgNameId}" th:value="${T(com.baosight.bscdkj.ky.sb.utils.TradeServiceUtil).getTechnicalName(value)}" class="form-control" type="hidden"/>
        </th:block>


    </th:block>
</div>




    <div th:fragment="choiceTechnical">
        <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	orgCodeId=${orgCodeId==null?'orgCodeId':orgCodeId},
               	 	orgNameId=${orgNameId==null?'orgNameId':orgNameId},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择服务：':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
                    selectType=${selectType==null?'S':selectType},
                    type=${type==null?'1':type}
               	 	">

            <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}"></label>
            <div th:class="${divClass}" th:if="${see!=null && !see}">
                <div class="input-group" th:onclick="choiceTechnical([[${orgCodeId}]],[[${orgNameId}]],[[${selectType}]],[[${level}]],[[${orgCode}]],[[${showLevel}]],[[${callback}]],[[${type}]])">
                    <input th:id="${orgCodeId}" th:name="${orgCodeId}" th:value="${value}" type="hidden" /> <input
                        class="form-control detailOrgOrUser" th:id="${orgNameId}" th:name="${orgNameId}"
                        th:value="${T(com.baosight.bscdkj.ky.sb.utils.TradeServiceUtil).getTechnicalName(value)}" type="text"  autocomplete="off" th:required="${isrequired!=null && isrequired}" readonly>
                    <span class="input-group-addon "><i
                            class="fa fa-search "></i></span>
                </div>
            </div>

            <div class="form-control-static" th:unless="${!see}" th:utext="${T(com.baosight.bscdkj.ky.sb.utils.TradeServiceUtil).getTechnicalName(value)}"></div>
        </th:block>
    </div>






</div>