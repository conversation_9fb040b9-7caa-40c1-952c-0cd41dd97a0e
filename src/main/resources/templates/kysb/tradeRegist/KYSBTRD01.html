<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('商标注册详细')" />
	<th:block th:include="kysb/countyr :: selectCountry"/>
	<th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
	<form class="form-horizontal m" id="form-demo" th:object="${tradeRegist}">
		<div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#jbxx" aria-expanded="false" class="collapsed">
							基本信息
							<span class="pull-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
								</span>
						</a>
					</h4>
				</div>
				<div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="form-group" hidden>
							<label class="col-sm-4 control-label">申请单位编码：</label>
							<div class="col-sm-8">
								<input name="tradeRegistId" th:field="*{tradeRegistId}" >
								<input name="orgCode" th:field="*{applyDeptCode}" class="form-control" type="text" >
							</div>
						</div>
						<div class="form-group">
							<div class="form-group">
								<div class="col-sm-12">
									<label class="col-sm-2 control-label">申请单位：</label>
									<div class="col-sm-10">
										<input name="applyDeptName" class="form-control" th:field="*{applyDeptName}" type="text">
									</div>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label">法定地址：</label>
									<div class="col-sm-8">
										<input name="deptAddress" class="form-control" th:field="*{deptAddress}" type="text">
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label is-required">单位电话：</label>
									<div class="col-sm-8">
										<input name="applyDeptPh" class="form-control" th:field="*{applyDeptPh}" type="text">
									</div>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label is-required">提出人（经办人）：</label>
									<div class="col-sm-8">
										<input name="applyPersonName" class="form-control" th:field="*{applyPersonName}" type="text">
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label">工号：</label>
									<div class="col-sm-8">
										<input name="applyPersonGh" class="form-control" th:field="*{applyPersonGh}" type="text"  readonly>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="col-sm-6">

								<div class="form-group">
									<label class="col-sm-4 control-label">EMAIL：</label>
									<div class="col-sm-8">
										<input name="applyPersonEmail" class="form-control" type="text" th:field="*{applyPersonEmail}">
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label is-required">手机号码：</label>
									<div class="col-sm-8">
										<input name="applyPersonPh" class="form-control" th:field="*{applyPersonPh}" type="tel">
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="panel-group" id="accordion2" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#sbxx" class="collapsed">
							商标信息
							<span class="pull-right">
									<i class="fa fa-chevron-down"></i>
								</span>
						</a>
					</h4>
				</div>
				<div id="sbxx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">商标名称：</label>
							<div class="col-sm-10">
								<input name="trademarkName" class="form-control"  th:field="*{trademarkName}" type="text">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label ">商标样式：</label>
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='tradeFile',name='tradeFile',see='true',
									 sourceId=*{tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeFile')">
							</div>
						</div>

						<div class="form-group">
							<label class="col-sm-2 control-label  is-required">商标含义：</label>
							<div class="col-sm-10">
                                <textarea name="trademarkMeaning" rows="7" cols="150" th:field="*{trademarkMeaning}"  class="form-control" rangelength="1,500"  required="required" readonly>
                                </textarea>
							</div>
						</div>
						<div class="form-group">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label  is-required">技术分类：</label>
									<div class="col-sm-8"
										 th:include="/kysb/tradeRegist/selectTechnicalx::init(isrequired=true,orgCodeId='techClass',selectType='M',type='2',orgNameId='techClassName',value=${tradeRegist.techClass})">
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-4 control-label">文档属性：</label>
									<div class="col-sm-8">
										<input name="docProperty" class="form-control" type="text" th:value="商标" readonly>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label ">商标注册类别：</label>
							<div class="col-sm-10">
								<input name="trademarkRegistType" class="form-control" type="hidden"  th:field="*{trademarkRegistType}" >
								<input  class="form-control" th:value="${tradeRegist.trademarkRegistTypeValue}"type="text" >
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">商品/服务项目：</label>
							<input name="commodityService" class="form-control" type="hidden"  th:field="*{commodityService}"  />
							<div class="col-sm-10"
								 th:include="/component/selectServce::init(isrequired=true,orgCodeId='commodityService',orgNameId='commodityServiceName',see='true',level='0',selectType='M',value=${tradeRegist.commodityService})">
							</div>
						</div>

						<div class="form-group">
							<label class="col-sm-2 control-label">商标标签：</label>
							<div class="col-sm-10">
								<th:block th:include="component/select :: init(id='trademarkLabel',multimultiple='true',see='true',
									 name='trademarkLabel' ,businessType='KYSB', dictCode='trademarkLabel',value=*{trademarkLabel})">
								</th:block>
							</div>
						</div>

						<div class="form-group">
							<label class="col-sm-2 control-label ">权利人：</label>
							<div class="col-sm-4">
								<input name="ownership" class="form-control" type="text"  th:field="*{ownership}" >
<!--								<input  class="form-control" th:value="${tradeRegist.ownershipValue}"type="text" >-->
							</div>
						</div>

						<div class="form-group">
							<label class="col-sm-2 control-label is-required">申请/展出国家/地区：</label>
							<div  th:include="kysb/countyr :: choiceCountry(divClass='col-sm-10',userCode='trade',userCodeId='countryRegion',see='true',
							userNameId='countryRegionName',selectType='M',value=${tradeRegist.countryRegion},isrequired=true)">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label  is-required">申请说明：</label>
							<div class="col-sm-10">
								<textarea name="applyRepresent" th:field="*{applyRepresent}" rows="7" cols="150" class="form-control" rangelength="2,500"   readonly>
								</textarea>
							</div>
						</div>
						<div style="height: 100px">
						</div>
					</div>
				</div>
			</div>
		</div>
	</form>
</div>
<script th:inline="javascript">
	$("#form-demo input").attr("readOnly",true);
	function submitHandler() {
		var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
		parent.layer.close(index);
	}
</script>
<div th:fragment="selectService">
	<script th:inline="javascript" type="text/javascript">
		var orgId = "orgId";
		var orgNameId = "deptName";

		function choiceService(orgCodeInputId, orgNameInputId, selectType, level, orgCode, showLevel, callback) {
			orgId = orgCodeInputId;
			orgNameId = orgNameInputId;
			if (selectType === undefined || selectType == null || selectType == '') {
				selectType = "S";
			}
			var url = ctx + "kysb/tradeService/selectServiceList?selectType=" + selectType;
			if (!(level === undefined) && level != null) {
				url += "&level=" + level;
			}
			if (!(showLevel === undefined) && showLevel != null) {
				url += "&showLevel=" + showLevel;
			}
			if (!(callback === undefined) && callback != null) {
				url += "&callback=" + callback;
			}
			url += "&values=" + $("#" + orgId).val();

			//debugger
			var options = {
				title: '选择服务',
				width: "580",
				height: '500',
				url: url,
				callBack: choiceServiceCallback
			};
			$.modal.openOptions(options);
		}

		function choiceServiceCallback(index, layero) {
			var tree = layero.find("iframe")[0].contentWindow.$._tree;
			var body = layer.getChildFrame('body', index);
			layero.find("iframe")[0].contentWindow.saveCheck();
			$("#" + orgId).val(body.find('#treeId').val());
			$("#" + orgNameId).val(body.find('#treeName').val());
			layer.close(index);
		}
	</script>
</div>
</body>
</html>