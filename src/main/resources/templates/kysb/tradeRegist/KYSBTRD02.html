<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('已处理列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse" >
        	<form id="formId" class="form-horizontal" style="width: 99%;">
				<div class="form-group">
					<label class="col-sm-1 control-label">商标名称:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="trademarkName" />
					</div>
					<input type="hidden" id = "registId" th:value="${registId}" />
					<label class="col-sm-1 control-label">注册号:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="regigterNo"/>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
				    <label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
		var prefix = ctx + "kysb/tradeRegist";
		var techClass = [[${@dict.getDictList('KYSB','techClass')}]];
		var ownership = [[${@dict.getDictList('KYSB','ownership')}]];
		var trademarkLabel = [[${@dict.getDictList('KYSB','trademarkLabel')}]];

		var registId = $("#registId").val();
        $(function() {
            var options = {
                url: prefix + "/pageTradeSelect/"+registId,
                queryParams: queryParams,
                modalName: "已处理",
                columns: [{
                    checkbox: true
                },
				{
					field: 'tradeRegistId',
					title: '主键',
					visible: false
				},
					{
						field: 'imgurl',
						title: '商标图样',
						formatter: function (value, row, index) {
							var s = '<img style="width:300;height:40px;"  src="'+value+'" />';
							return s;
						}
					},
					{
						field: 'trademarkName',
						title: '商标名称'
					},
					{
						field: 'regigterNo',
						title: '注册号'
					},
					{
						field: 'initRegisterDate',
						title: '注册日期',

					},
					{
						field: 'endTime',
						title: '有效期限'
					},
					{
						field: 'trademarkRegistTypeValue',
						title: '商标注册类别'
					},
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        var param = "'"+row.tradeRegistId+"','"+row.flowId+"'";
						actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="tradeUseList()"><i class="fa fa-edit"></i>查看使用信息</a> ');

						actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryYBDetail(' + param + ')"><i class="fa fa-edit"></i>查看</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
		function tradeUseList(){

			$.modal.openTab("商标使用",  ctx + "kysb/tradeUse" + "/KYSBTRU41/"+$("#registId").val() );
		};
        function queryParams(params) {
            var search = $.table.queryParams(params);
            search.businessNameLike = $("#businessNameLike").val();
            return search;
        }


		function queryYBDetail(businessId,processInstanceId){
			var url = prefix + "/queryYBDetail/KYSBTRD01/" + businessId + "/" + processInstanceId;
			// 选卡页方式打开并刷新当前页 isRefresh 是否刷新
			$.modal.openTab("商标详细", url, true);
		}
    </script>
</body>
</html>