<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('已处理列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse" >
        	<form id="formId" class="form-horizontal" style="width: 99%;">
				<div class="form-group">
					<label class="col-sm-1 control-label">单位电话:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="applyDeptPh" placeholder="支持模糊查询"/>
					</div>

					<label class="col-sm-1 control-label">经办人:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="applyPersonName" placeholder="支持模糊查询"/>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
				    <label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
		var prefix = ctx + "kysb/tradeRegist";
		var techClass = [[${@dict.getDictList('KYSB','techClass')}]];
		// var trademarkRegistType = [[${@dict.getDictList('KYSB','trademarkRegistType')}]];
		var ownership = [[${@dict.getDictList('KYSB','ownership')}]];
		// var commodityService = [[${@dict.getDictList('KYSB','commodityService')}]];
		// var countryRegion = [[${@dict.getDictList('KYSB','countryRegion')}]];
        $(function() {
            var options = {
                url: prefix + "/pageYB",
                queryParams: queryParams,
                modalName: "已处理",
                columns: [{
                    checkbox: true
                },
				{
					field: 'tradeRegistId',
					title: '主键',
					visible: false
				},
				{
					field: 'tradeNum',
					title: '流水号'
				},
				{
					field: 'applyDeptName',
					title: '申请单位'
				},
				{
					field: 'applyDeptPh',
					title: '单位电话'
				},
				{
					field: 'applyPersonName',
					title: '经办人'
				},
				// {
				// 	field: 'techClass',
				// 	title: '技术分类',
				// 	formatter:function (value,row,index) {
				// 		return $.table.selectDictLabel(techClass, value);
				// 	}
				// },
				// {
				// 	field: 'docProperty',
				// 	title: '文档属性'
				// },
				// {
				// 	field: 'trademarkRegistType',
				// 	title: '商标注册类别',
				// 	formatter:function (value,row,index) {
				//
				// 		return $.table.selectDictLabel(trademarkRegistType, value);
				// 	}
				// },
				// {
				// 	field: 'commodityService',
				// 	title: '商品/服务项目',
				// 	formatter:function (value,row,index) {
				//
				// 		return $.table.selectDictLabel(commodityService, value);
				// 	}
				// },
				// {
				// 	field: 'trademarkLabel',
				// 	title: '商标标签'
				// },
				{
					field: 'ownership',
					title: '权利人',
					// formatter:function (value,row,index) {
					// 	return $.table.selectDictLabel(ownership, value);
					// }
				},
				// {
				// 	field: 'countryRegion',
				// 	title: '申请/展出国家/地区',
				// 	formatter:function (value,row,index) {
				// 		return $.table.selectDictLabel(countryRegion, value);
				// 	}
				// },
				{
					field: 'createDate',
					title: '创建时间'
				},
				{
					field: 'currentOperator',
					title: '当前处理人',
					// formatter: function (value, row, index) {
					// 	var timeData = value.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g, '$1-$2-$3 $4:$5:$6');
					// 	return timeData;
					// }
				},
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        var param = "'"+row.businessId+"','"+row.flowId+"'";
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryYBDetail(' + param + ')"><i class="fa fa-edit"></i>查看</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
        
        function queryParams(params) {
            var search = $.table.queryParams(params);
            search.businessNameLike = $("#businessNameLike").val();
            return search;
        }
        
        function queryYBDetail(businessId,processInstanceId){
        	var url = prefix + "/queryYBDetail/KYSBTRC02/" + businessId + "/" + processInstanceId;
        	// 选卡页方式打开并刷新当前页 isRefresh 是否刷新
            $.modal.openTab("商标注册已处理详细", url, true);
        }
    </script>
</body>
</html>