<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('商标申请待办数量')" />
</head>
<body class="white-bg">
	<th:block th:include="include :: baseJs" />
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<table class="db">
			<th:block th:each="db : ${ei.blocks.result.rows}">
			<tr>
				<td class="td1">
					<a th:href="@{''}" th:text="${db.processName}+' / '+${db.currentActivityName}"></a>
				</td>
				<td class="td2">
					<a th:href="@{'/'}+'kysb/tradeRegist/toPage/KYSBTRB02?processCode='+${db.processCode}+'&activityCode='+${db.currentActivity}">
						<span class="span1" th:text="${db.todoNum}"></span>
						&nbsp;
						<span class="span2">份</span>
						&nbsp;&nbsp;
						<span>进入处理》</span>
					</a>
				</td>
			</tr>
			</th:block>
		</table>
	</div>
</body>
</html>