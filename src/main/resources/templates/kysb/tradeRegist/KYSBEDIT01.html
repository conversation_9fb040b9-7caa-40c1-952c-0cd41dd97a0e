<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增商标注册申请')" />
    <th:block th:include="kysb/countyr :: selectCountry"/>
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-tradeRegist-add" th:object="${tradeRegist}">
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" href="#jbxx" aria-expanded="false" class="collapsed">
                            基本信息
                            <span class="pull-right">
                                <i class="fa fa-chevron-down" aria-hidden="true"></i>
                            </span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="form-group" hidden>
                            <label class="col-sm-4 control-label">申请单位编码：</label>
                            <div class="col-sm-8">
                                <input name="tradeRegistId" th:field="*{tradeRegistId}" type="hidden">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <label class="col-sm-2 control-label is-required">流水号：</label>
                                    <div class="col-sm-10">
                                        <input name="tradeNum" th:field="*{tradeNum}"  class="form-control" type="text" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <label class="col-sm-2 control-label">申请提出单位：</label>
                                    <!--<div class="col-sm-10">
                                        <input name="applyDeptName" class="form-control" th:field="*{applyDeptName}" type="text">
                                    </div>-->
                                    <div class="col-sm-10"
                                         th:include="/component/selectOrg::init(isrequired=true,see='true',orgCodeId='applyDeptCode',orgNameId='applyDeptName',selectType='M',value=*{applyDeptCode})">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">单位电话：</label>
                                    <div class="col-sm-8">
                                        <input name="applyDeptPh" th:field="*{applyDeptPh}" class="form-control" isTel="true" type="text">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">提出人（经办人）：</label>
                                    <div class="col-sm-8">
                                        <input name="applyPersonName" class="form-control" th:value="*{applyPersonName}" type="text">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">工号：</label>
                                    <div class="col-sm-8">
                                        <input name="applyPersonGh" class="form-control" type="text" th:field="*{applyPersonGh}" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">EMAIL：</label>
                                    <div class="col-sm-8">
                                        <input name="applyPersonEmail" class="form-control" email="true" type="text" th:field="*{applyPersonEmail}">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">手机号码：</label>
                                    <div class="col-sm-8">
                                        <input name="applyPersonPh" class="form-control" isPhone="true" type="tel" th:field="*{applyPersonPh}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">商标法律状态：</label>
                                    <div class="col-sm-8">
                                        <input name="" class="form-control" type="text" value="注册后 自动生成" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label ">注册号：</label>
                                    <div class="col-sm-8">
                                        <input name="" class="form-control" type="tel" value="注册后 自动生成" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">注册日期：</label>
                                    <div class="col-sm-8">
                                        <input name="" class="form-control" type="text" value="注册后 自动生成" readonly>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label ">有效截止日：</label>
                                    <div class="col-sm-8">
                                        <input name="" class="form-control" type="tel" value="状态为终止时显示 自动带出" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#6">商标名称
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="6">
                <div class="panel-body">
                    <div class="row form-group">
                        <button class="btn btn-white btn-sm" onclick="addColumn('H')" type="button"><i
                                class="fa fa-plus"> 增加</i></button>
                        <button class="btn btn-white btn-sm" onclick="sub.delColumn()" type="button"><i
                                class="fa fa-minus"> 删除</i></button>
                        <div class="col-sm-12 select-table table-striped">
                            <table id="bootstrap-tableH"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group" id="accordion2" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" href="#sbxx" class="collapsed">
                            商标信息
                            <span class="pull-right">
									<i class="fa fa-chevron-down"></i>
								</span>
                        </a>
                    </h4>
                </div>
                <div id="sbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--							<div class="form-group">-->
                        <!--								<label class="col-sm-2 control-label is-required">商标中/英文名称（首选）：</label>-->
                        <!--								<div class="col-sm-10">-->
                        <!--									<input name="trademarkName" class="form-control" rangelength="1,100" type="text">-->
                        <!--								</div>-->
                        <!--							</div>-->
                        <!--							<div class="form-group">-->
                        <!--								<label class="col-sm-2 control-label ">商标中/英文名称（备选）：</label>-->
                        <!--								<div class="col-sm-10">-->
                        <!--									<input name="trademarkName2" class="form-control" rangelength="1,100" type="text">-->
                        <!--								</div>-->
                        <!--							</div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">商标样式及策划方案：</label>
                            <div class="col-sm-10">
                                <div  th:include="/component/attachment :: init(id='tradeFile',name='tradeFile',
                                 sourceId=*{tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeFile')"></div>
                                <!-- <div class="col-sm-8"
                             th:include="/component/attachment :: init(id='jspsfj',name='jspsfj',
                             sourceId=*{productSumGuid},sourceModule='KXCP',sourceLabel1='jspsfj')">
                        </div>-->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">商标含义：</label>
                            <div class="col-sm-10">
                                <textarea name="trademarkMeaning" rows="7" cols="150" th:field="*{trademarkMeaning}"  class="form-control" rangelength="1,500"  required="required"></textarea>
                            </div>
                        </div>
                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label  is-required">技术分类：</label>
<!--                                    <div class="col-sm-8">-->
<!--                                        <th:block th:include="component/select :: init(id='techClass', name='techClass',isfirst='true' ,businessType='KYSB', dictCode='techClass',value=*{techClass})">-->
<!--                                        </th:block>-->
<!--                                    </div>-->
                                    <div class="col-sm-8"
                                         th:include="/kysb/tradeRegist/selectTechnicalx::init(isrequired=true,orgCodeId='techClass',selectType='M',type='2',orgNameId='techClassName',value=*{techClass})">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">文档属性：</label>
                                    <div class="col-sm-8">
                                        <input name="docProperty" class="form-control" type="text" th:value="商标" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">商标注册类别：</label>
                            <!--								<div class="col-sm-10">-->
                            <!--									<th:block  th:include="component/select :: init(id='trademarkRegistType', name='trademarkRegistType',isfirst='true',multimultiple='true'-->
                            <!--                                ,businessType='KYSB', dictCode='trademarkRegistType',value=*{trademarkRegistType}) ">-->
                            <!--									</th:block>-->
                            <!--								</div>-->
                            <div class="col-sm-10">
                                <select class="form-control" name="trademarkRegistType" multiple="multiple">
                                    <option  th:each="type1:${tradeRegist.serviceList}" th:text="${type1.serviceName}" th:selected="${tradeRegist.trademarkRegistType.contains(type1.tradeServiceId)}" th:value="${type1.tradeServiceId}"></option>
                                </select>
                            </div>
                        </div>
                        <!--							<div class="form-group">-->
                        <!--								<label class="col-sm-2 control-label">商品/服务项目：</label>-->
                        <!--								<div class="col-sm-10">-->
                        <!--									<th:block  th:include="component/select :: init(id='commodityService', name='commodityService',isfirst='true',multimultiple='true'-->
                        <!--                                ,businessType='KYSB', dictCode='commodityService',value=*{commodityService}) ">-->
                        <!--									</th:block>-->
                        <!--								</div>-->
                        <!--							</div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label">商标形式：</label>
                            <div class="col-sm-4">
                                <th:block th:include="component/select :: init(id='layout', name='layout' ,businessType='KYSB', dictCode='layout',value=*{layout})">
                                </th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">商标标签：</label>
<!--                            <div class="col-sm-10">-->
<!--                                <input name="trademarkLabel" class="form-control" rangelength="1,50" type="text" th:field="*{trademarkLabel}">-->
<!--                            </div>-->
                            <div class="col-sm-10">
                                <th:block th:include="component/select :: init(id='trademarkLabel',multimultiple='true',
									 name='trademarkLabel' ,businessType='KYSB', dictCode='trademarkLabel',value=*{trademarkLabel})">
                                </th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">权利人：</label>
<!--                            <div class="col-sm-4">-->
<!--                                <th:block th:include="component/select :: init(id='ownership', name='ownership',isfirst='true',value=*{ownership} ,businessType='KYSB', dictCode='ownership')">-->
<!--                                </th:block>-->
<!--                            </div>-->
                            <div class="col-sm-4">
                                <select class="form-control" id="ownership" name="ownership">
                                    <option value="">请选择</option>
                                    <option  th:each="type:${tradeRegist.legalreprInfos}" th:text="${type.legalRepr}" th:selected="${type.legalRepr eq tradeRegist.ownership}" th:value="${type.legalRepr}"></option>
                                </select>
                                <!--                                        <input name="legalRepr" th:field="*{legalRepr}" rangelength="1,50" class="form-control" type="text">-->
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">法定地址：</label>
                            <div class="col-sm-4">
                                <input name="deptAddress" th:field="*{deptAddress}" class="form-control" rangelength="2,100" type="text" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">申请/展出国家/地区：</label>
                            <div  th:include="kysb/countyr :: choiceCountry(divClass='col-sm-10',userCode='trade',userCodeId='countryRegion',
                                userNameId='countryRegionName',selectType='M',value=*{countryRegion},isrequired=true)">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label  is-required">申请说明：</label>
                            <div class="col-sm-10">
									<textarea name="applyRepresent" rows="7" cols="150" th:field="*{applyRepresent}"
                                              class="form-control" rangelength="1,500"  required="required"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 流程意见end -->
        <!-- 流程相关信息 -->
        <th:block th:include="component/wfWorkFlow :: init(workFlow=${tradeRegist.workFlow})" />
        <!-- 流程相关信息end -->
    </form>
    <!-- 按钮区 -->
    <th:block th:include="kysb/tradeRegist/wfDetailButton :: init(workFlow=${tradeRegist.workFlow})" />
    <!-- 按钮区end -->
</div>

<script th:inline="javascript">
    var prefix = ctx + "kysb/tradeRegist"

    $("#form-tradeRegist-add").validate({
        focusCleanup : true
    });
        //tradeCountryName
    function doSubmit(transitionKey) {
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function() {
                //$("#workFlow_transitionKey").val(transitionKey); 这个js在提交组件里加了，没有用提交组件的需要这行
                $.operate.saveTabAlert(prefix + "/doSubmit", $(
                    '#form-tradeRegist-add').serialize());
            });
        }
    }

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/add", $('#form-tradeRegist-add')
                .serialize());
        }
    }
    var tradeRegistId =$("#tradeRegistId").val();
    var prefixLegar = ctx + "kysb/legalreprInfo";
    //法人信息联动法人地址
    $("#ownership").change(function (){
        var legalRepr = $("#ownership").val();
        if(legalRepr!=null && legalRepr!=""){
            var jsonData = {};
            jsonData["legalRepr"] = legalRepr;//法人
            $.ajax({//获取法人地址
                url: prefixLegar+"/getAddressByName",
                data: JSON.stringify(jsonData),
                dataType:"json",
                contentType: "application/json",
                type: 'POST',
                success: function (result) {
                    if (result.code == web_status.SUCCESS) {
                        $("[name=\"deptAddress\"]").val(result.data);
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    } else {
                        $.modal.alertError(result.msg);
                    }
                }

            })
        }
    })
    initSub();
    function initSub(){
        var optionsH = { // 推广移植后经济指标、产品、质量等情况
            id: "bootstrap-tableH",
            url: ctx+"kysb/tradeRegistNamesub/list/"+tradeRegistId,
            modalName: "子项",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            showFooter: true,
            sidePagination: "client",
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                        return columnIndex + $.table.serialNumber(index);
                    }
                },
                {
                    field: 'trademarkName',
                    align: 'center',
                    title: '商标名称(首选)',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='namesubs[%s].trademarkName' value='%s'>", index, value);
                        var html1 = $.common.sprintf("<input class='form-control' type='hidden' name='namesubs[%s].tradeUseSubId' value='%s'>" +
                            "", index, row.tradeUseSubId);
                        return html+html1;
                    }
                },
                {
                    field: 'trademarkName2',
                    align: 'center',
                    title: '商标名称(备选1)',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='namesubs[%s].trademarkName2' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'trademarkName3',
                    align: 'center',
                    title: '商标名称(备选2)',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='namesubs[%s].trademarkName3' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'trademarkName4',
                    align: 'center',
                    title: '商标名称(备选3)',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='namesubs[%s].trademarkName4' value='%s'>", index, value);
                        return html;
                    }
                },
            ]
        };
        $.table.init(optionsH);

    }
    function addColumn(tableId) {
        var row = {
            trademarkName:"",
            trademarkName2:"",
            trademarkName3:"",
            trademarkName4:""
        };
        sub.addColumn(row, "bootstrap-table" + "H");
    }
</script>
<!--选择技术分类 begin-->

<div th:fragment="selectTechnical">
    <script th:inline="javascript" type="text/javascript">
        var orgId = "orgId";
        var orgNameId = "deptName";
        function choiceTechnical(orgCodeInputId, orgNameInputId, selectType, level, showLevel, callback,type) {
            orgId = orgCodeInputId;
            orgNameId = orgNameInputId;
            if (selectType === undefined || selectType == null || selectType == '') {
                selectType = "S";
            }
            var url = ctx + "kysb/tradeTechnical/selectTecList?selectType=" + selectType;
            if (!(level === undefined) && level != null) {
                url += "&level=" + level;
            }
            if (!(showLevel === undefined) && showLevel != null) {
                url += "&showLevel=" + showLevel;
            }
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            if (!(type === undefined) && type != null) {
                url += "&type=" + type;
            }
            url += "&values=" + $("#" + orgId).val();

            //debugger
            var options = {
                title: '选择技术分类',
                width: "580",
                height: '500',
                url: url,
                callBack: choiceTechnicalCallback
            };
            $.modal.openOptions(options);
        }

        function choiceTechnicalCallback(index, layero) {
            var tree = layero.find("iframe")[0].contentWindow.$._tree;
            var body = layer.getChildFrame('body', index);
            layero.find("iframe")[0].contentWindow.saveCheck();

            $("#" + orgId).val(body.find('#treeId').val());
            $("#" + orgNameId).val(body.find('#treeName').val());
            layer.close(index);
        }
    </script>
</div>
<!--技术分类 end-->
</body>
</html>