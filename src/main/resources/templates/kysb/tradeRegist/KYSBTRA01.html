<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('商标注册申请列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">
					<label class="col-sm-1 control-label">单位电话:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="applyDeptPh" placeholder="支持模糊查询"/>
					</div>

					<label class="col-sm-1 control-label">经办人:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="applyPersonName" placeholder="支持模糊查询"/>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
				    <label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.addTab()" >
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeRegist";
		var techClass = [[${@dict.getDictList('KYSB','techClass')}]];
		var ownership = [[${@dict.getDictList('KYSB','ownership')}]];
		// var countryRegion = [[${@dict.getDictList('KYSB','countryRegion')}]];

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/newTradeRegist/KYSBTRA02",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "商标注册申请",
                columns: [{
                    checkbox: true
                },
				{
					field: 'tradeRegistId',
					title: '主键',
					visible: false
				},
				{
					field: 'applyDeptName',
					title: '申请单位'
				},
				{
					field: 'applyDeptPh',
					title: '单位电话'
				},
				{
					field: 'applyPersonName',
					title: '经办人'
				},

				{
					field: 'ownership',
					title: '权利人',

				},

				{
					field: 'createDate',
					title: '创建时间'
				},
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.tradeRegistId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.tradeRegistId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>