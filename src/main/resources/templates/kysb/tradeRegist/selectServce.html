<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('组织选择')" />
	<th:block th:include="include :: ztree-css" />
</head>
<style>
	body {
		height: auto;
		font-family: "Microsoft YaHei";
	}

	button {
		font-family: "SimSun", "Helvetica Neue", Helvetica, Arial;
	}
</style>
<body class="hold-transition box box-main">
<input id="treeId" name="treeId" type="hidden"  th:value="${values}"/>
<input id="treeName" name="treeName" type="hidden" th:value="${orgName}" />
<div class="wrapper">
	<div class="treeShowHideButton" onclick="$.tree.toggleSearch();">
		<label id="btnShow" title="显示搜索" style="display: none;">︾</label> <label
			id="btnHide" title="隐藏搜索">︽</label>
	</div>
	<div class="treeSearchInput" id="search">
		<label for="keyword">关键字：</label><input type="text" class="empty"
												id="keyword" maxlength="50">
		<button class="btn" id="btn" onclick="$.tree.searchNode()">
			搜索</button>
	</div>
	<div class="treeExpandCollapse" style="float: none;">
		<label class="check-box">
			<input type="checkbox" value="1" >展开/折叠</label>
		<label class="check-box" th:if="${selectType=='M'}">
			<input  type="checkbox"  value="2">全选/全不选</label>
<!--		<label class="check-box" th:if="${selectType=='M'}">-->
<!--			<input  type="checkbox" value="3" >父子联动</label>-->
	</div>


	<div id="tree" class="ztree ztree-border"></div>
</div>


<th:block th:include="include :: baseJs" />
<th:block th:include="include :: ztree-js" />
<script th:inline="javascript">

	$('.treeExpandCollapse input').on('ifChanged', function(obj){
		var type = $(this).val();
		var checked = obj.currentTarget.checked;
		if (type == 1) {
			if (checked) {
				$._tree.expandAll(true);
			} else {
				$._tree.expandAll(false);
			}
		} else if (type == "2") {
			if (checked) {
				$._tree.checkAllNodes(true);
			} else {
				$._tree.checkAllNodes(false);
			}
		} else if (type == "3") {
			if (checked) {
				$._tree.setting.check.chkboxType = { "Y": "ps", "N": "ps" };
			} else {
				$._tree.setting.check.chkboxType = { "Y": "", "N": "" };
			}
		}
	})
	$(function() {
		var url= ctx + "kysb/tradeService/treeData?"
		var level=[[${level}]];
		var values=[[${values}]];
		url+="level="+level;
		if(values!=null && values!=''){
			url+="&values="+values;
		}
		var selectedMulti=false;
		var enable=false;
		if([[${selectType=='M'}]]){
			selectedMulti=true;
			enable=true;
		}
		var options = {
			url: url,
			expandLevel: 0,
			onClick: zOnClick,
			onCheck: zonCheck,
			view:{selectedMulti:selectedMulti,fontCss:setVmCss},
			check:{enable:enable,chkboxType:  { "Y": "", "N": "" }},
			async:{
				enable: true,
				type: "get",//根据请求类型自己定义
				url: ctx + "kysb/tradeService/treeData",
				autoParam: ["id=parentCode"],//这个是会自动加上的参数，这里的参数，可以用别名，例如：id=Path,传参的时候就是ids = '1'；但是需要注意的是，这里的参数只支持ztree设置的数据属性，例如我们想传递Path字段，就不能在这里自动匹配了，需要另外写方法了
				otherParam:{"values":function(){return values},"level":function (){
						if(level!=null &&level!=''){
							return level;
						}
						return null;
					}},
				dataFilter: function(treeId, parentNode, resData){
					//这里要过滤你的数据，把请求回来的数据组装成你想要的格式,resData就是请求接口返回的数据
					//我们假装这里的数据就是我们自己想要的
					return resData

				}

			},
			onAsyncSuccess : zTreeOnAsyncSuccess
		};
		$.tree.init(options);
	});
	function setVmCss(treeId,treeNode){
		if(treeNode.level==0){
			return {color : "#5268A5"}
		}
		if(treeNode.level==1){
			return {color : "#178AA1"}
		}
		if(treeNode.level==2){
			return {color : "#69A35B"}
		}
	}
	//点击名称
	function zOnClick(event, treeId, treeNode) {

		var treeId = treeNode.id;
		var treeName = treeNode.name;
		$("#treeId").val(treeId);
		var name=getFilePath(treeNode);
		var nameList=name.split("/")
		var showName="";
		for(var i=0;i<nameList.length;i++){
			if(nameList[i]!=treeName){
				showName+=nameList[i]+"/";
			}
		}
		showName+=treeName
		var showLevel=[[${showLevel!=null?showLevel:1}]]
		showName=showName.substring(find(showName,"/",showLevel)+1,showName.length);
		$("#treeName").val(showName);
		if([[${selectType=='M'}]]){
			if(treeNode.checked){
				$._tree.checkNode(treeNode);
			}else{
				$._tree.checkNode(treeNode, true, true);
			}

		}

	}
	function getFilePath(treeObj){
		if(treeObj==null)return "";
		var filename = treeObj.name;
		var pNode = treeObj.getParentNode();
		if(pNode!=null){
			filename = getFilePath(pNode) +"/"+ filename;
		}
		return filename;
	}
	function zTreeOnAsyncSuccess(event, treeId, treeNode, msg) {
		if (!$._tree) {
			alert("error!");
			return
		}
		// var selectedNode = $._tree.getSelectedNodes();
		// var nodes = $._tree.getNodes();
		// $._tree.expandNode(nodes[0], true);
	}
	//复选框
	function zonCheck(event, treeId, treeNode){
		console.log("1")
	}

	function saveCheck(){
		if([[${selectType=='M'}]]){
			//组织编码
			$("#treeId").val($.tree.getCheckedNodes("id"));

			var nodes = $._tree.getCheckedNodes(true);
			var showNames=$.map(nodes, function (row) {

				var ztreeName=row["name"];
				// var name=getFilePath(row);
				// var nameList=name.split("/")
				var showName="";
				// for(var i=0;i<nameList.length;i++){
				// 	if(nameList[i]!=ztreeName){
				// 		showName+=nameList[i]+"/";
				// 	}
				// }
				 showName+=ztreeName
				var showLevel=[[${showLevel!=null?showLevel:1}]]
				showName=showName.substring(find(showName,"/",showLevel)+1,showName.length);
				return showName;
			}).join();
			$("#treeName").val(showNames);
		}
		var callback= [[${callback}]]
		if(callback!=null && callback!=''){
			parent.eval(callback+'("'+$("#treeId").val()+'","'+$("#treeName").val()+'")');
		}
	}


	function find(str,cha,num){
		var x=str.indexOf(cha);
		for(var i=0;i<num;i++){
			x=str.indexOf(cha,x+1);
		}
		return x;
	}
</script>
</body>
</html>
