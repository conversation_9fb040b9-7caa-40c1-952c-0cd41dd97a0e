<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('商标许可详细')" />

<th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="wrapper wrapper-content">
		<form class="form-horizontal m" id="form-demo" th:object="${tradeToobje}">
<!--			<div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">-->
<!--				<div class="panel panel-default">-->
<!--					<div class="panel-heading">-->
<!--						<h4 class="panel-title">-->
<!--							<a data-toggle="collapse" data-parent="#version"-->
<!--							   href="#jbxx" aria-expanded="false" class="collapsed">申请基本信息-->
<!--								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;-->
<!--								<span class="pull-right"><i class="fa fa-chevron-down"-->
<!--															aria-hidden="true"></i></span>-->
<!--							</a>-->
<!--						</h4>-->
<!--					</div>-->
<!--					<div id="registxx" class="panel-collapse collapse in"-->
<!--						 aria-expanded="false">-->
<!--						<div class="form-group">-->
<!--							<label class="col-sm-2 control-label is-required">流水号：</label>-->
<!--							<div class="col-sm-10">-->
<!--								<input  th:value="${tradeToobje.tradeRegist.tradeNum}" class="form-control" type="text" readonly>-->
<!--							</div>-->
<!--						</div>-->

<!--						<div class="form-group">-->
<!--							<label class="col-sm-2 control-label is-required">权利人：</label>-->
<!--							<div class="col-sm-10">-->
<!--								<th:block th:include="component/select :: init(id='ownership',see='true',value =${tradeToobje.tradeRegist.ownership},isfirst='true' ,businessType='KYSB', dictCode='ownership')">-->
<!--								</th:block>-->
<!--							</div>-->
<!--						</div>-->

<!--						<div class="form-group">-->
<!--							<label class="col-sm-2 control-label is-required">商标中/英文名称（首选）：</label>-->
<!--							<div class="col-sm-10">-->
<!--								<input name="trademarkName" class="form-control"  th:value="${tradeToobje.tradeRegist.trademarkName}" type="text" readonly>-->
<!--							</div>-->
<!--						</div>-->
<!--						<div class="form-group">-->
<!--							<label class="col-sm-2 control-label ">商标中/英文名称（备选）：</label>-->
<!--							<div class="col-sm-10">-->
<!--								<input name="trademarkName2"  th:value="${tradeToobje.tradeRegist.trademarkName2}" class="form-control" type="text" readonly>-->
<!--							</div>-->
<!--						</div>-->
<!--						<div class="form-group">-->
<!--							<div class="col-sm-6">-->
<!--								<div class="form-group">-->
<!--									<label class="col-sm-4 control-label">单位地址：</label>-->
<!--									<div class="col-sm-8">-->
<!--										<input name="deptAddress" class="form-control" th:value="${tradeToobje.tradeRegist.deptAddress}" type="text" readonly>-->
<!--									</div>-->
<!--								</div>-->
<!--							</div>-->
<!--							<div class="col-sm-6">-->
<!--								<div class="form-group">-->
<!--									<label class="col-sm-4 control-label is-required">单位电话：</label>-->
<!--									<div class="col-sm-8">-->
<!--										<input name="applyDeptPh" class="form-control" th:value="${tradeToobje.tradeRegist.applyDeptPh}" type="text" readonly>-->
<!--									</div>-->
<!--								</div>-->
<!--							</div>-->
<!--						</div>-->
<!--						<div class="form-group">-->
<!--							<div class="col-sm-6">-->
<!--								<div class="form-group">-->
<!--									<label class="col-sm-4 control-label is-required">经办人：</label>-->
<!--									<div class="col-sm-8">-->
<!--										<input name="applyPersonName" class="form-control" th:value="${tradeToobje.tradeRegist.applyPersonName}" type="text" readonly>-->
<!--									</div>-->
<!--								</div>-->
<!--							</div>-->
<!--							<div class="col-sm-6">-->
<!--								<div class="form-group">-->
<!--									<label class="col-sm-4 control-label">工号：</label>-->
<!--									<div class="col-sm-8">-->
<!--										<input name="applyPersonGh" class="form-control" th:value="${tradeToobje.tradeRegist.applyPersonGh}" type="text"  readonly>-->
<!--									</div>-->
<!--								</div>-->
<!--							</div>-->
<!--						</div>-->
<!--						<div class="form-group">-->
<!--							<div class="col-sm-6">-->

<!--								<div class="form-group">-->
<!--									<label class="col-sm-4 control-label">EMAIL：</label>-->
<!--									<div class="col-sm-8">-->
<!--										<input name="applyPersonEmail" class="form-control" type="text" th:value="${tradeToobje.tradeRegist.applyPersonEmail}" readonly>-->
<!--									</div>-->
<!--								</div>-->
<!--							</div>-->
<!--							<div class="col-sm-6">-->

<!--								<div class="form-group">-->
<!--									<label class="col-sm-4 control-label is-required">手机号码：</label>-->
<!--									<div class="col-sm-8">-->
<!--										<input name="applyPersonPh" class="form-control" th:value="${tradeToobje.tradeRegist.applyPersonPh}" type="tel" readonly>-->
<!--									</div>-->
<!--								</div>-->
<!--							</div>-->
<!--						</div>-->
<!--					</div>-->
<!--				</div>-->
<!--			</div>-->
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#qlr">权利人信息
							<span class="pull-right">
								<i aria-hidden="true" class="fa fa-chevron-down"></i>
							</span>
						</a>
					</h4>
				</div>
				<!--折叠区域-->
				<div aria-expanded="false" class="panel-collapse collapse in" id="qlr">
					<div class="panel-body">
						<div class="row form-group">
							<button class="btn btn-white btn-sm" onclick="addColumnQ('Q')" type="button"><i
									class="fa fa-plus"> 增加</i></button>
							<button class="btn btn-white btn-sm" onclick="sub.delColumn()" type="button"><i
									class="fa fa-minus"> 删除</i></button>
							<div class="col-sm-12 select-table table-striped">
								<table id="bootstrap-tableQ" class="table table-hover"></table>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#6">涉案商标信息
							<span class="pull-right">
								<i aria-hidden="true" class="fa fa-chevron-down"></i>
							</span>
						</a>
					</h4>
				</div>
				<!--折叠区域-->
				<div aria-expanded="false" class="panel-collapse collapse in" id="6">
					<div class="panel-body">
						<div class="row form-group">
							<button class="btn btn-white btn-sm" onclick="addColumn('H')" type="button"><i
									class="fa fa-plus"> 增加</i></button>
							<button class="btn btn-white btn-sm" onclick="sub.delColumn()" type="button"><i
									class="fa fa-minus"> 删除</i></button>
							<div class="col-sm-12 select-table table-striped">
								<table id="bootstrap-tableH" class="table table-hover"></table>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" data-parent="#version"
						   href="#yyxx" aria-expanded="false" class="collapsed">案件信息							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down"
														aria-hidden="true"></i></span>
						</a>
					</h4>
				</div>
				<div id="yyxx" class="panel-collapse collapse in"
					 aria-expanded="false">
					<div class="panel-body">
						<input name="tradeToobjeId" id = "tradeToobjeId" th:value="${tradeToobje.tradeToobjeId}" type="hidden">
						<div class="form-group" hidden>
							<label class="col-sm-4 control-label is-required">商标注册主键：</label>
							<div class="col-sm-8">
								<input name="tradeRegistId" th:value="${tradeToobje.tradeRegistId}" class="form-control" type="text" required>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label  is-required">案件性质：</label>
							<div class="col-sm-10">
								<th:block th:include="component/select :: init(id='objectionType', name='objectionType',isfirst='true' ,see='true',
                                     businessType='KYSB', dictCode='objectionType',value=*{objectionType})">
								</th:block>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label  is-required">具体类型：</label>
							<div class="col-sm-10">
								<th:block th:include="component/select :: init(id='yytype', name='yytype',isfirst='true' ,see='true',
                                     businessType='KYSB', dictCode='yytype',value=*{yytype})">
								</th:block>
								<input type="hidden" th:field="*{yytype}">

							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label  is-required">归口单位：</label>
							<div class="col-sm-10">
								<input type="text" class="form-control" th:field="*{ownership}" readonly>
							</div>
						</div>
						<div th:if='${ tradeToobje.workFlow.currentActivity eq "Manual2"}'>
							<div class="form-group">
								<label class="col-sm-2 control-label  is-required">是否应诉：</label>
								<div class="col-sm-10">
									<th:block th:include="component/select :: init(id='isLawsuit', name='isLawsuit',isfirst='true',
										 businessType='KYSB', dictCode='isLawsuit',value=*{isLawsuit})">
									</th:block>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">综合意见：</label>
								<div class="col-sm-10">
									<textarea name="opinion" rows="7" cols="150" th:field="*{opinion}"  class="form-control" rangelength="1,500"  required="required" readonly>
									</textarea>
								</div>
							</div>
						</div>
						<div th:if='${ tradeToobje.workFlow.currentActivity ne "Manual2" or  tradeToobje.status eq "end"}'>
							<div class="form-group">
								<label class="col-sm-2 control-label  is-required">是否应诉：</label>
								<div class="col-sm-10">
									<th:block th:include="component/select :: init(id='isLawsuit', name='isLawsuit',isfirst='true' ,see='true',
										 businessType='KYSB', dictCode='isLawsuit',value=*{isLawsuit})">
									</th:block>
									<input type="hidden" name='isLawsuit' th:value="${tradeToobje.isLawsuit}">
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">综合意见：</label>
								<div class="col-sm-10">
								<textarea name="opinion" rows="7" cols="150" th:field="*{opinion}"  class="form-control" rangelength="1,500"  required="required" readonly>
								</textarea>
								</div>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">代理机构建议：</label>
							<div class="col-sm-10">
								<textarea name="agencySug" rows="7" cols="150" th:field="*{agencySug}"  class="form-control" rangelength="1,500"  required="required" readonly>
								</textarea>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">案件通知书：</label>
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='otherTrade',name='otherTrade',see='true',
                                     sourceId=*{tradeToobjeId},sourceModule='KYSB',sourceLabel1='otherTrade')">
							</div>
						</div>
					</div>
				</div>
<!--				<input type="text" th:value="${tradeToobje.workFlow.currentActivity}">-->
				<div th:if='${tradeToobje.workFlow.currentActivity eq "Manual7" or  tradeToobje.status eq "end" or tradeToobje.workFlow.currentActivity eq "KYSBTRYYSLTZSLR" or tradeToobje.workFlow.currentActivity eq "Manual9" or tradeToobje.workFlow.currentActivity eq "ManualTJZJ" or tradeToobje.workFlow.currentActivity eq "Manual11"}'>
					<div class="form-group">
						<label class="col-sm-2 control-label  is-required">是否联合集团：</label>
						<div class="col-sm-10">
							<th:block th:include="component/select :: init(id='jointGroup', name='jointGroup',see='true',
										 businessType='KYSB', dictCode='jointGroup',value=*{jointGroup})">
							</th:block>
						</div>
					</div>
					<div class="form-group" >
						<label class="col-sm-2 control-label">委托手续及相关材料：</label>
						<div class="col-sm-10"
							 th:include="/component/attachment :: init(id='objeBczjFile',name='objeBczjFile',see='true',
								 sourceId=*{tradeToobjeId},sourceModule='KYSB',sourceLabel1='objeBczjFile')">
						</div>
					</div>

				</div>
				<div th:if='${tradeToobje.workFlow.currentActivity eq "Manual9" or tradeToobje.workFlow.currentActivity eq "ManualTJZJ" or tradeToobje.workFlow.currentActivity eq "Manual11" or  tradeToobje.status eq "end"}'><!-- 案件拆分材料 -->
					<div class="form-group" >
						<label class="col-sm-2 control-label">受理通知书：</label>
						<div class="col-sm-10"
							 th:include="/component/attachment :: init(id='objeSltzsFile',name='objeSltzsFile',see='true',
								 sourceId=*{tradeToobjeId},sourceModule='KYSB',sourceLabel1='objeSltzsFile')">
						</div>
					</div>
				</div>

				<div th:if='${tradeToobje.workFlow.currentActivity eq "ManualTJZJ" or tradeToobje.workFlow.currentActivity eq "Manual11" or  tradeToobje.status eq "end"}'><!-- 补充证据 -->
					<div class="form-group" >
						<label class="col-sm-2 control-label">补充证据：</label>
						<div class="col-sm-10"
							 th:include="/component/attachment :: init(id='objeWtclFile',name='objeWtclFile',see='true',
								 sourceId=*{tradeToobjeId},sourceModule='KYSB',sourceLabel1='objeWtclFile')">
						</div>
					</div>
				</div>

				<div th:if='${tradeToobje.status eq "end"}'><!-- 结果录入	 -->
					<div class="form-group">
						<label class="col-sm-2 control-label is-required">审查结果：</label>
						<div class="col-sm-10">
							<th:block th:include="component/select :: init(id='countryDecision', name='countryDecision',isfirst='true' ,businessType='KYSB', dictCode='countryDecision',value=*{countryDecision})">
							</th:block>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-2 control-label is-required">综合意见：</label>
						<div class="col-sm-10">
							<textarea name="opinion" rows="7" cols="150" th:field="*{countryOpinion}"  class="form-control" rangelength="1,500"  required="required" readonly>
							</textarea>
						</div>
					</div>
					<div class="form-group" >
						<label class="col-sm-2 control-label">决定通知书：</label>
						<div class="col-sm-10"
							 th:include="/component/attachment :: init(id='countryDecisionFile',name='countryDecisionFile',see='true',
												 sourceId=*{tradeToobjeId},sourceModule='KYSB',sourceLabel1='countryDecisionFile')">
						</div>
					</div>
				</div>
			</div>
			<div style="height: 100px">
			</div>
		</form>
		<!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">		
			<!-- 流程跟踪 -->
			<th:block th:include="component/wfCommentList :: init(processInstanceId=${processInstanceId})" />
			<button type="button" class="btn btn-danger" onclick="closeItem()">
				<i class="fa fa-reply-all"></i>
				关 闭
			</button>
		</div>
		<!--按钮区end-->
	</div>
	<script th:inline="javascript">
		$("#form-demo input").attr("readOnly",true);
		$("#form-demo select").attr("disabled",true);
		var useId =$("#tradeToobjeId").val();
		var optionsH = { // 推广移植后经济指标、产品、质量等情况
			id: "bootstrap-tableH",
			url: ctx + "kysb/tradeObjeCase"+"/list/"+useId,
			pagination: false,
			showSearch: false,
			showRefresh: false,
			showToggle: false,
			showColumns: false,
			sidePagination: "client",
			columns: [{
				checkbox: true
			},
				{
					field: 'orderNum',
					align: 'center',
					title: "序号",
					formatter: function (value, row, index) {
						var columnIndex = $.common.sprintf("<input type='hidden' name='objeCaseList[%s].index' value='%s'>", index, $.table.serialNumber(index));
						var columnId = $.common.sprintf("<input type='hidden' name='objeCaseList[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
						return columnIndex + $.table.serialNumber(index) + columnId;
					}
				},
				{
					field: 'toobjeOwner',
					align: 'center',
					title: '权利人',
					formatter: function (value, row, index) {
						var html = $.common.sprintf("<input class='form-control' type='text' name='objeCaseList[%s].toobjeOwner' value='%s' readonly>", index, value);
						return html ;
					}
				},
				// {
				// 	field: 'tradeToobjeId',
				// 	align: 'center',
				// 	title: '涉案商标异议主键',
				// 	formatter: function (value, row, index) {
				// 		var html = $.common.sprintf("<input class='form-control' type='hidden' name='objeCaseList[%s].tradeToobjeId' value='%s' readonly>", index, value);
				// 		return html;
				// 	}
				// },
				{
					field: 'objeTradeName',
					align: 'center',
					title: '商标名称',
					formatter: function (value, row, index) {
						var html = $.common.sprintf("<input class='form-control' type='text' name='objeCaseList[%s].objeTradeName' value='%s' readonly>", index, value);
						return html;
					}
				},
				{
					field: 'objeTradeType',
					align: 'center',
					title: '类别',
					formatter: function (value, row, index) {
						var html = $.common.sprintf("<input class='form-control' type='text' name='objeCaseList[%s].objeTradeType' value='%s' readonly>", index, value);
						return html;
					}
				},
				{
					field: 'objeTradeDate',
					align: 'center',
					title: '申请日',
					formatter: function (value, row, index) {
						var html = $.common.sprintf("<input class='form-control' type='date' name='objeCaseList[%s].objeTradeDate' value='%s' readonly>", index, value);
						return html;
					}
				},
				{
					field: 'objeTradeRegdate',
					align: 'center',
					title: '注册日',
					formatter: function (value, row, index) {
						var html = $.common.sprintf("<input class='form-control' type='date' name='objeCaseList[%s].objeTradeRegdate' value='%s' readonly>", index, value);
						return html;
					}
				},
				{
					field: 'closeDate',
					align: 'center',
					title: '应诉截止日期',
					formatter: function (value, row, index) {
						var html = $.common.sprintf("<input class='form-control' type='date' name='objeCaseList[%s].closeDate' value='%s' readonly>", index, value);
						return html;
					}
				},
			]
		};
		$.table.init(optionsH);
		initQlrSub();
		function initQlrSub() {
			var useId = $("#tradeToobjeId").val();
			var optionsQ = { // 推广移植后经济指标、产品、质量等情况
				id: "bootstrap-tableQ",
				url: ctx + "kysb/tradeObjeCase" + "/qlrInfolist/" + useId,
				pagination: false,
				showSearch: false,
				showRefresh: false,
				showToggle: false,
				showColumns: false,
				sidePagination: "client",
				columns: [{
					checkbox: true
				},
					{
						field: 'toobjeOwner',
						align: 'center',
						title: '权利人',
						formatter: function (value, row, index) {
							var html = $.common.sprintf("<input class='form-control' type='text' name='qlrInfoList[%s].toobjeOwner' value='%s' readonly>", index, value);
							return html ;
						}
					},
					{
						field: 'ownerMainBusi',
						align: 'center',
						title: '权利人主营业务',
						formatter: function (value, row, index) {
							var html = $.common.sprintf("<input class='form-control' type='text' name='qlrInfoList[%s].ownerMainBusi' value='%s' readonly>", index, value);
							return html;
						}
					},
					{
						field: 'ownerOtherTrade',
						align: 'center',
						title: '权利人其他已注册商标简介',
						formatter: function (value, row, index) {
							var html = $.common.sprintf("<input class='form-control ' type='test' name='qlrInfoList[%s].ownerOtherTrade' value='%s' readonly>", index, value);

							return html;
						}
					},
				]
			};
			$.table.init(optionsQ);


		}
		function addColumnQ(tableId) {
			var row = {
				orderNum: "",
				toobjeOwner: "",
				ownerMainBusi: "",
				ownerOtherTrade:"",
			};
			sub.addColumn(row, "bootstrap-table" + "Q");
		}
	</script>
</body>
</html>