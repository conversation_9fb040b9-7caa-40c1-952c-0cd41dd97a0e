<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('已处理列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse" >
			<input type="hidden" id = "registId" th:value="${registId}" />

            <div class="form-group">
                <label class="col-sm-2 control-label is-required">商标使用状态：</label>
                <div class="col-sm-2">
                    <th:block  th:include="component/select :: init(id='confirmUse', name='confirmUse',isfirst='true' ,businessType='KYSB', dictCode='useSituation') ">
                    </th:block>
                </div>
            </div>
		</div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
		var registId = $("#registId").val();
        var useSituation = [[${@dict.getDictList('KYSB','useSituation')}]];

        var prefix = ctx + "kysb/tradeContinue";
		$(function() {
			var options = {
				url: prefix + "/pageTradeSelect/"+registId,
				queryParams: queryParams,
				modalName: "商标续展-驳回复审（子）",
				columns: [{
					checkbox: true
				},
                {
                    field: 'tradeContinueId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'confirmUse',
                    title: '商标使用状态',
                    formatter:function (value,row,index) {
                        alert(value)
                        return $.table.selectDictLabel(useSituation, value);
                    }
                },
                {
                    field: 'opinion',
                    title: '综合意见'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var param = "'" + row.tradeContinueId + "'";
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryYBDetail(' + param + ')"><i class="fa fa-edit"></i>查看</a> ');
                        return actions.join('');
                    }
                }]
			};
			$.table.init(options);
		});
        
        function queryParams(params) {
            var search = $.table.queryParams(params);
            search.businessNameLike = $("#businessNameLike").val();
            return search;
        }
		function queryYBDetail(businessId){
			var url = prefix + "/queryYBDetail/KYSBTRF42/" + businessId ;
			// 选卡页方式打开并刷新当前页 isRefresh 是否刷新
			$.modal.openTab("商标续展", url, true);
		}
    </script>
</body>
</html>