<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('已处理列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse" >
        	<form id="formId" class="form-horizontal" style="width: 99%;">
				<div class="form-group">
					<label class="col-sm-2 control-label is-required">商标使用状态：</label>
					<div class="col-sm-2">
						<th:block  th:include="component/select :: init(id='confirmUse', name='confirmUse',isfirst='true' ,businessType='KYSB', dictCode='useSituation') ">
						</th:block>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
				    <label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
		var prefix = ctx + "kysb/tradeContinue";
		var useSituation = [[${@dict.getDictList('KYSB','useSituation')}]];

		$(function() {
			var options = {
				url: prefix + "/pageYB",
				queryParams: queryParams,
				modalName: "商标注册-驳回复审（子）",
				columns: [{
					checkbox: true
				},
				{
					field: 'tradeContinueId',
					title: '主键',
					visible: false
				},
				{
					field: 'confirmUse',
					title: '商标使用状态',
					formatter:function (value,row,index) {
						return $.table.selectDictLabel(useSituation, value);
					}
				},
				{
					field: 'opinion',
					title: '综合意见'
				},
				{
					field: 'createUserLabel',
					title: '创建人'
				},
				{
					field: 'createDate',
					title: '创建时间'
				},
				{
					field: 'currentOperator',
					title: '当前处理人',
					// formatter: function (value, row, index) {
					// 	var timeData = value.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g, '$1-$2-$3 $4:$5:$6');
					// 	return timeData;
					// }
				},
				{
					title: '操作',
					align: 'center',
					formatter: function(value, row, index) {
						var actions = [];
						var param = "'" + row.businessId + "','" + row.flowId + "'";
						actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryYBDetail(' + param + ')"><i class="fa fa-edit"></i>查看</a> ');
						return actions.join('');
					}
				}]
			};
			$.table.init(options);
		});
        
        function queryParams(params) {
            var search = $.table.queryParams(params);
            search.businessNameLike = $("#businessNameLike").val();
            return search;
        }
        
        function queryYBDetail(businessId,processInstanceId){
        	var url = prefix + "/queryYBDetail/KYSBTRF22/" + businessId + "/" + processInstanceId;
        	// 选卡页方式打开并刷新当前页 isRefresh 是否刷新
            $.modal.openTab("商标续展已处理详细", url, true);
        }
    </script>
</body>
</html>