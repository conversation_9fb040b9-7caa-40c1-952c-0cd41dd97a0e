<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改商标续展')" />

    <th:block th:include="include :: baseJs" />
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: summernote-js"/>
    <th:block th:include="include :: jquery-tmpl"/>
    <th:block th:include="include :: sub-tab-commons"/>
    <th:block th:include="kczginclude :: toolbox-css" />
    <th:block th:include="kczginclude :: toolbox-js" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-tradeContinue-edit" th:object="${tradeContinue}">
        <div class="panel-group" id="accordion1" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#sbxx" aria-expanded="false" class="collapsed">商标信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="sbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="form-group">
                        <label class="col-sm-2 control-label ">商标列表：</label>
                        <div class="col-sm-2" style="color: #1c6ac7">
                            <input type="button" class="form-control" onclick="tradeRegistList()" value="点击查看">
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!--使用情况概述-->
        <div class="panel panel-default scrollspy-item" id="row1">
            <div class="panel-heading" role="tab" id="headingTwo">
                <h4 class="panel-title" toolbox-title="使用情况概述">
                    <a role="button" data-toggle="collapse" data-parent="#collapseTwo" href="#collapseTwo"
                       aria-expanded="false" aria-controls="collapseTwo">
                        使用情况概述
                    </a>
                    <span class="pull-right">
                                 </span>
                    <div class="ibox-tools">
                    </div>
                </h4>
            </div>
            <div id="collapseTwo" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingTwo">
                <div class="panel-body">
                    <div class="click2edit  wrapper" id="requestReportEdit">
                    </div>
                </div>
            </div>
        </div>
        <input id="useSituation" th:value="${tradeContinue.useSituation}" name="useSituation" type="hidden">
        <div class="panel-group" id="accordion" role="tablist"  aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">续展信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>

                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <input name="tradeContinueId" th:value="${tradeContinue.tradeContinueId}" type="hidden">
                        <div class="form-group" hidden>
                            <label class="col-sm-4 control-label">商标使用主键：</label>
                            <div class="col-sm-8">
                                <input name="tradeRegistId" th:value="${tradeContinue.tradeRegistId}" class="form-control" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">商标使用状态：</label>
<!--                            <div class="col-sm-10">-->
<!--                                <input name="confirmUse"  th:field="*{confirmUse}" rangelength="1,10" class="form-control" type="text">-->
<!--                            </div>-->
                            <div class="col-sm-10">
                                <th:block  th:include="component/select :: init(id='confirmUse', name='confirmUse',isfirst='true' ,businessType='KYSB', dictCode='useSituation',value=*{confirmUse}) ">
                                </th:block>
                            </div>
                        </div>
<!--                        <div class="form-group">-->
<!--                            <label class="col-sm-2 control-label is-required">使用情况概述：</label>-->
<!--&lt;!&ndash;                            <div class="col-sm-10">&ndash;&gt;-->
<!--&lt;!&ndash;                                <input name="useSituation" th:field="*{useSituation}" rangelength="1,500" class="form-control" type="text">&ndash;&gt;-->
<!--&lt;!&ndash;                            </div>&ndash;&gt;-->
<!--                            <div class="col-sm-10"-->
<!--                                 th:include="/component/attachment :: init(id='useSituation',name='tradePermSub.useSituation',-->
<!--											 sourceId=*{tradeContinueId},sourceModule='KYSB',sourceLabel1='useSituation')">-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">相关附件：</label>
                            <div class="col-sm-10"
                                 th:include="/component/attachment :: init(id='continueXgFile',name='continueXgFile',
                                     sourceId=*{tradeContinueId},sourceModule='KYSB',sourceLabel1='continueXgFile')">
                            </div>
                        </div>
<!--                        <div class="form-group">-->
<!--                            <label class="col-sm-2 control-label is-required">续展证明：</label>-->
<!--                            <div class="col-sm-10"-->
<!--                                 th:include="/component/attachment :: init(id='continueFile',name='continueFile',-->
<!--                                     sourceId=*{tradeContinueId},sourceModule='KYSB',sourceLabel1='continueFile')">-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">综合意见：</label>
                            <div class="col-sm-10">
                                  <textarea name="opinion" rows="7" cols="150" th:field="*{opinion}"  class="form-control" rangelength="1,500"  required="required" >
                                  </textarea>
                            </div>
                        </div>
                        <div class="form-group" >
                            <label class="col-sm-2 control-label is-required">是否续展：</label><!-- KYSBRECHBLBHFS -->
                            <div class="col-sm-10">
                                <th:block th:include="component/select :: init(id='usestatus',value=*{usestatus}, name='usestatus',isfirst='true' ,businessType='KYSB', dictCode='usestatus')">
                                </th:block>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 流程相关信息 -->
        <th:block th:include="component/wfWorkFlow :: init(workFlow=${tradeContinue.workFlow})" />
        <!-- 流程相关信息end -->
    </form>
    <!--按钮区-->
    <th:block th:include="kysb/tradeContinue/wfDetailButtonEdit :: init(workFlow=${tradeContinue.workFlow})" />
    <!--按钮区end-->
</div>
<th:block th:include="include :: datetimepicker-js" />
<script th:inline="javascript">
    var prefix = ctx + "kysb/tradeContinue";

    $("#form-tradeContinue-edit").validate({
        focusCleanup: true
    });
    function doSubmit(transitionKey) {
        if ($.validate.form()) {
            if ($('#requestReportEdit').length > 0) {
                $("#useSituation").val($('#requestReportEdit').summernote('code'));
            }
            $.modal.confirm("确认提交吗？", function () {
                //$("#workFlow_transitionKey").val(transitionKey); 这个js在提交组件里加了，没有用提交组件的需要这行
                $.operate.saveTabAlert(prefix + "/doSubmit", $('#form-tradeContinue-edit').serialize());
            });
        }
    }
    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/edit", $('#form-tradeContinue-edit').serialize());
        }
    }
    var id = $("[name='tradeRegistId']").val();
    function tradeRegistList(){
        $.modal.openTab("商标列表",  ctx + "kysb/tradeRegist" + "/KYSBTRD02/"+id );
    };
    initEelement();
    function initEelement() {
        $('#requestReportEdit').summernote({
            lang: 'zh-CN',
            minHeight: 300,
            focus: true,
            readonly:true
        });
        $("[data-toggle=popover]").popover();

        $("#form-groupPlanApply-add").validate({
            focusCleanup: true
        });

        var otherData = null;
        ToolBox.initToolBox("catalogue",null, otherData);
    }
</script>
</body>
</html>