<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('待办处理')" />
<!-- 续展结果录入 -->
<th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="wrapper wrapper-content">
		<form class="form-horizontal m" id="form-tradeContinue-edit" th:object="${tradeContinue}">
			<div class="panel-group" id="accordion1" role="tablist" aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#sbxx" aria-expanded="false" class="collapsed">商标信息
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right"><i class="fa fa-chevron-down"
															aria-hidden="true"></i></span>
							</a>
						</h4>
					</div>
					<div id="sbxx" class="panel-collapse collapse in"
						 aria-expanded="false">
						<div class="form-group">
							<label class="col-sm-2 control-label ">商标列表：</label>
							<div class="col-sm-2" style="color: #1c6ac7">
								<input type="button" class="form-control" onclick="tradeRegistList()" value="点击查看">
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel panel-default scrollspy-item" id="row1">
				<div class="panel-heading" role="tab" id="headingTwo">
					<h4 class="panel-title" toolbox-title="使用情况概述">
						<a role="button" data-toggle="collapse" data-parent="#collapseTwo" href="#collapseTwo"
						   aria-expanded="false" aria-controls="collapseTwo" >
							使用情况概述<small>必填</small>
						</a>
						<span class="pull-right">
                                 </span>
						<div class="ibox-tools">
						</div>
					</h4>
				</div>
				<div id="collapseTwo" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingTwo">
					<div class="panel-body">
						<div class="click2edit  wrapper"  th:utext='*{useSituation}'  id="requestReportEdit">
						</div>
					</div>
				</div>
			</div>
			<input id="useSituation" th:value="*{useSituation}" name="useSituation" type="text">

			<div class="panel-group" id="accordion" role="tablist"
				 aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#jbxx" aria-expanded="false" class="collapsed">续展信息
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right"><i class="fa fa-chevron-down"
															aria-hidden="true"></i></span>
							</a>
						</h4>
					</div>
					<div id="jbxx" class="panel-collapse collapse in"
						 aria-expanded="false">
						<div class="panel-body">
							<input name="tradeContinueId" th:value="${tradeContinue.tradeContinueId}" type="hidden">
							<div class="form-group" hidden>
								<label class="col-sm-4 control-label">商标注册主键：</label>
								<div class="col-sm-8">
									<input name="tradeRegistId" th:value="${tradeContinue.tradeRegistId}" class="form-control" type="text">
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">商标使用状态：</label>
								<div class="col-sm-10">
									<th:block  th:include="component/select :: init(id='confirmUse',see='true', name='confirmUse',isfirst='true' ,businessType='KYSB', dictCode='useSituation',value=*{confirmUse}) ">
									</th:block>
									<input type="hidden" th:field="*{confirmUse}">

								</div>
							</div>
<!--							<div class="form-group">-->
<!--								<label class="col-sm-2 control-label is-required">使用情况概述：</label>-->
<!--&lt;!&ndash;								<div class="col-sm-10">&ndash;&gt;-->
<!--&lt;!&ndash;									<input name="useSituation" th:field="*{useSituation}" class="form-control" type="text">&ndash;&gt;-->
<!--&lt;!&ndash;								</div>&ndash;&gt;-->
<!--								<div class="col-sm-10">-->
<!--                                  <textarea name="useSituation" rows="7" cols="150" th:field="*{useSituation}"  class="form-control"-->
<!--											rangelength="1,500"  required="required" readonly></textarea>-->
<!--								</div>-->
<!--							</div>-->
							<div class="form-group">
								<label class="col-sm-2 control-label ">相关附件：</label>
								<div class="col-sm-10"
									 th:include="/component/attachment :: init(id='continueXgFile',name='continueXgFile',
                                     sourceId=*{tradeContinueId},sourceModule='KYSB',sourceLabel1='continueXgFile')">
								</div>
							</div>
<!--							<div class="form-group">-->
<!--								<label class="col-sm-2 control-label is-required">续展证明：</label>-->
<!--								<div class="col-sm-10"-->
<!--									 th:include="/component/attachment :: init(id='continueFile',see='true',name='continueFile',-->
<!--                                     sourceId=*{tradeContinueId},sourceModule='KYSB',sourceLabel1='continueFile')">-->
<!--								</div>-->
<!--							</div>-->
<!--							<div th:if='${tradeContinue.workFlow.currentActivity eq "Manual2"}'>-->
<!--								<div class="form-group">-->
<!--									<label class="col	-sm-2 control-label is-required">综合意见：</label>-->
<!--									<div class="col-sm-10">-->
<!--									  <textarea name="opinion" rows="7" cols="150" th:field="*{opinion}"  class="form-control" rangelength="1,500"  required="required" >-->
<!--									  </textarea>-->
<!--									</div>-->
<!--								</div>-->
<!--								<div class="form-group" >-->
<!--									<label class="col-sm-2 control-label is-required">是否续展：</label>&lt;!&ndash; KYSBRECHBLBHFS &ndash;&gt;-->
<!--									<div class="col-sm-10">-->
<!--										<th:block th:include="component/select :: init(id='usestatus',value=*{usestatus}, name='usestatus',isfirst='true' ,businessType='KYSB', dictCode='usestatus')">-->
<!--										</th:block>-->
<!--									</div>-->
<!--								</div>-->
<!--							</div>-->
<!--							<div th:if='${tradeContinue.workFlow.currentActivity ne "Manual2" and tradeContinue.workFlow.currentActivity ne "Manual1"}'>-->
								<div class="form-group">
									<label class="col-sm-2 control-label is-required">综合意见：</label>
									<div class="col-sm-10">
									  <textarea name="opinion" rows="7" cols="150" th:field="*{opinion}"  class="form-control" rangelength="1,500"  required="required" readonly>
									  </textarea>
									</div>
								</div>
								<div class="form-group" >
									<label class="col-sm-2 control-label is-required">是否续展：</label><!-- KYSBRECHBLBHFS -->
									<div class="col-sm-10">
										<th:block th:include="component/select :: init(id='usestatus',see='true' ,value=*{usestatus}, name='usestatus',isfirst='true' ,businessType='KYSB', dictCode='usestatus')">
										</th:block>
									</div>
									<input type="hidden" name ="usestatus" th:value="${tradeContinue.usestatus}">
								</div>
<!--							</div>-->
							<div class="form-group" >
								<label class="col-sm-2 control-label">续展材料：</label><!-- KYSBRECHBLBHFS -->
								<div class="col-sm-10"
									 th:include="/component/attachment :: init(id='xzclFile',name='xzclFile',see='true',
										 sourceId=*{tradeContinueId},sourceModule='KYSB',sourceLabel1='xzclFile')">
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">续展证明：</label>
								<div class="col-sm-10"
									 th:include="/component/attachment :: init(id='continueFile',name='continueFile',
									 sourceId=*{tradeContinueId},sourceModule='KYSB',sourceLabel1='continueFile')">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel-group" id="accordion12" role="tablist"
				 aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#ztxx" aria-expanded="false" class="collapsed">状态信息
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right"><i class="fa fa-chevron-down"
															aria-hidden="true"></i></span>
							</a>
						</h4>
					</div>
					<div id="ztxx" class="panel-collapse collapse in"
						 aria-expanded="false">
						<div class="panel-body">
							<div class="form-group">
								<!--                        <label class="col-sm-4 control-label">商标注册主键：</label>-->
								<!--                        <label class="col-sm-4 control-label">主键：</label>-->
								<input name="tradeStateId" th:field="*{tradeState.tradeStateId}" class="form-control" type="hidden">

							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label ">商标中英文名称：</label>
								<div class="col-sm-10">
									<input class="form-control"  th:field="*{tradeState.tradeRegist.trademarkName}" type="text" readonly>
								</div>
							</div>
							<div class="form-group">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">注册号：</label>
										<div class="col-sm-8">
											<input name="regigterNo" class="form-control"  th:field="*{tradeState.regigterNo}" type="text" readonly>

										</div>
									</div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">续展起始日期：</label>
										<div class="col-sm-8">
											<input type="hidden" name="initRegisterDate" th:field="*{tradeState.initRegisterDate}">
											<!--                                        <div th:include="/component/date :: init(id='endDate' ,name='endDate', strValue=*{endDate})"></div>-->
											<input name="registerDate" class="form-control"  th:field="*{tradeState.registerDate}" type="text" readonly>

										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">法律状态：</label>
										<div class="col-sm-8">
											<th:block th:include="component/select :: init(id='lawStatus', name='tradeState.lawStatus',isfirst='true' ,businessType='KYSB', dictCode='lawStatus',value=*{tradeState.lawStatus})">

											</th:block>

										</div>
									</div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">终止日期：</label>
										<div class="col-sm-8">
											<!--                                        <div th:include="/component/date :: init(id='endDate' ,name='endDate', strValue=*{endDate})"></div>-->
											<input name="endDate" class="form-control"  th:field="*{tradeState.endDate}" type="text" readonly>
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label ">填写人：</label>
										<div class="col-sm-8">
											<input name="applyPersonName" class="form-control"  th:field="*{tradeState.fillinPerson}" type="text" readonly>
										</div>
									</div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label">填写日期：</label>
										<div class="col-sm-8">
											<input name="applyPersonName" class="form-control" th:field="*{tradeState.fillinDate}"  type="text" readonly>
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">备注：</label>
								<div class="col-sm-10">
									<input name="remarks" rangelength="1,50" th:field="*{tradeState.remarks}" class="form-control" type="text">
								</div>
							</div>
<!--							<div class="form-group">-->
<!--								<label class="col-sm-2 control-label is-required">续展证明：</label>-->
<!--								<div class="col-sm-10">-->
<!--									<div  th:include="/component/attachment :: init(id='continueXzzmFile',name='continueXzzmFile',-->
<!--                                 sourceId=*{tradeState.tradeContinueId},sourceModule='KYSB',sourceLabel1='continueXzzmFile')"></div>-->
<!--								</div>-->
<!--							</div>-->
						</div>
					</div>
				</div>
			</div>
			<div class="panel-group" aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" href="#spyj" aria-expanded="false" class="collapsed">
								审批意见
								<span class="pull-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
								</span>
							</a>
						</h4>
					</div>
					<!-- name必须为 workFlow.comment -->
					<div id="spyj" class="panel-collapse collapse in" aria-expanded="false">
						<div class="panel-body">
	                        <div class="form-group col-sm-12">
	                            <textarea class="form-control" id="workFlow_comment" name="workFlow.comment" style="height: 200px; width: 100%;"
	                                      th:utext="${tradeContinue.workFlow.comment}"></textarea>
	                        </div>
                        </div>
					</div>
				</div>
			</div>
			<th:block th:include="component/wfCommentList3 :: init(businessId=${tradeContinue.workFlow.businessId})" />

			<!--流程相关信息-->
			<th:block th:include="component/wfWorkFlow :: init(workFlow=${tradeContinue.workFlow})" />
			<!--流程相关信息-->
		</form>
		<!--按钮区-->
		<th:block th:include="kysb/tradeContinue/wfDetailButton :: init(workFlow=${tradeContinue.workFlow})" />
		<!--按钮区end-->
	</div>
	<script th:inline="javascript">
		var prefix = ctx + "kysb/tradeContinue";
		function doSubmit(transitionKey) {
			if ($.validate.form()) {
				$.modal.confirm("确认提交吗？", function () {
					//$("#workFlow_transitionKey").val(transitionKey); 这个js在提交组件里加了，没有用提交组件的需要这行
					$.operate.saveTabAlert(prefix + "/doSubmit", $('#form-tradeContinue-edit').serialize());
				});		
			}
		}
		var id = $("[name='tradeRegistId']").val();
		function tradeRegistList(){
			$.modal.openTab("商标列表",  ctx + "kysb/tradeRegist" + "/KYSBTRD02/"+id );
		};
		var html = $("#useSituation").val();
		$("#requestReportEdit").html(html);
	</script>
</body>
</html>