<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('商标注册-异议-案件商标（异议的子）
宝钢对他人的异议子列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
			<input id = "tradeToobjeId" type="hidden" th:value="${tradeToobjeId}">
		</div>

       <div class="btn-group-sm" id="toolbar" role="group" style="color: red">
           请勾选需应诉商标(未勾选商标自动结束)
       </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeObjeCase";
		var tradeToobjeId = $("#tradeToobjeId").val();
        $(function() {
            var options = {
                url: prefix + "/list/"+tradeToobjeId,
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "商标注册-异议-案件商标（异议的子）宝钢对他人的异议子",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'tradeObjeCaseId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'tradeToobjeId',
                    title: '商标异议主键'
                },
                {
                    field: 'orderNum',
                    title: '排序'
                },
                {
                    field: 'objeTradeName',
                    title: '异议商标名称'
                },
                {
                    field: 'objeTradeType',
                    title: '异议商标类别'
                },
                {
                    field: 'objeTradeDate',
                    title: '异议商标的申请日'
                },
                {
                    field: 'objeTradeRegdate',
                    title: '异议商标的注册日'
                },
                {
                    field: 'closeDate',
                    title: '异议截止日期'
                },
               /* {
                    field: 'extra1',
                    title: '扩展字段1'
                },
                {
                    field: 'extra2',
                    title: '扩展字段2'
                },
                {
                    field: 'delStatus',
                    title: '删除状态'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    field: 'deleteUserLabel',
                    title: '删除人'
                },
                {
                    field: 'deleteDate',
                    title: '删除时间'
                },
                {
                    field: 'recordVersion',
                    title: '版本号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.tradeObjeCaseId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.tradeObjeCaseId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }*/]
            };
            $.table.init(options);
        });
		function submitHandler() {
			var row = $('#bootstrap-table').bootstrapTable('getSelections');
			var arr = new Array();
			for(var i=0;i<row.length;i++){
				arr[i] = row[i].tradeObjeCaseId;
			}
			console.log(arr)
			parent.opendTab(arr);
			var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
			parent.layer.close(index);
		}
    </script>
</body>
</html>