<div th:fragment="init">	
	<div id="toolbar-projectMember-user" role="group">
		<button type="button" class="btn btn-success btn-sm btn-circle" onclick="doProjectMemberUser()"><i class="fa fa-plus"></i></button>
<!--		<button type="button" class="btn btn-danger btn-sm btn-circle" onclick="delJlfpColumn()"><i class="fa fa-minus"></i></button>-->

	</div>
	<div class="col-sm-12 select-table table-striped">
    	 <table id="bootstrap-projectMember-user"></table>
	</div> 
	<script th:inline="javascript">
        $(function() {
            var options = {
            	id: "bootstrap-projectMember-user",
            	toolbar: "toolbar-projectMember-user",
                url: ctx + "kysb/zjpsMember/list/"+[[${sourceId}]]+"/"+[[${sourceType}]],
                modalName: "商标专家评审",
				pagination: false,
				showSearch: false,
				showRefresh: false,
				showToggle: false,
				showColumns: false,
				showFooter: true,
                columns: [
                {
                    field: 'rowId',
                    title: '记录ID',
                    visible: false
                },
                {
                    field: 'sourceName',
                    title: '评审专家',
                    align: 'center',
                    width: 200,
                    formatter: function(value, row, index) {
    	            	return row.sourceCode+"-"+row.sourceName;
                    }
                },
                {
                    field: 'sourceComment',
                    title: '评审意见',
                    halign: 'center'
                },
                {
                    field: 'sourceTime',
                    title: '评审时间',
                    align: 'center',
                    width: 200,
                },
                {
                    field: 'sourceStatus',
                    title: '状态',
                    align: 'center',
                    width: 200,
                    formatter: function(value, row, index) {
                    	if('YPS'==row.sourceStatus){
                    		return '已评审';
                    	}else if(!row.sourceStatus||'WPS'==row.sourceStatus){
                    		return '未评审';
                    	}else{
                    		return row.sourceStatus;
                    	}
                    }
                }]
            };
            $.table.init(options);
            
            if(![[${canAdd}]]){//是否隐藏toolbar
            	$('.fixed-table-toolbar').hide();
            }
        });

      	//通用用户评审
		function doProjectMemberUser(){
			try{
				zhuanjiaSave();//自动保存当前表单
			}catch(e){
			}
			var url = ctx + 'mpad/user/selectUserList';
			
	        url += "?selectType=M";//多选
	        
	        url += "&callback=doProjectMemberUserCallback";//回调
	        $.modal.open("选择用户", url, '1000', '500');

		}

		function delJlfpColumn() {
			table.config['bootstrap-projectMember-user'];
			sub.delColumn();
		}
	    function doProjectMemberUserCallback(userCode, userName) {
	    	if(!userCode){
	    		 $.modal.alertWarning("请选择用户");
	    		 return false;
	    	}
	    	var sourceId = [[${sourceId}]];//业务Id			
			if(!sourceId){
				$.modal.alertWarning("缺少来源ID");	 
			}
			var sourceType = [[${sourceType}]];//来源类型			
			if(!sourceType){
				$.modal.alertWarning("缺少来源类型");	 
			}
	    	var doOtherUrl = ctx+"kysb/zjpsMember/doProjectMemberUser/"+sourceId+"/"+sourceType;
			var jsonData = {};
			var businessId = [[${businessId}]];//业务Id			
			if(!businessId){
				$.modal.alertWarning("缺少业务ID");	 
			}
			jsonData["businessId"] = businessId;
			var taskId = [[${taskId}]];//任务ID
			if(!taskId){
				$.modal.alertWarning("缺少任务ID");
			}
			jsonData["taskId"] = taskId;
			var userLabelM = userCode;//流转人
			jsonData["userLabelM"] = userLabelM;
			$.ajax({
	            url: doOtherUrl,
	            data: JSON.stringify(jsonData),
	            dataType: "json",
	            contentType: "application/json",
	            type: "post",
	            success: function (result) {
	                if (result.code == web_status.SUCCESS) {
						$.table.refresh();
	               		$.modal.alertSuccess(result.msg);
	               		//回调

	               		/*
	            		var callback = [[${callback}]];
	            		if(callback){
	            			eval(callback+'("'+userCode+'","'+userName+'")');
	            		}
	            		*/
	                } else if (result.code == web_status.WARNING) {
	                    $.modal.alertWarning(result.msg)
	                } else {
	                    $.modal.alertError(result.msg);
	                }
	            },
	            error: function(XMLHttpRequest, textStatus, errorThrown) {
	            	$.modal.alertError(XMLHttpRequest.status);
	            	$.modal.alertError(XMLHttpRequest.responseText);
	            }
	        });
	    }
    </script>
</div>