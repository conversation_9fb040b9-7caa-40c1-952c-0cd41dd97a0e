<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('商标注册-驳回复审（子）列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">
					<label class="col-sm-1 control-label">代理机构代码:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="agencyCode" placeholder="支持模糊查询"/>
					</div>
					<label class="col-sm-1 control-label">代理机构名称:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="agencyName" placeholder="支持模糊查询"/>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
				    <label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success"  onclick="tradeRegistList()" >
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeRech";
		function tradeRegistList(){
			$.modal.open("商标", ctx + "kysb/tradeRegist"  + "/ENDTRADE",1000,500);
		};
		function opendTab(regId) {
			$.modal.openTab("新建驳回通知书", prefix + "/newTradeRech/KYSBTRA12/"+regId );
		}
		var agencyName = [[${@dict.getDictList('KYSB','dls')}]];
        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/newTradeRech/KYSBTRA12",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "商标注册-驳回复审（子）",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'tradeRechId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'recheckDate',
                    title: '复审截止日期'
                },
                {
                    field: 'agencyName',
                    title: '代理机构名称',
					formatter:function (value,row,index) {
						return $.table.selectDictLabel(agencyName, value);
					}
                },
                // {
                //     field: 'denyAnalyse',
                //     title: '驳回分析'
                // },
                // {
                //     field: 'mainAnalyse',
                //     title: '综合分析'
                // },
				//

                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.tradeRechId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.tradeRechId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>