<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('待办处理专家审批')" />

<th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="wrapper wrapper-content">
		<form class="form-horizontal m" id="form-tradeRech-add" th:object="${tradeRech}">
			<div class="panel-group" id="accordion1" role="tablist" aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#sbxx" aria-expanded="false" class="collapsed">商标信息
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right"><i class="fa fa-chevron-down"
															aria-hidden="true"></i></span>
							</a>
						</h4>
					</div>
					<div id="sbxx" class="panel-collapse collapse in"
						 aria-expanded="false">
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">商标名称：</label>
							<div class="col-sm-10">
								<input name="trademarkName" class="form-control" th:value="${tradeRech.tradeRegist.trademarkName}" type="text" readonly>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">商标图样：</label>
							<div class="col-sm-10">
								<img class="layui-upload-img " th:src="${tradeRech.tradeRegist.imgurl}" width="100" height="50" id="imgFile">
							</div>
						</div>
<!--						<div class="form-group">-->
<!--							<label class="col-sm-2 control-label">注册号：</label>-->
<!--							<div class="col-sm-10">-->
<!--								<input name="regigterNo" class="form-control" th:value="${tradeRech.tradeState.regigterNo}" type="text" readonly>-->
<!--							</div>-->
<!--						</div>-->
						<div class="form-group">
							<label class="col-sm-2 control-label">经办人：</label>
							<div class="col-sm-10">
								<input name="applyPersonName" class="form-control" th:value="${tradeRech.tradeRegist.applyPersonName}" type="text" readonly>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label">商标注册类别：</label>
							<div class="col-sm-10">
								<input type="text" class="form-control" th:value="${tradeRech.tradeRegist.trademarkRegistTypeValue}" readonly>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label ">授权信息：</label>
							<div class="col-sm-2" style="color: #1c6ac7">
								<input type="button" class="form-control" onclick="tradeRegistList()" value="点击查看">
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel-group" id="accordion" role="tablist"  aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#bhfsxx" aria-expanded="false" class="collapsed">注册驳回复审
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right">
                                    <i class="fa fa-chevron-down"  aria-hidden="true"></i>
                                </span>
							</a>
						</h4>
					</div>
					<div id="bhfsxx" class="panel-collapse collapse in"
						 aria-expanded="false">
						<div class="panel-body">
							<div class="form-group" hidden>
								<label class="col-sm-4 control-label is-required">商标注册主键：</label>
								<div class="col-sm-8">
									<input name="tradeRechId" class="form-control" type="hidden" th:value="${tradeRech.tradeRechId}" required>
									<input name="tradeRegistId" id="tradeRegistId" class="form-control" type="text" th:value="${tradeRech.tradeRegistId}" required>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">复审截止日期：</label>
								<div class="col-sm-10">
									<th:block th:include="/component/date::init(see='true',id='recheckDate',strValue=*{recheckDate})"/>
									<input type="hidden" name="recheckDate" th:value="${tradeRech.recheckDate}">
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">代理机构名称：</label>
								<div class="col-sm-10">
									<th:block th:include="component/select :: init(id='agencyName',value=*{agencyName}, name='agencyName' ,businessType='KYSB', dictCode='dls')">
									</th:block>
									<input type="hidden" name="agencyName" th:value="${tradeRech.agencyName}">
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">复审类型：</label>
								<div class="col-sm-10">
									<th:block th:include="component/select :: init(id='rechType', name='rechType',isfirst='true' ,businessType='KYSB', dictCode='rechType',value=*{rechType})">
									</th:block>
									<input type="hidden" name="rechType" th:value="${tradeRech.rechType}">
									<!-- 确认是否复审 isRech  复审材料说明 rechDescription 开始时间 startTime  是否补充证据whetherEvidence
                                                                审查结果 countryDecision   截止日期 endTime --->
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">驳回分析：</label>
								<div class="col-sm-10">
                                    <textarea name="denyAnalyse" rows="7" cols="150" th:field="*{denyAnalyse}"  class="form-control" rangelength="1,500"  required="required" readonly>
                                    </textarea><!--th:field="*{denyAnalyse}"-->
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">驳回通知书：</label>
								<div class="col-sm-10"
									 th:include="/component/attachment :: init(id='tradeRechFile',see='true',name='tradeRechFile',
                                 sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='tradeRechFile')">
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">综合分析：</label>
								<div class="col-sm-10">
									<textarea name="mainAnalyse" rows="7" cols="150"  th:field="*{mainAnalyse}"  class="form-control" rangelength="1,500"  required="required" readonly>
                                    </textarea>
								</div>
							</div>
							<!-- 只有公司初审时可以选择 -->

						</div>
					</div>
					<!--只有在准备证据节点和公司审查节点可改 start-->
					<div th:if='${tradeRech.workFlow.currentActivity eq "Manual4" or  tradeRech.workFlow.currentActivity eq "Manual5"}'><!-- 办理驳回复审申请相关材料录入 -->
						<div class="form-group" >
							<label class="col-sm-2 control-label is-required">证据及相关材料：</label><!-- KYSBRECHBLBHFS -->
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='rechZbzjFile',name='rechZbzjFile',
									 sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='rechZbzjFile')">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">复审材料说明：</label>
							<div class="col-sm-10">
									<textarea name="rechDescription" rows="7" cols="150"  th:field="*{rechDescription}"  class="form-control" rangelength="1,500"  required="required">
                                    </textarea>
							</div>
						</div>
					</div>
					<div th:if='${tradeRech.workFlow.currentActivity ne "Manual4" and  tradeRech.workFlow.currentActivity ne "Manual5" and (tradeRech.status eq "activedBhfs" or tradeRech.status eq "activedZbzj" or tradeRech.status eq "activedclhz") and tradeRech.workFlow.currentActivity ne "Manual4" and tradeRech.workFlow.currentActivity ne "Manual5"}'><!-- 办理驳回复审申请相关材料录入 -->
						<div class="form-group" >
							<label class="col-sm-2 control-label is-required">证据及相关材料：</label><!-- KYSBRECHBLBHFS -->
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='rechZbzjFile',name='rechZbzjFile',
									 sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='rechZbzjFile')">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">复审材料说明：</label>
							<div class="col-sm-10">
									<textarea name="rechDescription" rows="7" cols="150"  th:field="*{rechDescription}"  class="form-control" rangelength="1,500"  required="required" readonly>
                                    </textarea>
							</div>
						</div>
					</div>
					<!--只有在准备证据节点和公司审查节点可改 end-->
					<!--材料汇总签订委托协议节点需上传  复审委托材料  必传  start-->
					<div th:if='${tradeRech.workFlow.currentActivity eq "KYSBRECHCLHZ" }'><!-- 办理驳回复审申请相关材料录入 -->
						<div class="form-group" >
							<label class="col-sm-2 control-label is-required">复审委托材料：</label><!-- KYSBRECHBLBHFS -->
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='rechWtclFile',name='rechWtclFile',
									 sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='rechWtclFile')">
							</div>
						</div>
					</div>
					<div th:if='${tradeRech.workFlow.currentActivity ne "KYSBRECHCLHZ" and (tradeRech.status eq "activedclhz" or tradeRech.status eq "activedBhfs")}'><!-- 办理驳回复审申请相关材料录入 -->
						<div class="form-group" >
							<label class="col-sm-2 control-label is-required">复审委托材料：</label><!-- KYSBRECHBLBHFS -->
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='rechWtclFile',name='rechWtclFile',see='true',
									 sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='rechWtclFile')">
							</div>
						</div>
					</div>
					<!--材料汇总签订委托协议节点需上传  复审委托材料  必传  end-->
					<!--国知局复审材料 start -->
					<div th:if='${tradeRech.workFlow.currentActivity eq "KYSBRECHBLBHFS" }'><!-- 办理驳回复审申请相关材料录入 -->

						<div class="form-group" >
							<label class="col-sm-2 control-label is-required">复审材料：</label><!-- KYSBRECHBLBHFS -->
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='rechBhfsFile',name='rechBhfsFile',
									 sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='rechBhfsFile')">
							</div>
						</div>
					</div>



					<div th:if='${tradeRech.workFlow.currentActivity ne "KYSBRECHBLBHFS" and  tradeRech.status eq "activedBhfs"}'><!-- 办理驳回复审申请相关材料录入 -->

						<div class="form-group" >
							<label class="col-sm-2 control-label is-required">复审材料：</label><!-- KYSBRECHBLBHFS -->
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='rechBhfsFile',name='rechBhfsFile',
									 sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='rechBhfsFile')">
							</div>
						</div>

					</div>

					<!--国知局复审材料 end-->

<!--					<input type="text" th:value="${tradeRech.workFlow.currentActivity}">-->
					<!--受理通知书录入 start 1-->
					<div th:if='${tradeRech.workFlow.currentActivity eq "Manual11"}'>
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">受理通知书等：</label>
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='rechSltzsFile',name='rechSltzsFile',
									 sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='rechSltzsFile')">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">开始日期：</label>
							<div class="col-sm-10">
								<div th:include="/component/date :: init(id='startTime' ,name='startTime', strValue=*{startTime})"></div>

							</div>
						</div>
					</div>

					<div th:if='${tradeRech.workFlow.currentActivity eq "Manual13" or tradeRech.workFlow.currentActivity eq "Manual14" or tradeRech.workFlow.currentActivity eq "KYSBRECHBCZJ" }'>
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">受理通知书等：</label><!-- 补充证据 -->
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='rechSltzsFile',name='rechSltzsFile',
									 sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='rechSltzsFile')">
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">开始日期：</label>
							<div class="col-sm-10">
								<div th:include="/component/date :: init(id='startTime' ,name='startTime', strValue=*{startTime})"></div>
							</div>
						</div>
					</div>
					<!--受理通知书录入 end-->
					<!--补充证据 start-->
					<div th:if='${ tradeRech.workFlow.currentActivity eq "KYSBRECHBCZJ" }'>
						<div class="form-group">
							<label class="col-sm-2 control-label">补充证据：</label><!-- 补充证据 -->
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='rechBczjFile',name='rechBczjFile',
									 sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='rechBczjFile')">
							</div>
						</div>
					</div>
					<div th:if='${ tradeRech.workFlow.currentActivity eq "Manual13" and tradeRech.workFlow.currentActivity eq "Manual14" }'>
						<div class="form-group">
							<label class="col-sm-2 control-label">补充证据：</label><!-- 补充证据 -->
							<div class="col-sm-10"
								 th:include="/component/attachment :: init(id='rechBczjFile',name='rechBczjFile',
									 sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='rechBczjFile')">
							</div>
						</div>
					</div>
					<!--补充证据 end-->
					<!--复审结果录入节点 start 审查结果-->
<!--					<div th:if='${ tradeRech.workFlow.currentActivity eq "Manual14" }'>-->
<!--						<div class="form-group">-->
<!--							<label class="col-sm-2 control-label is-required">截止日期：</label>-->
<!--							<div class="col-sm-10">-->
<!--								<th:block th:include="/component/date::init(see='true',id='endTime',strValue=*{endTime})"/>-->
<!--							</div>-->
<!--						</div>-->
<!--						<div class="form-group">-->
<!--							<label class="col-sm-2 control-label">复审结果相关附件：</label>&lt;!&ndash; 补充证据 &ndash;&gt;-->
<!--							<div class="col-sm-10"-->
<!--								 th:include="/component/attachment :: init(id='rechFsjgFile',name='rechFsjgFile',-->
<!--									 sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='rechFsjgFile')">-->
<!--							</div>-->
<!--						</div>-->
<!--						<div class="form-group">-->
<!--							<label class="col-sm-2 control-label is-required">审查结果：</label>-->
<!--							<div class="col-sm-10">-->
<!--								<th:block th:include="component/select :: init(id='countryDecision', name='countryDecision',isfirst='true' ,businessType='KYSB', dictCode='countryDecision',value=*{countryDecision})">-->
<!--								</th:block>-->
<!--								&lt;!&ndash; 确认是否复审 isRech  复审材料说明 rechDescription 开始时间 startTime  是否补充证据whetherEvidence-->
<!--															审查结果 countryDecision   截止日期 endTime -&ndash;&gt;-->
<!--							</div>-->
<!--						</div>-->

<!--						<div class="form-group">-->
<!--							<label class="col-sm-2 control-label ">被驳回的商品/服务项目：</label>-->
<!--							<div class="col-sm-10"-->
<!--								 th:include="/component/selectServce::init(isrequired=true,orgCodeId='rechService',orgNameId='rechServiceName',level='0',selectType='M',value=${tradeRech.rechService})">-->
<!--							</div>-->
<!--							<input type="text" th:value="${tradeRech.rechService}">-->
<!--						</div>-->
<!--					</div>-->
					<!--复审结果录入节点 end-->
				</div>
			</div>

			<div class="panel-group" aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" href="#spyj" aria-expanded="false" class="collapsed">
								审批意见
								<span class="pull-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
								</span>
							</a>
						</h4>
					</div>
					<!-- name必须为 workFlow.comment -->
					<div id="spyj" class="panel-collapse collapse in" aria-expanded="false">
						<div class="panel-body">
	                        <div class="form-group col-sm-12">
	                            <textarea class="form-control" id="workFlow_comment" name="workFlow.comment" style="height: 200px; width: 100%;"
	                                      th:utext="${tradeRech.workFlow.comment}"></textarea>
	                        </div>
                        </div>
					</div>
				</div>
			</div>
			<th:block th:include="component/wfCommentList3 :: init(businessId=${tradeRech.workFlow.businessId})" />

			<!--流程相关信息-->
			<th:block th:include="component/wfWorkFlow :: init(workFlow=${tradeRech.workFlow})" />
			<!--流程相关信息-->
		</form>
		<!--按钮区-->
		<th:block th:include="kysb/tradeRech/wfDetailButton :: init(workFlow=${tradeRech.workFlow})" />
		<!--按钮区end-->
	</div>
	<script th:inline="javascript">
		$("#form-tradeRech-add input").attr("readOnly",true);
		$("#form-tradeRech-add select").attr("disabled",true);
		$("#isRecheck").attr("disabled",false);
		$("#countryDecision").attr("disabled",false);
		$("#rechService").attr("disabled",false);
		$(document).ready(function(){
			var jsIsChooseZj = [[${tradeRech.jsIsChooseZj}]];//是否专家评审
			if(!jsIsChooseZj || jsIsChooseZj == 'noNeed'){
				$('.ifJsIsChooseZj').addClass('hide');
			}
		});
		function ifJsIsChooseZj(item){
			if (item == 'need') {
				$('.ifJsIsChooseZj').removeClass('hide');
			} else {
				$('.ifJsIsChooseZj').addClass('hide');
			}
		}
		var prefix = ctx + "kysb/tradeRech";
		function doSubmit(transitionKey) {
			if ($.validate.form()) {
				$.modal.confirm("确认提交吗？", function () {
					//$("#workFlow_transitionKey").val(transitionKey); 这个js在提交组件里加了，没有用提交组件的需要这行
					$.operate.saveTabAlert(prefix + "/doSubmit", $('#form-tradeRech-add').serialize());
				});		
			}
		}
		var id = $("#tradeRegistId").val();
		function tradeRegistList(){
			$.modal.open("商标", ctx + "kysb/tradeRegist"  + "/selectDetail/KYSBTRD01/"+id,1000,500);
		};
	</script>
</body>
</html>