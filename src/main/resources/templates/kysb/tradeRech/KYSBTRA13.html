<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改商标注册-驳回复审（子）')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-tradeRech-edit" th:object="${tradeRech}">
            <div class="panel-group" id="accordion1" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version"
                               href="#sbxx" aria-expanded="false" class="collapsed">商标信息
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <span class="pull-right"><i class="fa fa-chevron-down"
                                                            aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="sbxx" class="panel-collapse collapse in"
                         aria-expanded="false">
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">商标名称：</label>
                            <div class="col-sm-10">
                                <input name="trademarkName" class="form-control" th:value="${tradeRech.tradeRegist.trademarkName}" type="text" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">商标图样：</label>
                            <div class="col-sm-10">
                                <img class="layui-upload-img " th:src="${tradeRech.tradeRegist.imgurl}" width="100" height="50" id="imgFile">
                            </div>
                        </div>
<!--                        <div class="form-group">-->
<!--                            <label class="col-sm-2 control-label">注册号：</label>-->
<!--                            <div class="col-sm-10">-->
<!--                                <input name="regigterNo" class="form-control" th:value="${tradeRech.tradeState.regigterNo}" type="text" readonly>-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label">经办人：</label>
                            <div class="col-sm-10">
                                <input name="applyPersonName" class="form-control" th:value="${tradeRech.tradeRegist.applyPersonName}" type="text" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">商标注册类别：</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" th:value="${tradeRech.tradeRegist.trademarkRegistTypeValue}" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">授权信息：</label>
                            <div class="col-sm-2" style="color: #1c6ac7">
                                <input type="button" class="form-control" onclick="tradeRegistList()" value="点击查看">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion" role="tablist"  aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version"
                               href="#bhfsxx" aria-expanded="false" class="collapsed">注册驳回复审
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <span class="pull-right">
                                    <i class="fa fa-chevron-down"  aria-hidden="true"></i>
                                </span>
                            </a>
                        </h4>
                    </div>
                    <div id="bhfsxx" class="panel-collapse collapse in"
                         aria-expanded="false">
                        <div class="panel-body">
                            <div class="form-group" hidden>
                                <label class="col-sm-4 control-label is-required">商标注册主键：</label>
                                <div class="col-sm-8">
                                    <input name="tradeRechId" class="form-control" type="hidden" th:value="${tradeRech.tradeRechId}" required>
                                    <input name="tradeRegistId" id= "tradeRegistId" class="form-control" type="text" th:value="${tradeRech.tradeRegistId}" required>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">复审截止日期：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="/component/date::init(name='recheckDate',id='recheckDate',strValue=*{recheckDate})"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">代理机构名称：</label>
                                <div class="col-sm-4">
                                    <th:block th:include="component/select :: init(id='agencyName',value=*{agencyName}, name='agencyName' ,businessType='KYSB', dictCode='dls')">
                                    </th:block>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">复审类型：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="component/select :: init(id='rechType', name='rechType',isfirst='true' ,businessType='KYSB', dictCode='rechType',value=*{rechType})">
                                    </th:block>
                                    <!-- 确认是否复审 isRech  复审材料说明 rechDescription 开始时间 startTime  是否补充证据whetherEvidence
                                                                审查结果 countryDecision   截止日期 endTime --->
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">驳回分析：</label>
                                <div class="col-sm-10">
                                    <textarea name="denyAnalyse" rows="7" cols="150" th:field="*{denyAnalyse}"  class="form-control" rangelength="1,500"  required="required">
                                    </textarea><!--th:field="*{denyAnalyse}"-->
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">驳回通知书：</label>
                                <div class="col-sm-10"
                                     th:include="/component/attachment :: init(id='tradeRechFile',name='tradeRechFile',
                                     sourceId=*{tradeRechId},sourceModule='KYSB',sourceLabel1='tradeRechFile')">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">综合分析：</label>
                                <div class="col-sm-10">
                                    <textarea name="mainAnalyse" rows="7" cols="150"  th:field="*{mainAnalyse}"  class="form-control" rangelength="1,500"  required="required">
                                    </textarea>
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
            <!-- 流程意见 name必须为 workFlow.comment -->

            <!-- 流程意见end -->
            <!-- 流程相关信息 -->
            <th:block th:include="component/wfWorkFlow :: init(workFlow=${tradeRech.workFlow})" />
            <!-- 流程相关信息end -->
        </form>
        <!--按钮区-->
        <th:block th:include="kysb/tradeRech/wfDetailButton :: init(workFlow=${tradeRech.workFlow})" />
        <!--按钮区end-->
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeRech";

        $("#form-tradeRech-edit").validate({
            focusCleanup: true
        });
        function doSubmit(transitionKey) {
            if ($.validate.form()) {
                $.modal.confirm("确认提交吗？", function () {
                    //$("#workFlow_transitionKey").val(transitionKey); 这个js在提交组件里加了，没有用提交组件的需要这行
                    $.operate.saveTabAlert(prefix + "/doSubmit", $('#form-tradeRech-edit').serialize());
                });
            }
        }
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-tradeRech-edit').serialize());
            }
        }
        var id = $("#tradeRegistId").val();
        function tradeRegistList(){
            $.modal.open("商标", ctx + "kysb/tradeRegist"  + "/selectDetail/KYSBTRD01/"+id,1000,500);
        };
    </script>
</body>
</html>