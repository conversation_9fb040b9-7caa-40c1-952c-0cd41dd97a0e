<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns="http://www.w3.org/1999/html">
<head>
    <th:block th:include="include :: header('选择国家')"/>
    <th:block th:include="include :: layout-latest-css"/>
    <th:block th:include="include :: ztree-css"/>
    <style>
        #nameDiv{
            display: inline-block;
            border-radius: 6px;
            margin-top: 10px;
            padding-top: 5px;
            padding-bottom: 13px;
            background: #ececec;
            box-shadow: 1px 1px 3px rgb(0 0 0 / 20%);
        }
        .nameSpan{
            border-radius: 6px;
            margin-top: 3px;
            padding-top: 3px;
            padding-bottom: 5px;
            background: #f5f5f5;
            box-shadow: 1px 1px 3px rgb(0 0 0 / 20%);
            cursor:pointer;
        }
    </style>
</head>
<body class="gray-bg">
<div class="ui-layout-center">
    <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="role-form">
                    <div class="select-list">
                        <ul>
                            <li style="display: none"  onclick="openText('userCode')">当前选中编号：<input type="text"  style="border: none;" id="userCode" disabled th:value="${userCode}" />
                            </li >
                            <li style="display: none"  onclick="openText('userName')">当前选中名称：<input type="text"  style="border: none;" id="userName" disabled th:value="${userName}" />
                            <li>国家/地区名称搜索：<input type="text" name="cnName" style="width:113px;"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm"
                                   onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm"
                                   onclick="resetData()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                            <li>
                                <a class="btn btn-success btn-rounded btn-sm" onclick="doSave()"><i class="fa fa-plus"></i>添加常用</a>
                            </li>
                        </ul>
                        <div id="nameDiv" style="width: 100%;">
                            <ul>

                            </ul>
                        </div>
                    </div>
                    <div class="select-list">
                        <input type="hidden" name="operator" th:value="${operator}">
                        <input type='hidden' class='form-control' id="type" name="type" value="1">
                        <div class="btn-group-sm" id="toolbar" role="group">
                            <button id="grcy" type="button" class="btn btn-danger"
                                    onclick="doBut('1')" disabled>
                                <i></i>常用国家/地区
                            </button>
                            <button id="all" type="button" class="btn btn-danger"
                                    onclick="doBut('3')">
                                <i></i>全部国家/地区
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>
</div>
<th:block th:include="include :: baseJs"/>
<th:block th:include="include :: layout-latest-js"/>
<th:block th:include="include :: ztree-js"/>
<script th:inline="javascript">

    function doSave() {
        var userCode = $("#userCode").val();
        var userName = $("#userName").val();
        $.operate.saveModal(ctx + "kysb/oftenCountry/save", "userCode=" + userCode + "&userName=" + userName);
    }

    function doBut(type) {
        if ('1' === type) {
            //收藏
            $("#grcy").attr("disabled","true");
            $("#gstj").removeAttr("disabled");
            $("#all").removeAttr("disabled");
        } else if('2' === type) {
            //推荐
            $("#gstj").attr("disabled","true");
            $("#grcy").removeAttr("disabled");
            $("#all").removeAttr("disabled");
        } else if('3' === type) {
            //全部
            $("#all").attr("disabled","true");
            $("#grcy").removeAttr("disabled");
            $("#gstj").removeAttr("disabled");
        }
        addElement(type);
        $.table.search();
    }

    function addElement(value) {
        $("#type").val(value);
    }

    $(function() {
        var panehHidden = false;
        if ($(this).width() < 769) {
            panehHidden = true;
        }
        $('body').layout({ initClosed: panehHidden, west__size: 300 });
        // 回到顶部绑定
        if ($.fn.toTop !== undefined) {
            var opt = {
                win:$('.ui-layout-center'),
                doc:$('.ui-layout-center')
            };
            $('#scroll-up').toTop(opt);
        }
        queryUserList();
    });

    function queryUserList() {
        var singleSelect = false;
        if ([[${selectType=='S'}]]) {
            singleSelect = true;
        }
        var options = {
            url: ctx + "kysb/tradeRegist/cyList",
            queryParams: queryParams,
            uniqueId:"userCode",
            modalName: "国家",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            onCheck:onCheck,
            onUncheck:onUncheck,
            onCheckAll: onCheckAll,
            onUncheckAll: onUncheckAll,
            singleSelect: singleSelect,
            showColumns: false,
            clickToSelect: true,
            rememberSelected: true,
            columns: [{
                field: 'state',
                checkbox: true,
                formatter: function(value, row, index) {
                    if($("#userCode").val().indexOf(row.userCode)>-1){
                        return true;
                    }
                    return false;
                }
            },
            {
                field: 'userCode',
                title: '国家/地区编码',
            },
            {
                field: 'userName',
                title: '国家/地区名称',
            }]
        };
        $.table.init(options);
    }

    function queryParams(params) {
        var search = $.table.queryParams(params);
        return search;
    }

    /* 添加用户-选择用户-提交  */
    function submitHandler() {
        var loginName = $("#userCode").val();
        var userName = $("#userName").val();
        parent.choiceUserCallback(loginName,userName);
        var callback= [[${callback}]]
        if(callback!=null && callback!=''){
            parent.eval(callback+'("'+loginName+'","'+userName+'")');
        }
        var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        parent.layer.close(index);
    }

    function onCheck(row, $element){
        var arrUserCode=[];
        var arrUserName=[];

        var userCodeS=$("#userCode").val();
        var userNameS=$("#userName").val();
        if(userCodeS!=null && userCodeS!=''){
            arrUserCode=userCodeS.split(",");
            arrUserName=userNameS.split(",");
        }
        if(!arrUserCode.includes(row.userCode)){
            if([[${selectType=='S'}]]){
                arrUserCode=[row.userCode];
                arrUserName=[row.userName];
                $("#"+userCodeS).remove();
                addNameSpan(row.userCode,row.userName);
            }else{
                arrUserCode.push(row.userCode);
                arrUserName.push(row.userName);
                addNameSpan(row.userCode,row.userName);
            }
        }
        $("#userCode").val(arrUserCode.join(","));
        $("#userName").val(arrUserName.join(","));



    }
    function onUncheck(row, $element){
        var userCodeS=$("#userCode").val();
        var userNameS=$("#userName").val();
        var arrUserCode=userCodeS.split(",");
        var arrUserName=userNameS.split(",");

        arrUserCode.splice(arrUserCode.indexOf(row.userCode),1);
        arrUserName.splice(arrUserName.indexOf(row.userName),1);
        $("#userCode").val(arrUserCode.join(","));
        $("#userName").val(arrUserName.join(","));

        $("#"+row.userCode).remove();
    }

    function openText(id){
        var length = $("#"+id).val().length;
        if (length > 15) {
            layer.alert($("#"+id).val(), {
                title : "信息内容",
                shadeClose : true,
                btn : [ '确认' ],
                btnclass : [ 'btn btn-primary' ],
            });
        }
    }
    function onCheckAll(rowsAfter,rowsBefore){
        for (var i=0;i<rowsAfter.length;i++){
            onCheck(rowsAfter[i]);
        }

    }
    function onUncheckAll(e,rowsAfter,rowsBefore){
        for (var i=0;i<rowsAfter.length;i++){
            onUncheck(rowsAfter[i]);
        }
    }

    function resetData(){
        $.form.reset();
    }
    function initNameSpan(){
        var userCode=$("#userCode").val();;

        if(userCode!=null && userCode!=''){
            var arr=userCode.split(",");
            var arr2=$("#userName").val().split(",");
            $("#nameDiv").find("ul").empty();
            var html="";
            for(var i=0;i<arr.length;i++){
                html+="<li id='"+arr[i]+"' ><span class='nameSpan' onclick='deleteNameSpan(&quot;" + arr[i] +"&quot;,&quot;" + arr2[i] +"&quot;)'>"+ arr[i] +"-"+arr2[i] +"<i class='fa fa-close'></i></span></li>";
            }
            $("#nameDiv").find("ul").append(html);
        }
    }
    initNameSpan();
    function addNameSpan(userCode,userName){
        var html="<li id='"+userCode+"'><span class='nameSpan'  onclick='deleteNameSpan(&quot;" + userCode +"&quot;,&quot;" + userName +"&quot;)'>"+ userCode +"-"+userName +"<i class='fa fa-close'></i></span></li>";

        $("#nameDiv").find("ul").append(html);
    }
    function deleteNameSpan(usreCode,userName){
        var json={};
        json.userCode=usreCode;
        json.userName=userName;
        onUncheck(json);
        $("#bootstrap-table").bootstrapTable('updateByUniqueId',{
            id:usreCode,
            row: {
                state: false
            }
        });
    }
</script>
</body>
</html>