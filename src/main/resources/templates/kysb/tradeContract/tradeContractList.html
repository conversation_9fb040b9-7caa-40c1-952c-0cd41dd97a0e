<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('【请填写功能名称】列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">
					<input type="hidden" id = "registId" th:value="${registId}" />
					<label class="col-sm-1 control-label">贸易合同号:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="tradeContractNo" placeholder="不支持模糊查询"/>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
				    <label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeContract";
		var registId = $("#registId").val();
        $(function() {
            var options = {
                url: prefix + "/pageTradeSelect/"+registId,
                modalName: "贸易信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'tradeContractId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'tradeContractNo',
                    title: '贸易合同号'
                },
                {
                    field: 'tradePartnes',
                    title: '贸易对象'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
				{
					field: 'createDate',
					title: '创建时间'
				},
                // {
                //     title: '操作',
                //     align: 'center',
                //     formatter: function(value, row, index) {
                //         var actions = [];
                //         actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.tradeContractId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                //         actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.tradeContractId + '\')"><i class="fa fa-remove"></i>删除</a>');
                //         return actions.join('');
                //     }
                // }
                ]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>