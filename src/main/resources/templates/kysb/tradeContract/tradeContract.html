<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('【请填写功能名称】信息')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-tradeContract-edit" th:object="${tradeContract}">
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <input name="tradeContractId" th:field="*{tradeContractId}" type="hidden">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">商标主键：</label>
                                <div class="col-sm-8">
                                    <input name="tradeRegistId" th:field="*{tradeRegistId}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">许可主键：</label>
                                <div class="col-sm-8">
                                    <input name="tradePermId" th:field="*{tradePermId}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">贸易合同号：</label>
                                <div class="col-sm-8">
                                    <input name="tradeContractNo" th:field="*{tradeContractNo}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">贸易对象：</label>
                                <div class="col-sm-8">
                                    <input name="tradePartnes" th:field="*{tradePartnes}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group" th:include="include :: initRadio(id='status', name='status',businessType=null, dictCode=null ,value=${tradeContract.status} ,labelName='状态')">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
		<!--按钮区end-->
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeContract";

        $("#form-tradeContract-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/save", $('#form-tradeContract-edit').serialize());
            }
        }

    </script>
</body>
</html>