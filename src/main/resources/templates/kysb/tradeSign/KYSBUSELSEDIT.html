<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改商标许可组织签约登记')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-tradeSign-edit" th:object="${tradeSign}">
        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body" >
                        <input name="tradeSignId" th:field="*{tradeSignId}" type="hidden">
                        <div class="form-group" hidden>
                            <label class="col-sm-4 control-label is-required">商标主键：</label>
                            <div class="col-sm-8">
                                <input name="tradePermId" th:field="*{tradeRegistId}" class="form-control" type="text" required>
                            </div>
                        </div>
<!--                        <div class="form-group">-->
<!--                            <label class="col-sm-2 control-label">商标名称：</label>-->
<!--                            <div class="col-sm-10">-->
<!--                                <input name="tradeName" th:value="${tradeSign.tradeName}" class="form-control" type="text" readonly>-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="form-group">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">许可人：</label>
                                    <div class="col-sm-8">
                                        <select class="form-control" id="permitPerson" name="permitPerson">
                                            <option value="">请选择</option>
                                            <option  th:each="type:${tradeSign.legalreprInfos}" th:text="${type.legalRepr}" th:selected="${type.legalRepr eq tradeSign.permitPerson}" th:value="${type.legalRepr}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>

                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">联系人：</label>
                                    <div class="col-sm-8">
                                        <input name="conPerson" th:field="*{conPerson}" class="form-control" type="text">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">联系电话：</label>
                                    <div class="col-sm-8">
                                        <input name="conTel" th:field="*{conTel}" isPhone="true" class="form-control" type="text">
                                    </div>
                                </div>
                            </div>

                        </div>
                        <div class="form-group">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">被许可人：</label>
                                    <div class="col-sm-8">
                                        <select class="form-control" name="licensedPerson">
                                            <option value="">请选择</option>
                                            <option  th:each="type:${tradeSign.legalreprInfos}" th:text="${type.legalRepr}" th:selected="${type.legalRepr eq tradeSign.licensedPerson}" th:value="${type.legalRepr}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">联系人：</label>
                                    <div class="col-sm-8">
                                        <input name="licPerson" th:field="*{licPerson}" class="form-control" type="text">
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">联系电话：</label>
                                    <div class="col-sm-8">
                                        <input name="licTel" th:field="*{licTel}" isPhone="true" class="form-control" type="text">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required" >合同编号：</label>
                                    <div class="col-sm-8">
                                        <input  name="contractNun"  class="form-control" type="text" required>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">是否长期有效：</label>
                                    <div class="col-sm-8">
                                        <th:block th:include="component/radio :: init( name='isCqyx', businessType='KYSB', dictCode='jsIsChooseZj',value=*{isCqyx}, callback=ifJsIsChooseZj,  isrequired=true)" />
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-4">
                                <div class="form-group" id ="ifJsIsChooseZj">
                                    <label class="col-sm-4 control-label is-required">商标许可有效期：</label>
                                    <div class="col-sm-8">
                                        <div th:include="/component/date :: init(id='tradePermVdate', name='tradePermVdate', strValue=*{tradePermVdate})"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">许可合同：</label>
                            <div class="col-sm-10"
                                 th:include="/component/attachment :: init(id='tradeSignFile',name='tradeSignFile',
                                 sourceId=*{tradeSignId},sourceModule='KYSB',sourceLabel1='tradeSignFile')">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-sm btn-primary"
                onclick="doSubmit()">
            <i class="fa fa-check"></i>保 存
        </button>
        &nbsp;
        <button type="button" class="btn btn-sm btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
    <!--按钮区end-->
</div>
<th:block th:include="include :: datetimepicker-js" />
<script th:inline="javascript">
    var prefix = ctx + "kysb/tradeSign";

    $("#form-tradeSign-edit").validate({
        focusCleanup: true
    });
    $(document).ready(function(){
        var isCqyx = [[${tradeSign.isCqyx}]];//是否专家评审
        if(!isCqyx || isCqyx == 'need'){
            $('#ifJsIsChooseZj').addClass('hide');
        }
    });
    function ifJsIsChooseZj(item){
        if (item == 'need') {
            $('#ifJsIsChooseZj').addClass('hide');
        } else {
            $('#ifJsIsChooseZj').removeClass('hide');
        }
    }
    function doSubmit(transitionKey) {
        alert($.validate.form())
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function () {
                //$("#workFlow_transitionKey").val(transitionKey); 这个js在提交组件里加了，没有用提交组件的需要这行
                $.operate.saveTabAlert(prefix + "/save", $('#form-tradeSign-edit').serialize());
            });
        }
    }

</script>
</body>
</html>