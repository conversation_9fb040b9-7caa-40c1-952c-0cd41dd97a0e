<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <meta charset="UTF-8">
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: summernote-js"/>
    <th:block th:include="KWZL/kwzl :: selectCountry"/>
</head>
<body>
<!-- 审批意见 -->
<div th:fragment="approveCommet">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h5 class="panel-title">
                <a data-toggle="collapse" data-parent="#version" href="#approveCommet1" aria-expanded="true"
                   class="collapsed">审批意见
                    <span class="pull-right">
                                <i class="fa fa-chevron-down" aria-hidden="true">
                                </i>
                            </span>
                </a>
            </h5>
        </div>
        <!--折叠区域-->
        <div id="approveCommet1" class="panel-collapse collapse in">
            <div class="panel-body">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                        <textarea id="comment" name="comment" class="form-control" style="height:150px; "
                                                  placeholder="请填写审批意见" required></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
<div th:fragment="buttonKWZL">
    <!-- 流程跟踪3 -->
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
    </div>
    <div class="row" style="padding: 15px 0;">
        <div class="toolbar toolbar-bottom" role="toolbar">
            <button type="button" class="btn btn-primary" th:if="${not #strings.isEmpty(processInstanceId)}"
                    th:onclick="workFlowProcess([[${processInstanceId}]])"><i
                    class="fa fa-eye"></i>流程跟踪图
            </button>
            <button type="button" class="btn btn-primary" th:if="${not #strings.isEmpty(ckzjpsId)}"
                    onclick="showZJPS()"><i
                    class="fa fa-check"></i>查看评审信息
            </button>
            <button type="button" class="btn btn-primary" th:if="${not #strings.isEmpty(zjpsId)}"
                    onclick="startZJPS()"><i
                    class="fa fa-check"></i>进入专家评审
            </button>
            <button type="button" class="btn btn-primary" th:if="${not #strings.isEmpty(gjcsId)}"
                    onclick="submitGJJS()"><i
                    class="fa fa-check"></i>进入国际阶段
            </button>
            <button type="button" class="btn btn-primary" th:if="${not #strings.isEmpty(zcId)}"
                    onclick="saveHandler()"><i
                    class="fa fa-hdd-o"></i>暂存
            </button>
            <!-- 流程button -->
            <th:block th:if="${not #strings.isEmpty(wfId)}">
                <th:block th:include="component/wfSubmit:: init(taskId=${taskId},callback=submitHandler)"/>
            </th:block>
            <th:block th:if="${not #strings.isEmpty(wfOneId)}">
                <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>
            </th:block>
            <th:block th:if="${not #strings.isEmpty(retId)}">
                <th:block th:include="component/wfReturn:: init(taskId=${taskId},callback=wfReturnHandler)"/>
            </th:block>
            <th:block th:if="${not #strings.isEmpty(returnId)}">
                <th:block th:include="component/wfReturn:: init(taskId=${taskId},callback=returnHandler)"/>
            </th:block>
            <button type="button" class="btn btn-primary" th:if="${not #strings.isEmpty(bcId)}"
                    onclick="submitAjax()"><i
                    class="fa fa-check"></i>保存
            </button>
            <button type="button" class="btn btn-primary" th:if="${not #strings.isEmpty(tjId)}"
                    onclick="submitAjax()"><i
                    class="fa fa-check"></i>提交
            </button>
            <button type="button" class="btn btn-primary" th:if="${not #strings.isEmpty(qdId)}"
                    onclick="submitAjax()"><i
                    class="fa fa-check"></i>启动
            </button>
            <button type="button" class="btn btn-primary" th:if="${not #strings.isEmpty(qdszscId)}"
                    onclick="submitAjax()"><i
                    class="fa fa-check"></i>启动实质审查
            </button>
            <button type="button" class="btn btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返回
            </button>
        </div>
    </div>
    <!--按钮组--->
    <script th:inline="javascript">
        var submitUrl = ctx + [[${submitUrl}]];
        var doSaveUrl = ctx + [[${doSaveUrl}]];
        var returnUrl = ctx + [[${returnUrl}]];

        var zjpsUrl = ctx + [[${zjpsUrl}]];
        var gjcsUrl = ctx + [[${gjcsUrl}]];

        //富文本数据
        var summData = [[${summData}]];
        var id = [[${id}]];
        var jwsqId = [[${jwsqId}]];
        var bizGuid = [[${bizGuid}]];
        var moduleCode = [[${moduleCode}]];

        /*退回流程*/
        function wfReturnHandler(activityKey) {
            var comment = $("#comment").val();
            if ($.common.isEmpty(comment)) {
                $.modal.alertWarning("请输入审批意见!");
                return;
            }
            $.modal.confirm("确认要退回吗？", function () {
                $.ajax({
                    url: returnUrl,
                    type: "post",
                    dataType: "json",
                    data: $("#" + id).serialize() + (activityKey ? ("&activityKey=" + activityKey) : ""),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                });
            })
        }

        /*退回流程*/
        function returnHandler(activityKey) {
            var comment = $("#comment").val();
            if ($.common.isEmpty(comment)) {
                $.modal.alertWarning("请输入审批意见!");
                return;
            }
            $.modal.confirm("确认要退回吗？", function () {
                $.ajax({
                    url: returnUrl,
                    type: "post",
                    dataType: "json",
                    data: $("#" + id).serialize() + (activityKey ? ("&activityKey=" + activityKey) : ""),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                });
            })
        }


        /*提交流程*/
        function submitHandler(transitionKey) {
            if ($.validate.form()) {
                $.modal.confirm("确认提交吗？", function () {
                    $.ajax({
                        url: submitUrl,
                        type: "post",
                        dataType: "json",
                        data: $("#" + id).serialize() + (transitionKey ? ("&transitionKey=" + transitionKey) : ""),
                        beforeSend: function () {
                            $.modal.loading("正在处理中，请稍后...");
                        },
                        success: function (result) {
                            $.operate.alertSuccessTabCallback(result);
                        }
                    });

                })
            }
        }

        //提交
        function submitAjax() {
            if ($.validate.form()) {
                $.modal.confirm("确认提交吗？", function () {
                    $.ajax({
                        url: submitUrl,
                        type: "post",
                        dataType: "json",
                        data: $("#" + id).serialize(),
                        beforeSend: function () {
                            $.modal.loading("正在处理中，请稍后...");
                        },
                        success: function (result) {
                            if ($.common.isNotEmpty(result.data)) {
                                if ($.common.isNotEmpty(result.data.downId)) {
                                    $("#downId").val(result.data.downId);
                                }
                            }
                            $.operate.alertSuccessTabCallback(result);
                        }
                    });

                })
            }
        }

        /*流程跟踪图*/
        function workFlowProcess(processInstanceId) {
            window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processInstanceId);
        }

        //暂存
        function saveHandler() {
            var config = {
                url: doSaveUrl,
                type: "post",
                dataType: "json",
                data: $("#" + id).serialize(),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    if (0 === result.code) {
                        if ($.common.isNotEmpty(result.data)) {
                            if ($.common.isNotEmpty(result.data.jwsqId)) {
                                $("#jwsqId").val(result.data.jwsqId);
                            }
                            if ($.common.isNotEmpty(result.data.downId)) {
                                $("#downId").val(result.data.downId);
                            }
                        }
                        $.modal.alertSuccess('暂存成功');
                    } else {
                        $.modal.alertError(result.msg)
                    }
                    $.modal.closeLoading();
                }
            };
            $.ajax(config)
        }

        //进入国际阶段
        function submitGJJS() {
            $.modal.confirm("确认提交吗？", function () {
                if ($.validate.form()) {
                    $.ajax({
                        url: gjcsUrl,
                        type: "post",
                        dataType: "json",
                        data: $("#" + id).serialize(),
                        beforeSend: function () {
                            $.modal.loading("正在处理中，请稍后...");
                        },
                        success: function (result) {
                            $.operate.alertSuccessTabCallback(result);
                        }
                    });
                }
            })
        }

        /* 启动专家评审 */
        function startZJPS() {
            $.modal.open("启动专家评审", zjpsUrl + "?bizGuid=" + bizGuid + "&moduleCode="+moduleCode+"&approveKind=MPPS_members_review", '1000', '500');
        }

        function showZJPS() {
            $.modal.openTab("查看评审信息", ctxGGMK + "mpps/reviewInfo/showPsByBizGuid?bizGuid=" + bizGuid, false);
        }

    </script>
</div>
<!--选择国家
userCodeInputId:工号input id 必填
userNameInputId:名称input id 必填
selectType 单选S 多选M 必填
-->
<div th:fragment="selectCountry">
    <script type="text/javascript" th:inline="javascript">
        var ctx = [[@{/}]];
        var userId = "userId";
        var userNameId = "userName";

        function choiceCountry(userCode, userName, selectType, callback) {

            userId = userCode;
            userNameId = userName;
            var url = ctx + 'kysb/tradeRegist/selectCountry'
            if (selectType === undefined || selectType == null || selectType == '') {
                selectType = "S";
            }
            url += "?selectType=" + selectType;
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            var userCode = $("#" + userCode).val();
            if (!(userCode === undefined) && userCode != null) {
                url += "&userCode=" + userCode;
            }
            var userName = $("#" + userName).val();
            if (!(userName === undefined) && userName != null) {
                url += "&userName=" + userName;
            }
            url += "&userId=" + $("#" + userId).val();
            $.modal.open("选择国家", url, '1000', '500');
        }

        function choiceUserCallback(userCode, userName) {
            $("#" + userId).val(userCode);
            $("#" + userNameId).val(userName);
        }
    </script>
</div>
<div th:fragment="choiceCountry">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	userCodeId=${userCodeId==null?'userCodeId':userCodeId},
               	 	userNameId=${userNameId==null?'userNameId':userNameId},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择国家：':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
                    selectType=${selectType==null?'S':selectType}
               	 	">

        <div th:class="${divClass}" th:if="${see!=null && !see}">
            <div class="input-group">
                <input th:id="${userCodeId}" th:name="${userCodeId}" th:value="${value}" type="hidden"/> <input
                    class="form-control" th:id="${userNameId}" th:name="${userNameId}"
                    th:onclick="choiceCountry([[${userCodeId}]],[[${userNameId}]],[[${selectType}]],[[${callback}]])"
                    th:required="${isrequired!=null && isrequired}"
                    th:value="${T(com.baosight.bscdkj.ky.sb.controller.ControllerKYSBTradeRegist).getCountryName(value)}" type="text">
                <span class="input-group-addon detailOrgOrUser"><i
                        class="fa fa-search "></i></span>
            </div>
        </div>
        <div th:class="${divClass!=null?divClass+' form-control-static':'form-control-static'}" th:unless="${!see}"
             th:utext="${T(com.baosight.bscdkj.ky.sb.controller.ControllerKYSBTradeRegist).getCountryName(value)}"></div>

    </th:block>
</div>
<!--选择发明人
userCodeInputId:工号input id 必填
userNameInputId:名称input id 必填
selectType 单选S 多选M 必填
-->
<div th:fragment="selectFmr">
    <script type="text/javascript" th:inline="javascript">
        var ctx = [[@{/}]];
        var userId = "userId";
        var userNameId = "userName";

        function choiceFmr(userCode, userName, selectType, jwsqId,callback) {
            userId = userCode;
            userNameId = userName;
            var url = ctx + 'KWZL/XTWH/selectFmr'
            if (selectType === undefined || selectType == null || selectType == '') {
                selectType = "S";
            }
            url += "?selectType=" + selectType;
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            if (!(jwsqId === undefined) && jwsqId != null) {
                url += "&jwsqId=" + jwsqId;
            }
            var userCode = $("#" + userCode).val();
            if (!(userCode === undefined) && userCode != null) {
                url += "&userCode=" + userCode;
            }
            var userName = $("#" + userName).val();
            if (!(userName === undefined) && userName != null) {
                url += "&userName=" + userName;
            }
            url += "&userId=" + $("#" + userId).val();
            $.modal.open("选择人员", url, '1000', '500');
        }

        function choiceUserCallback(userCode, userName) {
            $("#" + userId).val(userCode);
            $("#" + userNameId).val(userName);
        }
    </script>
</div>
<div th:fragment="choiceFmr">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	userCodeId=${userCodeId==null?'userCodeId':userCodeId},
               	 	userNameId=${userNameId==null?'userNameId':userNameId},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择人员：':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
                    selectType=${selectType==null?'S':selectType}
               	 	">
        <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}"
               th:text="${labelName}"></label>
        <div th:class="${divClass}" th:if="${see!=null && !see}">
            <div class="input-group">
                <input th:id="${userCodeId}" th:name="${userCodeId}" th:value="${value}" type="hidden"/> <input
                    class="form-control" th:id="${userNameId}" th:name="${userNameId}"
                    th:onclick="choiceFmr([[${userCodeId}]],[[${userNameId}]],[[${selectType}]],[[${jwsqId}]],[[${callback}]])"
                    th:required="${isrequired!=null && isrequired}"
                    th:value="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(value)}" type="text">
                <span class="input-group-addon detailOrgOrUser"><i
                        class="fa fa-search "></i></span>
            </div>
        </div>
        <div th:class="${divClass!=null?divClass+' form-control-static':'form-control-static'}" th:unless="${!see}"
             th:utext="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(value)}"></div>
    </th:block>
</div>
<!--展示发明设计人员列表
-->
<div th:fragment="initInventorUser">
    <th:block
            th:with="inventorUserList=${T(com.baosight.bscdkj.kw.zl.utils.KwzlUtil).getApplyFmr(jwsqId)}">
        <th:block th:if="${inventorUserList.size()}>0">
            <div class="panel-group" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#fmsjr" class="collapsed">
                                发明设计人
                                <span class="pull-right">
	                   		<i class="fa fa-chevron-down"></i>
	                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="fmsjr" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <table class="table table-bordered table-hover table-striped" id="mytable">
                                        <thead>
                                        <tr>
                                            <th style="text-align: center;">序号</th>
                                            <th style="text-align: center;">姓名</th>
                                            <th style="text-align: center;">工号</th>
                                            <th style="text-align: center;">单位</th>
                                            <th style="text-align: center;">贡献系数(%)</th>
                                        </tr>
                                        </thead>
                                        <tbody id="patentCreatePersonTr">
                                        <th:block th:each="fmr : ${inventorUserList}">
                                            <tr style="font-size: 16px; line-height: 40px;">
                                                <td style='text-align: center;' th:utext="${fmrStat.index+1}"></td>
                                                <td style='text-align: center;' th:utext="${fmr.empName}"></td>
                                                <td style='text-align: center;' th:utext="${fmr.empId}"></td>
                                                <td style='text-align: center;' th:utext="${fmr.extra1}"></td>
                                                <td style='text-align: center;' th:utext="${fmr.gxxs}"></td>
                                            </tr>
                                        </th:block>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </th:if>
        </th:block>
    </th:block>
</div>
</body>
</html>