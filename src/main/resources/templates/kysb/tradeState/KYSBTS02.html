<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改22-04商标状态维护')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-tradeState-edit" th:object="${tradeState}">
         <div class="panel-group" id="accordion" role="tablist"
                             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body">
                        <div class="form-group">
<!--                        <label class="col-sm-4 control-label">商标注册主键：</label>-->
                            <input name="tradeRegistId" th:field="*{tradeRegistId}" class="form-control" type="hidden">
                            <input name="tradeRegist.tradeRegistId" th:field="*{tradeRegist.tradeRegistId}" class="form-control" type="hidden">
                            <input name="tradeContinue.tradeContinueId" th:field="*{tradeContinue.tradeContinueId}" class="form-control" type="hidden">
<!--                        <label class="col-sm-4 control-label">主键：</label>-->
                            <input name="tradeStateId" th:field="*{tradeStateId}" class="form-control" type="hidden">

                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">商标名称：</label>
                            <div class="col-sm-10">
                                <input class="form-control"  th:field="*{tradeRegist.trademarkName}" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">提出人（经办人）：</label>
                            <div class="col-sm-10">
                                <input class="form-control"  th:field="*{tradeRegist.applyPersonName}" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">提出人（经办人）工号：</label>
                            <div class="col-sm-10">
                                <input class="form-control"  th:field="*{tradeRegist.applyPersonGh}" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">单位电话：</label>
                            <div class="col-sm-10">
                                <input class="form-control"  th:field="*{tradeRegist.applyDeptPh}" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">商标形式：</label>
                            <div class="col-sm-4">
                                <th:block th:include="component/select :: init(id='layout', name='tradeRegist.layout' ,businessType='KYSB', dictCode='layout',value=*{tradeRegist.layout})">
                                </th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">商品/服务项目：</label>
                            <div class="col-sm-10"
                                 th:include="/component/selectServce::init(orgCodeId='commodityService',orgNameId='commodityServiceName',level='0',selectType='M',value=${tradeState.tradeRegist.commodityService})">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">商标样式：</label>
                            <div class="col-sm-10"
                                 th:include="/component/attachment :: init(id='tradeFile',name='tradeRegist.tradeFile',
									 sourceId=*{tradeRegist.tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeFile')">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">商标图样：</label>
                            <div class="col-sm-10">
                                <input type="hidden" name="tradeRegist.imgurl" id="imgurl">
                                <div class="layui-upload-list">
                                    <img class="layui-upload-img" th:src="${tradeState.tradeRegist.imgurl}" width="300" height="170" id="imgFile">
                                    <p id="demoText"></p>
                                </div>
                                <button type="button" class="layui-btn" id="shangchuangTp">上传图片</button>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">授权信息：</label>
                            <div class="col-sm-2" style="color: #1c6ac7">
                                <input type="button" class="form-control" onclick="tradeRegistList()" value="点击查看">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">商标使用信息：</label>
                            <div class="col-sm-2" style="color: #1c6ac7">
                                <input type="button" class="form-control" onclick="tradeUseList()" value="点击查看">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">商标许可信息：</label>
                            <div class="col-sm-2" style="color: #1c6ac7">
                                <input type="button" class="form-control" onclick="tradeXukeList()" value="点击查看">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">商标续展信息：</label>
                            <div class="col-sm-2" style="color: #1c6ac7">
                                <input type="button" class="form-control" onclick="tradeContinueList()" value="点击查看">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">商标异议信息：</label>
                            <div class="col-sm-2" style="color: #1c6ac7">
                                <input type="button" class="form-control" onclick="tradeSbyyList()" value="点击查看">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">商标贸易信息：</label>
                            <div class="col-sm-2" style="color: #1c6ac7">
                                <input type="button" class="form-control" onclick="tradeSbmyList()" value="点击查看">
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">注册号：</label>
                                    <div class="col-sm-8">
                                        <input name="regigterNo" class="form-control"  th:field="*{regigterNo}" type="text" >

                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">注册日期：</label>
                                    <div class="col-sm-8">
                                        <input name="registerDate" class="form-control"  th:field="*{initRegisterDate}" type="text" >
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">法律状态：</label>
                                    <div class="col-sm-8">
                                        <th:block th:include="component/select :: init(id='lawStatus', name='lawStatus',isfirst='true' ,businessType='KYSB', dictCode='lawStatus',value=*{lawStatus})">
                                        </th:block>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">有效期截止日：</label>
                                    <div class="col-sm-8">
                                        <input name="endDate" class="form-control"  th:field="*{endDate}" type="text" >
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">填写人：</label>
                                    <div class="col-sm-8">
                                        <input name="applyPersonName" class="form-control"  th:field="*{fillinPerson}" type="text" >
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">填写日期：</label>
                                    <div class="col-sm-8">
                                        <input name="applyPersonName" class="form-control" th:field="*{fillinDate}"  type="text" >
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">备注：</label>
                            <div class="col-sm-10">
                                <input name="remarks" rangelength="1,50" th:field="*{remarks}" class="form-control" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">注册证：</label>
                            <div class="col-sm-10">
                                <div  th:include="/component/attachment :: init(id='tradeSbzczFile',name='tradeRegist.tradeSbzczFile',
									 sourceId=*{tradeRegist.tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeSbzczFile')">

                                </div>
                            </div>
                        </div>
<!--                        <div th:if='${tradeState.tradeContinue.status ne "notExsits" }'>-->
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">续展证明：</label>
                                <div class="col-sm-10"
                                         th:include="/component/attachment :: init(id='continueFile',name='tradeContinue.continueFile',
                                         sourceId=*{tradeContinue.tradeContinueId},sourceModule='KYSB',sourceLabel1='continueFile')">
                                </div>
                            </div>
<!--                        </div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label">变更证明：</label>
                            <div class="col-sm-10">
                                <div  th:include="/component/attachment :: init(id='tradeStateUpdateFile',name='tradeRegist.tradeStateUpdateFile',
                                 sourceId=*{tradeRegist.tradeRegistId},sourceModule='KYSB',sourceLabel1='tradeStateUpdateFile')"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">相关附件：</label>
                            <div class="col-sm-10">
                                <div  th:include="/component/attachment :: init(id='tradeStateFile',name='tradeStateFile',
                                 sourceId=*{tradeStateId},sourceModule='KYSB',sourceLabel1='tradeStateFile')"></div>
                            </div>
                            <input type="hidden" th:value="${tradeState.tradeStateId}">
                        </div>
                    </div>
                </div>
            </div>
         </div>
    </form>
    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-sm btn-primary"
            onclick="doSubmit()">
            <i class="fa fa-check"></i>保 存
        </button>
        &nbsp;
        <button type="button" class="btn btn-sm btn-danger"
            onclick="closeItem()">
            <i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
    <!--按钮区end-->
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeState";

        $("#form-tradeState-edit").validate({
            focusCleanup: true
        });
        var id = $("#tradeRegistId").val();
        function tradeRegistList(){
            $.modal.open("商标", ctx + "kysb/tradeRegist"  + "/selectDetail/KYSBTRD01/"+id,1000,500);
        };
        function tradeUseList(){
            $.modal.openTab("商标使用",  ctx + "kysb/tradeUse" + "/KYSBTRU41/"+id );
        };
        function tradeXukeList(){
            $.modal.openTab("商标许可",  ctx + "kysb/tradePerm" + "/KYSBTRP41/"+id );
        };
        function tradeContinueList(){
            $.modal.openTab("商标续展",  ctx + "kysb/tradeContinue" + "/KYSBTRF41/"+id );
        };
        function tradeSbyyList(){
            $.modal.openTab("商标异议",  ctx + "kysb/tradeFrobje" + "/KYSBTRG41/"+id );
        };
        function tradeSbmyList(){
            $.modal.openTab("商标贸易",  ctx + "kysb/tradeContract" + "/listByRegist/"+id );
        };

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-tradeState-edit').serialize());
            }
        }
        function doSubmit() {
            if ($.validate.form()) {
                $.modal.confirm("确认提交吗？", function() {
                    //$("#workFlow_transitionKey").val(transitionKey); 这个js在提交组件里加了，没有用提交组件的需要这行
                    $.operate.saveTabAlert(prefix + "/edit", $('#form-tradeState-edit').serialize());
                });
            }
        }
    </script>
</body>
<div th:fragment="selectService">
<script th:inline="javascript" type="text/javascript">
    var orgId = "orgId";
    var orgNameId = "deptName";

    function choiceService(orgCodeInputId, orgNameInputId, selectType, level, orgCode, showLevel, callback) {
        orgId = orgCodeInputId;
        orgNameId = orgNameInputId;
        if (selectType === undefined || selectType == null || selectType == '') {
            selectType = "S";
        }
        var url = ctx + "kysb/tradeService/selectServiceList?selectType=" + selectType;
        if (!(level === undefined) && level != null) {
            url += "&level=" + level;
        }
        if (!(showLevel === undefined) && showLevel != null) {
            url += "&showLevel=" + showLevel;
        }
        if (!(callback === undefined) && callback != null) {
            url += "&callback=" + callback;
        }
        url += "&values=" + $("#" + orgId).val();

        //debugger
        var options = {
            title: '选择服务',
            width: "580",
            height: '500',
            url: url,
            callBack: choiceServiceCallback
        };
        $.modal.openOptions(options);
    }

    function choiceServiceCallback(index, layero) {
        var tree = layero.find("iframe")[0].contentWindow.$._tree;
        var body = layer.getChildFrame('body', index);
        layero.find("iframe")[0].contentWindow.saveCheck();
        $("#" + orgId).val(body.find('#treeId').val());
        $("#" + orgNameId).val(body.find('#treeName').val());
        layer.close(index);
    }
    //常规使用 - 普通图片上传
    layui.use(['upload', 'element', 'layer'], function(){
        var $ = layui.jquery
            ,upload = layui.upload
            ,element = layui.element
            ,layer = layui.layer;

        //常规使用 - 普通图片上传
        var uploadInst = upload.render({
            elem: '#shangchuangTp'
            ,url: ctx+"attachment/upload"
            ,before: function(obj){
                //预读本地文件示例，不支持ie8
                obj.preview(function(index, file, result){
                    $('#imgFile').attr('src', result); //图片链接（base64）
                });
            }
            ,done: function(res){
                //如果上传失败
                if(res.code > 0){
                    return layer.msg('上传失败');
                }
                console.log(res)
                $("#imgurl").val(res.docUrl);
                //上传成功的一些操作
                //……
            }
            ,error: function(){
                layer.msg('上传失败');
            }
        });
    })
</script>
</html>