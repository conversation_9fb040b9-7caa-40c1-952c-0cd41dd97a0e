<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('商标附件管理')" />
    <th:block th:include="include :: layout-latest-css" />
	<th:block th:include="include :: ztree-css" />
</head>
<body class="gray-bg">
	<div class="ui-layout-west">
		<div class="box box-main">
			<div class="box-header">
				<div class="box-title">
					<i class="fa icon-grid"></i> 附件树
				</div>
			</div>
			<div class="ui-layout-content">
				<div id="tree" class="ztree"></div>
			</div>
		</div>
	</div>

	<div class="ui-layout-center">
		<div class="container-div">
			<div class="row">
				<div class="btn-group-sm" id="toolbar" role="group">
	                <a class="btn btn-success" onclick="addTreeFile()">
	                    <i class="fa fa-plus"></i> 批量上传
	                </a>
					<a class="btn btn-success" onclick="tradePic()">
						<i class="fa fa-plus"></i> 商标图样处理
					</a>
<!--					<a class="btn btn-success" onclick="addTreeChild()">-->
<!--						<i class="fa fa-plus"></i> 添加树节点-->
<!--					</a>-->
<!--	                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">-->
<!--	                    <i class="fa fa-remove"></i> 删除-->
<!--	                </a>-->
					<input id="flowName" type="hidden"/>
					<input id="serviceCode" type="hidden"/>
				</div>
		        <div class="col-sm-12 select-table table-striped">
				    <table id="bootstrap-table"></table>
				</div>
			</div>
		</div>
	</div>

	<th:block th:include="include :: baseJs" />
	<th:block th:include="include :: layout-latest-js" />
	<th:block th:include="include :: ztree-js" />
	<script th:inline="javascript">
		var prefix = ctx + "kysb/tradeState";
		$(function() {
		    var panehHidden = false;
		    if ($(this).width() < 769) {
		        panehHidden = true;
		    }
		    $('body').layout({ initClosed: panehHidden, west__size: 240 });
	    	initTree();
		    tableList();
		});

		function initTree() {
			$(".ztree").html("正在努力地加载数据中...");
			var options = {
		        url: ctx+"kysb/tradeState" + "/treeData",
		        //expandLevel: 2,
		       onClick : zOnClick
		    };
			$.tree.init(options);

			function zOnClick(event, treeId, treeNode) {
				$("#serviceCode").val(treeNode.code);
				$("#flowName").val(treeNode.name);
				$.table.search();
			}
		}

		function tableList() {
			var options = {
				url: prefix + "/fillList",
				createUrl: prefix + "/add",
				updateUrl: prefix + "/edit?rowId={id}",
				removeUrl: prefix + "/removeFile",
				modalName: "附件",
				queryParams: queryParams,
				columns: [{
					checkbox: true
				},
				{
					field: 'rowId',
					title: '主键',
				},
				{
					field: 'attachmentId',
					title: '主键',
					visible: false
				},
				{
					field: 'sourceModule',
					title: '模块编码',

				},

				{
					field: 'sourceLabel1',
					title: '文件类型',
					formatter: function (value, row, index) {
						if(value=='tradeSbzczFile') return "注册证书";
						if(value=='continueFile') return "商标续展证明";
						if(value=='tradeStateUpdateFile') return "商标变更证明";
						return '其他';
					}
				},
				{
					field: 'attachmentName',
					title: '文件名称',
				},
				{
					field: 'docUrl',
					title: '图样',
					formatter: function (value, row, index) {
						var s = '<img style="width:300;height:40px;"  src="'+value+'" />';
						if(row.sourceLabel1=='tradePic')
						return s;

						return "";
					}
				},
				{
					title: '操作',
					align: 'center',
					formatter: function (value, row, index) {
						var actions = [];
						// actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.id + '\')"><i class="fa fa-edit"></i>编辑</a> ');
						actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.rowId + '\')"><i class="fa fa-remove"></i>删除</a>');
						return actions.join('');
					}
				}]
			};
		    $.table.init(options);
		}

		function queryParams(params) {
            var search = $.table.queryParams(params);
            search.sourceModule = $("#serviceCode").val();
            return search;
        }

		function addTreeFile(){
			if(!$("#serviceCode").val()){
				alert("请选择附件树!");
				return false;
			}
			var fileType = $("#serviceCode").val();
			var treeName = $("#flowName").val()
			var addUrl = prefix + "/KYSBPLSC02?fileType="+fileType+"&treeName="+treeName;
			$.modal.openTab("批量上传", addUrl);
		}
		function tradePic(){
			var addUrl = prefix + "/KYSBPLSC03";
			$.modal.openTab("商标图样处理", addUrl);
		}
	</script>
</body>
</html>