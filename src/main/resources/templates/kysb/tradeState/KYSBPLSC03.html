<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改22-04商标状态维护')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-tradeState-edit" th:object="${tradeState}">
         <div class="panel-group" id="accordion" role="tablist"
                             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="form-group">
                    <label class="col-sm-2 control-label">商标图样：</label>
                    <div class="col-sm-10">
                        <input type="hidden" name="tradeRegist.imgurl" id="imgurl">
                        <div class="layui-upload-list">
                            <img class="layui-upload-img"  width="300" height="170" id="imgFile">
                            <p id="demoText"></p>
                        </div>
                        <button type="button" class="layui-btn" id="shangchuangTp">上传图片</button>
                    </div>
                </div>
            </div>
         </div>
    </form>
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeState";

        $("#form-tradeState-edit").validate({
            focusCleanup: true
        });
        var id = $("#tradeRegistId").val();

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-tradeState-edit').serialize());
            }
        }
        function doSubmit() {
            if ($.validate.form()) {
                $.modal.confirm("确认提交吗？", function() {
                    //$("#workFlow_transitionKey").val(transitionKey); 这个js在提交组件里加了，没有用提交组件的需要这行
                    $.operate.saveTabAlert(prefix + "/edit", $('#form-tradeState-edit').serialize());
                });
            }
        }
    </script>
</body>
<div th:fragment="selectService">
<script th:inline="javascript" type="text/javascript">
    //常规使用 - 普通图片上传
    layui.use(['upload', 'element', 'layer'], function(){
        var $ = layui.jquery
            ,upload = layui.upload
            ,element = layui.element
            ,layer = layui.layer;

        //常规使用 - 普通图片上传
        var uploadInst = upload.render({
            elem: '#shangchuangTp'
            ,url: ctx+"attachment/uploadTradePic"
            ,before: function(obj){
                //预读本地文件示例，不支持ie8
                obj.preview(function(index, file, result){
                    $('#imgFile').attr('src', result); //图片链接（base64）
                });
            }
            ,done: function(res){
                //如果上传失败
                if(res.code > 0){
                    return layer.msg('上传失败');
                }
                console.log(res)
                $("#imgurl").val(res.docUrl);
                //上传成功的一些操作
                //……
            }
            ,error: function(){
                layer.msg('上传失败');
            }
        });
    })
</script>
</html>