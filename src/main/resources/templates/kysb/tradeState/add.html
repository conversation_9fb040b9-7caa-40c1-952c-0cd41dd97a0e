<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增22-04商标状态维护')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-tradeState-add">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">商标注册主键：</label>
                                <div class="col-sm-8">
                                    <input name="tradeRegistId" class="form-control" type="text">
                                </div>
                            </div>
                           <div class="form-group" th:include="include :: initDate(id='endDate', name='endDate', labelName='创建时间')">
                              </div>
                            <div class="form-group" th:include="include :: initRadio(id='lawStatus', name='lawStatus',businessType=null, dictCode=null ,labelName='法律状态')">
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">贸易合同号：</label>
                                <div class="col-sm-8">
                                    <input name="tradeContractNo" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">贸易对象：</label>
                                <div class="col-sm-8">
                                    <input name="tradePartnes" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段1：</label>
                                <div class="col-sm-8">
                                    <input name="extra1" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段2：</label>
                                <div class="col-sm-8">
                                    <input name="extra2" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段3：</label>
                                <div class="col-sm-8">
                                    <input name="extra3" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段4：</label>
                                <div class="col-sm-8">
                                    <input name="extra4" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',businessType=null, dictCode=null ,labelName='删除状态')">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
		<!--按钮区end-->
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeState"

        $("#form-tradeState-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/add", $('#form-tradeState-add').serialize());
            }
        }

    </script>
</body>
</html>