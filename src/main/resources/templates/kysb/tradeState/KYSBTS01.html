<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('商标状态管理员维护列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
			<form id="formId" class="form-horizontal" style="width: 99%;">
				<div class="form-group">
					<div class="form-group" >
						<label class="col-sm-1 control-label">商标名称:</label>
						<div class="col-sm-3">
							<input type="text" class="form-control" name="trademarkName" placeholder="支持模糊查询"/>
						</div>
						<label class="col-sm-1 control-label">注册号:</label>
						<div class="col-sm-3">
							<input type="text" class="form-control" name="regigterNo" placeholder="支持模糊查询"/>
						</div>
					</div>
					<div class="form-group">
						<label class="col-sm-1 control-label"></label>
						<div class="col-sm-3">
						</div>
						<label class="col-sm-1 control-label"></label>
						<div class="col-sm-3">
						</div>
						<label class="col-sm-1 control-label"></label>
						<div class="col-sm-3">
							<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
								<i class="fa fa-search"></i>
								&nbsp;搜索
							</a>
							<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
								<i class="fa fa-refresh"></i>
								&nbsp;重置
							</a>
						</div>
					</div>
				</div>
			</form>
		</div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.addTab()" >
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefixState = ctx + "kysb/tradeState";
		var prefix = ctx + "kysb/tradeRegist";
		var techClass = [[${@dict.getDictList('KYSB','techClass')}]];
		var commodityService = [[${@dict.getDictList('KYSB','commodityService')}]];
		var countryRegion = [[${@dict.getDictList('KYSB','countryRegion')}]];
		// var trademarkLabel = [[${@dict.getDictList('KYSB','trademarkLabel')}]];
		$(function() {
			var options = {
				url: prefix + "/queryState",
				modalName: "商标状态",
				updateUrl: prefixState + "/editS01/{id}",
				pageSize:100,
				columns: [{
					checkbox: true
				},
				{
					field: 'tradeStateId',
					title: '主键',
					visible: false
				},
				{
					field: 'imgurl',
					title: '商标图样',
					formatter: function (value, row, index) {
						var s = '<img style="width:300;height:40px;"  src="'+value+'" />';
						return s;
					}
				},
				{
					field: 'trademarkName',
					title: '商标名称'
				},
				{
					field: 'regigterNo',
					title: '注册号'
				},
				{
					field: 'registerDate',
					title: '注册日期',
					sortable:"true"
				},
				{
					field: 'endTime',
					title: '有效期限',
					sortable:"true"
				},
				{
					field: 'commodityService',
					title: '商品/服务项目',
					formatter:function (value,row,index) {
						if(value==null || value=="")
						return "待维护";
						else return "已维护";
					},
					cellStyle:function (value,row,index){
						var commodityService = row.commodityService;
						if(commodityService=="" || commodityService==null)
						return {css:{"color":"red"}}
						else{return {css:{"color":"black"}}}
					}
				},
				{
					field: 'techClass',
					title: '技术分类',
					formatter:function (value,row,index) {
						if(value==null || value=="")
							return "待维护";
						else return "已维护";
					},
					cellStyle:function (value,row,index){
						var techClass = row.techClass;
						if(techClass=="" || techClass==null)
							return {css:{"color":"red"}}
						else{return {css:{"color":"black"}}}
					}
				},
				{
					field: 'trademarkRegistTypeValue',
					title: '商标注册类别'
				},
				// {
				// 	field: 'trademarkLabel',
				// 	title: '商标标签',
				// 	formatter:function (value,row,index) {
				// 		if(value==null || value=="")
				// 		return "待维护";
				// 		return $.table.selectDictLabel(trademarkLabel, value);
				// 	},
				// 	cellStyle:function (value,row,index){
				// 		var trademarkLabel = row.trademarkLabel;
				// 		if(trademarkLabel=="" || trademarkLabel==null)
				// 			return {css:{"color":"red"}}
				// 		else{return {css:{"color":"black"}}}
				// 	}
				// },

				{
					title: '操作',
					align: 'center',
					formatter: function (value, row, index) {
						var actions = [];
						actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.tradeStateId + '\')"><i class="fa fa-edit"></i>状态维护</a> ');
						return actions.join('');
					}
				}
			]};
			$.table.init(options);
		});
    </script>
</body>
</html>