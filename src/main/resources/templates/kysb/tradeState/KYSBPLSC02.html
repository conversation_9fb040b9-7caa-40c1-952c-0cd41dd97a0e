<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('批量上传附件')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-attachmentMap-add">

        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">附件树名称：</label>
                            <div class="col-sm-10">
                                <input  class="form-control" type="text" th:value="${treeName}"  readonly>
                            </div>
                        </div>
                        <input id="sourceModule" name="sourceModule" class="form-control" type="hidden" th:value="KYSB"  readonly>
                        <input id="sourceLabel1" name="sourceLabel1" class="form-control" type="hidden" th:value="${fileType}"  readonly>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">附件：</label>
                            <div class="col-sm-10">
                                <th:block
                                        th:replace="/component/attachment :: init(id='attachmentId', name='attachmentId',isrequired=true)"></th:block>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--按钮区-->
        <div class="toolbar toolbar-bottom" role="toolbar">
            <button type="button" class="btn btn-primary"
                    onclick="saveHandler()">
                <i class="fa fa-check"></i>保 存
            </button>
            <button type="button" class="btn btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>关 闭
            </button>
        </div>
        <!--按钮区end-->
    </form>
</div>
<script th:inline="javascript">
    var prefix = ctx + "attachment";

    function saveHandler() {
        postData(prefix + "/add", {}, function (result) {
            if ($("#check").val() === 0) {
                $.modal.msgSuccess('文件正在上传中，请稍后重试！');
            }
            if (result.code === 0) {
                $.modal.msgSuccess('保存成功！');
            } else {
                $.modal.alertError(result.msg);
            }
        });
    }

    function postData(url, data = {}, callback) {
        var formData = $('#form-attachmentMap-add').serializeArray();
        formData.forEach(obj => {
            data[obj.name] = obj.value;
        })
        postJsonData(url, data, callback);
    }

    function postJsonData(url, data, callback) {
        submitContentType(url, 'application/json;charset=UTF-8', 'post', 'json', JSON.stringify(data), callback);
    }

    function submitContentType(url, contentType, type, dataType, data, callback) {
        var config = {
            url: url,
            type: type,
            dataType: dataType,
            data: data,
            contentType: contentType,
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                if (typeof callback == "function") {
                    callback(result);
                }
                $.modal.closeLoading();
            }
        };
        $.ajax(config)
    }
</script>
</body>
</html>