<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('处理列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse" >
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">

					<label class="col-sm-1 control-label">使用性质：</label>
					<div class="col-sm-3">
						<th:block  th:include="component/select :: init(id='showType', name='showType',isfirst='true' ,businessType='KYSB', dictCode='showType') ">
						</th:block>
					</div>
					<label class="col-sm-1 control-label">申请理由:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="showReason" placeholder="支持模糊查询"/>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
				    <label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeShow";
		var showType = [[${@dict.getDictList('KYSB','showType')}]];

		$(function() {
			var options = {
				url: prefix + "/pageDB",
				queryParams: queryParams,
				modalName: "商标对外出示",
				columns: [{
					checkbox: true
				},
				{
					field: 'tradeShowId',
					title: '主键',
					visible: false
				},
				{
					field: 'showType',
					title: '对外出示类型',
					formatter:function (value,row,index) {
						return $.table.selectDictLabel(showType, value);
					}
				},
				{
					field: 'showReason',
					title: '申请理由'
				},
				{
					field: 'showTerm',
					title: '使用期限（天）'
				},
				{
					field: 'createUserLabel',
					title: '创建人'
				},
				{
					field: 'createDate',
					title: '创建时间'
				},
				{
					field: 'updateUserLabel',
					title: '更新人'
				},
				{
					field: 'updateDate',
					title: '更新时间'
				},
				{
					title: '操作',
					align: 'center',
					formatter: function(value, row, index) {
						var actions = [];
						var param = "'"+row.pageNo+"','"+row.businessId+"','"+row.taskId+"'";
						actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryDBDetailHandle(' + param + ')"><i class="fa fa-edit"></i>处理</a> ');
						return actions.join('');
					}
				}]
			};
			$.table.init(options);
		});
        
        function queryParams(params) {
            var search = $.table.queryParams(params);
            search.businessNameLike = $("#businessNameLike").val();
            search.processCode = [[${processCode}]];
            search.currentActivity = [[${activityCode}]];
            return search;
        }
        
        function queryDBDetailHandle(pageNo,businessId,taskId){
        	if(!pageNo||!pageNo.trim()){//节点上没有配form,注意需要trim
        		pageNo = "KYSBTRD13";
        	}
        	var url = prefix + "/queryDBDetailHandle/"+pageNo + "/" + businessId + "/" + taskId;
        	// 选卡页方式打开并刷新当前页 isRefresh 是否刷新
            $.modal.openTab("商标对外出示详细", url, true);
        }
    </script>
</body>
</html>