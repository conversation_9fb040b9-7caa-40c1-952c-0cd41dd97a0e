<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('商标注册详细')" />

<th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="wrapper wrapper-content">
		<form class="form-horizontal m" id="form-demo" th:object="${tradeShow}">
			<div class="panel-group" id="accordion1" role="tablist" aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#sbxx" aria-expanded="false" class="collapsed">商标信息
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right"><i class="fa fa-chevron-down"
															aria-hidden="true"></i></span>
							</a>
						</h4>
					</div>
					<div id="sbxx" class="panel-collapse collapse in"
						 aria-expanded="false">
						<div class="form-group">
							<label class="col-sm-2 control-label ">商标列表：</label>
							<div class="col-sm-2" style="color: #1c6ac7">
								<input type="button" class="form-control" onclick="tradeRegistList()" value="点击查看">
							</div>
						</div>
					</div>
				</div>
			</div>
			<div th:if='${tradeShow.status  eq "end"}'>
				<div class="panel-group" id="accordion2" role="tablist" aria-multiselectable="true">
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#version"
								   href="#sbxxx" aria-expanded="false" class="collapsed">下载
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<span class="pull-right"><i class="fa fa-chevron-down"
																aria-hidden="true"></i></span>
								</a>
							</h4>
						</div>
						<div id="sbxxx" class="panel-collapse collapse in"
							 aria-expanded="false">
							<div class="form-group">
								<label class="col-sm-2 control-label ">相关文件：</label>
								<div class="col-sm-2" style="color: #1c6ac7">
									<input type="button" class="form-control" onclick="download()" value="点击下载">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel-group" id="accordion" role="tablist"
				 aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right"><i class="fa fa-chevron-down"
															aria-hidden="true"></i></span>
							</a>
						</h4>
					</div>
					<div id="jbxx" class="panel-collapse collapse in"
						 aria-expanded="false">
						<div class="panel-body">
							<div class="form-group" hidden>
								<label class="col-sm-4 control-label is-required">商标注册主键：</label>
								<div class="col-sm-8">
									<input name="tradeShowId" th:field="*{tradeShowId}" class="form-control" type="hidden" required>
									<!-- <input name="tradeRegistId" th:field="*{tradeRegistId}" class="form-control" type="text" required>-->
									<input name="tradeRegistId" id ="tradeRegistId" class="form-control" type="text" th:field="*{tradeRegistId}" required>

								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">使用性质：</label>
								<div class="col-sm-10">
									<th:block  th:include="component/select :: init(id='showType', name='showType',isfirst='true' ,businessType='KYSB', dictCode='showType',value=*{showType}) ">
									</th:block>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">申请单位：</label>
								<div class="col-sm-10">
									<input name="applyDeptName" th:field="*{applyDeptName}" class="form-control" type="text">
								</div>
							</div>

							<div class="panel panel-default panel-group">
								<div class="panel-heading">
									<h4 class="panel-title">
										<a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#6">出示对方
											<span class="pull-right">
                                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                                            </span>
										</a>
									</h4>
								</div>
								<!--折叠区域-->
								<div aria-expanded="false" class="panel-collapse collapse in" id="6">
									<div class="panel-body">
										<div class="row form-group">
											<button class="btn btn-white btn-sm" onclick="addColumn('H')" type="button"><i
													class="fa fa-plus"> 增加</i></button>
											<button class="btn btn-white btn-sm" onclick="sub.delColumn()" type="button"><i
													class="fa fa-minus"> 删除</i></button>
											<div class="col-sm-12 select-table table-striped">
												<table id="bootstrap-tableH"></table>
											</div>
										</div>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">申请理由：</label>
								<div class="col-sm-10">
                                    <textarea name="denyAnalyse" rows="7" cols="150" th:field="*{showReason}"  class="form-control" rangelength="1,500"  required="required" readonly>
                                    </textarea><!--th:field="*{denyAnalyse}"-->
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">使用期限（天）：</label>
								<div class="col-sm-10">
									<input name="showTerm" th:field="*{showTerm}" class="form-control" type="text">
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">用途：</label>
								<div class="col-sm-10">
									<input name="purpose" th:field="*{purpose}" rangelength="1,15" class="form-control"  type="text">
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label ">附件：</label>
								<div class="col-sm-10"
									 th:include="/component/attachment :: init(id='tradeShowFile',see='true',name='tradeShowFile',
                                 sourceId=*{tradeShowId},sourceModule='KYSB',sourceLabel1='tradeShowFile')">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div style="height: 100px"></div>
		</form>
		<!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">		
			<!-- 流程跟踪 -->
			<th:block th:include="component/wfCommentList :: init(processInstanceId=${processInstanceId})" />
			<button type="button" class="btn btn-danger" onclick="closeItem()">
				<i class="fa fa-reply-all"></i>
				关 闭
			</button>
		</div>
		<!--按钮区end-->
	</div>
	<script th:inline="javascript">
		$("#form-demo input").attr("readOnly",true);
		$("#form-demo select").attr("disabled",true);
		var useId =$("#tradeShowId").val();
		var optionsH = { // 推广移植后经济指标、产品、质量等情况
			id: "bootstrap-tableH",
			url: ctx + "kysb/tradeShowSub"+"/list/"+useId,
			pagination: false,
			showSearch: false,
			showRefresh: false,
			showToggle: false,
			showColumns: false,
			sidePagination: "client",
			columns: [{
				checkbox: true
			},
			{
				field: 'orderNum',
				align: 'center',
				title: "序号",
				formatter: function (value, row, index) {
					var columnIndex = $.common.sprintf("<input type='hidden' name='showSubList[%s].index' value='%s'>", index, $.table.serialNumber(index));
					var columnId = $.common.sprintf("<input type='hidden' name='showSubList[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
					return columnIndex + $.table.serialNumber(index) + columnId;
				}
			},
			{
				field: 'showCompName',
				align: 'center',
				title: '单位/机构名称',
				formatter: function (value, row, index) {
					var html = $.common.sprintf("<input class='form-control' type='text' name='showSubList[%s].showCompName' value='%s' readonly>" +
							"" +
							"", index, value);
					var html1 = $.common.sprintf("<input class='form-control' type='hidden' name='showSubList[%s].tradeShowSubId' value='%s'>" +
							"" +
							"", index, row.tradeShowSubId);
					return html+html1;
			}
			},
			{
				field: 'showCompAddr',
				align: 'center',
				title: '单位/机构法定地址',
				formatter: function (value, row, index) {
					var html = $.common.sprintf("<input class='form-control' type='text' name='showSubList[%s].showCompAddr' value='%s' readonly>", index, value);
					return html;
				}
			},
			]
		};
		$.table.init(optionsH);
		var id = $("#tradeRegistId").val();
		function tradeRegistList(){
			$.modal.openTab("商标列表",  ctx + "kysb/tradeRegist" + "/KYSBTRD02/"+id );
		};

		function download() {
			if ($.validate.form()) {
				window.open(ctx + "kysb/tradepdf/download/"+useId);
			}
		}

	</script>
</body>
</html>