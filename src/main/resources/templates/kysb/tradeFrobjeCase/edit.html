<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改商标注册-异议-案件商标（异议的子）
他人对宝钢的异议子')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-tradeFrobjeCase-edit" th:object="${tradeFrobjeCase}">
         <div class="panel-group" id="accordion" role="tablist"
                             aria-multiselectable="true">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#version"
                                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                                        aria-hidden="true"></i></span>
                                        </a>
                                    </h4>
                                </div>
                                <div id="jbxx" class="panel-collapse collapse in"
                                     aria-expanded="false">
                                    <div class="panel-body">
            <input name="tradeFrobjeCaseId" th:field="*{tradeFrobjeCaseId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-4 control-label is-required">商标异议主键：</label>
                <div class="col-sm-8">
                    <input name="tradeFrobjeId" th:field="*{tradeFrobjeId}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-4 control-label">排序：</label>
                <div class="col-sm-8">
                    <input name="orderNum" th:field="*{orderNum}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-4 control-label">异议商标名称：</label>
                <div class="col-sm-8">
                    <input name="objeTradeName" th:field="*{objeTradeName}" class="form-control" type="text">
                </div>
            </div>

      <div class="form-group" th:include="include :: initSelectBox(id='objeTradeType', name='objeTradeType',businessType=null, dictCode=null, value=${tradeFrobjeCase.objeTradeType} ,labelName='异议商标类别')">
       </div>
            <div class="form-group">    
                <label class="col-sm-4 control-label">异议商标的申请日：</label>
                <div class="col-sm-8">
                    <input name="objeTradeDate" th:field="*{objeTradeDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-4 control-label">异议商标的注册日：</label>
                <div class="col-sm-8">
                    <input name="objeTradeRegdate" th:field="*{objeTradeRegdate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-4 control-label">异议截止日期：</label>
                <div class="col-sm-8">
                    <input name="closeDate" th:field="*{closeDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-4 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-4 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',businessType=null, dictCode=null ,value=${tradeFrobjeCase.delStatus} ,labelName='删除状态')">
            </div>
			</div>
                    </div>
                </div>
            </div>
        </form>
        <!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
		<!--按钮区end-->
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeFrobjeCase";

        $("#form-tradeFrobjeCase-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-tradeFrobjeCase-edit').serialize());
            }
        }

    </script>
</body>
</html>