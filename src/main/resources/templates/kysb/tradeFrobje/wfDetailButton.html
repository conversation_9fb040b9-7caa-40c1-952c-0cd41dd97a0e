<div th:fragment="init">
	<div class="toolbar toolbar-bottom" role="toolbar">		
		<button type="button" class="btn btn-primary" onclick="save()">
			<i class="fa fa-check"></i>
			暂存
		</button>
		<!-- 流程跟踪 -->
		<th:block th:include="component/wfCommentList :: init(processInstanceId=${workFlow.processInstanceId})" />
		<!-- 提交 -->
		<th:block th:include="component/wfSubmit:: init(taskId=${workFlow.taskId},callback=doSubmit)"/>
		<!-- 退回 -->
		<th:block th:include="component/wfReturn :: init(taskId=${workFlow.taskId},callback=doReturn)"/>
		<span th:if='${workFlow.currentActivity eq "DBGGSZGSH"}'>
			<!-- 自由流 -->
			<th:block th:include="component/wfDoOtherAndCloseItem :: init(businessId=${workFlow.businessId}, taskId=${workFlow.taskId}, addTaskType='X')"/>
		</span>
		<span th:if='${workFlow.currentActivity eq "ManualDBGTJCL"}'>
			<!-- 自由流 -->
			<th:block th:include="component/wfDoOtherAndCloseItem :: init(businessId=${workFlow.businessId}, taskId=${workFlow.taskId}, addTaskType='X')"/>
		</span>
		<button type="button" class="btn btn-danger" onclick="closeItem()">
			<i class="fa fa-reply-all"></i>
			关 闭
		</button>
	</div>
	<script th:inline="javascript">
		//暂存都是一样的，写在公共的里面
		var res ;
		function save(){
			var config = {
					url: ctx + "kysb/tradeFrobje/save",
					type: "post",
					dataType: "json",
					async:false,
					data: $('#form-tradeFrobje-edit').serialize(),
					beforeSend: function () {
						$.modal.loading("正在处理中，请稍后...");
					},
					success: function (result) {
						$.modal.alertSuccess(result.msg);
						res=result.msg;
						$.modal.closeLoading();
					}
				};
			$.ajax(config);
			if(res=="暂存成功"){
				$("#bootstrap-tableH").bootstrapTable("destroy");
				initSub();
			}
		}
		
		function doReturn(returnActivityKey) {
			var url = ctx + "kysb/tradeFrobje/doReturn";
			if ($.validate.form()) {
				$.modal.confirm("确认退回吗？", function () {
					//$("#workFlow_returnActivityKey").val(returnActivityKey);这个js在退回组件里加了，没有用退回组件的需要这行
						$.operate.saveTabAlert(url, $('#form-tradeFrobje-edit').serialize());
				});		
			}
		}
	</script>
</div>