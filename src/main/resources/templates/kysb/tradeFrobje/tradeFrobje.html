<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('商标注册-异议（子）
他人对宝钢异议子列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">
					<label class="col-sm-1 control-label">案件性质:</label>
					<div class="col-sm-3">
						<th:block th:include="component/select :: init(id='objectionType', name='objectionType',isfirst='true' ,
                                     businessType='KYSB', dictCode='objectionType')">
						</th:block>
					</div>
					<label class="col-sm-1 control-label">权利人:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="frobjeOwner" placeholder="支持模糊查询"/>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
				    <label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="tradeRegistList()" >
                <i class="fa fa-plus"></i> 新增
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>
            <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                <i class="fa fa-download"></i> 导出
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeFrobje";
		function tradeRegistList(){
			$.modal.open("商标", ctx + "kysb/tradeRegist"  + "/ENDTRADE",1000,500);
		};
		function opendTab(regId) {
			if(regId==null || regId ==""){
				$.modal.alertWarning("请至少选择一个商标");
				return;
			}
			$.modal.openTab("新建对宝钢商标异议", prefix + "/newTradeFrobje/edit/"+regId );
		}
		var objectionType = [[${@dict.getDictList('KYSB','objectionType')}]];
        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "商标注册-异议（子）他人对宝钢异议子",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'tradeFrobjeId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'objectionType',
                    title: '案件性质',
					formatter:function (value,row,index) {
						return $.table.selectDictLabel(objectionType, value);
					}
                },
                {
                    field: 'frobjeOwner',
                    title: '权利人'
                },
                // {
                //     field: 'applyPerson',
                //     title: '被异议的提出人'
                // },
                {
                    field: 'applyComp',
                    title: '申请人/申请单位'
                },

                {
                    field: 'createDate',
                    title: '创建时间'
                },

                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.tradeFrobjeId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.tradeFrobjeId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>