<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('已处理列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse" >
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">

					<label class="col-sm-1 control-label">案件性质:</label>
					<div class="col-sm-3">
						<th:block th:include="component/select :: init(id='objectionType', name='objectionType',isfirst='true' ,
                                     businessType='KYSB', dictCode='objectionType')">
						</th:block>
					</div>
					<label class="col-sm-1 control-label">权利人:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="frobjeOwner" placeholder="支持模糊查询"/>
					</div>
				</div>
				<div class="form-group">
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
				    <label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
				    </div>
					<label class="col-sm-1 control-label"></label>
					<div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
		var prefix = ctx + "kysb/tradeFrobje";
		var objectionType = [[${@dict.getDictList('KYSB','objectionType')}]];
		$(function() {
			var options = {
				url: prefix + "/pageYB",
				queryParams: queryParams,
				modalName: "商标注册-驳回复审（子）",
				columns: [{
					checkbox: true
				},
				{
					field: 'tradeFrobjeId',
					title: '主键',
					visible: false
				},
				{
					field: 'objectionType',
					title: '案件性质',
					formatter:function (value,row,index) {
						return $.table.selectDictLabel(objectionType, value);
					}
				},
				{
					field: 'frobjeOwner',
					title: '权利人'
				},
				// {
				// 	field: 'applyPerson',
				// 	title: '被异议的提出人'
				// },
				{
					field: 'applyComp',
					title: '申请人/申请单位'
				},

				{
					field: 'createDate',
					title: '创建时间'
				},
				{
					field: 'currentOperator',
					title: '当前处理人',
				},
				{
					title: '操作',
					align: 'center',
					formatter: function(value, row, index) {
						var actions = [];
						var param = "'" + row.businessId + "','" + row.flowId + "'";
						actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryYBDetail(' + param + ')"><i class="fa fa-edit"></i>查看</a> ');
						return actions.join('');
					}
				}]
			};
			$.table.init(options);
		});

        function queryParams(params) {
            var search = $.table.queryParams(params);
            search.businessNameLike = $("#businessNameLike").val();
            return search;
        }
        
        function queryYBDetail(businessId,processInstanceId){
        	var url = prefix + "/queryYBDetail/KYSBTRG22/" + businessId + "/" + processInstanceId;
        	// 选卡页方式打开并刷新当前页 isRefresh 是否刷新
            $.modal.openTab("宝钢商标异议已处理详细", url, true);
        }
    </script>
</body>
</html>