<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增商标注册-异议（子）
他人对宝钢异议子')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-tradeFrobje-add">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version"
                               href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <span class="pull-right"><i class="fa fa-chevron-down"
                                                            aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jbxx" class="panel-collapse collapse in"
                         aria-expanded="false">
                        <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-4 control-label is-required">商标注册主键：</label>
                            <div class="col-sm-8">
                                <input name="tradeRegistId" class="form-control" type="text" required>
                            </div>
                        </div>
                       <div class="form-group" th:include="include :: initSelectBox(id='objectionType', name='objectionType',businessType=null, dictCode=null, labelName='案件性质')">
                       </div>
                        <div class="form-group">
                            <label class="col-sm-4 control-label">权利人：</label>
                            <div class="col-sm-8">
                                <input name="frobjeOwner" class="form-control" type="text">
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-sm-4 control-label">被异议的提出人：</label>
                            <div class="col-sm-8">
                                <input name="applyPerson" class="form-control" type="text">
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-sm-4 control-label">被异议的提出单位：</label>
                            <div class="col-sm-8">
                                <input name="applyComp" class="form-control" type="text">
                            </div>
                        </div>


                        <div class="form-group">
                            <label class="col-sm-4 control-label">代理机构对异议的建议：</label>
                            <div class="col-sm-8">
                                <input name="agencySug" class="form-control" type="text">
                            </div>
                        </div>
                        <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',businessType=null, dictCode=null ,labelName='删除状态')">
                        </div>
                      </div>
                    </div>
                </div>
            </div>
        </form>
        <!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
		<!--按钮区end-->
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeFrobje"

        $("#form-tradeFrobje-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/add", $('#form-tradeFrobje-add').serialize());
            }
        }

    </script>
</body>
</html>