<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('已处理列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse" >
			<input type="hidden" id = "registId" th:value="${registId}" />
		</div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
		var prefix = ctx + "kysb/tradeFrobje";
        var registId = $("#registId").val();
        var objectionType = [[${@dict.getDictList('KYSB','objectionType')}]];
		$(function() {
			var options = {
				url: prefix + "/pageTradeSelect/"+registId,
				queryParams: queryParams,
				modalName: "商标注册-驳回复审（子）",
				columns: [{
					checkbox: true
				},
				{
					field: 'tradeFrobjeId',
					title: '主键',
					visible: false
				},
				{
					field: 'objectionType',
					title: '案件性质',
					formatter:function (value,row,index) {
						return $.table.selectDictLabel(objectionType, value);
					}
				},
				{
					field: 'frobjeOwner',
					title: '权利人'
				},
				// {
				// 	field: 'applyPerson',
				// 	title: '被异议的提出人'
				// },
				{
					field: 'applyComp',
					title: '申请人/申请单位'
				},

				{
					field: 'createDate',
					title: '创建时间'
				},

				{
					title: '操作',
					align: 'center',
					formatter: function(value, row, index) {
                        var param = "'" + row.tradeFrobjeId + "'";
                        var actions = [];
						actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryYBDetail(' + param + ')"><i class="fa fa-edit"></i>查看</a> ');
						return actions.join('');
					}
				}]
			};
			$.table.init(options);
		});

        function queryParams(params) {
            var search = $.table.queryParams(params);
            search.businessNameLike = $("#businessNameLike").val();
            return search;
        }

		function queryYBDetail(businessId){
			var url = prefix + "/queryYBDetail/KYSBTRG42/" + businessId ;
			// 选卡页方式打开并刷新当前页 isRefresh 是否刷新
			$.modal.openTab("商标异议详细", url, true);
		}
    </script>
</body>
</html>