<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('商标注册-异议（子）他人对宝钢异议子')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-tradeFrobje-edit" th:object="${tradeFrobje}">
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version"
                               href="#sbxx" aria-expanded="false" class="collapsed">商标信息
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <span class="pull-right"><i class="fa fa-chevron-down"
                                                            aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="sbxx" class="panel-collapse collapse in"
                         aria-expanded="false">
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">商标名称：</label>
                            <div class="col-sm-10">
                                <input name="trademarkName" id="trademarkName" class="form-control" th:value="${tradeFrobje.tradeRegist.trademarkName}" type="text" readonly>
                            </div>
                        </div>
                        <input name="trademarkRegistType" id="trademarkRegistType" class="form-control" th:value="${tradeFrobje.tradeRegist.trademarkRegistType}" type="hidden" readonly>

                        <input name="createTime" id="createTime" class="form-control" th:value="${tradeFrobje.tradeRegist.createTime}" type="hidden" readonly>

                        <input name="registerDate" id="registerDate" class="form-control" th:value="${tradeFrobje.tradeRegist.registerDate}" type="hidden" readonly>

                        <input name="trademarkRegistTypeValue" id="trademarkRegistTypeValue" class="form-control" th:value="${tradeFrobje.tradeRegist.trademarkRegistTypeValue}" type="hidden" readonly>

                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">商标图样：</label>
                            <div class="col-sm-10">
                                <img class="layui-upload-img " th:src="${tradeFrobje.tradeRegist.imgurl}" width="100" height="50" id="imgFile">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">注册号：</label>
                            <div class="col-sm-10">
                                <input name="regigterNo" class="form-control" th:value="${tradeFrobje.tradeState.regigterNo}" type="text" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">经办人：</label>
                            <div class="col-sm-10">
                                <input name="applyPersonName" class="form-control" th:value="${tradeFrobje.tradeRegist.applyPersonName}" type="text" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">商标注册类别：</label>
                            <div class="col-sm-10">
                                <input type="text" class="form-control" th:value="${tradeFrobje.tradeRegist.trademarkRegistTypeValue}" readonly>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label ">授权信息：</label>
                            <div class="col-sm-2" style="color: #1c6ac7">
                                <input type="button" class="form-control" onclick="tradeRegistList()" value="点击查看">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#6">涉案商标信息
                            <span class="pull-right">
								<i aria-hidden="true" class="fa fa-chevron-down"></i>
							</span>
                        </a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="6">
                    <div class="panel-body">
                        <div class="row form-group">
                            <button class="btn btn-white btn-sm" onclick="addColumn('H')" type="button"><i
                                    class="fa fa-plus"> 增加</i></button>
                            <button class="btn btn-white btn-sm" onclick="sub.delColumn()" type="button"><i
                                    class="fa fa-minus"> 删除</i></button>
                            <div class="col-sm-12 select-table table-striped">
                                <table id="bootstrap-tableH" class="table table-hover"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#yyxx" aria-expanded="false" class="collapsed">案件信息                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="yyxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body">
                        <input name="tradeFrobjeId" id = "tradeFrobjeId" th:value="${tradeFrobje.tradeFrobjeId}" type="hidden">
                        <div class="form-group" hidden>
                            <label class="col-sm-4 control-label is-required">商标注册主键：</label>
                            <div class="col-sm-8">
                                <input name="tradeRegistId" id="tradeRegistId" th:value="${tradeFrobje.tradeRegistId}" class="form-control" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label  is-required">案件性质：</label>
                            <div class="col-sm-10">
                                <th:block th:include="component/select :: init(id='objectionType', name='objectionType',isfirst='true' ,
                                     businessType='KYSB', dictCode='objectionType',value=*{objectionType})">
                                </th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label  is-required">具体类型：</label>
                            <div class="col-sm-10">
                                <th:block th:include="component/select :: init(id='yytype', name='yytype',isfirst='true' ,
                                     businessType='KYSB', dictCode='yytype',value=*{yytype})">
                                </th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">权利人：</label>
                            <div class="col-sm-10">
                                <input name="frobjeOwner" th:value="${tradeFrobje.tradeRegist.ownership}" class="form-control" type="text" readonly>
                            </div>

                        </div>
<!--                        <div class="form-group">-->
<!--                            <label class="col-sm-2 control-label is-required">申请人：</label>-->
<!--                            <div class="col-sm-10">-->
<!--                                <input name="applyPerson" th:field="*{applyPerson}" class="form-control" type="text">-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">申请人/申请单位：</label>
                            <div class="col-sm-10">
                                <input name="applyComp" th:field="*{applyComp}" class="form-control" type="text">
                            </div>
                        </div>
<!--                        <div class="form-group">-->
<!--                            <label class="col-sm-2 control-label ">应诉截止日期：</label>-->
<!--                            <div class="col-sm-10">-->
<!--                                <div th:include="/component/date :: init(id='responseCloseDate' ,name='responseCloseDate', strValue=*{responseCloseDate})"></div>-->

<!--                            </div>-->
<!--                        </div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">代理机构建议：</label>
                            <div class="col-sm-10">
                                <textarea name="agencySug" rows="7" cols="150" th:field="*{agencySug}"  class="form-control"
                                          rangelength="1,500"  required="required" ></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">案件通知书：</label>
                            <div class="col-sm-10"
                                 th:include="/component/attachment :: init(id='otherTrade',name='otherTrade',
                                     sourceId=*{tradeFrobjeId},sourceModule='KYSB',sourceLabel1='otherTrade')">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
<!--            &lt;!&ndash; 流程意见 name必须为 workFlow.comment &ndash;&gt;-->
<!--            <div class="panel-group" aria-multiselectable="true">-->
<!--                <div class="panel panel-default">-->
<!--                    <div class="panel-heading">-->
<!--                        <h4 class="panel-title">-->
<!--                            <a data-toggle="collapse" href="#spyj" aria-expanded="false" class="collapsed">-->
<!--                                审批意见-->
<!--                                <span class="pull-right">-->
<!--									<i class="fa fa-chevron-down" aria-hidden="true"></i>-->
<!--								</span>-->
<!--                            </a>-->
<!--                        </h4>-->
<!--                    </div>-->
<!--                    <div id="spyj" class="panel-collapse collapse in" aria-expanded="false">-->
<!--                        <div class="panel-body">-->
<!--                            <div class="form-group col-sm-12">-->
<!--	                            <textarea class="form-control" id="workFlow_comment" name="workFlow.comment" style="height: 200px; width: 100%;"-->
<!--                                          th:utext="${tradeFrobje.workFlow.comment}"></textarea>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                </div>-->
<!--            </div>-->
<!--            &lt;!&ndash; 流程意见end &ndash;&gt;-->
            <!-- 流程相关信息 -->
            <th:block th:include="component/wfWorkFlow :: init(workFlow=${tradeFrobje.workFlow})" />
            <!-- 流程相关信息end -->
            </div>
        </form>
    <!--按钮区-->
    <th:block th:include="kysb/tradeFrobje/wfDetailButton :: init(workFlow=${tradeFrobje.workFlow})" />
    <!--按钮区end-->
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "kysb/tradeFrobje";

        $("#form-tradeFrobje-edit").validate({
            focusCleanup: true
        });
        initSub();
        function initSub(){
          var useId =$("#tradeFrobjeId").val();
          var optionsH = { // 推广移植后经济指标、产品、质量等情况
              id: "bootstrap-tableH",
              url: ctx + "kysb/tradeFrobjeCase"+"/list/"+useId,
              pagination: false,
              showSearch: false,
              showRefresh: false,
              showToggle: false,
              showColumns: false,
              sidePagination: "client",
              onLoadSuccess: function() {
                  $('#bootstrap-tableH .date').datetimepicker({
                      format: 'yyyy-mm-dd',
                      weekStart: 0,
                      autoclose: true,
                      startView: 2,
                      minView: 2,
                      forceParse: false,
                      todayBtn: true,
                      language: 'zh-CN'
                  });

              },
              columns: [{
                  checkbox: true
              },
              {
                  field: 'index',
                  align: 'center',
                  title: "序号",
                  formatter: function (value, row, index) {
                      var columnIndex = $.common.sprintf("<input type='hidden' name='frobjeCaseList[%s].index' value='%s'>", index, $.table.serialNumber(index));
                      var columnId = $.common.sprintf("<input type='hidden' name='frobjeCaseList[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                      return columnIndex + $.table.serialNumber(index) + columnId;
                  }
              },
              {
                  field: 'objeTradeName',
                  align: 'center',
                  title: '商标名称',
                  formatter: function (value, row, index) {
                      var html = $.common.sprintf("<input class='form-control' type='text' name='frobjeCaseList[%s].objeTradeName' value='%s' readonly>", index, value);
                      var html1 = $.common.sprintf("<input class='form-control' type='hidden' name='frobjeCaseList[%s].tradeFrobjeCaseId' value='%s'>", index, row.tradeFrobjeCaseId);

                      return html+html1;
                  }
              },
              {
                  field: 'objeTradeType',
                  align: 'center',
                  title: '类别',
                  formatter: function (value, row, index) {
                      var html = $.common.sprintf("<input class='form-control' type='text' name='frobjeCaseList[%s].objeTradeType' value='%s' readonly>", index, value);
                      return html;
                  }
              },
              {
                  field: 'objeTradeDate',
                  align: 'center',
                  title: '申请日',
                  formatter: function (value, row, index) {
                      var html = $.common.sprintf("<input class='form-control ' type='test' name='frobjeCaseList[%s].objeTradeDate' value='%s' readonly>", index, value);
                      return html;
                  }
              },
              {
                  field: 'objeTradeRegdate',
                  align: 'center',
                  title: '注册日',
                  formatter: function (value, row, index) {
                      var html = $.common.sprintf("<input class='form-control ' type='test' name='frobjeCaseList[%s].objeTradeRegdate' value='%s' readonly> ", index, value);
                      return html;
                  }
              },
              {
                  field: 'closeDate',
                  align: 'center',
                  title: '应诉截止日',
                  formatter: function (value, row, index) {
                      var html = $.common.sprintf("<input class='form-control date' type='test' name='frobjeCaseList[%s].closeDate' value='%s'>", index, value);
                      return html;
                  }
              },

              ]

          };
          $.table.init(optionsH);

        }

        var trademarkName = $("#trademarkName").val();
        var trademarkRegistType = $("#trademarkRegistType").val();
        var createTime = $("#createTime").val();
        var registerDate = $("#registerDate").val();
        var trademarkRegistTypeValue = $("#trademarkRegistTypeValue").val();

        function addColumn(tableId) {
            var row = {
                orderNum: "",
                tradeFrobjeId: "",
                objeTradeName: trademarkName,
                objeTradeType:trademarkRegistTypeValue,
                objeTradeDate:createTime,
                objeTradeRegdate:registerDate,
                closeDate:"",

            };
            sub.addColumn(row, "bootstrap-tableH");
            $('#bootstrap-tableH .date').datetimepicker({
                format: 'yyyy-mm-dd',
                weekStart: 0,
                autoclose: true,
                startView: 2,
                minView: 2,
                forceParse: false,
                todayBtn: true,
                language: 'zh-CN'
            });
        }
        function doSubmit(transitionKey) {
            if ($.validate.form()) {
                $.modal.confirm("确认提交吗？", function () {
                    //$("#workFlow_transitionKey").val(transitionKey); 这个js在提交组件里加了，没有用提交组件的需要这行
                    $.operate.saveTabAlert(prefix + "/doSubmit", $('#form-tradeFrobje-edit').serialize());
                });
            }
        }
        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-tradeFrobje-edit').serialize());
            }
        }
        var id = $("#tradeRegistId").val();
        function tradeRegistList(){
            $.modal.open("商标", ctx + "kysb/tradeRegist"  + "/selectDetail/KYSBTRD01/"+id,1000,500);
        };
    </script>
</body>
</html>