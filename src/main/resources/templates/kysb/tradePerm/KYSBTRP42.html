<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('商标许可详细')" />

<th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="wrapper wrapper-content">
		<form class="form-horizontal m" id="form-demo" th:object="${tradePerm}">
			<div class="panel-group" id="accordion1" role="tablist" aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#sbxx" aria-expanded="false" class="collapsed">商标信息
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right"><i class="fa fa-chevron-down"
															aria-hidden="true"></i></span>
							</a>
						</h4>
					</div>
					<div id="sbxx" class="panel-collapse collapse in"
						 aria-expanded="false">
						<div class="form-group">
							<label class="col-sm-2 control-label ">商标列表：</label>
							<div class="col-sm-2" style="color: #1c6ac7">
								<input type="button" class="form-control" onclick="tradeRegistList()" value="点击查看">
							</div>
						</div>
					</div>
				</div>
			</div>
			<div th:if='${tradePerm.workFlow.currentActivity  eq "Manual6"
							or tradePerm.status eq "end"
							or tradePerm.workFlow.currentActivity  eq "Manual9"
							or tradePerm.workFlow.currentActivity  eq "Manual8"
							or tradePerm.workFlow.currentActivity  eq "Manual10"
							or tradePerm.workFlow.currentActivity  eq "Manual11"}'>
				<div class="panel-group" id="accordion1222" role="tablist" aria-multiselectable="true">
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#version"
								   href="#myxx" aria-expanded="false" class="collapsed">贸易信息
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<span class="pull-right"><i class="fa fa-chevron-down"
																aria-hidden="true"></i></span>
								</a>
							</h4>
						</div>
						<div id="myxx" class="panel-collapse collapse in" aria-expanded="false">
							<div class="form-group">
								<label class="col-sm-2 control-label ">点击查看：</label>
								<div class="col-sm-2" style="color: #1c6ac7">
									<input type="button" class="form-control" onclick="tradeContractList()" value="点击查看">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel-group" id="accordion" role="tablist"
				 aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right"><i class="fa fa-chevron-down"
															aria-hidden="true"></i></span>
							</a>
						</h4>
					</div>
					<div id="jbxx" class="panel-collapse collapse in"
						 aria-expanded="false">
						<div class="panel-body">
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">许可需求名称：</label>
								<div class="col-sm-10">
									<input name="prjectName" th:value="${tradePerm.prjectName}" class="form-control" type="text">
								</div>
							</div>
							<div class="row">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label">申请人：</label>
										<div class="col-sm-8">
											<input type="hidden" name ="tradeRegistId" th:value="${tradePerm.tradeRegistId}">
											<input name="applyPersonName" th:value="${tradePerm.applyPersonName}" class="form-control" type="text">
										</div>
									</div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">申请提出单位：</label>
										<div class="col-sm-8">
											<input name="permAppName" th:field="*{permAppName}" class="form-control" type="text">
										</div></div>
								</div>
							</div>
							<div class="row">
<!--								<div class="col-sm-6">-->
<!--									<div class="form-group">-->
<!--										<label class="col-sm-4 control-label is-required">申请许可的单位地址：</label>-->
<!--										<div class="col-sm-8">-->
<!--											<input name="permAppAddr" th:field="*{permAppAddr}" class="form-control" type="text">-->
<!--										</div>-->
<!--									</div>-->
<!--								</div>-->
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">法人单位：</label>
										<div class="col-sm-8">
											<input name="legalRepr" th:field="*{legalRepr}" class="form-control" type="text">
										</div> </div>
								</div>
							</div>
							<div class="row">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">单位电话：</label>
										<div class="col-sm-8">
											<input name="compTel" th:field="*{compTel}" class="form-control" type="text">
										</div> </div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label">手机：</label>
										<div class="col-sm-8">
											<input name="compPh" th:field="*{compPh}" class="form-control" type="text">
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">法人注所：</label>
										<div class="col-sm-8">
											<input name="compAddr" th:field="*{compAddr}" class="form-control" type="text">
										</div></div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">电子邮件：</label>
										<div class="col-sm-8">
											<input name="compEmail" th:field="*{compEmail}" class="form-control" type="text">
										</div>
									</div></div>
							</div>

							<div class="form-group">
								<label class="col-sm-2 control-label is-required">申请商标应用对象：</label>
								<div class="col-sm-6"
									 th:include="/kysb/tradeRegist/selectTechnicalx::init(isrequired=true,see='true',orgCodeId='permObj',type='1',selectType='M',orgNameId='permObjName',value=*{permObj})">
								</div>
							</div>

							<div class="form-group">
								<label class="col-sm-2 control-label is-required">大类产品简介：</label>
								<div class="col-sm-10">
									<textarea name="prodIntro" rows="7" cols="150" th:field="*{prodIntro}"  class="form-control" rangelength="1,500"  required="required" readonly>
									</textarea>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">申请理由：</label>
								<div class="col-sm-10">
									<textarea name="permReason" rows="7" cols="150" th:field="*{permReason}"  class="form-control" rangelength="1,500"  required="required" readonly>
									</textarea>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label ">综合意见：</label>
								<div class="col-sm-10">
								  <textarea name="opinion" rows="7" cols="150" th:field="*{opinion}"  class="form-control" rangelength="1,500"  required="required" readonly>
								  </textarea>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">相关附件：</label>
								<div class="col-sm-10"
									 th:include="/component/attachment :: init(id='permFile',see='true',name='permFile',
                        				 sourceId=*{tradePermId},sourceModule='KYSB',sourceLabel1='permFile')">
								</div>
							</div>

						</div>
					</div>
				</div>
			</div>
			<div style="height: 100px">
			</div>
		</form>
		<!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">		
			<!-- 流程跟踪 -->
			<button type="button" class="btn btn-danger" onclick="closeItem()">
				<i class="fa fa-reply-all"></i>
				关 闭
			</button>
		</div>
		<!--按钮区end-->
	</div>
	<script th:inline="javascript">
		$("#form-demo input").attr("readOnly",true);
		$("#form-demo select").attr("disabled",true);
		var id = $("[name='tradeRegistId']").val();
		function tradeRegistList(){
			$.modal.openTab("商标列表",  ctx + "kysb/tradeRegist" + "/KYSBTRD02/"+id );
		};
	</script>
</body>
</html>