<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('商标许可详细')" />

<th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="wrapper wrapper-content">
		<form class="form-horizontal m" id="form-demo" th:object="${tradePerm}">
			<div class="panel-group" id="accordion1" role="tablist" aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#sbxx" aria-expanded="false" class="collapsed">商标信息
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right"><i class="fa fa-chevron-down"
															aria-hidden="true"></i></span>
							</a>
						</h4>
					</div>
					<div id="sbxx" class="panel-collapse collapse in"
						 aria-expanded="false">
						<div class="form-group">
							<label class="col-sm-2 control-label ">商标列表：</label>
							<div class="col-sm-2" style="color: #1c6ac7">
								<input type="button" class="form-control" onclick="tradeRegistList()" value="点击查看">
							</div>
						</div>
					</div>
				</div>
			</div>
			<div th:if='${tradePerm.workFlow.currentActivity  eq "Manual6"
							or tradePerm.status eq "end"
							or tradePerm.workFlow.currentActivity  eq "Manual9"
							or tradePerm.workFlow.currentActivity  eq "Manual8"
							or tradePerm.workFlow.currentActivity  eq "Manual10"
							or tradePerm.workFlow.currentActivity  eq "Manual11"}'>
				<div class="panel-group" id="accordion1222" role="tablist" aria-multiselectable="true">
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#version"
								   href="#myxx" aria-expanded="false" class="collapsed">贸易信息
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<span class="pull-right"><i class="fa fa-chevron-down"
																aria-hidden="true"></i></span>
								</a>
							</h4>
						</div>
						<div id="myxx" class="panel-collapse collapse in" aria-expanded="false">
							<div class="form-group">
								<label class="col-sm-2 control-label ">点击查看：</label>
								<div class="col-sm-2" style="color: #1c6ac7">
									<input type="button" class="form-control" onclick="tradeContractList()" value="点击查看">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel-group" id="accordion" role="tablist"
				 aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right"><i class="fa fa-chevron-down"
															aria-hidden="true"></i></span>
							</a>
						</h4>
					</div>
					<div id="jbxx" class="panel-collapse collapse in"
						 aria-expanded="false">
						<div class="panel-body">
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">许可需求名称：</label>
								<div class="col-sm-10">
									<input name="prjectName" th:value="${tradePerm.prjectName}" class="form-control" type="text">
								</div>
							</div>
							<div class="row">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label">申请人：</label>
										<div class="col-sm-8">
											<input name="applyPersonName" th:value="${tradePerm.applyPersonName}" class="form-control" type="text">
										</div>
									</div>
								</div>
								<input type="hidden" name ="tradeRegistId" th:value="${tradePerm.tradeRegistId}">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">申请提出单位：</label>
										<div class="col-sm-8">
											<input name="permAppName" th:field="*{permAppName}" class="form-control" type="text">
										</div>
									</div>
								</div>
							</div>
							<div class="row">
<!--								<div class="col-sm-6">-->
<!--									<div class="form-group">-->
<!--										<label class="col-sm-4 control-label is-required">申请许可的单位地址：</label>-->
<!--										<div class="col-sm-8">-->
<!--											<input name="permAppAddr" th:field="*{permAppAddr}" class="form-control" type="text">-->
<!--										</div>-->
<!--									</div>-->
<!--								</div>-->
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">法人单位：</label>
										<div class="col-sm-8">
											<input name="legalRepr" th:field="*{legalRepr}" class="form-control" type="text">
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">单位电话：</label>
										<div class="col-sm-8">
											<input name="compTel" th:field="*{compTel}" class="form-control" type="text">
										</div>
									</div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label">手机：</label>
										<div class="col-sm-8">
											<input name="compPh" th:field="*{compPh}" class="form-control" type="text">
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">法人注所：</label>
										<div class="col-sm-8">
											<input name="compAddr" th:field="*{compAddr}" class="form-control" type="text">
										</div>
									</div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">电子邮件：</label>
										<div class="col-sm-8">
											<input name="compEmail" th:field="*{compEmail}" class="form-control" type="text">
										</div>
									</div>
								</div>
							</div>

							<div class="form-group">
								<label class="col-sm-2 control-label is-required">申请商标应用对象：</label>
								<div class="col-sm-6"
									 th:include="/kysb/tradeRegist/selectTechnicalx::init(isrequired=true,see='true',orgCodeId='permObj',type='1',selectType='M',orgNameId='permObjName',value=*{permObj})">
								</div>
							</div>

							<div class="form-group">
								<label class="col-sm-2 control-label is-required">大类产品简介：</label>
								<div class="col-sm-10">
									<textarea name="prodIntro" rows="7" cols="150" th:field="*{prodIntro}"  class="form-control" rangelength="1,500"  required="required" readonly>
									</textarea>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">申请理由：</label>
								<div class="col-sm-10">
									<textarea name="permReason" rows="7" cols="150" th:field="*{permReason}"  class="form-control" rangelength="1,500"  required="required" readonly>
									</textarea>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label ">综合意见：</label>
								<div class="col-sm-10">
								  <textarea name="opinion" rows="7" cols="150" th:field="*{opinion}"  class="form-control" rangelength="1,500"  required="required" readonly>
								  </textarea>
								</div>
							</div>
							<div th:if='${tradePerm.workFlow.currentActivity  ne "Manual2" and tradePerm.workFlow.currentActivity  ne "Manual3" and tradePerm.workFlow.currentActivity  ne "Manual4" or tradePerm.status eq "end"}'>
								<div class="form-group">
									<label class="col-sm-2 control-label is-required">是否需要评估：</label>
									<div class="col-sm-10">
										<th:block th:include="component/radio :: init( name='assessment', see='true',businessType='KYSB',value=*{assessment}, dictCode='jsIsChooseZj',  callback=ifJsIsChooseZj, isrequired=true)" />
									</div>
								</div>
								<div class="ifJsIsChooseZj">
									<div class="form-group">
										<label class="col-sm-2 control-label is-required">制造端评估部门：</label>
										<div class="col-sm-10">
											<th:block th:include="component/select :: init(id='zcDeptCode', see='true',name='zcDeptCode' ,businessType='KYSB', dictCode='zhizao',value=*{zcDeptCode})">
											</th:block>
										</div>
									</div>
									<div class="form-group">
										<label class="col-sm-2 control-label">市场端评估部门：</label>
										<div class="col-sm-10" style="color: #0C0C0C">
											<th:block  th:include="component/select :: init(id='scDeptCode',see='true',name='scDeptCode',isfirst='true',multimultiple='true'
                                					,businessType='KYSB', dictCode='shichangxs',value=*{scDeptCode}) ">
											</th:block>
											<!--											<input type="hidden" th:field="*{permObj}">-->
										</div>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-2 control-label  is-required">是否收费：</label>
									<div class="col-sm-10">
										<th:block th:include="component/select :: init(id='charge', name='charge' ,see='true',businessType='KYSB', dictCode='jsIsChooseZj',value=*{charge})">
										</th:block>
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">相关附件：</label>
								<div class="col-sm-10"
									 th:include="/component/attachment :: init(id='permFile',see='true',name='permFile',
                         sourceId=*{tradePermId},sourceModule='KYSB',sourceLabel1='permFile')">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div th:each="sub :${tradePerm.tradePermSubs}">
				<div class="panel-group" id="accordion122" role="tablist" aria-multiselectable="true">
					<div class="panel panel-default">
						<div class="panel-heading">
							<div th:if='${sub.deptType eq "zzglb"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#zzglb" aria-expanded="false" class="collapsed">
										制造管理部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
							<div th:if='${ sub.deptType eq "qiche"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#qiche" aria-expanded="false" class="collapsed">
										汽车板销售部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
							<div th:if='${ sub.deptType eq "lengban"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#lengban" aria-expanded="false" class="collapsed">
										冷板销售部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
							<div th:if='${ sub.deptType eq "guigang"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#guigang" aria-expanded="false" class="collapsed">
										硅钢销售部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
							<div th:if='${ sub.deptType eq "reya"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#reya" aria-expanded="false" class="collapsed">
										热轧销售部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
							<div th:if='${ sub.deptType eq "houban"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#houban" aria-expanded="false" class="collapsed">
										厚板销售部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
							<div th:if='${ sub.deptType eq "gangguan"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#gangguan" aria-expanded="false" class="collapsed">
										钢管条钢销售部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
						</div>
						<div th:id="${sub.deptType}" class="panel-collapse collapse in" aria-expanded="false">

							<div class="form-group">
								<label class="col-sm-2 control-label is-required">评估报告：</label>
								<div class="col-sm-10"
									 th:include="/component/attachment :: init(id='permSubPgFile',name='tradePermSub.permSubPgFile',see='true',
										 sourceId=${sub.tradePermSubId},sourceModule='KYSB',sourceLabel1='permSubPgFile')">
								</div>
							</div>
							<div th:if='${ sub.status eq "fp"}'>
								<div class="form-group">
									<label class="col-sm-2 control-label is-required">复评报告(如果复评需上传)：</label>
									<div class="col-sm-10"
										 th:include="/component/attachment :: init(id='permSubPgRechFile',name='tradePermSub.permSubPgRechFile',see='true',
												 sourceId=${sub.tradePermSubId},sourceModule='KYSB',sourceLabel1='permSubPgRechFile')">
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label  is-required">评估结论：</label>
								<div class="col-sm-10">
									<th:block th:include="component/select :: init(id='passOrNot',see='true',value=${sub.passOrNot}, name='tradePermSub.passOrNot',isfirst='true' ,businessType='KYSB', dictCode='passOrNot')">
									</th:block>
									<input type="hidden"  name="tradePermSub.passOrNot" th:value="${sub.passOrNot}">
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">评审意见：</label>
								<div class="col-sm-10">
									  <textarea name="tradePermSub.opinion" rows="7" cols="150" th:text="${sub.opinion}"  class="form-control" rangelength="1,250"
												readonly></textarea>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div th:if='${tradePerm.workFlow.currentActivity  eq "Manual6" or tradePerm.workFlow.currentActivity  eq "Manual9" or tradePerm.status  eq "end"
			or tradePerm.workFlow.currentActivity  eq "Manual10" or tradePerm.workFlow.currentActivity  eq "Manual11"}'>
				<div class="form-group">
					<label class="col-sm-2 control-label  is-required">评估结论：</label>
					<div class="col-sm-10">
						<th:block th:include="component/select :: init(id='passOrNot',see='true',value=${tradePerm.passOrNot}, name='passOrNot',isfirst='true' ,businessType='KYSB', dictCode='xkPassOrNot')">
						</th:block>
					</div>
				</div>
				<div class="panel panel-default scrollspy-item" id="row11">
					<div class="panel-heading" role="tab" id="headingTwo1">
						<h4 class="panel-title" toolbox-title="背景及意义">
							<a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseTwo"
							   aria-expanded="false" aria-controls="collapseTwo">
								请示报告
							</a>
							<span class="pull-right">
								 </span>
							<div class="ibox-tools">

							</div>
						</h4>
					</div>
					<div id="collapseTwo" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingTwo">
						<div class="panel-body">
							<div class="click2edit  wrapper"  id="requestReportEdits">
							</div>
						</div>
					</div>
				</div>
			</div>
			<input id="requestReport" th:value="*{requestReport}" name="requestReport" type="hidden">
			<div style="height: 100px">
			</div>
		</form>
		<!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">		
			<!-- 流程跟踪 -->
			<th:block th:include="component/wfCommentList :: init(processInstanceId=${processInstanceId})" />
			<button type="button" class="btn btn-danger" onclick="closeItem()">
				<i class="fa fa-reply-all"></i>
				关 闭
			</button>
		</div>
		<!--按钮区end-->
	</div>
	<script th:inline="javascript">
		$("#form-demo input").attr("readOnly",true);
		var id = $("[name='tradeRegistId']").val();
		function tradeRegistList(){
			$.modal.openTab("商标列表",  ctx + "kysb/tradeRegist" + "/KYSBTRD02/"+id );
		};
		var html = $("#requestReport").val();
		$("#requestReportEdits").html(html);
	</script>
</body>
</html>