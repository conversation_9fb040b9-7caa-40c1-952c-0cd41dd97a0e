<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('已处理列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse" >
		</div>
		<input type="hidden" id = "registId" th:value="${registId}" />
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
		var prefix = ctx + "kysb/tradePerm";
        var registId = $("#registId").val();
		$(function() {
			var options = {
				url: prefix + "/pageTradeSelect/"+registId,
				queryParams: queryParams,
				modalName: "商标注册-驳回复审（子）",
				columns: [{
					checkbox: true
				},
				{
					field: 'tradePermId',
					title: '主键',
					visible: false
				},
				{
					field: 'permAppName',
					title: '申请单位'
				},
				{
					field: 'legalRepr',
					title: '法定代表人'
				},
				{
					field: 'compTel',
					title: '单位电话'
				},
				{
					field: 'compPh',
					title: '手机'
				},
				{
					field: 'compEmail',
					title: '电子邮件'
				},
				{
					field: 'prodIntro',
					title: '大类产品简介'
				},
				{
					field: 'createDate',
					title: '创建时间'
				},
				{
					title: '操作',
					align: 'center',
					formatter: function(value, row, index) {
						var actions = [];
                        var param = "'" + row.tradePermId + "'";
						actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="queryYBDetail(' + param + ')"><i class="fa fa-edit"></i>查看</a> ');
						return actions.join('');
					}
				}]
			};
			$.table.init(options);
		});
        
        function queryParams(params) {
            var search = $.table.queryParams(params);
            search.businessNameLike = $("#businessNameLike").val();
            return search;
        }
		function queryYBDetail(businessId){
			var url = prefix + "/queryYBDetail/KYSBTRP42/" + businessId ;
			// 选卡页方式打开并刷新当前页 isRefresh 是否刷新
			$.modal.openTab("商标使用已处理详细", url, true);
		}
    </script>
</body>
</html>