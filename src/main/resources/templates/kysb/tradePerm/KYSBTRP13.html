<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('待办处理')" />

<th:block th:include="include :: baseJs" />
<th:block th:include="include :: summernote-css"/>
<th:block th:include="include :: summernote-js"/>
<th:block th:include="include :: jquery-tmpl"/>
<th:block th:include="include :: sub-tab-commons"/>

<th:block th:include="kczginclude :: toolbox-css" />
<th:block th:include="kczginclude :: toolbox-js" />
</head>
<body class="gray-bg">
	<div class="wrapper wrapper-content">
		<form class="form-horizontal m" id="form-tradePerm-edit" th:object="${tradePerm}">
			<div class="panel-group" id="accordion1" role="tablist" aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#sbxx" aria-expanded="false" class="collapsed">商标信息
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right"><i class="fa fa-chevron-down"
															aria-hidden="true"></i></span>
							</a>
						</h4>
					</div>
					<div id="sbxx" class="panel-collapse collapse in" aria-expanded="false">
						<div class="form-group">
							<label class="col-sm-2 control-label ">商标列表：</label>
							<div class="col-sm-2" style="color: #1c6ac7">
								<input type="button" class="form-control" onclick="tradeRegistList()" value="点击查看">
							</div>
						</div>
					</div>
				</div>
			</div>
			<div th:if='${tradePerm.workFlow.currentActivity  eq "Manual6"
							or tradePerm.workFlow.currentActivity  eq "Manual9"
							or tradePerm.workFlow.currentActivity  eq "Manual8"
							or tradePerm.workFlow.currentActivity  eq "Manual10"
							or tradePerm.workFlow.currentActivity  eq "Manual11"}'>
				<div class="panel-group" id="accordion1222" role="tablist" aria-multiselectable="true">
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#version"
								   href="#myxx" aria-expanded="false" class="collapsed">贸易信息
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<span class="pull-right"><i class="fa fa-chevron-down"
																aria-hidden="true"></i></span>
								</a>
							</h4>
						</div>
						<div id="myxx" class="panel-collapse collapse in" aria-expanded="false">
							<div class="form-group">
								<label class="col-sm-2 control-label ">点击查看：</label>
								<div class="col-sm-2" style="color: #1c6ac7">
									<input type="button" class="form-control" onclick="tradeContractList()" value="点击查看">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div th:if='${tradePerm.workFlow.currentActivity  eq "Manual11"}'>
				<div class="panel-group" id="accordion12" role="tablist" aria-multiselectable="true">
					<div class="panel panel-default">
						<div class="panel-heading">
							<h4 class="panel-title">
								<a data-toggle="collapse" data-parent="#version"
								   href="#qyxx" aria-expanded="false" class="collapsed">签约信息
									&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
									<span class="pull-right"><i class="fa fa-chevron-down"
																aria-hidden="true"></i></span>
								</a>
							</h4>
						</div>
						<div id="qyxx" class="panel-collapse collapse in"
							 aria-expanded="false">
							<div class="form-group">
								<label class="col-sm-2 control-label ">组织签约登记：</label>
								<div class="col-sm-2" style="color: #1c6ac7">
									<input type="button" class="form-control" onclick="tradeRegistState()" value="点击进行组织签约登记">
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel-group" id="accordion" role="tablist"
				 aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" data-parent="#version"
							   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
								&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
								<span class="pull-right"><i class="fa fa-chevron-down"
															aria-hidden="true"></i></span>
							</a>
						</h4>
					</div>
					<div id="jbxx" class="panel-collapse collapse in"
						 aria-expanded="false">
						<div class="panel-body">
							<input name="tradePermId" th:value="${tradePerm.tradePermId}" type="hidden">
							<input name="tradeRegistId"  th:value="${tradePerm.tradeRegistId}" type="hidden"  >
							<div class="form-group">
								<label class="col-sm-2 control-label">许可需求名称：</label>
								<div class="col-sm-10">
									<input name="prjectName" th:value="${tradePerm.prjectName}" class="form-control" type="text">
								</div>
							</div>
							<div class="row">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label">申请人：</label>
										<div class="col-sm-8">
											<input name="applyPersonName" th:value="${tradePerm.applyPersonName}" class="form-control" type="text">
										</div>
									</div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">申请提出单位：</label>
										<div class="col-sm-8">
											<input name="permAppName" th:field="*{permAppName}" class="form-control" type="text">
										</div></div>
								</div>
							</div>
							<div class="row">
<!--								<div class="col-sm-6">-->
<!--									<div class="form-group">-->
<!--										<label class="col-sm-4 control-label is-required">申请许可的单位地址：</label>-->
<!--										<div class="col-sm-8">-->
<!--											<input name="permAppAddr" th:field="*{permAppAddr}" class="form-control" type="text">-->
<!--										</div> </div>-->
<!--								</div>-->
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">法人单位：</label>
										<div class="col-sm-8">
											<input name="legalRepr" th:field="*{legalRepr}" class="form-control" type="text">
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">单位电话：</label>
										<div class="col-sm-8">
											<input name="compTel" th:field="*{compTel}" class="form-control" type="text">
										</div> </div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label">手机：</label>
										<div class="col-sm-8">
											<input name="compPh" th:field="*{compPh}" class="form-control" type="text">
										</div>
									</div>
								</div>
							</div>
							<div class="row">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">法人注所：</label>
										<div class="col-sm-8">
											<input name="compAddr" th:field="*{compAddr}" class="form-control" type="text">
										</div></div>
								</div>
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-4 control-label is-required">电子邮件：</label>
										<div class="col-sm-8">
											<input name="compEmail" th:field="*{compEmail}" class="form-control" type="text">
										</div>
									</div></div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">申请商标应用对象：</label>
<!--									<th:block  th:include="component/select :: init(id='permObj', name='permObj',isfirst='true',multimultiple='true'-->
<!--                                	,businessType='KYSB', dictCode='permObj',value=*{permObj}) ">-->
<!--									</th:block>-->
								<div class="col-sm-6"
									 th:include="/kysb/tradeRegist/selectTechnicalx::init(isrequired=true,see='true',orgCodeId='permObj',type='1',selectType='M',orgNameId='permObjName',value=*{permObj})">
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">大类产品简介：</label>
								<div class="col-sm-10">
									<textarea name="prodIntro" rows="7" cols="150" th:field="*{prodIntro}"  class="form-control" rangelength="1,500"  required="required" readonly>
									</textarea>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">申请理由：</label>
								<div class="col-sm-10">
									<textarea name="permReason" rows="7" cols="150" th:field="*{permReason}"  class="form-control" rangelength="1,500"  required="required" readonly>
									</textarea>
								</div>
							</div>
							<div th:if='${tradePerm.workFlow.currentActivity  eq "Manual2"}'>
								<div class="form-group">
									<label class="col-sm-2 control-label ">综合意见：</label>
									<div class="col-sm-10">
									  <textarea name="opinion" rows="7" cols="150" th:field="*{opinion}"  class="form-control" rangelength="1,500"  >
									  </textarea>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-2 control-label is-required">是否需要评估：</label>
									<div class="col-sm-10">
										<th:block th:include="component/radio :: init( name='assessment', businessType='KYSB',value=*{assessment}, dictCode='jsIsChooseZj',  callback=ifJsIsChooseZj, isrequired=true)" />
									</div>
								</div>
								<div class="ifJsIsChooseZj">
									<div class="form-group">
										<label class="col-sm-2 control-label is-required">制造端评估部门：</label>
										<div class="col-sm-10">
											<th:block th:include="component/select :: init(id='zcDeptCode', name='zcDeptCode' ,businessType='KYSB', dictCode='zhizao',value=*{zcDeptCode})">
											</th:block>
										</div>
									</div>
									<div class="form-group">
										<label class="col-sm-2 control-label">市场端评估部门：</label>
										<div class="col-sm-10" style="color: #0C0C0C">
											<th:block  th:include="component/select :: init(id='scDeptCode',name='scDeptCode',isfirst='true',multimultiple='true'
                                					,businessType='KYSB', dictCode='shichangxs',value=*{scDeptCode}) ">
											</th:block>
<!--											<input type="hidden" th:field="*{permObj}">-->
										</div>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-2 control-label  is-required">是否收费：</label>
									<div class="col-sm-10">
										<th:block th:include="component/select :: init(id='charge', name='charge' ,businessType='KYSB', dictCode='jsIsChooseZj',value=*{charge})">
										</th:block>
									</div>
								</div>

							</div>
							<div th:if='${tradePerm.workFlow.currentActivity  ne "Manual2" and tradePerm.workFlow.currentActivity  ne "Manual3" and tradePerm.workFlow.currentActivity  ne "Manual4" }'>
								<div class="form-group">
									<label class="col-sm-2 control-label ">综合意见：</label>
									<div class="col-sm-10">
									  <textarea name="opinion" rows="7" cols="150" th:field="*{opinion}"  class="form-control" rangelength="1,500"   readonly>
									  </textarea>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-2 control-label is-required">是否需要评估：</label>
									<div class="col-sm-10">
										<th:block th:include="component/radio :: init( name='assessment', see='true',businessType='KYSB',value=*{assessment}, dictCode='jsIsChooseZj',  callback=ifJsIsChooseZj, isrequired=true)" />
									</div>
								</div>
								<div class="ifJsIsChooseZj">
									<div class="form-group">
										<label class="col-sm-2 control-label is-required">制造端评估部门：</label>
										<div class="col-sm-10">
											<th:block th:include="component/select :: init(id='zcDeptCode', see='true',name='zcDeptCode' ,businessType='KYSB', dictCode='zhizao',value=*{zcDeptCode})">
											</th:block>
										</div>
									</div>
									<div class="form-group">
										<label class="col-sm-2 control-label">市场端评估部门：</label>
										<div class="col-sm-10" style="color: #0C0C0C">
											<th:block  th:include="component/select :: init(id='scDeptCode',see='true',name='scDeptCode',isfirst='true',multimultiple='true'
                                					,businessType='KYSB', dictCode='shichangxs',value=*{scDeptCode}) ">
											</th:block>
											<!--											<input type="hidden" th:field="*{permObj}">-->
										</div>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-2 control-label  is-required">是否收费：</label>
									<div class="col-sm-10">
										<th:block th:include="component/select :: init(id='charge', name='charge' ,see='true',businessType='KYSB', dictCode='jsIsChooseZj',value=*{charge})">
										</th:block>
									</div>
								</div>
							</div>
							<div th:if='${tradePerm.workFlow.currentActivity  eq "PERMSUB_GLY"
							or tradePerm.workFlow.currentActivity eq "PERMSUB_GLYFP"}'>
								<input type="hidden"  name="tradePermSub.deptType" th:value="${tradePerm.tradePermSub.deptType}">
								<input type="hidden" name="tradePermSub.tradePermSubId" th:value="${tradePerm.tradePermSub.tradePermSubId}">
								<div class="form-group">
									<label class="col-sm-2 control-label is-required">评估报告：</label>
									<div class="col-sm-10"
										 th:include="/component/attachment :: init(id='permSubPgFile',name='tradePermSub.permSubPgFile',
											 sourceId=*{tradePermSub.tradePermSubId},sourceModule='KYSB',sourceLabel1='permSubPgFile')">
									</div>
								</div>
								<div th:if='${ tradePerm.workFlow.currentActivity eq "PERMSUB_GLYFP"}'>
									<div class="form-group">
										<label class="col-sm-2 control-label is-required">复评报告(如果复评需上传)：</label>
										<div class="col-sm-10"
											 th:include="/component/attachment :: init(id='permSubPgRechFile',name='tradePermSub.permSubPgRechFile',
													 sourceId=*{tradePermSub.tradePermSubId},sourceModule='KYSB',sourceLabel1='permSubZltxRechFile')">
										</div>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-2 control-label  is-required">评估结论：</label>
									<div class="col-sm-10">
										<th:block th:include="component/select :: init(id='passOrNot',value=*{tradePermSub.passOrNot}, name='tradePermSub.passOrNot',isfirst='true' ,businessType='KYSB', dictCode='passOrNot')">
										</th:block>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-2 control-label is-required">评审意见：</label>
									<div class="col-sm-10">
									  <textarea name="tradePermSub.opinion" rows="7" cols="150" th:field="*{tradePermSub.opinion}"  class="form-control" rangelength="1,500"
									  ></textarea>
									</div>
								</div>
							</div>
							<div th:if='${ tradePerm.workFlow.currentActivity eq "PERMSUB_LDSH"}'>
								<input type="hidden"  name="tradePermSub.deptType" th:value="${tradePerm.tradePermSub.deptType}">
								<input type="hidden" name="tradePermSub.tradePermSubId" th:value="${tradePerm.tradePermSub.tradePermSubId}">
								<div class="form-group">
									<label class="col-sm-2 control-label is-required">评估报告：</label>
									<div class="col-sm-10"
										 th:include="/component/attachment :: init(id='permSubPgFile',name='tradePermSub.permSubPgFile',see='true',
											 sourceId=*{tradePermSub.tradePermSubId},sourceModule='KYSB',sourceLabel1='permSubPgFile')">
									</div>
								</div>
								<div th:if='${ tradePerm.tradePermSub.status eq "fp"}'>
									<div class="form-group">
										<label class="col-sm-2 control-label is-required">复评报告(如果复评需上传)：</label>
										<div class="col-sm-10"
											 th:include="/component/attachment :: init(id='permSubPgRechFile',name='tradePermSub.permSubPgRechFile',see='true',
													 sourceId=*{tradePermSub.tradePermSubId},sourceModule='KYSB',sourceLabel1='permSubPgRechFile')">
										</div>
									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-2 control-label  is-required">评估结论：</label>
									<div class="col-sm-10">
										<th:block th:include="component/select :: init(id='passOrNot',see='true',value=*{tradePermSub.passOrNot}, name='tradePermSub.passOrNot',isfirst='true' ,businessType='KYSB', dictCode='passOrNot')">
										</th:block>
										<input type="hidden"  name="tradePermSub.passOrNot" th:value="${tradePerm.tradePermSub.passOrNot}">

									</div>
								</div>
								<div class="form-group">
									<label class="col-sm-2 control-label is-required">评审意见：</label>
									<div class="col-sm-10">
									  <textarea name="tradePermSub.opinion" rows="7" cols="150" th:field="*{tradePermSub.opinion}"   class="form-control" rangelength="1,250"
												readonly ></textarea>
									</div>
								</div>
							</div>

							<div class="form-group">
								<label class="col-sm-2 control-label">相关附件：</label>
								<div class="col-sm-10"
									 th:include="/component/attachment :: init(id='permFile',see='true',name='permFile',
										 sourceId=*{tradePermId},sourceModule='KYSB',sourceLabel1='permFile')">
								</div>
							</div>

						</div>
					</div>
				</div>
			</div>
			<div th:each="sub :${tradePerm.tradePermSubs}">
				<div class="panel-group" id="accordion122" role="tablist" aria-multiselectable="true">
					<div class="panel panel-default">
						<div class="panel-heading">
							<div th:if='${sub.deptType eq "zzglb"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#zzglb" aria-expanded="false" class="collapsed">
										制造管理部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
							<div th:if='${ sub.deptType eq "qiche"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#qiche" aria-expanded="false" class="collapsed">
										汽车板销售部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
							<div th:if='${ sub.deptType eq "lengban"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#lengban" aria-expanded="false" class="collapsed">
										冷板销售部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
							<div th:if='${ sub.deptType eq "guigang"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#guigang" aria-expanded="false" class="collapsed">
										硅钢销售部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
							<div th:if='${ sub.deptType eq "reya"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#reya" aria-expanded="false" class="collapsed">
										热轧销售部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
							<div th:if='${ sub.deptType eq "houban"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#houban" aria-expanded="false" class="collapsed">
										厚板销售部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
							<div th:if='${ sub.deptType eq "gangguan"}'>
								<h4 class="panel-title">
									<a data-toggle="collapse" data-parent="#version"
									   href="#gangguan" aria-expanded="false" class="collapsed">
										钢管条钢销售部
										<span class="pull-right"><i class="fa fa-chevron-down"
																	aria-hidden="true"></i></span>
									</a>
								</h4>
							</div>
						</div>
						<div th:id="${sub.deptType}" class="panel-collapse collapse in" aria-expanded="false">
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">评估报告：</label>
								<div class="col-sm-10"
									 th:include="/component/attachment :: init(id='permSubPgFile',name='tradePermSub.permSubPgFile',see='true',
										 sourceId=${sub.tradePermSubId},sourceModule='KYSB',sourceLabel1='permSubPgFile')">
								</div>
							</div>
							<div th:if='${ sub.status eq "fp"}'>
								<div class="form-group">
									<label class="col-sm-2 control-label is-required">复评报告(如果复评需上传)：</label>
									<div class="col-sm-10"
										 th:include="/component/attachment :: init(id='permSubPgRechFile',name='tradePermSub.permSubPgRechFile',see='true',
												 sourceId=${sub.tradePermSubId},sourceModule='KYSB',sourceLabel1='permSubPgRechFile')">
									</div>
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label  is-required">评估结论：</label>
								<div class="col-sm-10">
									<th:block th:include="component/select :: init(id='passOrNot',see='true',value=${sub.passOrNot}, name='tradePermSub.passOrNot',isfirst='true' ,businessType='KYSB', dictCode='passOrNot')">
									</th:block>
									<input type="hidden"  name="tradePermSub.passOrNot" th:value="${sub.passOrNot}">
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">评审意见：</label>
								<div class="col-sm-10">
									  <textarea name="tradePermSub.opinion" rows="7" cols="150" th:text="${sub.opinion}"  class="form-control" rangelength="1,500"
												readonly></textarea>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>

			<!--请示报告-->
			<div th:if='${tradePerm.workFlow.currentActivity  eq "Manual8"}'>
				<div class="form-group">
					<label class="col-sm-2 control-label  is-required">评估结论：</label>
					<div class="col-sm-10">
						<th:block th:include="component/select :: init(id='passOrNot',value=${tradePerm.passOrNot}, name='passOrNot',isfirst='true' ,businessType='KYSB', dictCode='xkPassOrNot')">
						</th:block>
					</div>
				</div>
				<div class="panel panel-default scrollspy-item" id="row1">
					<div class="panel-heading" role="tab" id="headingTwo">
						<h4 class="panel-title" toolbox-title="请示报告">
							<a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseTwo"
							   aria-expanded="false" aria-controls="collapseTwo">
								请示报告
							</a>
							<span class="pull-right">
									 </span>
							<div class="ibox-tools">
							</div>
						</h4>
					</div>
					<div id="collapseTwo" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingTwo">
						<div class="panel-body">
							<div class="click2edit  wrapper" th:utext='*{requestReport}' id="requestReportEdit">
							</div>
						</div>
					</div>
				</div>
			</div>
			<!--使用情况概述-->
<!--			<div class="panel panel-default scrollspy-item" id="row1">-->
<!--				<div class="panel-heading" role="tab" id="headingTwo">-->
<!--					<h4 class="panel-title" toolbox-title="使用情况概述">-->
<!--						<a role="button" data-toggle="collapse" data-parent="#collapseTwo" href="#collapseTwo"-->
<!--						   aria-expanded="false" aria-controls="collapseTwo" >-->
<!--							使用情况概述<small>必填</small>-->
<!--						</a>-->
<!--						<span class="pull-right">-->
<!--                                 </span>-->
<!--						<div class="ibox-tools">-->
<!--						</div>-->
<!--					</h4>-->
<!--				</div>-->
<!--				<div id="collapseTwo" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingTwo">-->
<!--					<div class="panel-body">-->
<!--						<div class="click2edit  wrapper"  th:utext='*{tradeContinue.useSituation}'  id="requestReportEdit">-->
<!--						</div>-->
<!--					</div>-->
<!--				</div>-->
<!--			</div>-->
<!--			<input id="useSituation" th:value="${tradeContinue.useSituation}" name="useSituation" type="hidden">-->
			<input id="requestReport" th:value="${tradePerm.requestReport}" name="requestReport" type="hidden">
			<input id="extra1" th:value="${tradePerm.extra1}" name="extra1" type="hidden">
			<div class="panel-group" aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" href="#spyj" aria-expanded="false" class="collapsed">
								审批意见
								<span class="pull-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
								</span>
							</a>
						</h4>
					</div>
					<!-- name必须为 workFlow.comment -->
					<div id="spyj" class="panel-collapse collapse in" aria-expanded="false">
						<div class="panel-body">
	                        <div class="form-group col-sm-12">
	                            <textarea class="form-control" id="workFlow_comment" name="workFlow.comment" style="height: 200px; width: 100%;"
	                                      th:utext="${tradePerm.workFlow.comment}"></textarea>
	                        </div>
                        </div>
					</div>
				</div>
			</div>
			<th:block th:include="component/wfCommentList3 :: init(businessId=${tradePerm.workFlow.businessId})" />

			<!--流程相关信息-->
			<th:block th:include="component/wfWorkFlow :: init(workFlow=${tradePerm.workFlow})" />
			<!--流程相关信息-->
		</form>
		<!--按钮区-->
		<th:block th:include="kysb/tradePerm/wfDetailButton :: init(workFlow=${tradePerm.workFlow})" />
		<!--按钮区end-->
	</div>
	<script th:inline="javascript">
		$("#form-tradePerm-edit input").attr("readOnly",true);
		var prefix = ctx + "kysb/tradePerm";
		$(document).ready(function(){
			var assessment = [[${tradePerm.assessment}]];//是否专家评审
			if(!assessment || assessment == 'noNeed'){
				$('.ifJsIsChooseZj').addClass('hide');
			}
		});
		function ifJsIsChooseZj(item){
			if (item == 'need') {
				$('.ifJsIsChooseZj').removeClass('hide');
			} else {
				$('.ifJsIsChooseZj').addClass('hide');
			}
		}


		function doSubmit(transitionKey) {
			if ($.validate.form()) {
				$.modal.confirm("确认提交吗？", function () {

					if ($('#requestReportEdit').length > 0) {
						$("#requestReport").val($('#requestReportEdit').summernote('code'));
					}
					//$("#workFlow_transitionKey").val(transitionKey); 这个js在提交组件里加了，没有用提交组件的需要这行
					$.operate.saveTabAlert(prefix + "/doSubmit", $('#form-tradePerm-edit').serialize());
				});		
			}
		}
		var id = $("[name='tradeRegistId']").val();
		function tradeRegistList(){
			$.modal.openTab("商标列表",  ctx + "kysb/tradeRegist" + "/KYSBTRD02/"+id );
		};
		function tradeContractList(){
			var conurl = $("#extra1").val();
			$.modal.openTab("贸易信息", conurl);
		};
		var permid = $("[name='tradePermId']").val();
		function tradeRegistState(){
			$.modal.openTab("商标列表",  ctx + "kysb/tradeSign/newTradeSign/add/" +permid );
		};
		initEelement();
		var html = $("#requestReport").val();
		$("#requestReportEdits").html(html);
		function initEelement() {
			$('#requestReportEdit').summernote({
				lang: 'zh-CN',
				minHeight: 300,
				focus: true,
				readonly:true
			});
			$("[data-toggle=popover]").popover();

			$("#form-groupPlanApply-add").validate({
				focusCleanup: true
			});

			var otherData = null;
			ToolBox.initToolBox("catalogue",null, otherData);
		}
	</script>
	<style>
		a.a-no-btn{
			text-decoration: underline;
			color: #337ab7;
		}

		.bootstrap-table .fixed-table-container .table.top th {
			vertical-align: top;
		}

		.select2-container--bootstrap .select2-results__option[aria-selected=true] {
			background-color: #c8c8c8;
		}

		.ibox-tools a.btn {
			color: #fff;
		}
		ul.nav-pills {
			top: 60px;
			position: fixed;
		}

		.nav .nav-header {
			font-size: 18px;
			color: #92B901;
		}

		textarea.form-control {
			width: 99%;
		}

		/* 年度目标 Tab 里的样式 */
		.tabs-container .tabs-left > .nav-tabs > li.active > a, .tabs-container .tabs-left > .nav-tabs > li.active > a:hover, .tabs-container .tabs-left > .nav-tabs > li.active > a:focus {
			border-top: #e7eaec solid 1px;
			border-left: #1c6ac7 solid 4px;
		}


		.tabs-container .tabs-left > .nav-tabs .active > a, .tabs-container .tabs-left > .nav-tabs .active > a:hover, .tabs-container .tabs-left > .nav-tabs .active > a:focus {
			border-left-color: #1c6ac7 transparent #e7eaec #e7eaec;
			border-right-color: #fff;
		}

		.tabs-container .tabs-left > .nav-tabs > li.active > a, .tabs-container .tabs-left > .nav-tabs > li.active > a:hover, .tabs-container .tabs-left > .nav-tabs > li.active > a:focus {
			border-left: 4px solid #1c6ac7;
		}

		#row40 .tabs-left .tab-content .panel-body {
			width: 90%;
			margin-left: 10%;
		}


		.operation {
			position: absolute;
		}


		#collapseTwo > .panel-body {
			padding: 0px;
		}
		#collapseTwo1 > .panel-body {
			padding: 0px;
		}
		#row7 .panel-body, #row5 .panel-body {
			padding: 0px;
		}

		#row7 .panel-body .select-table, #row5 .panel-body .select-table {
			margin-top: 0px;
			border: 0px solid #ddd;
			border-radius: 0px 0px 6px 6px;
		}

		#yearTargetContent .panel-body {
			padding: 10px;
			padding-top: 0px;
		}

		#yearTargetContent .select-table {
			border: 0px;
			margin-top: 0px;
			border-radius: 0px;
			padding-top: 0px;
			box-shadow: 0px 0px 0px;
		}

		.note-editor.note-frame.panel {
			border: 0px;
			margin-bottom: 0px;
		}

		.note-toolbar.panel-heading {
			border-top: #f6f7f9 solid 1px;
			background-color: #dfebfe;
		}


	</style>
</body>
</html>