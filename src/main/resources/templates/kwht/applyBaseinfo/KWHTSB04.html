<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('对外合作_申请洽谈录入')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="kwht/kwhtInclude :: selectHzdw"/>

</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="from01" th:object="${data}">
        <!--框-->
        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#1"
                                               aria-expanded="false" class="collapsed">基本信息
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>
                <div id="1" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">流水号：</label>
                                    <div class="col-sm-6">
                                        <div class="form-control-static" th:utext="*{serialNum}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">合同名称：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="*{contructName}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label"></label>
                                    <div class="col-sm-9 form-control-static"><a
                                            style=" text-decoration: underline; color: #0000cc"
                                            th:onclick="dwhzApplyDetail()">点击此处查看申请表</a></div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="mnote-editor-title"><span class="txt-impt">*</span>申请内容：</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                                    <div class="sbrContent" th:utext="*{sbrContent}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">申请人：</label>
                                    <div class="col-sm-3">
                                        <div class="form-control-static" th:utext="*{sbrName}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">申请日期：</label>
                                    <div class="col-sm-3">
                                        <div class="form-control-static" th:utext="*{applyDate}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group"
                                     th:include="include :: initRadio(id='contructType',name='contructType',labelName='合同类型：',dictCode='KWHT_CONTRUCTTYPE',businessType='KWHT',isrequired='true',value=*{contructType},see='true')"></div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <th:block
                                            th:include="include :: initSelectBox(id='businessProperty', name='businessProperty',isrequired=true,businessType='KWHT',isfirst='true', dictCode='KWHT_BUSINESSPROPERTY',labelName='业务属性：',value=*{businessProperty},see='true')"></th:block>
                                </div>
                            </div>
                        </div>
                        <div id="source02" style="display: none">
                            <div class="row">
                                <div class="col-sm-6">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">是否比价：</label>
                                        <div class="col-sm-6">
                                            <div th:include="/component/radio :: init(id='extra1',name='extra1',businessType='KWHT',dictCode='KWHT_DEPTCHECK',value=*{extra1},callback='doDuty')"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6" style="display: none" id="zyxqDiv">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">众研需求表：</label>
                                        <div class="col-sm-3 form-control-static"><a
                                                style=" text-decoration: underline; color: #0000cc"
                                                th:onclick="viewZyxqb()">点击编辑</a></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" id="source01" style="display: none">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">选择合作单位：</label>
                                    <div class="col-sm-8">
                                        <div class="form-group"
                                             th:include="kwht/kwhtInclude :: choiceHzdw(labelClass='',labelName='',userCodeId='cooperateUnit',
									userNameId='cooperateUnitName',selectType='S',value=*{cooperateUnit})"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" id="source03" style="display: none">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">费用报价(含税)(万元)：</label>
                                    <div class="col-sm-3">
                                        <input class="form-control" name="offer" th:value="*{offer}" type="number">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-12">
                                <table class="table table-bordered table-hover table-striped" id="mytable">
                                    <thead>
                                    <tr>
                                        <th class="" style="width: 90px;" data-field="0" tabindex="0">
                                            <div class="th-inner ">
                                                <button class="btn btn-success btn-circle" type="button"
                                                        onclick="addColumns()"><i class="fa fa-plus"></i></button>
                                                <button class="btn btn-primary btn-circle" type="button"
                                                        onclick="delAllColumns()"><i class="fa fa-minus"></i>
                                                </button>
                                            </div>
                                        </th>
                                        <th style="text-align:center">机构名称</th>
                                        <th style="text-align:center"><span class="txt-impt">*</span>报价(含税)(万元)
                                        </th>
                                        <th style="text-align:center"><span class="txt-impt">*</span>洽谈是否选中
                                        </th>
                                        <th style="text-align: center;">操作</th>
                                    </tr>
                                    </thead>
                                    <tbody id="tabletBody">
                                    <tr th:data-index='${defitionStat.index}'
                                        th:each="defition:${data.applyDeptinfos}">
                                        <td class='bs-checkbox' style='width: 36px; text-align: center;'
                                            th:utext="${defitionStat.index+1}"></td>
                                        <td style='text-align: center;'>
                                            <div th:include="kwht/kwhtInclude :: choiceHzdw(labelName='',userCodeId='applyDeptinfos[' +${defitionStat.index} +'].recommonId',userNameId='applyDeptinfos[' +${defitionStat.index} +'].extra1',selectType='S',value=${defition.recommonId})"></div>
                                        </td>
                                        <td style='text-align: center;'>
                                            <input class='form-control'
                                                   th:name="'applyDeptinfos[' +${defitionStat.index} +'].offer'"
                                                   th:value="${defition.offer}" type='number'>
                                        </td>
                                        <td style='text-align: center;'>
                                            <div th:if="${#strings.isEmpty(defition.deptCheck)}">
                                                是<input type="radio"
                                                        th:name="'applyDeptinfos[' +${defitionStat.index} +'].deptCheck'"
                                                        value="1">
                                                否<input type="radio"
                                                        th:name="'applyDeptinfos[' +${defitionStat.index} +'].deptCheck'"
                                                        value="0" checked>
                                            </div>
                                            <div th:if="${defition.deptCheck} eq '1'">
                                                是<input type="radio"
                                                        th:name="'applyDeptinfos[' +${defitionStat.index} +'].deptCheck'"
                                                        value="1" checked>
                                                否<input type="radio"
                                                        th:name="'applyDeptinfos[' +${defitionStat.index} +'].deptCheck'"
                                                        value="0">
                                            </div>
                                            <div th:if="${defition.deptCheck} eq '0'">
                                                是<input type="radio"
                                                        th:name="'applyDeptinfos[' +${defitionStat.index} +'].deptCheck'"
                                                        value="1">
                                                否<input type="radio"
                                                        th:name="'applyDeptinfos[' +${defitionStat.index} +'].deptCheck'"
                                                        value="0" checked>
                                            </div>
                                        </td>
                                        <td style='text-align: center; width: 50px;'>
                                            <button class="btn btn-primary btn-circle btn-sm" type="button"
                                                    onclick="del(this)"><i class="fa fa-minus"></i></button>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="row" id="source04" style="display: none">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">相关附件(可上传明细)：</label>
                                    <div class="col-sm-8">
                                        <div class="form-group"
                                             th:include="/component/attachment :: init(name='bjxgfj',id='bjxgfj',sourceId=*{dwhzId},sourceModule='KWHT_BJXGFJ')">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group" id="source05" style="display: none">
                            <div class="mnote-editor-title"><span class="txt-impt">*</span>洽谈结果</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                            <textarea class="form-control" name="negotiaResult" id="negotiaResult" rows="5" cols="150"
                                      th:text="*{negotiaResult}"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">相关附件：</label>
                                    <div class="col-sm-8">
                                        <div class="form-group"
                                             th:include="/component/attachment :: init(name='qtxgfj',id='qtxgfj',sourceId=*{dwhzId},sourceModule='KWHT_QTXGFJ')">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">申请的相关附件：</label>
                                    <div class="col-sm-8">
                                        <div class="form-group"
                                             th:include="/component/attachment :: init(name='sbxgfj',id='sbxgfj',sourceId=*{dwhzId},sourceModule='KWHT_SBXGFJ',see='true')">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row" id="source06" style="display: none">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">参加洽谈人员：</label>
                                    <div class="col-sm-8">
                                        <div th:include="/component/selectUser :: init(userCodeId='negotiator',userNameId='negotiatorName',value=*{negotiator},selectType='M')"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <input name="dwhzId" id='dwhzId' th:value="${data.dwhzId}" type="hidden">
        <input name="taskId" type="hidden" th:value="${taskId}">
        <input name="activityCode" type="hidden" th:value="${activityCode}">
        <input name="processInstanceId" type="hidden" th:value="${processInstanceId}">
        <input name="businessGuid" type="hidden" th:value="${businessGuid}">
        <input name="comment" id="comment" type="hidden" value=".">
        <!--审批历史-->
        <p th:if="${processInstanceId}">
            <th:block th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})"></th:block>
        </p>
    </form>
</div>
<th:block
        th:include="kwht/kwhtInclude :: button(zcId='true',wfOneId='1',retId='1',id='from01',returnUrl='kwht/recommonBaseinfo/returnWF',submitUrl='kwht/applyBaseinfo/submitWF',doSaveUrl='kwht/applyBaseinfo/doSave',processInstanceId=${processInstanceId})"/>
<script th:inline="javascript">

    //是否开口合同
    doShow();

    function doShow() {
        var contructType = [[${data.contructType}]];
        if ($.common.isNotEmpty(contructType)) {
            //开口合同
            if ('1' == contructType) {
                $("#source01").show();
                $("#source03").show();
                $("#source05").show();
                $("#source06").show();
                $("#source02").hide();
            } else if ('2' == contructType) {
                //非开口合同
                doFybj();
            }
        }
    }

    //费用报价大于30W显示是否招投标
    function doFybj() {
        var costEsti = [[${data.costEsti}]];
        var costEstiNumber = Number.parseFloat(costEsti);
        var number = Number.parseFloat("30");
        if (costEstiNumber >= number) {
            $("#source02").show();
            $("input:radio[name='extra1']").attr("required", "");
        } else {
            $("#source01").show();
            $("#source03").show();
            $("#source04").show();
            $("#source05").show();
            $("#source06").show();
        }
    }

    var dwhzId = [[${data.dwhzId}]];
    /*项目申报详情页面*/
    function dwhzApplyDetail() {
        $.modal.openTab('项目申请表', ctx + 'kwht/applyBaseinfo/queryDT?dwhzId=' + dwhzId + "&type=sb", false)
    }

    //编辑众研需求表
    function viewZyxqb() {
        $.modal.openTab("编辑众研需求表", ctx + 'kwht/zyxqb/add?dwhzId=' + dwhzId);
    }

    /****
     * 从ehr选择单位
     * @param val
     */
    function chooerOrg(val) {
        var ownerName = "applyDeptinfos-" + val + "-extra1";
        var ownerGh = "applyDeptinfos-" + val + "-recommonId";
        choiceHzdw(ownerGh, ownerName, "S", null, null);
    }

    function addColumns() {
        var len = $("#tabletBody").find("tr").length;
        var length = len + 1;
        var extra2 = "是<input type=\"radio\" name='applyDeptinfos[" + len + "].deptCheck'  value='1'> 否<input type=\"radio\" name='applyDeptinfos[" + len + "].deptCheck'  value='0' checked>";
        var tr = "<tr data-index='" + len + "'>";
        tr = tr + "<td style='width: 10px; text-align: center;'>" + length + "</td>";
        tr = tr + "<td style='text-align: center;'><label class=\"col-sm-3 control-label\"></label><div class=\"col-sm-8\"><div class=\"input-group\"><input class='form-control' id='applyDeptinfos-" + len + "-extra1'  onclick='chooerOrg(" + len + ")' name='applyDeptinfos[" + len + "].extra1' type=\"text\" ><input id='applyDeptinfos-" + len + "-recommonId'  name='applyDeptinfos[" + len + "].recommonId' type=\"hidden\" ><span class=\"input-group-addon detailOrgOrUser\"><i class=\"fa fa-search \"></i></span></div></div></td>";
        tr = tr + "<td style='text-align: center;'><input type='text' class='form-control' name='applyDeptinfos[" + len + "].offer' ></td>";
        tr = tr + "<td style='text-align: center;'>" + extra2 + "</td>";
        tr = tr + "<td style='text-align: center; width: 50px;'> <button class='btn btn-primary btn-circle btn-sm' type='button' onclick='del(this)'><i class='fa fa-minus'></i></button></td>";
        tr = tr + "</tr>";
        $("#tabletBody").append(tr);
    }

    //删除技术指标-全部
    function delAllColumns() {
        $.modal.confirm("确认删除所有行吗?", function () {
            $('#mytable tbody').children().remove();
        });
    }

    //obj是点击行的this
    function del(obj) {
        //通过this找到父级元素节点
        var tr = obj.parentNode.parentNode;
        //找到表格
        var tbody = tr.parentNode;
        //删除行
        tbody.removeChild(tr);
        $('#tabletBody').find("tr").each(function (index, ele) {
            let indexs = $(this).find("td").eq(0).html();
            $(this).find("td").eq(0).html(index + 1);
        })
    }

    function doDuty(value) {
        console.log(value);
        if ('1' == value) {
            $('#zyxqDiv').show();
            $('#mytable').hide();
            $('#source04').hide();
            $('#source05').hide();
            $('#source06').hide();
        } else if ('0' == value){
            $('#zyxqDiv').hide();
            $('#mytable').show();
            $('#source04').show();
            $('#source05').show();
            $('#source06').show();
        }
    }

    f();
    function f() {
        var extra1 = [[${data.extra1}]];
        if ('1' == extra1) {
            $('#zyxqDiv').show();
            $('#mytable').hide();
        } else if ('0' == extra1){
            $('#mytable').show();
            $('#source04').show();
            $('#source05').show();
            $('#source06').show();
        }
    }

</script>
</body>
</html>