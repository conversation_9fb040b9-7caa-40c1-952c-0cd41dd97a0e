<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('对外合作_机构推荐')" />
	<th:block th:include="include :: baseJs" />
	<th:block th:include="include :: summernote-css"/>
	<th:block th:include="include :: sendFile"/>
	<th:block th:include="include :: summernote-js"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
	<form class="form-horizontal m" id="from01" th:object="${data}">
		<!--框-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#1" aria-expanded="false" class="collapsed">基本信息
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<div id="1" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">中文全称：</label>
									<div class="col-sm-9">
										<input class="form-control" name="chnFullname" th:value="*{chnFullname}"
											   type="text" required>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">中文简称：</label>
									<div class="col-sm-9">
										<input class="form-control" name="chnName" th:value="*{chnName}"
											   type="text">
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">英文全称：</label>
									<div class="col-sm-9">
										<input class="form-control" name="engFullname" th:value="*{engFullname}"
											   type="text">
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">英文简称：</label>
									<div class="col-sm-9">
										<input class="form-control" name="engName" th:value="*{engName}"
											   type="text">
									</div>
								</div>
							</div>
						</div>
						<fieldset id="global_location">
							<section>
								<div class="row">
									<div class="col-sm-6">
										<div class="form-group">
											<label class="col-sm-3 control-label is-required">国别：</label>
											<div class="col-sm-3">
												<select name="country" class="form-control country" required>
													<option if="${data.country}" th:value="${data.country}" text="${data.country}"></option>
												</select>
											</div>
										</div>
									</div>
									<div class="col-sm-6">
										<div class="form-group">
											<label class="col-sm-3 control-label is-required">省份：</label>
											<div class="col-sm-3">
												<select name="province" class="form-control state" data-required="true" required>
													<option if="${data.province}" th:value="${data.province}" text="${data.province}"></option>
												</select>
											</div>
										</div>
									</div>
								</div>
								<div class="row">
									<div class="col-sm-6">
										<div class="form-group">
											<label class="col-sm-3 control-label is-required">城市：</label>
											<div class="col-sm-3">
												<select name="city" class="form-control city" data-required="true">
													<option if="${data.city}" th:value="${data.city}" text="${data.city}"></option>
												</select>
											</div>
										</div>
									</div>
								</div>
							</section>
						</fieldset>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">公司地址：</label>
									<div class="col-sm-9">
										<input class="form-control" name="address" th:value="*{address}"
											   type="text">
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">邮政编码：</label>
									<div class="col-sm-3">
										<input class="form-control" name="postCode" th:value="*{postCode}"
											   type="text">
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">主页：</label>
									<div class="col-sm-6">
										<input class="form-control" name="webPage" th:value="*{webPage}"
											   type="text">
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">组织机构代码：</label>
									<div class="col-sm-6">
										<input class="form-control" name="deptCode" th:value="*{deptCode}"
											   type="text">
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">法人代表：</label>
									<div class="col-sm-9">
										<input class="form-control" name="legalRepresent" th:value="*{legalRepresent}"
											   type="text" required>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">委托法人：</label>
									<div class="col-sm-9">
										<input class="form-control" name="entrustLegal" th:value="*{entrustLegal}"
											   type="text">
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<th:block
											th:include="include :: initSelectBox(id='enterpriseNature', name='enterpriseNature',businessType='KWHT',isfirst='true', dictCode='enterpriseNature',labelName='企业性质：',value=*{enterpriseNature})"></th:block>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<th:block
											th:include="include :: initSelectBox(id='industry', name='industry',businessType='KWHT',isfirst='true', dictCode='industry',labelName='所属行业：',value=*{industry})"></th:block>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">统一社会信用代码号：</label>
									<div class="col-sm-9">
										<input class="form-control" name="creditCode" th:value="*{creditCode}"
											   type="text" required>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">税号：</label>
									<div class="col-sm-9">
										<input class="form-control" name="shuiCode" th:value="*{shuiCode}"type="text" required>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group"
									 th:include="include :: initDate(id='validStart',name='validStart',labelName='有效开始日期：',isrequired='true',strValue=*{validStart})"></div>
							</div>
							<div class="col-sm-6">
								<div class="form-group"
									 th:include="include :: initDate(id='validEnd',name='validEnd',labelName='有效终止日期：',isrequired='true',strValue=*{validEnd})"></div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">相关资质证书：</label>
									<div class="col-sm-6">
										<input class="form-control" name="qualiCert" th:value="*{qualiCert}"
											   type="text">
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">注册资金(万元)：</label>
									<div class="col-sm-6">
										<input class="form-control" name="registerCapital" th:value="*{registerCapital}"
											   type="number">
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">账号：</label>
									<div class="col-sm-6">
										<input class="form-control" name="account" th:value="*{account}"
											   type="text" required>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">开户行：</label>
									<div class="col-sm-6">
										<input class="form-control" name="openBank" th:value="*{openBank}"
											   type="text" required>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">联系人：</label>
									<div class="col-sm-6">
										<input class="form-control" name="contactPerson" th:value="*{contactPerson}"
											   type="text" required>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">联系电话：</label>
									<div class="col-sm-6">
										<input class="form-control" name="contactPhone" th:value="*{contactPhone}"
											   type="text">
									</div>
								</div>
							</div>
						</div>
						<div class="form-group">
							<div class="mnote-editor-title"><span class="txt-impt"></span>本单位特点及技术优势</div>
							<div class="mnote-editor-box">
								<div class="col-sm-12">
									<textarea class="form-control" name="technicalAdvant" rows="5" cols="150" th:text="*{technicalAdvant}"></textarea>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">三证合一证书：</label>
									<div class="col-sm-8">
										<div class="form-group"
											 th:include="/component/attachment :: init(name='szhyzs',id='szhyzs',sourceId=*{recommonId},sourceModule='KWHT_SZHYZS',isrequired='true')"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">相关资质证书：</label>
									<div class="col-sm-8">
										<div class="form-group" th:include="/component/attachment :: init(name='xgzzzs',id='xgzzzs',sourceId=*{recommonId},sourceModule='KWHT_XGZZZS')"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">其他证明：</label>
									<div class="col-sm-8">
										<div class="form-group" th:include="/component/attachment :: init(name='qtzm',id='qtzm',sourceId=*{recommonId},sourceModule='KWHT_QTZM')"></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<input class="form-control" name="recommonId" id='recommonId' th:value="*{recommonId}" type="hidden">
		<input name="taskId" type="hidden" th:value="${taskId}">
		<input name="activityCode" type="hidden" th:value="${activityCode}">
		<input name="processInstanceId" type="hidden" th:value="${processInstanceId}">
		<input name="businessGuid" type="hidden" th:value="${businessGuid}">
		<!--审批历史-->
		<p th:if="${processInstanceId}">
			<th:block th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})"></th:block>
		</p>
	</form>
</div>
<th:block th:include="kwht/kwhtInclude :: button(zcId='true',tjId='true',id='from01',submitUrl='kwht/recommonBaseinfo/startWF',doSaveUrl='kwht/recommonBaseinfo/doSave',processInstanceId=${processInstanceId})"/>
<script type="text/javascript" th:src="@{/kwzl/js/jquery.cxselect.js}"></script>
<script type="text/javascript" th:src="@{/kwht/globalData.min.json}"></script>
<script th:inline="javascript">
	var urlGlobal = [[@{/kwht/globalData.min.json}]];
	// 全球常见国家城市联动
	$('#global_location').cxSelect({
		url: urlGlobal,
		selects: ['country', 'state', 'city'],
		emptyStyle: 'none'
	});

</script>
</body>
</html>