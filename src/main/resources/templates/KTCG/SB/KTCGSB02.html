<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('零星技术采购申报')" />
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <link th:href="@{/css/treebutton.css}" rel="stylesheet"/>
    <th:block th:include="include :: sendFile"/>
    <th:block th:include="include :: summernote-js"/>
    <th:block th:include="include :: ycl-js"/>

</head>

<body class="gray-bg" th:with="re=${ei.blocks.r.rows[0]},i=${ei.blocks.i.rows[0]},tn=${ei.blocks.r.rows[0].tn}">
<!-- <div class="kjtree-button2"><a href="javascript:void(0);" th:onclick="queryKYXMLX22()"><div class=""><img class="kjtree-img" width="41" height="42"  alt=""/><br/>项目树</div></a></div>	 -->
<div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="KTCGSB02Form">
        	<input name="businessGuid" id="businessGuid" th:value="${re.businessGuid}" type="hidden">
        	<input name="needId" id="needId" th:value="${re.tn.needId}" type="hidden">
        	<input name="activityCode" id="activityCode" th:value="${i.activityCode}" type="hidden">
        	<input name="taskId" id="taskId" th:value="${i.taskId}" type="hidden">
        	<div class="panel-group" role="tablist" aria-multiselectable="true">
        		<div class="panel panel-default">
        			<!--1.o头部标签-->
        			<div class="panel-heading">
						<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
						</h4>
					</div>
					<!--折叠区域-->
					<div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
						<div class="panel-body">
							<div class="row col-sm-12">
								<<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">项目名称:</label>
									<div class="col-sm-8">
										<input name="projectName" id="projectName" class="form-control" th:value="${tn.projectName}" required/>
									</div>
								</div>
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">项目编号:</label>
									<div class="col-sm-8">
										<div class="form-control-static" th:utext="自动生成"></div>
									</div>
								</div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<input name="tcrUserCode" id="tcrUserCode" th:value="${tn.tcrUserCode}" type="hidden">
									<input name="tcrUserName" id="tcrUserName" th:value="${tn.tcrUserName}" type="hidden">
									<label class="col-sm-4 control-label is-required">提出人:</label>
									<div class="col-sm-8">
										<div class="form-control-static"  th:utext="${tn.tcrUserName}"></div>
									</div>
								</div>
								<div class="col-sm-12 form-group">
									<input name="deptCode" id="deptCode" th:value="${tn.deptCode}" type="hidden">
									<input name="deptCodePath" id="deptCodePath" th:value="${tn.deptCodePath}" type="hidden">
									<input name="deptName" id="deptName" th:value="${tn.deptName}" type="hidden">
									<label class="col-sm-2 control-label is-required">所在单位部门:</label>
									<div class="col-sm-10">
										<div class="form-control-static"  th:utext="${tn.deptName}"></div>
									</div>
		                        </div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">联系电话:</label>
									<div class="col-sm-8">
										<input name="tcrContactTel" class="form-control" th:value="${tn.tcrContactTel}" type="text" required="required">
									</div>
								</div>
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">采购类别:</label>
									<div class="form-group col-sm-8" th:include="component/select:: init(id='needType',name='needType',dictCode='need_type',businessType='KTCG',
									isrequired='true',isfirst='true',value=${tn.needType})"></div> 
								</div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">项目参加部门:</label>
									<div class="col-sm-8">
										<th:block th:include="component/selectOrg :: init(orgCodeId='xmcjdeptCode',orgNameId='xmcjdeptName',isrequired='true', selectType='S',value=${tn.xmcjdeptCode})"></th:block>
									</div>
								</div>
							</div>
						</div>
					</div>
					<!--折叠区域结束-->
        		</div>
        	</div>
        	<!--  如果是普通科研产品-->
        	<div class="panel-group" role="tablist" aria-multiselectable="true" >
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#wtsm" aria-expanded="false" class="collapsed">
							<span style="color: red">*</span>通过引进需要解决主要问题说明:
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
						</h4>
					</div>
					<!--折叠区域-->
					<div id="wtsm" class="panel-collapse collapse in">
						<div class="panel-body">
							<div class="form-group" >
								<div class="mnote-editor-box">
									<div th:include="component/richText::init(id='xyjjwtsm_ktcg_1',name='xyjjwtsm_ktcg_1',value=${tn.xyjjwtsm_ktcg_1},isrequired=true)"></div>
								</div>
	                        </div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel-group" role="tablist" aria-multiselectable="true" >
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#ssxg" aria-expanded="false" class="collapsed">
							<span style="color: red">*</span>预计引进实施后效果（包括技术指标/效益等）:
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
						</h4>
					</div>
					<!--折叠区域-->
					<div id="ssxg" class="panel-collapse collapse in">
						<div class="panel-body">
							<div class="form-group" >
								<div class="mnote-editor-box">
									<div th:include="component/richText::init(id='sshxg_ktcg_2',name='sshxg_ktcg_2',value=${tn.sshxg_ktcg_2},isrequired='true')"></div>
								</div>
	                        </div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel-group" role="tablist" aria-multiselectable="true" >
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#yjfy" aria-expanded="false" class="collapsed">
							<span style="color: red">*</span>预计引进费用（万元）（包括：知识产权费用、差旅费、材料费、资料费等）:
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
						</h4>
					</div>
					<!--折叠区域-->
					<div id="yjfy" class="panel-collapse collapse in">
						<div class="panel-body">
							<div class="form-group" >
								<div class="mnote-editor-box">
									<div th:include="component/richText::init(id='yjfy_ktcg_3',name='yjfy_ktcg_3',value=${tn.yjfy_ktcg_3},isrequired='true')"></div>
								</div>
	                        </div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel-group" role="tablist" aria-multiselectable="true" >
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jlqk" aria-expanded="false" class="collapsed">
							交流情况:
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
						</h4>
					</div>
					<!--折叠区域-->
					<div id="jlqk" class="panel-collapse collapse in">
						<div class="panel-body">
							<div class="form-group" >
								<div class="mnote-editor-box">
									<div th:include="component/richText::init(id='jlqk_ktcg_4',name='jlqk_ktcg_4',value=${tn.jlqk_ktcg_4})"></div>
								</div>
	                        </div>
						</div>
					</div>
				</div>
			</div>
        	<div class="panel-group" role="tablist" aria-multiselectable="true" >
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#xmms" aria-expanded="false" class="collapsed">
							完成时间和引进方:
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
						</h4>
					</div>
					<!--折叠区域-->
					<div id="xmms" class="panel-collapse collapse in">
						<div class="panel-body">
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">建议完成时间:</label>
									<div class="col-sm-8" th:include="component/date :: init(id='needFinishDate',name='needFinishDate',strValue=${re.tn.needFinishDate},isrequired=true)"></div>
								</div>
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">拟引进方:</label>
									<div class="col-sm-8">
										<input name="needYjf" id="needYjf" class="form-control" th:value="${tn.needYjf}" required/>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
			<!--1.0-->
			<div class="panel-group" role="tablist" aria-multiselectable="true">
        		<div class="panel panel-default">
        			<!--5.0头部标签-->
        			<div class="panel-heading">
						<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#fj" aria-expanded="false" class="collapsed">相关附件
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
						</h4>
					</div>
					<div id="fj" class="panel-collapse collapse in">
						<div class="panel-body">
							<div class="mnote-editor-box">
								<div class="row  col-sm-12 form-group">
				                     <div th:include="/component/attachment :: init(id='attachmentId14',name='attachmentId14',sourceId=${re.tn.needId},sourceModule='14',isrequired=true)"></div>
				                </div>
							</div>
		               </div>
					</div>
				</div>
			</div>
        </form>
        <div class="row" style="padding: 15px 0;">
			<div class="toolbar toolbar-bottom" role="toolbar">
				<button type="button" class="btn btn-primary" onclick="doSave(1)"><i class="fa fa-check"></i>暂存</button>
				&nbsp;
				<th:block th:include="component/wfSubmitOne:: init(taskId=${i.taskId},callback=doSubmit)"/>
				&nbsp;
				<th:block th:include="component/wfReturn:: init(taskId=${i.taskId},callback=doRuturn)"/>
				&nbsp;
				<button type="button" class="btn btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>
			</div>
		</div>
		<!--审批历史-->
		<!-- <div class="row" style="padding: 15px 0;" th:if="${i.processInstanceId !='' && i.processInstanceId != null}">
	        <th:block 
	                  th:include="include :: includeTaskHistoryList(instanceId=${i.processInstanceId})"></th:block>
        </div> -->
    </div>
        
    <script th:inline="javascript">
    $("#KTCGSB02Form").validate({
        focusCleanup: true,
        ignore: "" 
    });
	
	function doSave(v){
		$.operate.saveModal(ctx+"c/s?serviceName=KTCGSB01&methodName=doSave&pageNo="+[[${ei.attr.pageNo}]]+"&c=", $('#KTCGSB02Form').serialize());
    }
	//半小时，自动保存
	setInterval("doSave('1')", 1000*60*30);
	function doSubmit(transitionKey){
		if (!$.validate.form()){
			return false;
		}
		/* //意见框必填
		var comment = $("#comment").val();
		if(comment==null || comment ==""){
			$.modal.alertSuccess("意见框必填");
			return false;
		} */
	 	var url = ctx+"c/s?serviceName=KTCGSB01&methodName=doSubmit&pageNo="+[[${ei.attr.pageNo}]]+"&transitionKey="+transitionKey;
	 	submitForm(url,"KTCGSB02Form");
	}
	function doRuturn(activityKey){
		var url = ctx+"c/s?serviceName=KYXMLX01&methodName=doReturn&pageNo="+[[${ei.attr.pageNo}]]+"&activityKey="+activityKey;
		submitForm(url,"KTCGSB02Form");
	}

	

	function addMonth(date, offset) {
	    if (date instanceof Date && !isNaN(offset)) {
	        let givenMonth = date.getMonth();
	        let newMonth = givenMonth + offset;
	        date.setMonth(newMonth);
	        return dateToString(date);
	    }
	    throw Error('argument type error');
	}
	
	function getNextDay(d){
	    d = new Date(d);
	    d = +d + 1000*60*60*24;
	    d = new Date(d);
	    //return d;
	    //格式化
	    return d.getFullYear()+"-"+(d.getMonth()+1)+"-"+d.getDate();
	}
	
   function dateToString(date){ 
		  var year = date.getFullYear(); 
		  var month =(date.getMonth() + 1).toString(); 
		  var day = (date.getDate()).toString();  
		  if (month.length == 1) { 
		      month = "0" + month; 
		  } 
		  if (day.length == 1) { 
		      day = "0" + day; 
		  }
		  var dateTime = year + "-" + month + "-" + day;
		  return dateTime; 

   }

   function queryKYXMLX22(projectGuid){
	   var projectGuid = $("#projectGuid").val();
		var url = ctx+"c/l?serviceName=KYXMLX02&methodName=query&pageNo=KYXMLX38&rk=init&projectGuid="+projectGuid;
		var title = "项目树";
		
		// 选卡页方式打开并刷新当前页 isRefresh 是否刷新
		$.modal.openTab(title, url, true);
   }

	
</script>
</body>
</html>