<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('付款申请部门领导/项目主管/业务主管/主管部门领导审批')" />
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <link th:href="@{/css/treebutton.css}" rel="stylesheet"/>
    <th:block th:include="include :: sendFile"/>
    <th:block th:include="include :: summernote-js"/>
    <th:block th:include="include :: ycl-js"/>

</head>

<body class="gray-bg" th:with="re=${ei.blocks.r.rows[0]},i=${ei.blocks.i.rows[0]},tn=${ei.blocks.r.rows[0].tn},tp=${ei.blocks.r.rows[0].tp}">
<!-- <div class="kjtree-button2"><a href="javascript:void(0);" th:onclick="queryKYXMLX22()"><div class=""><img class="kjtree-img" width="41" height="42"  alt=""/><br/>项目树</div></a></div>	 -->
<div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="KTCGFK03Form">
        	<input name="businessGuid" id="businessGuid" th:value="${re.businessGuid}" type="hidden">
        	<input name="needId" id="needId" th:value="${re.tn.needId}" type="hidden">
        	<input name="activityCode" id="activityCode" th:value="${i.activityCode}" type="hidden">
        	<input name="taskId" id="taskId" th:value="${i.taskId}" type="hidden">
        	<div class="panel-group" role="tablist" aria-multiselectable="true">
        		<div class="panel panel-default">
        			<!--1.o头部标签-->
        			<div class="panel-heading">
						<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
						</h4>
					</div>
					<!--折叠区域-->
					<div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
						<div class="panel-body">
							<div class="row col-sm-12">
								<<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">项目名称:</label>
									<div class="col-sm-8">
										<div class="form-control-static" th:utext="${tn.projectName}"></div>
									</div>
								</div>
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">项目编号:</label>
									<div class="col-sm-8">
										<div class="form-control-static" th:utext="${tn.projectNum}"></div>
									</div>
								</div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">提出人:</label>
									<div class="col-sm-8">
										<div class="form-control-static"  th:utext="${tn.tcrUserName}"></div>
									</div>
								</div>
								<div class="col-sm-12 form-group">
									<label class="col-sm-2 control-label is-required">所在单位部门:</label>
									<div class="col-sm-10">
										<div class="form-control-static"  th:utext="${tn.deptName}"></div>
									</div>
		                        </div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">采购类别:</label>
									<div class=" col-sm-8" >
										<div th:include="component/select:: init(see='true',dictCode='need_type',businessType='KTCG',
										isrequired='true',isfirst='true',value=${tn.needType})"></div>
									</div> 
								</div>
							</div>
							
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">项目负责部门:</label>
									<div class="col-sm-8">
										<th:block th:include="component/selectOrg :: init(see='true', selectType='S',value=${tn.fzDeptCode})"></th:block>
									</div>
								</div>
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">项目负责人:</label>
									<div class="col-sm-8">
										<div class="form-control-static"  th:utext="${tn.fzrName}"></div>
									</div>
								</div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">项目参加部门:</label>
									<div class="col-sm-8">
										<th:block th:include="component/selectOrg :: init(see='true', selectType='S',value=${tn.xmcjdeptCode})"></th:block>
									</div>
								</div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">引进方:</label>
									<div class="col-sm-8">
										<div class="form-control-static" th:utext="${tn.needYjf}"></div>
									</div>
								</div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">有效期限开始日期:</label>
									<div class="col-sm-8">
										<div th:include="component/date :: init(see='true',labelName='项目开始时间:',isrequired='true',strValue=${re.tc.htBeginDate}) "></div>
									</div>
								</div>
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">有效期限结束日期:</label>
									<div class="col-sm-8">
										<div th:include="component/date :: init(see='true',labelName='项目结束时间:',isrequired='true',strValue=${re.tc.htEndDate}) "></div>
									</div>
								</div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">定价模式:</label>
									<div class="col-sm-8" >
										<div class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ty.utils.SDictUtil).getDictName('KTCG','ht_model',re.tc.htModel)}"></div>
									</div>
								</div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">币种:</label>
									<div class="col-sm-8" >
										<div class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ty.utils.SDictUtil).getDictName('KYGJ','currency',re.tc.htCny)}"></div>
									</div>
								</div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group" id="htTotal_div" th:style="'display: ' + @{(${!#strings.isEmpty(re.tc.htModel) && #strings.contains(re.tc.htModel, 'gdjg')} ? 'block' : 'none')} + ';'">
									<label class="col-sm-4 control-label is-required">预计合同额(万元):</label>
									<div class="col-sm-8">
										<div class="form-control-static" th:utext="${@ui.numberFormat(re.tc.htTotal)}"></div>
									</div>
								</div>
								<div class="col-sm-6 form-group" id="htCnyTotal_div" th:style="'display: ' + @{(${!#strings.isEmpty(re.tc.htCny) && !#strings.contains(re.tc.htCny, 'CNY')} ? 'block' : 'none')} + ';'">
									<label class="col-sm-4 control-label is-required">折合人民币(万元):</label>
									<div class="col-sm-8">
										<div class="form-control-static" th:utext="${@ui.numberFormat(re.tc.htCnyTotal)}"></div>
									</div>
								</div>
							</div>
							<div class="row col-sm-12" >
								<div class="col-sm-6 form-group" id="htPrice_div" th:style="'display: ' + @{(${!#strings.isEmpty(re.tc.htModel) && #strings.contains(re.tc.htModel, 'gdjg')} ? 'block' : 'none')} + ';'">
									<label class="col-sm-4 control-label is-required">价格幅度(万元):</label>
									<div class="col-sm-8">
										<div class="form-control-static" th:utext="${@ui.numberFormat(re.tc.htPrice)}"></div>
									</div>
								</div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">合同账号:</label>
									<div class="col-sm-8">
										<div class="form-control-static" th:utext="${re.tp.payHtbh}"></div>
									</div>
								</div>
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">开户行:</label>
									<div class="col-sm-8">
										<div class="form-control-static" th:utext="${re.tp.payCustomBank}"></div>
									</div>
								</div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">受款单位:</label>
									<div class=" col-sm-8" >
										<div th:include="component/select:: init(see='true',dictCode='SKDW',businessType='KTCG',
									isrequired='true',isfirst='true',value=${tp.paySkdw})"></div>
									</div> 
								</div>
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">申请日期:</label>
									<div class="col-sm-8" th:include="component/date :: init(see='true',strValue=${tp.payDate}) "></div>
								</div>
							</div>
							<div class="row col-sm-12">
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">受款单位税号:</label>
									<div class="col-sm-8">
										<div class="form-control-static" th:utext="${re.tp.payDwsh}"></div>
									</div>
								</div>
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">是否尾款:</label>
									<div class="col-sm-8" th:include="component/radio :: init(see='true',value=${tp.payIswk},dictCode='is_yes_no',businessType='MPTY') "></div>
								</div>
							</div>
						</div>
					</div>
					<!--折叠区域结束-->
        		</div>
        	</div>
        	<!--  如果是普通科研产品-->
        	<div class="panel-group" role="tablist" aria-multiselectable="true" >
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#wtsm" aria-expanded="false" class="collapsed">
							发票信息:
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
						</h4>
					</div>
					<!--折叠区域-->
					<div id="wtsm" class="panel-collapse collapse in">
						<div class="panel-body">
							<div class="form-group" >
								<div class="mnote-editor-box">
									<table class="table table-bordered table-hover table-striped" id="fptable" >
	                                    <thead>
		                                    <tr>
		                                        <th style="text-align:center" width="10%">序号</th>
		                                        <th style="text-align:center">发票类型</th>
		                                        <th style="text-align:center">报支细类</th>
		                                        <th style="text-align:center">发票号码</th>
		                                        <th style="text-align:center">发票金额(万元)(精确到分)</th>
		                                        <th style="text-align:center">开票日期</th>
		                                        <th style="text-align:center">税率/税额(万元)(精确到分)</th>
		                                        <th style="text-align:center">发票代码(10/12位)</th>
		                                    </tr>
		                                </thead>
		                                <tbody id="tbody_jsfzqs">
				                    		<tr th:each="cy,iterStat : ${re.pfList}">
					                    		<td align="center" th:text="${cy.xh}"></td>
					                    		<td align="center" th:text="${T(com.baosight.bscdkj.mp.ty.utils.SDictUtil).getDictName('KYWX','billType',cy.fbinfoType)}"></td>
					                    		<td align="center" th:text="${T(com.baosight.bscdkj.mp.ty.utils.SDictUtil).getDictName('KYWX','reportType',cy.fbinfoKind)}"></td>
					                    		<td align="center" th:text="${cy.fbinfoNum}"></td>
					                    		<td align="center" th:text="${@ui.numberFormat(cy.fbinfoTotal)}"></td>
					                    		<td align="center" th:text="${cy.fbinfoDate}"></td>
					                    		<td align="center" th:text="${T(com.baosight.bscdkj.mp.ty.utils.SDictUtil).getDictName('KYWX','taxRate',cy.fbinfoTaxrate)}"></td>
					                    		<td align="center" th:text="${cy.fbinfoCode}"></td>
					                    	</tr>
				                    	</tbody>
	                                 </table>
								</div>
	                        </div>
	                        <div class="form-group" >
	                        	<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label "></label>
									<div class="col-sm-8">
									</div>
								</div>
								<div class="col-sm-6 form-group">
									<label class="col-sm-4 control-label is-required">当前发票总额为:</label>
									<div class="col-sm-6">
										<div class="form-control-static" th:utext="${@ui.numberFormat(tp.paySumTotal)}"></div>
									</div>
									<label class="col-sm-2 control-label" style="text-align: left">(万元)</label>
								</div>
	                        </div>
						</div>
					</div>
				</div>
			</div>
			<div class="panel-group" role="tablist" aria-multiselectable="true" >
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#wtsm" aria-expanded="false" class="collapsed">
							需简述付款理由:
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
						</h4>
					</div>
					<!--折叠区域-->
					<div id="wtsm" class="panel-collapse collapse in">
						<div class="panel-body">
							<div class="form-group" >
								<div class="mnote-editor-box">
									<div class = "mnote-editor-box">
										<textarea readonly="readonly" class="form-control"  rows="8" th:utext="${tp.jsfkly_1}"></textarea>
									</div>
								</div>
	                        </div>
						</div>
					</div>
				</div>
			</div>
			
			<div class="panel-group" role="tablist" aria-multiselectable="true">
        		<div class="panel panel-default">
        			<!--5.0头部标签-->
        			<div class="panel-heading">
						<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#fj" aria-expanded="false" class="collapsed">相关附件
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
						</h4>
					</div>
					<div id="fj" class="panel-collapse collapse in">
						<div class="panel-body">
							<div class="row ">
		                        <div class="form-group col-sm-12">
		                            <label class="col-sm-3 control-label">发票复印件上传:</label>
		                            <div class="col-sm-9">
		                                <th:block th:include="include::layui-upload(see='true' ,sourceId=${re.tp.payId},sourceModule='17',id='attachmentId17',name='attachmentId17',labelClass='col-sm-1 control-label')"></th:block>
		                            </div>
		                        </div>
		                         <div class="form-group col-sm-12">
		                            <label class="col-sm-3 control-label">其他相关附件:</label>
		                            <div class="col-sm-9">
		                                <th:block th:include="include::layui-upload(see='true' ,sourceId=${re.tp.payId},sourceModule='18',id='attachmentId18',name='attachmentId18',labelClass='col-sm-1 control-label')"></th:block>
		                            </div>
		                        </div>
		                    </div>
		               </div>
					</div>
				</div>
			</div>
			<div class="panel-group" role="tablist" aria-multiselectable="true">
        		<div class="panel panel-default">
        			<!--5.0头部标签-->
        			<div class="panel-heading">
						<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#yj" aria-expanded="false" class="collapsed">审批意见
							&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
							<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
						</h4>
					</div>
					<div id="yj" class="panel-collapse collapse in">
						<div class="panel-body">
							<div class="row ">
		                        <div class="form-group col-sm-12">
									<!-- <th:block th:if="${i.activityCode eq 'MLX07' }">
					                	<div th:include="include :: initRadio(id='psjg',name='psjg',dictCode='implementationBase',businessType='KYND',
															labelName='评审结果:',isrequired='true')"></div>
					                </th:block><br> -->
					                <textarea class="form-control" id="comment" name="comment" th:utext="${ei.attr.comment}" style="width: 100%;height: 200px;" required="required"></textarea>
		                        </div>
		                    </div>
		                 </div>
		            </div>
				</div>
			</div>
        </form>
        <div class="row" style="padding: 15px 0;">
			<div class="toolbar toolbar-bottom" role="toolbar">
				<button type="button" class="btn btn-primary" onclick="doSave()"><i class="fa fa-check"></i>暂存</button>
				&nbsp;
				<th:block th:include="component/wfSubmitOne:: init(taskId=${i.taskId},callback=doSubmit)"/>
				&nbsp;
				<th:block th:include="component/wfReturn:: init(taskId=${i.taskId},callback=doRuturn)"/>
				&nbsp;
				<button type="button" class="btn btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>关 闭</button>
			</div>
		</div>
		<!--审批历史-->
		<!-- <div class="row" style="padding: 15px 0;" th:if="${i.processInstanceId !='' && i.processInstanceId != null}">
	        <th:block 
	                  th:include="include :: includeTaskHistoryList(instanceId=${i.processInstanceId})"></th:block>
        </div> -->
    </div>
        
    <script th:inline="javascript">
    
    $("#KTCGFK03Form").validate({
        focusCleanup: true,
        ignore: "" 
    });
	
	function doSave(v){
		//富文本框数据处理
		if(v == "1"){//不要提示
			$.operate.saveModal1(ctx+"c/s?serviceName=KTCGFK01&methodName=doSave&pageNo="+[[${ei.attr.pageNo}]]+"&c=", $('#KTCGFK03Form').serialize());
		}else{
			$.operate.saveModal(ctx+"c/s?serviceName=KTCGFK01&methodName=doSave&pageNo="+[[${ei.attr.pageNo}]]+"&c=", $('#KTCGFK03Form').serialize());
		}
    }
	//半小时，自动保存
	setInterval("doSave('1')", 1000*60*30);
	function doSubmit(transitionKey){
		 //意见框必填
		var comment = $("#comment").val();
		if(comment==null || comment ==""){
			$.modal.alertSuccess("意见框必填");
			return false;
		} 
	 	var url = ctx+"c/s?serviceName=KTCGFK01&methodName=doSubmit&pageNo="+[[${ei.attr.pageNo}]]+"&transitionKey="+transitionKey;
	 	submitForm(url,"KTCGFK03Form");
	}
	function doRuturn(activityKey){
		var url = ctx+"c/s?serviceName=KTCGFK01&methodName=doReturn&pageNo="+[[${ei.attr.pageNo}]]+"&activityKey="+activityKey;
		submitForm(url,"KTCGFK03Form");
	}

	

	function addMonth(date, offset) {
	    if (date instanceof Date && !isNaN(offset)) {
	        let givenMonth = date.getMonth();
	        let newMonth = givenMonth + offset;
	        date.setMonth(newMonth);
	        return dateToString(date);
	    }
	    throw Error('argument type error');
	}
	
	function getNextDay(d){
	    d = new Date(d);
	    d = +d + 1000*60*60*24;
	    d = new Date(d);
	    //return d;
	    //格式化
	    return d.getFullYear()+"-"+(d.getMonth()+1)+"-"+d.getDate();
	}
	
   function dateToString(date){ 
		  var year = date.getFullYear(); 
		  var month =(date.getMonth() + 1).toString(); 
		  var day = (date.getDate()).toString();  
		  if (month.length == 1) { 
		      month = "0" + month; 
		  } 
		  if (day.length == 1) { 
		      day = "0" + day; 
		  }
		  var dateTime = year + "-" + month + "-" + day;
		  return dateTime; 

   }

   function queryKYXMLX22(projectGuid){
	   var projectGuid = $("#projectGuid").val();
		var url = ctx+"c/l?serviceName=KYXMLX02&methodName=query&pageNo=KYXMLX38&rk=init&projectGuid="+projectGuid;
		var title = "项目树";
		
		// 选卡页方式打开并刷新当前页 isRefresh 是否刷新
		$.modal.openTab(title, url, true);
   }
   var tfoot_tr_pf = $("#tfoot_tr_pf").remove().clone().html()
   function add_pf(){
	   var num = parseInt($("#pfNum").val());
	   var tr = tfoot_tr_pf;
	   tr = tr.replace(RegExp("1000", "g"),num+"");
	   $("#tbody_pf").append(tr);
	   sortXH_pf();
	   $("#pfNum").val(num+1);
   }
   function sub_pf(){
	   $("#tbody_pf input[type='checkbox']:checked").each(function(){
  			$(this).parent().parent().remove();
  		});
  		sortXH_fp();
   }
   function sortXH_pf(){
	   $("#tbody_pf .xh").each(function(i){
			$(this).text(i+1);
		});
   }
	
</script>
</body>
</html>