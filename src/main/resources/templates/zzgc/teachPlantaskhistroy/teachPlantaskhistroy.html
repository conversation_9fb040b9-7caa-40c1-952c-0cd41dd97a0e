<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('计划任务书历史表列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>任务书变更主键：</label>
                                <input type="text" name="chtaskId"/>
                            </li>
                            <li>
                                <label>联系电话：</label>
                                <input type="text" name="taskTel"/>
                            </li>
                            <li>
                                <label>签发部门：</label>
                                <input type="text" name="taskDept"/>
                            </li>
                            <li>
                                <label>签订日期：</label>
                                <input type="text" name="taskDate"/>
                            </li>
                            <li>
                                <label>编制人工号：</label>
                                <input type="text" name="userCode"/>
                            </li>
                            <li>
                                <label>编制人姓名：</label>
                                <input type="text" name="userName"/>
                            </li>
                            <li>
                                <label>是否变更项目周期：</label>
                                <input type="text" name="taskBgzq"/>
                            </li>
                            <li>
                                <label>项目变更开始日期：</label>
                                <input type="text" name="prostartBgDate"/>
                            </li>
                            <li>
                                <label>项目变更结束日期：</label>
                                <input type="text" name="proendBgDate"/>
                            </li>
                            <li>
                                <label>实施费用：</label>
                                <input type="text" name="payTotal"/>
                            </li>
                            <li>
                                <label>管理费收取系数：</label>
                                <input type="text" name="payManxs"/>
                            </li>
                            <li>
                                <label>项目费用：</label>
                                <input type="text" name="proTotal"/>
                            </li>
                            <li>
                                <label>知识产权估算：</label>
                                <input type="text" name="knowledgeEstimate"/>
                            </li>
                            <li>
                                <label>经济效益费用估算：</label>
                                <input type="text" name="economyEstimate"/>
                            </li>
                            <li>
                                <label>人工费：</label>
                                <input type="text" name="payRgfOld"/>
                            </li>
                            <li>
                                <label>计划任务书版本：</label>
                                <input type="text" name="taskVersion"/>
                            </li>
                            <li>
                                <label>扩展字段1：</label>
                                <input type="text" name="extra1"/>
                            </li>
                            <li>
                                <label>扩展字段2：</label>
                                <input type="text" name="extra2"/>
                            </li>
                            <li>
                                <label>扩展字段3：</label>
                                <input type="text" name="extra3"/>
                            </li>
                            <li>
                                <label>扩展字段4：</label>
                                <input type="text" name="extra4"/>
                            </li>
                            <li>
                                <label>扩展字段5：</label>
                                <input type="text" name="extra5"/>
                            </li>
                            <li>
                                <label>删除状态：</label>
                                <select name="delStatus" th:with="type=${@dict.getDictList(null,null)}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzgc/teachPlantaskhistroy";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "计划任务书历史表",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'taskhisId',
                    title: '计划任务书历史主键',
                    visible: false
                },
                {
                    field: 'chtaskId',
                    title: '任务书变更主键'
                },
                {
                    field: 'taskTel',
                    title: '联系电话'
                },
                {
                    field: 'taskDept',
                    title: '签发部门'
                },
                {
                    field: 'taskDate',
                    title: '签订日期'
                },
                {
                    field: 'userCode',
                    title: '编制人工号'
                },
                {
                    field: 'userName',
                    title: '编制人姓名'
                },
                {
                    field: 'taskBgzq',
                    title: '是否变更项目周期'
                },
                {
                    field: 'prostartBgDate',
                    title: '项目变更开始日期'
                },
                {
                    field: 'proendBgDate',
                    title: '项目变更结束日期'
                },
                {
                    field: 'payTotal',
                    title: '实施费用'
                },
                {
                    field: 'payManxs',
                    title: '管理费收取系数'
                },
                {
                    field: 'proTotal',
                    title: '项目费用'
                },
                {
                    field: 'knowledgeEstimate',
                    title: '知识产权估算'
                },
                {
                    field: 'economyEstimate',
                    title: '经济效益费用估算'
                },
                {
                    field: 'payRgfOld',
                    title: '人工费'
                },
                {
                    field: 'taskVersion',
                    title: '计划任务书版本'
                },
                {
                    field: 'extra1',
                    title: '扩展字段1'
                },
                {
                    field: 'extra2',
                    title: '扩展字段2'
                },
                {
                    field: 'extra3',
                    title: '扩展字段3'
                },
                {
                    field: 'extra4',
                    title: '扩展字段4'
                },
                {
                    field: 'extra5',
                    title: '扩展字段5'
                },
                {
                    field: 'delStatus',
                    title: '删除状态'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    field: 'deleteUserLabel',
                    title: '删除人'
                },
                {
                    field: 'deleteDate',
                    title: '删除时间'
                },
                {
                    field: 'recordVersion',
                    title: '版本号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.taskhisId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.taskhisId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>