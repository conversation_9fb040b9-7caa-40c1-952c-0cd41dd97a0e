<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('调整计划任务书')" />
    <th:block th:include="include :: baseJs" />


</head>
<body class="white-bg">


    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-planTask-add" th:object="${plan}">
            <div class="form-group" th:include="include :: step(approveKind='ZZZC_CHANGE',currentNode=${activityCode})"></div><br/>
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
            <input id="planId" name="planId"  th:value="${plan.taskId}" type="hidden">
            <input id="chtaskId" name="chtaskId"  th:value="${change.chtaskId}" type="hidden">
            <input id="taskhisId" name="taskhisId"  th:value="${histroy.taskhisId}" type="hidden">
            <input id="fixId" name="fixId"  th:value="${teachFix.fixId}" type="hidden">
            <input id="taskIds" name="taskIds"  th:value="${taskId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
            <input id="comment" name="comment" type="hidden">


            <!--框-->
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#2" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目名称：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${main.projectName}"></div>
                                </div>
                                <label class="col-sm-2 control-label">项目编号：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                </div>
                            </div>
                            </div>
                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">项目范围：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/radio :: init(id='projectArea',name='projectArea',
                                   businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='projectArea',value=${main.projectArea} ,see=true)"></div>
                            </div>

                            <label class="col-sm-2 control-label">受让单位部门：</label>
                            <div class="col-sm-4">
                                <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.srfDeptCode)}"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">合同名称：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static" th:text="${main.projectHtName}"></div>
                            </div>
                            <label class="col-sm-2 control-label">合同编号：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static" th:text="${main.projectHtNum}"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">推广单位部门：</label>
                            <div class="col-sm-4">
                                <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.tgfDeptCode)}"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='tgfFzrCode',userNameId='tgfFzrName',value=${main.tgfFzrCode},see=true)"></div>
                            <label class="col-sm-2 control-label">联系电话：</label>
                            <div class="col-sm-4">
                             <input name="taskTel" th:value="${plan.taskTel}" type="hidden" >
                                    <div class="form-control-static" th:utext="${main.tgfXmfzrTel}"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">签发部门：</label>
                            <div class="col-sm-4">
                                <input name="taskDept" th:value="${plan.taskDept}" type="hidden" >
                                <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(plan.taskDept)}"></div>

                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">签发日期：</label>
                            <div class="col-sm-4">
                                <input name="taskDate" th:value="${plan.taskDate}" type="hidden" >
                                <div class="form-control-static" th:utext="${plan.taskDate}"></div>
                            </div>
                            <label class="col-sm-2 control-label">编制日期：</label>
                            <div class="col-sm-4">
                                <input name="extra1" th:value="${downDate}" type="hidden" >
                                  <div class="form-control-static" th:utext="${downDate}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">项目所属分类：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/select :: init(id='payKind', name='payKind',
                                businessType=*{constants.BUSINESS_TYPE_KTTG}, dictCode='payKind',value=${teachFix.payKind},isfirst=true,see=true)"></div>
                            </div>
                        </div>
                    </div>


                <div class="row">
                    <div class="form-group"><label class="col-sm-2 control-label"><b>项目起止日期</b></label></div>
                </div>
                <div class="row">
                    <div class="form-group ">
                        <label class="col-sm-2 control-label is-required">项目开始日期：</label>
                        <div class="col-sm-4">
                      <input name="prostartDate" required  th:value="${plan.prostartDate}" class="form-control"  type="date"/>
                        </div>
                        <label class="col-sm-2 control-label is-required">项目结束日期：</label>
                        <div class="col-sm-4">
                        <input name="proendDate" required th:value="${plan.proendDate}"  class="form-control"  type="date"/>
                        </div>

                    </div>
                </div>
                            <div class="form-group">
                                <label class="col-sm-3 ">点击<a class="form_list_a" th:onclick="$.modal.openTab('任务书申请单',ctx+'zzgc/teachChangetask/openDetail/'+[[${main.mainId}]]+'?type=alert')">此处</a>查看变更申请单</label>
                            </div>

                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>

            <div class="panel-group" id="accordion2"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmgx" class="collapsed">
                                <span style="color: red">*</span>项目范围
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmgx" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="taskArea" th:utext="${plan.taskArea}"  class="form-control" rows="8" required ></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion3"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmmb" class="collapsed">
                                <span style="color: red">*</span>项目目标
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmmb" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="taskTarget" th:utext="${plan.taskTarget}"  class="form-control" rows="8" required></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmfw" class="collapsed">
                                <span style="color: red">*</span>项目内容
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmfw" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="taskContent" th:utext="${plan.taskContent}"  class="form-control" rows="8" required></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion10" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jdjh" class="collapsed">
                                申请更改内容及原因
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="jdjh" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-planReson"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion10" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jdjh" class="collapsed">
                                进度计划
                                <span class="pull-right">
                <i class="fa fa-chevron-down"></i>
            </span>
                            </a>
                        </h4>
                    </div>
                    <div id="jdjh" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <button type="button" class="btn btn-white btn-sm" onclick="addTeachPlanColumn()"><i class="fa fa-plus"> 增加</i></button>
                                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-plan"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="panel-group" id="accordion7" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmzcytb" class="collapsed">
                                <span style="color: red">*</span>项目组织体系
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmzcytb" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <p class="select-title">受让方成员</p>
                                 <input id="shouCode"  name="shouCode" type="hidden">
                                    <input id="shouName"  name="shouName" type="hidden">
                                        <button type="button" class="btn btn-white btn-sm"  onclick="choiceUser('shouCode','shouName','M',null,'addShouRangColumn')"><i class="fa fa-plus"> 添加成员</i></button>
                                    <div class="col-sm-12 select-table " style="margin-bottom: 30px">
                                        <table id="bootstrap-table-srf"></table>
                                    </div>
                                    <br><br>
                                    <p class="select-title">推广方成员</p>
                                    <input id="teamCode"  name="teamCode" type="hidden">
                                    <input id="teamName"  name="teamName" type="hidden">
                                    <button type="button" class="btn btn-white btn-sm"  onclick="choiceUser('teamCode','teamName','M',null,'addProjectMemberColumn')"><i class="fa fa-plus"> 添加成员</i></button>
                                    <!--<button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>-->
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-member"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--第三块-->
            <div class="panel-group" id="accordion3" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#3"
                                                   aria-expanded="false"
                                                   class="collapsed">项目内部协作及分工
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span
                                    class="pull-right"><i
                                    class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                    </div>
                    <!--折叠区域-->
                    <div id="3" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <textarea name="taskXzfg" class="form-control" th:utext="${plan.taskXzfg}"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--第三块end-->


            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jfystb" class="collapsed">
                                <span style="color: red">*</span>费用估算（单位：万元）
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="jfystb" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <p class="select-title">实施费用估算</p>
                                    <button type="button" class="btn btn-white btn-sm" onclick="addTeachCostolumn()"><i class="fa fa-plus"> 增加</i></button>
                                    <button type="button" class="btn btn-white btn-sm" onclick="delCost()"><i class="fa fa-minus"> 删除</i></button>
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-ys"></table>
                                        <table class="table table-bordered table-hover">
                                            <tfoot style="margin-right: 0px;">
                                            <tr>
                                                <th style="text-align: center;  width:127px;">
                                                    <div class="th-inner"></div>
                                                    <div class="fht-cell" style="width: auto;"></div>
                                                </th>
                                                <th style="text-align: left;width: 300px;">
                                                    <div class="th-inner" style="width: 150px;">合计（万元）：</div>
                                                </th>
                                                <th style="text-align: left; ">
                                                    <div class="th-inner">
                                                        <div class="form-control-static" id="costTotal" th:utext="${#numbers.formatDecimal(plan.payTotal,1,2)}" style="text-align: left"></div>
                                                    </div>
                                                </th>
                                            </tr>
                                            </tfoot>
                                        </table>
                                        <table class="table table-bordered table-hover" style="margin-top:20px;border: 1px solid #e7e7e7;">
                                            <tfoot>
                                            <tr>
                                                <td style="text-align: center; ">现场支撑人日数</td>
                                                <td style="text-align: center; ">
                                                    <input name="payDay" id="payDay" class="form-control" th:value="${plan.payDay}" type="text" >
                                                </td>
                                                <td style="text-align: center; "><span style="color: red">*&nbsp;</span>远程支撑系数</td>
                                                <td style="text-align: center; ">
                                                    <input name="payXs" class="form-control" required th:value="${#numbers.formatDecimal(plan.payXs,1,2)}"onkeyup="this.value=this.value.replace(/[^\.\d]/g,'')" min="0" >
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="text-align: center; ">实施费用(A)万元</td>
                                                <td style="text-align: center; ">
                                                    <input name="payTotal" id="payTotal" th:value="${plan.payTotal}" type="hidden">
                                                    <div class="form-control-static" id="totala" th:utext="${#numbers.formatDecimal(plan.payTotal,1,2)}" style="text-align: left"></div>
                                                </td>
                                                <td style="text-align: center; ">管理费(B)%</td>
                                                <td style="text-align: center; ">
                                                    <input name="payManxs" class="form-control" th:value="${plan.payManxs}" min="0" onkeyup="this.value=this.value.replace(/[^\.\d]/g,'')" type="number" onblur="changeTotal()" required>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="text-align: center; ">项目费用</td>
                                                <td colspan="3" style="text-align: left; ">
                                                    <input name="proTotal" id="proTotal" th:value="${plan.proTotal}" type="hidden">
                                                    <div class="form-control-static" id="projectTotal" th:utext="${#numbers.formatDecimal(plan.proTotal,1,2)}" style="text-align: left"></div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="text-align: center; ">定价公式</td>
                                                <td colspan="3" style="text-align: left; ">
                                                    项目费用=A*(1 + B%)   &nbsp;&nbsp;&nbsp;  <span style="color: red">注： 上述费用含6%的增值税</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="text-align: center; ">知识产权估算(万元)</td>
                                                <td style="text-align: center; ">
                                                    <input name="knowledgeEstimate"  th:value="${#numbers.formatDecimal(plan.knowledgeEstimate,1,2)}"  class="form-control" type="text" required>
                                                </td>

                                                <td style="text-align: center; ">经济效益估算(万元)</td>
                                                <td style="text-align: center; ">
                                                    <input name="economyEstimate" th:value="${#numbers.formatDecimal(plan.economyEstimate,1,2)}" class="form-control" type="text" required>
                                                </td>

                                            </tr>
                                            <tr>
                                                <td colspan="4">&nbsp;&nbsp;说明：<br/>
                                                    1、差旅费：按照各基地相关管理制度执行<br/>
                                                    2、通讯费：按照各基地相关管理制度执行<br/>
                                                    3、人工费：按各基地岗位类别计价标准核算<br/>
                                                    4、其他费用是指内部检测费、测试费</td>
                                            </tr>
                                            </tfoot>
                                        </table>
                                        <p class="select-title">经济效益算法:</p>
                                        <input name="taskJjxy" id="taskJjxy" th:value="${plan.taskJjxy}" class="form-control" type="text" required>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion12"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xtsy" class="collapsed">
                                项目验收标准及方式
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xtsy" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="taskMethod" th:utext="${plan.taskMethod}"  class="form-control" rows="8" ></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion11"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#ysbz" class="collapsed">
                               相关附件
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="ysbz" class="panel-collapse collapse in">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">附件上传：</label>
                            <div class="col-sm-10">
                                <div th:include="/component/attachment :: init(display='none',name='planFj',id='planFj',sourceId=${plan.planId},sourceModule='PLAN_FJ')">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <input type="hidden" id="type" name="type" value="add"/>
        </form>
        <div class="m">
            <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
        </div>
    </div>
    <div class="row">
		<div class="toolbar toolbar-bottom" role="toolbar" >

			<button type="button" class="btn btn-primary"
				onclick="saveHandler()">
				<i class="fa fa-check"></i>暂 存
			</button>

            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

            <th:block th:include="component/wfReturn:: init(taskId=${taskId},callback=wfReturn)"/>
            <button type="button" class="btn btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返 回
            </button>
        </div>
		</div>
	</div>
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="component/wfCommentList::init(processInstanceId=${processInstanceId})"></div>

    <script type="text/html" id="scheduleThymeleaf">
        <select name="teachSchedule[%s].deType" th:class="form-control" id="teachSchedule[%s].deType"
                th:with="dictData=${@dict.getDictList('KTTG','deType')}">
            <option  value=" ">请选择</option>
            <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
        </select>
    </script>
    <script type="text/html" id="srfPeopleThymeleaf">
        <select name="srfPeople[%s].role" th:class="form-control" id="srfPeople[%s].role"
                th:with="dictData=${@dict.getDictList('KTTG','projectRole')}">
            <option  value=" ">请选择</option>
            <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
        </select>
    </script>
    <script type="text/html" id="tgfPeopleThymeleaf">
        <select name="tgfPeople[%s].role" th:class="form-control" id="tgfPeople[%s].role"
                th:with="dictData=${@dict.getDictList('KTTG','projectRole')}">
            <option  value=" ">请选择</option>
            <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
        </select>
    </script>

    <script th:inline="javascript">
        var prefix = ctx + "zzgc/teachPlantaskhistroy";
        $("input[name='prostartDate']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
        $("input[name='proendDate']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
        $("input[name='projectLxDate']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });

        $("#bootstrap-table-key").on("post-body.bs.table", function (e, args) {
            $("input[name$='beginTime']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });

        $("#bootstrap-table-key").on("post-body.bs.table", function (e, args) {
            $("input[name$='endTime']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });

        $("#bootstrap-table-plan").on("post-body.bs.table", function (e, args) {
            $("input[name$='beginTime']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });

        $("#bootstrap-table-plan").on("post-body.bs.table", function (e, args) {
            $("input[name$='endTime']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });

        $("#bootstrap-table-ys").on("post-body.bs.table", function (e, args) {
            $("input[name$='busiYear']").datetimepicker({
                format: "yyyy",
                weekStart: 1,
                minView: 4,
                startView: 4,
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });

        $("#form-teachAtt-add").validate({
            focusCleanup: true
        });


        function saveHandler() {
            if ($.validate.form()) {
                var config = {
                    url: prefix + "/addSave",
                    type: "post",
                    dataType: "json",
                    data: $('#form-planTask-add').serialize(),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $("#planId").val(result.data.planId);
                        $("#taskhisId").val(result.data.taskhisId);
                        $.modal.alertSuccess(result.msg);
                        $.modal.closeLoading();
                    }
                };
                $.ajax(config)
            }
        }

        function submitHandler(){
            var len =$("#bootstrap-table-srf").find("tr").length;
            for(var i=0;i<len-1;i++){
                var job =  $("input[name='srfPeople["+i+"].job']").val();
                var role =  $("select[name='srfPeople["+i+"].role']").val();
                if(job==''){
                    $.modal.alertError("请输入受让方成员主要任务！");
                    return;
                }
                if(role==''){
                    $.modal.alertError("请选择受让方成员项目角色！");
                    return;
                }
            }
            var len =$("#bootstrap-table-member").find("tr").length;
            for(var i=0;i<len-1;i++){
                var job =  $("input[name='tgfPeople["+i+"].job']").val();
                var role =  $("select[name='tgfPeople["+i+"].role']").val();
                if(job==''){
                    $.modal.alertError("请输入推广方成员主要任务！");
                    return;
                }
                if(role==''){
                    $.modal.alertError("请选择推广方成员项目角色！");
                    return;
                }
            }

            $.modal.confirm("确认提交吗？", function () {
                if ($.validate.form()) {
                    $("#type").val("submit");
                    $.operate.saveTabAlert(prefix + "/submitWF",$('#form-planTask-add').serialize());
                }
            })
        }
        //流程跟踪
        function workFlowProcess() {
            window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + [[${processInstanceId}]]);
        }

        function requiredPeople(){

        }

        //退回流程
        function wfReturn(activityKey) {
            if ($.validate.form()) {
                var data = $('#form-planTask-add').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
            }
        }


        $(function() {
            var options = {
                url: ctx + "zzgc/teachChangetasksub/teachSubList?chtaskId="+[[${change.chtaskId}]],
                id:"bootstrap-table-planReson",
                toolbar:"toolbar-planReson",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='changeTask[%s].index' value='%s'>", index, $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='changeTask[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'planTaskContent',
                        align: 'center',
                        title: '计划任务书内容'
                    },
                    {
                        field: 'changeAfterContent',
                        align: 'center',
                        title: '拟更改的内容'
                    },{
                        field: 'changeReason',
                        align: 'center',
                        title: '更改原因'
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });

        var roleDatas = [[${roleDatas}]];


        /***********************************推广方成员*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzgg/teachPropeople/teachPeopleList?bizId="+[[${plan.planId}]]+"&peType=tgf&extra1=P",
                id: "bootstrap-table-member",
                toolbar:"toolbar-xmfz",
                modalName: "推广方成员",
                uniqueId: "userId",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                onLoadSuccess:function(data){
                    var teamCode = $("#teamCode").val();
                    if(data.length > 0){
                        for (var i = 0; i < data.length; i++) {
                            if(!$.common.isEmpty(teamCode)){
                                teamCode+=",";
                            }
                            teamCode +=data[i].userId;
                        }
                    }
                    $("#teamCode").val(teamCode);
                },
                sidePagination: "client",
                columns: [
                    /*{
                        checkbox: true
                    },*/
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        witdh:'5%',
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='tgfPeople[%s].orderNum' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='tgfPeople[%s].peId' value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'userId',
                        align: 'center',
                        title: '工号',
                        witdh:'5%',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input required class='form-control' readonly type='text' name='tgfPeople[%s].userId' value='%s' >",index, value);
                            return html;
                        }
                    },
                    {
                        field: 'userName',
                        align: 'center',
                        title: '姓名',
                        witdh:'5%',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' readonly  type='text' name='tgfPeople[%s].userName' value='%s' >", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'position',
                        align: 'center',
                        title: '岗位',
                        witdh:'5%',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  type='text' name='tgfPeople[%s].position' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'point',
                        align: 'center',
                        title: '特殊技长',
                        witdh:'5%',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  type='text' name='tgfPeople[%s].point' value='%s'>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'job',
                        align: 'center',
                        title: '主要任务',
                        witdh:'5%',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'   type='text' name='tgfPeople[%s].job' value='%s'>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'role',
                        align: 'center',
                        title: '项目角色',
                        witdh:'15%',
                        formatter: function(value, row, index) {
                            var role = 'tgfPeople[' + index + '].role';
                            var id = 'tgfrole' + index;
                            return dictToSelect(roleDatas, value, role, id);
                        }
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });
        function addProjectMemberColumn(userCode, userName) {
            $("#teamCode").val(userCode);
            $("#teamName").val(userName);
            var teamCodes = $("#teamCode").val();
            var teamNames = $("#teamName").val();
            var teamCode = teamCodes.split(",");
            var teamName = teamNames.split(",");
            $("#bootstrap-table-member tbody").html("");
            var rows = "";
            var config = {          //查询出已有的成员
                url: ctx + "zzgg/teachPropeople/loadList?bizId=" + [[${plan.planId}]] + "&peType=tgf" + "&extra1=P",
                type: "post",
                dataType: "json",
                success: function (result) {
                    var data = result.data;
                    var blocks = data.blocks;
                    var attr = blocks.result.attr;
                    var row = attr.pageData.rows;
                    rows = row;
                    var newRow = new Array();
                    for (var i = 0; i < teamName.length; i++) {
                        if (teamCode[i]) {
                            var orderNum = $("#bootstrap-table-member").find("tr").length;
                            var len = $("#bootstrap-table-member").find("tr").length - 1;
                            if(rows.length>0) {
                                    for(var j=0;j<rows.length;j++) {
                                        if (rows[j].userId == teamCode[i]) {
                                            if(rows[j].position==null){
                                                rows[j].position = "";
                                            }
                                            if(rows[j].point==null){
                                                rows[j].point = "";
                                            }
                                            if(rows[j].job==null){
                                                rows[j].job = "";
                                            }
                                            var tr = "<tr data-index='" + len + "'>";
                                            tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='tgfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                            tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userId' value=" + rows[j].userId + "></td>";
                                            tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userName' value=" + rows[j].userName + "></td>";
                                            tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].position' value=" + rows[j].position + " ></td>";
                                            tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].point' value=" + rows[j].point + "></td>";
                                            tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].job' value=" + rows[j].job + "></td>";
                                            tr = tr + "<td style='text-align: center;'>" + $.common.sprintf(dictToSelect(roleDatas, rows[j].role, "tgfPeople[" + len + "].role", "tgfrole" + len)) + "</td>";
                                            tr = tr + "</tr>";
                                            $(".no-records-found").remove();
                                            $("#bootstrap-table-member tbody").append(tr);
                                            newRow.push(rows[j].userId);
                                        }
                                    }
                                    if(newRow.length>0){
                                        if(teamCode[i]!=newRow[newRow.length-1]){
                                            var tr = "<tr data-index='" + len + "'>";
                                            tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='tgfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                            tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userId' value=" + teamCode[i] + "></td>";
                                            tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userName' value=" + teamName[i] + "></td>";
                                            tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].position' ></td>";
                                            tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].point' ></td>";
                                            tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].job'></td>";
                                            tr = tr + "<td style='text-align: center;'>" + $.common.sprintf($("#tgfPeopleThymeleaf").html(), len, len) + "</td>";
                                            tr = tr + "</tr>";
                                            $(".no-records-found").remove();
                                            $("#bootstrap-table-member tbody").append(tr);
                                        }
                                    }else{
                                        var tr = "<tr data-index='" + len + "'>";
                                        tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='tgfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userId' value=" + teamCode[i] + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userName' value=" + teamName[i] + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].position' ></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].point' ></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].job'></td>";
                                        tr = tr + "<td style='text-align: center;'>" + $.common.sprintf($("#tgfPeopleThymeleaf").html(), len, len) + "</td>";
                                        tr = tr + "</tr>";
                                        $(".no-records-found").remove();
                                        $("#bootstrap-table-member tbody").append(tr);
                                    }

                            }else{
                                var tr = "<tr data-index='" + len + "'>";
                                tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='tgfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userId' value=" + teamCode[i] + "></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userName' value=" + teamName[i] + "></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].position' ></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].point' ></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].job'></td>";
                                tr = tr + "<td style='text-align: center;'>" + $.common.sprintf($("#tgfPeopleThymeleaf").html(), len, len) + "</td>";
                                tr = tr + "</tr>";
                                $(".no-records-found").remove();
                                $("#bootstrap-table-member tbody").append(tr);
                            }
                        }
                    }
                }
            };
            $.ajax(config)
        }

        /***********************************受让方成员*******************************************/
        $(function() {
            /*if(projectArea!=null&&projectArea!=""&&projectArea=='gfn') {*/
                var options = {
                    url: ctx + "zzgg/teachPropeople/teachPeopleList?bizId=" + [[${plan.planId}]] + "&peType=srf"+"&extra1=P",
                    id: "bootstrap-table-srf",
                    toolbar: "toolbar-xmfz",
                    modalName: "受让方成员",
                    uniqueId: "userId",
                    pagination: false,
                    showSearch: false,
                    showRefresh: false,
                    showToggle: false,
                    showColumns: false,
                    onLoadSuccess:function(data){
                        var shouCode = $("#shouCode").val();
                        if(data.length > 0){
                            for (var i = 0; i < data.length; i++) {
                                if(!$.common.isEmpty(shouCode)){
                                    shouCode+=",";
                                }
                                shouCode +=data[i].userId;
                            }
                        }
                        $("#shouCode").val(shouCode);
                    },
                    sidePagination: "client",
                    columns: [
                       /* {
                            checkbox: true
                         },*/
                        {
                            field: 'index',
                            align: 'center',
                            title: "序号",
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var columnIndex = $.common.sprintf("<input type='hidden' name='srfPeople[%s].orderNum' value='%s'>", $.table.serialNumber(index));
                                var columnId = $.common.sprintf("<input type='hidden' name='srfPeople[%s].peId' value='%s'>", index, row.id);
                                return columnIndex + $.table.serialNumber(index) + columnId;
                            }
                        },
                        {
                            field: 'userId',
                            align: 'center',
                            title: '工号',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input required class='form-control' readonly type='text' name='srfPeople[%s].userId' value='%s' >",index, value);
                                return html;
                            }
                        },
                        {
                            field: 'userName',
                            align: 'center',
                            title: '姓名',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control' readonly type='text' name='srfPeople[%s].userName' value='%s' >", index, value);
                                return html;
                            }
                        },
                        {
                            field: 'position',
                            align: 'center',
                            title: '岗位',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control' type='text' name='srfPeople[%s].position' value='%s'>", index, value);
                                return html;
                            }
                        }, {
                            field: 'point',
                            align: 'center',
                            title: '特殊技长',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control' type='text' name='srfPeople[%s].point' value='%s'>", index, value);
                                return html;
                            }
                        },
                        {
                            field: 'job',
                            align: 'center',
                            title: '主要任务',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control' type='text' name='srfPeople[%s].job' value='%s'>", index, value);
                                return html;
                            }
                        },
                        {
                            field: 'role',
                            align: 'center',
                            title: '项目角色',
                            witdh: '15%',
                            formatter: function (value, row, index) {
                                var role = 'srfPeople[' + index + '].role';
                                var id = 'srfrole' + index;
                                return dictToSelect(roleDatas, value, role, id);
                            }
                        }
                    ]
                };
          /*  }*/
            $.table.init(options);
            $(".no-records-found").remove();
        });
        function addTeachPeopleColumn() {
            var row = {
                userId: "",
                userName: "",
                position: "",
                point: "",
                job: "",
                role: "",
            }
            sub.addColumn(row,"bootstrap-table-srf");
        }

        function addShouRangColumn() {
            var shouCodes = $("#shouCode").val();
            var shouNames = $("#shouName").val();
            var shouCode = shouCodes.split(",");
            var shouName = shouNames.split(",");
            $("#bootstrap-table-srf tbody").html("");
            var rows = [];
            var config = {          //
                url: ctx +"zzgg/teachPropeople/loadList?bizId=" + [[${plan.planId}]] + "&peType=srf"+"&extra1=P",
                type: "post",
                dataType: "json",
                success: function (result) {
                    var data = result.data;
                    var blocks = data.blocks;
                    var attr = blocks.result.attr;
                    var row = attr.pageData.rows;
                    rows = row;
                    var newRow = new Array();
                    for(var i=0;i<shouCode.length;i++){
                        if(shouCode[i]){
                            var orderNum = $("#bootstrap-table-srf").find("tr").length;
                            var len = $("#bootstrap-table-srf").find("tr").length - 1;
                            if(rows.length>0) {
                                for(var j=0;j<rows.length;j++) {
                                    if (rows[j].userId == shouCode[i]) {
                                        if(rows[j].position==null){
                                            rows[j].position = "";
                                        }
                                        if(rows[j].point==null){
                                            rows[j].point = "";
                                        }
                                        if(rows[j].job==null){
                                            rows[j].job = "";
                                        }
                                        var tr = "<tr data-index='" + len + "'>";
                                        tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='srfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='srfPeople[" + len + "].userId' value=" + rows[j].userId + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='srfPeople[" + len + "].userName' value=" + rows[j].userName + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].position'  value=" + rows[j].position + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].point' value=" + rows[j].point + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].job' value=" + rows[j].job + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'>" + $.common.sprintf(dictToSelect(roleDatas, rows[j].role, "srfPeople[" + len + "].role", "role" + len)) + "</td>";
                                        tr = tr + "</tr>";
                                        $(".no-records-found").remove();
                                        $("#bootstrap-table-srf tbody").append(tr);
                                        newRow.push(rows[j].userId);
                                    }
                                }
                                if(newRow.length>0){
                                    if(shouCode[i]!=newRow[newRow.length-1]){
                                        var tr = "<tr data-index='" + len + "'>";
                                        tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='srfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly  type='text' name='srfPeople[" + len + "].userId' value=" + shouCode[i] + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='srfPeople[" + len + "].userName' value=" + shouName[i] + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].position' ></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].point' ></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].job'></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'>" + $.common.sprintf($("#srfPeopleThymeleaf").html(), len, len) + "</td>";
                                        tr = tr + "</tr>";
                                        $(".no-records-found").remove();
                                        $("#bootstrap-table-srf tbody").append(tr);
                                    }
                                }else{
                                    var tr = "<tr data-index='" + len + "'>";
                                    tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='srfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly  type='text' name='srfPeople[" + len + "].userId' value=" + shouCode[i] + "></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='srfPeople[" + len + "].userName' value=" + shouName[i] + "></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].position' ></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].point' ></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].job'></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'>" + $.common.sprintf($("#srfPeopleThymeleaf").html(), len, len) + "</td>";
                                    tr = tr + "</tr>";
                                    $(".no-records-found").remove();
                                    $("#bootstrap-table-srf tbody").append(tr);
                                }
                            }else{
                                var tr = "<tr data-index='" + len + "'>";
                                tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='srfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly  type='text' name='srfPeople[" + len + "].userId' value=" + shouCode[i] + "></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='srfPeople[" + len + "].userName' value=" + shouName[i] + "></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].position' ></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].point' ></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].job'></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'>" + $.common.sprintf($("#srfPeopleThymeleaf").html(), len, len) + "</td>";
                                tr = tr + "</tr>";
                                $(".no-records-found").remove();
                                $("#bootstrap-table-srf tbody").append(tr);
                            }
                        }
                    }
                }
            };
            $.ajax(config)

        }

        function addTeachPeopleColumn() {
            var row = {
                userId: "",
                userName: "",
                position: "",
                point: "",
                job: "",
                role: "",
            }
            sub.addColumn(row,"bootstrap-table-srf");
        }

        // 数据字典转下拉框
        function dictToSelect(datas, value, name, id) {
            var actions = [];
            actions.push($.common.sprintf("<select id='%s' class='form-control' name='%s'><option value=''>请选择</option>", id, name));
            $.each(datas, function (index, dict) {
                actions.push($.common.sprintf("<option value='%s'", dict.dictValue));
                if (dict.dictValue == ('' + value)) {
                    actions.push(' selected');
                }
                actions.push($.common.sprintf(">%s</option>", dict.dictName));
            });
            actions.push('</select>');
            return actions.join('');
        }

        /***********************************进度计划表*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzgg/teachPlan/teachPlanList?bizId="+[[${plan.planId}]],
                id:"bootstrap-table-plan",
                toolbar:"toolbar-plan",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: true
                },
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='teachPlans[%s].orderNum' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachPlans[%s].planId'  value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'beginTime',
                        align: 'center',
                        title: '阶段开始时间',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required  type='text' required name='teachPlans[%s].beginTime' placeholder='yyyy-MM-dd'  value='%s' placeholder='yyyy-MM-dd'>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'endTime',
                        align: 'center',
                        title: '阶段结束时间',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  required  type='text' required  name='teachPlans[%s].endTime' placeholder='yyyy-MM-dd'  value='%s' placeholder='yyyy-MM-dd'>", index, value);
                            return html;
                        }
                    },{
                        field: 'stageName',
                        align: 'center',
                        title: '阶段名称',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required   type='text' required  name='teachPlans[%s].stageName' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'stageTarget',
                        align: 'center',
                        title: '阶段目标',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required   type='text' required  name='teachPlans[%s].stageTarget' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'peoply',
                        align: 'center',
                        title: '人数',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required min='0'  type='number' required onblur='fenHeji()' name='teachPlans[%s].peoply' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'days',
                        align: 'center',
                        title: '天数',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required min='0'  type='number' required onblur='fenHeji()' name='teachPlans[%s].days' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'total',
                        align: 'center',
                        title: '分项合计',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required min='0' readonly  type='number' required  name='teachPlans[%s].total' value='%s'>", index, value);
                            return html;
                        }
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });

        function fenHeji(){
            $("#bootstrap-table-plan tbody").children().each(function () {
                var self = $(this);
                var peoply = self.find("td:eq(6)").find("input").val();
                var days = self.find("td:eq(7)").find("input").val();
                if(peoply==null||peoply==''){
                    peoply=0;
                }
                if(days==null||days==''){
                    days=0;
                }
                var total = parseInt(peoply)*parseInt(days);
                self.find("td:eq(8)").find("input").attr("value",total);
            })
        }

        function addTeachPlanColumn() {
            var row = {
                beginTime: "",
                endTime: "",
                stageName: "",
                stageTarget: "",
                peoply: "",
                days: "",
                total: "",
            }
            sub.addColumn(row,"bootstrap-table-plan");
        }

        /***********************************费用估算*******************************************/
        $(function() {
            var year=new Date().getFullYear();
            var options = {
                url: ctx + "zzgg/teachCost/teachCostList?bizId="+[[${plan.planId}]]+"&costType=pay",
                id: "bootstrap-table-ys",
                modalName: "费用估算",
                showFooter: true,
                footerStyle: footerStyle,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: true
                },
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        width: "20",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='orderNum' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachCost[%s].orderNum' value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'busiYear',
                        align: 'center',
                        title: '年度',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  required  type='text' name='teachCost[%s].busiYear' value='%s' >", index, value);
                            return html;
                        },
                        footerFormatter:function (value) {
                            return "小计：";
                        }

                    },
                    {
                        field: 'travelCost',
                        align: 'center',
                        title: '差旅费',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  required  min='0' type='number'name='teachCost[%s].travelCost' onchange='ysSum()' onkeyup=\"this.value=this.value.replace(/[^\\.\\d]/g,'')\" value='%s'>", index, value);
                            return html;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].travelCost!=null&&value[i].travelCost!=''){
                                    sumBalance += parseFloat(value[i].travelCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(2);
                            return  sumBalance;
                        }
                    },{
                        field: 'comCost',
                        align: 'center',
                        title: '通讯费',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required   min='0' type='number'name='teachCost[%s].comCost' onchange='ysSum()' onkeyup=\"this.value=this.value.replace(/[^\\.\\d]/g,'')\" value='%s'>", index, value);
                            return html;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].comCost!=null&&value[i].comCost!=''){
                                    sumBalance += parseFloat(value[i].comCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(2);
                            return  sumBalance;
                        }
                    },{
                        field: 'laborCost',
                        align: 'center',
                        title: '人工费',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  required  min='0' type='number'name='teachCost[%s].laborCost'  onchange='ysSum()' onkeyup=\"this.value=this.value.replace(/[^\\.\\d]/g,'')\" value='%s'>", index, value);
                            return html;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].laborCost!=null&&value[i].laborCost!=''){
                                    sumBalance += parseFloat(value[i].laborCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(2);
                            return  sumBalance;
                        }
                    },{
                        field: 'otherCost',
                        align: 'center',
                        title: '其他费用',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required   min='0' type='number'name='teachCost[%s].otherCost' onchange='ysSum()' onkeyup=\"this.value=this.value.replace(/[^\\.\\d]/g,'')\" value='%s'>", index, value);
                            return html;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].otherCost!=null&&value[i].otherCost!=''){
                                    sumBalance += parseFloat(value[i].otherCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(2);
                            return  sumBalance;
                        }
                    }
                ]
            };
            $(".no-records-found").remove();
            $.table.init(options);
        });


        function addTeachCostolumn() {
            var row = {
                busiYear: "",
                travelCost: "",
                comCost: "",
                laborCost: "",
                otherCost: "",

            }
            sub.addColumn(row,"bootstrap-table-ys");
        }

        function delCost(){
            sub.delColumn();
            ysSum();
        }

        //自动计算预算
        function ysSum(){
            //纵向和
            var len =$("#bootstrap-table-ys").find("tr").length;
            var travelCostTotal=0.0;
            var comCostTotal=0.0;
            var laborCostTotal=0.0;
            var otherCostTotal=0.0;
            var countMoneyTotal=0.0;
            for(var i=0;i<len-2;i++){
                //差旅费
                var travelCostMoney=$("input[name='teachCost["+i+"].travelCost']").val();
                if(travelCostMoney==null||travelCostMoney==''){
                    travelCostMoney=0.0;
                }
                travelCostTotal+=parseFloat(travelCostMoney);

                //通讯费
                var comCostMoney=$("input[name='teachCost["+i+"].comCost']").val();
                if(comCostMoney==null||comCostMoney==''){
                    comCostMoney=0.0;
                }
                comCostTotal+=parseFloat(comCostMoney);

                //人工费
                var laborCostMoney=$("input[name='teachCost["+i+"].laborCost']").val();
                if(laborCostMoney==null||laborCostMoney==''){
                    laborCostMoney=0.0;
                }
                laborCostTotal+=parseFloat(laborCostMoney);

                //其他费用
                var otherCostMoney=$("input[name='teachCost["+i+"].otherCost']").val();
                if(otherCostMoney==null||otherCostMoney==''){
                    otherCostMoney=0.0;
                }
                otherCostTotal+=parseFloat(otherCostMoney);

                //总和
                countMoneyTotal=parseFloat(travelCostTotal)+parseFloat(comCostTotal)+parseFloat(laborCostTotal)+ parseFloat(otherCostTotal);
                if(isNaN(countMoneyTotal)){
                    countMoneyTotal=0.0;
                }
            }

            $("#bootstrap-table-ys").find("tfoot").find("th").eq(3).find(".th-inner").html(travelCostTotal.toFixed(2));
            $("#bootstrap-table-ys").find("tfoot").find("th").eq(4).find(".th-inner").html(comCostTotal.toFixed(2));
            $("#bootstrap-table-ys").find("tfoot").find("th").eq(5).find(".th-inner").html(laborCostTotal.toFixed(2));
            $("#bootstrap-table-ys").find("tfoot").find("th").eq(6).find(".th-inner").html(otherCostTotal.toFixed(2));
            $("#costTotal").text(countMoneyTotal.toFixed(2));
            $("input[name='payTotal']").val(countMoneyTotal.toFixed(2));
            $("#totala").text(countMoneyTotal.toFixed(2));

            changeTotal();
        }

        function footerStyle(column) {
            return {
                budgetPerson: {
                    css: {  'font-weight': 'blod' }
                },
                index: {
                    css: {  'font-weight': 'blod' }
                }
            }[column.field]
        }

        function changeTotal(){
            var a = $("input[name='payTotal']").val();
            var b = $("input[name='payManxs']").val();
            /*(总计*(1+12/100)*/
            var total = parseFloat(a)*(1+parseFloat(b)/100);
            $("#proTotal").val(total.toFixed(2));
            $("#projectTotal").html(total.toFixed(2));
        }

    </script>
</body>
</html>