<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增任务书变更单')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div th:if="${taskId!=null&&taskId!=''}" class="form-group" th:include="include :: step(approveKind='ZZZC_CHANGE',currentNode=${activityCode})"></div><br/>

        <form class="form-horizontal m" id="form-teachChangetask-add" th:object="${change}">

            <input name="bizId" th:value="${main.mainId}" type="hidden"/>
            <input name="chtaskId" th:value="${change.chtaskId}" type="hidden"/>
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
            <input id="transitionKey" name="transitionKey" type="hidden">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目名称：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.projectName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">项目编号：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                            <label class="col-sm-2 control-label">项目类型：</label>
                                        <div class="col-sm-4">
                                            <div th:include="/component/radio :: init(id='projectType',name='projectType',
                                                businessType='KTTG',dictCode='projectType',value=${main.projectType} ,see=true)"></div>
                                        </div>
                                            <label class="col-sm-2 control-label">项目范围：</label>
                                        <div class="col-sm-4">
                                            <div th:include="/component/radio :: init(id='projectArea',name='projectArea',
                                                businessType='KTTG',dictCode='projectArea',value=${main.projectArea} ,see=true)"></div>
                                        </div>

                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">推广单位部门：</label>
                                        <div class="col-sm-4">
                                            <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.tgfDeptCode)}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">受让单位部门：</label>
                                        <div class="col-sm-4">
                                            <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.srfDeptCode)}"></div>

                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                            <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='tgfFzrCode',userNameId='tgfFzrName',value=${main.tgfFzrCode},see=true)"></div>

                                        <label class="col-sm-2 control-label">联系电话：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfXmfzrTel}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">受让方项目负责人：</label>

                                            <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='srfFzrCode',userNameId='srfFzrName',value=${main.srfFzrCode},see=true)"></div>

                                        <label class="col-sm-2 control-label">联系电话：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.srfTel}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row ">
                                    <div class="form-group">
                                            <label class="col-sm-2 control-label is-required">更改申请类型：</label>
                                            <div class="col-sm-4">
                                            <div th:include="/component/select :: init(id='changeType', name='changeType',
                                 businessType='KTTG', dictCode='changType',value=${change.changeType},isfirst=true,isrequired=true)"></div>
                                            </div>
                                        <label class="col-sm-2 control-label">申请日期：</label>
                                        <div class="col-sm-4">
                                            <div th:if="${change.applyDate==''||change.applyDate==null}" class="form-control-static">更改单申请提交时自动生成</div>
                                            <div th:if="${change.applyDate!=''||change.applyDate!=null}" class="form-control-static" th:text="${change.applyDate}"></div>
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        <!--折叠区域end-->
                    </div>
          </div>

                                <!--框-->
                                <div class="panel-group" id="accordion1" role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#ck" aria-expanded="false" class="collapsed">项目材料
                                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                                <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                                            </h4>
                                        </div>

                                        <!--折叠区域-->
                                        <div id="ck" class="panel-collapse collapse in" aria-expanded="false">
                                            <div class="panel-body" >
                                                <div style="margin-left: 100px;">
                                                <div class="form-group">
                                                    <div th:if="${main.projectSource eq 'gdxqsb'}" class="col-sm-2 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',ctx+'zzlx/need/detailM?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                                                    <div th:if="${main.projectSource eq 'ndjh'}" class="col-sm-2 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',[[${@environment.getProperty('app-context.ctxKY')}]] +'web/KYKTTGPD01?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-sm-12">点击<a class="form_list_a" th:onclick="$.modal.openTab('技术附件',ctx+'zzlx/teachAtt/openAtt/'+[[${main.mainId}]])">此处</a>查看技术附件</label>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-sm-12">点击<a class="form_list_a" th:onclick="$.modal.openTab('定价评审表',ctx+'zzlx/teachFix/openFix/'+[[${main.mainId}]])">此处</a>查看定价评审表</label>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-sm-12">点击<a class="form_list_a" th:onclick="$.modal.openTab('项目合同',ctx+'zzlx/teachContract/openContract/'+[[${main.mainId}]])">此处</a>查看项目合同</label>
                                                </div>
                                                <div class="form-group">
                                                    <label class="col-sm-12">点击<a class="form_list_a" th:onclick="$.modal.openTab('计划任务书',ctx+'zzlx/teachPlantask/openPlan/'+[[${main.mainId}]])">此处</a>查看计划任务书</label>
                                                </div>
                                                </div>
                                            </div>
                                        </div>
                                        <!--折叠区域end-->
                                    </div>
                                </div>


                                <div class="panel-group" id="accordion10" role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#jdjh" class="collapsed">
                                                    申请更改内容及原因
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="jdjh" class="panel-collapse collapse in">
                                            <div class="panel-body">
                                                <div class="form-group">
                                                    <div class="col-sm-12">
                                                        <button type="button" class="btn btn-white btn-sm" onclick="addChangeColumn()"><i class="fa fa-plus"> 增加</i></button>
                                                        <button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>
                                                        <div class="col-sm-12 select-table ">
                                                            <table id="bootstrap-table-plan"></table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xmfw" class="collapsed">
                                                    附件
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="xmfw" class="panel-collapse collapse in">
                                            <div class="form-group">
                                                <label class="col-sm-2 control-label">附件上传：</label>
                                                <div class="col-sm-10">
                                                    <div th:include="/component/attachment :: init(display='none',name='bgfjId',id='bgfjId',sourceId=${change.chtaskId},sourceModule='CHANGE_FJ')">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

        </form>
        <div class="m">
            <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
        </div>
    </div>
    <div class="row">
		<div class="toolbar toolbar-bottom" role="toolbar" >

			<button type="button" class="btn btn-primary"
				onclick="saveHandler()">
				<i class="fa fa-check"></i>保 存
			</button>

			<button type="button" class="btn btn-primary" th:if="${taskId==''||taskId==null}"
                  onclick="submitHandler()">
                 <i class="fa fa-check"></i>提交
            </button>
                <th:block th:if="${taskId!=null}" th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=returnSubmit)"/>

			<button type="button" class="btn btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>
	</div>
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="component/wfCommentList::init(processInstanceId=${processInstanceId})"></div>


    <script th:inline="javascript">
        var prefix = ctx + "zzgc/teachChangetask"

        $("#form-teachChangetask-add").validate({
            focusCleanup: true
        });

        function saveHandler() {
            var config = {
                url: prefix + "/add",
                type: "post",
                dataType: "json",
                data: $('#form-teachChangetask-add').serialize(),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $("#chtaskId").val(result.data.chtaskId);
                    $.modal.alertSuccess(result.msg);
                    $.modal.closeLoading();
                }
            };
            $.ajax(config)
        }
        function submitHandler(){
            $.modal.confirm("确认提交吗？", function () {
                if ($.validate.form()) {
                    $.operate.saveTabAlert(prefix + "/addSubmit",$('#form-teachChangetask-add').serialize());
                }
            })
        }

        function returnSubmit(transitionKey){
            $('#transitionKey').val(transitionKey);
            $.modal.confirm("确认提交吗？", function () {
                if ($.validate.form()) {
                    $.operate.saveTabAlert(prefix + "/submitWF",$('#form-teachChangetask-add').serialize());
                }
            })
        }


        $(function() {
            var options = {
                url: ctx + "zzgc/teachChangetasksub/teachSubList?chtaskId="+[[${change.chtaskId}]],
                id:"bootstrap-table-plan",
                toolbar:"toolbar-plan",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: true
                },
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='changeTask[%s].index' value='%s'>", index, $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='changeTask[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'planTaskContent',
                        align: 'center',
                        title: '计划任务书内容',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required  type='text' required name='changeTask[%s].planTaskContent' value='%s'>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'changeAfterContent',
                        align: 'center',
                        title: '拟更改的内容',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  required  type='text' required  name='changeTask[%s].changeAfterContent'  value='%s' >", index, value);
                            return html;
                        }
                    },{
                        field: 'changeReason',
                        align: 'center',
                        title: '更改原因',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required   type='text' required  name='changeTask[%s].changeReason' value='%s'>", index, value);
                            return html;
                        }
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });

        function addChangeColumn(){
            var row = {
                planTaskContent: "",
                changeAfterContent: "",
                changeReason: "",
            }
            sub.addColumn(row,"bootstrap-table-plan");
        }


    </script>
</body>
</html>