<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增项目月报表')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <div class="form-group" th:include="include :: step(approveKind='ZZZC_MONTH',currentNode=${activityCode})"></div><br/>

    <form class="form-horizontal m" id="form-teachMonth-add" th:object="${month}">
        <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
        <input id="planId" name="planId"  th:value="${plan.taskId}" type="hidden">
        <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
        <input id="monthId" name="monthId"  th:value="${month.monthId}" type="hidden">
        <input id="mainId" name="mainId"  th:value="${main.mainId}" type="hidden">
        <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
        <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
        <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
        <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">



        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目名称：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${main.projectName}"></div>
                                </div>
                                <label class="col-sm-2 control-label">项目编号：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">推广单位部门：</label>
                                <div class="col-sm-4">
                                    <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.tgfDeptCode)}"></div>
                                </div>
                                <label class="col-sm-2 control-label">受让单位部门：</label>
                                <div class="col-sm-4">
                                    <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.srfDeptCode)}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='tgfFzrCode',userNameId='tgfFzrName',value=${main.tgfFzrCode},see=true)"></div>

                                <label class="col-sm-2 control-label">联系电话：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${main.tgfXmfzrTel}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">受让方项目负责人：</label>
                                <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='srfFzrCode',userNameId='srfFzrName',value=${main.srfFzrCode},see=true)"></div>

                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">编写日期：</label>
                                <div th:if="${month.monthWriteDate==null || month.monthWriteDate==''}" class="col-sm-4" th:include="/component/date :: init(name='monthWriteDate',id='monthWriteDate',strValue=${downDate},isrequired=true)"></div>
                                <div th:if="${month.monthWriteDate!=null && month.monthWriteDate!=''}" class="col-sm-4" th:include="/component/date :: init(name='monthWriteDate',id='monthWriteDate',strValue=${month.monthWriteDate},isrequired=true)"></div>
                                <label class="col-sm-2 control-label">生成日期：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${month.monthCreateDate}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-group"><label class="col-sm-2 control-label"><b>项目起止日期</b></label></div>
                        </div>
                        <div class="row">
                            <div class="form-group ">
                                <label class="col-sm-2 control-label">项目开始日期：</label>
                                <div class="col-sm-3">
                                    <div class="form-control-static" th:utext="${main.projectStartDate}"></div>
                                </div>
                                <label class="col-sm-3 control-label">项目结束日期：</label>
                                <div class="col-sm-3">
                                    <div class="form-control-static" th:utext="${main.projectEndDate}"></div>
                                </div>
                            </div>
                        </div>

                </div>
            </div>
        </div>
</div>

        <div class="panel-group" id="accordion10" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" href="#jdjh" class="collapsed">
                            进度计划
                            <span class="pull-right">
                <i class="fa fa-chevron-down"></i>
            </span>
                        </a>
                    </h4>
                </div>
                <div id="jdjh" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <div class="col-sm-12 select-table ">
                                    <table id="bootstrap-table-plan"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" id="accordion10" role="tablist" aria-multiselectable="true">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" href="#zbqk" class="collapsed">
                        本月完成指标情况
                        <span class="pull-right">
                <i class="fa fa-chevron-down"></i>
            </span>
                    </a>
                </h4>
            </div>
            <div id="zbqk" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="form-group">
                        <div class="col-sm-12">
                            <button class="btn btn-white btn-sm" onclick="addColumn()" type="button"><i class="fa fa-plus"> 增加</i></button>
                            <button class="btn btn-white btn-sm" onclick="sub.delColumn()" type="button"><i class="fa fa-minus"> 删除</i></button>
                            <div class="col-sm-12 select-table ">
                                <table id="bootstrap-tableQ"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
        <div class="panel-group" id="accordion12"  role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" href="#xtsy" class="collapsed">
                            <span style="color: red">*&nbsp;</span>本月项目工作及进度
                            <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                        </a>
                    </h4>
                </div>
                <div id="xtsy" class="panel-collapse collapse in">
                    <div class="form-group">
                        <div class="col-sm-12 control-label">
                            <textarea name="monthWork" th:utext="${month.monthWork}"  class="form-control" rows="8" required></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group" id="accordion5"  role="tablist" aria-multiselectable="true" th:if="${month.monthType eq 'srf'}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" href="#tjcx" class="collapsed">
                            <span style="color: red">*&nbsp;</span>推进成效
                            <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                        </a>
                    </h4>
                </div>
                <div id="tjcx" class="panel-collapse collapse in">
                    <div class="form-group">
                        <div class="col-sm-12 control-label">
                            <textarea name="monthPromote" th:utext="${month.monthPromote}"  class="form-control" rows="8" required></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true" th:if="${month.monthType eq 'tgf'}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" href="#jhap" class="collapsed">
                            <span style="color: red">*&nbsp;</span>下月工作计划安排
                            <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                        </a>
                    </h4>
                </div>
                <div id="jhap" class="panel-collapse collapse in">
                    <div class="form-group">
                        <div class="col-sm-12 control-label">
                            <textarea name="monthNextplan" th:utext="${month.monthNextplan}"  class="form-control" rows="8" required></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group" id="accordion3"  role="tablist" aria-multiselectable="true" th:if="${month.monthType eq 'srf'}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" href="#jjff" class="collapsed">
                            <span style="color: red">*&nbsp;</span>存在问题及解决办法
                            <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                        </a>
                    </h4>
                </div>
                <div id="jjff" class="panel-collapse collapse in">
                    <div class="form-group">
                        <div class="col-sm-12 control-label">
                            <textarea name="monthSolunction" th:utext="${month.monthSolunction}"  class="form-control" rows="8" required></textarea>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group" id="accordion11"  role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" href="#ysbz" class="collapsed">
                            <span style="color: red">*&nbsp;</span>相关附件
                            <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                        </a>
                    </h4>
                </div>
                <div id="ysbz" class="panel-collapse collapse in">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">上传附件：</label>
                            <div class="col-sm-10">
                                <div th:if="${month.monthType eq 'tgf'}" th:include="/component/attachment :: init(display='none',name='monthFj',id='monthFj',sourceId=${month.monthId},sourceModule='MONTH_FJ')">
                                </div>
                                <div th:if="${month.monthType eq 'srf'}" th:include="/component/attachment :: init(display='none',name='monthFj',id='monthFj',sourceId=${month.monthId},sourceModule='MONTH_FJ',isrequired=true)">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
</form>
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
    </div>
</div>
<div class="row">
    <div class="toolbar toolbar-bottom" role="toolbar" >
        &nbsp;
        <button type="button" class="btn btn-primary"
                onclick="saveHandler()">
            <i class="fa fa-check"></i>暂 存
        </button>

        <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

        <button type="button" class="btn btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>
<div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
     th:include="component/wfCommentList::init(processInstanceId=${processInstanceId})"></div>



<script th:inline="javascript">
    var prefix = ctx + "zzgc/teachMonth"

    $("#form-teachMonth-add").validate({
        focusCleanup: true
    });
    $("input[name='monthWriteDate']").datetimepicker({
        format : "yyyy-mm-dd",
        minView : "month",
        autoclose : true
    }).on('keypress paste', function (e) {
        e.preventDefault();
        return false;
    });

    function saveHandler() {
        var config = {
            url: prefix + "/add",
            type: "post",
            dataType: "json",
            data: $('#form-teachMonth-add').serialize(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                $("#monthId").val(result.data.monthId);
                $.modal.alertSuccess(result.msg);
                $.modal.closeLoading();
            }
        };
        $.ajax(config)
    }

    function submitHandler() {
        $.modal.confirm("确认提交吗？", function () {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/submitWF", $('#form-teachMonth-add').serialize());
            }
        })
    }

    //流程跟踪
    function workFlowProcess() {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + [[${processInstanceId}]]);
    }

    /***********************************进度计划表*******************************************/
    $(function() {
        var options = {
            url: ctx + "zzgg/teachPlan/teachPlanList?bizId="+[[${plan.taskId}]],
            id:"bootstrap-table-plan",
            toolbar:"toolbar-plan",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='teachPlans[%s].orderNum' value='%s'>", $.table.serialNumber(index));
                        var columnId = $.common.sprintf("<input type='hidden' name='teachPlans[%s].planId'  value='%s'>", index, row.id);
                        return columnIndex + $.table.serialNumber(index) + columnId;
                    }
                },
                {
                    field: 'beginTime',
                    align: 'center',
                    title: '阶段开始时间'
                },
                {
                    field: 'endTime',
                    align: 'center',
                    title: '阶段结束时间'
                },{
                    field: 'stageName',
                    align: 'center',
                    title: '阶段名称'
                },{
                    field: 'stageTarget',
                    align: 'center',
                    title: '阶段目标'
                },{
                    field: 'peoply',
                    align: 'center',
                    title: '人数'
                },{
                    field: 'days',
                    align: 'center',
                    title: '天数'
                },{
                    field: 'total',
                    align: 'center',
                    title: '分项合计'
                }
            ]
        };
        $.table.init(options);
        $(".no-records-found").remove();
    });

    /**
     * 初始化数据
     */
    $(function () {
        var optionsQ = {// 目前技术经济指标、产品、质量等情况
            id: "bootstrap-tableQ",
            url: ctx + "zzgg/target/list",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.bizId = [[${month.monthId}]];
                search.zbType = "Q";
                return search;
            },
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='targetQ[%s].index' value='%s'>", index, $.table.serialNumber(index));
                        var columnId = $.common.sprintf("<input type='hidden' name='targetQ[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                        return columnIndex + $.table.serialNumber(index) + columnId;
                    }
                },
                {
                    field: 'zbName',
                    align: 'center',
                    title: '指标名称',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='targetQ[%s].zbName' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'zbNum',
                    align: 'center',
                    title: '指标数值',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='targetQ[%s].zbNum' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'zbUnit',
                    align: 'center',
                    title: '指标单位',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='targetQ[%s].zbUnit' value='%s'>", index, value);
                        return html;
                    }
                }]
        };
        $.table.init(optionsQ);
    });

    function addColumn() {
        var row = {
            index: "",
            zbName: "",
            zbNum: "",
            zbUnit: ""
        }
        sub.addColumn(row, "bootstrap-tableQ");
    }

</script>
</body>
</html>