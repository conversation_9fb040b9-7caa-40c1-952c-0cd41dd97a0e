<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('任务书变更记录列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="row form-group">
                <div class="col-sm-4">
                    <label class="col-sm-3 control-label">项目编号：</label>
                    <div class="col-sm-8">
                        <input class="form-control width100" name="projectNumLike" type="text">
                    </div>
                </div>

                <div class="col-sm-4">
                    <label class="col-sm-3 control-label">项目名称：</label>
                    <div class="col-sm-8">
                        <input class="form-control width100" name="projectNameLike" type="text">
                    </div>
                </div>

        </div>
                <div hidden="hidden" th:include="/component/select :: init(id='status', name='status',businessType='KTTG', dictCode='status')"></div>
                <div hidden="hidden" th:include="/component/select :: init(id='type', name='type',
                                 businessType='KTTG', dictCode='changType')"></div>

                <div class="select-list" style="float: right">
                    <ul>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

</div>


<script th:inline="javascript">
    var prefix = ctx + "zzgc/teachChangetask";
    var prefix2 = ctx + "zzjt/benefit"; //效益评审
    var prefix3 = ctx + "zzjt/afterAssess"; //项目后评估
    var prefix4 = ctx + "zznd/planNotice";  //年度计划启动通知

    var prefix1 = ctx + "zzgc/teachMonth";  //月报

    $(function () {
        var options = {
            url: prefix + "/changeList",
            createUrl: prefix + "/projectList",
            detailUrl: prefix + "/detail/{id}",
            updateUrl: prefix + "/history/{id}",
            modalName: "任务书变更",
            uniqueId: "chtaskId",
            queryParams: queryParams,
            columns: [
                {
                    field: 'updateDate',
                    title: '更改时间'
                },
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'changeType',
                    title: '更改申请类型',
                    formatter: function (value, row, index) {
                        return $("#type option[value='"+value+"']").text();
                    }
                },
                {
                    field: 'applyDate',
                    title: '申请时间'
                },
                {
                    field: 'currentOperator',
                    title: '当前操作人'
                },
                {
                    field: 'currentActivityName',
                    title: '当前状态'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.chtaskId + '\')"><i class="fa fa-eye"></i>查看</a>');
                        if (row.extra2 == 'end') {
                          actions.push('<a class="btn btn-primary btn-xs " href="javascript:void(0)" onclick="openHis(\'' + row.chtaskId + '\')"><i class="fa fa-eye"></i>计划任务书历史</a>');
                        }
                        return actions.join('');
                    }
                }
                ]
        };
        $.table.init(options);
    });
    function queryParams(params) {
        var search = $.table.queryParams(params);
        return search;
    };

    function startChange(mainId){
        $.modal.confirm("确认要下发变更申请吗？", function () {
            $.operate.saveTabAlert(prefix + "/startChange/"+mainId, {});
        })
    }

    function doStart(){
        $.modal.confirm("确认提交吗？", function () {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix3 + "/doStart");
            }
        })
    }

    function openHis(chtaskId){
        $.modal.openTab("计划任务书历史详情", prefix + "/history/"+chtaskId);
    };


</script>
</body>
</html>