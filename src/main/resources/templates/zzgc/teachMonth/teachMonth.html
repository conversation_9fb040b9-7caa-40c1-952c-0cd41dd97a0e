<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('项目月报表列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>项目编号：</label>
                                <input class="form-control" type="text" name="projectNumLike"/>
                            </li>
                            <li>
                                <label>项目名称：</label>
                                <input class="form-control" type="text" name="projectNameLike"/>
                            </li>
                            <li>
                                <label>项目负责人：</label>
                                <input class="form-control" type="text" name="tgfFzrNameLike"/>
                            </li>
                            <li>
                                <label>项目主管：</label>
                                <input class="form-control" type="text" name="projectZgNameLike"/>
                            </li>
                            <li>
                                <label>填报状态：</label>
                                <select class="form-control" name="monthStatus">
                                    <option value="">选择</option>
                                    <option value="未填写">未填写</option>
                                    <option value="已填写">已填写</option>
                                    <option value="未及时填写">未及时填写</option>
                                    <option value="未填写已结束">未填写已结束</option>
                                </select>
                            </li>
                            <li>
                                <label>推广单位部门：</label>
                                <div style="width: 30%;">
                                    <div th:include="/component/selectOrg :: init(orgCodeId='tgfDeptCode',orgNameId='tgfDeptName',selectType='S')"></div>
                                </div>
                            </li>
                            <li>
                                <label>受让单位部门：</label>
                                <div style="width: 30%">
                                    <div th:include="/component/selectOrg :: init(orgCodeId='srfDeptCode',orgNameId='srfDeptName',selectType='S')"></div>
                                </div>
                            </li>
                            <li>
                                <label>月报生成日期：</label>
                                <div style="width: 15%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='createDateMin',id='createDateMin')"/>
                                </div>
                                <span>~</span>
                                <div style="width: 15%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='createDateMax',id='createDateMax')"/>
                                </div>
                            </li>


                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
               <!-- <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzgc/teachMonth";
        var projectStatus = [[${@dict.getDictList('KTTG','projectStatus')}]]; //项目状态

        $(function() {
            var options = {
                url: prefix + "/reportList",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目月报填写概况表",
                columns: [{
                    checkbox: false
                },
                {
                    field: 'monthId',
                    title: '月报主键',
                    visible: false
                },
                {
                    field: 'bizId',
                    title: '业务主键',
                    visible: false
                },
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'monthCreateDate',
                    title: '月度'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'tgfDeptName',
                    title: '推广单位部门'
                },
                {
                    field: 'srfDeptName',
                    title: '受让单位部门'
                },
                {
                    field: 'monthStatus',
                    title: '填报状态'
                },
                {
                    field: 'tgfFzrName',
                    title: '项目负责人'
                },
                {
                    field: 'tgfXmzgName',
                    title: '项目主管'
                },
                {
                    field: 'srfFzrName',
                    title: '受让方项目负责人'
                },
                {
                    field: 'projectStatus',
                    title: '项目状态'
                },
                {
                    field: 'tgfXmfzrTel',
                    title: '联系电话'
                }
                ]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>