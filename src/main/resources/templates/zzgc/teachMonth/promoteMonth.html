<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('项目推进月度跟踪')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId" class="form-horizontal" style="width: 99%;">
                    <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目主管:</label>
                        <div class="col-sm-4">
                            <select class="form-control" id="tgfXmzgCode" name="tgfXmzgCode">
                                <option value="">选择</option>
                                <th:block th:each="cy,iterStat : ${xmzgList}">
                                    <option th:value="${cy.xmzgCode}" th:text="${cy.xmzgName}"></option>
                                </th:block>
                            </select>
                        </div>
                        <label class="col-sm-2 control-label">查询月度:</label>
                        <div class="col-sm-4">
                            <th:block th:include="/component/date::init(name='monthCreateDate',id='monthCreateDate',format='yyyy-mm')"/>
                        </div>
                    </div>
                    </div>
                    <div class="select-list" style="float: right">
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="searchParam()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
               <!-- <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzgc/teachMonth";
        var projectStatus = [[${@dict.getDictList('KTTG','projectStatus')}]]; //项目状态

        $(function() {
            var options = {
                url: prefix + "/genZongList",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/exportGen",
                firstLoad: false,
                modalName: "项目推进月度跟踪",
                columns: [
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'monthWork',
                    title: '推进事项'
                },
                {
                    field: 'monthPromote',
                    title: '推进成效'
                },
                {
                    field: 'zbName',
                    title: '当月指标'
                },
                {
                    field: 'zbName',
                    title: '原指标情况'
                },
                {
                    field: 'zbName',
                    title: '目标指标'
                }
                ]
            };
            $.table.init(options);
        });

        function searchParam(){
            if($("#tgfXmzgCode").val()==null||$("#tgfXmzgCode").val()==""){
                $.modal.alertWarning("项目主管必填！");
                return;
            }
            if($("#monthCreateDate").val()==null||$("#monthCreateDate").val()==""){
                $.modal.alertWarning("查询月度必填！");
                return;
            }
            $.table.search()
        }
    </script>
</body>
</html>