<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('费用估算列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>业务主键：</label>
                                <input type="text" name="bizId"/>
                            </li>
                            <li>
                                <label>排序：</label>
                                <input type="text" name="orderNum"/>
                            </li>
                            <li>
                                <label>年度：</label>
                                <input type="text" name="busiYear"/>
                            </li>
                            <li>
                                <label>差旅费：</label>
                                <input type="text" name="travelCost"/>
                            </li>
                            <li>
                                <label>通讯费：</label>
                                <input type="text" name="comCost"/>
                            </li>
                            <li>
                                <label>人工费：</label>
                                <input type="text" name="laborCost"/>
                            </li>
                            <li>
                                <label>其他费用：</label>
                                <input type="text" name="otherCost"/>
                            </li>
                            <li>
                                <label>资料费：</label>
                                <input type="text" name="zlfyCostOld"/>
                            </li>
                            <li>
                                <label>检测费：</label>
                                <input type="text" name="jcfyCostOld"/>
                            </li>
                            <li>
                                <label>费用版本：</label>
                                <input type="text" name="versionFy"/>
                            </li>
                            <li>
                                <label>类型：</label>
                                <select name="costType" th:with="type=${@dict.getDictList('${dictType}')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.paraName}" th:value="${dict.paraValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>扩展字段1：</label>
                                <input type="text" name="extra1"/>
                            </li>
                            <li>
                                <label>扩展字段2：</label>
                                <input type="text" name="extra2"/>
                            </li>
                            <li>
                                <label>扩展字段3：</label>
                                <input type="text" name="extra3"/>
                            </li>
                            <li>
                                <label>扩展字段4：</label>
                                <input type="text" name="extra4"/>
                            </li>
                            <li>
                                <label>删除状态：</label>
                                <select name="delStatus" th:with="type=${@dict.getDictList('${dictType}')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.paraName}" th:value="${dict.paraValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzgg/teachCost";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "费用估算",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'costId',
                    title: '项目费用主键',
                    visible: false
                },
                {
                    field: 'bizId',
                    title: '业务主键'
                },
                {
                    field: 'orderNum',
                    title: '排序'
                },
                {
                    field: 'busiYear',
                    title: '年度'
                },
                {
                    field: 'travelCost',
                    title: '差旅费'
                },
                {
                    field: 'comCost',
                    title: '通讯费'
                },
                {
                    field: 'laborCost',
                    title: '人工费'
                },
                {
                    field: 'otherCost',
                    title: '其他费用'
                },
                {
                    field: 'zlfyCostOld',
                    title: '资料费'
                },
                {
                    field: 'jcfyCostOld',
                    title: '检测费'
                },
                {
                    field: 'versionFy',
                    title: '费用版本'
                },
                {
                    field: 'costType',
                    title: '类型'
                },
                {
                    field: 'extra1',
                    title: '扩展字段1'
                },
                {
                    field: 'extra2',
                    title: '扩展字段2'
                },
                {
                    field: 'extra3',
                    title: '扩展字段3'
                },
                {
                    field: 'extra4',
                    title: '扩展字段4'
                },
                {
                    field: 'delStatus',
                    title: '删除状态'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    field: 'deleteUserLabel',
                    title: '删除人'
                },
                {
                    field: 'deleteDate',
                    title: '删除时间'
                },
                {
                    field: 'recordVersion',
                    title: '版本号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.costId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.costId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>