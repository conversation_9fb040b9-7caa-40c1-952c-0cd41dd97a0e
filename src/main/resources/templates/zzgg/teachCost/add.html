<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增费用估算')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-teachCost-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">业务主键：</label>
                <div class="col-sm-8">
                    <input name="bizId" class="form-control" type="text" required>
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">排序：</label>
                <div class="col-sm-8">
                    <input name="orderNum" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">年度：</label>
                <div class="col-sm-8">
                    <input name="busiYear" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">差旅费：</label>
                <div class="col-sm-8">
                    <input name="travelCost" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">通讯费：</label>
                <div class="col-sm-8">
                    <input name="comCost" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">人工费：</label>
                <div class="col-sm-8">
                    <input name="laborCost" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">其他费用：</label>
                <div class="col-sm-8">
                    <input name="otherCost" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">资料费：</label>
                <div class="col-sm-8">
                    <input name="zlfyCostOld" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">检测费：</label>
                <div class="col-sm-8">
                    <input name="jcfyCostOld" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">费用版本：</label>
                <div class="col-sm-8">
                    <input name="versionFy" class="form-control" type="text">
                </div>
            </div>



      <div class="form-group" th:include="include :: initSelectBox(id='costType', name='costType',dictType='${dictType}', labelName='类型')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="extra1" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="extra2" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段3：</label>
                <div class="col-sm-8">
                    <input name="extra3" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段4：</label>
                <div class="col-sm-8">
                    <input name="extra4" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',dictType='${dictType}' ,labelName='删除状态')">
            </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>


    <script th:inline="javascript">
        var prefix = ctx + "zzgg/teachCost"

        $("#form-teachCost-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/add", $('#form-teachCost-add').serialize());
            }
        }

    </script>
</body>
</html>