<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('项目成员列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>业务主键：</label>
                                <input type="text" name="bizId"/>
                            </li>
                            <li>
                                <label>排序：</label>
                                <input type="text" name="orderNum"/>
                            </li>
                            <li>
                                <label>工号：</label>
                                <input type="text" name="userId"/>
                            </li>
                            <li>
                                <label>姓名：</label>
                                <input type="text" name="userName"/>
                            </li>
                            <li>
                                <label>岗位：</label>
                                <input type="text" name="position"/>
                            </li>
                            <li>
                                <label>技术特长：</label>
                                <input type="text" name="point"/>
                            </li>
                            <li>
                                <label>主要任务：</label>
                                <input type="text" name="job"/>
                            </li>
                            <li>
                                <label>项目角色：</label>
                                <input type="text" name="role"/>
                            </li>
                            <li>
                                <label>类型：</label>
                                <select name="peType" th:with="type=${@dict.getDictList('${dictType}')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.paraName}" th:value="${dict.paraValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>扩展字段1：</label>
                                <input type="text" name="extra1"/>
                            </li>
                            <li>
                                <label>扩展字段2：</label>
                                <input type="text" name="extra2"/>
                            </li>
                            <li>
                                <label>扩展字段3：</label>
                                <input type="text" name="extra3"/>
                            </li>
                            <li>
                                <label>扩展字段4：</label>
                                <input type="text" name="extra4"/>
                            </li>
                            <li>
                                <label>删除状态：</label>
                                <select name="delStatus" th:with="type=${@dict.getDictList('${dictType}')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.paraName}" th:value="${dict.paraValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzgg/teachPropeople";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目成员",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'peId',
                    title: '项目成员主键',
                    visible: false
                },
                {
                    field: 'bizId',
                    title: '业务主键'
                },
                {
                    field: 'orderNum',
                    title: '排序'
                },
                {
                    field: 'userId',
                    title: '工号'
                },
                {
                    field: 'userName',
                    title: '姓名'
                },
                {
                    field: 'position',
                    title: '岗位'
                },
                {
                    field: 'point',
                    title: '技术特长'
                },
                {
                    field: 'job',
                    title: '主要任务'
                },
                {
                    field: 'role',
                    title: '项目角色'
                },
                {
                    field: 'peType',
                    title: '类型'
                },
                {
                    field: 'extra1',
                    title: '扩展字段1'
                },
                {
                    field: 'extra2',
                    title: '扩展字段2'
                },
                {
                    field: 'extra3',
                    title: '扩展字段3'
                },
                {
                    field: 'extra4',
                    title: '扩展字段4'
                },
                {
                    field: 'delStatus',
                    title: '删除状态'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    field: 'deleteUserLabel',
                    title: '删除人'
                },
                {
                    field: 'deleteDate',
                    title: '删除时间'
                },
                {
                    field: 'recordVersion',
                    title: '版本号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.peId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.peId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>