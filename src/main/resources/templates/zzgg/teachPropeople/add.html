<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增项目成员')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-teachPropeople-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">业务主键：</label>
                <div class="col-sm-8">
                    <input name="bizId" class="form-control" type="text" required>
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">排序：</label>
                <div class="col-sm-8">
                    <input name="orderNum" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">工号：</label>
                <div class="col-sm-8">
                    <input name="userId" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">姓名：</label>
                <div class="col-sm-8">
                    <input name="userName" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">岗位：</label>
                <div class="col-sm-8">
                    <input name="position" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">技术特长：</label>
                <div class="col-sm-8">
                    <input name="point" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">主要任务：</label>
                <div class="col-sm-8">
                    <input name="job" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">项目角色：</label>
                <div class="col-sm-8">
                    <input name="role" class="form-control" type="text">
                </div>
            </div>



      <div class="form-group" th:include="include :: initSelectBox(id='peType', name='peType',dictType='${dictType}', labelName='类型')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="extra1" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="extra2" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段3：</label>
                <div class="col-sm-8">
                    <input name="extra3" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段4：</label>
                <div class="col-sm-8">
                    <input name="extra4" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',dictType='${dictType}' ,labelName='删除状态')">
            </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>


    <script th:inline="javascript">
        var prefix = ctx + "zzgg/teachPropeople"

        $("#form-teachPropeople-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/add", $('#form-teachPropeople-add').serialize());
            }
        }

    </script>
</body>
</html>