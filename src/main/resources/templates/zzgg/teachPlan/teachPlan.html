<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('项目进度计划列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>业务主键：</label>
                                <input type="text" name="bizId"/>
                            </li>
                            <li>
                                <label>排序：</label>
                                <input type="text" name="orderNum"/>
                            </li>
                            <li>
                                <label>阶段开始日期：</label>
                                <input type="text" name="beginTime"/>
                            </li>
                            <li>
                                <label>阶段结束日期：</label>
                                <input type="text" name="endTime"/>
                            </li>
                            <li>
                                <label>阶段名称：</label>
                                <input type="text" name="stageName"/>
                            </li>
                            <li>
                                <label>阶段目标：</label>
                                <input type="text" name="stageTarget"/>
                            </li>
                            <li>
                                <label>人数：</label>
                                <input type="text" name="peoply"/>
                            </li>
                            <li>
                                <label>天数：</label>
                                <input type="text" name="days"/>
                            </li>
                            <li>
                                <label>分项合计：</label>
                                <input type="text" name="total"/>
                            </li>
                            <li>
                                <label>扩展字段1：</label>
                                <input type="text" name="extra1"/>
                            </li>
                            <li>
                                <label>扩展字段2：</label>
                                <input type="text" name="extra2"/>
                            </li>
                            <li>
                                <label>扩展字段3：</label>
                                <input type="text" name="extra3"/>
                            </li>
                            <li>
                                <label>扩展字段4：</label>
                                <input type="text" name="extra4"/>
                            </li>
                            <li>
                                <label>删除状态：</label>
                                <select name="delStatus" th:with="type=${@dict.getDictList('${dictType}')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.paraName}" th:value="${dict.paraValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzgg/teachPlan";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目进度计划",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'planId',
                    title: '项目进度计划主键',
                    visible: false
                },
                {
                    field: 'bizId',
                    title: '业务主键'
                },
                {
                    field: 'orderNum',
                    title: '排序'
                },
                {
                    field: 'beginTime',
                    title: '阶段开始日期'
                },
                {
                    field: 'endTime',
                    title: '阶段结束日期'
                },
                {
                    field: 'stageName',
                    title: '阶段名称'
                },
                {
                    field: 'stageTarget',
                    title: '阶段目标'
                },
                {
                    field: 'peoply',
                    title: '人数'
                },
                {
                    field: 'days',
                    title: '天数'
                },
                {
                    field: 'total',
                    title: '分项合计'
                },
                {
                    field: 'extra1',
                    title: '扩展字段1'
                },
                {
                    field: 'extra2',
                    title: '扩展字段2'
                },
                {
                    field: 'extra3',
                    title: '扩展字段3'
                },
                {
                    field: 'extra4',
                    title: '扩展字段4'
                },
                {
                    field: 'delStatus',
                    title: '删除状态'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    field: 'deleteUserLabel',
                    title: '删除人'
                },
                {
                    field: 'deleteDate',
                    title: '删除时间'
                },
                {
                    field: 'recordVersion',
                    title: '版本号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.planId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.planId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>