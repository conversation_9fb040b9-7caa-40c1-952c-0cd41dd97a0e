<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增项目进度计划')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-teachPlan-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">业务主键：</label>
                <div class="col-sm-8">
                    <input name="bizId" class="form-control" type="text" required>
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">排序：</label>
                <div class="col-sm-8">
                    <input name="orderNum" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">阶段开始日期：</label>
                <div class="col-sm-8">
                    <input name="beginTime" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">阶段结束日期：</label>
                <div class="col-sm-8">
                    <input name="endTime" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">阶段名称：</label>
                <div class="col-sm-8">
                    <input name="stageName" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">阶段目标：</label>
                <div class="col-sm-8">
                    <input name="stageTarget" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">人数：</label>
                <div class="col-sm-8">
                    <input name="peoply" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">天数：</label>
                <div class="col-sm-8">
                    <input name="days" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">分项合计：</label>
                <div class="col-sm-8">
                    <input name="total" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="extra1" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="extra2" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段3：</label>
                <div class="col-sm-8">
                    <input name="extra3" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段4：</label>
                <div class="col-sm-8">
                    <input name="extra4" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',dictType='${dictType}' ,labelName='删除状态')">
            </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>


    <script th:inline="javascript">
        var prefix = ctx + "zzgg/teachPlan"

        $("#form-teachPlan-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/add", $('#form-teachPlan-add').serialize());
            }
        }

    </script>
</body>
</html>