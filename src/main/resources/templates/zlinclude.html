<!--选择技术知识分类
id:knowledgeClass input id 必填
name:knowledgeClassName 名称input name 必填
type:M多选 S单选
value:值
cvalue:名称值
-->
<div th:fragment="selectKnowledgeClass">
	<th:block th:with="
					value=${value},
					cValue=${cValue},
					id=${id},
               	 	name=${name},
               	 	type=${type==null?'S':type},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'技术知识分类：':labelName},
               	 	divClass=${divClass==null?'form-control':divClass}
               	 	">
               	 	
        <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}">选择：</label>
        
        <div class="col-sm-6">
        	<input th:id="${id}" th:name="${id}" th:value="${value}" type="hidden" />
			<input th:id="${name}" th:name="${name}" th:class="${divClass}" th:value="${cvalue}" readonly="true"/>
		</div>
	<span class="btn btn-sm btn-primary" type="button" onclick="choiceKnowledgeClass()">选择</span>
    <script th:inline="javascript" type="text/javascript">
        var ctx = [[@{/}]];
       	if ([[${isrequired!=null && isrequired}]]) {
           $("#" + [[${name}]]).attr("required", "");
        }
        var knowledgeClass = "knowledgeClass";
        var knowledgeClassName = "knowledgeClassName";

        function choiceKnowledgeClass() {
        	var code = [[${id}]]; var name = [[${name}]]; var selectType = [[${type}]];
        	//$('input[id="'+name+'"]').attr("readonly",true);
        	knowledgeClass = code;
        	knowledgeClassName = name;
        	var url = ctx + 'mpad/knowledge/selectKnowledgeClassTree?selectType='+selectType;
        	url += "&values=" + $("#" + knowledgeClass).val();
            var options = {
                title: '选择',
                width: "380",
                height: '550',
                url: url,
                callBack: choiceKnowledgeClassCallback
            };
            $.modal.openOptions(options);
        }
        
        function choiceKnowledgeClassCallback(index, layero) {
            var tree = layero.find("iframe")[0].contentWindow.$._tree;
            var body = layer.getChildFrame('body', index);
            layero.find("iframe")[0].contentWindow.saveCheck();
            $("#" + knowledgeClass).val(body.find('#treeId').val());
            $("#" + knowledgeClassName).val(body.find('#treeName').val());
            layer.close(index);
        }
    </script>
    </th:block>
</div>

<th:block th:fragment="numberInput(name)"
		  th:with="id=${id!=null ? id : name},value=${value!=null ? value : null}">
	<input class="form-control" th:name="${name}" th:id="${id}" type="text" th:value="${value}"
		   onkeyup="value=value.replace(/[^\d^\.]+/g,'').replace('.','$#$').replace(/\./g,'').replace('$#$','.')"
	/>

	<script th:inline="javascript" th:if="${callback!=null}">
		var isrequired = [[${isrequired}]];
		var id = [[${id}]]
		if (isrequired != null && isrequired) {
			$("#" + id).attr("required", "");
		}

		$("#" + [[${id}]]).on('change', function (event) {
			var callback = [[${callback}]]
			eval(callback + '("' + $(this).val() + '")')
		});
	</script>
</th:block>


<div th:fragment="addEdit-buttons" th:with="divs=${divs}">
	<div style="height: 50px;">
		<div class="toolbar toolbar-bottom" role="toolbar">
			<th:block th:if="${divs != null}">
				<th:block th:replace="${divs}"></th:block>
			</th:block>

			<button type="button" class="btn  btn-primary"
					onclick="saveHandler()">
				<i class="fa fa-check"></i>暂存
			</button>

			<button th:if="${#strings.isEmpty(taskId)}" type="button" class="btn  btn-primary"
					onclick="submitHandler()">
				<i class="fa fa-check"></i>提交
			</button>
			<th:block th:if="${not #strings.isEmpty(taskId)}"
					  th:include="/component/wfSubmit :: init(taskId=${taskId},callback='submitHandler')"/>

			<button type="button" class="btn  btn-danger"
					onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关闭
			</button>
		</div>
	</div>
	<br/>
	<th:block th:if="${not #strings.isEmpty(taskId)}"
			  th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})"></th:block>
</div>