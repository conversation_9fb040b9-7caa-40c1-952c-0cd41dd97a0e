<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
  <th:block th:include="include :: header('模块待办数量')"/>
</head>
<style>
  table{
    width: 80%; margin-left: 10%;
  }
  table tr{
    border-left: #191f5c solid 3px; background-color: #f0f5fb; height: 38px; line-height: 38px; border-bottom: 15px solid #fff;
  }
  .td1{
    float: left; height: 40px; line-height: 40px; padding-left: 15px;width: 400px;
  }
  .color1{
    color: #000;
  }
  .td2{
    text-align: left;background-color: #f9fdff; padding-left: 100px;width: 300px;
  }
  .span1{
    color: #fff; border-radius: 50px; padding: 3px 5px; background-color: red; font-weight: bold;
  }
</style>
<body class="white-bg">
<th:block th:include="include :: baseJs"/>
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
  <table>
    <tr th:each="db : ${ei.blocks.result.rows}">
      <td class="td1">
        <a class="color1" th:href="@{''}" th:text="${db.processName}+' / '+${db.currentActivityName}"></a>
      </td>
      <td class="td2">
        <a th:onclick="$.modal.openTab('待办列表',ctx+'tc/flowInfo/agencyList?processCode='+[[${db.processCode}]]+'&activityCode='+[[${db.currentActivity}]])">
          <span class="span1" th:text="${db.todoNum}"></span>
          <span class="color1">份</span>
          <span>进入处理》</span>
        </a>
      </td>
    </tr>
    <th:block th:include="component/mppsDaiban :: init(moduleName='推广_需求专家评审',moduleCode='zzlx_need')" />
    <th:block th:include="component/mppsDaiban :: init(moduleName='推广_关键技术_专家评审',moduleCode='zzlx_kettechJs')" />
    <th:block th:include="component/mppsDaiban :: init(moduleName='项目立项-定价人确认',moduleCode='zzlx_teachfix')" />
    <th:block th:include="component/mppsDaiban :: init(moduleName='项目结题-项目验收评审',moduleCode='zzjt_check')" />
    <th:block th:include="component/mppsDaiban :: init(moduleName='项目结题_项目评估专家评审',moduleCode='zzjt_assess')" />
    <th:block th:include="component/xyDaiban :: init(moduleName='项目结题-经济效益评审',moduleCode='zzjt_benefit')" />
  </table>
</div>
</body>
</html>