<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('待办任务')"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label style="width: 100px;">名称：</label>
                            <input type="text" name="businessNameLike" id="businessNameLike"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
<th:block th:include="include :: baseJs"/>
<script th:inline="javascript">
    $(function () {
        var options = {
            url: ctx + "tc/flowInfo/queryDB",
            uniqueId: "rowId",
            queryParams: queryParams,
            columns: [{
                checkbox: true
            },
                {
                    field: 'businessName',
                    title: '名称'
                },
                {
                    field: 'currentActivityName',
                    title: '当前活动'
                },
                {
                    field: 'lastOperatorName',
                    title: '上一步处理人'
                },
                {
                    field: 'lastTime',
                    title: '上一步处理时间',
                    formatter: function (value, row, index) {
                        var timeData = value.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g, '$1-$2-$3 $4:$5:$6');
                        return timeData;
                    }
                },

                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs " href="javascript:void(0)" onclick="doHandle(\'' + row.processCode + '\',\'' + row.currentActivity + '\',\'' + row.pageNo + '\',\'' + row.businessId + '\',\'' + row.flowId + '\',\'' + row.taskId + '\',\'' + row.currentActivityName + '\')"><i class="fa fa-edit"></i> 处理</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function queryParams(params) {
        var search = $.table.queryParams(params);
        search.businessNameLike = $("#businessNameLike").val();
        search.flowCode = [[${processCode}]];
        search.currentActivity = [[${activityCode}]];
        search.businessType = [[${constants.BUSINESS_TYPE}]];
        search.displayOrder = "A.UPDATE_DATE desc";
        return search;
    }

    //处理
    function doHandle(processCode,activityCode,pageNo,businessGuid,processInstanceId,taskId,currentActivityName){
        var id = businessGuid+","+pageNo+","+taskId+","+activityCode+","+processInstanceId+","+processCode;
        var url = ctx+"zzlx/taskInfo/wfDetail?processCode="+processCode+"&activityCode="+activityCode+"&pageNo="+pageNo+"&businessGuid="+businessGuid+"&processInstanceId="+processInstanceId+"&taskId="+taskId;
        //科技奖励发奖流程
        /*if (processCode == "YWFJ_FZRFPZ" || processCode == "YWFJ_GXDFPZ") {
            url = ctxYWZT + "web/YWZTFJ01?id=" + id;
        }*/
        if (processCode.indexOf("YWFJ_FZRFPZ") > -1 || processCode.indexOf("YWFJ_GXDFPZ") > -1) { //发奖流程
            url = ctxYWZT + "web/YWZTFJ01?id=" + id;
        }
        $.modal.openTab(currentActivityName, url, true);
    }
</script>
</body>
</html>