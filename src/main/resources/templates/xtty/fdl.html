<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('放大列')" />

    <th:block th:include="include :: baseJs" />
    <style>
        .dropdown-menufd{

            top: 100%;
            z-index: 1000;
            min-width: 160px;
            padding: 5px 0px;
            margin: 2px 0px 0px;
            font-size: 14px;
            text-align: left;
            list-style: none;
            background-color: rgb(255, 255, 255);
            background-clip: padding-box;
            border: 1px solid rgba(0, 0, 0, 0.15);
            border-radius: 4px;
            box-shadow: rgb(0 0 0 / 18%) 0px 6px 12px;
            display: flex;flex-flow: column wrap;height: 500px;overflow: hidden;
        }
        .dropdown-menufd li{
            display: list-item;
            text-align: -webkit-match-parent;
            height: 30px;
        }
        .dropdown-menufd li input{
            margin: 5px 0px 0px;
            line-height: normal;
        }
        .dropdown-menufd li label{
            display: block;
            padding: 4px 23px;
            clear: both;
            font-weight: 400;
            line-height: 1.42857;
            font-size: 20px;
        }


    </style>
</head>
<body class="white-bg">

<ul class="dropdown-menufd sub-menu dhIdex" style=" margin: 0px;">
    <li>
        <label>
            <input  id="checkAll" type="checkbox" ><span >全选</span>
        </label>
    </li>
    <li th:each="fd:${list}" class="mt5" th:if="${fd.dataFieldName!=' 主键'}">
        <label>
            <input class="ycxsL" th:name="${fd.dataField}" type="checkbox" th:checked="${fd.checked=='1'}"><span th:utext="${fd.dataFieldName}"></span>
        </label>
    </li>
</ul>
<script th:inline="javascript">

    $(".ycxsL").click(function(){
        if($(this).prop('checked')){
            window.parent.$("#"+(window.parent.table.options.id)).bootstrapTable("showColumn", $(this).attr("name"));
        }else{
            window.parent.$("#"+(window.parent.table.options.id)).bootstrapTable("hideColumn", $(this).attr("name"));
        }
    })
    $("#checkAll").click(function(){
        if($(this).prop('checked')){
            $(".ycxsL").each(function(){
                $(this).prop("checked",true);
                window.parent.$("#"+(window.parent.table.options.id)).bootstrapTable("showColumn", $(this).attr("name"));
            })
        }else{
            $(".ycxsL").each(function(){
                $(this).prop("checked",false);
                window.parent.$("#"+(window.parent.table.options.id)).bootstrapTable("hideColumn", $(this).attr("name"));
            })

        }
    })
    function submitHandler(){
        closeItem();
    }
</script>
</body>
</html>