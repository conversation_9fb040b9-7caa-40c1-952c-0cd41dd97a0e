<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增工作联络单表')"/>

    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: summernote-js"/>
</head>
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-contactList-add" th:object="${contactListEx}">
        <input th:field="*{listId}" type="hidden">
        <input th:field="*{processInstanceId}" type="hidden">
        <input th:field="*{activityCode}" type="hidden">
        <input id="taskId" name="taskId" th:value="${taskId}" type="hidden">
        <input id="businessGuid" name="businessGuid" th:value="${businessGuid}" type="hidden">

        <!-- 基本信息 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#1">基本信息
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                <div class="panel-body">

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">流水号：</label>
                            <div class="col-sm-8">
                                <input th:field="*{listLsh}" type="hidden">
                                <div class="form-control-static" th:text="*{listLsh}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">联络单编号：</label>
                            <div class="col-sm-8">
                                <input class="form-control" th:field="*{listBh}" type="hidden">
                                <div class="form-control-static" th:if="*{#strings.isEmpty(listBh)}"
                                     th:text="推广方项目主管审批后生成联络单号"></div>
                                <div class="form-control-static" th:if="*{not #strings.isEmpty(listBh)}"
                                     th:text="*{listBh}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">项目名称：</label>
                            <div class="form-control-static col-sm-8" th:text="*{listName}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">项目范围：</label>
                            <div class="col-sm-8"
                                 th:include="/component/radio :: init(id='listArea', name='listArea',businessType=*{constants.BUSINESS_TYPE_KTTG}, dictCode=*{constants.PROJECT_AREA},callback='projectAreaClick',isfirst=true,value=*{listArea},see=true)"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6 srfDeptCode">
                            <label class="col-sm-4 control-label">受让单位部门：</label>
                            <input th:field="*{srfDeptCode}" type="hidden">
                            <div class="form-control-static col-sm-8" th:text="*{srfDeptName}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6 tgfDeptCode">
                            <label class="col-sm-4 control-label">推广单位部门：</label>
                            <input th:field="*{tgfDeptCode}" type="hidden">
                            <div class="form-control-static col-sm-8" th:text="*{tgfDeptName}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">受让方负责人：</label>
                            <div class="form-control-static col-sm-8" th:text="*{srfFzrName}"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">联系电话：</label>
                            <input th:field="*{srfFzrTel}" type="hidden">
                            <div class="form-control-static col-sm-8" th:text="*{srfFzrTel}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">申请人：</label>
                            <input class="form-control" th:field="*{tcrUserCode}" type="hidden">
                            <div class="form-control-static col-sm-8" th:text="*{tcrUserName}"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label"> 申请日期：</label>
                            <div class="col-sm-8"
                                 th:include="/component/date :: init(id='tcrDate', name='tcrDate',strValue=*{tcrDate} ,see=true)"></div>
                        </div>
                    </div>
                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label"> 工作时间：</label>
                            <div class="col-sm-8">
                                <label class="form-control-static" style="float: left">从</label>
                                <div class="col-sm-5"
                                     th:include="/component/date :: init(id='listBeginDate', name='listBeginDate',strValue=*{listBeginDate},see=true )"></div>
                                <label class="form-control-static" style="float: left">至</label>
                                <div class="col-sm-5"
                                     th:include="/component/date :: init(id='listEndDate', name='listEndDate',strValue=*{listEndDate},see=true)"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">计划人数：</label>
                            <div class="form-control-static col-sm-8" th:text="*{listPlanCount}"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">实际人数：</label>
                            <div class="form-control-static col-sm-8" th:text="*{listRealityCount}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">项目负责人：</label>
                            <div class="form-control-static col-sm-8" th:text="*{tgfXmzgName}"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">联系电话：</label>
                            <div class="form-control-static col-sm-8" th:text="*{tgfFzrTel}"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 基本信息 End -->

        <!-- 现状描述 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#2">现状描述
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="xzmsClob" th:utext="*{xzmsClob}"></div>
                        <input th:field="*{xzmsClob}" type="hidden">
                    </div>
                </div>
            </div>
        </div>
        <!-- 现状描述 End -->

        <!-- 工作内容和要求 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#3">工作内容和要求
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="3">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="gznryqClob" th:utext="*{gznryqClob}"></div>
                        <input th:field="*{gznryqClob}" type="hidden">
                    </div>
                </div>
            </div>
        </div>
        <!-- 工作内容和要求 End -->

        <!-- 预期目标 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#4">预期目标
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="4">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="yqmbClob" th:utext="*{yqmbClob}"></div>
                        <input th:field="*{yqmbClob}" type="hidden">
                    </div>
                </div>
            </div>
        </div>
        <!-- 预期目标 End -->

        <!-- 派遣人员信息 Start -->

        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#5">派遣人员信息
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="5">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-12 select-table table-striped">
                            <table id="bootstrap-table"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 派遣人员信息 End -->

        <!-- 联络单预期目标完成情况 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#6">联络单预期目标完成情况（逐一说明）
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="6">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="yqmbwcqkClob" th:utext="*{yqmbwcqkClob}"></div>
                        <input th:field="*{yqmbwcqkClob}" type="hidden">
                    </div>
                </div>
            </div>
        </div>
        <!-- 联络单预期目标完成情况 End -->

        <!-- 经验总结 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#7">经验总结
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="7">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="jyzjClob" th:utext="*{jyzjClob}"></div>
                        <input th:field="*{jyzjClob}" type="hidden">
                    </div>
                </div>
            </div>
        </div>
        <!-- 经验总结 End -->

        <!-- 技术咨询工作联络单结题小结 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#8">技术咨询工作联络单结题小结
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="8">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="mnote-editor-title"><span class="txt-impt">格式规范：一、现状描述；二、主要工作内容；三、目标达成情况；四、甲、乙人力资源投 入情况（按计价岗位列表）；五、经验总结。</span>
                        </div>
                        <div class="mnote-editor-box">
                            <div th:include="/component/attachment :: init(display='none',name=*{constants.ATT_CONTACTLIST_CONCLUSION},id=*{constants.ATT_CONTACTLIST_CONCLUSION},sourceId=*{listId},sourceModule=*{constants.BUSINESS_TYPE},sourceLabel1=*{constants.ATT_CONTACTLIST_CONCLUSION},see=true)"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 技术咨询工作联络单结题小结 End -->

        <!-- 阶段性成果技术文档 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#9">阶段性成果技术文档
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="9">
                <div class="panel-body">
                    <div class="row form-group">
                        <div th:include="/component/attachment :: init(display='none',name=*{constants.ATT_CONTACTLIST_ACHIEVEMENTS},id=*{constants.ATT_CONTACTLIST_ACHIEVEMENTS},sourceId=*{listId},sourceModule=*{constants.BUSINESS_TYPE},sourceLabel1=*{constants.ATT_CONTACTLIST_ACHIEVEMENTS},see=true)"></div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 阶段性成果技术文档 End -->

        <!-- 现场支撑报备申请 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#10">现场支撑报备申请
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="10">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-12 select-table table-striped">
                            <table id="bootstrap-tableXC"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 现场支撑报备申请 End -->

        <!-- 远程支撑报备申请 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#11">远程支撑报备申请
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="11">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-12 select-table table-striped">
                            <table id="bootstrap-tableYC"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 远程支撑报备申请 End -->

        <!-- 本次奖励额 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#12">本次奖励额
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="12">
                <div class="panel-body">

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">净收益（人工成本）：</label>
                            <input th:field="*{listAwardIncome}" type="hidden">
                            <div class="form-control-static col-sm-8" th:text="*{listAwardIncome}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">奖励系数：</label>
                            <div class="form-control-static col-sm-8" th:text="*{listAwardXs}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">本次奖励金额：</label>
                            <div>
                                <div class="form-control-static col-sm-8" th:text="*{listAwardDesJlje ?: 0}+'万元'"></div>
                                <input th:field="*{listAwardDesJlje}" type="hidden">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 本次奖励额 End -->

    </form>
</div>
<div class="row">
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button class="btn btn-sm btn-danger" onclick="closeItem()" type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>
<!-- 审批历史 -->
<div th:if="${! #strings.isEmpty(contactListEx.processInstanceId)}"
     th:include="component/wfCommentList::init(processInstanceId=${contactListEx.processInstanceId})"></div>

<script th:src="@{/zzzc/js/common.js}"></script>
<script th:inline="javascript">
    var prefix = ctx + "zzll/contactList"
    var postData = [[${@maintain.getPostType()}]];

    $("#form-contactList-add").validate({
        focusCleanup: true
    });


    /**
     * 初始化数据
     */
    $(function () {
        var options = {// 派遣人员信息
            id: "bootstrap-table",
            url: ctx + "zzll/peopleInfo/list",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.listId = [[${contactListEx.listId}]]
                return search;
            },
            headerStyle: function (column) {
                return {
                    peopleUserId: {css: {display: "none"}}
                } [column.field]
            },
            columns: [
                {
                    checkbox: true
                },
                {
                    field: 'orderNum',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return value;
                    }
                },
                {
                    field: 'peopleUserId',
                    cellStyle: {css: {display: "none"}},
                    formatter: function (value, row, index) {
                        return value;
                    }
                },
                {
                    field: 'peopleUserName',
                    align: 'center',
                    title: '姓名',
                    formatter: function (value, row, index) {
                        return value;
                    }
                },
                {
                    field: 'peopleGwName',
                    align: 'center',
                    title: '岗位名称',
                    formatter: function (value, row, index) {
                        return value;
                    }
                },
                {
                    field: 'peopleGwTarget',
                    align: 'center',
                    title: '岗位类型',
                    formatter: function (value, row, index) {
                        return dictToSelect(postData, value, null, null, true);
                    }
                },
                {
                    field: 'peopleUserRemark',
                    align: 'center',
                    title: '备注',
                    formatter: function (value, row, index) {
                        return value;
                    }
                }
            ]
        };

        $.table.init(options);

        var optionsXC = {// 现场支撑报备申请
            id: "bootstrap-tableXC",
            url: ctx + "zzfy/scene/sceneList",
            detailUrl: ctx + "zzfy/scene/detailScene/{id}",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            modalName: "现场支撑报备申请",
            queryParams: function (params) {
                var search = {};
                search.bizId = [[${contactListEx.listId}]]
                return search;
            },
            columns: [
                {
                    field: 'sceneId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'sceneDate',
                    align: 'center',
                    title: '申请日期'
                },
                {
                    field: 'sceneFyTotal',
                    align: 'center',
                    title: '差旅费(元)'
                },
                {
                    field: 'sceneBzdh',
                    align: 'center',
                    title: '报支单号'
                },
                {
                    field: 'status',
                    align: 'center',
                    title: '状态'
                },
                {
                    field: 'scenePeopleTotal',
                    align: 'center',
                    title: '人工成本'
                },
                {
                    title: '查看',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.sceneId + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        return actions.join('');
                    }
                }
            ]
        };
        $.table.init(optionsXC);

        var optionsYC = {// 远程支撑报备申请
            id: "bootstrap-tableYC",
            url: ctx + "zzfy/long/longList",
            detailUrl: ctx + "zzfy/long/detailLong/{id}",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            modalName: "远程支撑报备申请",
            queryParams: function (params) {
                var search = {};
                search.bizId = [[${contactListEx.listId}]]
                return search;
            },
            columns: [
                {
                    field: 'longId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'longDate',
                    align: 'center',
                    title: '申请日期'
                },
                {
                    field: 'status',
                    align: 'center',
                    title: '状态'
                },
                {
                    field: 'longRgfFyhj',
                    align: 'center',
                    title: '人工成本'
                },
                {
                    title: '查看',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.longId + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        return actions.join('');
                    }
                }
            ]
        };

        $.table.init(optionsYC);
    });

    /**
     * 主子表新增行
     */
    function addColumn() {
        var row = {
            orderNum: "",
            peopleUserName: "",
            peopleUserId: "",
            peopleGwName: "",
            peopleGwTarget: "",
            peopleUserRemark: ""
        }
        sub.addColumn(row, "bootstrap-table");
    }


    /**
     * 数据字典转下拉框
     * @param datas 下拉框数据
     * @param value 值
     * @param name
     * @param id
     * @param see 是否回显
     * @returns {string}
     */
    function dictToSelect(datas, value, name, id, see) {
        if ($.common.isEmpty(datas) || $.common.isEmpty(value)) {
            return '';
        }
        var actions = [];
        if (see) {
            $.each(datas, function (index, dict) {
                if (dict.dictCode == ('' + value)) {
                    var listClass = $.common.equals("default", dict.listClass) || $.common.isEmpty(dict.listClass) ? "" : "badge badge-" + dict.listClass;
                    actions.push($.common.sprintf("<span class='%s'>%s</span>", listClass, dict.dictName));
                    return false;
                }
            });
        } else {
            actions.push($.common.sprintf("<select id='%s' class='form-control' name='%s'>", id, name));
            $.each(datas, function (index, dict) {
                actions.push($.common.sprintf("<option value='%s'", dict.dictCode));
                if (dict.dictCode == ('' + value)) {
                    actions.push(' selected');
                }
                actions.push($.common.sprintf(">%s</option>", dict.dictName));
            });
            actions.push('</select>');
        }
        return actions.join('');
    }

    // 开始日期、结束日期
    $("#listBeginDate").datetimepicker().on("changeDate", function (e) {
        $("#listEndDate").datetimepicker('setStartDate', e.date);
    });
    $("#listEndDate").datetimepicker().on("changeDate", function (e) {
        $("#listBeginDate").datetimepicker('setEndDate', e.date);
    })

    // 计算奖金
    $("#listAwardXs").blur(function (e) {
        var listAwardIncome = $("#listAwardIncome").val();// 净收益
        var listAwardXs = $("#listAwardXs").val();// 奖励系数
        $("#listAwardDesJlje").val(mul(listAwardIncome, listAwardXs));// 本次决定奖励金额
    });

    function mul(arg1, arg2) {
        var r1 = arg1.toString(), // 将传入的数据转化为字符串
            r2 = arg2.toString(),
            m, resultVal, d = arguments[2];
        m = (r1.split(".")[1] ? r1.split(".")[1].length : 0) + (r2.split(".")[1] ? r2.split(".")[1].length : 0); // 获取两个数字的小数位数的和
        // 乘积的算法就是去掉小数点做整数相乘然后除去10的所有小数位的次方
        resultVal = Number(r1.replace(".", "")) * Number(r2.replace(".", "")) / Math.pow(10, m);
        return typeof d !== "number" ? Number(resultVal) : Number(resultVal.toFixed(parseInt(d)));
    }

</script>
</body>
</html>