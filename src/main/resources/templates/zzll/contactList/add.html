<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增工作联络单表')"/>

    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: summernote-js"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-contactList-add" th:object="${contactListEx}">
        <input th:field="*{listId}" type="hidden">
        <input th:field="*{processInstanceId}" type="hidden">
        <input th:field="*{activityCode}" type="hidden">
        <input id="taskId" name="taskId" th:value="${taskId}" type="hidden">
        <input id="businessGuid" name="businessGuid" th:value="${businessGuid}" type="hidden">
        <input id="transitionKey" name="transitionKey" type="hidden">

        <!-- 基本信息 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#1">基本信息
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                <div class="panel-body">

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">流水号：</label>
                            <div class="col-sm-8">
                                <input class="form-control" th:field="*{listLsh}" type="hidden">
                                <div class="form-control-static" th:if="*{#strings.isEmpty(listLsh)}"
                                     th:text="联络单提交后系统自动生成"></div>
                                <div class="form-control-static" th:if="*{not #strings.isEmpty(listLsh)}"
                                     th:text="*{listLsh}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">联络单编号：</label>
                            <div class="col-sm-8">
                                <input class="form-control" th:field="*{listBh}" type="hidden">
                                <div class="form-control-static" th:if="*{#strings.isEmpty(listBh)}"
                                     th:text="推广方项目主管审批后生成联络单号"></div>
                                <div class="form-control-static" th:if="*{not #strings.isEmpty(listBh)}"
                                     th:text="*{listBh}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">项目名称：</label>
                            <div class="col-sm-8">
                                <input autocomplete="off" class="form-control width100" required th:field="*{listName}"
                                       type="text">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">项目范围：</label>
                            <div class="col-sm-8"
                                 th:include="/component/radio :: init(id='listArea', name='listArea',businessType=*{constants.BUSINESS_TYPE_KTTG}, dictCode=*{constants.PROJECT_AREA},callback='projectAreaClick',isfirst=true,value=*{listArea})"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6 srfDeptCode">
                            <label class="col-sm-4 control-label">受让单位部门：</label>
                            <div th:class="'gfn col-sm-8 '+@{*{listArea} eq *{constants.PROJECT_AREA_GFN} ? '' : 'hidden'}"
                                 th:include="/component/selectOrg :: init(orgCodeId='srfDeptCode',orgNameId='srfDeptName',selectType='S',value=*{srfDeptCode},see=true)"></div>
                            <div th:class="'jtn col-sm-8 '+@{*{listArea} eq *{constants.PROJECT_AREA_JTN} ? '' : 'hidden'}">
                                <select class="form-control" id="srfDeptCodeJtn"
                                        th:with="dictData=${@maintain.getJtUnit()}">
                                    <option value="">请选择</option>
                                    <option th:each="dict : ${dictData}"
                                            th:selected="${contactListEx.srfDeptCode eq dict.dictCode}"
                                            th:text="${dict.dictName}" th:value="${dict.dictCode}"></option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6 tgfDeptCode">
                            <label class="col-sm-4 control-label">推广单位部门：</label>
                            <div th:class="'gfn col-sm-8 '+@{*{listArea} eq *{constants.PROJECT_AREA_GFN} ? '' : 'hidden'}"
                                 th:include="/component/selectOrg :: init(orgCodeId='tgfDeptCode',orgNameId='tgfDeptName',selectType='S',value=*{tgfDeptCode})"></div>
                            <div th:class="'jtn col-sm-8 form-control-static '+@{*{listArea} eq *{constants.PROJECT_AREA_JTN} ? '' : 'hidden'}">
                                <div class="form-control-static"
                                     th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(contactListEx.tgfDeptCode)}">
                                    111
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">受让方负责人：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectUser :: init(userCodeId='srfFzrCode',userNameId='srfFzrName',value=*{srfFzrCode},selectType='S',isrequired=true)"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">联系电话：</label>
                            <div class="col-sm-8">
                                <input autocomplete="off" class="form-control width100" required th:field="*{srfFzrTel}"
                                       type="text">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">申请人：</label>
                            <input class="form-control" th:field="*{tcrUserCode}" type="hidden">
                            <input class="form-control" th:field="*{tcrUserName}" type="hidden">
                            <div class="form-control-static" th:text="*{tcrUserName}"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label"> 申请日期：</label>
                            <div class="col-sm-8"
                                 th:include="/component/date :: init(id='tcrDate', name='tcrDate',strValue=*{tcrDate} ,isrequired=true)"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 基本信息 End -->

        <!-- 现状描述 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#2">现状描述
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="xzmsClob" th:utext="*{xzmsClob}"></div>
                        <input th:field="*{xzmsClob}" type="hidden">
                    </div>
                </div>
            </div>
        </div>
        <!-- 现状描述 End -->

        <!-- 工作内容和要求 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#3">工作内容和要求
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="3">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="gznryqClob" th:utext="*{gznryqClob}"></div>
                        <input th:field="*{gznryqClob}" type="hidden">
                    </div>
                </div>
            </div>
        </div>
        <!-- 工作内容和要求 End -->

        <!-- 预期目标 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#4">预期目标
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="4">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="yqmbClob" th:utext="*{yqmbClob}"></div>
                        <input th:field="*{yqmbClob}" type="hidden">
                    </div>
                </div>
            </div>
        </div>
        <!-- 预期目标 End -->
    </form>
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
    </div>
</div>
<div class="row">
    <div class="toolbar toolbar-bottom" role="toolbar" >

        <button class="btn btn-sm btn-primary" onclick="saveHandler()" type="button" th:if="${#strings.isEmpty(taskId)}">
            <i class="fa fa-check"></i>暂 存
        </button>
        <button class="btn btn-sm btn-primary" onclick="submitHandler()" type="button" th:if="${#strings.isEmpty(taskId)}">
            <i class="fa fa-check"></i>提 交
        </button>
        <th:block th:if="${! #strings.isEmpty(taskId)}"
                th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>
        <button class="btn btn-sm btn-danger" onclick="closeItem()" type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>


<script th:src="@{/zzzc/js/common.js}"></script>
<script th:inline="javascript">
    var prefix = ctx + "zzll/contactList"
    $("#form-contactList-add").validate({
        focusCleanup: true
    });
    /**
     * 初始化富文本 现状描述
     */
    $(".xzmsClob").summernote({
        lang: 'zh-CN',
        height: 200,
        callbacks: {
            onImageUpload: function (files) {

                sendFile(this, files[0], this);
            }
        }
    });
    /**
     * 初始化富文本 工作内容和要求
     */
    $(".gznryqClob").summernote({
        lang: 'zh-CN',
        height: 200,
        callbacks: {
            onImageUpload: function (files) {

                sendFile(this, files[0], this);
            }
        }
    });
    /**
     * 初始化富文本 预期目标
     */
    $(".yqmbClob").summernote({
        lang: 'zh-CN',
        height: 200,
        callbacks: {
            onImageUpload: function (files) {

                sendFile(this, files[0], this);
            }
        }
    });

    // 申请人所在部门，项目范围股份内时，受让方为申请人所在部门，集团内时，推广法为申请人部门
    var deptCode = [[${contactListEx.listArea eq contactListEx.constants.PROJECT_AREA_GFN}]] ? [[${contactListEx.srfDeptCode}]] : [[${contactListEx.tgfDeptCode}]];
    var deptName = [[${contactListEx.listArea eq contactListEx.constants.PROJECT_AREA_GFN}]] ? $("#srfDeptName").val() : $("#tgfDeptName").val();
    var tgfDeptCode = "", tgfDeptName = "", srfDeptCode = "", srfDeptName = "";// 项目范围变更集团内时。临时记录股份内选择的推广单位
    /**
     * 单选按钮回调函数 项目范围改变 集团内 下拉配置选择   股份内  单位选择按钮
     * @param str
     */
    function projectAreaClick(str) {
        if ([[${contactListEx.constants.PROJECT_AREA_GFN}]] === str) { // 股份内
            var data = $("#srfDeptCodeJtn").select2('data');
            if(data.length > 0){
                srfDeptCode = data[0].id;
                srfDeptName = data[0].text;
            }
            $(".gfn").removeClass("hidden");
            $(".jtn").addClass("hidden");

            $("#srfDeptCode").val(deptCode);
            $("#srfDeptName").val(deptName);
            $("#tgfDeptCode").val(tgfDeptCode);
            $("#tgfDeptName").val(tgfDeptName);
            $(".gfn .form-control-static").html(deptName);
        } else {// 集团内
            tgfDeptCode = $("#tgfDeptCode").val();
            tgfDeptName = $("#tgfDeptName").val();

            $(".jtn").removeClass("hidden");
            $(".gfn").addClass("hidden");

            $("#tgfDeptCode").val(deptCode);
            $("#tgfDeptName").val(deptName);
            $("#srfDeptCode").val(srfDeptCode);
            $("#srfDeptName").val(srfDeptName);
            if ($.common.isNotEmpty(srfDeptCode)) {
                $("#srfDeptCodeJtn").val(srfDeptCode.split(",")).trigger("change");
            }

            $(".jtn .form-control-static").html(deptName);
        }
    }

    /**
     * 项目范围是集团内，切换部门的时候赋值
     */
    $('#srfDeptCodeJtn').on('select2:select', function (e) {
        $("#srfDeptCode").val(e.params.data.id);
        $("#srfDeptName").val(e.params.data.text);
    });

    /**
     * 暂存
     */
    var summData = "xzmsClob,gznryqClob,yqmbClob";
    function saveHandler() {
        var listName = $("#listName").val();
        if ($.common.isEmpty(listName)) {
            $.modal.alertError("请输入项目名称！");
            return false;
        }
        checkClob(summData,false);
        saveAjax(prefix + "/saveHandler", $('#form-contactList-add').serialize(), '暂存', true);
    }

    /**
     * 提交
     */
    function submitHandler(transitionKey) {
        $('#transitionKey').val(transitionKey);
        if (checkClob(summData,true) && $.validate.form()) {
            $.modal.confirm("确认提交吗？", function () {
                $.operate.saveTabAlert(prefix + "/saveHandler", $('#form-contactList-add').serialize() + "&status=" + [[${contactListEx.constants.STATUS_TJ}]]);
            });
        }
    }

    // function checkClob() {
    //     var summData = "xzmsClob,gznryqClob,yqmbClob";
    //     let split = summData.split(",");
    //     for (const key of split) {
    //         var keyValue = $('.' + key).summernote('code');
    //         console.log(key + "----" + keyValue);
    //         if ($.common.isEmpty(keyValue)) {
    //             $.modal.alertError(key + ":不能为空")
    //             return false;
    //         }
    //         $("#" + key).val(keyValue);
    //     }
    //     return true
    // }

</script>
</body>
</html>