<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('工作联络单表列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <input th:field="${queryType}" type="hidden">
                <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">联络单编号：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="listBh" type="text">
                        </div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">项目名称：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="listName" type="text">
                        </div>
                    </div>
                </div>

                <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">受让单位部门：</label>
                        <div class="col-sm-8"
                             th:include="/component/selectOrg :: init(orgCodeId='frameSrfCode',orgNameId='frameSrfName',selectType='S')"></div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">推广单位部门：</label>
                        <div class="col-sm-8"
                             th:include="/component/selectOrg :: init(orgCodeId='frameTgfCode',orgNameId='frameTgfName',selectType='S')"></div>
                    </div>
                </div>

                <div class="select-list">
                    <ul>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group" th:if="${queryType} eq 'list'">
            <a class="btn btn-success" onclick="$.operate.addTab()">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzll/contactList";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/add?listId={id}",
            detailUrl: prefix + "/detail/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "工作联络单表",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.queryType = [[${queryType}]];
                return search;
            },
            columns: [{
                checkbox: true
            },
                {
                    field: 'listId',
                    title: '工作联络单主键',
                    visible: false
                },
                {
                    field: 'listBh',
                    title: '联络单编号'
                },
                {
                    field: 'listName',
                    title: '项目名称'
                },
                {
                    field: 'srfFzrName',
                    title: '项目负责人'
                },
                {
                    field: 'srfDeptName',
                    title: '受让单位部门'
                },
                {
                    field: 'tgfDeptName',
                    title: '推广单位部门'
                },
                {
                    field: 'activityName',
                    title: '状态',
                    formatter: function (value, row, index) {
                        if ($.common.isEmpty(value)) {
                            return "草稿"
                        }
                        return value
                    }
                },
                {
                    field: 'currentOperator',
                    title: '当前操作人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.listId + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        if ([[${queryType}]] === 'list') {
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.listId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                            actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.listId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }

                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>