<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('工作联络单奖励通知书')"/>

    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: summernote-js"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <!--    <div class="form-group" th:include="include :: step(approveKind=${needEx.constants.ZZLX_GDXQSB},currentNode='MANUAL1')"></div>-->
    <div aria-multiselectable="true" class="panel-group" id="accordion" role="tablist">

        <form class="form-horizontal m" id="form-contactList-add" th:object="${contactListEx}">
            <input th:field="*{listId}" type="hidden">
            <input th:field="*{processInstanceId}" type="hidden">
            <input th:field="*{activityCode}" type="hidden">
            <input id="taskId" name="taskId" th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid" th:value="${businessGuid}" type="hidden">
            <input id="noticeId" name="noticeId"  th:value="*{noticeId}" type="hidden">
            <input id="bizId" name="bizId"  th:value="*{listId}" type="hidden">
            <input th:field="*{listBh}" type="hidden">
            <input th:field="*{listName}" type="hidden">
            <input th:field="*{srfXmzgCode}" type="hidden">
            <input th:field="*{srfDeptCode}" type="hidden">
            <input th:field="*{tgfDeptCode}" type="hidden">
            <input th:field="*{srfDwdeptCode}" type="hidden">
            <input th:field="*{listArea}" type="hidden">

            <div aria-multiselectable="true" class="panel-group" id="accordion" role="tablist">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a aria-expanded="false" class="collapsed"
                               data-parent="#version" data-toggle="collapse" href="#jbxx">工作联络单奖励通知书
                                <span class="pull-right">
                                    <i aria-hidden="true" class="fa fa-chevron-down"></i>
                                </span>
                            </a>
                        </h4>
                    </div>
                    <div aria-expanded="false" class="panel-collapse collapse in" id="jbxx">
                        <div class="panel-body">
                            <div class="row" >
                                <div class="form-group">
                                    <label class="col-sm-12">推广单位部门：<span  th:utext="*{tgfDeptName}" ></span>;受让单位部门<span  th:utext="*{srfDeptName}" ></span></label>
                                    <div class="col-sm-12">
                                        贵单位工作联络单“<span th:utext="*{listBh}+'、' + *{listName}"/>“已履行完毕,其奖励情况为：<br>
                                        <label style="font-weight: bold">本次奖励额为：</label><input name="noticeDeciJlje" id="noticeDeciJlje"  th:value="${#numbers.formatDecimal(contactListEx.listAwardDesJlje,1,4)}" type="number" required   onchange="sumLC()">万元<br>
                                        <label style="font-weight: bold">管理留成为：本次预励奖金额 X</label>  <input style="width: 50px;" name="noticeLcXs" id="noticeLcXs" min="0.0" max="1" th:value="*{noticeLcXs}" type="number" required  onchange="sumLC()" >%＝<input name="noticeLcJe" id="noticeLcJe"  th:value="${#numbers.formatDecimal(contactListEx.noticeLcJe,1,4)}" type="number" required   >万元<br>
                                        <label style="font-weight: bold">实发励金额为：</label><input name="noticeSfJlje" id="noticeSfJlje"  th:value="${#numbers.formatDecimal(contactListEx.noticeSfJlje,1,4)}" type="number" required  onchange="sumTG()">万元<br>
                                        <div th:if="*{listArea eq constants.PROJECT_AREA_GFN}">
                                            <label style="font-weight: bold">推广励金额为:实发奖励金额 X </label> <input style="width: 50px;" name="noticeTgfXs" id="noticeTgfXs"  min="0.0" max="1" th:value="*{noticeTgfXs}" type="number" required   onchange="sumTG()">%＝<input name="noticeTgfJlje" id="noticeTgfJlje"  th:value="${#numbers.formatDecimal(contactListEx.noticeTgfJlje,1,4)}" type="number" required   >万元<br>
                                            <label style="font-weight: bold">受让奖励金额为:实发预奖励金额 X </label> <input style="width: 50px;" name="noticeSrfXs" id="noticeSrfXs" min="0.0" max="1"  th:value="*{noticeSrfXs}" type="number" required   onchange="sumTG()">%＝<input name="noticeSrfJlje" id="noticeSrfJlje"  th:value="${#numbers.formatDecimal(contactListEx.noticeSrfJlje,1,4)}" type="number" required  >万元<br>
                                        </div>
                                        *注:请推广方单位信凭此单进行奖金分配，受让方按奖励授权范围审批。<br>
                                    </div>
                                    <label class="col-sm-10 control-label"><div  th:include="/component/select :: init(see=true,value=${contactListEx.tgfDwdeptCode},businessType='KTTG', dictCode='rewardNoticeDept')"></div></label>
                                    <label class="col-sm-10 control-label">日期：<span  th:utext="*{#dates.format(#dates.createToday(),'yyyy年MM月dd日')}" ></span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <div class="m">
            <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
        </div>
    </div>
</div>

<div class="row">
    <div class="toolbar toolbar-bottom" role="toolbar" >
        <button class="btn btn-sm btn-primary" th:onclick="openProcessTrack([[${processInstanceId}]])" type="button">
            <i class="fa fa-eye"></i>&nbsp;流程跟踪图
        </button>
        <button class="btn btn-sm btn-primary" onclick="saveHandler()"  type="button">
            <i class="fa fa-check"></i>暂 存
        </button>
        <th:block th:if="${! #strings.isEmpty(taskId)}" th:include="/component/wfSubmitOne :: init(taskId=${taskId},callback='submitProcess')"></th:block>
        <th:block th:if="${! #strings.isEmpty(taskId)}" th:include="/component/wfReturn :: init(taskId=${taskId},callback=wfReturn)"/>
        <button class="btn btn-sm btn-danger" onclick="closeItem()" type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>
<!-- 审批历史 -->
<div th:if="${! #strings.isEmpty(contactListEx.processInstanceId)}" th:include="component/wfCommentList::init(processInstanceId=${contactListEx.processInstanceId})"></div>

<script th:src="@{/zzzc/js/common.js}"></script>
<script th:inline="javascript">
    var prefix = ctx + "zzll/contactList";

    $("#form-contactList-add").validate({
        focusCleanup: true
    });
    function sumLC(){
        var noticeDeciJlje=$("#noticeDeciJlje").val();
        if(noticeDeciJlje==null||noticeDeciJlje==''){
            noticeDeciJlje=0.0;
        }
        var noticeLcXs=$("#noticeLcXs").val();
        if(noticeLcXs==null||noticeLcXs==''){
            noticeLcXs=0.0;
        }
        var noticeLcJe=parseFloat(noticeDeciJlje)*parseFloat(noticeLcXs);
        $("#noticeLcJe").val(noticeLcJe.toFixed(4));

        var noticeSfJlje = parseFloat(noticeDeciJlje) - parseFloat(noticeLcJe);
        $("#noticeSfJlje").val(noticeSfJlje.toFixed(4));
    }

    function sumTG(){
        var noticeSfJlje=$("#noticeSfJlje").val();
        if(noticeSfJlje==null||noticeSfJlje==''){
            noticeSfJlje=0.0;
        }
        var noticeTgfXs=$("#noticeTgfXs").val();
        if(noticeTgfXs==null||noticeTgfXs==''){
            noticeTgfXs=0.0;
        }else {
            $("#noticeSrfXs").val(1-noticeTgfXs);
        }

        var noticeSrfXs=$("#noticeSrfXs").val();
        if(noticeSrfXs==null||noticeSrfXs==''){
            noticeSrfXs=0.0;
        }
        var noticeTgfJlje=parseFloat(noticeSfJlje)*parseFloat(noticeTgfXs);
        var noticeSrfJlje=parseFloat(noticeSfJlje)*parseFloat(noticeSrfXs);
        $("#noticeTgfJlje").val(noticeTgfJlje.toFixed(4));
        $("#noticeSrfJlje").val(noticeSrfJlje.toFixed(4));

    }
    /**
     * 暂存
     */
    function saveHandler() {
        $.modal.confirm("确认提交吗？", function () {
            saveAjax(prefix + "/saveHandler", $('#form-contactList-add').serialize(), '暂存', true);
        });
    }

    /**
     * 提交流程
     */
    function submitProcess() {
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function () {
                var data = $('#form-contactList-add').serialize();
                $.operate.saveTabAlert(prefix + "/submitProcess", data);
            });
        }
    }

    //流程跟踪
    function openProcessTrack(processInstanceId) {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processInstanceId);
    }

    //退回流程
    function wfReturn(activityKey) {
        if ($.validate.form()) {
            var data = $('#form-contactList-add').serialize() + "&activityKey=" + activityKey;
            $.operate.saveTabAlert(ctx + "mpwf/flowInfo/returnFlow", data);
        }
    }

</script>
</body>
</html>