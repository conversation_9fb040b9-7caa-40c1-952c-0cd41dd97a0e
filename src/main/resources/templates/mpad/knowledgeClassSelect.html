<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('技术知识分类选择')" />
<th:block th:include="include :: ztree-css" />
</head>
<style>
body {
	height: auto;
	font-family: "Microsoft YaHei";
}

button {
	font-family: "SimSun", "Helvetica Neue", Helvetica, Arial;
}
</style>
<body class="hold-transition box box-main">
	<input id="treeId" name="treeId" type="hidden" />
	<input id="treeName" name="treeName" type="hidden" />
	<div class="wrapper">
		<div class="treeShowHideButton" onclick="$.tree.toggleSearch();">
			<label id="btnShow" title="显示搜索" style="display: none;">︾</label> <label
				id="btnHide" title="隐藏搜索">︽</label>
		</div>
		<div class="treeSearchInput" id="search">
			<label for="keyword">关键字：</label><input type="text" class="empty"
				id="keyword" maxlength="50">
			<button class="btn" id="btn" onclick="$.tree.searchNode()">
				搜索</button>
		</div>
		<div class="treeExpandCollapse" style="float: none;">
			   <label class="check-box">
				        <input type="checkbox" value="1" >展开/折叠</label>
				    <label class="check-box" th:if="${selectType=='M'}">
				        <input  type="checkbox"  value="2">全选/全不选</label>
				    <label class="check-box" th:if="${selectType=='M'}">
				        <input  type="checkbox" value="3" >父子联动</label>
		</div>

	
		<div id="tree" class="ztree ztree-border"></div>
	</div>


	<th:block th:include="include :: baseJs" />
	<th:block th:include="include :: ztree-js" />
	<script th:inline="javascript">
	
	$('.treeExpandCollapse input').on('ifChanged', function(obj){
		var type = $(this).val();
		var checked = obj.currentTarget.checked;
		if (type == 1) {
		    if (checked) {
		        $._tree.expandAll(true);
		    } else {
		        $._tree.expandAll(false);
		    }
		} else if (type == "2") {
		    if (checked) {
		        $._tree.checkAllNodes(true);
		    } else {
		        $._tree.checkAllNodes(false);
		    }
		} else if (type == "3") {
		    if (checked) {
		        $._tree.setting.check.chkboxType = { "Y": "ps", "N": "ps" };
		    } else {
		        $._tree.setting.check.chkboxType = { "Y": "", "N": "" };
		    }
		}
	})
		$(function() {
			var url= ctx + "mpad/knowledge/knowledgeClassTreeData";
			/* var values=[[${values}]];
			if(values!=null && values!=''){
				url+="&values="+values;
			} */
			var selectedMulti=false;
			var enable=false;
			if([[${selectType=='M'}]]){
				selectedMulti=true;
				enable=true;
			}
			var options = {
		        url: url,
		        expandLevel: 1,
		        onClick: zOnClick,
		        view:{selectedMulti:selectedMulti},
				check:{enable:enable,chkboxType:  { "Y": "", "N": "" }},
				onAsyncSuccess : zTreeOnAsyncSuccess
		    };
			$.tree.init(options);
		});
		//点击名称
		function zOnClick(event, treeId, treeNode) {
			//组织编码

		    var treeId = treeNode.code;
		    var treeName = treeNode.name;
		    $("#treeId").val(treeId);
		    $("#treeName").val(treeName);
		    
			if([[${selectType=='M'}]]){
				if(treeNode.checked){
					$._tree.checkNode(treeNode);
				}else{
					$._tree.checkNode(treeNode, true, true);
				}

			}

		}
		function zTreeOnAsyncSuccess(event, treeId, treeNode, msg) {
			if (!$._tree) {
				alert("error!");
				return
			}
			var selectedNode = $._tree.getSelectedNodes();
			var nodes = $._tree.getNodes();
			$._tree.expandNode(nodes[0], true);
		}
		//复选框
		function zonCheck(event, treeId, treeNode){
			console.log("1")
		}
		
		function saveCheck(){
			if([[${selectType=='M'}]]){
				//组织编码
				$("#treeId").val($.tree.getCheckedNodes("code"));
				$("#treeName").val($.tree.getCheckedNodes("name"));
			}
			
			var callback= [[${callback}]]
			if(callback!=null && callback!=''){
				parent.eval(callback+'("'+$("#treeId").val()+'","'+$("#treeName").val()+'")');
			}
		}


		function find(str,cha,num){
			var x=str.indexOf(cha);
			for(var i=0;i<num;i++){
				x=str.indexOf(cha,x+1);
			}
			return x;
		}
	</script>
</body>
</html>
