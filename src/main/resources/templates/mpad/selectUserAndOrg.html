<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('选择用户')" />
	<th:block th:include="include :: layout-latest-css" />
	<th:block th:include="include :: ztree-css" />
	<style>
		#nameDiv{
			display: inline-block;
			border-radius: 6px;
			margin-top: 10px;
			padding-top: 5px;
			padding-bottom: 13px;
			background: #ececec;
			box-shadow: 1px 1px 3px rgb(0 0 0 / 20%);
		}
		.nameSpan{
			border-radius: 6px;
			margin-top: 3px;
			padding-top: 3px;
			padding-bottom: 5px;
			background: #f5f5f5;
			box-shadow: 1px 1px 3px rgb(0 0 0 / 20%);
			cursor:pointer;
		}
	</style>
</head>
<body class="gray-bg">
	<div class="ui-layout-west">
		<div class="box box-main">
			<div class="box-header">
				<div class="box-title">
					<i class="fa icon-grid"></i> 组织机构
				</div>
				<div class="box-tools pull-right">
					<button type="button" class="btn btn-box-tool" id="btnExpand"
						title="展开" style="display: none;">
						<i class="fa fa-chevron-up"></i>
					</button>
					<button type="button" class="btn btn-box-tool" id="btnCollapse"
						title="折叠">
						<i class="fa fa-chevron-down"></i>
					</button>
					<button type="button" class="btn btn-box-tool" id="btnRefresh" title="刷新部门"><i class="fa fa-refresh"></i></button>
				</div>
			</div>
			<div class="ui-layout-content">
				<div id="tree" class="ztree"></div>
			</div>
		</div>
	</div>
	<div class="ui-layout-center">
		<div class="container-div">
			<div class="row">
				<div class="col-sm-12 search-collapse">
					<form id="role-form">
						<div class="select-list">
						  <input type="hidden" id="orgCode" name="orgCode" th:value="${orgCode}"/>
              <input type="hidden" id="indexVal" name="indexVal" th:value="${indexVal}"/>
							<ul>
								<li style="display: none"  onclick="openText('userCode')">当前选中工号：<input type="text"  style="border: none;" id="userCode" disabled th:value="${values}" />
								</li >
								<li style="display: none"  onclick="openText('userName')">当前选中名称：<input type="text"  style="border: none;" id="userName" disabled th:value="${userName}" />
								</li>
								<li>用户工号：<input type="text" name="userCode"   style="width:113px;"/>
								</li>
								<li>用户名称：<input type="text" name="userName"  style="width:113px;"/>
								</li>
								<li><a class="btn btn-primary btn-rounded btn-sm"
									onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
									<a class="btn btn-warning btn-rounded btn-sm"
									onclick="resetData()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
								</li>
							</ul>
							<div id="nameDiv" style="width: 100%;">
								<ul>

								</ul>
							</div>
						</div>

					</form>

				</div>

				<div class="col-sm-12 select-table table-striped">
					<table id="bootstrap-table"></table>
				</div>
			</div>
		</div>
	</div>
	<th:block th:include="include :: baseJs" />
	<th:block th:include="include :: layout-latest-js" />
	<th:block th:include="include :: ztree-js" />
	<script th:inline="javascript">
		$(function() {
		    var panehHidden = false;
		    if ($(this).width() < 769) {
		        panehHidden = true;
		    }
		    $('body').layout({ initClosed: panehHidden, west__size: 300 });
	     	// 回到顶部绑定
	    	if ($.fn.toTop !== undefined) {
	    		var opt = {
	    			win:$('.ui-layout-center'),
	    			doc:$('.ui-layout-center')
	    		};
	    		$('#scroll-up').toTop(opt);
	    	}
		    queryUserList();
		    queryDeptTree();
		});

	function queryUserList(){
		var singleSelect=false;
			if([[${selectType=='S'}]]){
				singleSelect=true;
			}
			var options = {
		        url: ctx+ "mpad/user/userList",
		        queryParams: queryParams,
				uniqueId:"userCode",
		        modalName: "用户",
		        showSearch: false,
		        showRefresh: false,
		        showToggle: false,
				onCheck:onCheck,
				onUncheck:onUncheck,
				onCheckAll: onCheckAll,
				onUncheckAll: onUncheckAll,
				singleSelect:singleSelect,
		        showColumns: false,
		        clickToSelect: true,
		        rememberSelected: true,
		        columns: [{
		        	field: 'state',
		            checkbox: true,
					formatter: function(value, row, index) {
						if($("#userCode").val().indexOf(row.userCode)>-1){
							return true;
						}
						return false;
					}
		        },
		        {
		            field: 'userCode',
		            title: '用户工号',
		            sortable: true
		        },
		        {
		            field: 'userName',
		            title: '用户名称'
		        },
		        {
		            field: 'orgId',
		            title: '组织编码'
		        },
		        {
		        	field: 'orgName',
		        	title: '组织名称'
		        }]
		    };
		    $.table.init(options);
		}


	function queryDeptTree(){

		var url = ctx + "mpad/org/treeData?orgCode="+[[${orgCode}]];
		var options = {
			url: url,
			async:{
				enable: true,
				type: "get",//根据请求类型自己定义
				url: ctx + "mpad/org/treeData",
				autoParam: ["id=parentCode"],//这个是会自动加上的参数，这里的参数，可以用别名，例如：id=Path,传参的时候就是ids = '1'；但是需要注意的是，这里的参数只支持ztree设置的数据属性，例如我们想传递Path字段，就不能在这里自动匹配了，需要另外写方法了
				dataFilter: function(treeId, parentNode, resData){
					//这里要过滤你的数据，把请求回来的数据组装成你想要的格式,resData就是请求接口返回的数据
					//我们假装这里的数据就是我们自己想要的
					return resData

				}

			},
			expandLevel: 1,
			onClick : zOnClick,
			onAsyncSuccess : zTreeOnAsyncSuccess,
		};
		$.tree.init(options);

		function zOnClick(event, treeId, treeNode) {
			$("#orgCode").val(treeNode.id);
			$.table.search();
		}
		function zTreeOnAsyncSuccess(event, treeId, treeNode, msg) {
			if (!$._tree) {
				alert("error!");
				return
			}
			var selectedNode = $._tree.getSelectedNodes();
			var nodes = $._tree.getNodes();
			$._tree.expandNode(nodes[0], true);
		}

	}
		function queryParams(params) {
			var search = $.table.queryParams(params);
			return search;
		}

		/* 添加用户-选择用户-提交  */
		function submitHandler() {
			var loginName=$("#userCode").val();
			var userName=$("#userName").val();
      var orgCode=$("#selectOrgCode").val();
      var orgName=$("#selectOrgName").val();
      var indexVal =$("#indexVal").val();

			parent.choiceUserCallback(loginName,userName);
			var callback= [[${callback}]]
			if(callback!=null && callback!=''){
				parent.eval(callback+'("'+loginName+'","'+userName+'","'+orgCode+'","'+orgName+'","'+indexVal+'")');
			}

			var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
		  	parent.layer.close(index);
		}


		$('#btnExpand').click(function() {
			$._tree.expandAll(true);
		    $(this).hide();
		    $('#btnCollapse').show();
		});

		$('#btnCollapse').click(function() {
			$._tree.expandAll(false);
		    $(this).hide();
		    $('#btnExpand').show();
		});

		$('#btnRefresh').click(function() {
			queryDeptTree();
		});

		function onCheck(row, $element){
			var arrUserCode=[];
			var arrUserName=[];

			var userCodeS=$("#userCode").val();
			var userNameS=$("#userName").val();
			if(userCodeS!=null && userCodeS!=''){
				arrUserCode=userCodeS.split(",");
				arrUserName=userNameS.split(",");
			}
			if(!arrUserCode.includes(row.userCode)){
				if([[${selectType=='S'}]]){
					arrUserCode=[row.userCode];
					arrUserName=[row.userName];
					$("#"+userCodeS).remove();
					addNameSpan(row.userCode,row.userName);
					//设置 orgCode,orgName
          addOrgInfo(row.orgId,row.orgName);
				}else{
					arrUserCode.push(row.userCode);
					arrUserName.push(row.userName);
					addNameSpan(row.userCode,row.userName);
				}
			}
			$("#userCode").val(arrUserCode.join(","));
			$("#userName").val(arrUserName.join(","));



		}
		function onUncheck(row, $element){
			var userCodeS=$("#userCode").val();
			var userNameS=$("#userName").val();
			var arrUserCode=userCodeS.split(",");
			var arrUserName=userNameS.split(",");

			arrUserCode.splice(arrUserCode.indexOf(row.userCode),1);
			arrUserName.splice(arrUserName.indexOf(row.userName),1);
			$("#userCode").val(arrUserCode.join(","));
			$("#userName").val(arrUserName.join(","));

			$("#"+row.userCode).remove();
		}


		function openText(id){
			var length = $("#"+id).val().length;
			if (length > 15) {
				layer.alert($("#"+id).val(), {
					title : "信息内容",
					shadeClose : true,
					btn : [ '确认' ],
					btnclass : [ 'btn btn-primary' ],
				});
			}
		}
		function onCheckAll(rowsAfter,rowsBefore){
			for (var i=0;i<rowsAfter.length;i++){
				onCheck(rowsAfter[i]);
			}

		}
		function onUncheckAll(e,rowsAfter,rowsBefore){
			for (var i=0;i<rowsAfter.length;i++){
				onUncheck(rowsAfter[i]);
			}
		}

		function resetData(){
			$.form.reset();
		}
		function initNameSpan(){
			var userCode=$("#userCode").val();;

			if(userCode!=null && userCode!=''){
				var arr=userCode.split(",");
				var arr2=$("#userName").val().split(",");
				$("#nameDiv").find("ul").empty();
				var html="";
				for(var i=0;i<arr.length;i++){
					html+="<li id='"+arr[i]+"' ><span class='nameSpan' onclick='deleteNameSpan(&quot;" + arr[i] +"&quot;,&quot;" + arr2[i] +"&quot;)'>"+ arr[i] +"-"+arr2[i] +"<i class='fa fa-close'></i></span></li>";
				}
				$("#nameDiv").find("ul").append(html);
			}
		}
		initNameSpan();
		function addNameSpan(userCode,userName){
			var html="<li id='"+userCode+"'><span class='nameSpan'  onclick='deleteNameSpan(&quot;" + userCode +"&quot;,&quot;" + userName +"&quot;)'>"+ userCode +"-"+userName +"<i class='fa fa-close'></i></span></li>";

			$("#nameDiv").find("ul").append(html);
		}
    function addOrgInfo(orgCode,orgName){
      var orgCodeEl = '<input id="selectOrgCode" name="selectOrgCode" value="'+ orgCode +'" type="hidden" />';
      var orgNameEl = '<input id="selectOrgName" name="selectOrgName" value="'+ orgName +'" type="hidden" />';
      $("#nameDiv").append(orgCodeEl + orgNameEl);
    }
		function deleteNameSpan(usreCode,userName){
			var json={};
			json.userCode=usreCode;
			json.userName=userName;
			onUncheck(json);
			$("#bootstrap-table").bootstrapTable('updateByUniqueId',{
				id:usreCode,
				row: {
					state: false
				}
			});

		}


	</script>
</body>
</html>
