<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('后评估表评审')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="form-group" th:include="include :: step(approveKind='ZZZC_AFTER',currentNode=${activityCode})"></div><br/>

        <form class="form-horizontal m" id="form-afterAssess-add" th:Object="${after}">
            <input id="mainId" name="mainId"  th:value="${main.mainId}" type="hidden">
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
            <input id="afterId" name="afterId"  th:value="${after.afterId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">

          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">项目编号：</label>
                                    <div class="col-sm-10">
                                        <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">项目名称：</label>
                                    <div class="col-sm-10">
                                        <div class="form-control-static" th:utext="${main.projectName}"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">推广单位部门：</label>
                                    <div class="col-sm-10">
                                        <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.tgfDeptCode)}"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">受让单位部门：</label>
                                    <div class="col-sm-10">
                                        <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.srfDeptCode)}"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">推广方负责人：</label>
                                    <div class="col-sm-10">
                                        <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='tgfFzrCode',userNameId='tgfFzrName',value=${main.tgfFzrCode},see=true)"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">受让方负责人：</label>
                                    <div class="col-sm-10">
                                        <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='srfFzrCode',userNameId='srfFzrName',value=${main.srfFzrCode},see=true)"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">评估次数：</label>
                                    <div class="col-sm-10">
                                        <div class="form-control-static" th:utext="${after.afterPgcs}"></div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">结题日期：</label>
                                    <div class="col-sm-10">
                                        <div class="form-control-static" th:utext="${main.projectJtDate}"></div>
                                    </div>
                                </div>

                         </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion2"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmgx" class="collapsed">
                                项目情况
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmgx" class="panel-collapse collapse in">
                        <ul class="sortable-list connectList agile-list ui-sortable">
                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label  p-title">技术附件项目目标：</label>
                                            <div class="col-sm-9">
                                                <div class="form-control-static" th:utext="${teachAtt.teachAttTarget}"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label  p-title">实施前：</label>
                                            <div class="col-sm-9">
                                                <div class="form-control-static" th:utext="${after.afterAssessBefore}"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label  p-title">结题时：</label>
                                            <div class="col-sm-9">
                                                <div class="form-control-static" th:utext="${after.afterAssessJt}"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label  p-title">目前：</label>
                                            <div class="col-sm-9">
                                                <div class="form-control-static" th:utext="${after.afterAssessBq}"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>

                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion4" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#fj" aria-expanded="false" class="collapsed">相关附件
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>
                    <!--折叠区域-->
                    <div id="fj" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">上传附件：</label>
                                    <div class="col-sm-10" style="margin-bottom: 10px">
                                        <div th:include="/component/attachment :: init(display='none',sourceId=${after.afterId},sourceModule='ZZJT_HPGFJ',id='afterFj',name='afterFj',see=true)">
                                        </div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->

            <div th:if="${activityCode=='Manual3'}" class="panel-group" id="accordion5" role="tablist" aria-multiselectable="true" >
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#czxx" aria-expanded="false" class="collapsed">操作选项
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>
                    <!--折叠区域-->
                    <div id="czxx" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">是否指定人员：</label>
                                    <div class="col-sm-10">
                                        <div th:include="/component/radio :: init(id='afterFreeflow',name='afterFreeflow',
                            businessType='MPTY',dictCode='is_yes_no',value=${after.afterFreeflow},callback='changeFlow',isrequired=true)"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row" id="xuanze" style="display: none">
                                <div class="form-group">
                                    <label id="zhid" class="col-sm-2 control-label">选择指定人员：</label>
                                    <div class="col-sm-8" th:include="/component/selectUser :: init(userCodeId='afterUserCode',userNameId='afterUserName',value=*{afterUserCode},selectType='S')"></div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->
            <!-- 审批-->
            <th:block th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=${processInstanceId},required='true',freeFlow='false')"></th:block>

        </form>
        <div class="m">
            <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
        </div>
    </div>
    <div class="row">
		<div class="toolbar toolbar-bottom" role="toolbar" >
            <button th:if="${activityCode=='Manual3'}" type="button" class="btn btn-primary"
                    onclick="saveHandler()">
                <i class="fa fa-check"></i>暂 存
            </button>

            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

			<th:block th:include="component/wfReturn:: init(taskId=${taskId},callback=wfReturn)"/>

            <button type="button" class="btn btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>
	</div>
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="component/wfCommentList::init(processInstanceId=${processInstanceId})"></div>



    <script th:inline="javascript">
        var prefix = ctx + "zzjt/afterAssess"

        $("#form-afterAssess-add").validate({
            focusCleanup: true
        });

        function saveHandler() {
            var config = {
                url: prefix + "/add",
                type: "post",
                dataType: "json",
                data: $('#form-afterAssess-add').serialize(),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $("#afterId").val(result.data.afterId);
                    $.modal.alertSuccess(result.msg);
                    $.modal.closeLoading();
                }
            };
            $.ajax(config)
        }

        function submitHandler(){
            if ($.validate.form()) {
                $.modal.confirm("确认提交吗？", function () {
                    $.operate.saveTabAlert(prefix + "/submitWF",$('#form-afterAssess-add').serialize());
                })
            }
        }
        //流程跟踪
        function workFlowProcess() {
            window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + [[${processInstanceId}]]);
        }
        changeFlow([[${after.afterFreeflow}]]);
        function changeFlow(str) {
           if(str=='1'){
               $("#xuanze").show();
               $("#afterUserName").prop("required","required");
               $("#zhid").addClass("is-required");
           }else{
               $("#xuanze").hide();
               $("input[name='afterUserCode']").val("");
               $("input[name='afterUserName']").val("");
               $("#afterUserName").removeAttr('required');
               $("#zhid").removeClass("is-required");
           }
        }

        //退回流程
        function wfReturn(activityKey) {
            if ($.validate.form()) {
                var data = $('#form-afterAssess-add').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
            }
        }

    </script>
</body>
</html>