<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('后评估表列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-3 control-label">项目编号：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="projectNumLike" type="text">
                        </div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-3 control-label">项目名称：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="projectNameLike" type="text">
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">受让单位部门：</label>
                        <div class="col-sm-8" th:include="/component/selectOrg :: init(orgCodeId='srfDwdeptCode',orgNameId='srfDwdeptName',selectType='S')"></div>
                    </div>
                </div>
                    <div class="select-list" style="float: right">
                        <ul>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
              <!--  <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>-->
                <!--<a class="btn btn-success" onclick="doStart()" >
                    <i class="fa fa-plus"></i> 触发
                </a>-->
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzjt/afterAssess";

        $(function() {
            var options = {
                url: prefix + "/queryAfter",
                detailUrl: prefix + "/queryDetail/{id}",
                exportUrl: prefix + "/export",
                modalName: "后评估表",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'afterId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'srfDeptName',
                    title: '受让单位部门'
                },
                {
                    field: 'currentActivityName',
                    title: '状态'
                },
                {
                    field: 'currentOperator',
                    title: '当前操作人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.afterId + '\')"><i class="fa fa-eye"></i>详情</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
        function doStart(){
            $.modal.confirm("确认提交吗？", function () {
                if ($.validate.form()) {
                    $.operate.saveTabAlert(ctx+"zzjt/afterAssess/doStart2");
                }
            })
        }
    </script>
</body>
</html>