<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('后评估跟踪统计')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-3 control-label">项目编号：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="projectNumLike" type="text">
                        </div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-3 control-label">项目名称：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="projectNameLike" type="text">
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label class="col-sm-3 control-label">推广方负责人:</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" name="tgfFzrNameLike" placeholder="支持模糊查询"/>
                        </div>
                    </div>
                </div>
                    <div class="row form-group">
                        <div class="col-sm-4">
                            <label class="col-sm-3 control-label">受让单位部门：</label>
                            <div class="col-sm-8" th:include="/component/selectOrg :: init(orgCodeId='srfDeptCode',orgNameId='srfDeptName',selectType='S')"></div>
                        </div>
                        <div class="col-sm-4">
                            <label class="col-sm-3 control-label">推广单位部门：</label>
                            <div class="col-sm-8" th:include="/component/selectOrg :: init(orgCodeId='tgfDeptCode',orgNameId='tgfDeptName',selectType='S')"></div>
                        </div>

                        <div class="col-sm-4">
                            <label class="col-sm-3 control-label">受让方负责人:</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="srfFzrNameLike" placeholder="支持模糊查询"/>
                            </div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="col-sm-4">
                            <label class="col-sm-3 control-label">年度:</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="year" />
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <label class="col-sm-3 control-label">项目主管:</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="projectZgNameLike" placeholder="支持模糊查询"/>
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <label class="col-sm-3 control-label">评估次数:</label>
                            <div class="col-sm-8">
                                <input type="text" class="form-control" name="afterPgcs"/>
                            </div>
                        </div>
                    </div>
                    <div class="select-list" style="float: right">
                        <ul>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
              <!--  <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>-->
                <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzjt/afterAssess";

        $(function() {
            var options = {
                url: prefix + "/queryAfterReport",
                detailUrl: prefix + "/queryDetail/{id}",
                exportUrl: prefix + "/export",
                firstLoad: false,
                modalName: "后评估跟踪统计表",
                columns: [
                {
                    field: 'afterId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'srfDeptName',
                    title: '受让单位部门'
                },
                {
                    field: 'afterPgcs',
                    title: '评估次数'
                },
                {
                    field: 'tgfDeptName',
                    title: '推广单位部门'
                },
                {
                    field: 'tgfXmzgName',
                    title: '项目主管'
                },
                {
                    field: 'tgfFzrName',
                    title: '项目负责人'
                },
                {
                    field: 'srfFzrName',
                    title: '受让方项目负责人'
                },
                {
                    field: 'projectJtDate',
                    title: '项目结题日期',
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'afterAssessBefore',
                    title: '项目情况实施前'
                },
                {
                    field: 'afterAssessJt',
                    title: '项目情况结题时'
                },
                {
                    field: 'afterAssessBq',
                    title: '项目情况当前'
                },
                {
                    field: 'currentActivityName',
                    title: '当前状态'
                },
                {
                    field: 'currentOperator',
                    title: '当前操作人'
                },
                ]
            };
            $.table.init(options);
        });
        function doStart(){
            $.modal.confirm("确认提交吗？", function () {
                if ($.validate.form()) {
                    $.operate.saveTabAlert(ctx+"zzjt/afterAssess/doStart");
                }
            })
        }
    </script>
</body>
</html>