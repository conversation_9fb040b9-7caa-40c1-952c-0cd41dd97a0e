<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('项目结算表审批')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div  class="form-group" th:include="include :: step(approveKind='ZZZC_ZZJT',currentNode=${activityCode})"></div>
        <form class="form-horizontal m" id="form-check-add" th:object="${settle}">
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
            <input id="guid" name="guid"  th:value="${main.mainId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目名称：</label>
                                        <div class="col-sm-4">
                                            <input id="settleGuid" name="settleGuid"  th:field="*{settleGuid}" type="hidden">
                                            <div class="form-control-static" th:utext="${main.projectName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">项目编号：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目移植方：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfDeptName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">受让单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.srfDeptName}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfFzrName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">联系电话：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfXmfzrTel}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div id="ck" class="panel-collapse collapse in" aria-expanded="false">
                                    <div class="panel-body">

                                        <div class="form-group">
                                            <label class="col-sm-12">点击<a class="form_list_a" th:onclick="$.modal.openTab('定价评审表',ctx+'zzlx/teachFix/openFix/'+[[${main.mainId}]])">此处</a>查看定价评审表</label>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-sm-12">点击<a th:onclick="$.modal.openTab('结题报告',ctx+'zzjt/report/queryDetail/'+[[${main.mainId}]])" class="form_list_a">此处</a>查看结题报告</label>
                                        </div>

                                    </div>
                                </div>

                                <div class="panel-group" id="accordion3"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xmmb" class="collapsed">
                                                    项目费用预估（单位：万元）
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="xmmb" class="panel-collapse collapse in">
                                            <table class="table table-bordered table-hover" style="margin-top:20px;border: 1px solid #e7e7e7;">
                                                <tfoot>
                                                <tr>
                                                    <td style="text-align: center; ">差旅费</td>
                                                    <td style="text-align: center; ">通讯费</td>
                                                    <td style="text-align: center; ">人工费</td>
                                                    <td style="text-align: center; ">其他费用</td>
                                                    <td style="text-align: center; ">合计</td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; "> <div class="form-control-static"  th:utext="${#numbers.formatDecimal(teachCostTotal.travelCost,1,2)}" style="text-align: left"></div></td>
                                                    <td style="text-align: center; "> <div class="form-control-static"  th:utext="${#numbers.formatDecimal(teachCostTotal.comCost,1,2)}" style="text-align: left"></div></td>
                                                    <td style="text-align: center; "> <div class="form-control-static"  th:utext="${#numbers.formatDecimal(teachCostTotal.laborCost,1,2)}" style="text-align: left"></div></td>
                                                    <td style="text-align: center; "> <div class="form-control-static"  th:utext="${#numbers.formatDecimal(teachCostTotal.otherCost,1,2)}" style="text-align: left"></div></td>
                                                    <td style="text-align: center; "> <div class="form-control-static"  th:utext="${#numbers.formatDecimal(teachCostTotal.total,1,2)}" style="text-align: left"></div></td>
                                                </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xtsy" class="collapsed">
                                                    项目实施费用
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="xtsy" class="panel-collapse collapse in">
                                            <table class="table table-bordered table-hover" style="margin-top:20px;border: 1px solid #e7e7e7;">
                                                <tfoot>
                                                <tr>
                                                    <td style="text-align: center; ">人工费A</td>
                                                    <td colspan="3" style="text-align: left; ">
                                                        <div class="form-control-static"  th:utext="*{settleRgfy}" style="text-align: left"></div>
                                                        <input name="settleRgfy" id="settleRgfy" th:value="*{settleRgfy}" type="hidden" >
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; ">差旅费B</td>
                                                    <td colspan="3" style="text-align: left; ">
                                                        <div class="form-control-static"  th:utext="*{settleClfy}" style="text-align: left"></div>
                                                        <input name="settleClfy" id="settleClfy" th:value="*{settleClfy}" type="hidden" >
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; ">通讯费C</td>
                                                    <td colspan="3" style="text-align: left; ">
                                                        <div class="form-control-static"  th:utext="*{settleTsfy}" style="text-align: left"></div>
                                                        <input name="settleTsfy" id="settleTsfy" th:value="*{settleTsfy}" type="hidden" >
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; ">其他费D</td>
                                                    <td colspan="3" style="text-align: left; ">
                                                        <div class="form-control-static"  th:utext="*{settleQtfy}" style="text-align: left"></div>
                                                        <input name="settleQtfy" id="settleQtfy" th:value="*{settleQtfy}" type="hidden" >
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; ">分包费用E</td>
                                                    <td colspan="3" style="text-align: left; ">
                                                        <div class="form-control-static" id="settleFbDiv"  style="text-align: left"></div>  <span style="color: red">各分包项目费用合计*1.06</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; ">管理费(A+B+C+D+E)*12%</td>
                                                    <td colspan="3" style="text-align: left; ">
                                                        <div class="form-control-static" id="settleGlfyDiv" th:utext="*{settleGlfy}" style="text-align: left"></div>
                                                        <input name="settleGlfy" id="settleGlfy" th:value="*{settleGlfy}" type="hidden">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; ">费用合计(A+B+C+D+E)*112%</td>
                                                    <td colspan="3" style="text-align: left; ">
                                                        <div class="form-control-static" id="settleSumFyDiv" th:utext="*{settleSumFy}" style="text-align: left"></div>
                                                        <input name="settleSumFy" id="settleSumFy" th:value="*{settleSumFy}" type="hidden">
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; ">差旅费总税额</td>
                                                    <td colspan="3" style="text-align: left; ">
                                                        <div class="form-control-static" id="settleClfySeDiv" th:utext="*{settleClfySe}" style="text-align: left"></div>
                                                        <input name="settleClfySe" id="settleClfySe" th:value="*{settleClfySe}" type="hidden">
                                                        <span style="color: red">注：该金额已包含在收入合计中</span>
                                                    </td>
                                                </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>
                                </div>


                </div>
                    </div>
        </div>
            <th:block th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=${processInstanceId},required='true',freeFlow='false')"></th:block>

        </form>
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
    </div>
    </div>
<div class="toolbar toolbar-bottom" role="toolbar">

            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

            <th:block th:include="/component/wfReturn :: init(taskId=${taskId},callback=wfReturn)"/>
			<button type="button" class="btn  btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>

<!--审批历史-->
<div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
     th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})">
</div>

    <script th:inline="javascript">
        var prefix = ctx + "zzjt/settle"

        changeCost();

        $("input[name='jtysFinishEnd']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });

        //点击查看定价评审表
        function fixDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "定价评审",
                content: ctx + "zzlx/teachFix/openFix/"+[[${main.mainId}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }

        //点击查看结题报告
        function jtReportDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "结题报告",
                content: ctx + "zzjt/report/queryDetail/"+[[${main.mainId}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }


        $("#form-check-add").validate({
            focusCleanup: true
        });

        function saveHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/add", $('#form-check-add').serialize());
            }
        }

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/submitWF", $('#form-check-add').serialize());
            }
        }

        //流程跟踪
        function openProcessTrack(processId){
            window.open(ctxGGMK+"web/EWPI01?inqu_status-0-processInstanceId="+processId);
        }

        function changeCost(){
            var settleRgfy=$("#settleRgfy").val();
            if(settleRgfy==null||settleRgfy==''){
                settleRgfy=0.0;
            }
            var settleClfy=$("#settleClfy").val();
            if(settleClfy==null||settleClfy==''){
                settleClfy=0.0;
            }
            var settleTsfy=$("#settleTsfy").val();
            if(settleTsfy==null||settleTsfy==''){
                settleTsfy=0.0;
            }
            var settleQtfy=$("#settleQtfy").val();
            if(settleQtfy==null||settleQtfy==''){
                settleQtfy=0.0;
            }
            var total=parseFloat(settleRgfy)+parseFloat(settleClfy)+parseFloat(settleTsfy)+parseFloat(settleQtfy);
            var settleFb=0.0;
            $("#settleFbDiv").html(settleFb.toFixed(2));
        }

        //退回流程
        function wfReturn(activityKey) {
            if ($.validate.form()) {
                var data = $('#form-check-add').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
            }
        }
    </script>
</body>
</html>