<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('【奖励通知单统计报表】列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="search-collapse">
            <form id="formId"  class="form-horizontal" style="width: 99%;">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目编号:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectNumLike" placeholder="支持模糊查询"/>
                        </div>
                        <label class="col-sm-2 control-label">项目名称:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectNameLike" placeholder="支持模糊查询"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">推广方负责人:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="tgfFzrNameLike" placeholder="支持模糊查询"/>
                        </div>
                        <label class="col-sm-2 control-label">受让方负责人:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="srfFzrNameLike" placeholder="支持模糊查询"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目主管:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectZgNameLike" placeholder="支持模糊查询"/>
                        </div>

                    </div>
                    <div class="row col-sm-12">
                        <div class="col-sm-6 form-group">
                            <label class="col-sm-4 control-label">奖励通知日期：</label>
                            <div class="col-sm-8">
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='noticeXfDateMin',id='noticeXfDateMin')"/>
                                </div>
                                <span>~</span>
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='noticeXfDateMax',id='noticeXfDateMax')"/>
                                </div>
                            </div>
                        </div>
                    </div>


                <div class="form-group">
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-4">
                    </div>
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-4">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                            <i class="fa fa-search"></i>
                            &nbsp;搜索
                        </a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                            <i class="fa fa-refresh"></i>
                            &nbsp;重置
                        </a>
                    </div>
                </div>
            </form>
        </div>

                <div class="btn-group-sm" id="toolbar" role="group">
                    <a class="btn btn-warning" onclick="$.table.exportExcel()">
                        <i class="fa fa-download"></i> 导出
                    </a>
                </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzjt/rewardNotice";

    var projectTypeList = [[${@dict.getDictList('KTTG','projectType')}]];
    var projectAreaList = [[${@dict.getDictList('KTTG','projectArea')}]];
    var projectKindList = [[${@dict.getDictList('KTTG','payKind')}]];
    var projectSource = [[${@dict.getDictList('KTTG','projectSource')}]];

    $(function () {
        var options = {
            url: prefix + "/reportList",
            createUrl: prefix + "/add",
            exportUrl: prefix + "/exportExcel",
            showFooter: true,
            modalName: "推广移植奖励通知统计表",
            columns: [
                {
                    field: 'MAIN_ID',
                    visible: false
                },
                {
                    field: 'PROJECT_NUM',
                    title: '项目编号',
                    footerFormatter:function (value) {
                        return "合计：";
                    }
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'tgfXmzgName',
                    title: '项目主管'
                },
                {
                    field: 'tgfDeptName',
                    title: '推广单位部门'
                },
                {
                    field: 'srfDeptName',
                    title: '受让单位部门'
                },
                {
                    field: 'NOTICE_XF_DATE',
                    title: '奖励通知日期',
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'tgfYwmainKfpje',
                    title: '推广方奖励金额',
                    footerFormatter:function (value) {
                        var sumBalance = 0;
                        for (var i in value) {
                            if(value[i].tgfYwmainKfpje!=null&&value[i].tgfYwmainKfpje!=''){
                                sumBalance += parseFloat(value[i].tgfYwmainKfpje);
                            }
                        }
                        sumBalance = sumBalance.toFixed(2);
                        return  sumBalance;
                    }
                },
                {
                    field: 'srfYwmainKfpje',
                    title: '受让方奖励金额',
                    footerFormatter:function (value) {
                        var sumBalance = 0;
                        for (var i in value) {
                            if(value[i].srfYwmainKfpje!=null&&value[i].srfYwmainKfpje!=''){
                                sumBalance += parseFloat(value[i].srfYwmainKfpje);
                            }
                        }
                        sumBalance = sumBalance.toFixed(2);
                        return  sumBalance;
                    }
                },
                {
                    field: 'tgfFzrName',
                    title: '推广方负责人'
                },
                {
                    field: 'srfFzrName',
                    title: '受让方负责人'
                },
                {
                    field: 'currentActivityNameTgf',
                    title: '推广方奖励状态'
                },
                {
                    field: 'currentActivityNameSrf',
                    title: '受让方奖励状态'
                }
                ]
        };
        $.table.init(options);
    });

    function shuchu(obj){
        if ($(obj).prop("checked")) {
            $.table.showColumn(obj.id);
        } else {
            $.table.hideColumn(obj.id);
        }
    }

</script>
</body>
</html>