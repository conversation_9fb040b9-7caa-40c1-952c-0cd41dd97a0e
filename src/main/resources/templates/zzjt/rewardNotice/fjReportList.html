<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('【推广移植发奖书统计表】列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="search-collapse">
            <form id="formId"  class="form-horizontal" style="width: 99%;">
                    <div class="row col-sm-12">
                        <div class="col-sm-6 form-group">
                            <label class="col-sm-4 control-label">发奖日期：</label>
                            <div class="col-sm-8">
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='fjDateMin',id='fjDateMin')"/>
                                </div>
                                <span>~</span>
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='fjDateMax',id='fjDateMax')"/>
                                </div>
                            </div>
                        </div>
                    </div>


                <div class="form-group">
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-4">
                    </div>
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-4">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                            <i class="fa fa-search"></i>
                            &nbsp;搜索
                        </a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                            <i class="fa fa-refresh"></i>
                            &nbsp;重置
                        </a>
                    </div>
                </div>
            </form>
        </div>

                <div class="btn-group-sm" id="toolbar" role="group">
                    <a class="btn btn-warning" onclick="$.table.exportExcel()">
                        <i class="fa fa-download"></i> 导出
                    </a>
                </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzjt/rewardNotice";

    var projectTypeList = [[${@dict.getDictList('KTTG','projectType')}]];
    var projectAreaList = [[${@dict.getDictList('KTTG','projectArea')}]];
    var projectKindList = [[${@dict.getDictList('KTTG','payKind')}]];
    var projectSource = [[${@dict.getDictList('KTTG','projectSource')}]];

    $(function () {
        var options = {
            url: prefix + "/fjList",
            createUrl: prefix + "/add",
            exportUrl: prefix + "/fjExcel",
            firstLoad: false,
            modalName: "推广移植发奖书统计表",
            columns: [
                {
                    field: 'PROJECT_NUM',
                    title: '项目编号'
                },
                {
                    field: 'fjje',
                    title: '发奖金额'
                },
                {
                    field: 'jlType',
                    title: '奖励类型'
                },
                {
                    field: 'fjDate',
                    title: '发奖日期'
                },
                {
                    field: 'srfDeptName',
                    title: '受让单位部门'
                },
                {
                    field: 'tgfDeptName',
                    title: '推广单位部门'
                },
                {
                    field: 'tgfXmzgName',
                    title: '项目主管'
                },
                ]
        };
        $.table.init(options);
    });

    function shuchu(obj){
        if ($(obj).prop("checked")) {
            $.table.showColumn(obj.id);
        } else {
            $.table.hideColumn(obj.id);
        }
    }

</script>
</body>
</html>