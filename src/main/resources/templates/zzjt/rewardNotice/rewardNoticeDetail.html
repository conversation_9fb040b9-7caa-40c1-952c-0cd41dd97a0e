<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('奖励审批表详情')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-check-add" th:object="${rewardNotice}">
            <input id="noticeId" name="noticeId"  th:value="*{noticeId}" type="hidden">
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">奖励通知书
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                                    <div class="row">
                                        <div class="form-group">
                                            <label class="col-sm-12">推广单位部门：<span  th:utext="${main.tgfDeptName}" ></span>;受让单位部门<span  th:utext="${main.srfDeptName}" ></span></label>
                                            <div class="col-sm-12">
                                                贵单位技术输出/受让项目 <span  th:utext="${main.projectNum}" ></span><span  th:utext="${main.projectName}" ></span>已履行完毕，按照规定的程序和标准我们组织了对项目的评估，估计总分为<span th:utext="${assess.assessProPgSum}"></span><input type="hidden" name="extra1" th:value="${assess.assessProPgSum}">分。根据公司《技术输出项目评估及奖励管理标准》规定下发奖金。
                                                总奖金额为：<span  th:utext="${#numbers.formatDecimal(award.awardDeciJlje,1,2)}" ></span>,其奖励情况为：<br>
                                                <label style="font-weight: bold">本次奖励额为：</label><span th:text="${#numbers.formatDecimal(rewardNotice.noticeDeciJlje,1,2)}"></span>万元<br>
                                                <label style="font-weight: bold">管理留成为：本次预励奖金额 X</label>  <span th:text="*{noticeLcXs}"></span>%＝<span th:text="${#numbers.formatDecimal(rewardNotice.noticeLcJe,1,2)}"></span>万元<br>
                                                 <label style="font-weight: bold">实发励金额为：</label><span th:text="${#numbers.formatDecimal(rewardNotice.noticeSfJlje,1,2)}"></span>万元<br>
                                                <div th:if="${main.projectArea eq 'gfn'}">
                                                 <label style="font-weight: bold">推广励金额为:实发奖励金额 X </label> <span th:text="*{noticeTgfXs}"></span>%＝<span th:text="${#numbers.formatDecimal(rewardNotice.noticeTgfJlje,1,2)}">万元<br>
                                                 <label style="font-weight: bold">受让奖励金额为:实发预奖励金额 X </label> <span th:text="*{noticeSrfXs}"></span>%＝<span th:text="${#numbers.formatDecimal(rewardNotice.noticeSrfJlje,1,2)}">万元<br>
                                               </div>
                                                *注:请推广方单位信凭此单进行奖金分配，受让方按奖励授权范围审批。<br>
                                            </div>
                                            <label class="col-sm-10 control-label"><div  th:include="/component/select :: init(see=true,value=${main.tgfDwdeptCode},businessType='KTTG', dictCode='rewardNoticeDept')"></div></label>
                                            <label class="col-sm-10 control-label">日期：<span  th:utext="*{noticeXfDate}" ></span>
                                            </label>
                                        </div>
                                    </div>

                                </div>
                            </div>
        </div>
                    </div>
        </form>
<div class="toolbar toolbar-bottom" role="toolbar">

			<button type="button" class="btn  btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>

    <script th:inline="javascript">
        var prefix = ctx + "zzjt/rewardNotice"


        $("input[name='jtysFinishEnd']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });


        $("#form-check-add").validate({
            focusCleanup: true
        });


        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/submitWF", $('#form-check-add').serialize());
            }
        }

        //流程跟踪
        function openProcessTrack(processId){
            window.open(ctxGGMK+"web/EWPI01?inqu_status-0-processInstanceId="+processId);
        }

        function sumLC(){
            var noticeDeciJlje=$("#noticeDeciJlje").val();
            if(noticeDeciJlje==null||noticeDeciJlje==''){
                noticeDeciJlje=0.0;
            }
            var noticeLcXs=$("#noticeLcXs").val();
            if(noticeLcXs==null||noticeLcXs==''){
                noticeLcXs=0.0;
            }
            var noticeLcJe=parseFloat(noticeDeciJlje)*parseFloat(noticeLcXs/100);
            $("#noticeLcJe").val(noticeLcJe.toFixed(2));
            var noticeSfJlje=noticeDeciJlje-noticeLcJe;
            $("#noticeSfJlje").val(noticeSfJlje.toFixed(2));
            sumTG();
        }

        function sumTG(){
            var projectArea=[[${main.projectArea}]];
            if(projectArea=='gfn'){
                var noticeSfJlje=$("#noticeSfJlje").val();
                if(noticeSfJlje==null||noticeSfJlje==''){
                    noticeSfJlje=0.0;
                }
                var noticeTgfXs=$("#noticeTgfXs").val();
                if(noticeTgfXs==null||noticeTgfXs==''){
                    noticeTgfXs=0.0;
                }
                var noticeSrfXs=$("#noticeSrfXs").val();
                if(noticeSrfXs==null||noticeSrfXs==''){
                    noticeSrfXs=0.0;
                }
                var noticeTgfJlje=parseFloat(noticeSfJlje)*parseFloat(noticeTgfXs/100);
                var noticeSrfJlje=parseFloat(noticeSfJlje)*parseFloat(noticeSrfXs/100);
                $("#noticeTgfJlje").val(noticeTgfJlje.toFixed(2));
                $("#noticeSrfJlje").val(noticeSrfJlje.toFixed(2));
            }

        }





        //退回流程
        function wfReturn(activityKey) {
            if ($.validate.form()) {
                var data = $('#form-check-add').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
            }
        }

        function back(){
          alert("aaa");
        }




    </script>
</body>
</html>