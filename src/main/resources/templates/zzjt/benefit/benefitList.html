<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('经济效益表列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label style="width: 100px;">项目编号：</label>
                            <input type="text" name="projectNumLike"/>
                        </li>
                        <li>
                            <label style="width: 100px;">项目名称：</label>
                            <input type="text" name="projectNameLike"/>
                        </li>
                        <li>
                            <label style="width: 100px;">项目负责人：</label>
                            <input name="fzrCode" id="fzrCode" type="hidden"   />
                            <input name="fzrName" id="fzrName"     readonly="readonly" class="form-control" type="text"  onclick="choiceUser('fzrCode','fzrName','S',null,'')">
                        </li>
                       <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="zhreset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<th:block th:include="include :: baseJs"/>
<script th:inline="javascript">
    var prefix = ctx + "zzjt/benefit";

    $(function () {
        var options = {
            url: prefix + "/lcList",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "经济效益表",
            columns: [{
                checkbox: true
            },
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'tgfFzrName',
                    title: '推广方项目负责人'
                },
                {
                    field: 'srfFzrName',
                    title: '受让方项目负责人'
                },
                {
                    field: 'currentActivityName',
                    title: '当前状态'
                },
                {
                    field: 'currentOperator',
                    title: '当前操作人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.mainId + '\')"><i class="fa fa-eye"></i>详情</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });


    function queryParams(params) {
        var search = $.table.queryParams(params);
        // search.processKey = "KYPSProfitApply";
        return search;
    }

    function  zhreset(){
        $("#fzrCode").val("");
        $("#xmzgCode").val("");
        $("#fzrDept").val("");
        $.form.reset();

    }


</script>
</body>
</html>