<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增经济效益表')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-benefit-add" th:object="${benefit}">
            <input id="mainId" name="mainId"  th:value="${main.mainId}" type="hidden">
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
            <input id="settleId" name="settleId"  th:value="${benefit.settleId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">

            <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">

            <div class="form-group">    
                <label class="col-sm-2 control-label">项目编号：</label>
                <div class="col-sm-10">
                    <div class="form-control-static" th:utext="${main.projectNum}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">项目名称：</label>
                <div class="col-sm-10">
                    <div class="form-control-static" th:utext="${main.projectName}"></div>
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">受让单位部门：</label>
                <div class="col-sm-10">
                    <div class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.srfDwdeptCode)}"></div>

                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">推广单位部门：</label>
                <div class="col-sm-10">
                    <div class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.tgfDwdeptCode)}"></div>

                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-2 control-label">项目主管：</label>
                <div class="col-sm-10">
                    <div class="form-control-static" th:utext="${main.srfXmzgName}"></div>
                </div>
            </div>
             <div class="form-group">
                <label class="col-sm-2 control-label">项目类型：</label>
                <div class="col-sm-10">
                    <div th:include="/component/radio :: init(id='projectType',name='projectType',
                    businessType='KTTG',dictCode='projectType',value=${main.projectType} ,see=true)"></div>
                </div>
              </div>
             <div class="form-group">
                <label class="col-sm-2 control-label">项目负责人：</label>
                <div class="col-sm-10">
                    <div class="form-control-static" th:utext="${main.srfFzrName}"></div>
                </div>
            </div>

                    </div>
                </div>
            </div>
        </div>

            <div class="panel-group" id="accordion2"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmgx" class="collapsed">
                                <span style="color: red">*&nbsp;</span>项目概要
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmgx" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="benefitInfor" th:text="${benefit.benefitInfor}" class="form-control" rows="8" required></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion3"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmgs" class="collapsed">
                                <span style="color: red">*&nbsp;</span>经济效益计算函数式
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmgs" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="benefitMulsFunction" th:text="${benefit.benefitMulsFunction}" class="form-control" rows="8" required></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmjs" class="collapsed">
                                <span style="color: red">*&nbsp;</span> 效益计算
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmjs" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="benefitXyMuls" th:text="${benefit.benefitXyMuls}"  class="form-control" rows="4" required></textarea>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label  class="col-sm-2 control-label is-required">该项目年经济效益为</label>
                                <div class="col-sm-4">
                                   <input name="settleJjxy" min="0" th:value="${#numbers.formatDecimal(benefit.settleJjxy,1,2)}" class="form-control" type="number" required/>
                                </div>
                                <label  class="col-sm-0 control-label">万元</label>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <input id="tzsCode" name="tcrUserCode" th:value="${loginCode}" type="hidden">
                                <input id="tzsName" name="tcrUserName" th:value="${loginName}" type="hidden">
                                <input id="tcrDate" name="tcrDate" th:value="${downDate}" type="hidden">
                                <div class="col-sm-3 form-control-static" style="float: right">
                                    提交人：
                                    <span th:utext="${loginName}"></span>
                                    日期：
                                    <span th:utext="${downDate}"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--框-->
            <div class="panel-group" id="accordion4" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#fj" aria-expanded="false" class="collapsed">上传相关附件
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>
                    <!--折叠区域-->
                    <div id="fj" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">1、结题报告附件：</label>
                                    <div class="col-sm-10" th:if="${not #strings.isEmpty(benefit.settleId)}" style="margin-bottom: 10px">
                                        <div th:include="/component/attachment :: init(display='none',sourceId=${benefit.settleId},sourceModule='ZZJT_JTBGFJ',id='reportAttachmentId',name='reportAttachmentId',isrequired=true)">
                                        </div>
                                    </div>
                                    <div class="col-sm-10" th:if="${#strings.isEmpty(benefit.settleId)}" style="margin-bottom: 10px">
                                        <div th:include="/component/attachment :: init(display='none',sourceId=${reportEx.reportId},sourceModule='ZZJT_JTBGFJ',id='reportAttachmentId',name='reportAttachmentId',isrequired=true)">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">2、验收报告证明（扫描件）：</label>
                                    <div class="col-sm-10" style="margin-bottom: 10px">
                                        <div th:include="/component/attachment :: init(display='none',sourceId=${benefit.settleId},sourceModule='ZZJT_YSBGZM',id='ysbgzmId',name='ysbgzmId',isrequired=true)">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">3、效益计算数据来源证明（扫描件）：</label>
                                    <div class="col-sm-10" style="margin-bottom: 10px">
                                        <div th:include="/component/attachment :: init(display='none',sourceId=${benefit.settleId},sourceModule='ZZJT_SJLYZM',id='sjlyId',name='sjlyId',isrequired=true)">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->

        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
            <button type="button" class="btn btn-sm btn-primary"
                    onclick="workFlowProcess()">
                <i class="fa fa-eye"></i>流程跟踪图
            </button>

			<button type="button" class="btn btn-sm btn-primary"
				onclick="saveHandler()">
				<i class="fa fa-check"></i>暂 存
			</button>

            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>&nbsp;
            &nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="component/wfCommentList::init(processInstanceId=${processInstanceId})"></div>


    <script th:inline="javascript">
        var prefix = ctx + "zzjt/benefit"

        $("#form-benefit-add").validate({
            focusCleanup: true
        });

        function saveHandler() {
            var config = {
                url: prefix + "/add",
                type: "post",
                dataType: "json",
                data: $('#form-benefit-add').serialize(),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $("#settleId").val(result.data.settleId);
                    $.modal.alertSuccess(result.msg);
                    $.modal.closeLoading();
                }
            };
            $.ajax(config)
        }
        function submitHandler(){
            $.modal.confirm("确认提交吗？", function () {
                if ($.validate.form()) {
                    $.operate.saveTabAlert(prefix + "/submitWF",$('#form-benefit-add').serialize());
                }
            })
        }

        //流程跟踪
        function workFlowProcess() {
            window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + [[${processInstanceId}]]);
        }

    </script>
</body>
</html>