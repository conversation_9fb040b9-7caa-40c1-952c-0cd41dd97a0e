<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('经济效益跟踪')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <input name="queryType" type="hidden" value="genzong">
                <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-3 control-label">项目编号：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="projectNumLike" type="text">
                        </div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-3 control-label">项目名称：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="projectNameLike" type="text">
                        </div>
                    </div>
                    <div class="col-sm-4">
                        <label class="col-sm-3 control-label">负责人:</label>
                        <div class="col-sm-8">
                            <input type="text" class="form-control" name="tgfFzrNameLike" placeholder="支持模糊查询"/>
                        </div>
                    </div>
                </div>
                <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-3 control-label">受让单位部门：</label>
                        <div class="col-sm-8" th:include="/component/selectOrg :: init(orgCodeId='srfDeptCode',orgNameId='srfDeptName',selectType='S')"></div>
                    </div>
                    <div class="col-sm-4">
                        <label class="col-sm-3 control-label">推广单位部门：</label>
                        <div class="col-sm-8" th:include="/component/selectOrg :: init(orgCodeId='tgfDeptCode',orgNameId='tgfDeptName',selectType='S')"></div>
                    </div>
                </div>
                <div class="select-list" style="float: right">
                    <ul>
                       <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.table.refresh()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<th:block th:include="include :: baseJs"/>
<script th:inline="javascript">
    var prefix = ctx + "zzjt/benefit";

    $(function () {
        var options = {
            url: prefix + "/xyReportList",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "经济效益流程跟踪",
            columns: [{
                checkbox: true
            },
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'tgfDeptName',
                    title: '推广单位部门'
                },
                {
                    field: 'srfDeptName',
                    title: '受让单位部门'
                },
                {
                    field: 'tgfFzrName',
                    title: '项目负责人'
                },
                {
                    field: 'currentActivityName',
                    title: '当前状态'
                },
                {
                    field: 'currentOperator',
                    title: '当前操作人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs " href="javascript:void(0)" onclick="seeXY(\'' + row.recordId + '\')"><i class="fa fa-eye"></i>查看效益信息</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function seeXY(recordId){
        $.modal.openTab("查看评审信息",ctxYWZT+"web/KJXYSHOW?spId="+recordId+"&activityCode=Manual3");
    }

    function queryParams(params) {
        var search = $.table.queryParams(params);
        // search.processKey = "KYPSProfitApply";
        return search;
    }

    function  zhreset(){
        $("#fzrCode").val("");
        $("#xmzgCode").val("");
        $("#fzrDept").val("");
        $.form.reset();

    }


</script>
</body>
</html>