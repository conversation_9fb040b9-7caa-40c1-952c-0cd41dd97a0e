<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('【结题统计报表】列表')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: bootstrap-table-fixed-columns-js" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="search-collapse">
            <form id="formId"  class="form-horizontal" style="width: 99%;">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目编号:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectNumLike" placeholder="支持模糊查询"/>
                        </div>
                        <label class="col-sm-2 control-label">项目名称:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectNameLike" placeholder="支持模糊查询"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">年度:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="year" />
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">推广方负责人:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="tgfFzrNameLike" placeholder="支持模糊查询"/>
                        </div>
                        <label class="col-sm-2 control-label">受让方负责人:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="srfFzrNameLike" placeholder="支持模糊查询"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目主管:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectZgNameLike" placeholder="支持模糊查询"/>
                        </div>
                        <label class="col-sm-2 control-label">状态:</label>
                        <div class="col-sm-4">
                            <div th:include="/component/select :: init(id='projectStatus', name='projectStatus',businessType='KTTG', dictCode='projectStatus',isfirst=true)"></div>
                        </div>
                    </div>
                    <div class="row col-sm-12">
                        <div class="col-sm-6 form-group">
                            <label class="col-sm-4 control-label">项目开始日期：</label>
                            <div class="col-sm-8">
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectStartDateMin',id='projectStartDateMin')"/>
                                </div>
                                <span>~</span>
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectStartDateMax',id='projectStartDateMax')"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 form-group">
                            <label class="col-sm-4 control-label">项目结束时间：</label>
                            <div class="col-sm-8">
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectEndDateMin',id='projectEndDateMin')"/>
                                </div>
                                <span>~</span>
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectEndDateMax',id='projectEndDateMax')"/>
                                </div>
                            </div>
                        </div>
                    </div>


                <div class="form-group">
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-4">
                    </div>
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-4">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                            <i class="fa fa-search"></i>
                            &nbsp;搜索
                        </a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                            <i class="fa fa-refresh"></i>
                            &nbsp;重置
                        </a>
                    </div>
                </div>
            </form>
        </div>

                <div class="btn-group-sm" id="toolbar" role="group">
                    <a class="btn btn-warning" onclick="$.table.exportExcel()">
                        <i class="fa fa-download"></i> 导出
                    </a>
                </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzjt/conclusion";

    var projectTypeList = [[${@dict.getDictList('KTTG','projectType')}]];
    var projectAreaList = [[${@dict.getDictList('KTTG','projectArea')}]];
    var projectKindList = [[${@dict.getDictList('KTTG','payKind')}]];
    var projectSource = [[${@dict.getDictList('KTTG','projectSource')}]];

    $(function () {
        var options = {
            url: prefix + "/reportList",
            createUrl: prefix + "/add",
            exportUrl: prefix + "/exportJt",
            firstLoad: false,
            modalName: "项目结题统计报表",
            pagination: true,
            fixedColumns: true,
            fixedNumber: 2, //固定列数
            columns: [
                {
                    field: 'mainId',
                    visible: false
                },
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'tgfDeptName',
                    title: '推广单位部门'
                },
                {
                    field: 'tgfFzrName',
                    title: '推广方负责人'
                },
                {
                    field: 'srfDeptName',
                    title: '受让单位部门'
                },
                {
                    field: 'srfFzrName',
                    title: '受让方负责人'
                },
                {
                    field: 'tgfXmzgName',
                    title: '项目主管'
                },
                {
                    field: 'projectStatus',
                    title: '状态'
                },
                {
                    field: 'projectStartDate',
                    title: '项目开始日期',
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'projectEndDate',
                    title: '项目结束日期',
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'submitJtDate',
                    title: '结题通知日期',
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'projectJtDate',
                    title: '项目实际结束日期',
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'schedules',
                    title: '知识产权',
                },
                {
                    field: 'laborCostYs',
                    title: '人工费用预算(万元)'
                },
                {
                    field: 'laborCostSj',
                    title: '人工费用实绩(万元)'
                },
                {
                    field: 'payDayJh',
                    title: '人日数(计划)'
                },
                {
                    field: 'payDaySj',
                    title: '人日数(实绩)',
                    formatter: function(value, row, index) {
                        if(row.payDaySjX!=""&&row.payDaySjX!=null){
                            return row.payDaySjX;
                        }else if(value==""&&value==null){
                            return 0;
                        }else{
                            return value;
                        }
                    },
                },
                {
                    field: 'payTotalYs',
                    title: '项目费用预算(万元)'
                },
                {
                    field: 'payTotalSj',
                    title: '项目费用实绩(万元)'
                },
                {
                    field: 'benefitYs',
                    title: '预计效益(万元)'
                },
                {
                    field: 'benefitSj',
                    title: '效益实绩(万元)'
                },
                {
                    field: 'projectJjxyDate',
                    title: '效益审批日期',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'monthAll',
                    title: '月报期数'
                },
                {
                    field: 'fillOnTime',
                    title: '按时填报数'
                },
                {
                    field: 'fillFailure',
                    title: '未及时填报'
                },
                {
                    field: 'fillNot',
                    title: '未填报'
                }
                ]
        };
        $.table.init(options);
    });

    function shuchu(obj){
        if ($(obj).prop("checked")) {
            $.table.showColumn(obj.id);
        } else {
            $.table.hideColumn(obj.id);
        }
    }

</script>
</body>
</html>