<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增结题通知')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div  class="form-group" th:include="include :: step(approveKind='ZZZC_ZZJT',currentNode=${activityCode})"></div>
        <form class="form-horizontal m" id="form-conclusion-add" th:object="${conclusion}">
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目名称：</label>
                                        <div class="col-sm-4">
                                            <input id="conId" name="conId"  th:value="*{conId}" type="hidden">
                                            <div class="form-control-static" th:utext="${main.projectName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">项目编号：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目范围：</label>
                                        <div class="col-sm-4">
                                            <div  th:include="/component/select :: init(see=true,labelName='项目范围：',value=${main.projectArea},businessType='KTTG', dictCode='projectArea')"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">受让单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.srfDeptName}"></div>
                                        </div>
                                 </div>
                                </div>
                                <div class="row">
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                            <div class="col-sm-4">
                                                <div class="form-control-static" th:utext="${main.tgfFzrName}"></div>
                                            </div>
                                            <label class="col-sm-2 control-label">联系电话：</label>
                                            <div class="col-sm-4">
                                                <div class="form-control-static" th:utext="${main.tgfXmfzrTel}"></div>
                                            </div>
                                        </div>
                                    </div>
                            <div class="row">
                                <div class="form-group ">
                                    <label class="col-sm-2 control-label">项目开始日期：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${main.projectStartDate}"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">项目结束日期：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${main.projectEndDate}"></div>
                                      </div>
                                </div>
                                </div>
                                <!--折叠区域-->
                                <div id="ck" class="panel-collapse collapse in" aria-expanded="false">
                                    <div class="panel-body">
                                        <div class="form-group">
                                            <label class="col-sm-1"></label>
                                            <label class="col-sm-11">点击<a class="form_list_a" th:onclick="$.modal.openTab('项目详细信息',ctx+'zzlx/main/detail?mainId='+[[${main.mainId}]])">此处</a>查看项目详细信息</label>
                                        </div>

                                    </div>
                                </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label  is-required">提交选项：</label>
                                    <div class="col-sm-10">
                                         <label class="radio-box"><input type="radio" value="1" id="submitChoose1" name="submitChoose">按计划结题</label>
                                         <label class="radio-box"><input type="radio" value="0" id="submitChoose2" name="submitChoose">申请延期</label>
                                    </div>
                                </div>
                            </div>
                                <!--折叠区域end-->



                    </div>
                </div>
            </div>
        </div>

        </form>
    </div>
<!--按钮区-->
<div class="toolbar toolbar-bottom" role="toolbar">

            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>
			<button type="button" class="btn  btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>

    <script th:inline="javascript">
        var prefix = ctx + "zzjt/conclusion"

        $("#form-conclusion-add").validate({
            focusCleanup: true
        });

        function submitHandler(transitionKey) {

          //  if ($.validate.form()) {
                var submitChoose=$("input[name='submitChoose']:checked").val();
                if(submitChoose==null||submitChoose==''||submitChoose=='undefined'){
                    $.modal.alertError("请选择提交选项！");
                    return;
                }
                $.operate.saveTabAlert(prefix + "/submitWF", $('#form-conclusion-add').serialize() + "&transitionKey="+transitionKey);
          //  }
        }

        //流程跟踪
        function openProcessTrack(processId){
            window.open(ctxGGMK+"web/EWPI01?inqu_status-0-processInstanceId="+processId);
        }

    </script>
</body>
</html>