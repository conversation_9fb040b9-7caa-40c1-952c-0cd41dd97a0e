<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增项目评估表')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="form-group" th:include="include :: step(approveKind='ZZZC_ZZJT',currentNode=${activityCode})"></div>
    <form class="form-horizontal m" id="form-check-add" th:object="${assess}">
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
            <input id="guid" name="guid"  th:value="${main.mainId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
            <input id="comment" name="comment"   type="hidden">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目名称：</label>
                                        <div class="col-sm-4">
                                            <input id="assessGuid" name="assessGuid"  th:field="*{assessGuid}" type="hidden">
                                            <div class="form-control-static" th:utext="${main.projectName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">项目编号：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目范围：</label>
                                        <div class="col-sm-4">
                                            <input type="hidden" name="assessArea" th:value="*{assessArea}">
                                            <div  th:include="/component/select :: init(see=true,value=*{assessArea},businessType='KTTG', dictCode='projectArea')"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">项目类型：</label>
                                        <div class="col-sm-4">
                                            <div  th:include="/component/select :: init(see=true,value=${main.projectType},businessType='KTTG', dictCode='projectType')"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                       <label class="col-sm-2 control-label">受让单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.srfDeptName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">技术移植方：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfDeptName}"></div>
                                        </div>

                                    </div>
                                </div>


                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfFzrName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">联系电话：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfXmfzrTel}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">项目所属分类：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:include="/component/select :: init(name='assessKind' ,value=*{assessKind},businessType='KTTG', dictCode='payKind',required='true',onchange='sumJL()')"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <div th:if="${main.projectSource eq 'gdxqsb'}" class="col-sm-12 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',ctx+'zzlx/need/detailM?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                                    <div th:if="${main.projectSource eq 'ndjh'}" class="col-sm-5 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',[[${@environment.getProperty('app-context.ctxKY')}]] +'web/KYKTTGPD01?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                                    <div class="col-sm-12">点击<a class="form_list_a" th:onclick="$.modal.openTab('技术附件',ctx+'zzlx/teachAtt/openAtt/'+[[${main.mainId}]])">此处</a>查看技术附件</div>
                                    <div class="col-sm-12" >点击<a class="form_list_a" th:onclick="$.modal.openTab('定价评审表',ctx+'zzlx/teachFix/openFix/'+[[${main.mainId}]])">此处</a>查看定价评审表</div>
                                    <div class="col-sm-12">点击<a class="form_list_a" th:onclick="$.modal.openTab('计划任务书',ctx+'zzlx/teachPlantask/openPlan/'+[[${main.mainId}]])">此处</a>查看计划任务书</div>
                                    <div class="col-sm-12">点击<a class="form_list_a" th:onclick="$.modal.openTab('验收申请表',ctx+'zzjt/check/queryDetail/'+[[${main.mainId}]])" >此处</a>查看验收申请表</div>
                                    <div class="col-sm-12">点击<a class="form_list_a" th:onclick="$.modal.openTab('项目结题报告',ctx+'zzjt/report/queryDetail/'+[[${main.mainId}]])">此处</a>查看项目结题报告</div>
                                    <div class="col-sm-12">点击<a class="form_list_a" th:onclick="$.modal.openTab('项目结算表',ctx+'zzjt/settle/queryDetail/'+[[${main.mainId}]])">此处</a>查看项目结算表</div>
                                </div>
                                <!--经济效益-->
                                <th:block th:include="/component/xyView :: init(ywId=${main.mainId},moduleCode='zzjt_benefit',titleName='推广经济效益')"></th:block>

                                <div class="panel-group" id="accordion1"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading " >
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xmmb" class="collapsed">
                                                    一、计划完成及通过现场验收情况A，满分：A类20分、B类30分、C类35分。（附技术受让方验收申请）,<label style="color: red">*</label>评价分：

                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                                <input name="assessPlanSumNum" id="assessPlanSumNum" min="0" max="35" th:value="*{assessPlanSumNum}" type="number" onchange="sumTotal()" required>分
                                            </h4>
                                        </div>
                                        <div id="xmmb" class="panel-collapse collapse in">
                                            <table class="table table-bordered table-hover" style="margin-top:20px;border: 1px solid #e7e7e7;">
                                                <tfoot>
                                                <tr>
                                                    <td style="text-align: center; ">序号</td>
                                                    <td style="text-align: center; ">评价内容</td>
                                                    <td style="text-align: center; ">评分标准</td>
                                                    <td style="text-align: center; ">A类</td>
                                                    <td style="text-align: center; ">B类</td>
                                                    <td style="text-align: center; ">C类</td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; "> 1</td>
                                                    <td style="text-align: center; "> <label style="color: red">*</label>项目计划完成情况 </br> <input name="assessFinishNum" id="assessFinishNum" th:value="*{assessFinishNum}" type="number" required  onchange="sumPlan()" min="0" max="14">分</td>
                                                    <td style="text-align: left; "> ■项目按计划或提前完成</br>
                                                        ■项目拖期完成（1月内）</br>
                                                        ■项目拖期完成（2月内）</br>
                                                        ■项目拖期2月以上	</td>
                                                    <td style="text-align: left; "> 8分</br> 5～7分</br> 0～5分</br>0分</td>
                                                    <td style="text-align: left; "> 12分</br> 6～11分</br> 0～6分</br>0分</td>
                                                    <td style="text-align: left; "> 14分</br> 7～13分</br> 0～7分</br>0分</td>
                                                    </tr>
                                                <tr>
                                                    <td style="text-align: center; "> 2</td>
                                                    <td style="text-align: center; "> <label style="color: red">*</label>项目通过验收情况</br>  <input name="assessEndNum" id="assessEndNum" th:value="*{assessEndNum}" type="number" required  onchange="sumPlan()" min="0" max="21">分</td>
                                                    <td style="text-align: left; "> ■全部指标达到合同规定的验收指标，部分指标超过合同规定的验收指标，通过验收。</br>
                                                        ■全部指标达到合同规定的验收指标，通过验收。</br>
                                                        ■个别指标未达到合同规定的验收指标要求，但通过验收的。</br>
                                                        ■通过增加任务和成本，才达到合同规定的验收指标，通过验收的。</br>
                                                        ■未达到合同规定的验收指标，双方通过修改合同内容，才通过验收的。</br>
                                                        ■未达到合同验收指标，双方协议合同中止的。</td>
                                                    <td style="text-align: left; "> 12分</br> 8～11分</br> 6～8分</br> 4～6分</br>1～4分</br>0分</td>
                                                    <td style="text-align: left; "> 18分</br> 13～17分</br> 8～12分</br> 5～8分</br>1～5分</br>0分</td>
                                                    <td style="text-align: left; "> 21分</br> 16～21分</br> 15～20分</br> 9～14分</br>1～8分</br>0分</td>
                                                </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion2"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xtsy" class="collapsed">
                                                    二、知识产权产生情况B， 满分：A、B、C类均为5分，<label style="color: red">*</label>评价分：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                                <input th:if="${assess.assessKnowSumNum!=null && assess.assessKnowSumNum!=''}" name="assessKnowSumNum" id="assessKnowSumNum" min="0" max="5" th:value="*{assessKnowSumNum}" type="number" required onchange="sumTotal()">
                                                <input th:if="${assess.assessKnowSumNum==null || assess.assessKnowSumNum==''}" name="assessKnowSumNum" id="assessKnowSumNum" min="0" max="5" th:value="${numPf}" type="number" required onchange="sumTotal()">分
                                            </h4>
                                        </div>
                                        <div id="xtsy" class="panel-collapse collapse in">
                                            <div class="form-group">
                                                <div class="col-sm-2">
                                                    <label>具体评分标准</label>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-10">
                                                    <label>每申请1项发明专利为5分，1项实用新型专利为3分，1项技术秘密为2分，计分累加超过5分，按5分计。</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion3"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#jsy" class="collapsed">
                                                    三、项目净收益的情况C，满分：A、B、C类10分,<label style="color: red">*</label>评价分：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                                <input name="assessJsySumNum" id="assessJsySumNum" min="0" max="10" th:value="*{assessJsySumNum}" type="number" required onchange="sumTotal()">分
                                            </h4>
                                        </div>
                                        <div id="jsy" class="panel-collapse collapse in">
                                            <div class="form-group">
                                                <div class="col-sm-2">
                                                    <label>具体评分标准</label>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-10">
                                                    <label>项目收益在10万以上的，       10分</br>
                                                        项目净收益在3万～10万的，    5～9分</br>
                                                        项目净收益在3万以下的，     1～5分</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#jjxy" class="collapsed">
                                                    四、预计经济效益的准确度D，满分A类10分，B、C类0分,<label style="color: red">*</label>评价分：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                                <input name="assessJjxySumNum" id="assessJjxySumNum" min="0" max="10" th:value="*{assessJjxySumNum}" type="number" required onchange="sumTotal()">分
                                            </h4>
                                        </div>
                                        <div id="jjxy" class="panel-collapse collapse in">
                                            <div class="form-group">
                                                <div class="col-sm-2">
                                                    <label>具体评分标准</label>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-10">
                                                    <label>预计效益的准确度为>90%， 9～10分</br>
                                                        预计效益的准确度为<90%且>80%，      8分</br>
                                                        预计效益的准确度为<80%且>70%，      7分</br>
                                                        预计效益的准确度为<70%且>60%，      6分</br>
                                                        预计效益的准确度为<60%， 1～5分</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion5"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#fyys" class="collapsed">
                                                    五、项目费用预算执行情况E，满分：A、B、C类均为10分,<label style="color: red">*</label>评价分：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                                <input name="assessFyysSumNum" id="assessFyysSumNum" min="0" max="10" th:value="*{assessFyysSumNum}" type="number" required onchange="sumTotal()">分
                                            </h4>
                                        </div>
                                        <div id="fyys" class="panel-collapse collapse in">
                                            <div class="form-group">
                                                <div class="col-sm-2">
                                                    <label>具体评分标准</label>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-10">
                                                    <label>
                                                        ■项目分项和总项费用预算未超过下发指标，总项预算节余10%及以上的，10分；</br>
                                                        ■项目分项和总项费用预算未超过下发指标， 6～9分；</br>
                                                        ■项目分项或总项费用预算超过下发指标，通过纠偏程序追加费用在原预算5%以内的，4～5分；</br>
                                                        ■项目分项或总项费用预算超过下发指标，通过纠偏程序追加费用为原预算6～10%的，1～3分；</br>
                                                        ■项目分项或总项费用预算超过下发指标，通过纠偏程序追加费用超过原预算10%的，0分。</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion6"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xmgc" class="collapsed">
                                                    六、项目过程推进及信息沟通情况F，满分为：A、B类30分,C类25分。<label style="color: red">*</label>评价分：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                                <input name="assessGcSumNum" id="assessGcSumNum" min="0" max="30" th:value="*{assessGcSumNum}" type="number" required onchange="sumTotal()">分
                                            </h4>
                                        </div>
                                        <div id="xmgc" class="panel-collapse collapse in">
                                            <div class="form-group">
                                                <div class="col-sm-2 control-label">
                                                    <label>具体评分标准</label>
                                                </div>
                                            </div>
                                            <table class="table table-bordered table-hover" style="margin-top:20px;border: 1px solid #e7e7e7;">
                                                <tfoot>
                                                <tr>
                                                    <td style="text-align: center; ">序号</td>
                                                    <td style="text-align: center; ">评分标准</td>
                                                    <td style="text-align: center; ">A类</td>
                                                    <td style="text-align: center; ">B类</td>
                                                    <td style="text-align: center; ">C类</td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; "> 1</td>
                                                    <td style="text-align: left; "> ■项目实施过程中根据项目计划按重要节点开展了推进会并有书面记录材料，并且每月上报项目实施进展情况。</br>
                                                        ■项目实施过程中根据项目计划按大部分节点开展了推进会并有书面记录材料，基本按月上报项目实施进展情况。</br>
                                                        ■项目实施过程中开展了部分推进会并有部分书面记录材料，上报过项目实施进展情况。</td>
                                                    <td style="text-align: left; "> 20～30分</br> 10～20分</br> 0～9分</td>
                                                    <td style="text-align: left; "> 20～30分</br> 10～20分</br> 0～9分</td>
                                                    <td style="text-align: left; "> 21～25分</br> 16～20分</br> 0～15分</td>
                                                </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion7"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xmcg" class="collapsed">
                                                    七、项目成果固化情况G，满分：A、B、C类均为10分,<label style="color: red">*</label>评价分：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                                <input name="assessCgSumNum" id="assessCgSumNum" min="0" max="10" th:value="*{assessCgSumNum}" type="number" required onchange="sumTotal()">分
                                            </h4>
                                        </div>
                                        <div id="xmcg" class="panel-collapse collapse in">
                                            <div class="form-group">
                                                <div class="col-sm-2">
                                                    <label>具体评分标准</label>
                                                </div>
                                            </div>
                                            <table class="table table-bordered table-hover" style="margin-top:20px;border: 1px solid #e7e7e7;">
                                                <tfoot>
                                                <tr>
                                                    <td style="text-align: left; ">■项目成果100%纳入了相应的规程、标准等管理制度</td>
                                                    <td style="text-align: left; ">10分</td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: left; ">■有1－3个项目成果未纳入相应的规程、标准等管理制度</td>
                                                    <td style="text-align: left; ">7~9分</td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: left; ">■有3个以上的项目成果未纳入相应的规程、标准等管理制度</td>
                                                    <td style="text-align: left; ">0分</td>
                                                </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion8"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#jyjx" class="collapsed">
                                                    八、项目取得的经验教训H，满分：A、B、C类均为5分，<label style="color: red">*</label>评价分：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                                <input name="assessExperSumNum" id="assessExperSumNum" min="0" max="5" th:value="*{assessExperSumNum}" type="number" required onchange="sumTotal()">分
                                            </h4>
                                        </div>
                                        <div id="jyjx" class="panel-collapse collapse in">
                                            <div class="form-group">
                                                <div class="col-sm-2 ">
                                                    <label>具体评分标准</label>
                                                </div>
                                            </div>
                                            <div class="form-group">
                                                <div class="col-sm-12 ">
                                                    <label>
                                                        项目取得的经验教训，经书面提交给主管部门，对今后项目实施或风险防范有借鉴意义，根据实际情况予以评分。无书面提交经验教训，或随书面提交了经验教训，但无借鉴意义的，不予评分。</label>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion9"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#ndxs" class="collapsed">
                                                    九、难度系数X的确定：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="ndxs" class="panel-collapse collapse in">
                                            <div class="form-group">
                                                <div class="col-sm-4">
                                                    <label>说明：X为难度系数，根据项目技术含量、组织难度、满意度三个因素考虑</label>
                                                </div>
                                            </div>
                                            <table class="table table-bordered table-hover" style="margin-top:20px;border: 1px solid #e7e7e7;">
                                                <tfoot>
                                                <tr>
                                                    <td style="text-align: left; " rowspan="5">难度系数评价标准</td>
                                                    <td style="text-align: left; "><label style="color: red">*</label>技术集成X1
                                                        （根据技术集成度取0.1～0.4）</td>
                                                    <td style="text-align: left; "> <input name="assessNdJsjc" id="assessNdJsjc" min="0.1" max="0.4" th:value="*{assessNdJsjc}" type="number" required onchange="sumNd()"></td>
                                                    <td style="text-align: left; ">■4项单体技术以上集成组合，系数取0.4</br>
                                                        ■2~4项单体技术集成，系数取0.2～0.3</br>
                                                        ■2项单体技术以下集成，系数取0.1-0.2</td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: left; "><label style="color: red">*</label>工作量X2
                                                        （根据工作量大小取0.1～0.6）人力资源数：</td>
                                                    <td style="text-align: left; "> <input name="assessNdWork" id="assessNdWork" min="0.1" max="0.6" th:value="*{assessNdWork}" type="number" required onchange="sumNd()"></td>
                                                    <td style="text-align: left; ">	■持续时间一至二年以上的项目，系数取0.6</br>
                                                        ■投入工作量30人日以上的，系数取0.2～0.6</br>
                                                        ■投入工作量30人日以下的，系数取0.1-0.2</td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: left; "><label style="color: red">*</label>组织难度X3
                                                        （根据组织难度大小取0.1～0.5）</td>
                                                    <td style="text-align: left; "> <input name="assessNdDegree" id="assessNdDegree" min="0.1" max="0.5" th:value="*{assessNdDegree}" type="number" required onchange="sumNd()"></td>
                                                    <td style="text-align: left; ">■单部门或分厂为主实施的项目，系数取0.3～0.5</br>
                                                        ■单部室或单作业区为主实施的项目，系数取0.1-0.3</td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: left; "><label style="color: red">*</label>下工序或相关部门满意度X4
                                                        （根据满意度取0～0.5）</td>
                                                    <td style="text-align: left; "> <input name="assessNdStatis" id="assessNdStatis" min="0" max="0.5" th:value="*{assessNdStatis}" type="number" required onchange="sumNd()"></td>
                                                    <td style="text-align: left; ">	■下工序或相关部门对该项目非常满意的，系数取0.5</br>
                                                        ■下工序或相关部门对该项目满意的，系数取0.3～0.5</br>
                                                        ■下工序或相关部门对该项目基本满意的，系数取0.1～0.3</br>
                                                        ■下工序或相关部门对该项目不满意的，系数取0</td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: left; "><label style="color: red">*</label>总的难度系数X＝（X1＋X2＋X3＋X4）
                                                     </td>
                                                    <td style="text-align: left; "> <input name="assessNdSumNum" id="assessNdSumNum" min="0.3" max="2.0" th:value="*{assessNdSumNum}" type="number" required></td>
                                                    <td style="text-align: left; ">0.3-2.0</td>
                                                </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion10"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#wcl" class="collapsed">
                                                    十、完成率K的确定(月报按时填写率:<span th:text="*{extra1}"></span>%)
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="wcl" class="panel-collapse collapse in">
                                            <input name="extra1" th:value="*{extra1}" type="hidden"/>
                                            <table class="table table-bordered table-hover" style="margin-top:20px;border: 1px solid #e7e7e7;">
                                                <tfoot>
                                                <tr>
                                                    <td style="text-align: left; ">具体评分标准</td>
                                                    <td style="text-align: left; "><label style="color: red">*</label><input name="assessFinishSumNum" id="assessFinishSumNum" min="0.1" max="1.2" th:value="*{assessFinishSumNum}" type="number" required onchange="sumJL()"></td>
                                                    <td style="text-align: left; ">■月报按时完成率80-100%，K取0.8~1.2</br>
                                                        ■月报按时完成率60-80%，K取0.6~0.8</br>
                                                        ■月报按时完成率50-60%，K取0.5~0.6</br>
                                                        ■月报按时完成率50%以下，K取0.1~0.5</td>
                                                </tr>
                                                </tfoot>
                                            </table>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">附件上传：</label>
                                        <div class="col-sm-8">
                                            <div class="form-group"
                                                 th:include="/component/attachment :: init(name='xmpgAttachmentId',id='xmpgAttachmentId',sourceId=*{assessGuid},sourceModule='ZZJT_XMPGFJ')"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion12" role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#zscq" class="collapsed">
                                                    预奖信息
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="zscq" class="panel-collapse collapse in">
                                            <div class="panel-body">
                                                <div class="form-group">
                                                    <div class="col-sm-12">
                                                        <div class="col-sm-12 select-table ">
                                                            <table id="bootstrap-table-key"></table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion11"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#pgr" class="collapsed">
                                                    评估人确认
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="pgr" class="panel-collapse collapse in">
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">项目评估总分M</label>
                                        <div class="col-sm-10">
                                            <div class="form-control-static" id="assessProPgSumDiv"  style="text-align: left" th:utext="*{assessProPgSum}"></div>
                                            <input name="assessProPgSum" id="assessProPgSum" th:value="*{assessProPgSum}" type="hidden" required class="form-control">
                                          </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">项目难度系数X</label>
                                        <div class="col-sm-4">
                                            <input name="assessNdSumNum1" id="assessNdSumNum1" th:value="*{assessNdSumNum}" type="number" required class="form-control">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">项目账面收支差额C</label>
                                        <div class="col-sm-4">
                                            <input name="assessProBalance" id="assessProBalance" th:value="*{assessProBalance}" type="number" required class="form-control" onchange="sumJL()">
                                        </div>
                                        <label class="col-sm-2">(万元)</label>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">项目审定的经济效益Y</label>
                                        <div class="col-sm-4">
                                            <input name="assessProJjxy" id="assessProJjxy" th:value="*{assessProJjxy}" type="number" required class="form-control">
                                        </div>
                                        <label class="col-sm-2">(万元)</label>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">奖励额J</label>
                                        <div class="col-sm-4">
                                            <input name="assessProJlje" id="assessProJlje" th:value="*{assessProJlje}" type="number" required class="form-control">
                                        </div>
                                        <label class="col-sm-1">(万元)</label>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-2"></label>
                                        <div class="col-sm-10">
                                            <div> A=C×K; B=㏑（Y）×（M/100）×X; J＝(A+B)/2=万元 J>C×1.2 J=C×1.2  （A类显示）</br>
                                                A=C×K;  D=C×M/50×X; J＝(A+D)/2＝万元  （B类显示）</br>
                                                A=C×K; E=C×M/100; J＝(A+E)/2＝万元 （C类显示）</div>
                                        </div>
                                    </div>
                                </div>
                                        </div>
                                    </div>
                                </div>

                                <!--专家评审信息-->
                                <div class="panel-body">
                                    <div class="form-group" th:include="/component/expertReview::init(bizId=${businessGuid},moduleCode='zzjt_assess')">
                                    </div>
                                </div>
                                <!--专家评审信息end-->

                            </div>
        </div>
                    </div>
          </div>
        </form>
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
    </div>
    <div class="toolbar toolbar-bottom" role="toolbar">

            <button type="button" class="btn  btn-primary"
                    onclick="saveHandler()">
                <i class="fa fa-check"></i>暂 存
            </button>

            <th:block>
                <button type="button" class="btn  btn-primary"
                        th:onclick="zzps()">
                    <i class="fa fa-group"></i>&nbsp; 专家评审
                </button>&nbsp;
            </th:block>

            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

            <th:block th:include="/component/wfReturn :: init(taskId=${taskId},callback=wfReturn)"/>
			<button type="button" class="btn  btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>

    <!--审批历史-->
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})">
    </div>
    <script th:inline="javascript">
        var prefix = ctx + "zzjt/assess"


        $("input[name='jtysFinishEnd']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });


        $("#form-check-add").validate({
            focusCleanup: true
        });

         //暂存
        function saveHandler() {
            var config = {
                url: prefix + "/add",
                type: "post",
                dataType: "json",
                data: $('#form-check-add').serialize(),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $("#assessGuid").val(result.data.assessGuid);
                    $.modal.alertSuccess(result.msg);
                    $.modal.closeLoading();
                }
            };
            $.ajax(config)
        }

        function submitHandler() {
            var assessKind=$("#assessKind option:selected").val();
            //有些分值每个类型的最大值不一样，需再次校验
            if(assessKind=='A'){
                $("#assessPlanSumNum").attr("max",20);
                $("#assessFinishNum").attr("max",8);
                $("#assessEndNum").attr("max",12);
            }else if(assessKind=='B'){
                $("#assessPlanSumNum").attr("max",30);
                $("#assessFinishNum").attr("max",12);
                $("#assessEndNum").attr("max",18);
                $("#assessJjxySumNum").attr("max",0);
            }else if(assessKind=='C'){
                $("#assessJjxySumNum").attr("max",0);
                $("#assessGcSumNum").attr("max",25);
            }
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/submitWF", $('#form-check-add').serialize());
            }
        }

        function zzps(){
            var moduleCode = "zzjt_assess";
            var moduleName = "专家评审";
            var pslx = "项目结题-项目评估专家评审";
            $.modal.confirm("确认"+moduleName+"吗？", function () {
                $.post(ctx+'zzlx/taskInfo/base64BaseApplyInfo',{'mklx':'技术推广','pslx':pslx,'bizGuids':$("#businessGuid").val(),'mainId':$("#bizId").val()},function(base64String){
                    var url = ctxGGMK+"web/MPPS01?bizGuid="+$("#businessGuid").val()+"&moduleCode="+moduleCode+"&approveKind=MPPS_members_review&jbxx="+base64String;//mpps/process/startPS?
                 //   var url = "http://eplattest.baogang.info/bscdkj-ggmk/web/MPPS01?bizGuid=20211220173241188692224&moduleCode=zzjt_assess&approveKind=MPPS_members_review&jbxx=eyIyMDIxMTIyMDE3MzI0MTE4ODY5MjIyNCI6W3sibmFtZSI6IuaooeWdl-exu-WeiyIsInZhbHVlIjoi5oqA5pyv5o6o5bm_In0seyJuYW1lIjoi6K-E5a6h57G75Z6LIiwidmFsdWUiOiLpobnnm67nu5Ppopgt6aG555uu6K-E5Lyw5LiT5a626K-E5a6hIn0seyJuYW1lIjoi6aG555uu5ZCN56ewIiwidmFsdWUiOiLmtYvor5UwMDEifV19";
                    $.modal.openTab(moduleName, url);
                });
            });
        }

        //流程跟踪
        function openProcessTrack(processId){
            window.open(ctxGGMK+"web/EWPI01?inqu_status-0-processInstanceId="+processId);
        }

        $(function() {
            var options = {
                url: ctx + "zzjl/rewardFp/listByBizId?bizId="+[[${main.mainId}]],
                id:"bootstrap-table-key",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: false
                },
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            return  $.table.serialNumber(index) ;
                        }
                    },
                    {
                        field: 'createUserLabel',
                        align: 'center',
                        title: '申请人'
                    },
                    {
                        field: 'fpShowDate',
                        align: 'center',
                        title: '申请日期'
                    },
                    {
                        field: 'fpSjjlje',
                        align: 'center',
                        title: '奖励金额'
                    },
                    {
                        field: 'jtawardRemark',
                        align: 'center',
                        title: '操作',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.fpGuid + '\')"><i class="fa fa-edit"></i>查看</a> ');
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });

        function sumTotal(){
            var assessPlanSumNum=$("#assessPlanSumNum").val();
            if(assessPlanSumNum==null||assessPlanSumNum==''){
                assessPlanSumNum=0.0;
            }
            var assessKnowSumNum=$("#assessKnowSumNum").val();
            if(assessKnowSumNum==null||assessKnowSumNum==''){
                assessKnowSumNum=0.0;
            }
            var assessJsySumNum=$("#assessJsySumNum").val();
            if(assessJsySumNum==null||assessJsySumNum==''){
                assessJsySumNum=0.0;
            }
            var assessJjxySumNum=$("#assessJjxySumNum").val();
            if(assessJjxySumNum==null||assessJjxySumNum==''){
                assessJjxySumNum=0.0;
            }
            var assessFyysSumNum=$("#assessFyysSumNum").val();
            if(assessFyysSumNum==null||assessFyysSumNum==''){
                assessFyysSumNum=0.0;
            }
            var assessGcSumNum=$("#assessGcSumNum").val();
            if(assessGcSumNum==null||assessGcSumNum==''){
                assessGcSumNum=0.0;
            }
            var assessCgSumNum=$("#assessCgSumNum").val();
            if(assessCgSumNum==null||assessCgSumNum==''){
                assessCgSumNum=0.0;
            }
            var assessExperSumNum=$("#assessExperSumNum").val();
            if(assessExperSumNum==null||assessExperSumNum==''){
                assessExperSumNum=0.0;
            }

            var assessProPgSum=parseFloat(assessPlanSumNum)+parseFloat(assessKnowSumNum)+parseFloat(assessJsySumNum)+parseFloat(assessJjxySumNum)+parseFloat(assessFyysSumNum)+parseFloat(assessGcSumNum)+parseFloat(assessCgSumNum)+parseFloat(assessExperSumNum);
            $("#assessProPgSum").val(assessProPgSum.toFixed(2));
            $("#assessProPgSumDiv").html(assessProPgSum.toFixed(2));
            sumJL();
        }

        function sumPlan(){
            var assessEndNum=$("#assessEndNum").val();
            if(assessEndNum==null||assessEndNum==''){
                assessEndNum=0.0;
            }
            var assessFinishNum=$("#assessFinishNum").val();
            if(assessFinishNum==null||assessFinishNum==''){
                assessFinishNum=0.0;
            }
            var assessPlanSumNum=parseFloat(assessEndNum)+parseFloat(assessFinishNum);
            $("#assessPlanSumNum").val(assessPlanSumNum.toFixed(2));
            sumTotal();
        }

        function sumNd(){
            var assessNdJsjc=$("#assessNdJsjc").val();
            if(assessNdJsjc==null||assessNdJsjc==''){
                assessNdJsjc=0.0;
            }
            var assessNdWork=$("#assessNdWork").val();
            if(assessNdWork==null||assessNdWork==''){
                assessNdWork=0.0;
            }
            var assessNdDegree=$("#assessNdDegree").val();
            if(assessNdDegree==null||assessNdDegree==''){
                assessNdDegree=0.0;
            }
            var assessNdStatis=$("#assessNdStatis").val();
            if(assessNdStatis==null||assessNdStatis==''){
                assessNdStatis=0.0;
            }
            var assessNdSumNum=parseFloat(assessNdJsjc)+parseFloat(assessNdWork)+parseFloat(assessNdDegree)+parseFloat(assessNdStatis);
            $("#assessNdSumNum").val(assessNdSumNum.toFixed(2));
            $("#assessNdSumNum1").val(assessNdSumNum.toFixed(2));
            sumJL();
        }

        function sumJL(){
            var assessProBalance=$("#assessProBalance").val();
            if(assessProBalance==null||assessProBalance==''){
                assessProBalance=0.0;
            }
            var assessFinishSumNum=$("#assessFinishSumNum").val();
            if(assessFinishSumNum==null||assessFinishSumNum==''){
                assessFinishSumNum=0.0;
            }
            var assessProJjxy=$("#assessProJjxy").val();
            if(assessProJjxy==null||assessProJjxy==''){
                assessProJjxy=0.0;
            }
            var assessProPgSum=$("#assessProPgSum").val();
            if(assessProPgSum==null||assessProPgSum==''){
                assessProPgSum=0.0;
            }
            var assessNdSumNum=$("#assessNdSumNum").val();
            if(assessNdSumNum==null||assessNdSumNum==''){
                assessNdSumNum=0.0;
            }
            var assessKind=$("#assessKind option:selected").val();
            var J=0.0;
            if(assessKind=="A"){
                //A=C×K; B=㏑（Y）×（M/100）×X; J＝(A+B)/2=万元 J>C×1.2 J=C×1.2 （A类显示）
                var A=assessProBalance*assessFinishSumNum;
                var B=Math.log(assessProJjxy)*(assessProPgSum/100)*assessNdSumNum;
                J=(A+B)/2;
                var max=assessProBalance*1.2;
                if(J>max){
                    J=max;
                }
            }else if(assessKind=="B"){
                //A=C×K; D=C×M/50×X; J＝(A+D)/2＝万元 （B类显示）
                var A=assessProBalance*assessFinishSumNum;
                var D=assessProBalance*(assessProPgSum/50)*assessNdSumNum;
                J=(A+D)/2;
            }else if(assessKind=="C"){
                //A=C×K; E=C×M/100; J＝(A+E)/2＝万元 （C类显示）
                var A=assessProBalance*assessFinishSumNum;
                var E=assessProBalance*(assessProPgSum/100);
                J=(A+E)/2;
            }
            $("#assessProJlje").val(J.toFixed(2));

        }

        //退回流程
        function wfReturn(activityKey) {
        //    if ($.validate.form()) {
                var data = $('#form-check-add').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
         //   }
        }

    </script>
</body>
</html>