<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('结题验收表')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-check-add" th:object="${check}">
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目名称：</label>
                                        <div class="col-sm-4">
                                            <input id="jtysId" name="jtysId"  th:field="*{jtysId}" type="hidden">
                                            <div class="form-control-static" th:utext="${main.projectName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">项目编号：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">推广单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfDeptName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">受让单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.srfDeptName}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfFzrName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">完成日期：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="*{jtysFinishEnd}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div id="ck" class="panel-collapse collapse in" aria-expanded="false">
                                    <div class="panel-body">

                                        <div class="form-group">
                                            <label class="col-sm-1"></label>
                                            <label class="col-sm-11">点击<a class="form_list_a" th:onclick="$.modal.openTab('项目详细信息',ctx+'zzlx/main/detail?mainId='+[[${main.mainId}]])">此处</a>查看项目详细信息</label>
                                        </div>

                                    </div>
                                </div>

                                <div class="panel-group" id="accordion3"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xmmb" class="collapsed">
                                                    技术推广合同主要内容：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="xmmb" class="panel-collapse collapse in">
                                                <div class="form-control-static" th:utext="${plan.taskContent}"></div>
                                            </div>
                                        </div>
                                    </div>


                                <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xtsy" class="collapsed">
                                                    验收标准和验收形式：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="xtsy" class="panel-collapse collapse in">
                                                  <div class="form-control-static" th:utext="${plan.taskMethod}">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion5"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#wcnr" class="collapsed">
                                                    验收完成内容：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="wcnr" class="panel-collapse collapse in">
                                                <div class="form-control-static" th:utext="*{jtysFinishContent}">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion6"  role="tablist" aria-multiselectable="true" >
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#ysjl" class="collapsed">
                                                    验收结论：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="ysjl" class="panel-collapse collapse in">
                                            <div class="form-control-static" th:utext="*{jtysYsjl}">
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="row" >
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">验收结果：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" >通过</div>
                                        </div>
                                       <!-- <label class="col-sm-2 control-label">验收负责人：</label>
                                       <div class="col-sm-4"  >
                                           <div class="form-control-static" th:utext="*{pszjName}"></div>
                                       </div>-->
                                        </div>
                                    </div>
                    </div>
                </div>
                    </div>
            </div>


        </form>
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
    </div>
    </div>
    <!--<div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
           <button type="button" class="btn btn btn-primary"
                   th:onclick="openProcessTrack([[${processInstanceId}]])">
               <i class="fa fa-eye"></i>&nbsp;流程跟踪图
           </button>
&nbsp;&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>-->
<div class="row">
    <div class="toolbar toolbar-bottom" role="toolbar" >
        <button type="button" class="btn btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>返回
        </button>
    </div>
</div>
    <!--审批历史-->
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})">
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzjt/check"

        $("input[name='jtysFinishEnd']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });


        //流程跟踪
        function openProcessTrack(processId){
            window.open(ctxGGMK+"web/EWPI01?inqu_status-0-processInstanceId="+processId);
        }


    </script>
</body>
</html>