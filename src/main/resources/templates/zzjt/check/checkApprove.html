<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增结题验收表')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div  class="form-group" th:include="include :: step(approveKind='ZZZC_ZZJT',currentNode=${activityCode})"></div>
        <form class="form-horizontal m" id="form-check-add" th:object="${check}">
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
            <input id="comment" name="comment"   type="hidden">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目名称：</label>
                                        <div class="col-sm-4">
                                            <input id="jtysId" name="jtysId"  th:field="*{jtysId}" type="hidden">
                                            <div class="form-control-static" th:utext="${main.projectName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">项目编号：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">推广单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfDeptName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">受让单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.srfDeptName}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfFzrName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">完成日期：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="*{jtysFinishEnd}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div id="ck" class="panel-collapse collapse in" aria-expanded="false">
                                    <div class="panel-body">

                                        <div class="form-group">
                                            <label class="col-sm-1"></label>
                                            <label class="col-sm-11">点击<a class="form_list_a" th:onclick="$.modal.openTab('项目详细信息',ctx+'zzlx/main/detail?mainId='+[[${main.mainId}]])">此处</a>查看项目详细信息</label>
                                        </div>

                                    </div>
                                </div>

                                <div class="panel-group" id="accordion3"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xmmb" class="collapsed">
                                                    技术推广合同主要内容：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="xmmb" class="panel-collapse collapse in">
                                                <div class="form-control-static" th:utext="${plan.taskContent}"></div>
                                            </div>
                                        </div>
                                    </div>


                                <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xtsy" class="collapsed">
                                                    验收标准和验收形式：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="xtsy" class="panel-collapse collapse in">
                                                  <div class="form-control-static" th:utext="${plan.taskMethod}">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion5"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#wcnr" class="collapsed">
                                                    <span style="color: red">*</span>验收完成内容：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="wcnr" class="panel-collapse collapse in">
                                                <div class="form-control-static" th:utext="*{jtysFinishContent}">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion6"  role="tablist" aria-multiselectable="true" >
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#ysjl" class="collapsed">
                                                    <span style="color: red">*</span>验收结论：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="ysjl" class="panel-collapse collapse in">
                                            <div class="form-group">
                                                <div class="col-sm-12 control-label">

                                                    <textarea name="jtysYsjl" th:utext="*{jtysYsjl}"  class="form-control" rows="8" required></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="row" >
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">验收结果：</label>
                                        <div class="col-sm-4">
                                            <label class="radio-box"><input type="radio" value="1" id="jtysYsjg1" name="jtysYsjg">通过</label>
                                            <label class="radio-box"><input type="radio" value="0" id="jtysYsjg2" name="jtysYsjg">不通过</label>
                                        </div>
                                        <!--
                                        <label class="col-sm-2 control-label is-required">验收负责人：</label>
                                       <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='pszjCode',userNameId='pszjName',value=*{pszjCode},selectType='S',isrequired=true)"></div>
                                        </div>-->
                                    </div>
                                    <!--专家评审信息-->
                                    <div class="panel-body">
                                        <div class="form-group" th:include="/component/expertReview::init(bizId=${businessGuid},moduleCode='zzjt_check')">
                                        </div>
                                    </div>
                                    <!--专家评审信息end-->
                                <div class="form-group"></div>
                                <div class="form-group" style="float: right;margin-right: 30px" th:if="${!experFlag}"><span  style="color:red;">专家评审流程未结束或未启动</span></div>
                                <div class="form-group"></div>
                    </div>
                </div>
                    </div>
            </div>

        </form>
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
    </div>
    </div>
<div class="toolbar toolbar-bottom" role="toolbar">
            <th:block>
                <button type="button" class="btn  btn-primary"
                        th:onclick="zzps()">
                    <i class="fa fa-group"></i>&nbsp; 组织评审
                </button>&nbsp;
            </th:block>

            <th:block th:if="${experFlag}" th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

            <th:block th:include="/component/wfReturn :: init(taskId=${taskId},callback=wfReturn)"/>
			<button type="button" class="btn  btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>

<!--审批历史-->
<div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
     th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})">
</div>
    <script th:inline="javascript">
        var prefix = ctx + "zzjt/check";

        $("input[name='jtysFinishEnd']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });

        $("#form-check-add").validate({
            focusCleanup: true
        });

        function saveHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/add", $('#form-check-add').serialize());
            }
        }

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/submitWF", $('#form-check-add').serialize());
            }
        }

        //流程跟踪
        function openProcessTrack(processId){
            window.open(ctxGGMK+"web/EWPI01?inqu_status-0-processInstanceId="+processId);
        }

        function zzps(){
            var moduleCode = "zzjt_check";
            var moduleName = "组织评审";
            var pslx = "项目结题-项目验收评审";
            $.modal.confirm("确认"+moduleName+"吗？", function () {
                $.post(ctx+'zzlx/taskInfo/base64BaseApplyInfo',{'mklx':'技术推广','pslx':pslx,'bizGuids':$("#businessGuid").val(),'mainId':$("#bizId").val()},function(base64String){
                     var url = ctxGGMK+"web/MPPS01?bizGuid="+$("#businessGuid").val()+"&moduleCode="+moduleCode+"&approveKind=MPPS_leaderReview&jbxx="+base64String;//mpps/process/startPS?
                  // var url = "http://eplattest.baogang.info/bscdkj-ggmk/web/MPPS01?bizGuid=20220114105333122995328&moduleCode=zzjt_check&approveKind=MPPS_leaderReview&jbxx=eyIyMDIyMDExNDEwNTMzMzEyMjk5NTMyOCI6W3sibmFtZSI6IuaooeWdl-exu-WeiyIsInZhbHVlIjoi5oqA5pyv5o6o5bm_In0seyJuYW1lIjoi6K-E5a6h57G75Z6LIiwidmFsdWUiOiLpobnnm67nu5Ppopgt6aG555uu6aqM5pS26K-E5a6hIn0seyJuYW1lIjoi6aG555uu5ZCN56ewIiwidmFsdWUiOiLmu5rliqjpnIDmsYLmjqjlub_np7vmpI0yMDIyLTAxLTEwIn1dfQ";
                    $.modal.openTab(moduleName, url);
                });
            });
        }

        //退回流程
        function wfReturn(activityKey) {
            if ($.validate.form()) {
                var data = $('#form-check-add').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
            }
        }

    </script>
</body>
</html>