<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('结题报告表查看')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-check-add" th:object="${reportEx}">
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目名称：</label>
                                        <div class="col-sm-4">
                                            <input id="reportId" name="reportId"  th:field="*{reportId}" type="hidden">
                                            <div class="form-control-static" th:utext="${main.projectName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">项目编号：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">技术移植方：</label>
                                        <div class="col-sm-10">
                                            <div class="form-control-static" th:utext="${main.tgfDeptName}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目范围：</label>
                                        <div class="col-sm-4">
                                            <div  th:include="/component/select :: init(see=true,labelName='项目范围：',value=${main.projectArea},businessType='KTTG', dictCode='projectArea')"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">受让单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.srfDeptName}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfFzrName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">联系电话：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfXmfzrTel}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">完成日期：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="*{reportFinishDate}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">知识分类：</label>
                                        <div class="col-sm-4"
                                                 th:include="/component/select :: init(see=true,value=*{reportKind},id='reportKind', name='reportKind',businessType='KTTG', dictCode='attKind')"></div>

                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">模板：</label>
                                        <div class="col-sm-10">

                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">结题报告附件：</label>
                                        <div class="col-sm-8">
                                            <span style="color: red">注：负责人上传项目实施过程文档如：诊断报告、建议案、实施方案等</span>
                                            <div class="form-group"
                                                 th:include="/component/attachment :: init(see=true,name='reportAttachmentId',id='reportAttachmentId',sourceId=*{reportId},sourceModule='ZZJT_JTBGFJ')"></div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">结题评审汇报材料：</label>
                                        <div class="col-sm-8">
                                            <div class="form-group"
                                                 th:include="/component/attachment :: init(see=true,name='hbclAttachmentId',id='hbclAttachmentId',sourceId=*{reportId},sourceModule='ZZJT_JTfHBCL')"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-1"></label>
                                    <label class="col-sm-11">点击<a th:onclick="$.modal.openTab('验收申请表',ctx+'zzjt/check/queryDetail/'+[[${main.mainId}]])" class="form_list_a">此处</a>查看验收申请表</label>
                                </div>
                                <div class="panel-group" id="accordion3"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xmmb" class="collapsed">
                                                    摘要：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="xmmb" class="panel-collapse collapse in">
                                            <div class="form-control-static" th:utext="*{repostAbstract}">
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xtsy" class="collapsed">
                                                    总体项目实施概况：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="xtsy" class="panel-collapse collapse in">
                                            <div class="form-control-static" th:utext="*{repostInfor}">
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="panel-group" id="accordion5"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#wcnr" class="collapsed">
                                                    合同及技术附件规定的主要任务完成情况：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="wcnr" class="panel-collapse collapse in">

                                            <div class="form-group">
                                                <div class="col-sm-2 control-label">
                                                    <label>项目计划内容完成情况:</label>
                                                </div>
                                                <div class="col-sm-10">
                                                    <div class="form-control-static" th:utext="*{repostFinishInfor}"></div>
                                                </div>
                                                <!--<div class="col-sm-2 control-label">
                                                    <label>项目通过验收情况:</label>
                                                </div>
                                                <div class="col-sm-10">
                                                 <div class="form-control-static" th:utext="*{repostCheckInfor}"></div>
                                                </div>-->
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            <div class="panel-group" id="accordion7" role="tablist" aria-multiselectable="true">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" href="#zscq" class="collapsed">
                                                新增知识产权情况
                                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="zscq" class="panel-collapse collapse in">
                                        <div class="panel-body">
                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <div class="col-sm-12 select-table ">
                                                        <table id="bootstrap-table-key"></table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                                <!--
                            <div id="ck" class="panel-collapse collapse in" aria-expanded="false">
                                <div class="panel-body">

                                    <div class="form-group">
                                        <label class="col-sm-3  control-label ">点击<a th:onclick="projectDetail()">此处</a>查看知识共享专题描述</label>
                                        <div class="col-sm-9">
                                        </div>
                                    </div>

                                </div>
                            </div>
                                -->
                                <div class="panel-group" id="accordion6"  role="tablist" aria-multiselectable="true" >
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#ysjl" class="collapsed">
                                                    项目费用执行情况：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="ysjl" class="panel-collapse collapse in">
                                            <div class="form-control-static" th:utext="*{repostFyInfor}"></div>
                                        </div>
                                    </div>
                                </div>


                                <div class="row" th:if="${main.projectArea eq 'jtn'}">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label ">是否有经济效益：</label>
                                        <div class="col-sm-10">
                                            <div class="form-control-static" th:if="*{reportJjxy eq '1'}">是</div>
                                            <div class="form-control-static" th:if="*{reportJjxy eq '0'}">0</div>
                                        </div>
                                    </div>
                                </div>
                            <div class="row" th:if="${main.projectArea eq 'jtn'}">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label " >经济效益（万元）：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="*{reportXmzxy}"></div>
                                    </div>
                                    <label class="col-sm-2 control-label ">效益体系日期：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="*{reportXydate}"></div>
                                    </div>
                                 </div>
                            </div>

                            <div class="row" th:if="${main.projectArea eq 'jtn'}">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">效益评审附件：</label>
                                    <div class="col-sm-8">
                                        <div class="form-group"
                                             th:include="/component/attachment :: init(see=true,name='xypsAttachmentId',id='xypsAttachmentId',sourceId=*{reportId},sourceModule='ZZJT_XYPSFJ')"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="panel-group" id="accordion8"  role="tablist" aria-multiselectable="true" th:if="${main.projectArea eq 'gfn'}">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" href="#jjxy" class="collapsed">
                                                项目产生的经济效益或效果：
                                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="jjxy" class="panel-collapse collapse in">
                                        <div class="form-control-static" th:utext="*{repostJjxtInfor}"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="panel-group" id="accordion9"  role="tablist" aria-multiselectable="true" >
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" href="#jyjx" class="collapsed">
                                                项目取得的经验教训及其它需要说明的情况：
                                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="jyjx" class="panel-collapse collapse in">
                                        <div class="form-control-static" th:utext="*{repostQtsmInfor}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>
        </div>
                    </div>

    </form>
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
    </div>
    <div class="row">
        <div class="toolbar toolbar-bottom" role="toolbar" >
            <button type="button" class="btn btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返回
            </button>
        </div>
    </div>

    <!--审批历史-->
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})">
    </div>
    <script th:inline="javascript">
        var prefix = ctx + "zzjt/report"

        $("input[name='reportFinishDate']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });


        $("#form-check-add").validate({
            focusCleanup: true
        });

        function saveHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/add", $('#form-check-add').serialize());
            }
        }

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/submitWF", $('#form-check-add').serialize());
            }
        }

        //流程跟踪
        function openProcessTrack(processId){
            window.open(ctxGGMK+"web/EWPI01?inqu_status-0-processInstanceId="+processId);
        }

        function addColumn() {
            var row = {
                propertyType: "",
                propertyNum: "",
                propertyName: ""
            }
            sub.addColumn(row,"bootstrap-table-key");
        }

        var  methodDatas =[[${@dict.getDictList('KTTG','propertyType')}]];
        /***********************************知识产权描述*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzjt/property/propertyList?reportId="+[[${reportEx.reportId}]],
                id:"bootstrap-table-key",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: true
                },
                    {
                        field: 'orderNum',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            return  $.table.serialNumber(index) ;
                        }
                    },
                    {
                        field: 'propertyType',
                        align: 'center',
                        title: '类别',
                        formatter:function (value,row,index){
                            return $.table.selectDictLabel(methodDatas,value);
                        }
                    },
                    {
                        field: 'propertyNum',
                        align: 'center',
                        title: '专利号\\技术秘密号'
                    },
                    {
                        field: 'propertyName',
                        align: 'center',
                        title: '专利名称\\技术秘密名称'
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });

        //退回流程
        function wfReturn(activityKey) {
            if ($.validate.form()) {
                var data = $('#form-check-add').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
            }
        }

    </script>
</body>
</html>