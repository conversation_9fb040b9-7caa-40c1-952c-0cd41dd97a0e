<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('结题报告列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <input type="hidden" name="queryType" th:value="${queryType}">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>项目编号：</label>
                                <input type="text" name="projectNum"/>
                            </li>
                            <li>
                                <label>项目名称：</label>
                                <input type="text" name="projectName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
<!--
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="startNotice()" >
                    <i class="fa fa-plus"></i> 下发结题通知(定时)
                </a>
            </div>-->
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzjt/report";
        var noticePrefix = ctx + "zzjt/conclusion";

        $(function() {
            var options = {
                url: prefix + "/jtProList",
                createUrl: prefix + "/add",
                detailUrl: ctx + "zzlx/main/detail?mainId={id}",
                modalName: "项目列表",
                columns: [
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'prostartDate',
                    title: '项目开始时间'
                },
                {
                    field: 'proendDate',
                    title: '项目结束时间'
                },
                {
                    field: 'tgfFzrCode',
                    title: '项目负责人工号'
                },
                {
                    field: 'tgfFzrName',
                    title: '项目负责人姓名'
                },
                {
                    field: 'srfDeptName',
                    title: '受让单位部门'
                },
                {
                    field: 'tgfDeptName',
                    title: '推广单位部门'
                },
                {
                    title: '操作',
                    align: 'center',
                    width: '150',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="startMainNotice(\'' + row.mainId + '\')"><i class="fa fa-edit"></i>结题</a> ');
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.mainId + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function startNotice(){
            $.modal.confirm("确认要手动定时下发结题通知吗？", function () {
                $.operate.post(noticePrefix + "/startNotice", {});
            })
        }

        function startMainNotice(mainId){
            $.modal.confirm("确认要下发结题通知吗？", function () {
                $.operate.post(noticePrefix + "/startMainNotice/"+mainId, {});
            })
        }
    </script>
</body>
</html>