<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增结题报告表')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div  class="form-group" th:include="include :: step(approveKind='ZZZC_ZZJT',currentNode=${activityCode})"></div>
        <form class="form-horizontal m" id="form-check-add" th:object="${reportEx}">
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
            <input id="comment" name="comment"   type="hidden">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目名称：</label>
                                        <div class="col-sm-4">
                                            <input id="reportId" name="reportId"  th:field="*{reportId}" type="hidden">
                                            <div class="form-control-static" th:utext="${main.projectName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">项目编号：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">技术移植方：</label>
                                        <div class="col-sm-10">
                                            <div class="form-control-static" th:utext="${main.tgfDeptName}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目范围：</label>
                                        <div class="col-sm-4">
                                            <div  th:include="/component/select :: init(see=true,labelName='项目范围：',value=${main.projectArea},businessType='KTTG', dictCode='projectArea')"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">受让单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.srfDeptName}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfFzrName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">联系电话：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfXmfzrTel}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">完成日期：</label>
                                        <div class="col-sm-4">
                                            <input name="reportFinishDate" class="form-control" type="text" th:value="*{reportFinishDate}" required>
                                        </div>
                                        <label class="col-sm-2 control-label is-required">知识分类：</label>
                                        <div class="col-sm-4"
                                                 th:include="/component/select :: init(value=*{reportKind},id='reportKind', name='reportKind',isrequired=true,businessType='KTTG', dictCode='attKind')"></div>

                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">模板：</label>
                                        <div class="col-sm-10">
                                            <div class="form-control-static"><a class="form_list_a"  href="javascript:void(0)" th:onclick="downLoad([[${fileBg.fileId}]])" th:text="${fileBg.fileName}"></a>&nbsp;&nbsp;&nbsp;(<span th:text="${fileBg.fileSize}"></span>)</div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <span style="color: red">注：负责人上传项目实施过程文档如：诊断报告、建议案、实施方案等。建议word</span>
                                        <label class="col-sm-3 control-label">结题报告附件：</label>
                                        <div class="col-sm-8">
                                            <div class="form-group"
                                                 th:include="/component/attachment :: init(name='reportAttachmentId',id='reportAttachmentId',sourceId=*{reportId},sourceModule='ZZJT_JTBGFJ')"></div>
                                        </div>
                                    </div>
                                    <div class="form-group">
                                        <span style="color: red">注：负责人上传项目实施过程文档如：诊断报告、建议案、实施方案等。建议ppt</span>
                                        <label class="col-sm-3 control-label">结题评审汇报材料：</label>
                                        <div class="col-sm-8">
                                            <div class="form-group"
                                                 th:include="/component/attachment :: init(name='hbclAttachmentId',id='hbclAttachmentId',sourceId=*{reportId},sourceModule='ZZJT_JTfHBCL')"></div>
                                        </div>
                                    </div>

                                </div>
                                <div class="form-group">
                                    <label class="col-sm-1"></label>
                                    <label class="col-sm-11">点击<a th:onclick="$.modal.openTab('验收申请表',ctx+'zzjt/check/queryDetail/'+[[${main.mainId}]])" class="form_list_a">此处</a>查看验收申请表</label>
                                </div>
                                <div class="panel-group" id="accordion3"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xmmb" class="collapsed">
                                                    <span style="color: red">*</span>摘要：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="xmmb" class="panel-collapse collapse in">
                                            <div class="form-group">
                                            <div class="col-sm-12 control-label">
                                                <textarea name="repostAbstract" th:utext="*{repostAbstract}"  class="form-control" rows="8" required></textarea>
                                            </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xtsy" class="collapsed">
                                                    <span style="color: red">*</span>总体项目实施概况：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="xtsy" class="panel-collapse collapse in">
                                            <div class="form-group">
                                            <div class="col-sm-12 control-label">
                                                <textarea name="repostInfor" th:utext="*{repostInfor}"  class="form-control" rows="8" required placeholder="文字描述：简明扼要地、提纲式的概括项目实施的精华
数字描述：根据项目类型，描述项目目标中技术经济指标、产量、达产等完成情况"></textarea>
                                            </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="panel-group" id="accordion5"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#wcnr" class="collapsed">
                                                    <span style="color: red">*</span>合同及技术附件规定的主要任务完成情况：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="wcnr" class="panel-collapse collapse in">

                                            <div class="form-group">
                                                <div class="col-sm-2 control-label">
                                                    <label>项目计划内容完成情况</label>
                                                </div>
                                                <div class="col-sm-12 control-label">
                                                    <textarea name="repostFinishInfor" th:utext="*{repostFinishInfor}"  class="form-control" rows="8" required placeholder="数字描述：增加产量、产品替代、延长寿命、提高质量、降低成本、指标改进"></textarea>
                                                </div>
                                                <!--<div class="col-sm-2 control-label">
                                                    <label>项目通过验收情况</label>
                                                </div>
                                                <div class="col-sm-12 control-label">
                                                    <textarea name="repostCheckInfor" th:utext="*{repostCheckInfor}"  class="form-control" rows="8" required placeholder="数字描述：增加产量、产品替代、延长寿命、提高质量、降低成本、指标改进"></textarea>
                                                </div>-->
                                            </div>
                                        </div>
                                    </div>
                                </div>

                            <div class="panel-group" id="accordion7" role="tablist" aria-multiselectable="true">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" href="#zscq" class="collapsed">
                                                <span style="color: red">*</span>新增知识产权情况
                                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="zscq" class="panel-collapse collapse in">
                                        <div class="panel-body">
                                            <div class="form-group">
                                                <div class="col-sm-12">
                                                    <button type="button" class="btn btn-white btn-sm" onclick="addColumn()"><i class="fa fa-plus"> 增加</i></button>
                                                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>
                                                    <div class="col-sm-12 select-table ">
                                                        <table id="bootstrap-table-key"></table>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                                <!--
                            <div id="ck" class="panel-collapse collapse in" aria-expanded="false">
                                <div class="panel-body">

                                    <div class="form-group">
                                        <label class="col-sm-3  control-label ">点击<a th:onclick="projectDetail()">此处</a>查看知识共享专题描述</label>
                                        <div class="col-sm-9">
                                        </div>
                                    </div>

                                </div>
                            </div>
                                  -->
                                <div class="panel-group" id="accordion6"  role="tablist" aria-multiselectable="true" >
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#ysjl" class="collapsed">
                                                    <span style="color: red">*</span>项目费用执行情况：
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="ysjl" class="panel-collapse collapse in">
                                            <div class="form-group">
                                                <div class="col-sm-12 control-label">
                                                    <textarea name="repostFyInfor" th:utext="*{repostFyInfor}"  class="form-control" rows="8" required></textarea>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>


                                <div class="row" th:if="${main.projectArea eq 'jtn'}">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">是否有经济效益：</label>
                                        <div class="col-sm-10">
                                            <label class="radio-box"><input type="radio" value="1" id="reportJjxy1" name="reportJjxy">是</label>
                                            <label class="radio-box"><input type="radio" value="0" id="reportJjxy2" name="reportJjxy">否</label>
                                        </div>
                                    </div>
                                </div>
                            <div class="row" th:if="${main.projectArea eq 'jtn'}">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required" >经济效益（万元）：</label>
                                    <div class="col-sm-4">
                                        <input name="reportXmzxy" class="form-control"  th:value="*{reportXmzxy}" type="number">
                                    </div>
                                    <label class="col-sm-2 control-label is-required">效益体系日期：</label>
                                    <div class="col-sm-4">
                                        <input name="reportXydate" class="form-control" type="text" th:value="*{reportXydate}">
                                    </div>
                                 </div>
                            </div>

                            <div class="row" th:if="${main.projectArea eq 'jtn'}">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">效益评审附件：</label>
                                    <div class="col-sm-8">
                                        <div class="form-group"
                                             th:include="/component/attachment :: init(name='xypsAttachmentId',id='xypsAttachmentId',sourceId=*{reportId},sourceModule='ZZJT_XYPSFJ')"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="panel-group" id="accordion8"  role="tablist" aria-multiselectable="true" th:if="${main.projectArea eq 'gfn'}">
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" href="#jjxy" class="collapsed">
                                                <span style="color: red">*</span>项目产生的经济效益或效果：
                                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="jjxy" class="panel-collapse collapse in">
                                        <div class="form-group">
                                            <div class="col-sm-12 control-label">
                                                <textarea name="repostJjxtInfor" th:utext="*{repostJjxtInfor}"  class="form-control" rows="8" required th:placeholder="数字描述：以公司经济效益管理办法计算为依据"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="panel-group" id="accordion9"  role="tablist" aria-multiselectable="true" >
                                <div class="panel panel-default">
                                    <div class="panel-heading">
                                        <h4 class="panel-title">
                                            <a data-toggle="collapse" href="#jyjx" class="collapsed">
                                                <span style="color: red">*</span>项目取得的经验教训及其它需要说明的情况：
                                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                            </a>
                                        </h4>
                                    </div>
                                    <div id="jyjx" class="panel-collapse collapse in">
                                        <div class="form-group">
                                            <div class="col-sm-12 control-label">
                                                <textarea name="repostQtsmInfor" th:utext="*{repostQtsmInfor}"  class="form-control" rows="8" required ></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                </div>
        </div>
          </div>
        </form>
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
    </div>
    <div class="toolbar toolbar-bottom" role="toolbar">

            <button type="button" class="btn  btn-primary"
                    onclick="saveHandler()">
                <i class="fa fa-check"></i>暂 存
            </button>
            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

            <th:block th:include="/component/wfReturn :: init(taskId=${taskId},callback=wfReturn)"/>
			<button type="button" class="btn  btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>

    <!--审批历史-->
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})">
    </div>

    <script type="text/html" id="propertyThymeleaf">
        <select name="property[%s].propertyType" th:class="form-control" id="propertyType%s"
                th:with="dictData=${@dict.getDictList('KTTG','propertyType')}">
            <option  value=" ">请选择</option>
            <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
        </select>
    </script>


    <script th:inline="javascript">
        var prefix = ctx + "zzjt/report"

        $("input[name='reportFinishDate']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });


        $("#form-check-add").validate({
            focusCleanup: true
        });
        function downLoad(key) {
            window.open(ctx + "attachment/download/" + key);
        }

        //暂存
        function saveHandler() {
            var config = {
                url: prefix + "/add",
                type: "post",
                dataType: "json",
                data: $('#form-check-add').serialize(),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $("#reportId").val(result.data.reportId);
                    $.modal.alertSuccess(result.msg);
                    $.modal.closeLoading();
                }
            };
            $.ajax(config)
        }

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/submitWF", $('#form-check-add').serialize());
            }
        }

        //流程跟踪
        function openProcessTrack(processId){
            window.open(ctxGGMK+"web/EWPI01?inqu_status-0-processInstanceId="+processId);
        }

        function addColumn() {
            var row = {
                propertyType: "",
                propertyNum: "",
                propertyName: ""
            }
            sub.addColumn(row,"bootstrap-table-key");
        }

        /** 根据专利编号获取专利名称 */
        function selectNumber (index,obj) {
            var value = $(obj).val();
            var sourceType = "";
            var sourceId = value;
            var options = $("#propertyType"+index+" option:selected");
            var ovalue = options.val();
            if(ovalue=='01'||ovalue=='02'){ //专利
                sourceType = "KYZL";
            }else if(ovalue=='03'){ //技术秘密
                sourceType = 'KYMM';
            }
            $.post(ctx + "zzlx/teachAtt/getSource", {"sourceType": sourceType,"sourceId":sourceId},
                function (data) {
                    if(data!=null&&data.length!=0){
                        $("input[name='property["+index+"].propertyName']").val(data.sourceName);
                    }else{
                        $("input[name='property["+index+"].propertyName']").val("");
                    }
                }
            );
        }
        /***********************************知识产权描述*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzjt/property/propertyList?reportId="+[[${reportEx.reportId}]],
                id:"bootstrap-table-key",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        checkbox: true
                    },
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='property[%s].orderNum' value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index)+columnId ;
                        }
                    },
                    {
                        field: 'propertyType',
                        align: 'center',
                        title: '类别',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf($("#propertyThymeleaf").html(),index,index);
                            return $.common.isEmpty(value) ? html : html.replace("\""+value+"\"","\""+value+"\" selected");
                        }
                    },
                    {
                        field: 'propertyNum',
                        align: 'center',
                        title: '专利号\\技术秘密号',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  id='number%s' onblur=\"selectNumber('%s',this)\"  type='text' required  name='property[%s].propertyNum'  value='%s'>",index,index, index, value);
                            return html;
                        }
                    },
                    {
                        field: 'propertyName',
                        align: 'center',
                        title: '专利名称\\技术秘密名称',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'   type='text' required  name='property[%s].propertyName'  value='%s'>", index, value);
                            return html;
                        }
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });

        //退回流程
        function wfReturn(activityKey) {
            if ($.validate.form()) {
                var data = $('#form-check-add').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
            }
        }
    </script>
</body>
</html>