<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增奖励审批表')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div  class="form-group" th:include="include :: step(approveKind='ZZZC_ZZJT',currentNode=${activityCode})"></div>
        <form class="form-horizontal m" id="form-check-add" th:object="${award}">
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目名称：</label>
                                        <div class="col-sm-4">
                                            <input id="awardId" name="awardId"  th:field="*{awardId}" type="hidden">
                                            <div class="form-control-static" th:utext="${main.projectName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">项目编号：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目范围：</label>
                                        <div class="col-sm-4">
                                            <div th:include="/component/select :: init(see=true,labelName='项目范围：',value=${main.projectArea},businessType='KTTG', dictCode='projectArea')"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">项目类型：</label>
                                        <div class="col-sm-4">
                                            <div  th:include="/component/select :: init(see=true,value=${main.projectType},businessType='KTTG', dictCode='projectType')"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">受让单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.srfDeptName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">推广单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfDeptName}"></div>
                                        </div>
                                    </div>
                                </div>


                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfFzrName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">联系电话：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfXmfzrTel}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion1"  role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading " >
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#xmmb" class="collapsed">
                                                    项目材料
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="xmmb" class="panel-collapse collapse in">
                                            <div class="form-group">
                                                <label class="col-sm-1"></label>
                                                <label class="col-sm-11">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',ctx+'zzlx/need/detailM?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</label>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-1"></label>
                                                <label class="col-sm-11">点击<a class="form_list_a" th:onclick="$.modal.openTab('技术附件',ctx+'zzlx/teachAtt/openAtt/'+[[${main.mainId}]])">此处</a>查看技术附件</label>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-1"></label>
                                                <label class="col-sm-11">点击<a class="form_list_a" th:onclick="$.modal.openTab('定价评审表',ctx+'zzlx/teachFix/openFix/'+[[${main.mainId}]])">此处</a>查看定价评审表</label>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-1"></label>
                                                <label class="col-sm-11">点击<a class="form_list_a" th:onclick="$.modal.openTab('计划任务书',ctx+'zzlx/teachPlantask/openPlan/'+[[${main.mainId}]])">此处</a>查看计划任务书</label>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-1"></label>
                                                <label class="col-sm-11">点击<a th:onclick="$.modal.openTab('验收申请表',ctx+'zzjt/check/queryDetail/'+[[${main.mainId}]])" class="form_list_a">此处</a>查看验收申请表</label>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-1"></label>
                                                <label class="col-sm-11">点击<a th:onclick="$.modal.openTab('结题报告',ctx+'zzjt/report/queryDetail/'+[[${main.mainId}]])" class="form_list_a">此处</a>查看结题报告</label>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-1"></label>
                                                <label class="col-sm-11">点击<a th:onclick="$.modal.openTab('项目结算表',ctx+'zzjt/settle/queryDetail/'+[[${main.mainId}]])" class="form_list_a">此处</a>查看项目结算表</label>
                                            </div>
                                            <div class="form-group">
                                                <label class="col-sm-1"></label>
                                                <label class="col-sm-11">点击<a th:onclick="$.modal.openTab('项目评估表',ctx+'zzjt/assess/queryDetail/'+[[${main.mainId}]])" class="form_list_a">此处</a>查看项目评估表</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!--经济效益-->
                                <th:block th:include="/component/xyView :: init(ywId=${main.mainId},moduleCode='zzjt_benefit',titleName='推广经济效益')"></th:block>


                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label ">项目评估总分M：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="*{awardProPgSum}"></div>
                                           </div>
                                        <label class="col-sm-2 control-label ">项目难度系数X：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="*{awardNdSumNum}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label ">完成率K：</label>
                                        <div class="col-sm-2">
                                            <div class="form-control-static" th:utext="*{awardFinishSumNum}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label ">项目账面收支差额C：</label>
                                        <div class="col-sm-2">
                                            <div class="form-control-static" th:utext="${#numbers.formatDecimal(award.awardProBalance,1,4)}" ></div>
                                        </div>
                                        <label class="col-sm-1 control-label" th:utext="(万元)"></label>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label ">项目审定的经济效益Y：</label>
                                        <div class="col-sm-2">
                                            <div class="form-control-static" th:utext="${#numbers.formatDecimal(award.awardProJjxy,1,4)}" ></div>
                                        </div>
                                        <label class="col-sm-1 control-label" th:utext="(万元)"></label>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label ">奖励额J：</label>
                                        <div class="col-sm-2">
                                            <div class="form-control-static" th:utext="${#numbers.formatDecimal(award.awardProJlje,1,4)}" ></div>
                                        </div>
                                        <label class="col-sm-1 control-label" th:utext="(万元)"></label>
                                        <label class="col-sm-4">
                                            <div th:if="${assess.assessKind == 'A'}">A=C×K; B=㏑（Y）×（M/100）×X; J＝(A+B)/2; </br>J>C×1.2: J=C×1.2</div>
                                            <div th:if="${assess.assessKind == 'B'}">A=C×K;  D=C×M/50×X; J＝(A+D)/2</div>
                                            <div th:if="${assess.assessKind == 'C'}">A=C×K; E=C×M/100; J＝(A+E)/2</div></label>

                                    </label>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label ">建议奖励额：</label>
                                        <div class="col-sm-10">
                                            <div class="form-control-static" th:utext="*{awardContent}" ></div>
                                         </div>
                                    </div>
                                    <div class="form-group">
                                        <label class="col-sm-1 "></label>
                                        <label class="col-sm-2 control-label ">总奖励额：</label>
                                        <div class="col-sm-2">
                                            <div class="form-control-static" th:utext="${#numbers.formatDecimal(award.awardJjJlje,1,4)}"  ></div>
                                        </div>
                                        <label class="col-sm-1 control-label" th:utext="(万元)"></label>
                                    </div>
                                </div>
                                <div class="row">
                                    <!--<div class="form-group">
                                        <label class="col-sm-2 control-label is-required">本次决定奖励额：</label>
                                        <div class="col-sm-10">
                                            <textarea name="extra1" th:text="*{extra1}" class="form-control" rows="8" required placeholder="说明"></textarea>
                                         </div>
                                    </div>-->
                                    <div class="form-group">
                                        <label class="col-sm-1 "></label>
                                        <label class="col-sm-2 control-label is-required">决定奖励总额：</label>
                                        <div class="col-sm-4">
                                            <input th:if="${award.awardDeciJlje==null}" name="awardDeciJlje" id="awardDeciJlje"  th:value="${#numbers.formatDecimal(award.awardJjJlje,1,4)}" type="number" required class="form-control"  >
                                            <input th:if="${award.awardDeciJlje!=null}" name="awardDeciJlje" id="awardDeciJlje"  th:value="${#numbers.formatDecimal(award.awardDeciJlje,1,4)}" type="number" required class="form-control"  >
                                        </div>
                                        <label class="col-sm-1 control-label" th:utext="(万元)"></label>
                                    </div>
                                </div>

                                <div class="panel-group" id="accordion7" role="tablist" aria-multiselectable="true" th:if="${main.projectArea eq 'jtn'}">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#zscq" class="collapsed">
                                                    奖励分配建议方案
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="zscq" class="panel-collapse collapse in">
                                            <div class="panel-body">
                                                <div class="form-group">
                                                    <div class="col-sm-12">
                                                       <div class="col-sm-12 select-table ">
                                                            <table id="bootstrap-table-key"></table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label">附件上传：</label>
                                        <div class="col-sm-8">
                                            <div class="form-group"
                                                 th:include="/component/attachment :: init(see=true,name='jlspAttachmentId',id='jlspAttachmentId',sourceId=*{awardId},sourceModule='ZZJT_JLSPFJ')"></div>
                                        </div>
                                    </div>
                                </div>


                                </div>
                            </div>
        </div>
                    </div>
          </div>
            <th:block th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=${processInstanceId},required='true',freeFlow='false')"></th:block>

        </form>
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
    </div>
    <div class="toolbar toolbar-bottom" role="toolbar">

            <button type="button" class="btn  btn-primary"
                    onclick="saveHandler()">
                <i class="fa fa-check"></i>暂 存
            </button>

            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

            <th:block th:include="/component/wfReturn :: init(taskId=${taskId},callback=wfReturn)"/>
			<button type="button" class="btn  btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>

    <!--审批历史-->
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})">
    </div>
    <script th:inline="javascript">
        var prefix = ctx + "zzjt/award"


        $("input[name='jtysFinishEnd']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });


        $("#form-check-add").validate({
            focusCleanup: true
        });

        function saveHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/add", $('#form-check-add').serialize());
            }
        }

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/submitWF", $('#form-check-add').serialize());
            }
        }

        //流程跟踪
        function openProcessTrack(processId){
            window.open(ctxGGMK+"web/EWPI01?inqu_status-0-processInstanceId="+processId);
        }


        function sumJL(){
            var awardProBalance=$("#awardProBalance").val();
            if(awardProBalance==null||awardProBalance==''){
                awardProBalance=0.0;
            }
            var awardFinishSumNum=$("#awardFinishSumNum").val();
            if(awardFinishSumNum==null||awardFinishSumNum==''){
                awardFinishSumNum=0.0;
            }
            var awardProJjxy=$("#awardProJjxy").val();
            if(awardProJjxy==null||awardProJjxy==''){
                awardProJjxy=0.0;
            }
            var awardProPgSum=$("#awardProPgSum").val();
            if(awardProPgSum==null||awardProPgSum==''){
                awardProPgSum=0.0;
            }
            var awardNdSumNum=$("#awardNdSumNum").val();
            if(awardNdSumNum==null||awardNdSumNum==''){
                awardNdSumNum=0.0;
            }
            var assessKind=[[${assess.assessKind}]];
            var J=0.0;
            if(assessKind=="A"){
                //A=C×K; B=㏑（Y）×（M/100）×X; J＝(A+B)/2=万元 J>C×1.2 J=C×1.2 （A类显示）
                var A=awardProBalance*awardFinishSumNum;
                var B=Math.log(awardProJjxy)*(awardProPgSum/100)*awardNdSumNum;
                J=(A+B)/2;
                var max=awardProBalance*1.2;
                if(J>max){
                    J=max;
                }
            }else if(assessKind=="B"){
                //A=C×K; D=C×M/50×X; J＝(A+D)/2＝万元 （B类显示）
                var A=awardProBalance*awardFinishSumNum;
                var D=awardProBalance*(awardProPgSum/50)*awardNdSumNum;
                J=(A+D)/2;
            }else if(assessKind=="C"){
                //A=C×K; E=C×M/100; J＝(A+E)/2＝万元 （C类显示）
                var A=awardProBalance*awardFinishSumNum;
                var E=awardProBalance*(awardProPgSum/100);
                J=(A+E)/2;
            }
            $("#awardProJlje").val(J.toFixed(2));

        }



        /***********************************知识产权描述*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzjt/proJtAward/proJtAwardList?awardId="+[[${award.awardId}]],
                id:"bootstrap-table-key",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: true
                },
                    {
                        field: 'orderNum',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            return  $.table.serialNumber(index) ;
                        }
                    },
                    {
                        field: 'jtawardDeptCode',
                        align: 'center',
                        title: '部门代码'
                    },
                    {
                        field: 'jtawardDeptName',
                        align: 'center',
                        title: '部门名称'
                    },
                    {
                        field: 'jtawardTotal',
                        align: 'center',
                        title: '金额'
                    },
                    {
                        field: 'jtawardTeamCount',
                        align: 'center',
                        title: '先进团队'
                    },
                    {
                        field: 'jtawardPeoCount',
                        align: 'center',
                        title: '先进个人'
                    },
                    {
                        field: 'jtawardSumCount',
                        align: 'center',
                        title: '合计（万元）'
                    },
                    {
                        field: 'jtawardRemark',
                        align: 'center',
                        title: '备注'
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });

        //退回流程
        function wfReturn(activityKey) {
            if ($.validate.form()) {
                var data = $('#form-check-add').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
            }
        }

        //点击查看技术附件
        function attDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "技术附件",
                content: ctx + "zzlx/teachAtt/openAtt/"+[[${main.mainId}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }

        //点击查看定价评审表
        function fixDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "定价评审",
                content: ctx + "zzlx/teachFix/openFix/"+[[${main.mainId}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }


        //点击查看合同表
        function htDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "模拟市场合同",
                content: ctx + "zzlx/teachContract/openContract/"+[[${main.mainId}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }

        //点击查看计划任务书
        function planDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "计划任务书",
                content: ctx + "zzlx/teachPlantask/openPlan/"+[[${main.mainId}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }

        //点击查看结题报告
        function jtReportDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "结题报告",
                content: ctx + "zzjt/report/queryDetail/"+[[${main.mainId}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }

        //点击查看评估表
        function assessDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "结题评估表",
                content: ctx + "zzjt/assess/queryDetail/"+[[${main.mainId}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }

        //点击查看项目结算表
        function settleDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "项目结算表",
                content: ctx + "zzjt/settle/queryDetail/"+[[${main.mainId}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }




    </script>
</body>
</html>