<!-- 审批意见 -->
<div th:fragment="approveCommet">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a data-toggle="collapse" data-parent="#version" href="#approveCommet1" aria-expanded="false"
                   class="collapsed">审批意见
                    <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
            </h4>
        </div>
        <!--折叠区域-->
        <div id="approveCommet1" class="panel-collapse collapse in" aria-expanded="false">
            <div class="panel-body">
                <table class="table table-bordered">
                    <tbody>
                    <tr th:if="${approve eq'true'}">
                        <td style="text-align: right;" width="20%" th:text="${headName==null ? '审批结果' : headName}"></td>
                        <td style="text-align: left;">
                            <div class="radio radio-info radio-inline" required>
                                <input type="radio" class="styled" th:required="${required ==null ?'false':required}"
                                       th:name="${approveName==null ? 'approve' : approveName}" value="1">
                                <label th:text="${headTitle1==null ? '同意' : headTitle1}"> </label>
                            </div>
                            <div class="radio radio-info radio-inline" required>
                                <input type="radio" class="styled" th:required="${required ==null ?'false':required}"
                                       th:name="${approveName==null ? 'approve' : approveName}" value="0">
                                <label th:text="${headTitle2==null ? '不同意' : headTitle2}"> </label>
                            </div>
                        </td>
                    </tr>
                    <tr th:if="${nextStep eq'true'}" id="nextStep">
                        <td style="text-align: right;" width="20%">
                            下一步流转给
                        </td>
                        <td style="text-align: left;">
                            <div class="radio radio-info radio-inline"
                                 th:each="dict:${@WorkFlowInfoService.getNextTransition(instanceId)}" required>
                                <input type="radio" class="styled" name="transitionKey"
                                       th:value="${dict.transitionKey}">
                                <label th:text="${dict.transitionName}"
                                       th:if="${not #strings.isEmpty(dict.transitionName)}">InlineOne </label>
                            </div>
                        </td>
                    </tr>

                    <tr>
                        <td style="text-align: right;" width="20%">
                            <label id="comTitle" th:text="${commentName==null ? '审批意见:' :commentName}"></label>
                        </td>
                        <td style="text-align: left;">
                            <textarea name="comment" id="comment" style="height:150px; "
                                      class="form-control"></textarea>
                        </td>
                    </tr>
                    <tr th:if="${returnsActivitie eq'true'}">
                        <td style="text-align: right;" width="20%">退回</td>
                        <td style="text-align: left;">
                            <div class="radio radio-info radio-inline"
                                 th:each="dict:${T(com.baosight.zgbwkj.mp.wf.util.WorkFlowUtil).getReturnsActivities(taskId)}"
                                 required>
                                <input type="radio" class="styled"
                                       th:name="${returnsName==null ? 'returnsName' :returnsName}"
                                       th:value="${dict.nodeKey}">
                                <label th:text="${dict.nodeName}"> </label>
                            </div>
                        </td>
                    </tr>

                    <tr th:if="${freeFlow eq'true'}">
                        <td style="text-align: right;" width="20%">
                            转任务给：
                        </td>
                        <td style="text-align: left;">
                            <div class="col-sm-5">
                                <div class="input-group">
                                    <input name="userCode" id="userCode" type="hidden"> <input
                                        name="userName" id="userName" onclick="choiceUser('userCode','userName')"
                                        type="text" placeholder="请选择人员"
                                        class="form-control"> <span class="input-group-addon"><i
                                        class="fa fa-search"></i></span>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <button type="button" class="btn btn-sm btn-primary" onclick="submitFreeFlow('1')"><i
                                        class="fa fa-check"></i>转其直接处理
                                </button>
                                <button type="button" class="btn btn-sm btn-primary" onclick="submitFreeFlow('2')"><i
                                        class="fa fa-check"></i>转其拟处理
                                </button>
                            </div>
                        </td>
                    </tr>
                    </tbody>
                </table>
                <script type="text/javascript" th:inline="javascript">
                    var nextStep = [[${nextStep}]];
                    var approve = [[${approve}]];
                    if (nextStep == "true" && approve == 'true') {
                        $("#nextStep").hide();
                        $("input[name='approve']").change(function () {
                            var val = $('input:radio[name="approve"]:checked').val();
                            if (val == '1') {
                                $("#nextStep").show();
                            }
                            if (val == '0') {
                                $("#nextStep").hide();
                            }
                        });
                    }
                    var required = [[${required}]];
                    if (required == "true") {
                        $("#comTitle").addClass("control-label is-required");
                        $("#comment").prop("required", "required");
                    }
                    // var approveName = [[${approveName}]];
                    // if(!approveName)
                    //     approveName = "approve";
                    // $("input[type=radio][name='"+approveName+"']").click(function(){
                    //     if(this.value=="1"){
                    //         $("#comment").val("拟同意");
                    //     }else{
                    //         $("#comment").val("不同意");
                    //     }
                    // });
                </script>
            </div>
        </div>
    </div>
</div>


<!-- 批处理 -->
<div th:fragment="batch">
    <div class="btn-group-sm" role="group" style="text-align: center;">
        <a class="btn btn-success ishide" onclick="batchProcessing()"><i class="fa fa-upload"></i> 提交</a>
        <th:block th:include="/component/wfReturnByCode :: init(processCode=${processCode},currentActivity=${activityCode},callback=allBack)"/>
        <br>
        <br>
        <a class="btn btn-success ishide" onclick="batchProcessing('all');"><i class="fa fa-upload"></i> 一键提交</a>
    </div>

    <script th:src="@{/zzzc/js/common.js}"></script>
    <script th:inline="javascript">
        var tabId = [[${tabId}]];
        //提交
        function batchProcessing(type) {
            if ($.modal.confirm("确认要提交吗?", function () {
                var index = type === 'all' ? $('#' + tabId).bootstrapTable('getData') : $('#' + tabId).bootstrapTable('getSelections');
                if (index.length == 0) {
                    $.modal.alertWarning("请至少选择一条记录");
                    return;
                }
                saveAjax(ctx + "zzlx/taskInfo/batchProcessing", getCheckDate(index),"",tabId);
            })) ;
        }

        //一键退回
        function allBack(backJd) {
            if ($.modal.confirm("确认要退回吗?", function () {
                var index = $('#' + tabId).bootstrapTable('getData');
                var checkDate = getCheckDate(index);
                for (var el of checkDate) {
                    el["psjg"] = "returnDeclaration";
                    el["ativeCode"] = backJd;
                }
                saveAjax(ctx + "KYND/doReturnAll", checkDate,"",tabId);
            })) ;
        }

    </script>

</div>