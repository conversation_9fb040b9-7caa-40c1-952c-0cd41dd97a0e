<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('项目决算信息')" />
	<th:block th:include="include :: baseJs" />
	<th:block th:include="ktmy/ktmyBase :: init" />
</head>
<body class="gray-bg">
	<div class="wrapper wrapper-content">
		<div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true" th:object="${finalMainVO}">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" data-parent="#version" href="#htxx" aria-expanded="false" class="collapsed">
							合同信息
							<span class="pull-right">
								<i class="fa fa-chevron-down" aria-hidden="true"></i>
							</span>
						</a>
					</h4>
				</div>
				<div id="htxx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<input name="finalMainVO.jsId" th:field="*{jsId}" type="hidden">
						<input name="finalMainVO.projectId" th:field="*{projectId}" type="hidden">
						<div class="form-group">
							<div class="col-sm-12">
								<table class="table table-bordered table-hover table-striped">
									<thead>
										<tr>
											<th style="text-align: center;" width="100px">序号</th>
											<th style="text-align: center;" width="100px">内容</th>
											<th style="text-align: center;"></th>
										</tr>
									</thead>
									<tbody>
										<tr>
											<td style="text-align: center;">1</td>
											<td style="text-align: center;">收入</td>
											<td>
												<div class="form-group">
													<label class="col-sm-2 control-label">实际收款额：</label>
													<div class="col-sm-10">
														<div class="form-control-static" th:utext="*{totalAmount}"></div>
													</div>
												</div>
												<div class="form-group">
													<label class="col-sm-2 control-label">是否有增值收入：</label>
													<div class="col-sm-10">
														<th:block th:include="component/radio :: init(see=true, id='finalMainVO_isAdded', name='finalMainVO.isAdded',businessType='MPTY', dictCode='is_yes_no', value=*{isAdded}, isrequired=true)" />
													</div>
												</div>
												<div class="form-group">
													<div class="col-sm-12">
														<th:block th:include="ktmy/finalIncome/finalIncomeTableRead :: init(data=*{finalIncomeList}, isAdded=*{isAdded})" />
													</div>
												</div>
											</td>
										</tr>
										<tr>
											<td style="text-align: center;">2</td>
											<td style="text-align: center;">支出</td>
											<td>
												<table class="table table-bordered table-hover table-striped">
													<tbody>
														<tr>
															<td style="text-align: left;" colspan="2">一、直接费用</td>
															<td></td>
														</tr>
														<tr>
															<td width="100px"></td>
															<td style="text-align: left;" width="200px">1.项目费用支出</td>
															<td></td>
														</tr>
														<tr>
															<td colspan="3">
																<div class="form-group">
																	<div class="col-sm-12">
																		<th:block th:include="ktmy/finalPay/finalPayTableRead :: init(data=*{finalPayList})" />
																	</div>
																</div>
															</td>
														</tr>
														<tr>
															<td></td>
															<td>2.技术贸易公共费用分摊额</td>
															<td>
																<input id="costPub" name="finalMainVO.costPub" th:field="*{costPub}" class="form-control" type="text" readonly="readonly" placeholder="总收入*公摊率/100">
															</td>
														</tr>
														<tr>
															<td></td>
															<td>3.营业税额及附加</td>
															<td>
																<input id="costAdd" name="finalMainVO.costAdd" th:field="*{costAdd}" class="form-control" type="text" readonly="readonly">
															</td>
														</tr>
														<tr>
															<td colspan="2">二、间接费用</td>
															<td></td>
														</tr>
														<tr>
															<td></td>
															<td>1.人工成本</td>
															<td>
																<input id="rgIndirect" name="finalMainVO.rgIndirect" th:field="*{rgIndirect}" class="form-control" type="text" required="required" readonly="readonly">
															</td>
														</tr>
														<tr>
															<td></td>
															<td>2.企业所得税</td>
															<td>
																<input id="qyIndirect" name="finalMainVO.qyIndirect" th:field="*{qyIndirect}" class="form-control" type="text" required="required" readonly="readonly">
															</td>
														</tr>
														<tr>
															<td></td>
															<td>3.其他间接费用</td>
															<td>
																<input id="qtIndirect" name="finalMainVO.qtIndirect" th:field="*{qtIndirect}" class="form-control" type="text" required="required" readonly="readonly">
															</td>
														</tr>
														<tr>
															<td></td>
															<td>4.其他间接费用扣除率%</td>
															<td th:text="*{kcIndirect}"></td>
														</tr>
														<tr>
															<td colspan="2">三、分包费用</td>
															<td>
																<input id="fbje" name="finalMainVO.fbje" th:field="*{fbje}" class="form-control" type="text" readonly="readonly">
															</td>
														</tr>
														<tr>
															<td colspan="2">四、总支出</td>
															<td>
																<input id="expenditure" name="finalMainVO.expenditure" th:field="*{expenditure}" class="form-control" type="text" readonly="readonly">
															</td>
														</tr>
														<tr>
															<td></td>
															<td>项目费用支出备注</td>
															<td>
																<input class="form-control" name="finalMainVO.richText[zcbz]" type="text" th:value="*{richText?.get('zcbz')}" readonly="readonly">
															</td>
														</tr>
													</tbody>
												</table>
											</td>
										</tr>
										<tr>
											<td style="text-align: center;">3</td>
											<td style="text-align: center;">净收益</td>
											<td>
												<input id="netIncome" name="finalMainVO.netIncome" th:field="*{netIncome}" class="form-control" type="text" readonly="readonly">
											</td>
										</tr>
									</tbody>
								</table>
							</div>
						</div>
						<div class="form-group">
							<label class="col-sm-2 control-label is-required">是否常规项目：</label>
							<div class="col-sm-10">
								<th:block th:include="component/radio :: init(see=true, name='finalMainVO.isNormal',businessType='MPTY', dictCode='is_yes_no', value=*{isNormal}, isrequired=true, callback=ifIsNormal)" />
							</div>
						</div>

						<div class="ifIsNormal">
							<div class="form-group">
								<label class="col-sm-2 control-label is-required">预计总奖励额度(元)：</label>
								<div class="col-sm-4">
									<input name="finalMainVO.netYjTotal" th:field="*{netYjTotal}" class="form-control" type="text" readonly="readonly">
								</div>
							</div>
							<div class="form-group">
								<label class="col-sm-2 control-label">建议总奖励额(元)：</label>
								<div class="col-sm-4">
									<input name="finalMainVO.awardAdv" th:field="*{awardAdv}" class="form-control" type="text" readonly="readonly">
								</div>
								<label class="col-sm-2 control-label">本次结题奖励额(元)：</label>
								<div class="col-sm-4">
									<input name="finalMainVO.awardJlje" th:field="*{awardJlje}" class="form-control" type="text" readonly="readonly">
								</div>
							</div>
						</div>

						<div class="form-group">
							<div class="col-sm-6"></div>
							<label class="col-sm-2 control-label">签名：</label>
							<div class="col-sm-1">
								<th:block th:include="/component/selectUser::init(see=true, userCodeId='finalMainVO.awardCode', userNameId='finalMainVO.awardName', selectType='S', value=*{awardCode}, isrequired=true)" />
							</div>
							<label class="col-sm-1 form-control-static">日期：</label>
							<div class="col-sm-2">
								<input name="finalMainVO.awardDate" th:field="*{awardDate}" class="form-control" type="text" readonly="readonly">
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">
			<button type="button" class="btn btn-sm btn-danger" onclick="closeItem()">
				<i class="fa fa-reply-all"></i>
				关 闭
			</button>
		</div>
		<!--按钮区end-->
	</div>
	<script th:inline="javascript">
        var prefix = ctx + "ktmy/finalMain";
        $(document).ready(function(){
			var isNormal = [[${finalMainVO.isNormal}]];//是否常规项目
			if(!isNormal || isNormal == '0'){
				ifIsNormal(isNormal);
			}
		});
        
        function ifIsNormal(item){
			if (item == '1') {
				$('.ifIsNormal').removeClass('hide');
			} else {
				$('.ifIsNormal').addClass('hide');
			}
		}
    </script>
</body>
</html>