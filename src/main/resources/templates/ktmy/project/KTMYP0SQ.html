<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('项目申请列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
     	<div class="search-collapse">
        	<form id="formId" class="form-horizontal" style="width: 99%;">
            	<div class="form-group">
            		<label class="col-sm-1 control-label">项目名称:</label>
					<div class="col-sm-3">
						<input type="text" class="form-control" name="projectNameLike" placeholder="支持模糊查询"/>
				    </div>
				    <div class="col-sm-3">
						<a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
							<i class="fa fa-search"></i>
							&nbsp;搜索
						</a>
						<a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
							<i class="fa fa-refresh"></i>
							&nbsp;重置
						</a>
					</div>
				</div>
			</form>
		</div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="chooseProjectMethod()" >
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                <i class="fa fa-edit"></i> 编辑
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "ktmy/project";
        var projectKindList = [[${@dict.getDictList('KTMY','PROJECT_KIND')}]];
        $(function() {
            var options = {
                url: prefix + "/pageDraft",
                createUrl: prefix + "/toPage/KTMYPA01",
                updateUrl: ctx + "ktmy/workFlow/queryDBDetail/KTMY_PROJCET_APPLY/KTMYPA01/{id}/",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "项目申请",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'projectId',
                    title: '申请表ID',
                    visible: false
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'projectStatus',
                    title: '项目状态',
                    formatter: function(value, row, index) {
                    	if('draft'==value){
                    		return '草稿';
                    	}
                    	return value;
                    }
                }, 
                {
                    field: 'projectTradeunit',
                    title: '贸易对象'
                },
                {
                    field: 'projectKind',
                    title: '项目性质',
                    formatter: function(value, row, index) {
                    	return $.table.selectDictLabel(projectKindList, value);
                    }
                },
                {
                    field: 'projectDeptName',
                    title: '合同执行单位名称'
                },
                {
                    field: 'projectXmglName',
                    title: '项目管理单位'
                },
                {
                    field: 'projectCjName',
                    title: '参加单位'
                },
                {
                    field: 'projectFzrName',
                    title: '项目负责人名称'
                },

                {
                    field: 'projectXmzgCode',
                    title: '项目主管工号'
                },
                {
                    field: 'projectXmzgName',
                    title: '项目主管名称'
                },        
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.projectId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.projectId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
        
        function chooseProjectMethod(){
        	$.modal.open("请选择申请表类型", prefix + "/toPage/projectMethod", 500, 300);
        }
        
        function toCreate(projectMethod){
        	$.modal.openTab("项目申请", prefix + "/toWFPage/KTMYPA01/"+projectMethod);
        }
    </script>
</body>
</html>