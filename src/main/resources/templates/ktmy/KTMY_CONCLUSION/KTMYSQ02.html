<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('项目奖励申请表')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content" th:object="${conclusionVO}">
        <form class="form-horizontal m" id="form-conclusionVO">
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true" th:object="${conclusionVO.project}">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">项目信息
                                <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目名称：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static" th:utext="*{projectName}"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目编号：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static" th:utext="*{projectNum}"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">贸易对象：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="*{projectTradeunit}"></div>
                                </div>
                                <label class="col-sm-2 control-label">项目性质：</label>
                                <div class="col-sm-4">
                                    <th:block th:include="component/select :: init(see=true, businessType='KTMY', dictCode='PROJECT_KIND', isfirst=true, value=*{projectKind}, see=true)" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">合同执行单位：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static" th:utext="*{projectDeptName}"></div>
                                </div>
                            </div>
                            <div class="form-group">
                            	<label class="col-sm-2 control-label">项目详细：</label>
                            	<div class="col-sm-10">
	                                <th:block th:include="ktmy/ktmyBase :: projectDetailHref(projectId=*{projectId})" />
                            	</div>
                            </div>
                            <div class="form-group">
	                        	<label class="col-sm-2 control-label">主合同详细：</label>
	                        	<div class="col-sm-10">
	                             	<th:block th:include="ktmy/ktmyBase :: contractDetailHref(projectId=*{projectId})" />
	                        	</div>
	                        </div>
	                        <div class="form-group">
	                        	<label class="col-sm-2 control-label">收款合同：</label>
	                        	<div class="col-sm-10">
	                             	<th:block th:include="ktmy/ktmyBase :: collectionContractDetailByProjectIdHref(projectId=*{projectId})" />
	                        	</div>
	                        </div>
	                        <div class="form-group">
	                        	<label class="col-sm-2 control-label">分包合同列表：</label>
	                        	<div class="col-sm-10">
	                             	<th:block th:include="ktmy/ktmyBase :: contractFBListHref(projectId=*{projectId})" />
	                        	</div>
	                        </div>
							<div class="form-group">
	                        	<label class="col-sm-2 control-label">决算详细：</label>
	                        	<div class="col-sm-10">
	                             	<th:block th:include="ktmy/ktmyBase :: finalMainDetailHref(projectId=*{projectId})" />
	                        	</div>
	                        </div>
	                        <div class="form-group">
	                        	<label class="col-sm-2 control-label">评估详细：</label>
	                        	<div class="col-sm-10">
	                             	<th:block th:include="ktmy/ktmyBase :: accessDetailHref(projectId=*{projectId})" />
	                        	</div>
	                        </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="panel-group" aria-multiselectable="true" th:object="${conclusionVO.award}">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" href="#xmjl" aria-expanded="false" class="collapsed">
								项目奖励
								<span class="pull-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
								</span>
							</a>
						</h4>
					</div>
					<div id="xmjl" class="panel-collapse collapse in" aria-expanded="false">
						<div class="panel-body">
                            <input name="award.awardId" th:field="*{awardId}" type="hidden">
                            <input name="award.projectId" th:field="*{projectId}" type="hidden">
                            <div class="mnote-editor-title">系数确定标准</div>
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <table th:if="${conclusionVO.finalMainVO.isNormal == '1'}" class="table table-bordered table-hover table-striped">
                                    	<tr>
                                    		<td style="width: 200px;">评估分值M（分）</td>
                                    		<td>M＜50</td>
                                    		<td>65＞M≥50</td>
                                    		<td>75＞M≥65</td>
                                    		<td>90＞M≥75</td>
                                    		<td>M≥90</td>
                                    	</tr>
                                    	<tr>
                                    		<td>奖励系数S（20～35％）</td>
                                    		<td>20～23％</td>
                                    		<td>24～26％</td>
                                    		<td>27～29％</td>
                                    		<td>30～32％</td>
                                    		<td>33～35％</td>
                                    	</tr>
                                    </table>
                                    <th:block th:if="${conclusionVO.finalMainVO.isNormal == '0'}">
	                                    <table th:if="${conclusionVO.access.pgPro == 'YBMY'}" id="YBMY" class="pgProClass table table-bordered table-hover table-striped">
	                                    	<tr>
	                                    		<td style="width: 200px;">评估分值M（分）</td>
	                                    		<td>M＜65</td>
	                                    		<td>75＞M≥65</td>
	                                    		<td>85＞M≥75</td>
	                                    		<td>95＞M≥85</td>
	                                    		<td>M≥95</td>
	                                    	</tr>
	                                    	<tr>
	                                    		<td>奖励系数S（20～35％）</td>
	                                    		<td>20～23％</td>
	                                    		<td>24～26％</td>
	                                    		<td>27～29％</td>
	                                    		<td>30～32％</td>
	                                    		<td>33～35％</td>
	                                    	</tr>
	                                    </table>
	                                    <table th:if="${conclusionVO.access.pgPro == 'TDKY'}" id="TDKY" class="pgProClass table table-bordered table-hover table-striped">
	                                    	<tr>
	                                    		<td style="width: 200px;">评估分值M（分）</td>
	                                    		<td>M＜50</td>
	                                    		<td>70＞M≥50</td>
	                                    		<td>M≥70</td>
	                                    	</tr>
	                                    	<tr>
	                                    		<td>奖励系数S（0～15％）</td>
	                                    		<td>0～5％</td>
	                                    		<td>6～14％</td>
	                                    		<td>15％</td>
	                                    	</tr>
	                                    </table>
	                                    <table th:if="${conclusionVO.access.pgPro == 'ZDCG'}" id="ZDCG" class="pgProClass table table-bordered table-hover table-striped">
	                                    	<tr>
	                                    		<td style="width: 200px;">评估分值M（分）</td>
	                                    		<td>M＜50</td>
	                                    		<td>90＞M≥50</td>
	                                    		<td>M≥90</td>
	                                    	</tr>
	                                    	<tr>
	                                    		<td>奖励系数S（0～50％）</td>
	                                    		<td>0～40％</td>
	                                    		<td>41～49％</td>
	                                    		<td>50％</td>
	                                    	</tr>
	                                    </table>
	                                    <table th:if="${conclusionVO.access.pgPro == 'TDXM'}" id="TDXM" class="pgProClass table table-bordered table-hover table-striped">
	                                    	<tr>
	                                    		<td style="width: 200px;">评估分值M（分）</td>
	                                    		<td>M＜60</td>
	                                    		<td>75＞M≥60</td>
	                                    		<td>M≥75</td>
	                                    	</tr>
	                                    	<tr>
	                                    		<td>奖励系数S（0～10％）</td>
	                                    		<td>0～5％</td>
	                                    		<td>6～9％</td>
	                                    		<td>10％</td>
	                                    	</tr>
	                                    </table>
                                    </th:block>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目评估分M：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="*{extra2}"></div>
                                    <input th:field="*{extra2}" type="hidden"/>
                                </div>
                                <label class="col-sm-2 control-label">项目净收益Y：</label>
                                <div class="col-sm-4">
                                	<div class="form-control-static" th:utext="*{extra3}"></div>
                                    <input th:field="*{extra3}" type="hidden"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">奖励系数S：</label>
                                <div class="col-sm-10">
                                	<div class="form-control-static" th:utext="*{extra1}+'%'"></div>
                                    <input th:field="*{extra1}" type="hidden"/>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">预奖信息：</label>
                                <div class="col-sm-10">
                                	<th:block th:include="ktmy/award/awardYJTableRead :: init(projectId=*{projectId})" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">建议总奖励额(元)：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="*{awardJyjlje}"></div>
                                </div>
                                <label class="col-sm-2 control-label">预奖金额(元)：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${conclusionVO.yjje}"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">本次结题奖励额(元)：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="*{awardThisJlje}"></div>
                                </div>
                                <label class="col-sm-2 control-label">决定结题奖励额(元)：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="*{awardJlje}"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">备注：</label>
                                <div class="col-sm-8">
                                    <div class="form-control-static" th:utext="*{awardRemark}"></div>
                                </div>
                            </div>                          
                        </div>
					</div>
				</div>
			</div>

            <!-- 流程意见 name必须为 workFlow.comment -->
			<div class="panel-group" aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" href="#spyj" aria-expanded="false" class="collapsed">
								审批意见
								<span class="pull-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
								</span>
							</a>
						</h4>
					</div>
					<div id="spyj" class="panel-collapse collapse in" aria-expanded="false">
						<div class="panel-body">
	                        <div class="form-group">
	                        	<div class="col-sm-12">
		                            <textarea class="form-control" id="workFlow_comment" name="workFlow.comment" style="height: 200px; width: 100%;"
		                                      th:utext="*{workFlow.comment}"></textarea>
	                            </div>
	                        </div>
                        </div>
					</div>
				</div>
			</div>
			<!-- 流程意见end -->		
			<!-- 流程相关信息 -->
			<th:block th:include="component/wfWorkFlow :: init(workFlow=*{workFlow})" />
			<!-- 流程相关信息end -->
        </form>
		<!-- 按钮区 -->
		<th:block th:include="ktmy/wfDetailButton :: init(workFlow=*{workFlow})" />
		<!-- 按钮区end -->
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "ktmy/conclusion";

        $("#form-conclusionVO").validate({
            focusCleanup: true
        });

        function save(){
			var config = {
				url: prefix + "/save",
				type: "post",
				dataType: "json",
				data: $('#form-conclusionVO').serialize(),
				beforeSend: function () {
					$.modal.loading("正在处理中，请稍后...");
				},
				success: function (result) {
					$.modal.alertSuccess(result.msg);
					$.modal.closeLoading();
				}
			};
			$.ajax(config);
		}
        
        function doSubmit(transitionKey) {
			if ($.validate.form()) {
				$.modal.confirm("确认提交吗？", function () {
					$.operate.saveTabAlert(prefix + "/doSubmit", $('#form-conclusionVO').serialize());
				});		
			}
		}
		
		function doReturn(returnActivityKey) {
			var url = prefix + "/doReturn";
			if ($.validate.form()) {
				$.modal.confirm("确认退回吗？", function () {
					$.operate.saveTabAlert(url, $('#form-conclusionVO').serialize());
				});		
			}
		}

    </script>
</body>
</html>