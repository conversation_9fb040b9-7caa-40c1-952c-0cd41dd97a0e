<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('关键技术')" />
    <th:block th:include="include :: baseJs" />
    <th:block th:include="ktmy/ktmyBase :: init" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-projectApplyVO">
        	<div class="panel-group" aria-multiselectable="true" th:object="${projectApplyVO}">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" href="#xmxx" aria-expanded="false" class="collapsed">
								项目信息
								<span class="pull-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
								</span>
							</a>
						</h4>
					</div>
					<div id="xmxx" class="panel-collapse collapse in" aria-expanded="false">
						<div class="panel-body">
	                        <input name="projectId" th:field="*{projectId}" type="hidden">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">项目名称：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static" th:utext="*{projectName}"></div>
                                </div>
                            </div>
                            <div class="form-group">
								<label class="col-sm-2 control-label is-required">执行单位：</label>
								<div class="col-sm-10">
									<th:block th:include="/component/selectOrg::init(see=true, orgCodeId='projectDeptCode', orgNameId='projectDeptName', selectType='S', value=*{projectDeptCode}, isrequired=true )" />
								</div>
							</div>
                        </div>
					</div>
				</div>
			</div>
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true" th:object="${projectApplyVO.kettechJsVO}">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version" href="#gjjs" aria-expanded="false" class="collapsed">关键技术
                                <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="gjjs" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <input name="kettechJsVO.jsGuid" th:field="*{jsGuid}" type="hidden">
                            <input name="kettechJsVO.projectId" th:field="*{projectId}" type="hidden">
                            <input name="kettechJsVO.jsPszzCode" th:field="*{jsPszzCode}" type="hidden">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">技术类别：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="component/checkbox :: init(see=true, name='kettechJsVO.jsJslb',businessType='KTMY', dictCode='JS_JSLB', isfirst=true, value=*{jsJslb}, isrequired=true)" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">是否申请专利：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="component/radio :: init(see=true, name='kettechJsVO.jsIsSqzl', businessType='MPTY', dictCode='is_yes_no', value=*{jsIsSqzl}, isrequired=true)" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">是否审定技术秘密：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="component/radio :: init(see=true, name='kettechJsVO.jsIsTechscreat', businessType='MPTY', dictCode='is_yes_no', value=*{jsIsTechscreat}, isrequired=true)" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">技术产权状况：</label>
                                <div class="col-sm-10">
                                    <textarea readonly="readonly" class="form-control" style="height: 200px;" name="kettechJsVO.jsRichText[cqzk]" width="100%" th:utext="*{jsRichText?.get('cqzk')}"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">现有技术水平：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="component/radio :: init(see=true, name='kettechJsVO.jsXyjssp', businessType='KTMY', dictCode='JS_XYJSSP', value=*{jsXyjssp}, isrequired=true)" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">技术水平应用情况：</label>
                                <div class="col-sm-10">
                                    <textarea readonly="readonly" class="form-control" style="height: 200px;" name="kettechJsVO.jsRichText[jssp]" width="100%" th:utext="*{jsRichText?.get('jssp')}"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">国内外是否具有同类技术：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="component/radio :: init(see=true, name='kettechJsVO.jsIsSametech', businessType='MPTY', dictCode='is_yes_no', value=*{jsIsSametech})" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">同类技术比较：</label>
                                <div class="col-sm-10">
                                    <textarea readonly="readonly" class="form-control" style="height: 200px;" name="kettechJsVO.jsRichText[tljs]" width="100%" th:utext="*{jsRichText?.get('tljs')}"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">该项技术的储备情况：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="component/radio :: init(see=true, name='kettechJsVO.jsTechsave', businessType='MPTY', dictCode='is_yes_no', value=*{jsTechsave})" />                                  
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">技术储备：</label>
                                <div class="col-sm-10">
                                    <textarea readonly="readonly" class="form-control" style="height: 200px;" name="kettechJsVO.jsRichText[jscb]" width="100%" th:utext="*{jsRichText?.get('jscb')}"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">产品定位：</label>
                                <div class="col-sm-10">
                                    <textarea readonly="readonly" class="form-control" style="height: 200px;" name="kettechJsVO.jsRichText[cpdw]" width="100%" th:utext="*{jsRichText?.get('cpdw')}"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">产线规划：</label>
                                <div class="col-sm-10">
                                    <textarea readonly="readonly" class="form-control" style="height: 200px;" name="kettechJsVO.jsRichText[cxgh]" width="100%" th:utext="*{jsRichText?.get('cxgh')}"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">竞争威胁：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="component/radio :: init(see=true, name='kettechJsVO.jsIsDanger', businessType='KTMY', dictCode='JS_IS_DANGER', value=*{jsIsDanger})" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">具体理由：</label>
                                <div class="col-sm-10">
                                    <textarea readonly="readonly" class="form-control" style="height: 200px;" name="kettechJsVO.jsRichText[jzwx]" width="100%" th:utext="*{jsRichText?.get('jzwx')}"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">是否准予输出：</label>
                                <div class="col-sm-10" th:if="${projectApplyVO.workFlow.currentActivity=='GJJS_GSFGLD' || projectApplyVO.workFlow.currentActivity=='GJJS_GSLD'}">
                                    <th:block th:include="component/radio :: init(name='kettechJsVO.jsIsOutput', businessType='KTMY', dictCode='JS_IS_OUTPUT', value=*{jsIsOutput}, isrequired=true)" />                                   
                                </div>
                                <div class="col-sm-10" th:unless="${projectApplyVO.workFlow.currentActivity=='GJJS_GSFGLD' || projectApplyVO.workFlow.currentActivity=='GJJS_GSLD'}">
                                    <th:block th:include="component/radio :: init(see=true, name='kettechJsVO.jsIsOutput', businessType='KTMY', dictCode='JS_IS_OUTPUT', value=*{jsIsOutput}, isrequired=true)" />                                   
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">结论陈述：</label>
                                <div class="col-sm-10">
                                    <textarea readonly="readonly" class="form-control" style="height: 200px;" name="kettechJsVO.jsRichText[jlcs]" width="100%" th:utext="*{jsRichText?.get('jlcs')}"></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">是否选择专家评审：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="component/radio :: init(see=true, name='kettechJsVO.jsIsChooseZj', businessType='MPTY', dictCode='is_yes_no', value=*{jsIsChooseZj}, callback=ifJsIsChooseZj, isrequired=true)" />   
                                </div>
                            </div>                          
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="panel-group ifJsIsChooseZj" aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" href="#lxyc" aria-expanded="false" class="collapsed">
								专家评审
								<span class="pull-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
								</span>
							</a>
						</h4>
					</div>
					<div id="lxyc" class="panel-collapse collapse in" aria-expanded="false">
						<div class="panel-body">
                            <th:block th:include="ktmy/projectMember/projectMember_user :: init(sourceId=${projectApplyVO.kettechJsVO.jsGuid},sourceType='userGjjs',taskId=${projectApplyVO.workFlow.taskId})" />
                        </div>
					</div>
				</div>
			</div>
        </form>
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
	    $(document).ready(function(){
			var jsIsChooseZj = [[${projectApplyVO.kettechJsVO.jsIsChooseZj}]];//是否专家评审
			if(!jsIsChooseZj || jsIsChooseZj == '0'){
				$('.ifJsIsChooseZj').addClass('hide');
			}
		});
    </script>
</body>
</html>