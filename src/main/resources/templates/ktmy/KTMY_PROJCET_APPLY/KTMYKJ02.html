<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('关键技术')" />
    <th:block th:include="include :: baseJs" />
    <th:block th:include="ktmy/ktmyBase :: init" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content" th:object="${projectApplyVO}">
        <form class="form-horizontal m" id="form-projectApplyVO">
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">项目信息
                                <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">项目名称：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static" th:utext="*{projectName}"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">贸易对象：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="*{projectTradeunit}"></div>
                                </div>
                                <label class="col-sm-2 control-label">项目性质：</label>
                                <div class="col-sm-4">
                                    <th:block th:include="component/select :: init(businessType='KTMY', dictCode='PROJECT_KIND', isfirst=true, value=*{projectKind}, see=true)" />
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">合同执行单位：</label>
                                <div class="col-sm-10">
                                    <div class="form-control-static" th:utext="*{projectDeptName}"></div>
                                </div>
                            </div>
                            <div class="form-group">
                            	<label class="col-sm-2 control-label">项目信息：</label>
                            	<div class="col-sm-10">
                            		<th:block th:include="ktmy/ktmyBase :: projectDetailHref(projectId=*{projectId})" />
                            	</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <input name="kettechJsVO.jsGuid" th:field="${projectApplyVO.kettechJsVO.jsGuid}" type="hidden">
            <!-- 流程意见 name必须为 workFlow.comment -->
			<div class="panel-group" aria-multiselectable="true">
				<div class="panel panel-default">
					<div class="panel-heading">
						<h4 class="panel-title">
							<a data-toggle="collapse" href="#spyj" aria-expanded="false" class="collapsed">
								审批意见
								<span class="pull-right">
									<i class="fa fa-chevron-down" aria-hidden="true"></i>
								</span>
							</a>
						</h4>
					</div>
					<div id="spyj" class="panel-collapse collapse in" aria-expanded="false">
						<div class="panel-body">
	                        <div class="form-group">
	                        	<div class="col-sm-12">
		                            <textarea class="form-control" id="workFlow_comment" name="workFlow.comment" style="height: 200px; width: 100%;"
		                                      th:utext="*{workFlow.comment}" required></textarea>
	                            </div>
	                        </div>
                        </div>
					</div>
				</div>
			</div>
			<!-- 流程意见end -->		
			<!-- 流程相关信息 -->			
			<th:block th:include="component/wfWorkFlow :: init(workFlow=*{workFlow})" />
			<!-- 流程相关信息end -->
        </form>
		<!-- 按钮区 -->
		<th:block th:include="ktmy/wfDetailButton :: init(workFlow=*{workFlow})" />
		<!-- 按钮区end -->
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
    	var prefix = ctx + "ktmy/project";

        $("#form-projectApplyVO").validate({
            focusCleanup: true
        });

        function save(){
			var config = {
				url: prefix + "/save",
				type: "post",
				dataType: "json",
				data: $('#form-projectApplyVO').serialize(),
				beforeSend: function () {
					$.modal.loading("正在处理中，请稍后...");
				},
				success: function (result) {
					$.modal.alertSuccess(result.msg);
					$.modal.closeLoading();
				}
			};
			$.ajax(config);
		}
        
        function doSubmit(transitionKey) {
			if ($.validate.form()) {
				$.modal.confirm("确认提交吗？", function () {
					$.operate.saveTabAlert(prefix + "/doSubmit", $('#form-projectApplyVO').serialize());
				});		
			}
		}
		
		function doReturn(returnActivityKey) {
			var url = prefix + "/doReturn";
			if ($.validate.form()) {
				$.modal.confirm("确认退回吗？", function () {
					$.operate.saveTabAlert(url, $('#form-projectApplyVO').serialize());
				});		
			}
		}

    </script>
</body>
</html>