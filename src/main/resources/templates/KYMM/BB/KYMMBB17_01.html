<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('个人汇总绩效')" />
<th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
	<div class="container-div">
		<div class="row">
			<div class="col-sm-12 select-table table-bordered">
				<form id="formId">
					<input type="hidden" id="year" name="year" th:value="${year}">
					<input type="hidden" id="month" name="month" th:value="${month}">
					<input type="hidden" id="orgCode" name="orgCode" th:value="${orgCode}">
				</form>
				<table id="bootstrap-table">
				</table>
			</div>
		</div>
	</div>

	<script th:inline="javascript">
        var prefix = ctx + "kymm/BB";     
        $(function() {
            var options = {
                url: prefix + "/list/query17_01",
		        showSearch: false,
		        showRefresh: false,
		        showToggle: false,
		        showColumns: false,
		        pagination: false,
		        showFooter: true,
		        queryParams: queryParams,
		        columns : [
		        	{
						align : 'center',
						title : '序号',
						width : 50,
						formatter: function(value, row, index) {
							return index+1;
						},
		        		footerFormatter:function (value) {
					        return '合计';
					    }
					},
					{
						field : 'memberName',
						align : 'center',
						title : '姓名'
					},
					{
						field : 'memberGh',
						align : 'center',
						title : '工号'
					},
					{
						field : 'deptName',
						align : 'center',
						title : '提出部门'
					},
					{
						field : 'benefitDirectIndi',
						align : 'center',
						title : '经济效益',
						formatter: function(value, row, index) {
							var actions = [];
			            	actions.push('<a class="editable editable-click" href="javascript:void(0)" onclick="to1702Detail(\'' + row['memberGh'] +'\',\'' + row['memberName'] +'\')">'+value+'</a>');
							return actions.join('');
			            },
						footerFormatter:function (value) {
		                	var sum = 0;
					        for (var i in value) {
					        	if($.isNumeric( value[i].benefitDirectIndi )){
					        		sum += parseFloat(value[i].benefitDirectIndi);
					        	}		        	
					        }
							return sum.toFixed(2);
					    }
					}
				]
            };
            $.table.init(options);
        });
        function queryParams(params) {
			var search = $.table.queryParams(params);
			return search;
		}
        
        function to1702Detail(memberGh,memberName){
        	var year = $('#year').val();
        	var month = $('#month').val();
        	var orgCode = $('#orgCode').val();
        	var url = prefix+"/toPage/KYMMBB17_02?year="+year+"&month="+month+"&orgCode="+orgCode+"&memberGh="+memberGh;
        	var tabName;
        	if(month=='0'||month=='13'){
        		tabName = memberName+"-"+year+"年技术秘密效益统计";
        	}else{
        		tabName = memberName+"-"+year+"年"+month+"月技术秘密效益统计";
        	}
    		$.modal.openTab(tabName, url, true);
        }
    </script>
</body>
</html>