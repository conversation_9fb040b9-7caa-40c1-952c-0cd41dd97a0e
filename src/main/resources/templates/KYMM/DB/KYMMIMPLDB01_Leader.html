<!DOCTYPE html>
<html lang="zh" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('待办任务')"/>
    <th:block th:include="KYMM/mmInclude :: mmBaseJs"/>
    <style>
        .input {
            border: 1px solid #ddd;
            border-radius: 4px;
            background: transparent;
            outline: 0;
            height: 30px;
            width: 200px;
            padding-left: 5px;
        }
    </style>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
<!--        <div class="form-group"-->
<!--             th:include="include :: step(approveKind='kymmreward',currentNode=${activityCode})"></div>-->
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <input id="businessType" name="businessType" type="hidden" value="KYND"/>
                <input id="processCode" name="processCode" th:value="${processCode}" type="hidden"/>
                <input id="currentActivity" name="currentActivity" th:value="${activityCode}" type="hidden"/>
                <div class="select-list">

                    <ul>
                        <li class="col-sm-8">
                            <label style="width: 100px;">项目名称：</label>
                            <input id="businessNameLike" name="businessNameLike" type="text"/>
                        </li>
                        <li class="col-sm-6">
                            <label style="width: 100px;">申报组织：</label>
                            <th:block th:include="/component/selectOrg::init(orgCodeId='orgCodeDb', orgNameId='orgNameDb', selectType='S')" />
                        </li>

                        <li class="col-sm-6">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="KYMMRDDB01_Leader"></table>
<!--            <th:block th:include="KYMM/batchWfbuttonGroup::batchWfbuttonGroup(tabId='KYMMRDDB01_Leader',processCode=${processCode},activityCode=${activityCode})"></th:block>-->
            <br/>
            <div class="form-group">
                <label class="col-sm-2 control-label is-required">评审意见：</label>
                <div class="col-sm-8">
                      <textarea name="comment" id="comment" style="height:150px;white-space: break-spaces;"
                                class="form-control"></textarea>
                </div>
            </div>
            <br/></div>
<!--            <div class="btn-group-sm" role="group" style="text-align: center;">-->

<!--                <a class="btn btn-success" onclick="allBatchProcessing();">-->
<!--                    <i class="fa fa-upload"></i> 一键提交-->
<!--                </a>-->
<!--            </div>-->
        </div>
        <div class="toolbar toolbar-bottom" role="toolbar">
            <div class="btn-group dropup">
                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    &nbsp;批量提交
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-center">
                    <th:block th:if="${activityCode=='KYMM_ZGDEPT_ZG'}">
                        <li><a href="javascript:void(0);" th:onclick="batchSubmit('KYMM_ZGDEPT_ZG','Transition12')" th:text="提交主管部门领导"></a></li>
                        <li><a href="javascript:void(0);" th:onclick="batchSubmit('KYMM_ZGDEPT_ZG','Transition3')" th:text="发奖"></a></li>
                    </th:block>
                    <th:block th:if="${activityCode=='KYMM_ZGDEPT_LEADER'}">
                        <li><a href="javascript:void(0);" th:onclick="batchSubmit('KYMM_ZGDEPT_LEADER','Transition13')" th:text="提交公司领导审批"></a></li>
                        <li><a href="javascript:void(0);" th:onclick="batchSubmit('KYMM_ZGDEPT_LEADER','Transition14')" th:text="发奖"></a></li>
                    </th:block>
                </ul>
            </div>
            <th:block th:if="${activityCode=='KYMM_GS_FG_LEADER'}">
                <button type="button" class="btn btn-danger" onclick="batchSubmit('KYMM_GS_FG_LEADER','Transition15')"><i class="fa fa-check"></i>发奖</button>
            </th:block>
            &nbsp;
            <div class="btn-group dropup">
                <button type="button" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    &nbsp;批量退回
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu dropdown-menu-center">
                    <li><a href="javascript:void(0);" th:onclick="batchReturn('KYMM_START')" th:text="退回编写实施奖申报"></a></li>
                    <li><a href="javascript:void(0);" th:onclick="batchReturn('KYMM_DEPT_ZG')" th:text="退回主管部门管理员"></a></li>
                </ul>
            </div>
            &nbsp;
<!--            <button type="button" class="btn btn-danger" onclick="batchNo()"><i class="fa fa-reply-all"></i>批量否定</button>-->
        </div>
    </div>
</div>
<script th:inline="javascript">
    var refresh = {};
    var activityCode = [[${activityCode}]];

    var sourceType = [[${@dict.getDictList('KIZL','KI_FROM_TYPE')}]];
    var applicationWay = [[${@dict.getDictList('KYMM','applicationWay')}]];
    var reasonforUnuse = [[${@dict.getDictList('KYMM','reasonforUnuse')}]];
    var techLabel = [[${@dict.getDictList('KYMM','techLabel')}]];
    var technologyField = [[${@dict.getDictList('MPTY','YFBJ')}]];
    var techUse = [[${@dict.getDictList('KYMM','techUse')}]];
    var techClass = [[${@dict.getDictList('KYMM','techClass')}]];
    var expectedResult = [[${@dict.getDictList('KYMM','expectedResult')}]];

    $(function () {
        let options = kyxm();
        $.table.init(options);
    });

    //科研项目
    function kyxm() {
        var options = {
            url: ctx + 'KYMM/queryDB',
            uniqueId: "rowId",
            id: "KYMMRDDB01_Leader",
            pageSize: 50,
            exportUrl: ctx + 'KYXM/ND/excel/exportDB',
            importUrl: ctx + "KYXM/ND/excel/importDB",
            modalName: "技术秘密认定待办",
            queryParams: queryParams,
            rowStyle: function (row, index) {
                if ($.common.isNotEmpty(row.colorType)) {
                    if (row.colorType.indexOf(activityCode) > -1) {
                        return {css: {'background-color': '#FAFAD2'}};
                    }
                }
                return '';
            },
            columns: [{
                checkbox: true
            },
                {
                    field: 'technologyId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'technologyName',
                    title: '技术秘密名称'
                },
                {
                    field: 'confirmNum',
                    title: '认定号'
                },
                {
                    field: 'firstdeptName',
                    title: '申报部门'
                },
                {
                    field: 'applyUser',
                    title: '申请人',
                },
                {
                    field: 'applyTypeName',
                    title: '申报奖项',
                },
                {
                    field: 'lastOperatorName',
                    title: '上一步提交人',
                },
                {
                    field: 'lastTime',
                    title: '提交日期',
                    formatter: function (value, row, index) {
                        var timeData = value.replace(/(\d{4})(\d{2})(\d{2})(\d{2})(\d{2})(\d{2})/g, '$1-$2-$3 $4:$5:$6');
                        return timeData;
                    }
                },
                {
                    field: 'psStatus',
                    title: '评审状态',
                    formatter: function (value, row, index) {

                        var reviewStatus;
                        switch (value){
                            case "Not_Review" :
                                reviewStatus = "未评审";
                                break;
                            case "In_Review" :
                                reviewStatus = "评审中";
                                break;
                            case "Complete_Review" :
                                reviewStatus = "已评审";
                                break;
                            default:
                                reviewStatus = "";
                                $.table.hideColumn('psStatus');
                                $.table.refresh();
                        }
                        return reviewStatus;
                    }
                },
                {
                    field: 'award',
                    title: '奖励金额(元)',
                    formatter: function (value, row, index) {
                        if (!row.showAward) {
                            $.table.hideColumn('award');
                            $.table.refresh();
                        }
                        return value;
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs " href="javascript:void(0)" onclick="doHandle(\'' + row.processCode + '\',\'' + row.currentActivity + '\',\'' + row.pageNo + '\',\'' + row.businessId + '\',\'' + row.flowId + '\',\'' + row.taskId + '\',\'' + row.currentActivityName + '\')"><i class="fa fa-edit"></i> 处理</a>');
                        return actions.join('');
                    }
                }]
        };

        return options;
    }

    function queryParams(params) {
        var search = $.table.queryParams(params);
        search.businessNameLike = $("#businessNameLike").val();
        search.projectType = $("#projectType").val();
        search.businessType = 'KYMM';
        search.processCode = [[${processCode}]];
        search.currentActivity = [[${activityCode}]];
        return search;
    }

    //处理
    function doHandle(processCode, activityCode, pageNo, businessGuid, processInstanceId, taskId, currentActivityName) {
        var url = ctx + "KYMM/dbDetail/" + businessGuid + "/" + pageNo + "/" + processInstanceId + "/" + taskId + "/" + processCode + "/" + activityCode;
        $.modal.openTab(currentActivityName, url, true);
    }

    //批量提交
    function batchSubmit(activityCode,transitionKey){
        if ($.modal.confirm("确认要提交吗?", function () {
            var indexAll = $('#KYMMRDDB01_Leader').bootstrapTable('getData');
            var index = $('#KYMMRDDB01_Leader').bootstrapTable('getSelections');
            if (index.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            if($("#comment").val()==''){
                $.modal.alertWarning("请填写评审意见");
                return;
            }
            console.info("-->")
            console.info(indexAll);
            console.info(index);
            var arrys = [];
            for (let i = 0; i < index.length; i++) {
                var data = {};
                data["businessId"] = index[i].businessId;
                data["processCode"] = index[i].processCode;
                data["processInstanceId"] = index[i].processInstanceId;
                data["taskId"] = index[i].taskId;
                data["currentActivity"] = activityCode;
                data["activityCode"] = activityCode;
                data["transitionKey"] = transitionKey;
                data["comment"] = $("#comment").val();
                arrys[i] = data;
            }

            var config = {
                url: ctx + "kymm/common/batchSubmit",
                type: "post",
                dataType: "json",
                data: JSON.stringify(arrys),
                contentType: 'application/json;charset=UTF-8',
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    var str = result.msg;
                    if(str.indexOf("请联系系统管理员")!= -1){
                        $.modal.alertError(result.msg);
                        $.modal.closeLoading();
                    }else{
                        $.modal.alertSuccess(result.msg);
                        $.modal.closeLoading();
                        $.table.search();
                        $("#comment").val("");
                    }
                }
            };
            $.ajax(config)
        })) ;
    }

    //批量退回
    function batchReturn(activityCode,transitionKey){
        if ($.modal.confirm("确认要退回吗?", function () {
            var indexAll = $('#KYMMRDDB01_Leader').bootstrapTable('getData');
            var index = $('#KYMMRDDB01_Leader').bootstrapTable('getSelections');
            if (index.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            if($("#comment").val()==''){
                $.modal.alertWarning("请填写评审意见");
                return;
            }
            console.info("-->")
            console.info(indexAll);
            console.info(index);
            var arrys = [];
            for (let i = 0; i < index.length; i++) {
                var data = {};
                data["businessId"] = index[i].businessId;
                data["processCode"] = index[i].processCode;
                data["processInstanceId"] = index[i].processInstanceId;
                data["taskId"] = index[i].taskId;
                data["returnActivityKey"] = activityCode;
                data["comment"] = $("#comment").val();
                arrys[i] = data;
            }

            var config = {
                url: ctx + "kymm/common/doBatchReturn",
                type: "post",
                dataType: "json",
                data: JSON.stringify(arrys),
                contentType: 'application/json;charset=UTF-8',
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    var str = result.msg;
                    $.modal.alertSuccess(result.msg);
                    $.modal.closeLoading();
                    $.table.search();
                    $("#comment").val("");
                    // if(str.indexOf("异常:")!= -1){
                    //     $.modal.alertError(result.msg);
                    //     $.modal.closeLoading();
                    // }else{
                    //     $.modal.alertSuccess(result.msg);
                    //     $.modal.closeLoading();
                    //     $.table.search();
                    // }
                }
            };
            $.ajax(config)
        })) ;
    }

    //批量否定
    function batchNo(){
        if ($.modal.confirm("确认要否定吗?", function () {
            var indexAll = $('#KYMMRDDB01_Leader').bootstrapTable('getData');
            var index = $('#KYMMRDDB01_Leader').bootstrapTable('getSelections');
            if (index.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            var arrys = [];
            for (let i = 0; i < index.length; i++) {
                var data = {};
                data["businessId"] = index[i].businessId;
                data["processCode"] = index[i].processCode;
                data["processInstanceId"] = index[i].processInstanceId;
                data["taskId"] = index[i].taskId;
                data["comment"] = $("#comment").val();
                arrys[i] = data;
            }

            var config = {
                url: ctx + "kymm/common/batchNo",
                type: "post",
                dataType: "json",
                data: JSON.stringify(arrys),
                contentType: 'application/json;charset=UTF-8',
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    var str = result.msg;
                    $.modal.alertSuccess(result.msg);
                    $.modal.closeLoading();
                    $.table.search();
                }
            };
            $.ajax(config)
        })) ;
    }

    function reset() {
        $("#orgCodeDb").val("");
        $.form.reset();
    }
</script>
</body>
</html>