<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改实施情况')"/>
    <th:block th:include="KYMM/mmInclude :: mmBaseJs"/>
    <th:block th:include="KYMM/mmInclude :: commonJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: sendFile"/>
    <th:block th:include="include :: summernote-js"/>
</head>
<body class="white-bg">

<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="from01" th:object="${result.tkymmTechnologyEx}">
        <!--框-->
        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <input id="technologyId" name="technologyId" th:value="*{technologyId}" type="hidden">
                                    <label class="col-sm-3 control-label">认定号：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="*{confirmNum}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">技术秘密名称：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="*{technologyName}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--框end-->
        <!--框-->
        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#ssqk" aria-expanded="false" class="collapsed">
                        实施情况
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div id="ssqk" class="panel-collapse collapse in">
                    <div class="panel-body">
                        <div class="row ">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">应用方式：</label>
                                <div class="col-sm-10"
                                     th:include="/component/radio :: init(id='applicationWay', name='applicationWay',businessType='KYMM',value=*{applicationWay},dictCode='applicationWay',callback='radioCallbackApplicationWay' ,isrequired=true)"></div>
                            </div>
                        </div>

                        <div class="row" id="applyDept">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">应用部门：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="/component/selectOrg::init(orgCodeId='appDepartment', orgNameId='appDepartmentName', selectType='M',value=*{appDepartment})" />
                                </div>
                            </div>
                        </div>


                        <div class="row availables">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">初始应用日：</label>
                                <div class="col-sm-10"
                                     th:include="/component/date :: init(id='useDate',name='useDate',strValue=*{useDate})"></div>
                            </div>
                        </div>

                        <div class="row notAvailable">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">未应用原因：</label>
                                <div class="col-sm-10"
                                     th:include="/component/radio :: init(id='reasonforUnuse',value=*{reasonforUnuse}, name='reasonforUnuse',businessType='KYMM', dictCode='reasonforUnuse')"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--框end-->
    </form>
</div>
<div class="toolbar toolbar-bottom" role="toolbar">
    <button type="button" class="btn btn-primary" onclick="saveHandler()"><i class="fa fa-save"></i>保 存</button>
    <button type="button" class="btn btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>返 回</button>
</div>
<br/><br/>

<script th:inline="javascript">
    var prefix = ctx + "kymm/technology";

    $(document).ready(function () {
        //根据应用方式来控制元素的隐藏与显示
        radioCallbackApplicationWay([[${result.tkymmTechnologyEx.applicationWay}]]);
    });
    function radioCallbackApplicationWay(a) {
        //是否应用通过应用方式控制元素的显示与隐藏
        //未应用
        if (a == "Not_applied") {
            $(".availables").hide();
            $(".notAvailable").show();
            $("#applyDept").hide();
        } else if (a == "Internal_implementation_of_the_unit") {
            $(".notAvailable").hide();
            $(".availables").show();
            $("#applyDept").show();
        }
    }

    //保存
    function saveHandler() {
        if ($.validate.form()) {
            $.modal.confirm("确认修改吗？", function () {
                var config = {
                    url: prefix + "/saveSsqk",
                    type: "post",
                    dataType: "json",
                    data: $('#from01').serialize(),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        console.info(result);
                        if (result.code == 500) {
                            $.modal.alertError(result.msg);
                            $.modal.closeLoading();
                        } else {
                            $.operate.alertSuccessTabCallback(result);
                        }
                    }
                };
                $.ajax(config)
            });
        }
    }
</script>
</body>
</html>