<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增技术秘密认定')"/>
    <th:block th:include="KYMM/mmInclude :: mmBaseJs"/>
    <th:block th:include="KYMM/mmInclude :: commonJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: sendFile"/>
    <th:block th:include="include :: summernote-js"/>
<!--    <th:block th:include="include :: baseJs"/>-->
    <th:block th:include="KYMM/mmInclude :: selectTechAreaKYMM" />
    <th:block th:include="KYMM/mmInclude :: selectTechClassKYMM" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
<!--    <div class="form-group"-->
<!--         th:include="include :: step(approveKind='kymmapply',currentNode='TECHNICAL_SECRETS')"></div>-->
    <form class="form-horizontal m" id="form-technology-add" th:object="${result.tkymmTechnologyEx}">

        <div aria-multiselectable="true" class="panel-group" id="logo" role="tablist">
            <span style="font-weight:bold;border: solid;padding: 10px;border-color: #00a0e9;">普通商密  解密前</span>
        </div>
        <!--第一块-->
        <div aria-multiselectable="true" class="panel-group" id="accordion" role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version"
                           data-toggle="collapse" href="#1">基本信息<span class="pull-right"><i
                                aria-hidden="true" class="fa fa-chevron-down"></i></span></a></h5>
                </div>
                <input id="technologyId" name="technologyId" th:value="*{technologyId}" type="hidden">
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="form-group col-sm-12">
                                <div class="col-sm-12">
                                    <p>
                                        关于《签订保密协议》的说明：<br>
                                        为了将技术秘密保密工作从源头抓起，技术秘密管理系统新增了保密措施，即对申报材料进行初步审查的管理员、评审专家如果未与公司签订《保密协议》，应及时补签；在进行技术秘密申报时，提出人必须明确告知其是否已签订过《保密协议》，如没有签订过，提出人将申报材料提交给管理员，由管理员将所申报的技术秘密内容请有关评审专家评审，评审后认定为技术秘密的，应向部门有关领导进行说明，与该提出人签订《保密协议》，如评审专家认为所申报内容属于公知公用的现有技术，可以不与提出人签订《保密协议》。
                                    </p>
                                </div>
                            </div>
                        </div>


                        <div class="row ">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">技术秘密名称：</label>
                                <div class="col-sm-8">
                                    <input class="form-control" name="technologyName" required
                                           th:value="*{technologyName}" type="text">
                                </div>
<!--                                <div class="col-sm-2">-->
<!--                                    <a href="javascript:void(0);" th:onclick="openNewTab('技术秘密申请信息','kymm/technology/retrieve')" th:style="'color:blue'">检索</a>-->
<!--                                </div>-->
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">类型：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="/component/radio :: init(id='secretLevel', name='secretLevel',businessType='KYMM', value=*{secretLevel},dictCode='secretLevel' ,isrequired=true,callback='secretCallback')"></th:block>
                                    <br><label id="secretLabel" style="color: red;">提示：泄露会使公司的经济利益遭受严重损害的确定为核心技术秘密，一般指在国际上首发的产品制造技术、在国际上具有领先优势的技术。提出人不得将比较核心的技术方案写在核心技术秘密的摘要中。认定为核心技术秘密则全文将被隐藏。</label>
                                </div>
                            </div>
                        </div>


                        <div class="row col-sm-12">
                            <div class="form-group col-sm-6">
                                <label class="col-sm-4 control-label ">认定号：</label>
                                <div class="col-sm-8">
                                </div>
                            </div>
                            <div class="form-group col-sm-6">
                                <label class="col-sm-4 control-label ">认定日期：</label>
                                <div class="col-sm-8">
                                </div>
                            </div>
                        </div>


                        <div class="row col-sm-12">
                            <div class="form-group col-sm-6">
                                <label class="col-sm-4 control-label is-required">联系人：</label>
                                <div class="col-sm-8">
                                    <label class="control-label"> [[*{contactPersonName}]]</label>
                                    <input class="form-control" name="contactPersonName" required
                                           th:value="*{contactPersonName}" type="hidden">
                                    <input class="form-control" name="contactPersonGh" required
                                           th:value="*{contactPersonGh}" type="hidden">
                                </div>
                            </div>
                            <div class="form-group col-sm-6">
                                <label class="col-sm-4 control-label is-required">联系人Email：</label>
                                <div class="col-sm-8">
                                    <input class="form-control" id="contactpersonEmail" name="contactpersonEmail"
                                           th:value="*{contactpersonEmail}" type="text" required>
                                </div>
                            </div>
                        </div>


                        <div class="row col-sm-12">
                            <div class="form-group col-sm-6">
                                <label class="col-sm-4 control-label is-required">联系人手机：</label>
                                <div class="col-sm-8">
                                    <input class="form-control" name="contactpersonPh" th:value="*{contactpersonPh}"
                                           type="text" required>
                                </div>
                            </div>
                            <div class="form-group col-sm-6">
                                <label class="col-sm-4 control-label is-required">联系人电话：</label>
                                <div class="col-sm-8">
                                    <input class="form-control" name="contactpersonTel" th:value="*{contactpersonTel}"
                                           type="text" required>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">第一申报单位：</label>
<!--                                <div class="form-group col-sm-10">-->
                                <div class="col-sm-10">
                                    <!--  <th:block-->
                                    <!--       th:include="include :: choiceOrg(orgCodeId='firstdeptCode',orgCode='BGTA',labelName='第一申报单位：',labelClass='col-sm-2 control-label is-required' ,orgNameId='firstdeptName',value=*{firstdeptCode} ,selectType='S')"></th:block>-->
                                    <th:block th:include="/component/selectOrg::init(orgCodeId='firstdeptCode',orgNameId='firstdeptName' ,value=*{firstdeptCode} ,selectType='S')" />
                                </div>
                            </div>
                        </div>
                        <div class="row col-sm-12">
                            <div class="form-group col-sm-6">
                                <label class="col-sm-4 control-label is-required">权属：</label>
                                <div class="col-sm-8">
                                    <label class="control-label" id="ownershipNumLabel" th:utext="*{ownershipNum}"></label>
                                    <input class="form-control" id="ownershipNum" name="ownershipNum"   th:value="*{ownershipNum}" type="hidden">
                                </div>
                            </div>
                            <div class="form-group col-sm-6">
                                <label class="col-sm-4 control-label">提出日期：</label>
                                <div class="col-sm-8">
                                    <label class="control-label"> [[*{applyDate}]]</label>
                                    <input class="form-control" id="applyDate" name="applyDate" th:value="*{applyDate}"
                                           type="hidden">
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">来源：</label>
                                <div class="col-sm-10"
                                     th:include="/component/radio :: init(id='sourceType', name='sourceType',businessType='KIZL', dictCode='KI_FROM_TYPE',multimultiple=false ,value=*{sourceType},callback='radioCallback')"></div>
                                <input class="form-control" id="sourceTypeValue" name="sourceTypeValue" required
                                       th:value="*{sourceType}" type="hidden">
                            </div>
                        </div>

                        <div class="row source">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">来源编号：</label>
                                <div class="col-sm-8">
                                    <input class="form-control" id="sourceNum" name="sourceNum" th:value="*{sourceNum}"
                                           type="text" onchange="getSource()">
                                </div>
                                <div class="col-sm-2">
                                    <button class="btn btn-primary" onclick="getSource()" type="button">获取来源信息
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="row source">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label ">来源名称：</label>
                                <div class="col-sm-8">
<!--                                    <label class="control-label" id="sourceNameLabel">[[*{sourceName}]]</label>-->
<!--                                    <input class="form-control" id="sourceName" name="sourceName" th:value="*{sourceName}"-->
<!--                                           type="hidden">-->
                                    <input class="form-control" id="sourceName" name="sourceName" th:value="*{sourceName}"/>
                                </div>
                            </div>
                        </div>

                        <div class="row source">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label ">同来源专利：</label>
<!--                                <div class="col-sm-10">-->
<!--                                    <label  style="white-space:normal"-->
<!--                                           id="samesourcePatentLabel">[[*{samesourcePatent}]]</label>-->
<!--                                    <input class="form-control" id="samesourcePatent" name="samesourcePatent" th:value="*{samesourcePatent}"-->
<!--                                           type="hidden">-->
<!--                                </div>-->
                                <br/><br/>
                                <div class="col-sm-12" style="margin-left:17%;width:60%">
                                    <table class="table table-bordered table-hover table-striped">
                                        <thead>
                                        <tr>
                                            <th style="text-align:center">专利名称</th>
                                            <th style="text-align:center">最新状态</th>
                                            <th style="text-align:center">接收编号</th>
                                            <th style="text-align:center">操作</th>
                                        </tr>
                                        </thead>
                                        <tbody id="tllzl">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div class="row source">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label ">同来源技术秘密：</label>
<!--                                <div class="col-sm-10">-->
<!--                                    <label class="control-label" style="white-space: break-spaces;" id="samesourceTechLabel">[[*{samesourceTech}]]</label>-->
<!--                                    <input class="form-control" id="samesourceTech" name="samesourceTech" th:value="*{samesourceTech}"-->
<!--                                           type="hidden">-->
<!--                                </div>-->
                                <br/><br/>
                                <div class="col-sm-12" style="margin-left:17%;width:60%">
                                    <table class="table table-bordered table-hover table-striped">
                                        <thead>
                                        <tr>
                                            <th style="text-align:center">技术秘密名称</th>
                                            <th style="text-align:center">最新状态</th>
                                            <th style="text-align:center">认定号</th>
                                            <th style="text-align:center">操作</th>
                                        </tr>
                                        </thead>
                                        <tbody id="tllmm">
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>


                        <div class="row" id="otherSource">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">其他来源情况说明：</label>
                                <div class="col-sm-10">
                                    <input class="form-control" name="ohterSource" th:value="*{ohterSource}"
                                           type="text">
                                </div>
                            </div>
                        </div>


                        <div class="row ">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">应用方式：</label>
                                <div class="col-sm-10"
                                     th:include="/component/radio :: init(id='applicationWay', name='applicationWay',businessType='KYMM',value=*{applicationWay},dictCode='applicationWay',callback='radioCallbackApplicationWay' ,isrequired=true)"></div>
                            </div>
                        </div>

                        <div class="row" id="applyDept">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">应用部门：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="/component/selectOrg::init(orgCodeId='appDepartment', orgNameId='appDepartmentName', selectType='M',value=*{appDepartment})" />
                                </div>
                            </div>
                        </div>


                        <div class="row availables">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">初始应用日：</label>
                                <div class="col-sm-10"
                                     th:include="/component/date :: init(id='useDate',name='useDate',strValue=*{useDate})"></div>
                            </div>
                        </div>

                        <div class="row notAvailable">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">未应用原因：</label>
                                <div class="col-sm-10"
                                     th:include="/component/radio :: init(id='reasonforUnuse',value=*{reasonforUnuse}, name='reasonforUnuse',businessType='KYMM', dictCode='reasonforUnuse')"></div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">研发布局：</label>
                                <div class="col-sm-8">
                                    <div th:include="component/select:: init(id='technologyField',name='technologyField',dictCode='YFBJ',businessType='MPTY',
                        										isrequired=true,isfirst='true',value=*{technologyField})"></div>
                                </div>
                            </div>
                        </div>
<!--                        <div class="row">-->
<!--                            <div class="form-group col-sm-12">-->
<!--                                <label class="col-sm-2 control-label is-required">标签：</label>-->
<!--                                <div class="col-sm-10"-->
<!--                                     th:include="/component/checkbox :: init(id='techLabel', name='techLabel',businessType='KYMM',value=*{techLabel},  dictCode='techLabel',multimultiple=false,isrequired=true)">-->
<!--                                </div>-->

<!--                            </div>-->
<!--                        </div>-->

<!--                        <div class="row">-->
<!--                            <div class="form-group col-sm-12">-->
<!--                                <label class="col-sm-2 control-label is-required">技术领域：</label>-->
<!--                                <div class="col-sm-8">-->
<!--                                    <div  th:include="component/select:: init(id='jsly',name='technologyField',dictCode='jsly',businessType='KYND',-->
<!--										isrequired='true',isfirst='true',value=*{technologyField})"></div>-->
<!--                                </div>-->
<!--                                <script th:inline="javascript">-->
<!--                                    $(function(){-->
<!--                                        $("#jsly").change(function(){-->
<!--                                            var dictCode = $("#jsly").val();-->
<!--                                            //-->
<!--                                            initJslyxl();-->
<!--                                        });-->
<!--                                    });-->
<!--                                </script>-->
<!--                            </div>-->
<!--                        </div>-->


<!--                        <div class="row">-->
<!--                            <div class="form-group col-sm-12">-->
<!--                                <label class="col-sm-2 control-label">技术领域细类：</label>-->
<!--                                <div class="col-sm-8">-->
<!--                                    <input id="jslyxlName" name="jslyxlName" type="hidden">-->
<!--                                    <select id="jslyxl" name="extra4" class="form-control" onchange="setTextVal()">-->
<!--                                        <option value="">请选择</option>-->
<!--                                    </select>-->
<!--                                    <script th:inline="javascript">-->
<!--                                        var jslyxl = [[${result.tkymmTechnologyEx.extra4}]];-->

<!--                                        $(document).ready(function () {-->
<!--                                            initJslyxl();-->
<!--                                        });-->

<!--                                        function initJslyxl() {-->
<!--                                            var dictCode = $("#jsly").val();-->
<!--                                            if (dictCode) {-->
<!--                                                var url = prefix + "getDict/dictCode/" + dictCode;-->
<!--                                                var config = {-->
<!--                                                    url: url,-->
<!--                                                    type: "get",-->
<!--                                                    dataType: "json",-->
<!--                                                    success: function (result) {-->
<!--                                                        // debugger;-->
<!--                                                        if (result.code == 0) {-->
<!--                                                            var dictList = result.data;-->
<!--                                                            $("#jslyxl").empty();-->
<!--                                                            $("#jslyxlName").val('');-->
<!--                                                            var intitOpt = "";-->
<!--                                                            intitOpt = intitOpt + "<option value=''>请选择</option>";-->
<!--                                                            if (dictList) {-->
<!--                                                                for (var el of dictList) {-->
<!--                                                                    if (jslyxl == el.dictValue) {-->
<!--                                                                        intitOpt = intitOpt + "<option value='" + el.dictValue + "' data='" + el.dictName + "' selected>" + el.dictName + "</option>";-->
<!--                                                                        $("#jslyxlName").val(el.dictName);-->
<!--                                                                    } else {-->
<!--                                                                        intitOpt = intitOpt + "<option value='" + el.dictValue + "' data='" + el.dictName + "' >" + el.dictName + "</option>";-->
<!--                                                                    }-->
<!--                                                                }-->
<!--                                                            }-->
<!--                                                            $("#jslyxl").append(intitOpt);-->
<!--                                                        } else {-->
<!--                                                            $("#jslyxl").empty();-->
<!--                                                            $("#jslyxlName").val('');-->
<!--                                                            var intitOpt = "";-->
<!--                                                            intitOpt = intitOpt + "<option value=''>请选择</option>";-->
<!--                                                            $("#jslyxl").append(intitOpt);-->
<!--                                                            $.modal.alertError(result.msg);-->
<!--                                                        }-->
<!--                                                    }-->
<!--                                                };-->
<!--                                                $.ajax(config)-->
<!--                                            }-->
<!--                                        }-->

<!--                                        function setTextVal() {-->
<!--                                            $("#jslyxlName").val($("#jslyxl option:selected").text());-->
<!--                                        }-->
<!--                                    </script>-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->



                        <div class="row" id="usePurpose">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">用途：</label>
                                <input type="hidden" id="techUse" name="techUse"/>
                                <div class="col-sm-8">
                                    <input name="extra2" id="extra2" class="form-control" th:value="*{extra2}" readonly="true"/>
                                </div>
                                <span class="btn btn-sm btn-primary" type="button" onclick="choiceTechAreaKYMM('techUse','extra2','M','YT', null)">选择</span>
                            </div>
                        </div>


                        <div class="row">
                            <!--<div class="form-group col-sm-12">-->
                                <!--<label class="col-sm-2 control-label is-required">技术知识分类：</label>-->
<!--&lt;!&ndash;                                <div class="col-sm-4"&ndash;&gt;-->
<!--&lt;!&ndash;                                     th:include="/component/checkbox :: init(id='techClass', name='techClass',businessType='KYMM',value=*{techClass},  dictCode='techClass',multimultiple=false,isrequired=true)">&ndash;&gt;-->
<!--&lt;!&ndash;                                </div>&ndash;&gt;-->
<!--&lt;!&ndash;                                <div class="col-sm-6 pull-left">&ndash;&gt;-->
<!--&lt;!&ndash;                                	<label for="techClass" class="error" style="position: static;"></label>&ndash;&gt;-->
<!--&lt;!&ndash;                                </div>&ndash;&gt;-->
                                <!--<input type="hidden" id="techClass" name="techClass"/>-->
                                <!--<div class="col-sm-8">-->
                                    <!--<input name="extra3" id="extra3" class="form-control" th:value="*{extra3}" readonly="true"/>-->
                                <!--</div>-->
                                <!--<span class="btn btn-sm btn-primary" type="button" onclick="choiceTechClassKYMM('techClass','extra3','M', null);">选择</span>-->
                            <!--</div>-->

<!--                            <div class="form-group" style="margin-right:31%">-->
<!--                                <th:block th:include="include :: initSelectBox(id='techClass', name='techClass',isrequired=true,businessType='KYND',isfirst='true',-->
<!--                                lableClass='col-sm-3 control-label', dictCode='jsfl',labelName='技术分类：',value=*{techClass})"></th:block>-->
<!--                            </div>-->
                        </div>


                        <div class="row">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">预计效果：</label>
                                <div class="col-sm-8" th:include="/component/checkbox :: init(id='expectedResult', name='expectedResult',businessType='KYMM',value=*{expectedResult}, dictCode='expectedResult',isrequired=true )"></div>
                                <div class="col-sm-2 pull-left">
                                	<label for="expectedResult" class="error" style="position: static;"></label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <!--第二块-->
        <div aria-multiselectable="true" class="panel-group" id="accordion" role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version"
                           data-toggle="collapse" href="#2">技术秘密权利人单位<span class="pull-right"><i
                                aria-hidden="true" class="fa fa-chevron-down"></i></span></a></h5>
                </div>

                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                    <div class="panel-body">
                        <table class="table table-bordered table-hover table-striped" id="myjstable">
                            <input id="qlrNum" name="qlrNum" type="hidden" th:value="${result.tkymmTechnologyEx.qlrNum}"/>
                            <thead>
                            <tr>
<!--                                <th style="text-align: center;width: 200px;">-->
<!--                                    &nbsp;&nbsp;<a href="javascript:void(0)" onclick="insertColumns()"><img-->
<!--                                        th:src="@{/img/add.png}"></a>-->
<!--                                    &nbsp;&nbsp;<a href="javascript:void(0)" onclick="delDeptAll()"><img-->
<!--                                        th:src="@{/img/del.png}"></a>-->
<!--                                </th>-->
                                <th class="" style="width: 90px; " data-field="0" tabindex="0">
                                    <div class="th-inner ">
                                        <button class="btn btn-success btn-circle" type="button" onclick="insertColumns()"><i class="fa fa-plus"></i> </button>
                                        <button class="btn btn-primary btn-circle" type="button" onclick="delDeptAll()"><i class="fa fa-minus"></i> </button>
                                    </div>
                                </th>
                                <th style="text-align: center;">单位类型</th>
                                <th style="text-align: center;">单位名称</th>
                                <th style="text-align: center;">操作</th>
                            </tr>
                            </thead>
                            <tbody id="tabletBodys">

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div aria-multiselectable="true" class="panel-group" id="accordion" role="tablist">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h5 class="panel-title">
                                <a aria-expanded="false" class="collapsed" data-parent="#version"
                                   data-toggle="collapse" id="TechnicalSecretObligeeUnit"
                                   name="TechnicalSecretObligeeUnit" href="#4">提出人信息<span class="pull-right"><i
                                        aria-hidden="true" class="fa fa-chevron-down"></i></span></a></h5>
                        </div>
                        <!--折叠区域-->
                        <div aria-expanded="false" class="panel-collapse collapse in" id="4">
                            <div class="panel-body">
                                <table class="table table-bordered table-hover table-striped" id="mytable">
                                    <input id="fmrNum" name="fmrNum" type="hidden" th:value="${result.tkymmTechnologyEx.fmrNum}"/>
                                    <thead>
                                    <input type="hidden" name="userCodeLabels" id="userCodeLabels" />
                                    <input type="hidden" name="userNameLabels" id="userNameLabels" />
                                    <tr>
<!--                                        <th style="text-align: center;width: 200px;">-->
<!--                                            &nbsp;&nbsp;<a href="javascript:void(0)" onclick="addBatchColumns(5)"><img-->
<!--                                                th:src="@{/img/add.png}"></a>-->
<!--                                            &nbsp;&nbsp;<a href="javascript:void(0)" onclick="addColumns()"><img-->
<!--                                                th:src="@{/img/add.png}"></a>-->
<!--                                            &nbsp;&nbsp;<a href="javascript:void(0)" onclick="delAll()"><img-->
<!--                                                th:src="@{/img/del.png}"></a>-->

<!--                                        </th>-->
                                        <th class="" style="width: 90px; " data-field="0" tabindex="0">
                                            <div class="th-inner ">
<!--                                                <button class="btn btn-success btn-circle" type="button" onclick="addBatchColumns()"><i class="fa fa-plus"></i> </button>-->
                                                <button class="btn btn-success btn-circle" type="button" onclick="addInventor()"><i class="fa fa-plus"></i> </button>
                                                <button class="btn btn-primary btn-circle" type="button" onclick="delAll()"><i class="fa fa-minus"></i> </button>
                                            </div>
                                        </th>
                                        <th style="text-align: center;">姓名</th>
                                        <th style="text-align: center;">工号</th>
                                        <th style="text-align: center;">单位</th>
                                        <th style="text-align: center;" >贡献系数(%)</th>
                                        <th style="text-align: center;">职称</th>
                                        <th style="text-align: center;">岗位</th>
                                        <th style="text-align: center;">备注</th>
                                        <th style="text-align: center;">操作</th>
                                    </tr>
                                    </thead>
                                    <tbody id="tabletBody">

                                    </tbody>
                                </table>

                            </div>
                        </div>
                    </div>
                </div>

        <div aria-multiselectable="true" class="panel-group" id="accordion" role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version"
                           data-toggle="collapse" id="attAndSummay"
                           name="attAndSummay" href="#5">技术秘密摘要及附件<span class="pull-right"><i
                                aria-hidden="true" class="fa fa-chevron-down"></i></span></a></h5>
                </div>
                <div aria-expanded="false" class="panel-collapse collapse in" id="5">
                    <div class="panel-body">

                        <div class="row">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">摘要：</label>
                                <div class="col-sm-10">
                                    <textarea name="summary"  class="form-control" required
                                              style="width: 100%; height: 150px;" th:utext="*{summary}"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">相关附件：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="/component/attachment::init(display='none' ,sourceId=*{technologyId},sourceModule='KYMM',sourceLabel1='SBFILE',id='sbFile',name='sbFile',labelClass='control-label',isrequired=true)"></th:block>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label">知识产权协议附件：</label>
                                <div class="col-sm-10">
                                    <th:block th:include="/component/attachment::init(display='none' ,sourceId=*{technologyId},sourceModule='KYMM',sourceLabel1='Property_Rights',id='Property_Rights',name='propertyRights',labelClass='control-label')"></th:block>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-group col-sm-12">
                                <label class="col-sm-2 control-label is-required">声明：</label>
                                <div class="col-sm-10">
                                    <th:block
                                            th:include="/component/checkbox :: init(id='declare', name='declare',businessType='KYMM', dictCode='declare',value=*{declare},isrequired=true )"></th:block><br>
                                    <span style="color: red;">提示：其他提出人已确认第一提出人填写的申报内容及贡献系数分配方案</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button class="btn btn-primary" onclick="saveAxios()" type="button">
            <i class="fa fa-check"></i>暂存
        </button>
        &nbsp;
        <button class="btn btn-primary" onclick="startProcess()" type="button">
            <i class="fa fa-check"></i>提交
        </button>

        <button class="btn btn-primary" onclick="delAxios()"
                th:if="${not #strings.isEmpty(result.tkymmTechnologyEx.technologyId)}" type="button">
            <i class="fa fa-check"></i>删除
        </button>

        <button class="btn btn-danger" onclick="closeItem()"
                type="button">
            <i class="fa fa-reply-all"></i>返回
        </button>
    </div>
</div>

<script th:inline="javascript">
    var tub = [[@{/img/del.png
    }]]
    ;

    var tubs = [[@{/img/del.png
    }]]
    ;
    var prefix = ctx + "kymm/technology/";

    //添加提出人信息
    function addColumns() {
        var num = parseInt($("#fmrNum").val());

        let len = $("#tabletBody").find("tr").length;
        var length = len + 1;
        var tr = "<tr data-index='" + len + "'>";
        tr = tr + "<td class='bs-checkbox' style='width: 100px;text-align: center; '><input name='members[" + num + "].displayOrder' type='hidden' value='" + length + "'>" + length + "</td>";
        tr = tr + "<td style='text-align: center;'><input type='hidden'  class='form-control' id='members-" + num + "-memberType'  name='members[" + num + "].memberType' ><input type='text' required class='form-control' id='members-" + num + "-memberName'  readonly onclick='chooer(" + num + ")' name='members[" + num + "].memberName' ></td>";
        tr = tr + "<td style='text-align: center;'><input type='text' class='form-control' id='members-" + num + "-memberGh' readonly  onclick='chooer(" + num + ")'  name='members[" + num + "].memberGh' ></td>";
        tr = tr + "<td style='text-align: center;'><input type='text' required class='form-control' name='members[" + num + "].deptName' readonly id='members-" + num + "-deptName'  onclick='chooer(" + num + ")'><input type='hidden' class='form-control' id='members-" + num + "-deptCode' name='members[" + num + "].deptCode' ></td>";
        tr = tr + "<td style='text-align: center;'><input type='text' required class='form-control' name='members[" + num + "].contribution' readonly oninput='calculationContribution();' ></td>";
        tr = tr + "<td style='text-align: center;'><input type='text' required class='form-control' id='members-" + num + "-postTitel' readonly name='members[" + num + "].postTitel'  ></td>";
        tr = tr + "<td style='text-align: center;'><input type='text' required class='form-control' id='members-" + num + "-postion' readonly name='members[" + num + "].postion' ></td>";
        tr = tr + "<td style='text-align: center;'><input type='text'  class='form-control' name='members[" + num + "].mark' ></td>";
        // tr = tr + "<td style='text-align: center;'> <a href='javascript:void(0)' onclick='del(this)'><img src='" + tub + "'></a></td>";
        // tr = tr + "<td style='text-align: center; width: 50px;'><button class='btn btn-primary btn-circle btn-sm' type='button' onclick='del(this)'><i class='fa fa-minus'></i></button></td>";
        tr = tr + "<td style='text-align: center; width: 50px;'><button class='btn btn-success btn-xs' type='button' onclick='editInventor("+num+")'><i class='fa fa-edit'></i>修改</button><button class='btn btn-danger btn-xs' type='button' onclick='del(this)'><i class='fa fa-remove'></i>删除</button></td>";
        tr = tr + "</tr>";
        $("#tabletBody").append(tr);
    }

    //批量添加提出人信息
    function addBatchColumns() {
        // for (var i = 1; i <= n; i++) {
        //     addColumns();
        // }
        choiceUserMM(null, null, "M", null, null);
    }

    function nullTonString(va){
        if($.common.isEmpty(va)){
            return '';
        }
        return va;
    }

    //添加提出人信息
    function readyAddColumns(option) {
        let len = $("#tabletBody").find("tr").length;
        var contribution = option.contribution;
        if (contribution == null) {
            contribution = '';
        }
        var length = len + 1;
        var tr = "<tr data-index='" + len + "'>";
        tr = tr + "<td class='bs-checkbox' style='width: 100px;text-align: center; '><input name='members[" + len + "].displayOrder' type='hidden' value='" + length + "'>" + length + "</td>";
        tr = tr + "<td style='text-align: center;'><input type='hidden'  class='form-control' id='members-" + len + "-memberType'  name='members[" + len + "].memberType' ><input type='text' required class='form-control' id='members-" + len + "-memberName'  readonly onclick='chooer(" + len + ")' name='members[" + len + "].memberName' value='" + nullTonString(option.memberName) + "' ></td>";
        tr = tr + "<td style='text-align: center;'><input type='text' class='form-control' id='members-" + len + "-memberGh'  readonly onclick='chooer(" + len + ")'  name='members[" + len + "].memberGh' value='" + nullTonString(option.memberGh) + "' ></td>";
        tr = tr + "<td style='text-align: center;'><input type='text' required class='form-control' name='members[" + len + "].deptName'  readonly onclick='chooer(" + len + ")' id='members-" + len + "-deptName' value='" + nullTonString(option.deptName) + "' ><input type='hidden' class='form-control' id='members-" + len + "-deptCode' name='members[" + len + "].deptCode'  value='" + nullTonString(option.deptCode) + "' ></td>";
        tr = tr + "<td style='text-align: center;'><input type='text' required class='form-control' name='members[" + len + "].contribution' readonly oninput='calculationContribution();'  value='" + contribution + "' ></td>";

        if ($.common.isNotEmpty(option.postTitel) && option.postTitel != "null") {
            tr = tr + "<td style='text-align: center;'><input type='text'  readonly class='form-control'  id='members-" + len + "-postTitel' name='members[" + len + "].postTitel'  value='" + nullTonString(option.postTitel) + "'></td>";
        } else {
            tr = tr + "<td style='text-align: center;'><input type='text'  required  class='form-control' id='members-" + len + "-postTitel' name='members[" + len + "].postTitel'  value='" + nullTonString(option.postTitel) + "'></td>";

        }
        if ($.common.isNotEmpty(option.postion)) {
            tr = tr + "<td style='text-align: center;'><input type='text' required class='form-control' readonly id='members-" + len + "-postion' name='members[" + len + "].postion' value='" + nullTonString(option.postion) + "' ></td>";
        } else {
            tr = tr + "<td style='text-align: center;'><input type='text' required class='form-control' id='members-" + len + "-postion' name='members[" + len + "].postion' value='" + nullTonString(option.postion) + "' ></td>";

        }
        tr = tr + "<td style='text-align: center;'><input type='text'  class='form-control' name='members[" + len + "].mark'  value='" + nullTonString(option.mark) + "'></td>";
        // tr = tr + "<td style='text-align: center;'> <a href='javascript:void(0)' onclick='del(this)'><img src='" + tub + "'></a></td>";
        // tr = tr + "<td style='text-align: center; width: 50px;'><button class='btn btn-primary btn-circle btn-sm' type='button' onclick='del(this)'><i class='fa fa-minus'></i></button></td>";
        tr = tr + "<td style='text-align: center; width: 50px;'><button class='btn btn-success btn-xs' type='button' onclick='editInventor("+len+")'><i class='fa fa-edit'></i>修改</button><button class='btn btn-danger btn-xs' type='button' onclick='del(this)'><i class='fa fa-remove'></i>删除</button></td>";
        tr = tr + "</tr>";
        $("#tabletBody").append(tr);
    }

    //添加权属单位
    function insertColumns() {

        var theDeptCode = [[${result.tkymmTechnologyEx.firstdeptCode}]];
        var theDeptName = [[${result.tkymmTechnologyEx.firstdeptName}]];
        var num = parseInt($("#qlrNum").val());

        let len = $("#tabletBodys").find("tr").length;
        var extra1 = "集团内<input type=\"radio\" id='showState'  name='owners[" + num + "].extra1'  onchange='deptClick(" + num + ",this);' value='1' checked> 集团外<input type=\"radio\" id='hiddenState'  name='owners[" + num + "].extra1'  value='0'  onchange='deptClick(" + num + ",this);'>";
        var length = len + 1;
        var tr = "<tr data-index='" + len + "'>";
        tr = tr + "<td class='bs-checkbox' style='width: 100px;text-align: center; '><input name='owners[" + num + "].displayOrder' type='hidden' value='" + length + "'>" + length + "</td>";
        tr = tr + "<td class='bs-checkbox' style='width: 300px;text-align: center; '>" + extra1 + "</td>";
        tr = tr + "<td style='text-align: center;'><input type='text' required class='form-control' id='owners-" + num + "-techOwnerName'  onclick='chooerOrg(" + num + ")' name='owners[" + num + "].techOwnerName'><input type='hidden' required class='form-control' id='owners-" + num + "-techOwnerCode'  name='owners[" + num + "].techOwnerCode' value='" + theDeptCode + "'></td>";
        // tr = tr + "<td style='text-align: center;'> <a href='javascript:void(0)' onclick='delDept(this)'><img src='" + tub + "'></a></td>";
        tr = tr + "<td style='text-align: center; width: 50px;'><button class='btn btn-primary btn-circle btn-sm' type='button' onclick='delDept(this)' width='20px'><i class='fa fa-minus'></i></button></td>";
        tr = tr + "</tr>";
        $("#tabletBodys").append(tr);
        $("#ownershipNumLabel").text(len+1);
        $("#ownershipNum").val(len+1);
        $("#qlrNum").val(num + 1);
    }

    //外部单位
    var externalUnit = [[${@dict.getDictList('KYMM','external_unit')}]];

    //添加权属单位
    function readyInsertColumns(option) {
        var extra1;
        let len = $("#tabletBodys").find("tr").length;
        if (option.extra1 == 1) {
            extra1 = "集团内<input type=\"radio\"  name='owners[" + len + "].extra1' onchange='deptClick(" + len + ",this);' value='1' checked='checked'> 集团外<input type=\"radio\" name='owners[" + len + "].extra1'  value='0' onchange='deptClick(" + len + ",this);'>";
        } else {
            extra1 = "集团内<input type=\"radio\"  name='owners[" + len + "].extra1' onchange='deptClick(" + len + ",this);' value='1' > 集团外<input type=\"radio\" name='owners[" + len + "].extra1' checked='checked' value='0' onchange='deptClick(" + len + ",this);'>";
        }
        var length = len + 1;

        var tr = "<tr data-index='" + len + "'>";

        tr = tr + "<td class='bs-checkbox' style='width: 100px;text-align: center; '><input name='owners[" + len + "].displayOrder' type='hidden' value='" + option.displayOrder + "'>" + length + "</td>";
        tr = tr + "<td class='bs-checkbox' style='width: 300px;text-align: center; '>" + extra1 + "</td>";

        if (option.extra1 == 1) {
            tr = tr + "<td style='text-align: center;'><input type='text' required class='form-control' id='owners-" + len + "-techOwnerName' value='" + nullTonString(option.techOwnerName) + "'  onclick='chooerOrg(" + len + ")' name='owners[" + len + "].techOwnerName' ><input type='hidden' required class='form-control' id='owners-" + len + "-techOwnerCode'  name='owners[" + len + "].techOwnerCode' value='" + option.techOwnerCode + "' ></td>";
        } else {
            /*
            tr = tr + "<td style='text-align: center;'><select name='owners[" + len + "].techOwnerName'  id='owners-" + len + "-techOwnerName' class=\"form-control\" >";
            for (const key of externalUnit) {
                tr = tr + "<option value='" + key.dictCode + "'>" + key.dictName + "</option>";
            }
            tr = tr + "</select></td>";
            */
            tr = tr + "<td style='text-align: center;'><input type='text' required name='owners[" + len + "].techOwnerName'  id='owners-" + len + "-techOwnerName' class=\"form-control\" value='" + nullTonString(option.techOwnerName) + "'>";
            tr = tr + "</input></td>";
        }
        // tr = tr + "<td style='text-align: center;'> <a href='javascript:void(0)' onclick='delDept(this)'><img src='" + tub + "'></a></td>";
        tr = tr + "<td style='text-align: center; width: 50px;'><button class='btn btn-primary btn-circle btn-sm' type='button' onclick='delDept(this)'><i class='fa fa-minus'></i></button></td>";
        tr = tr + "</tr>";

        console.log(tr);
        $("#tabletBodys").append(tr);
    }


    function deptClick(obj,obj1) {
        var theDeptCode = [[${result.tkymmTechnologyEx.firstdeptCode}]];
        var theDeptName = [[${result.tkymmTechnologyEx.firstdeptName}]];
        var trObj = obj1.parentNode.parentNode;

        var len = obj + 1;
        var xh = parseInt($("xh").text());
        var nameSelect = "owners[" + obj + "].extra1";
        var checkedVal = $("input[type='radio'][name='" + nameSelect + "']:checked").val();
        if (checkedVal == 1) {
            var inputHtml = "<input type='text' required class='form-control' id='owners-" + obj + "-techOwnerName'  onclick='chooerOrg(" + obj + ")' name='owners[" + obj + "].techOwnerName' value='" + theDeptName + "'><input type='hidden' required class='form-control' id='owners-" + obj + "-techOwnerCode'  name='owners[" + obj + "].techOwnerCode' value='" + theDeptCode + "'>";
            // $("#tabletBodys").find("tr").eq(obj).find("td").eq(2).html(inputHtml);
            $("#tabletBodys").find("tr").eq($(trObj).index()).find("td").eq(2).html(inputHtml);
            // obj1.parentNode.parentNode.find("td").eq(2).html(inputHtml);
        } else {
        	/*
            var selectHtml = "<select name='owners[" + obj + "].techOwnerName'  id='owners-" + obj + "-techOwnerName' class=\"form-control\" >";
            for (const key of externalUnit) {
                selectHtml = selectHtml + "<option value='" + key.dictCode + "'>" + key.dictName + "</option>";
            }
            selectHtml = selectHtml + "</select>";
            */
            var selectHtml = "<input type='text' required name='owners[" + obj + "].techOwnerName'  id='owners-" + obj + "-techOwnerName' class=\"form-control\"/>";
            // $("#tabletBodys").find("tr").eq(obj).find("td").eq(2).html(selectHtml);
            $("#tabletBodys").find("tr").eq($(trObj).index()).find("td").eq(2).html(selectHtml);
            // obj1.parentNode.parentNode.find("td").eq(2).html(selectHtml);
        }
    }


    //删除最后一行数据
    function deleteColumns(obj) {
        $("#tabletBodys").find("tr:last").remove();
    }

    /****
     * 删除所有行
     */
    function delAll() {
        $.modal.confirm("确定删除所有行吗？", function () {
            $('#mytable tbody').children().remove();
        });
    }

    /****
     * 删除所有行（权利人）
     */
    function delDept(obj) {
        $.modal.confirm("确定删除当前行吗？", function () {
            $(obj).parent().parent().remove();
            //通过this找到父级元素节点
            // var tr = obj.parentNode.parentNode;
            //找到表格
            // var tbody = tr.parentNode;
            //删除行
            // tbody.removeChild(tr);

            $('#myjstable tbody').find("tr").each(function (index, ele) {
                var indexs = $(this).find("td").eq(0).html();
                $(this).find("td").eq(0).html(index + 1);
            })

            let len = $("#tabletBodys").find("tr").length;
            $("#ownershipNum").val(len);
            $("#ownershipNumLabel").text(len);
        });
    }

    /****
     * 删除所有行（权利人）
     */
    function delDeptAll() {
        $.modal.confirm("确定删除所有行吗？", function () {
            $('#tabletBodys').children().remove();
            $("#ownershipNum").val(0);
            $("#ownershipNumLabel").text(len);
        });
    }

    /****
     * 删除所有行
     */
    /* function deleteAll() {
         $('#myjstable tbody').children().remove();
     }*/
    //obj是点击行的this
    function del(obj) {
        $.modal.confirm("确定删除当前行吗？", function () {
            //通过this找到父级元素节点
            var tr = obj.parentNode.parentNode;
            //找到表格
            var tbody = tr.parentNode;
            //删除行
            tbody.removeChild(tr);

            $('#mytable tbody').find("tr").each(function (index, ele) {
                var indexs = $(this).find("td").eq(0).html();
                $(this).find("td").eq(0).html(index + 1);
            })
        });
    }

    //obj是点击行的this
    function deletParent(obj) {
        //通过this找到父级元素节点
        var tr = obj.parentNode.parentNode;
        //找到表格
        var tbody = tr.parentNode;
        //删除行
        tbody.removeChild(tr);

        $('#myjstable tbody').find("tr").each(function (index, ele) {
            var indexs = $(this).find("td").eq(0).html();
            $(this).find("td").eq(0).html(index + 1);
        })
    }


    /****
     * 从ehr选择人员
     * @param val
     */
    function chooer(val) {
        // var memberName = "members-" + val + "-memberName";
        // var memberGh = "members-" + val + "-memberGh";
        // choiceUserMM(memberGh, memberName, "S", null, null);
    }

    function choiceUserMM(userCodeInputId, userNameInputId, selectType, orgCode, callback) {
        userId = userCodeInputId;
        userNameId = userNameInputId;
        var url = ctx + 'mpad/user/selectUserList';
        if (selectType === undefined || selectType == null || selectType == '') {
            selectType = "S";
        }
        url += "?selectType=" + selectType;
        if (!(orgCode === undefined) && orgCode != null) {
            url += "&orgCode=" + orgCode;
        }
        var values = $("#" + userCodeInputId).val();
        if (!(values === undefined) && values != null) {
            url += "&values=" + values;
        }
        if (!(callback === undefined) && callback != null) {
            url += "&callback=" + callback;
        }
        url += "&userId=" + $("#" + userId).val() + "&userName" + $("#" + userNameId).val();
        $.modal.open("选择用户", url, '1000', '500');
    }

    function choiceUserCallback(userCode, userName) {
        // $("#" + userId).val(userCode);
        // $("#" + userNameId).val(userName);
        // getOrgCode(userCode, userId);;
        console.info("userCode->"+userCode);
        console.info("userName->"+userName);
        var userCodeNum = userCode.split(",");
        var userNameNum = userName.split(",");
        var len;
        for(var i = 0;i<userCodeNum.length;i++){
            addColumns();
            len = $("#tabletBody").find("tr").length - 1;
            var num = parseInt($("#fmrNum").val());
            console.info("len"+i+"-->"+len);
            console.info(userCodeNum[i]);
            $("#members-" + num + "-memberGh").val(userCodeNum[i]);
            $("#members-" + num + "-memberName").val(userNameNum[i]);
            getOrgCode(userCodeNum[i], "members-" + num + "-memberGh");
            $("#fmrNum").val(num + 1);
        }

    }

    function bacthChoiceUserCallback(userCode, userName) {

    }


    /****
     * 从ehr选择单位
     * @param val
     */
    function chooerOrg(val) {
        var ownerName = "owners-" + val + "-techOwnerName";
        var ownerGh = "owners-" + val + "-techOwnerCode";
        choiceOrgMM(ownerGh, ownerName, "S", -1, null);
    }


    function choiceOrgMM(orgCodeInputId, orgNameInputId, selectType, level, orgCode, showLevel, callback) {
        orgId = orgCodeInputId;
        orgNameId = orgNameInputId;
        if (selectType === undefined || selectType == null || selectType == '') {
            selectType = "S";
        }
        var url = ctx + "mpad/org/selectOrgList?selectType=" + selectType;
        if (!(level === undefined) && level != null) {
            url += "&level=" + level;
        }
        if (!(orgCode === undefined) && orgCode != null) {
            url += "&orgCode=" + orgCode;
        }
        if (!(showLevel === undefined) && showLevel != null) {
            url += "&showLevel=" + showLevel;
        }
        if (!(callback === undefined) && callback != null) {
            url += "&callback=" + callback;
        }
        url += "&values=" + $("#" + orgId).val();
        var options = {
            title: '选择组织',
            width: "380",
            height: '500',
            url: url,
            callBack: choiceOrgCallback
        };
        $.modal.openOptions(options);
    }

    function choiceOrgCallback(index, layero) {
        var tree = layero.find("iframe")[0].contentWindow.$._tree;
        var body = layer.getChildFrame('body', index);
        layero.find("iframe")[0].contentWindow.saveCheck();
        $("#" + orgId).val(body.find('#treeId').val());
        $("#" + orgNameId).val(body.find('#treeName').val());
        layer.close(index);
    }

    /****
     *获取组织
     */
    function getOrgCode(userCodeId, userId) {
        console.info(userCodeId)
        console.info(userId);
        axios.get(
            prefix + "orgCode/" + userCodeId,
        ).then(res => {
            var data = res.data.data;
            console.log(res);
            console.log(res.data);
            console.log(data);
            var deptName = userId.replace("memberGh", "deptName");
            var deptCode = userId.replace("memberGh", "deptCode");
            var postion = userId.replace("memberGh", "postion");
            var postTitel = userId.replace("memberGh", "postTitel");
            var orgCode = data.adOrg.orgCode;
            var orgName = data.adOrg.orgName;
            var userZhiCheng = data.adUser.userZhiCheng;
            var postName = data.adUser.postName;
            $("#" + deptName).val(orgName);
            $("#" + deptCode).val(orgCode);
            $("#" + postTitel).val(userZhiCheng);
            $("#" + postion).val(postName);
            if($.common.isNotEmpty(postName)){
                $("#" + postion).attr("readonly",true);
            }else{
                $("#" + postion).removeAttr("readonly");
            }

            if($.common.isNotEmpty(userZhiCheng)){
                $("#" + postTitel).attr("readonly",true);
            }else{
                $("#" + postTitel).removeAttr("readonly");
            }
        })
    }


    //计算贡献系数
    function calculationContribution() {
        var count = 0
        $('#mytable tbody').find("tr").each(function (index, ele) {
            var val = $(this).find("td").eq(4).find("input").val();
            if ($.common.isNotEmpty(val)) {
                var number = parseInt(val);
                count = count + number;
            }
        })
        if (count > 100) {
            $.modal.msgError("贡献系数之和不能大于100");
            return;
        }
    }


    /****
     * 暂存
     */
    function saveAxios() {
        // 向给定ID的用户发起请求
        axios.post(
            prefix + "saveOrUpdate",
            $("#form-technology-add").serialize()
        ).then(res => {
            var data = res.data;
            console.log(data);
            if (data.code == '0') {
                var technologyId = data.data.technologyId;
                $("#technologyId").val(technologyId);
                $.modal.msgSuccess(data.msg);
            } else {
                $.modal.msgError(data.msg);
            }
        }).catch(res => {
            // 处理错误情况
            console.log(res);
        })
    }


    /****
     * 删除项目
     */
    function delAxios() {
        // 向给定ID的用户发起请求
        var technologyId = $("#technologyId").val();
        if ($.common.isEmpty(technologyId)) {
            $.modal.msgError("尚未保存，不能删除");
            return;
        }
        axios(
            {
                method: 'Post',
                url: prefix + "remove",
                params: {
                    ids: technologyId
                }
            }
        ).then(res => {
            var data = res.data;
            if (data.code == '0') {
                closeItem();
                $.modal.msgSuccess(data.msg);
            } else {
                $.modal.msgError(data.msg);
            }
        }).catch(res => {
            // 处理错误情况
            console.log(res);
        })
    }


    //启动流程
    function startProcess() {
        calculationContribution();
        // confirmDeclare();
        if ($.validate.form() && $.modal.confirm("确认要提交吗？", function () {
            $.operate.saveTabAlert(prefix + "startProcess", $('#form-technology-add').serialize(), function (res) {
                $.operate.alertSuccessTabCallback(res);
            });

        })) ;
    }

    //初始化
    $(document).ready(function () {
        $(".availables").hide();
        $(".notAvailable").hide();
        $("#otherSource").hide();
        $(".source").hide();
        $("#applyDept").hide();
        $("#usePurpose").hide();
        $("#secretLabel").hide();//秘密类型提示语
        //根据应用方式来控制元素的隐藏与显示a
        radioCallbackApplicationWay([[${result.tkymmTechnologyEx.applicationWay}]]);
        //来源
        radioCallback([[${result.tkymmTechnologyEx.sourceType}]]);
        secretCallback([[${result.tkymmTechnologyEx.secretLevel}]]);

        //权属人单位
        var owners = [[${result.tkymmTechnologyEx.owners}]];
        if (owners != null && owners != '') {
            for (var owner of owners) {
                readyInsertColumns(owner);
            }
        }
        //提出人
        var members = [[${result.tkymmTechnologyEx.members}]];
        if (members != null && members != '') {
            for (var member of members) {
                readyAddColumns(member);
            }
        }

        //用途显示隐藏
        getIron();
        // var extra2Value = [[${result.tkymmTechnologyEx.extra2}]];
        // if ($.common.isEmpty(extra2Value)) {
        //     $("#usePurpose").hide();
        // } else {
        //     $("#usePurpose").show();
        // }





    });

    function radioCallbackApplicationWay(a) {
        //是否应用通过应用方式控制元素的显示与隐藏
        //未应用
        if (a == "Not_applied") {
            $(".availables").hide();
            $(".notAvailable").show();
            $("#applyDept").hide();
        } else if (a == "Internal_implementation_of_the_unit") {
            $(".notAvailable").hide();
            $(".availables").show();
            $("#applyDept").show();
        }
    }

    function secretCallback(a) {
        if ($.common.isEmpty(a)) {
            return ;
        }
        if (a == "core") {
            $("#secretLabel").show();
        } else {
            $("#secretLabel").hide();
        }
    }

    //radio 回掉
    function radioCallback(a) {
        if ($.common.isEmpty(a)) {
            return;
        }
        if (a == 'NO_SOURCE') {
            $(".source").hide();
            $("#otherSource").hide();
        } else if (a == 'OTHER_SOURCE') {
            $(".source").hide();
            $("#otherSource").show();
        } else {
            if(a!=$("input[name='sourceType']:checked").val()){
                //来源类型改变，编号、名称置空
                $("#sourceNum").val("");
                $("#sourceName").val("");
                //同来源专利秘密清空
                $("#tllzl").empty();
                $("#tllmm").empty();
                //技改、改善无模块  --（后续加上只读控制）
                // if (a == 'JG' || a == 'GSGS') {
                //     $("#sourceName").removeAttr("readonly");
                // }else{
                //     $("#sourceName").attr("readonly",true);
                // }
                //后续注释掉
                $("#sourceName").removeAttr("readonly");
            }
            $("#otherSource").hide();
            $(".source").show();
        }
        $("#sourceTypeValue").val(a);
        getSource();
    }

    //获取来源信息
    function getSource() {
        var sourceNum = $("#sourceNum").val();
        var sourceType = $("#sourceTypeValue").val();
        axios.get(
            ctx + "kymm/common/getSource/" + sourceType + "/" + sourceNum
        ).then(res => {
            var data = res.data.data;
            if(res.data.code==500){
                $.modal.alertWarning(res.data.msg);
            }else {
                // $("#sourceNameLabel").text(data.sourceName);
                // $("input[type='text'][name='sourceName']").val(data.sourceName);
                $("#sourceName").val();
                // $("#samesourceTechLabel").text(data.samesourceTech);
                // $("input[type='text'][name='samesourceTech']").val(data.samesourceTech);
                // $("#samesourceTech").val(data.samesourceTech);
                // $("#samesourcePatentLabel").text(data.samesourcePatent);
                // $("input[type='text'][name='samesourcePatent']").val(data.samesourcePatent);
                // $("#samesourcePatent").val(data.samesourcePatent);
                var zllist = data.samesourcePatentList;
                var mmlist = data.samesourceTechList;
                if(zllist!=undefined){
                    $("#tllzl").empty();
                    for(var i=0;i<zllist.length;i++){
                        var tr = "<tr id='c" + i + "'>";
                        tr = tr + "<td style='text-align:center'>"+zllist[i].applyName+"</td>";
                        tr = tr + "<td style='text-align:center'>"+zllist[i].lastStatus+"</td>";
                        tr = tr + "<td style='text-align:center'>"+zllist[i].jsbh+"</td>";
                        tr = tr + '<td style="text-align:center"><a onClick="tllzlDetail('+"'"+zllist[i].applyId+"'"+')">'+"<font color='blue'>查看</font></a></td>";
                        tr = tr + "</tr>";
                        $("#tllzl").append(tr);
                    }
                }
                if(mmlist!=undefined){
                    $("#tllmm").empty();
                    for(var i=0;i<mmlist.length;i++){
                        var tr = "<tr id='c" + i + "'>";
                        tr = tr + "<td style='text-align:center'>"+mmlist[i].technologyName+"</td>";
                        tr = tr + "<td style='text-align:center'>"+mmlist[i].lastStatus+"</td>";
                        tr = tr + "<td style='text-align:center'>"+mmlist[i].confirmNum+"</td>";
                        tr = tr + '<td style="text-align:center"><a onClick="tllmmDetail('+"'"+mmlist[i].technologyId+"'"+')">'+"<font color='blue'>查看</font></a></td>";
                        tr = tr + "</tr>";
                        $("#tllmm").append(tr);
                    }
                }
            }
        }).catch(res => {
            // 处理错误情况
            console.log(res);
        });
    }
    //同来源专利、同来源技术秘密 查看详情
    function tllzlDetail(id){
        var title = "专利申请详细信息";
        var url = ctx+"KIZL/PA/AB/queryDT?id="+id;
        $.modal.openTab(title, url, true);
    }
    function tllmmDetail(id){
        var title = "技术秘密详细信息";
        var url = ctx+"kymm/technology/detailTj/"+id;
        $.modal.openTab(title, url, true);
    }


    /*获取权属信息并和技术权利单位产生联系*/
    function ownershipNumTes() {
        var ownershipNum = $("#ownershipNum").val();
        if ($.common.isNotEmpty(ownershipNum)) {
            var number = Number.parseInt(ownershipNum);
            let len = $("#tabletBodys").find("tr").length;
            if (number > len) {
                number = number - len;
                for (let i = 0; i < number; i++) {
                    insertColumns();
                }
            } else if (len > number) {
                var lens = len - number;
                for (let i = len; i > number; i--) {
                    deleteColumns();
                }
                return;
            }
        } else {
            let len = $("#tabletBodys").find("tr").length;
            if (len > 0) {
                $("#tabletBodys").children("tr").remove();
            }
        }
    }

    function fieldCallback(treeId,treeName) {
        console.info(treeId);
        console.info(treeName);
        var treeId = treeId;
        var treeName = treeName;
        if ($.common.isEmpty(treeId)) {
            $("#usePurpose").hide();
            $("#extra2").removeAttr("required");
        }
        axios.get(
            ctx + "kymm/common/getIron/" + treeId + "/" + treeName
        ).then(res => {
            console.info(res);
            var data = res.data;
            console.info(data);
            console.info(data.data);
            if(res.data.data){
                $("#usePurpose").show();
                $("#extra2").attr('required','true');
            }else {
                $("#usePurpose").hide();
                $("#extra2").removeAttr("required");
            }
        }).catch(res => {
            // 处理错误情况
            console.log(res);
        });
    }

    function getIron(){
        var treeId = [[${result.tkymmTechnologyEx.technologyField}]];
        var treeName = [[${result.tkymmTechnologyEx.extra1}]];
        console.info("treeId-->"+treeId);
        axios.get(
            ctx + "kymm/common/getIron/" + treeId + "/" + treeName
        ).then(res => {
            console.info(res);
            var data = res.data;
            console.info(data);
            console.info(data.data);
            if(res.data.data){
                $("#usePurpose").show();
                $("#extra2").attr('required','true');
            }else {
                $("#usePurpose").hide();
                $("#extra2").removeAttr("required");
            }
        }).catch(res => {
            // 处理错误情况
            console.log(res);
        });
    }

    // function sourceRadioCallback(a) {
    //
    //     if ($.common.isEmpty(a)) {
    //         return;
    //     }
    //     if (a == 'NO_SOURCE') {
    //         $(".source").hide();
    //         $("#otherSource").hide();
    //     } else if (a == 'OTHER_SOURCE') {
    //         $(".source").hide();
    //         $("#otherSource").show();
    //     } else {
    //         //来源类型改变，编号、名称置空
    //         $("#sourceNum").val("");
    //         $("#sourceName").val("");
    //         //技改、改善无模块
    //         if (a == 'JG' || a == 'GSGS') {
    //             $("#sourceName").removeAttr("readonly");
    //         }else{
    //             $("#sourceName").attr("readonly",true);
    //         }
    //         $("#otherSource").hide();
    //         $(".source").show();
    //     }
    //     $("#sourceTypeValue").val(a);
    // }


    //发明人--------------------------------------------------------------
    //添加发明人--小窗体
    function addInventor(){
        var url = ctx+"kymm/technology/choiceInventor";
        $.modal.open("添加提出人", url, '1000', '590');
    }

    //发明人数据添加
    function addInventorUserCallback(ryxh,rylx,legalId,legalName,empId,empName,deptCode,deptName,postCTitle,postLevel,postCName,gxxs,idCard,contentMemo) {
        // alert(empId+"--"+empName+"--"+deptCode+"--"+deptName+"--"+postTitle+"--"+postName);
        addColumns();
        // len = $("#tabletBody").find("tr").length - 1;
        var num = parseInt($("#fmrNum").val());
        $("#members-" + num + "-memberType").val(rylx);
        $("#members-" + num + "-memberGh").val(empId);
        $("#members-" + num + "-memberName").val(empName);
        $("#members-" + num + "-deptName").val(deptName);
        $("#members-" + num + "-deptCode").val(deptCode);
        $("#members-" + num + "-postTitel").val(postCTitle);
        $("#members-" + num + "-postion").val(postCName);
        $("input[name='members[" + num + "].contribution']").val(gxxs);
        $("input[name='members[" + num + "].mark']").val(contentMemo);
        if($.common.isNotEmpty(postCName)){
            $("#members-" + num + "-postion").attr("readonly",true);
        }else{
            $("#members-" + num + "-postion").removeAttr("readonly");
        }

        if($.common.isNotEmpty(postCTitle)){
            $("#members-" + num + "-postTitel").attr("readonly",true);
        }else{
            $("#members-" + num + "-postTitel").removeAttr("readonly");
        }
        $("#fmrNum").val(num + 1);
    }

    //修改发明人--小窗体
    function editInventor(id){
        if ($.common.isEmpty(id)) {
            $.modal.alertError("id不能为空");
            return false;
        }
        var empId = $("#members-" + id + "-memberGh").val();
        var empName = $("#members-" + id + "-memberName").val();
        var deptName = $("#members-" + id + "-deptName").val();
        var deptCode = $("#members-" + id + "-deptCode").val();
        var postTitle = $("#members-" + id + "-postTitel").val();
        var postName = $("#members-" + id + "-postion").val();
        var gxxs = $("input[name='members[" + id + "].contribution']").val();
        var mark = $("input[name='members[" + id + "].mark']").val();

        // if ($.common.isEmpty(empId)) {
        //     empId = "-";
        // }
        // if ($.common.isEmpty(empName)) {
        //     empName = "-";
        // }
        // if ($.common.isEmpty(deptName)) {
        //     deptName = "-";
        // }else{
        //     deptName = deptName.replace("/", "-");
        //     alert(deptName);
        // }
        // if ($.common.isEmpty(deptCode)) {
        //     deptCode = "-";
        // }else{
        //     deptCode.replace("/", "-");
        //     alert(deptCode);
        // }
        // if ($.common.isEmpty(postTitle)) {
        //     postTitle = "-";
        // }
        // if ($.common.isEmpty(postName)) {
        //     postName = "-";
        // }
        // if ($.common.isEmpty(gxxs)) {
        //     gxxs = "-";
        // }
        // if ($.common.isEmpty(mark)) {
        //     mark = "-";
        // }

        // var url = ctx + "kymm/technology/editInventor/" + id + "/" + empId + "/" + empName + "/" + deptName + "/" + deptCode + "/" + postTitle + "/" + postName + "/" + gxxs + "/" + mark;
        var url = ctx + "kymm/technology/editInventor/" + id + "?empId=" + empId + "&empName="
            + empName + "&deptName=" + deptName + "&deptCode=" + deptCode
            + "&postTitle=" + postTitle + "&postName=" + postName + "&gxxs="
            + gxxs + "&mark=" + mark;
        //alert(url);
        $.modal.open("修改提出人", url, '1000', '590');
    }

    //发明人数据修改
    function editInventorUserCallback(id,ryxh,rylx,legalId,legalName,empId,empName,deptCode,deptName,postCTitle,postLevel,postCName,gxxs,idCard,contentMemo) {
        $("#members-" + id + "-memberType").val(rylx);
        $("#members-" + id + "-memberGh").val(empId);
        $("#members-" + id + "-memberName").val(empName);
        $("#members-" + id + "-deptName").val(deptName);
        $("#members-" + id + "-deptCode").val(deptCode);
        $("#members-" + id + "-postTitel").val(postCTitle);
        $("#members-" + id + "-postion").val(postCName);
        $("input[name='members[" + id + "].contribution']").val(gxxs);
        $("input[name='members[" + id + "].mark']").val(contentMemo);
        if($.common.isNotEmpty(postCName)){
            $("#members-" + id + "-postion").attr("readonly",true);
        }else{
            $("#members-" + id + "-postion").removeAttr("readonly");
        }

        if($.common.isNotEmpty(postCTitle)){
            $("#members-" + id + "-postTitel").attr("readonly",true);
        }else{
            $("#members-" + id + "-postTitel").removeAttr("readonly");
        }
    }

    //确认声明
    function confirmDeclare() {
        var vd = $('input[name="declare"]:checked').val();

        if (vd == undefined) {
            $.modal.alertWarning("请先点击声明确认!!!");
            return false;
        }

        //遍历
        // $('input[name="declare"]:checked').each(function(index, element) {
        //     if ($(this).val() == "") {
        //
        //     }
        // });
    }
</script>
</body>
</html>