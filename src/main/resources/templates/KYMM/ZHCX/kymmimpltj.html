<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('技术秘密实施统计')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form class="form-horizontal m" id="formId">
                    <!--框-->
                    <div class="panel-group" role="tablist" aria-multiselectable="true">
                        <div class="panel panel-default">
                            <div class="panel-heading">
                                <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">技术秘密实施综合查询
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                                </h4>
                            </div>
                            <!--折叠区域-->
                            <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                                <div class="panel-body">
                                    <fieldset>
                                        <legend style="padding:3px;">基本信息:</legend>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">技术秘密名称：</label>
                                                    <div class="col-sm-6">
                                                        <input type="text" class="form-control" name="technologyNameLike" placeholder="支持模糊查询"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">认定号：</label>
                                                    <div class="col-sm-6">
                                                        <input type="text" class="form-control" name="confirmNumLike" placeholder="支持模糊查询"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">申请人工号：</label>
                                                    <div class="col-sm-6">
                                                        <input type="text" class="form-control" name="applyUserGh"/>
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">申请人姓名：</label>
                                                    <div class="col-sm-6">
                                                        <input type="text" class="form-control" name="applyUserName"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">第一申报部门：</label>
                                                    <div class="col-sm-8">
                                                        <th:block th:include="/component/selectOrg::init(orgCodeId='firstdeptCode', orgNameId='firstdeptName', selectType='S')" />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
<!--                                        <div class="row">-->
<!--                                            <div class="col-sm-6">-->
<!--                                                <div class="form-group">-->
<!--                                                    <label class="col-sm-3 control-label">实施部门：</label>-->
<!--                                                    <div class="col-sm-8">-->
<!--                                                        <th:block th:include="/component/selectOrg::init(orgCodeId='implDeptCode', orgNameId='implDeptName', selectType='S')" />-->
<!--                                                    </div>-->
<!--                                                </div>-->
<!--                                            </div>-->
<!--                                        </div>-->
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group" th:include="include :: initDate(id='implDateStart',name='implDateStart',labelName='初始实施时间起：')"></div>
                                            </div>
                                            <div class="col-sm-6">
                                                <div class="form-group" th:include="include :: initDate(id='implDateEnd',name='implDateEnd',labelName='初始实施时间止：')"></div>
                                            </div>
                                        </div>
<!--                                        <div class="row">-->
<!--                                            <div class="col-sm-6">-->
<!--                                                <div class="form-group" th:include="include :: initDate(id='useDateStart',name='useDateStart',labelName='申请时间：')"></div>-->
<!--                                            </div>-->
<!--                                            <div class="col-sm-6">-->
<!--                                                <div class="form-group" th:include="include :: initDate(id='useDateEnd',name='useDateEnd',labelName='应用结束日期：')"></div>-->
<!--                                            </div>-->
<!--                                        </div>-->
<!--                                        <div class="row">-->
<!--                                            <div class="col-sm-6">-->
<!--                                                <div class="form-group" th:include="include :: initDate(id='confirmTimeStart',name='confirmTimeStart',labelName='认定开始日期：')"></div>-->
<!--                                            </div>-->
<!--                                            <div class="col-sm-6">-->
<!--                                                <div class="form-group" th:include="include :: initDate(id='confirmTimeEnd',name='confirmTimeEnd',labelName='认定结束日期：')"></div>-->
<!--                                            </div>-->
<!--                                        </div>-->
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">奖项类型：</label>
<!--                                                    <div class="col-sm-6">-->
<!--                                                        <select name="sourceType" th:with="dictData=${@dict.getDictList('KYMM','applyType')}" class="form-control select2-hidden-accessible">-->
<!--                                                            <option value="">全部</option>-->
<!--                                                            <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>-->
<!--                                                        </select>-->
<!--                                                    </div>-->
                                                    <div class="col-sm-8" th:include="/component/select :: init(id='applyType', name='applyType',businessType='KYMM', dictCode='applyType',isfirst=true)">
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">来源类别：</label>
<!--                                                    <div class="col-sm-6">-->
<!--                                                        <select name="sourceType" th:with="dictData=${@dict.getDictList('KYMM','sourceType')}" class="form-control select2-hidden-accessible">-->
<!--                                                            <option value="">全部</option>-->
<!--                                                            <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>-->
<!--                                                        </select>-->
<!--                                                    </div>-->
                                                    <div class="col-sm-8" th:include="/component/select :: init(id='sourceType', name='sourceType',businessType='KYMM', dictCode='sourceType',isfirst=true)">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">来源编号：</label>
                                                    <div class="col-sm-6">
                                                        <input type="text" class="form-control" name="sourceNum"/>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">计奖效益：</label>
                                                    <div class="col-sm-3">
                                                        <input type="number" class="form-control" name="benefitAwardFrom">
                                                    </div>
                                                    <div class="col-sm-1">
                                                        -
                                                    </div>
                                                    <div class="col-sm-3">
                                                        <input type="number" class="form-control" name="benefitAwardTo">
                                                    </div>
                                                    <div class="col-sm-2">
                                                        <label>(万元)</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">奖励金额：</label>
                                                    <div class="col-sm-3">
                                                        <input type="number" class="form-control" name="awardAmountFrom">
                                                    </div>
                                                    <div class="col-sm-1">
                                                        -
                                                    </div>
                                                    <div class="col-sm-3">
                                                        <input type="number" class="form-control" name="awardAmountTo">
                                                    </div>
                                                    <div class="col-sm-2">
                                                        <label>(万元)</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </fieldset>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--框end-->
                    <div class="form-group" >
                        <div class="col-sm-10" style="text-align: right;margin-left:210px;">
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </div>
                    </div>
                </form>
            </div>


            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>

         <div class="btn-group-sm" id="toolbar" role="group">
             <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                 <i class="fa fa-download"></i> 导出
             </a>
         </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kymm/techImpl";

        var sourceType = [[${@dict.getDictList('KIZL','KI_FROM_TYPE')}]];
        var applyType = [[${@dict.getDictList('KYMM','applyType')}]];

        $(function() {
            var options = {
                url: prefix + "/implTjList",
                detailUrl:prefix+"/detailTj/{id}",
                queryParams: queryParams,
                exportUrl: prefix + "/exportImplTj",
                modalName: "实施统计查询",
                pageSize:50,
                pageList: [50, 100, 200],
                columns: [{
                    checkbox: true
                },
                {
                    field: 'techImplId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'technologyId',
                    title: '技术秘密主键',
                    visible: false
                },
                {
                    field: 'confirmNum',
                    title: '认定号'
                },
                    {
                        field: 'technologyName',
                        title: '技术秘密名称'
                    },
                    // {
                    //     field: 'contactPersonName',
                    //     title: '第一提出人'
                    // },
                // {
                //     field: 'applyUserGh',
                //     title: '提出人工号'
                // },
                // {
                //     field: 'applyUserName',
                //     title: '提出人姓名'
                // },
                    {
                        field: 'firstDeptName',
                        title: '第一申报部门',
                    },
                {
                    field: 'implDept',
                    title: '实施部门'
                },
                    {
                        field: 'applyType',
                        title: '申报类型',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(applyType, value);
                        }
                    },
                    {
                        field: 'applyTimes',
                        title: '申报次数'
                    },
                    {
                        field: 'sourceType',
                        title: '来源',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(sourceType, value);
                        }
                    },
                // {
                //     field: 'benefitAward',
                //     title: '计奖效益'
                // },
                // {
                //     field: 'awardAmount',
                //     title: '奖励金额'
                // },
                // {
                //     field: 'implDate',
                //     title: '初始实施时间'
                // },
                // {
                //     field: 'benefitYearIndiStart',
                //     title: '奖励时间'
                // },
                //     {
                //         field: 'applyUserName',
                //         title: '提出人'
                //     },
                //     {
                //         field: 'implStatus',
                //         title: '最新状态'
                //     },
                // {
                //     field: 'benefitYearIndiStart',
                //     title: '效益体现年起始时间'
                // },
                    {
                        field: 'currentActivityName',
                        title: '当前状态'
                    },
                    {
                        field: 'currentOperator',
                        title: '当前操作人'
                    },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.techImplId + '\',1000,600)"><i class="fa fa-edit"></i>查看</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        var  loginName= [[${T(com.baosight.iplat4j.core.web.threadlocal.UserSession).getLoginName()}]]

        function queryParams(params) {
            var search = $.table.queryParams(params);
            return search;
        }

        function reset() {

            $("#firstdeptCode").val("");
            $("#implDeptCode").val("");
            $("#sourceType").val("").select2();
            $("#applyType").val("").select2();
            $.form.reset();
        }

    </script>
</body>
</html>