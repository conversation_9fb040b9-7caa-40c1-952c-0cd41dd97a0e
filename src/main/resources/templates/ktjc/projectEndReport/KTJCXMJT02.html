<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('项目结题领导审批')"/>

    <th:block th:include="include :: baseJs"/>
    <style>
        table th, td {
            text-align: center;
        }

        .layui-elem-quote {
            padding: 8px;
            margin-left: 17px;
        }

    </style>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-projectEndReport-edit">

        <input id="recordGuid" name="recordGuid" th:value="${projectEndReport.recordGuid}" type="hidden">
        <input id="projectGuid" name="projectGuid" type="hidden" th:value="${projectEndReport.projectGuid}">

        <!--        流程节点-->
        <input name="currentActivity" type="hidden" th:value="${workFlow.currentActivity}">
        <input id="taskId" name="taskId" th:value="${workFlow.taskId}" type="hidden">
        <input id="processInstanceId" name="processInstanceId" th:value="${workFlow.processInstanceId}" type="hidden">
        <input id="businessGuid" name="businessGuid" th:value="${workFlow.businessGuid}" type="hidden">
        <input id="processCode" name="processCode" th:value="${workFlow.processCode}" type="hidden">

        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false"
                           class="collapsed">基本信息
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">项目名称：</label>
                                    <div class="col-sm-8">
                                        <input readonly required name="projectName"
                                               th:value="${projectInitialReport.projectName}" class="form-control"
                                               type="text">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">项目编号：</label>
                                    <div class="col-sm-8 form-control-static"
                                         th:utext="${project.projectCode}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">项目负责人：</label>
                                    <div class="col-sm-8 form-control-static"
                                         th:utext="${projectInitialReport.fzrName}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">联系电话：</label>
                                    <div class="col-sm-8 form-control-static"
                                         th:utext="${projectInitialReport.fzrContactTel}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <label class="col-sm-2 control-label is-required">项目负责单位：</label>
                            <div class="col-sm-10"
                                 th:include="/component/selectOrg::init(see=true,isrequired=true,orgCodeId='fzdwCode',orgNameId='fzdwName',value=${projectInitialReport.fzdwCode},selectType='M')">
                            </div>
                        </div>

                        <br/>
                        <div class="row ">
                            <label class="col-sm-2 control-label is-required">项目参与单位：</label>
                            <div class="col-sm-10"
                                 th:include="/component/selectOrg::init(see=true,isrequired=true,orgCodeId='cydwCode',orgNameId='cydwName',value=${projectInitialReport.cydwCode},selectType='M')">
                            </div>
                        </div>

                        <br/>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">项目开始日期：</label>
                                    <div class="col-sm-8">
                                        <div th:include="/component/date :: init(see=true,isrequired=true,id='projectStartDate', name='projectStartDate',strValue=${project.projectStartDate})"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">项目结束日期：</label>
                                    <div class="col-sm-8">
                                        <div th:include="/component/date :: init(see=true,isrequired=true,id='projectEndDate', name='projectEndDate',strValue=${project.projectEndDate})"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">实际完成日期：</label>
                                    <div class="col-sm-8">
                                        <div th:include="/component/date :: init(see=true,isrequired=true,id='sjwcrq', name='sjwcrq',strValue=${projectEndReport.sjwcrq})"></div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">

                                    <label class="col-sm-4 control-label">项目详细信息：</label>
                                    <div class="col-sm-8 form-control-static"><a
                                            style=" text-decoration: underline; color: #0000cc"
                                            th:onclick="viewXmlx()">点击此处查看</a></div>

                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx1"
                                               aria-expanded="false" class="collapsed">
                        项目集成综述&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>

                <div id="jbxx1" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="form-group">
                            <div class="mnote-editor-box">
                                <div th:include="component/richText::init(id='jczs',name='jczs',value=${projectEndReport.jczs},see='true')"></div>
                            </div>
                        </div>
                        <!--内容end-->
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx2"
                                               aria-expanded="false" class="collapsed">
                        原理和设计&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>

                <div id="jbxx2" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="form-group">
                            <div class="mnote-editor-box">
                                <div th:include="component/richText::init(id='ylsj',name='ylsj',value=${projectEndReport.ylsj},see='true')"></div>
                            </div>
                        </div>
                        <!--内容end-->
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx3"
                                               aria-expanded="false" class="collapsed">
                        摘要&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>

                <div id="jbxx3" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="form-group">
                            <div class="mnote-editor-box">
                                <div th:include="component/richText::init(id='jtzy',name='jtzy',value=${projectEndReport.jtzy},see='true')"></div>
                            </div>
                        </div>
                        <!--内容end-->
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx4"
                                               aria-expanded="false" class="collapsed">
                        制造生产阶段&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>

                <div id="jbxx4" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="form-group">
                            <div class="mnote-editor-box">
                                <div th:include="component/richText::init(id='scjd',name='scjd',value=${projectEndReport.scjd},see='true')"></div>
                            </div>
                        </div>
                        <!--内容end-->
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx5"
                                               aria-expanded="false" class="collapsed">
                        应用阶段&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>

                <div id="jbxx5" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="form-group">
                            <div class="mnote-editor-box">
                                <div th:include="component/richText::init(id='yyjd',name='yyjd',value=${projectEndReport.yyjd},see='true')"></div>
                            </div>
                        </div>
                        <!--内容end-->
                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx6"
                                               aria-expanded="false" class="collapsed">
                        技术差距分析及发展&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>

                <div id="jbxx6" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="form-group">
                            <div class="mnote-editor-box">
                                <div th:include="component/richText::init(id='fxfz',name='fxfz',value=${projectEndReport.fxfz},see='true')"></div>
                            </div>
                        </div>
                        <!--内容end-->
                    </div>
                </div>
            </div>
        </div>

        <div th:if="${'Manual3' eq workFlow.currentActivity}" class="panel-group" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx8"
                                               aria-expanded="false" class="collapsed">
                        评审信息&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>

                <div id="jbxx8" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row ">
                            <div class="layui-elem-quote"><span class="txt-impt">*</span>
                                综合评审意见
                            </div>
                        </div>
                        <div class="row ">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                                <textarea rows="8" name="psyj" class="form-control"
                                                          required>[[${projectEndReport.psyj}]]</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <br/>
                        <br/>

                        <div class="row">
                            <div>
                                <div class="form-group">
                                    <label style="margin-left: 10px"
                                           class="col-sm-2 control-label is-required">评审附件：</label>
                                    <div class="col-sm-8"
                                         th:include="/component/attachment :: init(isrequired=true,sourceId=${projectEndReport.recordGuid},sourceModule='KTJC_XMJT_PSFJ',name='xmjtPsfjId',id='xmjtPsfjId')"></div>
                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>

        <div th:if="${'Manual3' ne workFlow.currentActivity && 'Manual2' ne workFlow.currentActivity }"
             class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx8"
                                               aria-expanded="false" class="collapsed">
                        评审信息&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>

                <div id="jbxx8" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row ">
                            <div class="layui-elem-quote"><span class="txt-impt">*</span>
                                综合评审意见
                            </div>
                        </div>
                        <div class="row ">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <div class="col-sm-12 textarea-readonly" th:utext="${projectEndReport.psyj}">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <br/>
                        <br/>

                        <div class="row">
                            <div>
                                <div class="form-group">
                                    <label style="margin-left: 10px"
                                           class="col-sm-2 control-label is-required">评审附件：</label>
                                    <div class="col-sm-8"
                                         th:include="/component/attachment :: init(see=true,sourceId=${projectEndReport.recordGuid},sourceModule='KTJC_XMJT_PSFJ',name='xmjtPsfjId',id='xmjtPsfjId')"></div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>

        <div th:if="${psEnd!=null && psEnd}">
            <div th:if="${'Manual2' ne workFlow.currentActivity}" class="form-group">

                <div class="col-sm-12">
                    <div class="form-control-static" style="padding-top: 0px;color: red">综合评价计分</div>
                </div>
            </div>

            <div id="div-xmpsfPjf"
                 th:if="${'Manual3' eq workFlow.currentActivity && #strings.isEmpty(projectEndReport.xmpsf)}">
                <th:block
                        th:include="ktjc/ktjcdf :: pjf(sumId='xmpsfHj',moduleCode='ktjc_jt_zjdf',PJFmaintainGuid='20220624100058248860160',DFMaintainGuid='20220501163424801655360',isSee=false,titleName='项目评审分',bizGuid=${workFlow.businessGuid},dfUser=${projectEndReport.extra1 +'-1'})"/>
            </div>

            <div id="div-xmpsfDf"
                 th:if="${'Manual3' eq workFlow.currentActivity && !#strings.isEmpty(projectEndReport.xmpsf)}">
                <th:block
                        th:include="ktjc/ktjcdf :: init(sumId='xmpsfHj',titleName='项目评审分',bizGuid=${workFlow.businessGuid},bs='KTJC_XMPSF',dfUser=${projectEndReport.extra1 +'-1'})"/>
            </div>

            <div id="div-gxxsDf" th:if="${'Manual3' eq workFlow.currentActivity}">
                <th:block
                        th:include="ktjc/ktjcdf :: init(sumId='gxxsHj',titleName='集成贡献分',bizGuid=${workFlow.businessGuid},bs='KTJC_GXXS',dfUser=${projectEndReport.extra1 +'-2'})"/>
            </div>

            <th:block th:if="${'Manual3' ne workFlow.currentActivity && 'Manual2' ne workFlow.currentActivity }"
                      th:include="ktjc/ktjcdf :: init(see=true,sumId='xmpsfHj',titleName='项目评审分',bizGuid=${workFlow.businessGuid},bs='KTJC_XMPSF',dfUser=${projectEndReport.extra1 +'-1'})"/>

            <th:block th:if="${'Manual3' ne workFlow.currentActivity && 'Manual2' ne workFlow.currentActivity }"
                      th:include="ktjc/ktjcdf :: init(see=true,sumId='gxxsHj',titleName='集成贡献分',bizGuid=${workFlow.businessGuid},bs='KTJC_GXXS',dfUser=${projectEndReport.extra1 +'-2'})"/>

            <div th:if="${'Manual2' ne workFlow.currentActivity}" class="panel-group" role="tablist"
                 aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx7"
                                                   aria-expanded="false" class="collapsed">
                            奖励信息&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <div id="jbxx7" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="row ">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label is-required">项目评审分：</label>
                                        <div class="col-sm-8">
                                            <input readonly required name="xmpsf" id="xmpsf"
                                                   th:value="${projectEndReport.xmpsf}" class="form-control"
                                                   type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label">贡献系数：</label>
                                        <div class="col-sm-8">
                                            <input readonly required name="gxxs" id="gxxs"
                                                   th:value="${projectEndReport.gxxs}" class="form-control"
                                                   type="text">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row ">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label is-required">建议奖励金额（万元）：</label>
                                        <div class="col-sm-8">
                                            <input readonly required name="adviceRewardAmount" id="adviceRewardAmount"
                                                   th:value="${T(com.baosight.bscdkj.kl.rf.common.KlrfUtil).subZeroAndDot(projectEndReport.adviceRewardAmount)}"
                                                   class="form-control"
                                                   type="text">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div th:if="${'Manual4' eq workFlow.currentActivity}" class="row ">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label is-required">奖励金额（万元）：</label>
                                        <div class="col-sm-8">
                                            <input required name="rewardAmount" id="rewardAmount"
                                                   th:value="${T(com.baosight.bscdkj.kl.rf.common.KlrfUtil).subZeroAndDot(projectEndReport.rewardAmount)}"
                                                   class="form-control"
                                                   type="text">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div th:if="${'Manual5' eq workFlow.currentActivity  || 'Manual7' eq workFlow.currentActivity}"
                                 class="row ">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-sm-4 control-label is-required">奖励金额（万元）：</label>
                                        <div class="col-sm-8">
                                            <input readonly required name="rewardAmount" id="rewardAmount"
                                                   th:value="${T(com.baosight.bscdkj.kl.rf.common.KlrfUtil).subZeroAndDot(projectEndReport.rewardAmount)}"
                                                   class="form-control"
                                                   type="text">
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="form-group" th:if="${'Manual3' eq workFlow.currentActivity}">

            <div class="col-sm-12">
                <div class="form-control-static" style="color: red"> 专家评审未启动或未结束不能提交流程!</div>
            </div>
        </div>

        <th:block th:include="/ktjc/ktjcInclude :: wf-comment(showApprove=false)"></th:block>

        <!-- 流程跟踪3 -->
        <th:block th:include="component/wfCommentList3 :: init(businessId=${workFlow.businessGuid})"/>

        <th:block th:include="include :: includeTaskHistoryList(instanceId=${workFlow.processInstanceId})"></th:block>

    </form>
    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">

        <button th:if="${'Manual3' eq workFlow.currentActivity}" class="btn btn btn-primary" onclick="startPS()"
                type="button"><i class="fa fa-check"></i>启动专家评审
        </button>

        <button th:if="${'Manual3' eq workFlow.currentActivity}" type="button" class="btn btn-primary"
                onclick="zcXmjt()">
            <i class="fa fa-hdd-o"></i>&nbsp;暂存
        </button>


        <th:block th:if="${(psEnd!=null && psEnd) || 'Manual3' ne workFlow.currentActivity}"
                  th:include="component/wfSubmitOne :: init(taskId=${workFlow.taskId},callback=submitHandler)"/>

        <th:block th:include="component/wfReturn :: init(taskId=${workFlow.taskId},callback=wfReturn)"/>

        <button type="button" class="btn btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>&nbsp;返 回
        </button>
    </div>
    <!--按钮区end-->
</div>
<script th:inline="javascript">
    var prefix = ctx + "ktjc/projectEndReport";

    $("#form-projectEndReport-edit").validate({
        focusCleanup: true
    });

    function startPS() {
        $.modal.openTab("项目结题专家评审", ctxGGMK + "web/MPPS01?bizGuid=" + [[${workFlow.businessGuid}]] + "&moduleCode=ktjc_jt_zjdf&approveKind=MPPS_leaderReview&jbxx=" + [[${@ServiceKTJCProjectEndReport.getJbxx(workFlow.businessGuid)}]]);
    }

    //暂存项目结题
    function zcXmjt() {

        if ([[${psEnd}]]) {
            if ([[${#strings.isEmpty(projectEndReport.xmpsf)}]]) {
                ggmkPjfSubmit();
            } else {
                ggmkDfSubmit(serializeObject("div-xmpsfDf"));
            }
            ggmkDfSubmit(serializeObject("div-gxxsDf"));
        }

        var config = {
            url: prefix + "/zcXmjt",
            type: "post",
            dataType: "json",
            data: $('#form-projectEndReport-edit').serialize(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    $.modal.alertSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            }
        };
        $.ajax(config)
    }

    function submitHandler(transitionKey) {
        if ($.validate.form()) {
            if ($.modal.confirm("确认要提交吗?", function () {
                if ([[${workFlow.currentActivity eq 'Manual3'}]]) {
                    if ([[${#strings.isEmpty(projectEndReport.xmpsf)}]]) {
                        ggmkPjfSubmit();
                    } else {
                        ggmkDfSubmit(serializeObject("div-xmpsfDf"));
                    }
                    ggmkDfSubmit(serializeObject("div-gxxsDf"));
                }
                var data = $('#form-projectEndReport-edit').serialize();
                data += "&transitionKey=" + transitionKey;
                $.operate.saveTabAlert(prefix + "/sumbitXmjtWf", data);
            })) ;
        }
    }

    //查看立项报告
    function viewXmlx() {
        url = ctx + "ktjc/projectInitialReport/detail/" + [[${projectInitialReport.recordGuid}]];
        $.modal.openTab("立项报告详情", url);
    }

    //退回流程
    function wfReturn(activityKey) {
        if ($("#form-projectEndReport-edit").validate().element($("#comment"))) {
            if ($.modal.confirm("确认要退回吗?", function () {
                var data = $('#form-projectEndReport-edit').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
            })) ;
        }
    }


    //评分改变时计算建议奖励金额
    $("#form-projectEndReport-edit").on("change", "#xmpsfHj", function () {
        calculateJl();
    })
    $("#form-projectEndReport-edit").on("change", "#gxxsHj", function () {
        calculateJl();
    })

    function calculateJl() {
        var xmpsf = $("#xmpsfHj").val();
        var gxxs = $("#gxxsHj").val();
        $("#xmpsf").val(xmpsf);
        $("#gxxs").val(gxxs);

        var adviceRewardAmount = accMul(accMul(xmpsf, gxxs), 0.001);

        $("#adviceRewardAmount").val(adviceRewardAmount);

    }

    //乘法
    function accMul(arg1, arg2) {
        var m = 0, s1 = arg1.toString(), s2 = arg2.toString();
        try {
            m += s1.split(".")[1].length
        } catch (e) {
        }
        try {
            m += s2.split(".")[1].length
        } catch (e) {
        }
        return Number(s1.replace(".", "")) * Number(s2.replace(".", "")) / Math.pow(10, m)
    }

    //序列化打分结果
    function serializeObject(divId) {
        // 处理结果对象
        var result = {};

        $("#" + divId).find("input").each(function () {
            result[$(this).attr("name")] = $(this).val();
        })
        return result;
    }

    //打分保存
    function ggmkDfSubmit(data) {

        var config = {
            url: ctxGGMK + "mpps/maintain/submitDf",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            data: JSON.stringify(data),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {

            }
        };
        $.ajax(config)
    }


</script>
</body>
</html>