<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('项目变更申请退回负责人重新编制页面')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-xmmodify-edit">

        <input id="recordGuid" name="recordGuid" th:value="${xmmodify.recordGuid}" type="hidden">
        <input id="projectGuid" name="projectGuid" type="hidden" th:value="${projectInitialReport.projectGuid}">

        <!--        流程节点-->
        <input name="currentActivity" type="hidden" th:value="${workFlow.currentActivity}">
        <input id="taskId" name="taskId" th:value="${workFlow.taskId}" type="hidden">
        <input id="processInstanceId" name="processInstanceId" th:value="${workFlow.processInstanceId}" type="hidden">
        <input id="businessGuid" name="businessGuid" th:value="${workFlow.businessGuid}" type="hidden">
        <input id="processCode" name="processCode" th:value="${workFlow.processCode}" type="hidden">

        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label">项目编号：</label>
                                    <div class="col-sm-8 form-control-static"
                                         th:utext="${project.projectCode}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">项目名称：</label>
                                    <div class="col-sm-8 form-control-static"
                                         th:utext="${projectInitialReport.projectName}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">项目负责人：</label>
                                    <div class="col-sm-8 form-control-static" th:utext="${projectInitialReport.fzrName}">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">联系电话：</label>
                                    <div class="col-sm-8 form-control-static"
                                         th:utext="${projectInitialReport.fzrContactTel}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <label class="col-sm-2 control-label is-required">项目负责单位：</label>
                            <div class="col-sm-10"
                                 th:include="/component/selectOrg::init(see=true,isrequired=true,orgCodeId='fzdwCode',orgNameId='fzdwName',value=${projectInitialReport.fzdwCode},selectType='M')">
                            </div>
                        </div>

                        <br/>
                        <div class="row ">
                            <label class="col-sm-2 control-label is-required">项目参与单位：</label>
                            <div class="col-sm-10"
                                 th:include="/component/selectOrg::init(see=true,isrequired=true,orgCodeId='cydwCode',orgNameId='cydwName',value=${projectInitialReport.cydwCode},selectType='M')">
                            </div>
                        </div>

                        <br/>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">项目开始日期：</label>
                                    <div class="col-sm-8">
                                        <div th:include="/component/date :: init(see=true,isrequired=true,id='projectStartDate', name='projectStartDate',strValue=${project.projectStartDate})"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-4 control-label is-required">项目结束日期：</label>
                                    <div class="col-sm-8">
                                        <div th:include="/component/date :: init(see=true,isrequired=true,id='projectEndDate', name='projectEndDate',strValue=${project.projectEndDate})"></div>
                                    </div>
                                </div>
                            </div>
                        </div>


                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">

                                    <label class="col-sm-4 control-label">项目详细信息：</label>
                                    <div class="col-sm-8 form-control-static"><a
                                            style=" text-decoration: underline; color: #0000cc"
                                            th:onclick="viewXmlx()">点击此处查看</a></div>

                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">

                                    <label class="col-sm-4 control-label">立项任务书：</label>
                                    <div class="col-sm-8 form-control-static"><a
                                            style=" text-decoration: underline; color: #0000cc"
                                            th:onclick="viewLxrws()">点击此处查看</a></div>

                                </div>
                            </div>
                        </div>


                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx5"
                                               aria-expanded="false" class="collapsed">
                        项目变更内容及原因&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>

                <div id="jbxx5" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="row ">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <div class="col-sm-12">
                                                <textarea rows="12" name="changeContent" class="form-control"
                                                          required>[[${xmmodify.changeContent}]]</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--内容end-->

                    </div>
                </div>
            </div>
        </div>

        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx6"
                                               aria-expanded="false" class="collapsed">
                        相关附件&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>

                <div id="jbxx6" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">附件：</label>
                                <div class="col-sm-10"
                                     th:include="/component/attachment :: init(sourceId=${xmmodify.recordGuid},sourceModule='KTJC_XMBG_XGFJ',name='xmbgXgfjId',id='xmbgXgfjId')"></div>
                            </div>
                        </div>
                        <!--内容end-->

                        <br/>
                        <br/>
                        <br/>
                    </div>
                </div>
            </div>
        </div>

        <!-- 流程跟踪3 -->
        <th:block th:include="component/wfCommentList3 :: init(businessId=${workFlow.businessGuid})"/>
        <th:block th:include="include :: includeTaskHistoryList(instanceId=${workFlow.processInstanceId})"></th:block>

    </form>
    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-primary"
                onclick="zcXmbg()">
            <i class="fa fa-hdd-o"></i>&nbsp;暂存
        </button>

        <th:block th:include="component/wfSubmitOne :: init(taskId=${workFlow.taskId},callback=submitHandler)"/>

        <button type="button" class="btn btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>&nbsp;返 回
        </button>

    </div>
    <!--按钮区end-->
</div>
<script th:inline="javascript">
    var prefix = ctx + "ktjc/xmmodify";

    $("#form-xmmodify-edit").validate({
        focusCleanup: true
    });

    //暂存项目变更
    function zcXmbg() {
        var config = {
            url: prefix + "/zcXmbg",
            type: "post",
            dataType: "json",
            data: $('#form-xmmodify-edit').serialize(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    $("#recordGuid").val(result.data.xmbgGuid);
                    $.modal.alertSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            }
        };
        $.ajax(config)
    }

    function submitHandler(transitionKey) {
        if ($.validate.form()) {
            if ($.modal.confirm("确认要提交吗?", function () {
                var data = $('#form-xmmodify-edit').serialize();
                data += "&transitionKey=" + transitionKey;
                $.operate.saveTabAlert(prefix + "/sumbitXmbgWf", data);
            })) ;
        }
    }

    //查看立项报告
    function viewXmlx() {
        url = ctx + "ktjc/projectInitialReport/detail/" +[[${projectInitialReport.recordGuid}]];
        $.modal.openTab("立项报告详情", url);
    }

    //立项任务书
    function viewLxrws() {
        url = ctx + "ktjc/notice/detail/" + [[${tktjcNotice.recordGuid}]];
        $.modal.openTab("立项任务书详情", url);
    }


</script>
</body>
</html>