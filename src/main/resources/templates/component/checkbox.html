

<!--
name:checkbox name 默认 checkbox
id  每一个选项id为id+选项value   id默认为name值
callback： 点击时回调函数名
see:回显(true,false) 默认false
isrequired:是否必填 默认false
value: 回显值
dictCode businessType: 数据字典key
notShowValue 不显示值（选项）
-->


<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	name=${name==null?'checkbox':name},
               	 	id=${id==null?name:id},
               	 	arrayValue=${value==null?null:#strings.arraySplit(value, ',')},
               	 	arrayNotValue=${notShowValue==null?null:#strings.arraySplit(notShowValue, ',')}">

        <th:block th:if="${!see}">
            <label class="check-box" th:unless="${arrayNotValue!=null && #lists.contains(arrayNotValue,dict.dictValue)}"   th:each="dict : ${@dict.getDictList(businessType,dictCode)}">
                <input  th:unless="${arrayNotValue!=null && #lists.contains(arrayNotValue,dict.dictValue)}" type="checkbox"  th:id="${id+dict.dictValue}"  th:name="${name}"
                       th:value="${dict.dictValue}"    th:attr="checked=${arrayValue!=null && #lists.contains(arrayValue,dict.dictValue)}" th:text="${dict.dictName}"></label>
            <label th:for="${name}" class="error"></label>
        </th:block>
            <div th:unless="${!see}"  class="form-control-static" th:utext="${@dict.getDictName(businessType,dictCode,value)}"></div>

        <script th:inline="javascript" th:if="${!see}">
            if ([[${isrequired!=null && isrequired}]]) {
                $("input:checkbox[name='"+[[${name}]]+"']").attr("required", "");
            }

        </script>
        <script th:inline="javascript" th:if="${callback!=null}">

            $('input:checkbox[name="'+[[${name}]]+'"]').on('ifClicked', function(event){
                var callback= [[${callback}]]
                eval(callback+'("'+$(this).val()+'",'+!$(this).is(":checked")+')');
            });

        </script>
    </th:block>

</div>


