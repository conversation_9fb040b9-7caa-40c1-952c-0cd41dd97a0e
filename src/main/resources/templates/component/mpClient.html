<!-- 
id 客商名称输入对应的input id
callback 回调函数
-->
<div th:fragment="init">
	<button type="button" class="btn btn-primary" th:onclick="doCheckClient([[${callback}]])" id="checkKeShang" title="检测客商">
		<i class="fa fa-share"></i>&nbsp;获取客商信息
	</button>
	<script type="text/javascript" th:inline="javascript">
	function doCheckClient(callback){
		var operator = [[${@UserUtil.getLoginName()}]];
		if(!operator){
			$.modal.alertWarning("获取不到当前用户");
			return false;
		}
		var id = [[${id}]];
		if(!id){
			$.modal.alertWarning("请传入客商名称对应的id");
			return false;
		}
		var clientName = $('#'+id).val();
		var userNumId = [[${userNum}]];
		var userNum = $('#'+userNumId).val();
		
		if(!clientName&&!userNum){
			$.modal.alertWarning("客商名称和客商代码不能都为空");
			return false;
		}
		
		var url = ctx + "service";
		var jsonData = {};
		jsonData['operator'] = operator;
		jsonData['serviceId'] = 'S_MP_KS_01';
		jsonData['sysCode'] = '8P';
		jsonData['clientName'] = clientName;//客商中文代码
		jsonData['userNum'] = userNum;//客商代码
		var config = {
			url: ctx + "service",
			type: "post",
			cache: false,
			dataType: "json",
			data: JSON.stringify(jsonData),
			contentType: "application/json",
			beforeSend: function () {
				$.modal.loading("正在处理中，请稍后...");
			},
			success: function (result) {
				var status = result.__sys__.status;
				var msg = result.__sys__.msg;
				if(status>0){
					var clientDTO = result.clientDTO;
					if(!clientDTO){
						$.modal.alertError(msg);
					}else{
						if('queryFail'==clientDTO.synStatus){//查询不到信息
							$.modal.confirm(msg+",是否创建客商?", function () {
								//$.modal.alertWarning(clientDTO.clientId);
								if(result.createClientUrl){
									$.modal.openTab("创建客商", result.createClientUrl);			
								}else{
									$.modal.alertWarning('获取不到创建客商的链接');
								}											
							});	
							$.modal.closeLoading();
						}else if('notOne'==clientDTO.synStatus){//信息不唯一
							$.modal.alertWarning(msg+',请重新输入客商全称');
							$.modal.closeLoading();
						}else if('dfk'==clientDTO.synStatus){//待客商反馈
							$.modal.alertWarning(msg+',请等待10秒后重新点击');
							$.modal.closeLoading();							
						}else if('noBank'==clientDTO.synStatus){//无账号信息
							//$.modal.alertSuccess("【"+clientDTO.clientName+"】已存在，无需添加。");
							$.modal.confirm("无银行账号信息,是否去补充?", function () {
								//$.modal.alertWarning(clientDTO.clientId);
								if(result.createClientBankUrl){
									$.modal.openTab("客商账号信息", result.createClientBankUrl);			
								}else{
									$.modal.alertWarning('获取不到创建客商账号信息的链接');
								}											
							});	
							$.modal.closeLoading();
							var jsonData = JSON.stringify(clientDTO);
							eval(callback+"("+jsonData+")");
						}else if('success'==clientDTO.synStatus){//成功
							//$.modal.alertSuccess("【"+clientDTO.clientName+"】已存在，无需添加。");
							$.modal.closeLoading();
							var jsonData = JSON.stringify(clientDTO);
							eval(callback+"("+jsonData+")");
						}else{//启动错误
							$.modal.alertWarning(msg);
							$.modal.closeLoading();
						}
					}
				}else{
					$.modal.alertError("请求失败:"+msg);
					$.modal.closeLoading();
				}
			}
		};
		$('#checkKeShang').attr("disabled",true);
		
		$.ajax(config);	
		
		setTimeout(function () {
			$.modal.closeLoading();
			$('#checkKeShang').attr("disabled",false);
		},11000);
	}
	</script>
</div>