<!-- 选择组织封装

orgCodeId:orgCode name id
orgNameId:orgName name id
selectType:S 单选 M 多选 默认S
level:组织层级 -1只显示公司
showLevel:显示组织层级
callback: 回调函数
isrequired:是否必填 默认false
value: 当前值
see:是否查看

-->

<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	orgCodeId=${orgCodeId==null?'orgCodeId':orgCodeId},
               	 	orgNameId=${orgNameId==null?'orgNameId':orgNameId},
                    selectType=${selectType==null?'S':selectType}
               	 	">


        <th:block th:if="${!see}">
            <div th:class="'input-group'" th:onclick="choiceOrg([[${orgCodeId}]],[[${orgNameId}]],[[${selectType}]],[[${level}]],[[${orgCode}]],[[${showLevel}]],[[${callback}]])">
                <input th:name="${orgCodeId}" th:id="${orgCodeId}" type="hidden" th:value="${value}" /> <input
                    th:name="${orgNameId}" th:id="${orgNameId}" th:value="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(value)}" class="form-control  detailOrgOrUser" type="text"
                    th:required="${isrequired!=null && isrequired}"  readonly>
                <span class="input-group-addon"  ><i
                        class="fa fa-search " ></i></span>
            </div>
        </th:block>
        <th:block th:unless="${!see}">
            <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(value)}"></div>
            <input th:name="${orgCodeId}" th:id="${orgCodeId}" type="hidden" th:value="${value}" /> <input
                th:name="${orgNameId}" th:id="${orgNameId}" th:value="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(value)}" class="form-control" type="hidden"/>
        </th:block>


    </th:block>
</div>