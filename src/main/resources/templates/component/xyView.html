
<!-- 附件上传
ywId
moduleCode
columns

-->
<div th:fragment="init">
    <th:block  th:with="thisId=${#numbers.formatDecimal(T(java.lang.Math).floor(T(java.lang.Math).random()*10000),1,0)},xyData=${xyData==null?T(com.baosight.bscdkj.utils.XyUtil).getXyList(ywId,moduleCode):xyData}">
        <th:block th:if="${!#lists.isEmpty(xyData)}">
            <div class="panel-group" th:id="${thisId+'accordion'}" role="tablist"
                 aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version"
                               th:href="${'#'+thisId+'PSJBXX'}" aria-expanded="false" class="collapsed">[[${titleName==null?'经济效益信息':titleName}]]
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <span class="pull-right"><i class="fa fa-chevron-down"
                                                            aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div th:id="${thisId+'PSJBXX'}" class="panel-collapse collapse in"
                         aria-expanded="false">
                        <div class="panel-body">
                            <div class="form-group">
                                <table class="layui-table" th:id="${thisId+'PSTable'}">

                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <script th:inline="javascript">
                $(function() {
                    var options = {
                        data:[[${xyData}]],
                        id:[[${thisId+'PSTable'}]],
                        uniqueId:'recordId',
                        pagination:false,
                        showSearch:false,
                        showRefresh:false,
                        showColumns:false,
                        showToggle:false
                    };

                    var  columns= [
                        {
                            field: 'no',
                            title: '序号',
                            sortable: true,
                            align: "center",
                            width: 40,
                            formatter: function (value, row, index) {
                                //获取每页显示的数量
                                var pageSize=$('#'+[[${thisId+'PSTable'}]]).bootstrapTable('getOptions').pageSize;
                                //获取当前是第几页
                                var pageNumber=$('#'+[[${thisId+'PSTable'}]]).bootstrapTable('getOptions').pageNumber;
                                //返回序号，注意index是从0开始的，所以要加上1
                                return pageSize * (pageNumber - 1) + index + 1;
                            }
                        },
                        {
                            field: 'xytxns',
                            title: '效益开始日期'
                        },
                        {
                            field: 'xytxne',
                            title: '效益结束日期'
                        },
                        {
                            field: 'workFlow.currentActivityName',
                            title: '当前节点'
                        },
                        {
                            field: 'workFlow.currentOperator',
                            title: '当前操作人'
                        },
                        {
                            field: 'jjxy',
                            title: '经济效益(万元)'
                        },
                        {
                            field: 'tgjjxy',
                            title: '计奖效益(万元)'
                        },
                        {
                            field: 'jsyj',
                            title: '效益公式'
                        }];
                    if(isFunction("addXYColumn")){
                        addXYColumn(columns);
                    }
                    columns.push({
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="seeXY(\'' + row.recordId + '\')"><i class="fa fa-plus"></i>查看效益信息</a> ');
                            return actions.join('');
                        }
                    })
                    options["columns"]=columns;
                    $.table.init(options);

                });

                function isFunction(funcName){
                    try {
                        if(typeof(eval(funcName))=="function"){
                            return true;
                        }
                    }catch(e){

                    }
                    return false;
                }
                function seeXY(recordId){
                    $.modal.openTab("查看评审信息",ctxYWZT+"web/KJXYSHOW?spId="+recordId+"&activityCode=Manual3");
                }
            </script>

        </th:block>

    </th:block>
</div>
