<!-- 选择文档属性封装

codeId:code name id
nameId:name name id
topCode:
callback: 回调函数
isrequired:是否必填 默认false
value: 当前值
see:是否查看

-->

<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	topCode=${topCode!=null?topCode:'ROOT'},
               	 	codeId=${codeId==null?'codeId':codeId},
               	 	nameId=${nameId==null?'nameId':nameId}
               	 	">


        <th:block th:if="${!see}">
            <div th:class="'input-group'" th:onclick="choiceNode([[${codeId}]],[[${nameId}]],[[${topCode}]],[[${orgCode}]],[[${callback}]])">
                <input th:name="${codeId}" th:id="${codeId}" type="hidden" th:value="${value}" /> <input
                    th:name="${nameId}" th:id="${nameId}" th:value="${T(com.baosight.bscdkj.kj.wd.util.KJWDUtil).getAttrPathName(value)}" class="form-control  detailOrgOrUser" type="text"
                    th:required="${isrequired!=null && isrequired}" readonly>
                <span class="input-group-addon"  ><i
                        class="fa fa-search " ></i></span>
            </div>
        </th:block>
        <th:block th:unless="${!see}">
            <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.kj.wd.util.KJWDUtil).getAttrPathName(value)}"></div>
            <input th:name="${codeId}" th:id="${codeId}" type="hidden" th:value="${value}" /> <input
                th:name="${nameId}" th:id="${nameId}" th:value="${T(com.baosight.bscdkj.kj.wd.util.KJWDUtil).getAttrPathName(value)}" class="form-control" type="hidden"/>
        </th:block>

        <script th:inline="javascript" type="text/javascript">
            var codeId = "codeId";
            var nameId = "nameId";

            function choiceNode(codeInputId, nameInputId, topCode, orgCode, callback) {
                codeId = codeInputId;
                nameId = nameInputId;
                var url = ctx + "kjwd/docAttrConfig/selectNodeList?topCode=" + topCode;
                if (!(orgCode === undefined) && orgCode != null) {
                    url += "&code=" + orgCode;
                }
                if (!(callback === undefined) && callback != null) {
                    url += "&callback=" + callback;
                }
                url += "&values=" + $("#" + codeId).val();
                var options = {
                    title: '选择属性',
                    width: "380",
                    height: '500',
                    url: url,
                    callBack: choiceNodeCallback
                };
                $.modal.openOptions(options);
            }

            function choiceNodeCallback(index, layero) {
                var tree = layero.find("iframe")[0].contentWindow.$._tree;
                var body = layer.getChildFrame('body', index);
                layero.find("iframe")[0].contentWindow.saveCheck();
                $("#" + codeId).val(body.find('#treeId').val());
                $("#" + nameId).val(body.find('#treeName').val());
                layer.close(index);
            }
        </script>
    </th:block>
</div>