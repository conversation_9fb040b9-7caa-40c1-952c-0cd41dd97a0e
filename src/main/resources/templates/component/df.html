<!-- 选择组织封装

bs: 模板code
bizGuid:业务id
dfUser: 用户
see:是否查看
sumId:合计id
-->

<div th:fragment="init">
    <th:block  th:if="${bizGuid!=null && bs!=null} " th:with="thisId=${#numbers.formatDecimal(T(java.lang.Math).floor(T(java.lang.Math).random()*10000),1,0)}">
        <div  th:id="${thisId}">

        </div>
        <script th:inline="javascript">
            var ggmkDfUrl=ctxGGMK+"mpps/maintain/getDf?bs="+[[${bs}]]+"&bizGuid="+[[${bizGuid}]];
            // var ggmkDfUrl="http://localhost:8080/demo_dm_ggmk/"+"mpps/maintain/getDf?bs="+[[${bs}]]+"&bizGuid="+[[${bizGuid}]];
            if([[${dfUser!=null}]]){
                ggmkDfUrl+="&dfUser="+[[${dfUser}]];
            }
            if([[${titleName!=null}]]){
                ggmkDfUrl+="&titleName="+[[${titleName}]];
            }
            if([[${see!=null}]]){
                ggmkDfUrl+="&see="+[[${see}]]
            }
            $("#"+[[${thisId}]]).load(ggmkDfUrl);

            $("#"+[[${thisId}]]).on("blur",".inputFs input",function(){
                var sumId=[[${sumId==null?'dfsum':sumId}]];
                $("#"+sumId).val(0);
                var dfsum = 0;
                $("#"+[[${thisId}]]).find(".inputFs").find("input").each(function () {
                    var val = $(this).val();
                    if (val != null && val != '') {
                        var num = Number(val);
                        dfsum += num;
                    }
                })


                $("#"+sumId).val(dfsum);
                $("#"+sumId).change();

            })

            function serializeObject() {
                // 处理结果对象
                var result = {};

                $("#"+[[${thisId}]]).find("input").each(function(){
                    result[$(this).attr("name")] = $(this).val();
                })
                return result;
            }

            function ggmkDfSubmit() {

                    var config = {
                        url: ctxGGMK + "mpps/maintain/submitDf",
                        type: "post",
                        dataType: "json",
                        contentType: "application/json",
                        data: JSON.stringify(serializeObject()),
                        beforeSend: function () {
                            $.modal.loading("正在处理中，请稍后...");
                        },
                        success: function (result) {

                        }
                    };
                    $.ajax(config)



            }

        </script>
    </th:block>
</div>