
<!--
name:input name 默认 radio
id:radio  每一个id为 id+选择value   默认 name 值
callback： 点击时回调函数名
see:回显(true,false) 默认false
isrequired:是否必填 默认false
value: 回显值
dictCode  businessType: 数据字典key
notShowValue 不显示值（选项）
-->


<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	name=${name==null?'radio':name},
               	 	id=${id==null?name:id},
               	 	arrayNotValue=${notShowValue==null?null:#strings.arraySplit(notShowValue, ',')}">

        <th:block th:if="${!see}">
            <div class="radio-box"
                 th:each="dict : ${@dict.getDictList(businessType,dictCode)}" th:unless="${arrayNotValue!=null && #lists.contains(arrayNotValue,dict.dictValue)}">
                <input   type="radio" th:id="${id+dict.dictValue}"  th:name="${name}"
                       th:value="${dict.dictValue}"   th:attr="checked=${value==dict.dictValue?true:false}" > 
                <label th:for="${'type_'+dictCode}" th:text="${dict.dictName}" ></label>              
            </div>
            <label th:for="${name}" class="error"></label>
        </th:block>
            <div th:unless="${!see}"  class="form-control-static" th:utext="${@dict.getDictName(businessType,dictCode,value)}"></div>


        <script th:inline="javascript" th:if="${!see}">
            if ([[${isrequired!=null && isrequired}]]) {
                $("input:radio[name='"+[[${name}]]+"']").attr("required", "");
            }

        </script>
        <script th:inline="javascript" th:if="${callback!=null}">

            $('input:radio[name="'+[[${name}]]+'"]').on('ifClicked', function(event){
                var callback= [[${callback}]]

                eval(callback+'("'+$(this).val()+'")');
            });

        </script>
    </th:block>

</div>




