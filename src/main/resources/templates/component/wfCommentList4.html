<!-- 
	流程审批履历
	传阅的履历不显示
输入参数:
	businessId 业务ID
-->
<div th:fragment="init">
	<div class="panel-group" aria-multiselectable="true" th:if="${not #strings.isEmpty(businessId)}">
		<th:block th:with="commentList=${@SWorkFlowUtil.getCommentList4(businessId)}">
			<div th:if="${commentList.size()>0}" class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#spyj_commentList" aria-expanded="false" class="collapsed">
							审批履历
							<span class="pull-right">
								<i class="fa fa-chevron-down" aria-hidden="true"></i>
							</span>
							<a th:if="${@UserUtil.isAdmin()}" th:onclick="processAdmin([[${businessId}]])" style="text-decoration:underline;">管理员操作</a>
						</a>
					</h4>
				</div>
				<div id="spyj_commentList" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="form-group">
							<div class="col-sm-12">
								<table class="table table-bordered table-hover table-striped">
									<thead class="treetable-thead">
										<tr>
											<th class="taskName_cls" style="width: 30%;">任务名称</th>
											<th class="opinion_cls">审批意见</th>
										</tr>
									</thead>
									<tbody>
										<th:block th:if="${commentList.size()<3}">
											<th:block th:each="comment : ${commentList}" >
												<tr>
													<td th:text="${comment.taskName} +'-'+ ${comment.assigneeFullname} +'-'+ ${comment.endTime?:''}"></td>
													<td th:utext="${comment.opinion}"></td>
												</tr>
											</th:block>
										</th:block>
										<th:block th:if="${commentList.size()>2}">
											<th:block th:each="comment,commentStat : ${commentList}" >
												<th:block th:if="${commentStat.index<2}">
													<tr>
														<td th:text="${comment.taskName} +'-'+ ${comment.assigneeFullname} +'-'+ ${comment.endTime?:''}"></td>
														<td th:text="${comment.opinion}"></td>
													</tr>
												</th:block>
												<th:block th:if="${commentStat.index==2}">
												<tr>
													<td colspan="2" align="right">
														<a onclick="moreComment()" href="javascript:void(0);">
															<div id="moreCommentId" class="form-read-more">
																<img th:src="@{/img/moreplus.png}">
																更多
															</div>
														</a>
													</td>
												</tr>
												</th:block>
												<th:block th:if="${commentStat.index>=2}">
													<tr class="moreComment" style="display: none;">
														<td th:text="${comment.taskName} +'-'+ ${comment.assigneeFullname} +'-'+ ${comment.endTime?:''}"></td>
														<td th:text="${comment.opinion}"></td>
													</tr>
												</th:block>
											</th:block>
										</th:block>								
									</tbody>							
								</table>
							</div>
						</div>
					</div>
				</div>
			</div>
		</th:block>
	</div>
	<script type="text/javascript" th:inline="javascript">
	function moreComment(){
		if($('.moreComment').is(':hidden')){
			$('.moreComment').show();
			var imgSrc = [[@{/img/moreminus.png}]];
			$('#moreCommentId').html("<img src='"+imgSrc+"'/>隐藏");
		}else{
			$('.moreComment').hide();
			var imgSrc = [[@{/img/moreplus.png}]];
			$('#moreCommentId').html("<img src='"+imgSrc+"'/>更多");
		}
	}

	function processAdmin(businessId){
		var url = ctxGGMK + "web/MPWF0010?businessId="+businessId;
		window.open(url);
	}
	</script>
</div>
