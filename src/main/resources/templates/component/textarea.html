<!--
placeholder:默认''
maxlength:默认250
rows:默认5
value:回显值
name:input name 默认 textareaInput
id:input id 默认 name值
class：input class
isrequired:是否必填 默认false
see:默认false
-->
<div th:fragment="init">
    <th:block
            th:with="
               	 	placeholder=${placeholder==null?'':placeholder},
               	 	maxlength=${maxlength==null?'250':maxlength},
               	 	rows=${rows==null?'5':rows},
               	 	value=${value==null?null:value.trim()},
               	 	name=${name==null?'textareaInput':name},
               	 	id=${id==null?name:id},
               	 	class=${class==null?name:class},
               	 	isrequired=${isrequired==null?false:isrequired},
               	 	see=${see==null?false:see}
               	 	">

        <textarea
                style="padding: 0;border: 1px solid #e5e6e7;border-radius: 4px;"
                th:id="${id}"
                th:placeholder="${placeholder}"
                th:class="${class}"
                th:name="${name}"
                th:maxlength="${maxlength}"
                th:rows="${rows}"
                th:text="${value}"
                th:readonly="${see}"
        ></textarea>
        <div class="tip-comment">
            <span th:id="${id}+'_textCount'">0</span>
            /
            <span th:text="${maxlength}"></span>
        </div>
        <script th:inline="javascript" type="text/javascript">
            if ([[${isrequired!=null && isrequired}]]) {
                $("#" + [[${id}]]).attr("required", "");
            }
            $("#" + [[${id}]]).keyup(function () {
                $("#" + [[${id}]] + '_textCount').html(this.value.length);
            });
            $("#" + [[${id}]]).keyup();
        </script>
    </th:block>
</div>