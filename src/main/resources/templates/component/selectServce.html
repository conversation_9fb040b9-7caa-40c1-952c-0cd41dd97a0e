<!-- 选择组织封装

labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认附件
divClass:指定附件的div 的class 默认col-sm-8
orgCodeId:orgCode name id
orgNameId:orgName name id
selectType:S 单选 M 多选 默认S
level:组织层级 -1只显示公司
showLevel:显示组织层级
callback: 回调函数
isrequired:是否必填 默认false
value: 当前值
see:是否查看

-->

<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	orgCodeId=${orgCodeId==null?'orgCodeId':orgCodeId},
               	 	orgNameId=${orgNameId==null?'orgNameId':orgNameId},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择服务：':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
                    selectType=${selectType==null?'S':selectType}

               	 	">

        <th:block th:if="${!see}">
            <div th:class="'input-group'+' '+${divClass}" th:onclick="choiceService([[${orgCodeId}]],[[${orgNameId}]],[[${selectType}]],[[${level}]],[[${showLevel}]],[[${callback}]])">
                <input th:name="${orgCodeId}" th:id="${orgCodeId}" type="hidden" th:value="${value}" /> <input
                    th:name="${orgNameId}" th:id="${orgNameId}" th:value="${T(com.baosight.bscdkj.ky.sb.utils.TradeServiceUtil).getServiceName(value)}" class="form-control  detailOrgOrUser" type="text"
                    th:required="${isrequired!=null && isrequired}"  readonly>
                <span class="input-group-addon"  ><i
                        class="fa fa-search " ></i></span>
            </div>
        </th:block>
        <th:block th:unless="${!see}">
            <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.ky.sb.utils.TradeServiceUtil).getServiceName(value)}"></div>
            <input th:name="${orgCodeId}" th:id="${orgCodeId}" type="hidden" th:value="${value}" /> <input
                th:name="${orgNameId}" th:id="${orgNameId}" th:value="${T(com.baosight.bscdkj.ky.sb.utils.TradeServiceUtil).getServiceName(value)}" class="form-control" type="hidden"/>
        </th:block>


    </th:block>
</div>




    <div th:fragment="choiceService">
        <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	orgCodeId=${orgCodeId==null?'orgCodeId':orgCodeId},
               	 	orgNameId=${orgNameId==null?'orgNameId':orgNameId},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择服务：':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
                    selectType=${selectType==null?'S':selectType}

               	 	">

            <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}"></label>
            <div th:class="${divClass}" th:if="${see!=null && !see}">
                <div class="input-group" th:onclick="choiceService([[${orgCodeId}]],[[${orgNameId}]],[[${selectType}]],[[${level}]],[[${orgCode}]],[[${showLevel}]],[[${callback}]])">
                    <input th:id="${orgCodeId}" th:name="${orgCodeId}" th:value="${value}" type="hidden" /> <input
                        class="form-control detailOrgOrUser" th:id="${orgNameId}" th:name="${orgNameId}"
                        th:value="${T(com.baosight.bscdkj.ky.sb.utils.TradeServiceUtil).getServiceName(value)}" type="text"  autocomplete="off" th:required="${isrequired!=null && isrequired}" readonly>
                    <span class="input-group-addon "><i
                            class="fa fa-search "></i></span>
                </div>
            </div>

            <div class="form-control-static" th:unless="${!see}" th:utext="${T(com.baosight.bscdkj.ky.sb.utils.TradeServiceUtil).getServiceName(value)}"></div>
        </th:block>
    </div>






</div>
<!--选择服务 start-->

<div th:fragment="selectService">
    <script th:inline="javascript" type="text/javascript">
        var orgId = "orgId";
        var orgNameId = "deptName";

        function choiceService(orgCodeInputId, orgNameInputId, selectType, level, orgCode, showLevel, callback) {
            orgId = orgCodeInputId;
            orgNameId = orgNameInputId;
            if (selectType === undefined || selectType == null || selectType == '') {
                selectType = "S";
            }
            var url = ctx + "kysb/tradeService/selectServiceList?selectType=" + selectType;
            if (!(level === undefined) && level != null) {
                url += "&level=" + level;
            }
            if (!(showLevel === undefined) && showLevel != null) {
                url += "&showLevel=" + showLevel;
            }
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            url += "&values=" + $("#" + orgId).val();

            //debugger
            var options = {
                title: '选择服务',
                width: "580",
                height: '500',
                url: url,
                callBack: choiceServiceCallback
            };
            $.modal.openOptions(options);
        }

        function choiceServiceCallback(index, layero) {
            var tree = layero.find("iframe")[0].contentWindow.$._tree;
            var body = layer.getChildFrame('body', index);
            layero.find("iframe")[0].contentWindow.saveCheck();
            $("#" + orgId).val(body.find('#treeId').val());
            $("#" + orgNameId).val(body.find('#treeName').val());
            layer.close(index);
        }
</script>
</div>
    <!--选择服务 end-->