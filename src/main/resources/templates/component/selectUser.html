
<!-- 选择人员封装

labelName:label的text 选择人员
userCodeId:userCode name id
userNameId:userName name id
name 默认为 userCodeId
selectType:S 单选 M 多选 默认S
callback: 回调函数
isrequired:是否必填 默认false
value: 当前值
see:是否查看

-->
<div th:fragment="init">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	userCodeId=${userCodeId==null?'userCodeId':userCodeId},
               	 	name=${name==null?userCodeId:name},
               	 	userNameId=${userNameId==null?'userNameId':userNameId},
               	 	labelName=${labelName==null?'选择人员：':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
                    selectType=${selectType==null?'S':selectType}
               	 	">
        <th:block th:if="${!see}">
            <div th:class="input-group" th:onclick="choiceUser([[${userCodeId}]],[[${userNameId}]],[[${selectType}]],[[${orgCode}]],[[${callback}]])">
                <input th:name="${name}" th:id="${userCodeId}" type="hidden" th:value="${value}" /> 
                <input th:name="${userNameId}" th:id="${userNameId}" th:value="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(value)}" class="form-control detailOrgOrUser" type="text"
                    th:required="${isrequired!=null && isrequired}" readonly >
                <span class="input-group-addon " ><i class="fa fa-search "></i></span>
            </div>
        </th:block>
        <th:block th:unless="${!see}">
            <div class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(value)}"></div>
            <input th:name="${userCodeId}" th:id="${userCodeId}" type="hidden" th:value="${value}" /> <input
                th:name="${userNameId}" th:id="${userNameId}"  type="hidden" th:value="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(value)}" class="form-control" />
        </th:block>
    </th:block>
</div>