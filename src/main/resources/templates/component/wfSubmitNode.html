<!-- 
流程提交 用于单任务
	多个转移会让用户选择那个transitionKey,提交给退回节点transitionKey为jumpReturnNode@@-[节点编码]
输入参数:
taskId 任务ID
callback 回调函数,第一个参数固定为用户选择的提交节点

title 鼠标提示
-->
<div th:fragment="init">
	<th:block th:with="transitionS=${@SWorkFlowUtil.getNextTransitionForSubmitOne(taskId,null)}">
		<th:block th:if="${transitionS?.size()>0}">
			<th:block th:each="transition : ${transitionS}" >
				<!-- <th:block th:with="title=${title!=nul ? title:transition.nodeName}"> -->
				<th:block th:with="title=${title!=nul ? title:''}">
					<button type="button" class="btn btn-primary" th:onclick="doWorkFlowSubmit([[${transition.transitionKey}]],[[${callback}]])" th:attr="title=${transition.nodeName}" data-toggle="tooltip" data-html="true">
						<i class="fa fa-share"></i>&nbsp;[[${transition.transitionName}]]
					</button>
				</th:block>
			</th:block>
		</th:block>
	</th:block>
	<script type="text/javascript" th:inline="javascript">
	$(document).ready(function () {
        $('[data-toggle="tooltip"]').tooltip();
    });

	function doWorkFlowSubmit(transitionKey,callback){
		$("#workFlow_transitionKey").val(transitionKey);
		eval(callback+'("'+transitionKey+'")');
	}
	</script>
</div>