<!-- 平均分封装

moduleCode: 模板code 必填
bizGuid:业务id 必填
reviewId:评审要求主键 默认nul 查询最新一次评审
userType默认 null  查询人员类型平均分  zz 组长平均分  zy 组员平均分

-->

<div th:fragment="init">
    <th:block  th:if="${bizGuid!=null && moduleCode!=null} " th:with="thisId=${#numbers.formatDecimal(T(java.lang.Math).floor(T(java.lang.Math).random()*10000),1,0)} ">
        <div  th:id="${thisId}">

        </div>
        <script th:inline="javascript">
            var reviewId=[[${reviewId}]];
            var userType=[[${userType}]];

            var ggmkDfUrl=ctxGGMK+"web/MPPS06?moduleCode="+[[${moduleCode}]]+"&bizGuid="+[[${bizGuid}]];
            if(reviewId!=null){
                ggmkDfUrl=ggmkDfUrl +"&reviewId="+reviewId;
            }
            if(userType!=null){
                ggmkDfUrl=ggmkDfUrl +"&userType="+userType;
            }
            $("#"+[[${thisId}]]).load(ggmkDfUrl);
        </script>
    </th:block>
</div>