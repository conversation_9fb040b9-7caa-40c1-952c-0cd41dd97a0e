<!-- 
	流程相关参数
输入参数:
	workFlow, 通过 SWorkFlowUtil.getWorkFlowByTaskId(String taskId) 获取
-->
<div th:fragment="init">
	<input id="workFlow_taskId" name="workFlow.taskId" type="hidden" th:value="${workFlow?.taskId}">
	<input id="workFlow_operator" name="workFlow.operator" type="hidden" th:value="${workFlow?.operator}">
	<input id="workFlow_businessId" name="workFlow.businessId" type="hidden" th:value="${workFlow?.businessId}">
	<input id="workFlow_businessName" name="workFlow.businessName" type="hidden" th:value="${workFlow?.businessName}">
	<input id="workFlow_businessType" name="workFlow.businessType" type="hidden" th:value="${workFlow?.businessType}">
	<input id="workFlow_processCode" name="workFlow.processCode" type="hidden" th:value="${workFlow?.processCode}">
	<input id="workFlow_processInstanceId" name="workFlow.processInstanceId" type="hidden" th:value="${workFlow?.processInstanceId}">
	<input id="workFlow_currentActivity" name="workFlow.currentActivity" type="hidden" th:value="${workFlow?.currentActivity}">
	<input id="workFlow_currentActivityName" name="workFlow.currentActivityName" type="hidden" th:value="${workFlow?.currentActivityName}">
	<input id="workFlow_currentOperator" name="workFlow.currentOperator" type="hidden" th:value="${workFlow?.currentOperator}">
	<input id="workFlow_flowState" name="workFlow.flowState" type="hidden" th:value="${workFlow?.flowState}">
	
	<input id="workFlow_transitionKey" name="workFlow.transitionKey" type="hidden" >
	<input id="workFlow_userLabelM" name="workFlow.userLabelM" type="hidden" >
	<input id="workFlow_returnActivityKey" name="workFlow.returnActivityKey" type="hidden" >
	<input id="workFlow_jumpActivityKey" name="workFlow.jumpActivityKey" type="hidden" >
	<input id="workFlow_addTaskType" name="workFlow.addTaskType" type="hidden" >
	
</div>