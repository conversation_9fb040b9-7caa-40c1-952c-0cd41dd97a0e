
<!--
name:input name 默认 fwb
id:fwb  每一个id为 id+选择value   默认 name 值
see:回显(true,false) 默认false
value: 回显值
height 富文本默认高度
isrequired:是否必填 默认false
height:高度

-->

<div th:fragment="js">
	<script type="text/javascript"  th:src="@{/ueditor/ueditor.config.js}" charset="utf-8"></script>
	<script type="text/javascript"  th:src="@{/ueditor/ueditor.all.js}" charset="utf-8"></script>
	<script type="text/javascript" th:src="@{/ueditor/lang/zh-cn/zh-cn.js}"></script>
	

</div>

<div th:fragment="init">
	<th:block th:with="thisId=${'fwb' + #numbers.formatDecimal(T(java.lang.Math).floor(T(java.lang.Math).random()*10000),1,0)},
    	            see=${see!=null&&see?true:false},
               	 	name=${name==null?'fwb':name},
               	 	id=${id==null?name:id},
               	 	height=${height==null?200:height}
">
		<th:block th:if="${!see}">
			<textarea th:id="${id}" th:name="${name}" th:required="${isrequired!=null && isrequired}" th:utext="${value}"></textarea>
			<label th:for="${name}" class="error" style="position: static;"></label>
		</th:block>
		<th:block th:unless="${!see}">
			<div  th:id="${thisId}" th:utext="${value}" class="form-control-static"></div>
			<script th:inline="javascript">
					// $("#"+[[${thisId}]]).find("span").css('font-family','').css('font','');
					// $("#"+[[${thisId}]]).find("p").css('font-family','').css('font','');
			</script>
		</th:block>
		<script th:inline="javascript" th:if="${!see}">
			UE.getEditor(''+[[${id}]],{
				serverUrl:ctx+"attachment/fwb",
				initialFrameHeight: [[${height}]], // 高度
				autoHeightEnabled: false,//是否自动扩充宽度
				wordCount:false,//是否统计数字
				zIndex: 100
			}).ready(function (){
				//WordPaster快捷键 Ctrl + V
				/*
				this.addshortcutkey({
					"wordpaster": "ctrl+86"
				});
				*/
			});
        </script>
	</th:block>
</div>