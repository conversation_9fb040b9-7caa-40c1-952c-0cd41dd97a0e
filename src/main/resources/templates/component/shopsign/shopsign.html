<!-- 选择psr封装

labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认附件
divClass:指定附件的div 的class 默认col-sm-8
id:brandId name id
name:name
isrequired:是否必填 默认false
value: 当前值
see:回显(true,false) 默认false
prefix:请求url地址前缀

-->
<!--<th:block th:include="/component/shopsign/shopsign :: init(id='psr', name='psr')" ></th:block>-->
<!-- 该组件会返回一个JSON字符串在隐藏的input上，如果需要存在list上需通过JSON.parse方法转换成list。-->
<!--实例-->
<!-- PSR信息 -->
<div th:fragment="init">
    <th:block th:with="
                  see=${see!=null&&see?true:false},
                  labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
                  labelName=${labelName==null?'psrNum：':labelName},
                  divClass=${divClass==null?'col-sm-8':divClass},
                  id=${id==null?'psr':id},
                  prefix=${prefix==null?'kycg/shopsign':prefix},
                  name=${name==null?'psr':name}	">

        <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}"
               th:text="${labelName}"></label>

        <div th:class="col-sm-9">
            <th:block th:if="${!see}">
                <div th:class="'input-group'+' '+${divClass}">
                    <input
                            th:name="${name}" th:id="${id}" th:value="${value}" class="form-control psrNum"
                            type="hidden"
                            th:required="${isrequired!=null && isrequired}" th:onclick="addPsr()" readonly>
                    <button type="button" class="btn btn-primary"
                            onclick="addPsr()">
                        <i class="fa fa-edit"></i> 选 择
                    </button>
                </div>
                <div class="form-group">
                    <div class="col-sm-12">
                        <table id="bootstrap-table-brand"></table>
                    </div>
                </div>
            </th:block>
            <th:block th:unless="${!see}" th:if="${null!=value}">
                <div class="form-group">
                    <div class="col-sm-12">
                        <table id="bootstrap-table-brand-detail"></table>
                    </div>
                </div>
            </th:block>
        </div>


    </th:block>

    <script type="text/javascript" th:inline="javascript">
        var optionValue = "";

        //添加一个元素
        function addPsr() {
            optionValue = "add";
            var options = {
                title: 'psr数据信息',
                width: "1000",
                height: '600',
                url: ctx + [[${prefix}]] + "/toPage/add",
                callBack: choiceGFFlCallbacks
            };
            $.modal.openOptions(options);
        }

        //编辑一个元素
        var editObj = {}

        function edit(index) {
            optionValue = "edit";
            editObj = brand[index];
            editObj.idx = index;
            var options = {
                title: 'psr数据信息',
                width: "1000",
                height: '600',
                url: ctx + [[${prefix}]] + "/toPage/edit",
                callBack: choiceGFFlCallbacks
            };
            $.modal.openOptions(options);
        }


        function urlToObject(data) {
            var jsonData = {};
            for (var item in data) {
                var value = data[item].value;
                if (value != null && value != '') {
                    if (jsonData[data[item].name] != null && jsonData[data[item].name] != '' && jsonData[data[item].name] != undefined) {
                        jsonData[data[item].name] += ',' + data[item].value
                    } else {
                        jsonData[data[item].name] = data[item].value;
                    }

                }
            }
            return jsonData;
        };
        var brand = []

        function choiceGFFlCallbacks(index, layero) {
            var body = layer.getChildFrame('body', index);
            var data;
            if ("add" == optionValue) {
                data = urlToObject(body.find('#form-shopsignInfo-add').serializeArray());
                var isValidate=body.find('#form-shopsignInfo-add').validate().form();
                if(!isValidate){
                    return
                }
            } else {
                data = urlToObject(body.find('#form-shopsignInfo-edit').serializeArray());
                var isValidate=body.find('#form-shopsignInfo-edit').validate().form();
                if(!isValidate){
                    return
                }
            }
            var obj = {}
            for (var key in data) {
                obj[key] = data[key]
            }

            $.ajax({
                url: ctx + [[${prefix}]] + "/getPsrNum",
                type: "post",
                dataType: "json",
                contentType: 'application/json;charset=UTF-8',
                data: JSON.stringify(obj),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    if (result.code == '0') {
                        layer.close(index);
                        var psrNum = "";
                        //获取psrNum列表用，隔开
                        for (var i = 0; i < result.data.length; i++) {
                            var elemet = result.data[i]
                            psrNum = psrNum + "," + elemet.psr
                        }
                        psrNum = psrNum.slice(1)
                        obj.psrNum = psrNum;
                        if ("add" === optionValue) {
                            brand.push(obj);
                        } else {
                            brand[obj.idx] = obj
                        }
                        $('#' + [[${id}]]).val(JSON.stringify(brand));
                        initBrand(brand)
                    } else {
                        $.modal.msgError(result.msg);
                    }
                    $.modal.closeLoading();
                }
            })
        }

        //详情查看
        var options_brand_detail = {
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            id: 'bootstrap-table-brand-detail',
            data: [],
            columns: [
                {
                    title: '序号',
                    formatter: function (value, row, index) {
                        return index + 1
                    },
                },
                {
                    field: 'pzqfm',
                    title: '品种区分代码'
                },
                {
                    field: 'dpzdm',
                    title: '大品种代码'
                },
                {
                    field: 'shopsign',
                    title: '牌号',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a style="color: #00a0e9; text-decoration: underline" href="javascript:void(0)" onclick="detail(\'' + index + '\')" >' + value+  '</a>');
                        return actions.join('');
                    }
                }
            ]
        };

        function initBrand(list) {
            options_brand.data = list
            $.table.refreshOptions(options_brand, 'bootstrap-table-brand')
        }

        var options_brand = {
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            id: 'bootstrap-table-brand',
            data: [],
            columns: [
                {
                    title: '序号',
                    formatter: function (value, row, index) {
                        return index + 1
                    },
                },
                {
                    field: 'pzqfm',
                    title: '品种区分代码'
                },
                {
                    field: 'dpzdm',
                    title: '大品种代码'
                },
                {
                    field: 'shopsign',
                    title: '牌号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="edit(\'' + index + '\')"><i class="fa fa-edit"></i>修改</a>');
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="remove(\'' + index + '\')"><i class="fa fa-remove"></i>删除</a> ');
                        return actions.join('');
                    }

                }
            ]
        };

        //如果是查看
        if ([[${see}]] == true) {
            $.table.init(options_brand_detail);
        } else {
            $.table.init(options_brand);
        }
        if ([[${see}]] == true || [[${value}]] != null) {
            brand = [[${value}]];

            if ([[${see}]] == true) {
                options_brand_detail.data = [[${value}]];
                $.table.refreshOptions(options_brand_detail, 'bootstrap-table-brand-detail')
            } else {
                $('#' + [[${id}]]).val(JSON.stringify(brand));
                options_brand.data = [[${value}]];
                $.table.refreshOptions(options_brand, 'bootstrap-table-brand')
            }
        }

        //移除一个元素
        function remove(index) {
            $.modal.confirm("是否确认删除", function () {
                brand.splice(index, 1)
                $('#' + [[${id}]]).val(JSON.stringify(brand));
                initBrand(brand)
            })
        }


        //查看一个
        var detailObj = {};

        function detail(index) {
            detailObj = brand[index];
            detailObj.idx = index;
            var options = {
                title: 'psr数据信息',
                width: "1000",
                height: '600',

                url: ctx + [[${prefix}]] + "/toPage/detail"
            };
            this.openOptions(options);
        }

        //打开详情
        function openOptions(options) {
            var _url = $.common.isEmpty(options.url) ? "/404.html" : options.url;
            var _title = $.common.isEmpty(options.title) ? "系统窗口" : options.title;
            var _width = $.common.isEmpty(options.width) ? "800" : options.width;
            var _height = $.common.isEmpty(options.height) ? ($(window).height() - 50) : options.height;
            var _btn = ['<i class="fa fa-close"></i> 关闭'];
            var btnCallback = {};
            var index = layer.open($.extend({
                type: 2,
                maxmin: $.common.isEmpty(options.maxmin) ? true : options.maxmin,
                shade: 0.3,
                title: _title,
                fix: false,
                area: [_width + 'px', _height + 'px'],
                content: _url,
                shadeClose: $.common.isEmpty(options.shadeClose) ? true : options.shadeClose,
                skin: options.skin,
                btn: $.common.isEmpty(options.btn) ? _btn : options.btn,
                yes: options.yes,
                cancel: function () {
                    return true;
                }
            }, btnCallback));
        }
    </script>
</div>