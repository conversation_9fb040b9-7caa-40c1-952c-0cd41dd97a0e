<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改psr')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: ztree-js"/>

</head>
<body class="gray-bg">
<form class="form-horizontal m" id="form-shopsignInfo-add">
    <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false"
                       class="collapsed">PSR数据信息
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                    </a>
                </h4>
            </div>
            <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                <div class="panel-body">

                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">品种区分代码：</label>
                        <div class="col-sm-6">
                            <th:block
                                    th:include="/component/select :: init(isrequired=true,isfirst =true,id='pzqfm', name='pzqfm',businessType='KYCG',  dictCode='KYCG_PZQFM')"></th:block>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">大品种代码：</label>
                        <div class="col-sm-6">
                            <th:block
                                    th:include="/component/select :: init(isrequired=true,isfirst =true, id='dpzdm', name='dpzdm',businessType='KYCG',  dictCode='KYCG_DPZDM')"></th:block>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-3 control-label is-required">牌号：</label>
                        <div class="col-sm-6">
                            <input required id="shopsign" name="shopsign" class="form-control shopsign" type="text"
                                   onblur="getPsrMap(this)">
                        </div>
                    </div>
                    <!--                  <div class="form-group">-->
                    <!--                    <label class="col-sm-3 control-label">品种区分代码：</label>-->
                    <!--                    <div class="col-sm-9">-->
                    <!--                      <input name="varietyDivDesc" class="form-control varietyDivDesc" type="text" required readonly>-->
                    <!--                    </div>-->
                    <!--                  </div>-->
                    <!--                  <div class="form-group">-->
                    <!--                    <label class="col-sm-3 control-label">大品种代码：</label>-->
                    <!--                    <div class="col-sm-9">-->
                    <!--                      <input name="psr1Desc" class="form-control psr1Desc" type="text" required readonly>-->
                    <!--                    </div>-->
                    <!--                  </div>-->


                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">板卷类型：</label>
                        <div class="col-sm-9 plateCoilTypeCodeMap">
                        </div>
                    </div>
                    <!--                  <div class="form-group ifHide hide">-->
                    <!--                    <label class="col-sm-3 control-label">切边形态：</label>-->
                    <!--                    <div class="col-sm-9 trimmingFigureMap">-->
                    <!--                    </div>-->
                    <!--                  </div>-->
                    <!--                  <div class="form-group ifHide hide">-->
                    <!--                    <label class="col-sm-3 control-label">产品等级：</label>-->
                    <!--                    <div class="col-sm-9 prodGradeMap">-->
                    <!--                    </div>-->
                    <!--                  </div>-->
                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">产品分类：</label>
                        <div class="col-sm-9 prodCatCodeMap">
                        </div>
                    </div>

                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">产品技术标准：</label>
                        <div class="col-sm-9 prodTechStdMap">
                        </div>
                    </div>
                    <!--                  <div class="form-group ifHide hide">-->
                    <!--                    <label class="col-sm-3 control-label">不锈钢产品：</label>-->
                    <!--                    <div class="col-sm-9 susProdSeriesMap">-->
                    <!--                    </div>-->
                    <!--                  </div>-->
                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">表面结构：</label>
                        <div class="col-sm-9 surfaceStrcMap">
                        </div>
                    </div>
                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">涂料代码（上表面）：</label>
                        <div class="col-sm-9 paintCodeTopMap">
                        </div>
                    </div>
                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">涂料代码（下表面）：</label>
                        <div class="col-sm-9 paintCodeBottomMap">
                        </div>
                    </div>
                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">管端型式代码：</label>
                        <div class="col-sm-9 pipeendTypeCodeMap">
                        </div>
                    </div>
                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">接箍类型代码：</label>
                        <div class="col-sm-9 couplingTypeCodeMap">
                        </div>
                    </div>
                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">螺纹类型代码：</label>
                        <div class="col-sm-9 threadTypeCodeMap">
                        </div>
                    </div>
                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">内螺纹类型代码：</label>
                        <div class="col-sm-9 inThreadTypeCodeMap">
                        </div>
                    </div>
                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">钢管制造方法代码：</label>
                        <div class="col-sm-9 steeltubeManufMethodCodeMap">
                        </div>
                    </div>
                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">后处理方式代码：</label>
                        <div class="col-sm-9 afterProcMethodCodeMap">
                        </div>
                    </div>
                    <div class="form-group ifHide hide">
                        <label class="col-sm-3 control-label">镀层种类代码：</label>
                        <div class="col-sm-9 coatingCatCodeMap">
                        </div>
                    </div>

                    <!--                  <div class="form-group ifHide hide">-->
                    <!--                    <label class="col-sm-3 control-label">镀层重量：</label>-->
                    <!--                    <div class="col-sm-9 coatingWtMap">-->
                    <!--                    </div>-->
                    <!--                  </div>-->

                    <!--                  <div class="form-group ifHide hide">-->
                    <!--                    <label class="col-sm-3 control-label">硅钢涂层代码：</label>-->
                    <!--                    <div class="col-sm-9 sisteelCoatingMap">-->
                    <!--                    </div>-->
                    <!--                  </div>-->
                    <!--                  <div class="form-group ifHide hide">-->
                    <!--                    <label class="col-sm-3 control-label">彩涂基板类型代码：</label>-->
                    <!--                    <div class="col-sm-9 colorcoatSubstrateTypeMap">-->
                    <!--                    </div>-->
                    <!--                  </div>-->
                    <!--                  <div class="form-group ifHide hide">-->
                    <!--                    <label class="col-sm-3 control-label">涂油重量：</label>-->
                    <!--                    <div class="col-sm-9 oilWtMap">-->
                    <!--                    </div>-->
                    <!--                  </div>-->
                    <!--                  <div class="form-group ifHide hide">-->
                    <!--                    <label class="col-sm-3 control-label">油品种类代码：</label>-->
                    <!--                    <div class="col-sm-9 oilsCatMap">-->
                    <!--                    </div>-->
                    <!--                  </div>-->
                    <!--                  <div class="form-group ifHide hide">-->
                    <!--                    <label class="col-sm-3 control-label">彩涂涂层表面状态：</label>-->
                    <!--                    <div class="col-sm-9 colorcoatCoatingSurfaceStatusMap">-->
                    <!--                    </div>-->
                    <!--                  </div>-->
                    <!--                  <div class="form-group ifHide hide">-->
                    <!--                    <label class="col-sm-3 control-label">彩涂涂层结构：</label>-->
                    <!--                    <div class="col-sm-9 colorcoatCoatingStrcMap">-->
                    <!--                    </div>-->
                    <!--                  </div>-->
                    <!--                  <div class="form-group ifHide hide">-->
                    <!--                    <label class="col-sm-3 control-label">膜厚度(上表面)：</label>-->
                    <!--                    <div class="col-sm-9 filmThickTopMap">-->
                    <!--                    </div>-->
                    <!--                  </div>-->
                    <!--                  -->

                </div>
            </div>
        </div>
    </div>
</form>
<script th:inline="javascript">
    var prefix = ctx + "kycg/shopsign"
    $("#form-shopsignInfo-add").validate({
        focusCleanup: true
    });

    function urlToObject(data) {
        var jsonData = {};
        for (var item in data) {
            var value = data[item].value;
            if (value != null && value != '') {
                if (jsonData[data[item].name] != null && jsonData[data[item].name] != '' && jsonData[data[item].name] != undefined) {
                    jsonData[data[item].name] += ',' + data[item].value
                } else {
                    jsonData[data[item].name] = data[item].value;
                }

            }
        }
        return jsonData;
    };


    //发送请求
    function getPsrMap(e) {
        var shopsign = $(e).val() || null
        if ('' == shopsign || undefined == shopsign || null == shopsign) {
            $.modal.msgError('牌号不能为空');
            $('.ifHide').addClass('hide')
            return;
        }
        var item = {
            shopsign: shopsign
        }
        $.ajax({
            url: prefix + "/getPsrType",
            type: "post",
            dataType: "json",
            contentType: 'application/json;charset=UTF-8',
            data: JSON.stringify(item),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                if (result.code == '0') {
                    $('.ifHide').addClass('hide')
                    for (var resultKey in result.data) {
                        var inputName = resultKey.substring(0, resultKey.length - 3)
                        var array = [];
                        var html = ''
                        for (var resultKey1 in result.data[resultKey]) {
                            var data = {};
                            html += `<label class="check-box"><input class="icheckbox-blue" type="checkbox" id="${resultKey}${resultKey1}" name="${inputName}" value="${resultKey1}" style="position: absolute; opacity: 0;">${result.data[resultKey][resultKey1]}</label>`
                        }
                        $('.' + resultKey).html(html)
                        $('.' + resultKey).parents('.ifHide').removeClass('hide')
                    }
                    $('input[type="checkbox"]').iCheck({
                        checkboxClass: 'icheckbox-blue',
                    });
                    $('#shopsign').val(shopsign);
                } else {
                    $('.ifHide').addClass('hide')
                    $('#shopsign').val('')
                    $.modal.msgError(result.msg);
                }
                $.modal.closeLoading();
            }
        })
    }


</script>
</body>
</html>