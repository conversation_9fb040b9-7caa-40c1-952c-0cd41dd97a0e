<!-- 
	流程审批历史

输入参数:
	processInstanceId 流程实例ID
	businessId 业务ID
-->
<div th:fragment="init">
	<div class="kjtree-button2" th:if="${not #strings.isEmpty(processInstanceId) or not #strings.isEmpty(businessId)}" style="margin-bottom: 0px;">
		<a href="javascript:void(0)" >
			<div th:onclick="openTabCommentList([[${processInstanceId}]],[[${businessId}]])">
				<i class="fa fa-history"></i>
				<br/>审批履历
			</div>
		</a>
	</div>
    <script type="text/javascript" th:inline="javascript">
        function openTabCommentList(processInstanceId,businessId){  
        	if(!processInstanceId){
        		processInstanceId = '';
        	}
        	if(!businessId){
        		businessId = '';
        	}
        	var url = ctxGGMK + "web/MPWF0011?processInstanceId="+processInstanceId+"&businessId="+businessId;
        	$.modal.openTab("审批历史",url,true);
        	//window.open(url);
        }
    </script>
</div>