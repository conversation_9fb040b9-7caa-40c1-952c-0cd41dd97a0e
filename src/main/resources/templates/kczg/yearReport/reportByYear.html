<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('20_09创值团队年度总结列表')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: summernote-js"/>

</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form method="post" action="#" class="form-horizontal" id="form-yearReport-add">
        <input type="hidden" id="yreportId"/>
        <div id="title_panel" class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a role="button" data-toggle="collapse">
                        基本信息
                    </a>
                </h4>
            </div>
            <div class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">创值团队名称:</label>
                                <div class="col-sm-8">
                                    <div class="bs-component form-control">
                                        <span th:utext="|${groupYearReportDto.groupInfo.groupName}|"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">

                            <div class="form-group">
                                <label class="col-sm-4 control-label">负责单位:</label>
                                <div class="col-sm-8">
                                    <div class="bs-component form-control">
                                        <span th:utext="|${groupYearReportDto.groupInfo.fzDeptName}|"/>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">团队长:</label>
                                <div class="col-sm-8">
                                    <div class="bs-component form-control">
                                        <span th:utext="|${groupYearReportDto.groupInfo.leaderName}|"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">联系电话:</label>
                                <div class="col-sm-8">
                                    <div class="bs-component form-control">
                                        <span th:utext="|${groupYearReportDto.groupInfo.tel}|"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">年度:</label>
                                <div class="col-sm-8">
                                    <div class="bs-component form-control">
                                        <span th:utext="|${#strings.isEmpty(groupYearReportDto.groupInfo.stageYear)?'':'第'+groupYearReportDto.groupInfo.stageYear+'期'}|"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div id="target_panel" class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a role="button" data-toggle="collapse">
                        绩效指标完成情况
                    </a>
                </h4>
            </div>
            <div class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row" style="text-align: center">
                        <div class="col-sm-12 table-bordered">
                            <table id="group-table" class="table-striped"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel panel-default" id="mainReport_panel">
            <div class="panel-heading" role="tab" id="headingTwo">
                <h4 class="panel-title" toolbox-title="重点工作总结">
                    <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseTwo"
                       aria-expanded="false" aria-controls="collapseTwo">
                        重点工作总结(主要亮点)
                    </a>
                    <span class="pull-right">

                  </span>
                </h4>

            </div>
            <div id="collapseTwo" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingTwo">
                <div class="panel-body">
                    <input id="yearReportGlobMainReport" name="yearReportGlobMainReport" type="hidden">
                    <div class="click2edit  wrapper reportEdit" id="yearReportGlobMainReportEdit">

                    </div>
                </div>
            </div>
        </div>

        <div id="hxjy_panel" class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title" toolbox-title="后续建议">
                    <a role="button" data-toggle="collapse">
                        <span style="color: red"> * </span>后续建议
                    </a>
                </h4>
            </div>
            <div class="panel-collapse collapse in collapseTwoEdit" role="tabpanel" aria-labelledby="headingTwo">
                <div class="panel-body">
                    <input th:field='${groupYearReportDto.yearReport.hxjy}' id="hxjy" name="hxjy" type="hidden">
                    <div th:utext="${groupYearReportDto.yearReport.hxjy}" class="click2edit  wrapper reportEdit" id="hxjyEdit">

                    </div>
                </div>
            </div>
        </div>
        <div id="sum_panel" class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a role="button" data-toggle="collapse">
                        工作总结
                    </a>
                </h4>
            </div>
            <div class="panel-collapse collapse in">
                <div class="panel-body">
                    <div id="row42" class="form-group" th:include="include :: layui-upload(id='relevantAttachments', name='relevantAttachments',
                                  labelClass='control-label',
                                  labelName='')">
                    </div>
                </div>
            </div>
        </div>
        <div id="targetValue_panel" class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a role="button" data-toggle="collapse">
                        指标明细数据
                    </a>
                </h4>
            </div>
            <div class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="form-group"
                         th:include="include :: layui-upload(id='targetDetailFiles',
                                                                name='targetDetailFiles',
                                                                  labelClass='control-label',
                                                                  labelName='',
                                                                  sourceId=${groupYearReportDto.yearReport.yreportId}+'_target',
                                                                  sourceModule='KCZG')">
                    </div>
                </div>
            </div>
        </div>

    </form>
</div>
<div class="toolbar toolbar-bottom" role="toolbar">
    <button type="button" class="btn  btn-primary"
            onclick="saveHandler()">
        <i class="fa fa-check"></i>暂 存
    </button>
    <button type="button" class="btn  btn-primary"
            onclick="submitHandler()">
        <i class="fa fa-check"></i>提 交
    </button>
    <button type="button" class="btn  btn-danger"
            onclick="closeItem()">
        <i class="fa fa-reply-all"></i>返 回
    </button>
</div>

<script th:inline="javascript">
    var prefix = ctx + "kczg/yearReport";
    let groupTargets = [[${groupYearReportDto.yearReport.tkczgTargetValues}]];

    function saveHandler() {
        saveOrSubmit(false)
    }

    function submitHandler() {
        if ($.validate.form()) {
            if ($('#yearReportGlobMainReportEdit').summernote('code') === '<p><br></p>') {
                $.modal.alertWarning("请填写重点工作总结")
                return
            }
            if ($('#hxjyEdit').summernote('code') === '<p><br></p>') {
                $.modal.alertWarning("请填写后续建议")
                return
            }

            if (!$("#targetDetailFiles").val()) {
                $.modal.alertWarning("请上传指标明细数据")
                return;
            }
            $.modal.confirm("确认提交吗？", function () {
                saveOrSubmit(true)
            })

        }
    }

    function saveOrSubmit(isSubmit) {
        let submitParam = {}
        submitParam.yearReportGlobMainReport = $('#yearReportGlobMainReportEdit').summernote('code')
        let targetValues = []
        const groupInfo = [[${groupYearReportDto.groupInfo}]];
        groupTargets.forEach((val, index) => {
            let targetValue = {}
            targetValue.valueId = val.valueId
            targetValue.targetCode = val.targetCode
            targetValue.targetName = val.targetName
            targetValue.ratio = val.ratio
            targetValue.valueCurrent = $("#valueCurrent" + index).val()
            targetValue.valueTotal = $("#valueTotal" + index).val()
            targetValues.push(targetValue)
        })
        submitParam.tkczgTargetValues = targetValues
        submitParam.hxjy =  $('#hxjyEdit').summernote('code')
        submitParam.relevantAttachments = $("#relevantAttachments").val()
        submitParam.targetDetailFiles = $("#targetDetailFiles").val()
        submitParam.groupId = groupInfo.groupId
        submitParam.stageYear = groupInfo.stageYear
        submitParam.isSubmit = isSubmit
        submitParam.yreportId = $("#yreportId").val()

        const config = {
            url: prefix + "/saveInsert",
            type: "post",
            dataType: "json",
            contentType: "application/json",
            data: JSON.stringify(submitParam),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                if (isSubmit) {
                    $.operate.alertSuccessTabCallback(result)
                } else {
                    if (result.code == web_status.SUCCESS) {
                        $.modal.alertSuccess(result.msg);
                        $("#yreportId").val(result.data.yreportId)
                        groupTargets = result.data.tkczgTargetValues
                    } else if (result.code == web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                }
            }
        };
        $.ajax(config)
    }

    $(function () {
        var options = {
            id: 'group-table',
            data: [[${groupYearReportDto.yearReport.tkczgTargetValues}]],
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            showFooter: false,
            columns: [
                {
                    field: 'numberIndex',
                    align: 'center',
                    title: '序号',
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'targetName',
                    align: 'center',
                    title: '指标名称'
                },
                {
                    field: 'ratio',
                    align: 'center',
                    title: '权重'

                },
                {
                    field: 'valueCurrent',
                    align: 'center',
                    title: '当期年度实绩',
                    formatter: function (val, row, index) {
                        return "<input required type=\"number\" oninput='if(value>100000000000000)value=99999999999999;if(value<0)value=0' class='form-control' id='valueCurrent" + index + "' value=" + val + ">";
                    }
                },
                {
                    field: 'valueTotal',
                    align: 'center',
                    title: '累计实绩',
                    formatter: function (val, row, index) {
                        return "<input type=\"number\" oninput='if(value>100000000000000)value=99999999999999;if(value<0)value=0' class='form-control' id='valueTotal" + index + "' value=" + val + ">";
                    }
                }]
        };
        $.table.init(options);
        //初始化富文本编辑
        initEelement();
    });

    function initEelement() {
        $('.reportEdit').summernote({
            lang: 'zh-CN',
            minHeight: 500,
            focus: true
        });

        $("[data-toggle=popover]").popover();

        $("#form-yearReport-add").validate({
            focusCleanup: true
        });
    }

</script>
</body>
<style>
    .bs-component {
        padding: 7px 6px 3px;
    }

    .note-editor.note-frame.panel {
        border: 0px;
        margin-bottom: 0px;
    }

    .note-toolbar.panel-heading {
        border-top: #f6f7f9 solid 1px;
        background-color: #dfebfe;
    }

    #collapseTwo .panel-body {
        padding: 0px;
    }
</style>
</html>
