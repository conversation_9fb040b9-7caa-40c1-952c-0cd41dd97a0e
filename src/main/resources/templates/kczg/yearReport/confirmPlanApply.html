<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改 创值团队方案')"/>

    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: summernote-js"/>
    <th:block th:include="include :: jquery-tmpl"/>
    <th:block th:include="include :: sub-tab-commons"/>
    <th:block th:include="kczginclude :: toolbox-css"/>
    <th:block th:include="kczginclude :: toolbox-js"/>
</head>
<body class="white-bg" data-spy="scroll" data-target="#catalogueItem" data-offset="200">
<div class="wrapper wrapper-content fadeInRight ibox-content">

    <form class="form-horizontal m" id="form-groupPlanApply-edit">
        <input type="hidden" id="businessId" name="businessId" th:field="${workFlow.businessId}">
        <input type="hidden" id="businessType" name="businessType" value="ZJ">
        <input type="hidden" id="beforePlanId" name="beforePlanId" th:field="${groupPlanApplyDto.beforePlanId}">
        <!-- 隐藏 但需要提交的字段 -->
        <input type="hidden" id="planId" name="planId" th:value="${groupPlanApplyDto.planId}">
        <input type="hidden" id="groupName" name="groupName" th:value="${groupPlanApplyDto.groupName}">
        <input type="hidden" id="groupId" name="groupId" th:value="${groupPlanApplyDto.groupId}">
        <input type="hidden" name="groupPlanNotice.noticeId"
               th:value="${groupPlanApplyDto.groupPlanNotice != null ?groupPlanApplyDto.groupPlanNotice.noticeId: ''}">
        <input type="hidden" name="manageDept" th:value="${groupPlanApplyDto.manageDept}">
        <input type="hidden" name="manageLevel" th:value="${groupPlanApplyDto.manageLevel}">
        <input type="hidden" name="groupType" th:value="${groupPlanApplyDto.groupType}">
        <input type="hidden" id="riskType" name="riskType" th:value="${groupPlanApplyDto.riskType}">


        <input type="hidden" id="isSubmit" name="isSubmit">
        <!-- 基本信息 -->
        <div id="title" class="panel panel-default scrollspy-item">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a role="button" data-toggle="collapse">
                        基本信息
                    </a>
                </h4>
            </div>
            <div class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">创值团队名称:</label>
                                <div class="col-sm-8">
                                    <div class="bs-component form-control-static">
                                        <span th:utext="|${groupPlanApplyDto.group.groupName}|"/>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">管理模式:</label>
                                <div class="col-sm-8">
                                    <div class="bs-component form-control-static">
                                        <span id="manageLevelName"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">类型:</label>
                                <div class="col-sm-8">
                                    <div class="bs-component form-control-static">
                                        <span id="groupTypeName"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">风险金类型:</label>
                                <div class="col-sm-8">
                                    <div class="bs-component form-control-static">
                                        <span id="riskTypeName"></span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group"
                                 th:include="include :: choiceUserAndOrg(userCodeId='leaderId',userNameId='leaderName',selectType='S',labelName='团队长:',callback='choseUserCallBack',isrequired=true)">
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">联系电话:</label>
                                <div class="col-sm-8">
                                    <input type="text" id="tel" name="tel" required
                                           th:field="*{groupPlanApplyDto.tel}" class=" bs-component form-control"
                                           data-mask="999-9999-9999" placeholder="请输入手机号码">
                                </div>
                            </div>
                        </div>

                    </div>

                    <div class="row">
                        <div class="col-lg-6 col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">负责单位:</label>
                                <div class="col-sm-8">
                                    <div class="bs-component ">
                                        <input type="hidden" th:value="${groupPlanApplyDto.fzDeptCode}" id="fzDeptCode"
                                               name="fzDeptCode" class="form-control-static"/>
                                        <input type="text" th:value="${groupPlanApplyDto.fzDeptName}" id="fzDeptName"
                                               name="fzDeptName" placeholder="对应人员所在单位" class="form-control" readonly>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-6 col-md-6">

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--背景及意义-->
        <div class="panel panel-default scrollspy-item" id="row1">
            <div class="panel-heading" role="tab" id="headingTwo">
                <h4 class="panel-title" toolbox-title="背景及意义">
                    <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseTwo"
                       aria-expanded="false" aria-controls="collapseTwo">
                        背景及意义
                    </a>
                    <div class="ibox-tools">
                        <a type="button" class="btn btn-sm btn-primary" data-container="body" data-toggle="popover"
                           data-placement="top" title="产品类创值团队请包含以下内容"
                           data-html="true"
                           style="margin-top: -10px;"
                           data-content="
                      <ul>
                        <li>1. 国内外环境政策、市场需求量（中高端情况）、发展趋势；</li>
                        <li>2. 宝钢该产品规划目标; </li>
                        <li>3. 宝钢该产品产销研进展情况及年度销售目标; </li>
                        <li>4. 宝钢该产品相关机组制造能力情况及年度产量计划 </li>
                        <li>5. 创值团队市场拓展的主要突破点。</li></ul>">
                            填写提示
                        </a>
                    </div>
                </h4>

            </div>
            <div id="collapseTwo" class="panel-collapse collapse in" role="tabpanel"
                 aria-labelledby="headingTwo">
                <div class="panel-body">
                    <input id="backgroundAndSignificance"
                           th:field="${groupPlanApplyDto.backgroundAndSignificance}"
                           name="backgroundAndSignificance" type="hidden">
                    <div class="click2edit  wrapper" th:utext='${groupPlanApplyDto.backgroundAndSignificance}'
                         id="backgroundAndSignificanceEdit">

                    </div>
                </div>
            </div>
        </div>

        <!-- 创值团队涉及的产品 -->
        <div id="row7" class="panel panel-default scrollspy-item">
            <div class="panel-heading">
                <h4 class="panel-title" toolbox-title="产品范围">
                    <a role="button">
                      产品范围 <small>钢铁产品类必填</small>
                    </a>
                </h4>
            </div>
            <div class="panel-collapse collapse in">
                <div class="panel-body">
                    <div id="product-toolbar">
                        <button type="button" class="btn btn-danger btn-xs" onclick="addColumnSub('product-table')">
                            <i
                                    class="fa fa-plus"> 增加</i></button>
                        <button type="button" class="btn btn-danger btn-xs" onclick="deleteProduct()">
                            <i
                                    class="fa fa-minus"> 删除</i></button>
                    </div>
                    <div class="col-sm-12 select-table table-bordered">
                        <table id="product-table" class="table-striped"></table>
                    </div>
                </div>
            </div>
        </div>

        <!-- 变革思路 -->
        <div id="row2" class="panel panel-default scrollspy-item">
            <div class="panel-heading" role="tab" id="headingThree">
                <h4 class="panel-title">
                    <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseThree"
                       aria-expanded="false" aria-controls="collapseThree">
                        变革思路
                    </a>
                </h4>
            </div>
            <div id="collapseThree" class="panel-collapse collapse in" role="tabpanel"
                 aria-labelledby="headingThree">
                <div class="panel-body">
                            <textarea required maxlength="325" class="form-control" th:field="${groupPlanApplyDto.bgsl}" id="bgsl"
                                      name="bgsl"
                                      rows="5"></textarea>
                </div>
            </div>
        </div>

        <!-- 团队主要职责 -->
        <div id="row3" class="panel panel-default scrollspy-item">
            <div class="panel-heading" role="tab" id="headingFour">
                <h4 class="panel-title">
                    <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseFour"
                       aria-expanded="false" aria-controls="headerFour">
                        主要职责
                    </a>
                </h4>
            </div>
            <div id="collapseFour" class="panel-collapse collapse in" role="tabpanel"
                 aria-labelledby="headingFour">
                <div class="panel-body">
                            <textarea required maxlength="325" class="form-control" th:field="${groupPlanApplyDto.tdzyzz}" id="tdzyzz"
                                      name="tdzyzz"
                                      rows="5"></textarea>
                </div>
            </div>
        </div>

        <!-- 绩效目标与激励方案 -->
        <div id="row4" class="panel panel-default scrollspy-item">
            <div class="panel-heading" role="tab">
                <h4 class="panel-title" toolbox-title="绩效方案">
                    绩效方案</h4>
            </div>
            <div id="collapseFive" class="panel-collapse collapse in">
                <div class="panel-body">
                    <div class="tabs-container" id="row40">
                        <input type="hidden" id="stageTotal" name="stageTotal" value="1">
                        <input type="hidden" id="stageYear" name="stageYear" th:value="${groupPlanApplyDto.group.stageYear}">
                        <div class="tabs-left">
                            <ul id="yearTargetIndex" class="nav nav-tabs">
                            </ul>

                            <div id="yearTargetContent" class="tab-content ">

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div id="row41" class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title" toolbox-title="备注说明">
                    备注说明: <small>若第二年度指标的上年度实绩值为空，则必须在此处填写说明
                    个性化指标，需要在此处注明指标定义</small>
                </h4>
            </div>
            <div class="panel-body">
                        <textarea class="form-control" th:field="${groupPlanApplyDto.remark}" id="remark" name="remark"
                                  rows="3" maxlength="180"></textarea>
            </div>
        </div>

        <div id="row42" class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title" toolbox-title="相关附件">
                    相关附件: <small>创值团队实施前12个月各绩效指标实绩明细,数据来源财务系统或制造系统等公司业务系统</small>
                </h4>
            </div>
            <div class="panel-body">
                <div class="form-group" th:include="include :: layui-upload(id='relevantAttachments',
                  name='relevantAttachments',
                  divClass='col-sm-12',
                  labelClass='relevantAttachmentsLabelClass',
                  labelName=' ',
                  sourceId=${groupPlanApplyDto.planId},
									sourceModule='KCZG',
									sourceLabel1='KCZG_GROUP_ADD_APPLY',
                  sourceLabel2='MD_APPLY_START')">
                </div>
            </div>
        </div>

        <!--团队成员-->
        <div id="row5" class="panel panel-default scrollspy-item">
            <div class="panel-heading" role="tab">
                <h4 class="panel-title">
                    <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseSix"
                       aria-expanded="false" aria-controls="headingSix">
                        团队成员
                    </a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingSix">
                <div class="panel-body">
                    <div id="member-toolbar">
                        <button type="button" class="btn btn-danger btn-xs"
                                onclick="addColumnSub('groupMember-table')"><i class="fa fa-plus"> 增加</i>
                        </button>
                    </div>
                    <div class="col-sm-12 select-table table-bordered">
                        <table id="groupMember-table" class="table-striped"></table>
                    </div>
                </div>
            </div>
        </div>

        <!--激励兑现说明-->
        <div id="row6" class="panel panel-default scrollspy-item">
            <div class="panel-heading" role="tab">
                <h4 class="panel-title">
                    <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseSeven"
                       aria-expanded="false" aria-controls="collapseSeven">
                        激励兑现
                    </a>
                </h4>
            </div>
            <div class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingSeven">
                <div class="panel-body">
                            <textarea required maxlength="325" class="form-control" th:field="${groupPlanApplyDto.jjdxsm}" name="jjdxsm"
                                      rows="5"></textarea>
                </div>
            </div>
        </div>

    </form>

</div>
<div th:include="kczginclude :: toolbox"/>

<div class="toolbar toolbar-bottom">
    <button type="button" class="btn  btn-primary" th:if="${workFlow != null && workFlow.processInstanceId!=null}"
            th:onclick="openProcessTrack([[${workFlow.processInstanceId}]])">
        <i class="fa fa-eye"></i>&nbsp;流程跟踪图
    </button>

    <button type="button" class="btn  btn-primary"
            onclick="saveHandler()">
        <i class="fa fa-check"></i> 暂 存
    </button>

    <button type="button" class="btn  btn-primary"
            onclick="submitHandler()">
        <i class="fa fa-check"></i> 提 交
    </button>
    <button type="button" class="btn  btn-danger"
            onclick="closeItem()">
        <i class="fa fa-reply-all"></i> 返 回
    </button>
</div>

<script th:inline="javascript">
    var prefix = ctx + "kczg/groupPlanApply";
    var yearReportPrefix = ctx + "kczg/yearReport"
    var prefixProject = ctx + "kczg/project/info/";
    var prefixNotice = ctx + "kczg/groupPlanNotice/";

    var managerLevels = [[${@dict.getDictList('KCZG','MANAGEMENT_MODE')}]];
    var groupRoleList = [[${@dict.getDictList('KCZG','GROUP_ROLE')}]];
    var groupTypeList = [[${@dict.getDictList('KCZG','PRODUCT_TYPE')}]];
    var zzbase = [[${@dict.getDictList('KYND','implementationBase')}]];
    var projectStatus = [[${@dict.getDictList('KYXM','project_status')}]]; //项目状态

    var leaderId = [[${groupPlanApplyDto.leaderId}]];
    var leaderName = [[${groupPlanApplyDto.leaderName}]];
    var group = [[${groupPlanApplyDto.group}]];
    var backgroundAndSignificance = [[${groupPlanApplyDto.backgroundAndSignificance}]];
    var groupPlanNotice = [[${groupPlanApplyDto.groupPlanNotice}]]; //策划通知
    var groupMemberList = [[${groupPlanApplyDto.groupMemberList}]]; //团队成员
    var productList = [[${groupPlanApplyDto.productList}]];         //产品列表
    var groupTargetDtoList = [[${groupPlanApplyDto.groupTargetDtoList}]]; //绩效目标
    var riskType = [[${groupPlanApplyDto.riskType}]];
    var workFlow = [[${workFlow}]];

    function openInfo() {
        var noticeId = groupPlanNotice != null ? groupPlanNotice.noticeId : "";
        var url = prefixNotice + "detail/" + noticeId;
        $.modal.openTab("策划通知", url);
    }

    /**
     * 指令型的 操作
     */
    function initIsChtzOther() {
        managerLevels.forEach((manageLevel) => {
            if (manageLevel.dictValue == group.manageLevel) {
                $("#manageLevelName").text(manageLevel.dictName);
            }
        });
        groupTypeList.forEach((productType) => {
            if (productType.dictValue == group.groupType) {
                $("#groupTypeName").text(productType.dictName);
            }
        });
        if (group.riskType === "0") {
            $("#riskTypeName").text("不抵押");
        } else {
            $("#riskTypeName").text("抵押");
        }
    }


    function initEelement() {
        $('#backgroundAndSignificanceEdit').summernote({
            lang: 'zh-CN',
            minHeight: 300,
            focus: true,
        });
        $("[data-toggle=popover]").popover();
        $("#form-groupPlanApply-add").validate({
            focusCleanup: true
        });
    }

    /**
     * 保存
     **/
    function saveHandler() {
        $("#isSubmit").val(false);
        submitBefore();
        $("#backgroundAndSignificance").val($('#backgroundAndSignificanceEdit').summernote('code'));
        $.operate.saveModal(prefix + "/saveAndSubmit", $('#form-groupPlanApply-edit').serialize());
    }

    function submitBefore() {

    }

    /**
     * 提交
     **/
    function submitHandler() {
      var keyValue = $('#backgroundAndSignificanceEdit').summernote('code');
      $("#backgroundAndSignificance").val(keyValue);
      if ($('#backgroundAndSignificanceEdit').summernote('isEmpty') || $.common.isEmpty(keyValue)) {
        $.modal.alertError("背景及意义:不能为空")
        return;
      }
      var groupType = $("#groupType").val();
      if ($.common.equals("01",groupType)) {
        var productList =  $("#product-table").bootstrapTable("getData");
        if(productList.length == 0){
          $.modal.alertError('类型为"钢铁类"时, 创值团队涉及的产品 不能为空。');
          return;
        }
      }
      //每一期的 权重 合计 等于 100
      var stageTotal = $("#stageTotal").val(); //总期数
      var ratioSumError = "";
      for (var i = 0; i < stageTotal; i++) {
        var index = i + 1;
        var tableId = "yearTargetTab-" + index + "-table"
        var yearTargetList = $("#" + tableId).bootstrapTable("getData");
        if (yearTargetList.length == 0) {
          $.modal.alertError(getYearTargetLabel(index) + ' 指标 不能为空。');
          return;
        }
        var ratioSum = 0;
        if (i == 0) {
          var isError = false;
          yearTargetList.forEach(item => {
            if ($.common.isEmpty(item.valueBeforYear)) isError = true;
          });
          if (isError) {
            $.modal.alertError("第一期中，上年度实际 不能为空。");
            return;
          }
        }
        yearTargetList.forEach(item => {
          ratioSum += parseFloat(item.ratio||0);
        });
        if(ratioSum != 100) ratioSumError += "第 " + (i+1) + " 期，权重合计必须等于 100; <br /> ";
      }
      if(!$.common.isEmpty(ratioSumError)){
        $.modal.alertError(ratioSumError);
        return;
      }
      if ($.validate.form()) {
        $.modal.confirm("确认提交吗？", function () {
          $("#isSubmit").val(true);
            $.operate.saveTabAlert(yearReportPrefix + "/submitLastWF", $('#form-groupPlanApply-edit').serialize());
        });
      }
    }

    function choseUserCallBack(userCode, userName, orgCode, orgName, index) {
        $("#fzDeptCode").val(orgCode);
        $("#fzDeptName").val(orgName);
        getOrgPathName(orgCode, function (result) {
            $("#fzDeptName").val(result);
            //新增 成员
            var data = {
                id: 1,
                empCode: userCode,
                empName: userName,
                deptCode: orgCode,
                deptName: result,
                role: "ROLE_A",
                task: "",
                riskDetailAmt: 0,
                delStatus: 0
            };
            addMemberRoleA(data);
        });
    }

    /**
     * 添加 团队长
     **/
    function addMemberRoleA(data) {
        var dataList = $("#groupMember-table").bootstrapTable("getData");
        // 判断团队长是否存在，如果不存在 添加，存在，需要修改
        var existRoleA = false;
        if (dataList.length > 0) {
            for (var i = 0; i < dataList.length; i++) {
                if (dataList[i].role == "ROLE_A") {
                    //existRoleA = true;
                    dataList[i].delStatus = "1"; //将原来的团队长 状态修改为退出
                    //dataList[i].empCode = data.empCode;
                    //dataList[i].empName = data.empName;
                    //dataList[i].deptCode = data.deptCode;
                    //dataList[i].deptName = data.deptName;
                    $("#groupMember-table").bootstrapTable('updateRow', {index: dataList[i].index, row: dataList[i]});
                }
            }
            //如果列表中没有团队长，添加，完善后，应该添加到第一行
            if (!existRoleA) {
                var currentId = $.common.isEmpty("groupMember-table") ? table.options.id : "groupMember-table";
                table.set(currentId);
                sub.editColumn();
                data.delStatus = "-1";
                $("#groupMember-table").bootstrapTable('insertRow', {index: 0, row: data});
                // sub.addColumn(data, "groupMember-table");
            }
        } else {
            var currentId = $.common.isEmpty("groupMember-table") ? table.options.id : "groupMember-table";
            table.set(currentId);
            sub.editColumn();
            $("#groupMember-table").bootstrapTable('insertRow', {index: 0, row: data});
        }
    }


    function choseUserCallBackRow(userCode, userName, orgCode, orgName, index) {
        $($.common.sprintf("#groupMemberList_%s_deptCode", index)).val(orgCode);
        getOrgPathName(orgCode, function (result) {
            $($.common.sprintf("#groupMemberList_%s_deptName", index)).val(result);
            sub.editColumn();
        });
    }


    $(function () {
        //初始页面组件
        initEelement();
        initIsChtzOther();
        $("#leaderId").val(leaderId);
        $("#leaderName").val(leaderName);
        initProductTable("product-table");

        groupRoleList.forEach(item =>{
          // 设置 禁止 选择
          if(item.dictValue === "ROLE_A"){
            groupRoleASelect.push(item);
          }else {
            groupRoleSelect.push(item);
          }
        });
        initGroupTable("groupMember-table");
        // 初始化 年度目标
        initYearTarget();
        var noticeId = groupPlanNotice != null ? groupPlanNotice.noticeId : "";
        var otherData = [];
        if (noticeId != "" && noticeId != null) {
            otherData = [{id: 1, name: '策划通知', url: prefixNotice + "detail/" + noticeId}];
        }
        if(workFlow != null && workFlow.processInstanceId !=null){
          otherData.push({id: 5, name: '审批历史', url: ctxGGMK + "web/MPWF0011?processInstanceId="+workFlow.processInstanceId});
        }
        ToolBox.initToolBox("catalogue", null, otherData);

      // 初始化 抵押金的 控制
      changeTargetRule(riskType == 1);
      //changeGroupMember(riskType == 0);
    });

    // 选择抵押金类型
    function changeRiskType(e){
      riskType = $(e).val();
      // 抵押型，绩效指标细则为只读；非抵押保持现状
      // 设置 绩效指标细则为只读
      changeTargetRule(riskType == 1); //抵押为true
      // 抵押型和不抵押型团队成员上的计划抵押金要控制，不抵押时，抵押金合计不能大于0
      // 不抵押时： 团队成员 计划抵押金为只读
      changeGroupMember(riskType == 0);
    }
    function changeTargetRule(readonly){
      var stageTotal = $("#stageTotal").val();
      for (var i = 0; i < stageTotal; i++) {
        $("input[name='groupTargetDtoList["+(i+1)+"].groupTargetRules.jlxzX1']").attr("readonly",readonly);
        $("input[name='groupTargetDtoList["+(i+1)+"].groupTargetRules.jlxzX2']").attr("readonly",readonly);
        $("input[name='groupTargetDtoList["+(i+1)+"].groupTargetRules.jlxzX3']").attr("readonly",readonly);
        $("input[name='groupTargetDtoList["+(i+1)+"].groupTargetRules.jlxzX4']").attr("readonly",readonly);
        $("input[name='groupTargetDtoList["+(i+1)+"].groupTargetRules.jlxzX5']").attr("readonly",readonly);
      }
    }
    function changeGroupMember(readonly){
      var memberSize = $("#groupMember-table").bootstrapTable("getData").length;
      for (var i = 0; i < memberSize; i++) {
        $("input[name='groupMemberList["+i+"].riskDetailAmt']").attr("readonly",readonly);
      }
    }

    function showProjectPage(projectCode) {
      var ky = "/bscdkj-ky/"
      var url = ky+"c/l?serviceName=KYXMLX02&methodName=query&pageNo=KYXMLX21&projectGuid=";
      var title = "项目一栏表";
      // 选卡页方式打开并刷新当前页 isRefresh 是否刷新
      $.get(prefixProject + "ajax/" + projectCode, function (data) {
        if (data.code == 0) {
          var project = data.data;
          if (project != null) {
            console.log(project);
            $.modal.openTab(title, url + project.recordGuid, true);
          }
        }
      });
    }

    /**
     * 选择 号牌
     */
    function choseBrandNo(el){
      var nameId = $(el).attr("id");
      var ky = "/bscdkj-ky/";
      $.modal.open("选择牌号", ky + "kxcp/yyph/selectPh?selectType=S&codeId=shopsign&nameId="+nameId, '1000', '500');
    }

    function choseProject(el,index){
      var nameId = $(el).attr("id");
      $.modal.open("选择项目", ctx + "kczg/project/"+index, '1000', '500');
    }

    function choseProjectBack(index,rowData){
      var projectCode = 'productList_' + index + '_projectCode';
      var projectName = 'productList_' + index + '_projectName';
      var projectStatus = 'productList_' + index + '_projectStatus';
      $("#" + projectCode + "_v").val(rowData.projectCode);
      $("#" + projectName + "_v").val(rowData.projectName);
      $("#" + projectName + "_a").text(rowData.projectName);
      $("#" + projectStatus + "_v").val(rowData.projectStatus);
      //$("#" + projectStatus + "_a").text(rowData.projectStatus);
      sub.editColumn();
      refreshProductTableSelect2();
    }

    /**
     * 获取 机构全名称
     */
    function getOrgPathName(orgCode, fn) {
        var url = ctx + "kczg/orgName/" + orgCode;
        $.post(url, {}, fn);
    }

    var groupRoleSelect = []; //其它下拉项目
    var groupRoleASelect = []; //团队长

    //获取 项目信息
    function getProjectInfo(e, projectName) {
        var projectCode = $(e).val();
        $.get(prefixProject + "ajax/" + projectCode, function (data) {
            if (data.code == 0) {
                var project = data.data;
                if (project != null) {
                    $("#" + projectName + "_v").val(project.projectName);
                    $("#" + projectName + "_a").text(project.projectName);
                    sub.editColumn();
                }
            }
        });
    }

    /**
     * 显示 项目页面
     */
    // function showProjectPage(projectCode) {
    //     var url = prefixProject + projectCode;
    //     $.modal.openTab("项目详细", url);
    // }

    /**
     * 初始化 产品 表格
     * @param {表格ID} id
     */
    function initProductTable(id) {
      var data = productList;
      var options = {
        id: id,
        data: data,
        toolbar: 'product-toolbar',
        pagination: false,
        showSearch: false,
        showRefresh: false,
        showToggle: false,
        showColumns: false,
        sidePagination: "client",
        modalName: "产品",
        columns: [
          { checkbox: true },
          {
            field: 'index',
            align: 'center',
            title: "序号",
            formatter: function (value, row, index) {
              var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
              var orderNo = $.common.sprintf("<input type='hidden' name='productList[%s].orderNo' value='%s'>", index, index);
              var columnId = $.common.sprintf("<input type='hidden' name='productList[%s].id' value='%s'>", index, row.id);
              return columnIndex + $.table.serialNumber(index) + orderNo + columnId;
            }
          },
          {
            field: 'brandNo',
            align: 'center',
            title: '产品牌号',
            formatter: function (value, row, index,field) {
              var html = $.common.sprintf("<input class='form-control' required fieldName='%s' type='text' id='productList_%s_brandNo' name='productList[%s].brandNo' value='%s' onclick='choseBrandNo(this)'>", field,index, index, value);
              return html;
            }
          },
          {
            field: 'zzBase',
            align: 'center',
            title: '制造基地',
            width: '150',
            formatter: function (value, row, index, field) {
              //var html = $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='productList[%s].zzBase' value='%s'>",field, index, value);
              var data = {
                fieldName: field,
                name: "productList["+ index+ "].zzBase",
                selectValue: value,
                selectItems: zzbase
              };
              return $("#select2Tpl").tmpl(data).html();
              //return html;
            }
          },
          {
            field: 'productLine',
            align: 'center',
            title: '产线/机组',
            formatter: function (value, row, index, field) {
              var html = $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='productList[%s].productLine' value='%s'>", field, index, value);
              return html;
            }
          },
          {
            field: 'isNew',
            align: 'center',
            title: '是否新产品',
            formatter: function (value, row, index, field) {
              var data = {
                fieldName: field,
                name: "productList["+ index+ "].isNew",
                checkValue: value,
                radios: [{
                  id: 'yes',
                  value: '1',
                  label: '是'
                },
                  {
                    id: 'yes',
                    value: '0',
                    label: '否'
                  }
                ]
              };
              return $("#radiosTpl").tmpl(data).html();
            }
          },
          {
            field: 'projectCode',
            align: 'left',
            title: '项目编号',
            formatter: function (value, row, index, field) {
              //var html = `<input class='form-control' fieldName='%s' type='text' name='productList[%s].projectCode' value='%s' bak_onblur='getProjectInfo(this,"%s")'>`;
              var html = `<input class='form-control' fieldName='%s' type='text' id="productList_%s_projectCode_v" name='productList[%s].projectCode' value='%s' onclick='choseProject(this,\"%s\")'">`;
              return $.common.sprintf(html, field,index, index, value,index);
              //return $.common.sprintf(html, field, index, value,'productList_'+index+'_projectName');
            }
          },
          {
            field: 'projectName',
            align: 'center',
            title: '项目名称',
            width: '100',
            formatter: function (value, row, index, field) {
              var html = `<input class='form-control' type='hidden' id="productList_%s_projectName_v" name='productList[%s].projectName' value='%s'>
                    <a class='a-no-btn' id="productList_%s_projectName_a" style="width: 150px;white-space: break-spaces;" href='javascript:showProjectPage(\"%s\")' >%s</a>`;
              return $.common.sprintf(html,index, index, value, index, row.projectCode, value);
            }
          },
          {
            field: 'projectStatus',
            align: 'center',
            title: '项目状态',
            width: '100',
            formatter: function (value, row, index) {
              var label = $.table.selectDictLabel(projectStatus, value) || "";
              var html = `
            <input class='form-control' type='hidden' id="productList_%s_projectStatus_v" name='productList[%s].projectStatus' value='%s'>
            `+label;
              return $.common.sprintf(html, index, index, value);
            }
          },
          {
            title: '操作',
            align: 'center',
            formatter: function (value, row, index) {
              var actions = [];
              actions.push('<a class="btn btn-danger btn-xs" href="javascript:deleteByInde(\'index\',\'' + (index+1) + '\')"><i class="fa fa-remove"></i>删除</a>');
              return actions.join('');
              return "";
            }
          }
        ]
      };
      $.table.init(options);
      var selectItems = $("#product-table .select");
      for(var i = 0; i < selectItems.length; i++){
        var data = $(selectItems[i]).attr("selectValue");
        $(selectItems[i]).val(data.split(","));
      }
      $("#product-table .select").select2();
    }

    function deleteByInde(byIndex,index){
      sub.delColumnByIndex(byIndex,index);
      refreshProductTableSelect2();
    }

    /**
     * 初始化 团队成员 表格
     * @param {表格ID} id
     */
    function initGroupTable(id) {
        var data = groupMemberList;
        var footerStyle = function (column) {
            return {
                price: {
                    css: {
                        color: 'black',
                        'font-weight': 'normal'
                    }
                },
                duty: {
                    css: {
                        color: 'red',
                        'font-weight': 'normal'
                    }
                },
            } [column.field]
        }

        var options = {
            id: id,
            data: data,
            toolbar: "member-toolbar",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            showFooter: true,
            footerStyle: footerStyle,
            sidePagination: "client",
            columns: [
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    width: '30',
                    formatter: function (value, row, index, field) {
                        //人员ID
                        var memberIdInput = $.common.sprintf("<input class='form-control' fieldName='%s' type=\"hidden\" name='groupMemberList[%s].memberId' value='%s'>", field, index, row.memberId);

                        var columnIndex = $.common.sprintf("<input type='hidden' fieldName='index' name='index' value='%s'>", $.table.serialNumber(index));
                        var orderNo = $.common.sprintf("<input type='hidden' fieldName='orderNo' name='groupMemberList[%s].orderNo' value='%s'>", index, $.table.serialNumber(index));
                        var columnId = $.common.sprintf("<input type='hidden' fieldName='id' name='groupMemberList[%s].id' value='%s'>", index, row.id);
                        return  columnIndex + memberIdInput + orderNo + columnId + $.table.serialNumber(index);
                    }
                },
              {
                field: 'empName',
                align: 'center',
                title: '姓名',
                width: '100',
                formatter: function (value, row, index, field) {
                  // 如果是 团队长，此处不可修改
                  if(row.role === "ROLE_A"){
                    return $.common.sprintf("<input readonly class='form-control' fieldName='%s' type='text' id='groupMemberList_%s_empName' name='groupMemberList[%s].empName' value='%s'>", field, index, index, value);
                  }
                  var codeId = $.common.sprintf("groupMemberList_%s_empCode", index),
                    nameId = $.common.sprintf("groupMemberList_%s_empName", index),
                    nameName = $.common.sprintf("groupMemberList[%s].empName", index);
                  var data = {
                    fieldName: field,
                    id:nameId,
                    name:nameName,
                    inputValue: value,
                    clickFun: $.common.sprintf('choiceUserAndOrg("%s","%s","S","","%s", "choseUserCallBackRow")',codeId,nameId,index)
                  };
                  return $("#inputTpl").tmpl(data).html();
                }
              },
              {
                field: 'empCode',
                align: 'center',
                title: '工号',
                width: '100',
                formatter: function (value, row, index, field) {
                  return $.common.sprintf("<input class='form-control' readonly required type='text' id='groupMemberList_%s_empCode' name='groupMemberList[%s].empCode' value='%s'>", index, index, value);
                }
              },
              {
                field: 'deptCode',
                align: 'center',
                title: '',
                width: '1',
                formatter: function (value, row, index) {
                  var deptCode = $.common.sprintf("<input type='hidden' id='groupMemberList_%s_deptCode' name='groupMemberList[%s].deptCode' value='%s'>", index, index, value);
                  return  deptCode;
                }
              },
              {
                field: 'deptName',
                align: 'center',
                title: '部门',
                width: '250',
                formatter: function (value, row, index, field) {
                  var deptName = $.common.sprintf("<input type='hidden' id='groupMemberList_%s_deptName' name='groupMemberList[%s].deptName' value='%s'>", index, index, value);
                  return deptName + value;
                }
              },
                {
                    field: 'role',
                    align: 'center',
                    title: '角色',
                    formatter: function (value, row, index, field) {
                        var data = {
                            name: "groupMemberList[" + index + "].role",
                            selectValue: value,
                            selectItems: value === 'ROLE_A' ? groupRoleList : groupRoleSelect,
                            disabled: value === 'ROLE_A'

                        };
                        return $("#selectDictTpl").tmpl(data).html();
                    }
                },

                {
                    field: 'task',
                    align: 'center',
                    title: '主要职责',
                    formatter: function (value, row, index, field) {
                        var html = $.common.sprintf("<input class='form-control'  fieldName='%s' type='text' name='groupMemberList[%s].task' value='%s'>", field, index, value);
                        return html;
                    },
                    footerFormatter: function (value) {
                        return "抵押金合计(万元)";
                    }
                },
                {
                    field: 'riskDetailAmt',
                    align: 'left',
                    title: '抵押金（万元）',
                    formatter: function (value, row, index, field) {
                      if (group.riskType === "0") {
                        return $.common.sprintf("<input readonly class='form-control'  type='text' id='%s' name='groupMemberList[%s].riskDetailAmt' value='%s' onBlur='refreshRiskDetailAmtSum(\"" + id + "\",%s)' >", "riskDetailAmt" + index, index, 0, index);
                      } else {
                        if (row.delStatus === '1') { //退出
                          var html = $.common.sprintf("<input readonly class='form-control'  type='text' id='%s' name='groupMemberList[%s].riskDetailAmt' value='%s' onBlur='refreshRiskDetailAmtSum(\"" + id + "\",%s)' >", "riskDetailAmt" + index, index, value, index);
                          return html;
                        } else {
                          return $.common.sprintf("<input class='form-control'  name='groupMemberList[%s].riskDetailAmt' value='%s' onBlur='refreshRiskDetailAmtSum(\"" + id + "\",%s)' id='%s'>", index, value, index, "riskDetailAmt" + index);
                        }
                      }
                    },
                    footerFormatter: function (value) {
                        var riskDetailAmtSum = 0;
                        for (var i in value) {
                            if (value[i].delStatus !== "1") {
                              riskDetailAmtSum += parseFloat(value[i].riskDetailAmt);
                            }
                        }
                        var html = $.common.sprintf("<span id='%s' >" + riskDetailAmtSum.toFixed(4) + "</span>", "countRiskDetailAmt");
                        return html;
                    }
                },
                {
                    field: 'delStatus',
                    align: 'left',
                    title: '是否退出',
                    formatter: function (value, row, index, field) {
                        var html = $.common.sprintf("<input class='form-control' type='hidden' name='groupMemberList[%s].delStatus' value='%s'>", index, value);
                        var valueName = '正常'
                      if (value === "1" || value === "-2") {
                        valueName = '退出'
                      } else if (value === '-1') {
                        valueName = '新进'
                      }
                        html += $.common.sprintf("<input readonly class='form-control' type='text' name='groupMemberList[%s].delStatusName' value='%s'>", index, valueName);
                        return html;
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.role !== "ROLE_A") {
                            if (row.delStatus === "-1") {
                                actions.push('<a class="btn btn-danger btn-xs" href=\"javascript:sub.delColumnByIndex(\'index\',' + (index + 1) + ')\">删除</a>');
                            } else if(row.delStatus === "0") {
                              var operValue = '退出';
                              actions.push('<a class="btn btn-danger btn-xs" id="operate' + index + '" href="javascript:exitMember(\'' + index + '\')">' + operValue + '</a>');
                            } else if(row.delStatus === '1'){
                              var operValue = '恢复';
                              actions.push('<a class="btn btn-danger btn-xs" id="operate' + index + '" href="javascript:exitMember(\'' + index + '\')">' + operValue + '</a>');
                            }
                        }
                        return actions.join('\n');
                    }
                }
            ]
        };
        $.table.init(options);
    }

    function exitMember(index) {
        var delStatusInput = $("input[name='groupMemberList[" + index + "].delStatus']");
        if (delStatusInput.val() === "0") {
            delStatusInput.val("1")
        } else {
            let hasMember = false;
            $("#groupMember-table").bootstrapTable('getData').forEach((val, i) => {
                if (index != i) {
                    if ($("#groupMemberList_" + index + "_empCode").val() === $("#groupMemberList_" + i + "_empCode").val()) {
                        hasMember = true
                    }
                }
            });
            if (hasMember) {
                $.modal.alertWarning("此成员已添加在列表中，请勿重复添加");
            } else {
                delStatusInput.val("0")
            }
        }
        sub.editColumn();

    }

    /**
     * 根据数据初始化 年度目标
     */
    function initYearTarget(){
      if(groupTargetDtoList == null || groupTargetDtoList.length ==0){
        addYearTarget();
      }else{
        var compare = function (obj1, obj2) {
          var val1 = obj1.stageYear;
          var val2 = obj2.stageYear;
          if (val1 < val2) {
            return -1;
          } else if (val1 > val2) {
            return 1;
          } else {
            return 0;
          }
        }
        groupTargetDtoList = groupTargetDtoList.sort(compare);
        groupTargetDtoList.forEach(groupTargetDto => {
          addYearTarget(groupTargetDto.groupTargetList,groupTargetDto.groupTargetRules);
        });
      }
    }

    /**
     * 增加年度目标
     */
    function addYearTarget(targetList,groupTargetRules) {

      var yearTargetLi = $("#yearTargetIndex").children("li");
      var index = yearTargetLi.length + 1;
      $("#stageTotal").val(index);  //设置总期数
      var data = {
        className: index == 1 ? "active" : "",
        tabIndex: "yearTargetTab-" + index,
        index:index,
        tabLabel: getYearTargetLabel(index),
        tableId: "yearTargetTab-" + index + "-table"
      };

      $("#yearTargetIndex").append($("#yearTargetLiTpl").tmpl(data).html());
      $("#yearTargetContent").append($("#yearTargetContentTpl").tmpl(data).html());

      // 渲染 table
      initPerformanceTable(data.tableId, index, targetList,groupTargetRules);
      setTimeout(function () {
        $("#yearTargetIndex").children("li:first").addClass("active ");
        $("#yearTargetIndex").children("li:first").children("a").attr("aria-expanded",true);
      }, 500);
    }

    /**
     * 初始 年度指标 表格
     * @param { 表格 ID } id
     * @param { 第几期 } stageYear
     */
    function initPerformanceTable(id, stageYear, data,groupTargetRules) {
      var data = data || [];
      var groupTargetRules = groupTargetRules || {
        jlxzX1: "",
        jlxzX2: "",
        jlxzX3: "",
        jlxzX4: "",
        jlxzX5: "",
        x1Desc: "扣抵押金",
        x2Desc: "返还全部抵押金",
        x3Desc: "奖励1倍抵押金",
        x4Desc: "奖励2倍抵押金",
        x5Desc: "奖励4倍抵押金"
      };

      var footerStyle = function (column) {
        return {
          price: {
            css: {
              color: 'black',
              'font-weight': 'normal'
            }
          },
          duty: {
            css: {
              color: 'red',
              'font-weight': 'normal'
            }
          },
        } [column.field]
      };

      var options = {
        id: id,
        data: data,
        toolbar: "yearTargetTab-" + stageYear + "-toolbar",
        pagination: false,
        showSearch: false,
        showRefresh: false,
        showToggle: false,
        showColumns: false,
        footerStyle: footerStyle,
        showFooter: true,
        sidePagination: "client",
        columns: [{
          checkbox: true
        }, {
          field: 'index',
          align: 'center',
          title: "序号",
          width: '40',
          formatter: function (value, row, index, field) {
            var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
            var orderNo = $.common.sprintf("<input type='hidden' name='groupTargetDtoList[%s].groupTargetList[%s].orderNo' value='%s'>",stageYear, index, $.table.serialNumber(index));
            var columnId = $.common.sprintf("<input type='hidden' name='groupTargetDtoList[%s].groupTargetList[%s].id' value='%s'>", index, row.id);
            return columnIndex + orderNo + columnId + $.table.serialNumber(index);
          }
        },
          {
            field: 'orderNo',
            align: 'center',
            title: "",
            width: '1',
            formatter: function (value, row, index) {
              var orderNo = $.common.sprintf("<input type='hidden' name='groupTargetDtoList[%s].groupTargetList[%s].orderNo' value='%s'>",stageYear, index, $.table.serialNumber(index));
              return orderNo;
            }
          },
          {
            field: 'targetName',
            align: 'center',
            title: '指标名称',
            formatter: function (value, row, index, field) {
              return $.common.sprintf("<input class='form-control' required fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].targetName' value='%s'>", field, stageYear, index, value);
            }
          },
          {
            field: 'ratio',
            align: 'center',
            width: '70',
            title: '权重(%)',
            formatter: function (value, row, index, field) {
              return $.common.sprintf("<input class='form-control' required fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].ratio' value='%s' onblur='refreshRatio()' >", field, stageYear, index, value);
            },
            footerFormatter: function (value, row, index) {
              var totalRatio = 0;
              for (var i in value) {
                totalRatio += parseFloat(value[i].ratio);
              }
              if(totalRatio > 100){
                $.modal.alertError('权重合计 不能为超过 100。');
                return;
              }
              return "";
            }
          },
          {
            field: 'valueBeforYear',
            align: 'center',
            title: '上年度实际',
            width: '80',
            formatter: function (value, row, index, field) {
              return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].valueBeforYear' value='%s' onblur='refreshRatio()'>", field, stageYear, index, value);
            },
            footerFormatter: function (value, row, index) {
              return "奖励额度（万元):";
            }
          },
          {
            field: 'gradeT1',
            align: 'center',
            title: 'T1',
            width: '80',
            formatter: function (value, row, index, field) {
              return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].gradeT1' value='%s'>", field, stageYear, index, value);
            },
            footerFormatter: function (value, row, index) {
              var htmlStr = `<input class='form-control' type='text' name='groupTargetDtoList[%s].groupTargetRules.jlxzX1' placeholder='%s' value="%s" onblur='refreshJlxzDesc(this)' >
                           <input type='hidden' name='groupTargetDtoList[%s].groupTargetRules.x1Desc' value="%s">
                           <span class='help-block m-b-none'><i class='fa fa-info-circle'></i>%s</span>`;
              return $.common.sprintf(htmlStr,stageYear, -1,groupTargetRules.jlxzX1,stageYear,groupTargetRules.x1Desc,groupTargetRules.x1Desc);
            }
          },
          {
            field: 'gradeT2',
            align: 'center',
            title: 'T2',
            width: '80',
            formatter: function (value, row, index, field) {
              return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].gradeT2' value='%s'>", field, stageYear, index, value);
            },
            footerFormatter: function (value, row, index) {
              var htmlStr = `<input class='form-control' type='text' name='groupTargetDtoList[%s].groupTargetRules.jlxzX2' placeholder='%s' value="%s" onblur='refreshJlxzDesc(this)'>
                           <input type='hidden' name='groupTargetDtoList[%s].groupTargetRules.x2Desc' value="%s">
                           <span class='help-block m-b-none'><i class='fa fa-info-circle'></i>%s</span>`;
              return $.common.sprintf(htmlStr,stageYear, 0,groupTargetRules.jlxzX2,stageYear,groupTargetRules.x2Desc,groupTargetRules.x2Desc);
            }
          },
          {
            field: 'gradeT3',
            align: 'center',
            title: 'T3',
            width: '80',
            formatter: function (value, row, index, field) {
              return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].gradeT3' value='%s'>", field, stageYear, index, value);
            },
            footerFormatter: function (value, row, index) {
              var htmlStr = `<input class='form-control' type='text' name='groupTargetDtoList[%s].groupTargetRules.jlxzX3' placeholder='%s' value="%s" onblur='refreshJlxzDesc(this)'>
                           <input type='hidden' name='groupTargetDtoList[%s].groupTargetRules.x3Desc' value="%s">
                           <span class='help-block m-b-none'><i class='fa fa-info-circle'></i>%s</span>`;
              return $.common.sprintf(htmlStr,stageYear, 1,groupTargetRules.jlxzX3,stageYear,groupTargetRules.x3Desc,groupTargetRules.x3Desc);
            }
          },
          {
            field: 'gradeT4',
            align: 'center',
            title: 'T4',
            width: '80',
            formatter: function (value, row, index, field) {
              return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].gradeT4' value='%s'>", field, stageYear, index, value);
            },
            footerFormatter: function (value, row, index) {
              var htmlStr = `<input class='form-control' type='text' name='groupTargetDtoList[%s].groupTargetRules.jlxzX4' placeholder='%s' value="%s" onblur='refreshJlxzDesc(this)'>
                           <input type='hidden' name='groupTargetDtoList[%s].groupTargetRules.x4Desc' value="%s">
                           <span class='help-block m-b-none'><i class='fa fa-info-circle'></i>%s</span>`;
              return $.common.sprintf(htmlStr,stageYear, 2,groupTargetRules.jlxzX4,stageYear,groupTargetRules.x4Desc,groupTargetRules.x4Desc);
            }
          },
          {
            field: 'gradeT5',
            align: 'center',
            title: 'T5',
            width: '80',
            formatter: function (value, row, index, field) {
              return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].gradeT5' value='%s'>", field, stageYear, index, value);
            },
            footerFormatter: function (value, row, index) {
              var htmlStr = `<input class='form-control' type='text' name='groupTargetDtoList[%s].groupTargetRules.jlxzX5' placeholder='%s' value="%s" onblur='refreshJlxzDesc(this)'>
                           <input type='hidden' name='groupTargetDtoList[%s].groupTargetRules.x5Desc' value="%s">
                           <span class='help-block m-b-none'><i class='fa fa-info-circle'></i>%s</span>`;
              return $.common.sprintf(htmlStr,stageYear, 4,groupTargetRules.jlxzX5,stageYear,groupTargetRules.x5Desc,groupTargetRules.x5Desc);
            }
          },
          {
            field: 'dataSource',
            align: 'center',
            title: '数据来源',
            formatter: function (value, row, index, field) {
              return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].dataSource' value='%s'>", field, stageYear, index, value);
            }
          }
        ]
      };
      $.table.init(options);
    }

    function refreshRiskDetailAmtSum(tableId, rowIndex) {
        var riskDetailAmt = $("input[name='groupMemberList[" + rowIndex + "].riskDetailAmt']").val()

        if (group.riskType + '' === '0' && riskDetailAmt > 0) {
            $.modal.alertError('非抵押型团队，抵押金合计不能大于0');
            $("input[name='groupMemberList[" + rowIndex + "].riskDetailAmt']").val(0)
            return;
        }
        sub.editColumn();
    }

    function refreshRatio() {
        sub.editColumn();
      changeTargetRule(riskType == 1);
    }

    function deleteProduct(){
      sub.delColumn();
      refreshProductTableSelect2();
    }

    /**
     * 删除 绩效方案
     */
    function deletePerformanceTable(){
      sub.delColumn();
      changeTargetRule(riskType == 1);
    }

    /**
     * 刷新 奖励细节描述
     */
    function refreshJlxzDesc(e){
      var defaultVal = $(e).attr("placeholder");
      var jlxz = $(e).val();
      var desc = "";
      if(jlxz == "") {
        if(defaultVal == -1){
          desc = "扣抵押金";
        }else if(defaultVal == 0){
          desc = "返还全部抵押金";
        }else{
          desc = "奖励"+defaultVal+"倍抵押金";
        }
      }else{
        desc = "奖励 " + jlxz + " 万元";
      }
      $(e).parent(".th-inner").find(".help-block").html("<i class='fa fa-info-circle'></i>" + desc);
      $(e).parent(".th-inner").find("input[type='hidden']").val(desc);
    }

    /**
     * 刷新 产品 中的 select2 样式
     */
    function refreshProductTableSelect2(){
      var selectItems = $("#product-table .select");
      for(var i = 0; i < selectItems.length; i++){
        var data = $(selectItems[i]).attr("selectValue");
        $(selectItems[i]).val(data.split(","));
      }
      $("#product-table .select").select2();
    }

    /**
     * 增加 行
     */
    function addColumnSub(id) {
        var row = {};
        var data = $("#" + id).bootstrapTable("getData");
        var count = data.length;
        var dataId = 100000 + count;
        if (id === "product-table") {
            row = {
                id: dataId,
                orderNo: "",
                brandNo: "",
                zzBase: "",
                productLine: "",
                isNew: "0",
                projectName: "",
                projectCode: "",
                projectStatus: ""
            };
        } else if (id.indexOf("yearTargetTab-") > -1) {
            row = {
                id: dataId,
                targetName: "",
                ratio: "",
                valueBeforYear: "",
                gradeT1: "",
                gradeT2: "",
                gradeT3: "",
                gradeT4: "",
                gradeT5: "",
                dataSource: ""
            };
        } else {
            row = {
                id: dataId,
                empCode: "",
                empName: "",
                deptCode: "",
                deptName: "",
                role: "",
                task: "",
                riskDetailAmt: "0",
                delStatus: "-1"
            }
        }
        sub.addColumn(row, id);
      if (id === "product-table") {
        refreshProductTableSelect2();
      }else if (id.indexOf("yearTargetTab-") > -1) {
        changeTargetRule(riskType == 1);
      }
    }

    /**
     * 获取 年度指标的 期数 名称
     * @param {年度指标 期数 序列} num
     * @returns
     */
    function getYearTargetLabel(num) {
        return '第' + toChinesNum(num) + "期";
    }

    /**
     * 数字 转 汉语 大写
     * @param {数字} num
     * @returns
     */
    function toChinesNum(num) {
        var changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
        var unit = ["", "十", "百", "千", "万"];
        num = parseInt(num);
        var getWan = (temp) => {
            var strArr = temp.toString().split("").reverse();
            var newNum = "";
            for (var i = 0; i < strArr.length; i++) {
                newNum = (i == 0 && strArr[i] == 0 ? "" : (i > 0 && strArr[i] == 0 && strArr[i - 1] == 0 ? "" : changeNum[strArr[i]] + (strArr[i] == 0 ? unit[0] : unit[i]))) + newNum;
            }
            return newNum;
        }
        var overWan = Math.floor(num / 10000);
        var noWan = num % 10000;
        if (noWan.toString().length < 4) {
            noWan = "0" + noWan;
        }
        return overWan ? getWan(overWan) + "万" + getWan(noWan) : getWan(num);
    }

    //流程跟踪
    function openProcessTrack(processId) {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processId);
    }
</script>
<style>

  a.a-no-btn{
    text-decoration: underline;
    color: #337ab7;
  }

  .bootstrap-table .fixed-table-container .table.top th {
    vertical-align: top;
  }

  .select2-container--bootstrap .select2-results__option[aria-selected=true] {
    background-color: #c8c8c8;
  }
    .bs-component {
        padding: 7px 6px 3px;
    }

    ul.nav-pills {
        top: 60px;
        position: fixed;
    }

    .scrollspy-main {
        height: 850px;
        overflow: auto;
        position: relative;
    }

    .nav .nav-header {
        font-size: 18px;
        color: #92B901;
    }

    textarea.form-control {
        width: 99%;
    }

    /* 年度目标 Tab 里的样式 */
    .tabs-container .tabs-left > .nav-tabs > li.active > a, .tabs-container .tabs-left > .nav-tabs > li.active > a:hover, .tabs-container .tabs-left > .nav-tabs > li.active > a:focus {
        border-top: #e7eaec solid 1px;
        border-left: #1c6ac7 solid 4px;
    }


    .tabs-container .tabs-left > .nav-tabs .active > a, .tabs-container .tabs-left > .nav-tabs .active > a:hover, .tabs-container .tabs-left > .nav-tabs .active > a:focus {
        border-left-color: #1c6ac7 transparent #e7eaec #e7eaec;
        border-right-color: #fff;
    }

    .tabs-container .tabs-left > .nav-tabs > li.active > a, .tabs-container .tabs-left > .nav-tabs > li.active > a:hover, .tabs-container .tabs-left > .nav-tabs > li.active > a:focus {
        border-left: 4px solid #1c6ac7;
    }

    #row40 .tabs-left .tab-content .panel-body {
        width: 90%;
        margin-left: 10%;
    }

    #collapseFive .tabs-container .tabs-left > #yearTargetIndex.nav-tabs {
        width: 10%;
        margin-top: 0px;
        margin-right: 1px;
    }

    .operation {
        position: absolute;
    }

    #collapseFive .panel-body .form-group {
        margin: 15px 0px;
    }

    #collapseFive .panel-body .form-group label {
        margin: 0px 0px 10px;
    }

    #collapseTwo > .panel-body {
        padding: 0px;
    }

    #row7 .panel-body, #row5 .panel-body {
        padding: 0px;
    }

    #row7 .panel-body .select-table, #row5 .panel-body .select-table {
        margin-top: 0px;
        border: 0px solid #ddd;
        border-radius: 0px 0px 6px 6px;
    }

    #yearTargetContent .panel-body {
        padding: 10px;
        padding-top: 0px;
    }

    #yearTargetContent .select-table {
        border: 0px;
        margin-top: 0px;
        border-radius: 0px;
        padding-top: 0px;
        box-shadow: 0px 0px 0px;
    }

    .note-editor.note-frame.panel {
        border: 0px;
        margin-bottom: 0px;
    }

    .note-toolbar.panel-heading {
        border-top: #f6f7f9 solid 1px;
        background-color: #dfebfe;
    }

    .help-block.m-b-none {
        font-size: 12px;
        font-weight: normal;
        color: #737373;
    }

    .input-group {
        width: 90%;
    }
</style>
</body>
</html>

<!-- 年度目标 素引模板 -->
<script id="yearTargetLiTpl" type="text/x-jquery-tmpl">
  <div>
    <li class="${className}">
      <a data-toggle="tab" href="#${tabIndex}" id="a-${tabIndex}">
        ${tabLabel}
      </a>
    </li>
  </div>
</script>

<!-- 年度目标 内容模板-->
<script id="yearTargetContentTpl" type="text/x-jquery-tmpl">
  <div>
    <div id="${tabIndex}" class="tab-pane ${className}">
      <div class="panel-body">
        <div id="${tabIndex}-toolbar">
          <button type="button" class="btn btn-danger btn-xs" onclick="addColumnSub('${tableId}')"><i class="fa fa-plus"> 增加</i></button>
          <button type="button" class="btn btn-danger btn-xs" onclick="deletePerformanceTable()"><i class="fa fa-minus"> 删除</i></button>
        </div>
        <input readonly type="hidden" id="groupTargetDtoList_${index}_stageYear" name="groupTargetDtoList[${index}].stageYear" value="${index}">
        <div class="col-sm-12 select-table table-bordered table-hover">
          <table id="${tableId}" class="top"></table>
        </div>

      </div>
    </div>
  </div>
  </script>
<!-- 单选 模板 -->
<script id="radiosTpl" type="text/x-jquery-tmpl">
  <div>
    <div class="radio">
      {{each(i,item) radios}}
        <label class="radio-inline">
          <input type="radio" fieldName="${fieldName}" name="${name}" id="${item.id}" value="${item.value}" {{if checkValue===item.value}}checked{{/if}}> ${item.label}
        </label>
      {{/each}}
    </div>
  </div>
</script>

<!--
  下拉选择：
    参数说明：
      name: 组件对应 name
      type: 需要选中的value
      selectItems: 数组
        value: 对应值
        label: 对应显示值
-->
<script id="selectTpl" type="text/x-jquery-tmpl">
  <div>
    <select class='form-control' name='${name}' fieldName="${fieldName}">
      <option value="">------ 请选择  -----</option>
      {{each(i,item) selectItems}}
          <option value="${item.value}" {{if selectValue===item.value}}selected{{/if}}>${item.label}</option>
      {{/each}}
    </select>
  </div>
</script>

<script id="select2Tpl" type="text/x-jquery-tmpl">
  <div>
    <select class='selectpicker select' name='${name}' fieldName="${fieldName}" multiple selectValue="${selectValue}">
      {{each(i,item) selectItems}}
          <option value="${item.dictValue}">${item.dictName}</option>
      {{/each}}
    </select>
  </div>


</script>

<script id="selectDictTpl" type="text/x-jquery-tmpl">
  <div>
<input type='hidden' {{if selectValue==='ROLE_A'}}name='${name}'{{/if}} value='${selectValue}'>
    <select required class='form-control' name='${name}' {{if disabled===true}}disabled{{/if}} >
      <option value="">------ 请选择  -----</option>
      {{each(i,item) selectItems}}
          <option value="${item.dictValue}" {{if selectValue===item.dictValue}}selected{{/if}}>${item.dictName}</option>
      {{/each}}
    </select>
  </div>
</script>

<script id="inputTpl" type="text/x-jquery-tmpl">
<div>
  <input class='form-control' required type='text' id="${id}" name='${name}' value='${inputValue}' {{if clickFun !== null}} onclick="${clickFun}" {{/if}} >
</div>
</script>

<script id="choiceUserTpl" type="text/x-jquery-tmpl">
<div>
  <input class='form-control' required
    id='${userName_idEl}' name='${userName_nameEl}'
    onclick='choiceUserAndOrg("${userCode_idEl}","${userName_idEl}","S", "${orgCode}", "${index}", "choseUserCallBackRow")' type='text'  value='${userName}' >
</div>
</script>
