<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改 创值团队策划通知')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-groupPlanNotice-edit" th:object="${groupPlanNotice}">
            <input name="noticeId" th:field="*{noticeId}" type="hidden">
            <div class="form-group">
                <label class="col-sm-3 control-label">创值团队主键：</label>
                <div class="col-sm-8">
                    <input name="groupId" th:field="*{groupId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">创值团队名称：</label>
                <div class="col-sm-8">
                    <input name="groupName" th:field="*{groupName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">管理模式：厂部级C； 基地、分子公司、事业部级B；  公司级A：</label>
                <div class="col-sm-8">
                    <input name="manageLevel" th:field="*{manageLevel}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">负责单位编码：</label>
                <div class="col-sm-8">
                    <input name="fzDeptCode" th:field="*{fzDeptCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">负责单位名称：</label>
                <div class="col-sm-8">
                    <input name="fzDeptName" th:field="*{fzDeptName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">团队长工号：</label>
                <div class="col-sm-8">
                    <input name="leaderId" th:field="*{leaderId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">团队长姓名：</label>
                <div class="col-sm-8">
                    <input name="leaderName" th:field="*{leaderName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">截止日期：</label>
                <div class="col-sm-8">
                    <input name="endDate" th:field="*{endDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">下发人：</label>
                <div class="col-sm-8">
                    <input name="xfUserId" th:field="*{xfUserId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">下发单位代码：规划科技部：</label>
                <div class="col-sm-8">
                    <input name="xfDeptCode" th:field="*{xfDeptCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">下发时间：</label>
                <div class="col-sm-8">
                    <input name="xfDate" th:field="*{xfDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" th:include="include :: initRadio(id='status', name='status',dictType='${dictType}' ,value=${groupPlanNotice.status} ,labelName='状态：草稿，下发')">
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">
                <label class="col-sm-3 control-label">扩展字段3：</label>
                <div class="col-sm-8">
                    <input name="extra3" th:field="*{extra3}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',dictType='${dictType}' ,value=${groupPlanNotice.delStatus} ,labelName='删除状态')">
            </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>

    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "kczg/groupPlanNotice";

        $("#form-groupPlanNotice-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-groupPlanNotice-edit').serialize());
            }
        }

    </script>
</body>
</html>
