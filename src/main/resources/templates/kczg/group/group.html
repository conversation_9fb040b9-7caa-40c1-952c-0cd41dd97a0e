<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('20_02创值团队列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>流水号：</label>
                                <input type="text" name="serialNo"/>
                            </li>
                            <li>
                                <label>创值团队名称：</label>
                                <input type="text" name="groupName"/>
                            </li>
                            <li>
                                <label>管理模式：</label>
                                <input type="text" name="manageLevel"/>
                            </li>
                            <li>
                                <label>管理单位：</label>
                                <input type="text" name="manageDept"/>
                            </li>
                            <li>
                                <label>负责单位编码：</label>
                                <input type="text" name="fzDeptCode"/>
                            </li>
                            <li>
                                <label>负责单位名称：</label>
                                <input type="text" name="fzDeptName"/>
                            </li>
                            <li>
                                <label>团队长工号：</label>
                                <input type="text" name="leaderId"/>
                            </li>
                            <li>
                                <label>团队长姓名：</label>
                                <input type="text" name="leaderName"/>
                            </li>
                            <li>
                                <label>联系电话：</label>
                                <input type="text" name="tel"/>
                            </li>
                            <li>
                                <label>产品类型：</label>
                                <select name="groupType" th:with="type=${@dict.getDictList('MPTY','${dictType}')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.paraName}" th:value="${dict.paraValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>风险金类型：</label>
                                <select name="riskType" th:with="type=${@dict.getDictList('MPTY','${dictType}')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.paraName}" th:value="${dict.paraValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>责任开始日期：</label>
                                <input type="text" name="dutyStartDate"/>
                            </li>
                            <li>
                                <label>责任结束日期：</label>
                                <input type="text" name="dutyEndDate"/>
                            </li>
                            <li>
                                <label>年度：</label>
                                <input type="text" name="stageYear"/>
                            </li>
                            <li>
                                <label>奖励来源：</label>
                                <input type="text" name="rewardSource"/>
                            </li>
                            <li>
                                <label>奖励金额(万元)不含抵押金：</label>
                                <input type="text" name="totalAmount"/>
                            </li>
                            <li>
                                <label>是否指令型：</label>
                                <input type="text" name="isChtz"/>
                            </li>
                            <li>
                                <label>项目状态：</label>
                                <select name="groupStatus" th:with="type=${@dict.getDictList('MPTY','${dictType}')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.paraName}" th:value="${dict.paraValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-bordered">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kczg/group";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "20_02创值团队",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'groupId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'serialNo',
                    title: '流水号'
                },
                {
                    field: 'groupName',
                    title: '名称'
                },
                {
                    field: 'manageLevel',
                    title: '管理模式'
                },
                {
                    field: 'manageDept',
                    title: '管理单位'
                },
                {
                    field: 'fzDeptCode',
                    title: '负责单位编码'
                },
                {
                    field: 'fzDeptName',
                    title: '负责单位名称'
                },
                {
                    field: 'leaderId',
                    title: '团队长工号'
                },
                {
                    field: 'leaderName',
                    title: '团队长姓名'
                },
                {
                    field: 'tel',
                    title: '联系电话'
                },
                {
                    field: 'groupType',
                    title: '产品类型'
                },
                {
                    field: 'riskType',
                    title: '风险金类型'
                },
                {
                    field: 'dutyStartDate',
                    title: '责任开始日期'
                },
                {
                    field: 'dutyEndDate',
                    title: '责任结束日期'
                },
                {
                    field: 'stageYear',
                    title: '年度'
                },
                {
                    field: 'rewardSource',
                    title: '奖励来源'
                },
                {
                    field: 'totalAmount',
                    title: '奖励金额(万元)不含抵押金'
                },
                {
                    field: 'isChtz',
                    title: '是否指令型'
                },
                {
                    field: 'groupStatus',
                    title: '项目状态'
                },
                {
                    field: 'delStatus',
                    title: '删除状态'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    field: 'deleteUserLabel',
                    title: '删除人'
                },
                {
                    field: 'deleteDate',
                    title: '删除时间'
                },
                {
                    field: 'recordVersion',
                    title: '版本号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.groupId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.groupId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
