<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('20_04创值团队方案历史列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>变更申请主键、年度总结主键：</label>
                                <input type="text" name="bizId"/>
                            </li>
                            <li>
                                <label>来源：变更BG、年度总结ZJ：</label>
                                <input type="text" name="bizSource"/>
                            </li>
                            <li>
                                <label>流水号：</label>
                                <input type="text" name="serialNo"/>
                            </li>
                            <li>
                                <label>创值团队名称：</label>
                                <input type="text" name="groupName"/>
                            </li>
                            <li>
                                <label>管理模式：厂部级C； 基地、分子公司、事业部级B；  公司级A：</label>
                                <input type="text" name="manageLevel"/>
                            </li>
                            <li>
                                <label>管理单位：</label>
                                <input type="text" name="manageDept"/>
                            </li>
                            <li>
                                <label>负责单位编码：</label>
                                <input type="text" name="fzDeptCode"/>
                            </li>
                            <li>
                                <label>负责单位名称：</label>
                                <input type="text" name="fzDeptName"/>
                            </li>
                            <li>
                                <label>团队长工号：</label>
                                <input type="text" name="leaderId"/>
                            </li>
                            <li>
                                <label>团队长姓名：</label>
                                <input type="text" name="leaderName"/>
                            </li>
                            <li>
                                <label>联系电话：</label>
                                <input type="text" name="tel"/>
                            </li>
                            <li>
                                <label>钢铁产品类、非钢铁产品类：</label>
                                <select name="groupType" th:with="type=${@dict.getDictList('MPTY','${dictType}')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.paraName}" th:value="${dict.paraValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>风险金类型：1抵押、0不抵押：</label>
                                <select name="riskType" th:with="type=${@dict.getDictList('MPTY','${dictType}')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.paraName}" th:value="${dict.paraValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>变革思路：</label>
                                <input type="text" name="bgsl"/>
                            </li>
                            <li>
                                <label>团队主要职责：</label>
                                <input type="text" name="tdzyzz"/>
                            </li>
                            <li>
                                <label>创值团队共开展几年，一年为一期：</label>
                                <input type="text" name="stageTotal"/>
                            </li>
                            <li>
                                <label>创值团队当前所处年度：第1期，第2期……：</label>
                                <input type="text" name="stageYear"/>
                            </li>
                            <li>
                                <label>激励兑现说明：</label>
                                <input type="text" name="jjdxsm"/>
                            </li>
                            <li>
                                <label>评审结果：</label>
                                <input type="text" name="reviewFlag"/>
                            </li>
                            <li>
                                <label>扩展字段1：</label>
                                <input type="text" name="extra1"/>
                            </li>
                            <li>
                                <label>扩展字段2：</label>
                                <input type="text" name="extra2"/>
                            </li>
                            <li>
                                <label>扩展字段3：</label>
                                <input type="text" name="extra3"/>
                            </li>
                            <li>
                                <label>扩展字段4：</label>
                                <input type="text" name="extra4"/>
                            </li>
                            <li>
                                <label>扩展字段5：</label>
                                <input type="text" name="extra5"/>
                            </li>
                            <li>
                                <label>删除状态：</label>
                                <select name="delStatus" th:with="type=${@dict.getDictList('MPTY','${dictType}')}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.paraName}" th:value="${dict.paraValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "kczg/groupPlanHis";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "20_04创值团队方案历史",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'planHisId',
                    title: '方案表主键',
                    visible: false
                },
                {
                    field: 'bizId',
                    title: '变更申请主键、年度总结主键'
                },
                {
                    field: 'bizSource',
                    title: '来源：变更BG、年度总结ZJ'
                },
                {
                    field: 'serialNo',
                    title: '流水号'
                },
                {
                    field: 'groupName',
                    title: '创值团队名称'
                },
                {
                    field: 'manageLevel',
                    title: '管理模式：厂部级C； 基地、分子公司、事业部级B；  公司级A'
                },
                {
                    field: 'manageDept',
                    title: '管理单位'
                },
                {
                    field: 'fzDeptCode',
                    title: '负责单位编码'
                },
                {
                    field: 'fzDeptName',
                    title: '负责单位名称'
                },
                {
                    field: 'leaderId',
                    title: '团队长工号'
                },
                {
                    field: 'leaderName',
                    title: '团队长姓名'
                },
                {
                    field: 'tel',
                    title: '联系电话'
                },
                {
                    field: 'groupType',
                    title: '钢铁产品类、非钢铁产品类'
                },
                {
                    field: 'riskType',
                    title: '风险金类型：1抵押、0不抵押'
                },
                {
                    field: 'bgsl',
                    title: '变革思路'
                },
                {
                    field: 'tdzyzz',
                    title: '团队主要职责'
                },
                {
                    field: 'stageTotal',
                    title: '创值团队共开展几年，一年为一期'
                },
                {
                    field: 'stageYear',
                    title: '创值团队当前所处年度：第1期，第2期……'
                },
                {
                    field: 'remark',
                    title: '备注说明'
                },
                {
                    field: 'jjdxsm',
                    title: '激励兑现说明'
                },
                {
                    field: 'reviewFlag',
                    title: '评审结果'
                },
                {
                    field: 'extra1',
                    title: '扩展字段1'
                },
                {
                    field: 'extra2',
                    title: '扩展字段2'
                },
                {
                    field: 'extra3',
                    title: '扩展字段3'
                },
                {
                    field: 'extra4',
                    title: '扩展字段4'
                },
                {
                    field: 'extra5',
                    title: '扩展字段5'
                },
                {
                    field: 'delStatus',
                    title: '删除状态'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    field: 'deleteUserLabel',
                    title: '删除人'
                },
                {
                    field: 'deleteDate',
                    title: '删除时间'
                },
                {
                    field: 'recordVersion',
                    title: '版本号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.planHisId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.planHisId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>
