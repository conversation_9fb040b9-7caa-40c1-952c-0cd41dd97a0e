<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
  <th:block th:include="include :: header('新增 创值团队方案')"/>

  <th:block th:include="include :: baseJs"/>
  <th:block th:include="include :: summernote-css"/>
  <th:block th:include="include :: summernote-js"/>
  <th:block th:include="include :: jquery-tmpl"/>
  <th:block th:include="include :: sub-tab-commons"/>

  <th:block th:include="kczginclude :: toolbox-css" />
  <th:block th:include="kczginclude :: toolbox-js" />

</head>
<body class="white-bg" data-spy="scroll" data-target="#catalogueItem" data-offset="200">
<div class="wrapper wrapper-content fadeInRight ibox-content">

  <div class="row">
    <div class="col-sm-12 scrollspy-main">
      <form class="form-horizontal m" id="form-groupPlanApply-add">
        <!-- 基本信息 -->
        <input type="hidden" id="isSubmit" name="isSubmit">
        <input type="hidden" id="businessType" name="businessType" value="ADD">
        <input type="hidden" name="manageDept" th:value="${manageDept}">
        <div id="baseInfo" class="panel panel-default scrollspy-item">
          <div class="panel-heading">
            <h4 class="panel-title">
              <a role="button" data-toggle="collapse">
                基本信息
              </a>
            </h4>
          </div>
          <div class="panel-collapse collapse in">
            <div class="panel-body">
              <div class="row">
                <div class="col-lg-6 col-md-6">
                  <div class="form-group">
                    <label for="groupName" class="col-sm-3 control-label is-required">创值团队名称:</label>
                    <div class="col-sm-8">
                      <div class="bs-component">
                        <input type="text" id="groupName" name="groupName" required maxlength="50" class="form-control"
                               placeholder="">
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-lg-6 col-md-6">
                  <div class="form-group">
                    <label class="col-sm-3 control-label is-required">管理模式:</label>
                    <div class="col-sm-8">
                      <div class="bs-component">
                        <select required class="form-control noselect2 selectpicker" name="manageLevel"
                                th:with="type=${@dict.getDictList('KCZG','MANAGEMENT_MODE')}">
                          <option value="">----- 请选择 ------</option>
                          <option th:each="dict : ${type}" th:text="${dict.dictName}"
                                  th:value="${dict.dictValue}"></option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-lg-6 col-md-6">
                  <div class="form-group">
                    <label class="col-sm-3 control-label is-required">类型:</label>
                    <div class="col-sm-8">
                      <div class="bs-component">
                        <select required class="form-control noselect2 selectpicker" name="groupType" id="groupType"
                                th:with="type=${@dict.getDictList('KCZG','PRODUCT_TYPE')}">
                          <option value="">--请选择产品类型--</option>
                          <option th:each="dict : ${type}" th:text="${dict.dictName}"
                                  th:value="${dict.dictValue}"></option>
                        </select>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-lg-6 col-md-6">
                  <div class="form-group">
                    <label class="col-sm-3 control-label is-required">风险金类型:</label>
                    <div class="col-sm-8">
                      <div class="bs-component">
                        <div class="radio">
                          <label class="radio-inline" th:each="dict: ${@dict.getDictList('KCZG','RISK_AMOUNT')}">
                            <input type="radio" required name="riskType" th:value="${dict.dictValue}" onclick="changeRiskType(this)"
                                   th:text="${dict.dictName}">
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-lg-6 col-md-6">
                  <div class="form-group"
                       th:include="include :: choiceUserAndOrg(userCodeId='leaderId', value=${leaderId}, userNameId='leaderName',selectType='S',labelName='团队长:',callback='choseUserCallBack',isrequired=true)">
                  </div>
                </div>
                <div class="col-lg-6 col-md-6">
                  <div class="form-group">
                    <label for="tel" class="col-sm-3 control-label is-required">联系电话:</label>
                    <div class="col-sm-8">
                      <div class="bs-component">
                        <input type="text" id="tel" name="tel" required class="form-control" data-mask="999-9999-9999"
                               placeholder="请输入手机号码">
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="row">
                <div class="col-lg-6 col-md-6">
                  <div class="form-group">
                    <label class="col-sm-3 control-label">负责单位:</label>
                    <div class="col-sm-8">
                      <div class="bs-component">
                        <input type="hidden" id="fzDeptCode" name="fzDeptCode" th:value="${fzDeptCode}"/>
                        <input type="text" id="fzDeptName" name="fzDeptName" th:value="${fzDeptName}"
                               placeholder="对应人员所在单位" class="form-control"
                               readonly>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="col-lg-6 col-md-6">

                </div>
              </div>
            </div>
          </div>
        </div>

        <!--背景及意义-->
        <div class="panel panel-default scrollspy-item" id="row1">
          <div class="panel-heading" role="tab" id="headingTwo">
            <h4 class="panel-title" toolbox-title="背景及意义">
              <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseTwo"
                 aria-expanded="false" aria-controls="collapseTwo">
                背景及意义
              </a>
              <span class="pull-right">
                  </span>
              <div class="ibox-tools">
                <a type="button" class="btn btn-sm btn-danger" data-container="body" data-toggle="popover"
                   data-placement="top" title="产品类创值团队请包含以下内容"
                   data-html="true"
                   style="margin-top: -10px;"
                   data-content="
                      <ul>
                        <li>1. 国内外环境政策、市场需求量（中高端情况）、发展趋势；</li>
                        <li>2. 宝钢该产品规划目标; </li>
                        <li>3. 宝钢该产品产销研进展情况及年度销售目标; </li>
                        <li>4. 宝钢该产品相关机组制造能力情况及年度产量计划 </li>
                        <li>5. 创值团队市场拓展的主要突破点。</li></ul>">
                  填写提示
                </a>
              </div>
            </h4>

          </div>
          <div id="collapseTwo" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingTwo">
            <div class="panel-body">
              <input id="backgroundAndSignificance" name="backgroundAndSignificance" type="hidden">
              <div class="click2edit  wrapper" id="backgroundAndSignificanceEdit">

              </div>
            </div>
          </div>
        </div>

        <!-- 创值团队涉及的产品 -->
        <div id="row7" class="panel panel-default scrollspy-item">
          <div class="panel-heading">
            <h4 class="panel-title" toolbox-title="产品范围">
              <a role="button">
                产品范围 <small>钢铁产品类必填</small>
              </a>
            </h4>
          </div>
          <div class="panel-collapse collapse in">
            <div class="panel-body">
              <div id="product-toolbar">
                <button type="button" class="btn btn-danger btn-xs" onclick="addColumnSub('product-table')"><i
                  class="fa fa-plus"> 增加</i></button>
                <button type="button" class="btn btn-danger btn-xs" onclick="deleteProduct()"><i class="fa fa-minus">
                  删除</i></button>
              </div>
              <div class="col-sm-12 select-table table-bordered">
                <table id="product-table"></table>
              </div>
            </div>
          </div>
        </div>

        <!-- 变革思路 -->
        <div id="row2" class="panel panel-default scrollspy-item">
          <div class="panel-heading" role="tab" id="headingThree">
            <h4 class="panel-title">
              <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseThree"
                 aria-expanded="false" aria-controls="collapseThree">
                变革思路
              </a>
            </h4>
          </div>
          <div id="collapseThree" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingThree">
            <div class="panel-body">
              <textarea class="form-control" required maxlength="325" id="bgsl" name="bgsl" rows="5"></textarea>
            </div>
          </div>
        </div>

        <!-- 团队主要职责 -->
        <div id="row3" class="panel panel-default scrollspy-item">
          <div class="panel-heading" role="tab" id="headingFour">
            <h4 class="panel-title">
              <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseFour"
                 aria-expanded="false" aria-controls="headerFour">
                主要职责
              </a>
            </h4>
          </div>
          <div id="collapseFour" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingFour">
            <div class="panel-body">
              <textarea class="form-control" id="tdzyzz" required maxlength="325" name="tdzyzz" rows="5"></textarea>
            </div>
          </div>
        </div>

        <!-- 绩效目标与激励方案 -->
        <div id="row4" class="panel panel-default scrollspy-item">
          <div class="panel-heading" role="tab" id="headingFive">
            <h4 class="panel-title">
              <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseFive"
                 aria-expanded="false" aria-controls="headingFive">
                绩效方案
              </a>
            </h4>
          </div>
          <div id="collapseFive" class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingFive">
            <div class="panel-body">
              <div class="tabs-container" id="row40">
                <input readonly type="hidden" id="stageTotal" name="stageTotal" value="1">
                <input readonly type="hidden" id="stageYear" name="stageYear" value="1">
                <div class="tabs-left">
                  <div class="operation">
                    <button type="button" class="btn btn-sm btn-primary" onclick="addYearTarget()">
                      <i class="fa fa-plus"></i>
                    </button>
                    <button type="button" class="btn btn-sm btn-danger" onclick="subYearTarget()">
                      <i class="fa fa-minus"></i>
                    </button>
                  </div>
                  <ul id="yearTargetIndex" class="nav nav-tabs">
                  </ul>
                  <div id="yearTargetContent" class="tab-content ">
                  </div>
                </div>
              </div>

            </div>
          </div>
        </div>

        <!-- 备注说明 -->
        <div id="row41" class="panel panel-default">
          <div class="panel-heading">
            <h4 class="panel-title" toolbox-title="备注说明">
              <a href="#row41">
                备注说明: <small>若第二年度指标的上年度实绩值为空，则必须在此处填写说明个性化指标，需要在此处注明指标定义</small>
              </a>
            </h4>
          </div>
          <div class="panel-body">
            <textarea class="form-control" id="remark" name="remark" rows="3" maxlength="180">1、各档之间按照线性差值法计算;</textarea>
          </div>
        </div>

        <!-- 相关附件 -->
        <div id="row42" class="panel panel-default">
          <div class="panel-heading">
            <h4 class="panel-title" toolbox-title="相关附件">
              <a href="#row42">
                相关附件: <small>创值团队实施前12个月各绩效指标实绩明细,数据来源财务系统或制造系统等公司业务系统</small>
              </a>
            </h4>
          </div>
          <div class="panel-body">
            <div class="form-group" th:include="include :: layui-upload(id='relevantAttachments',
                  name='relevantAttachments',
                  divClass='col-sm-12',
                  labelClass='relevantAttachmentsLabelClass',
                  labelName=''
                  )"></div>

          </div>
        </div>

        <!--团队成员-->
        <div id="row5" class="panel panel-default scrollspy-item">
          <div class="panel-heading" role="tab">
            <h4 class="panel-title">
              <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseSix"
                 aria-expanded="false" aria-controls="headingSix">
                团队成员
              </a>
            </h4>
          </div>
          <div class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingSix">
            <div class="panel-body">
              <div id="member-toolbar">
                <button type="button" class="btn btn-danger btn-xs" onclick="addColumnSub('groupMember-table')"><i
                  class="fa fa-plus"> 增加</i></button>
                <!--<button type="button" class="btn btn-danger btn-xs" onclick="sub.delColumn()"><i class="fa fa-minus">
                  删除</i></button>-->
              </div>
              <div class="col-sm-12 select-table table-bordered">
                <table id="groupMember-table"></table>
              </div>
            </div>
          </div>
        </div>

        <!--激励兑现说明-->
        <div id="row6" class="panel panel-default scrollspy-item">
          <div class="panel-heading" role="tab">
            <h4 class="panel-title">
              <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapseSeven"
                 aria-expanded="false" aria-controls="collapseSeven">
                激励兑现
              </a>
            </h4>
          </div>
          <div class="panel-collapse collapse in" role="tabpanel" aria-labelledby="headingSeven">
            <div class="panel-body">
              <textarea class="form-control" required maxlength="325" name="jjdxsm" rows="5">1. 团队成员上一年度收入按角色减发额度作为风险金，完成T2目标后开始正激励，至T5封顶;
2. 任务每年度跟踪实绩，公司根据团队任务完成实绩进行绩效评测并一次性兑现奖励;
3. 对团队成员的奖励按本方案列示的名单落实，团队成员以外的协同人员奖励发放范围不包括公司D层级及以上管理人员;
4. 团队内分配原则：（1）主要根据团队成员抵押金进行约定分配；（2）同时根据团队成员绩效和对团队目标的贡献度，团队长有对各成员不超过50%比例的激励调配权。
              </textarea>
            </div>
          </div>
        </div>

      </form>

    </div><!--/.fluid-container-->

  </div> <!--end row -->

</div>

<div th:include="kczginclude :: toolbox" />

<div class="toolbar toolbar-bottom">
  <button type="button" class="btn btn-primary"
          onclick="saveHandler()">
    <i class="fa fa-check"></i> 暂 存
  </button>

  <button type="button" class="btn btn-primary"
          onclick="submitHandler()">
    <i class="fa fa-check"></i> 提 交
  </button>
  &nbsp;
  <button type="button" class="btn btn-danger"
          onclick="closeItem()">
    <i class="fa fa-reply-all"></i> 返 回
  </button>
</div>

<script th:inline="javascript">
  var prefix = ctx + "kczg/groupPlanApply"
  var prefixProject = ctx + "kczg/project/info/";


  // var managerLevels = [[${@dict.getDictList('KCZG','MANAGEMENT_MODE')}]];
  var groupRoleList = [[${@dict.getDictList('KCZG','GROUP_ROLE')}]];
  var zzbase = [[${@dict.getDictList('KYND','implementationBase')}]];
  var projectStatus = [[${@dict.getDictList('KYXM','project_status')}]]; //项目状态

  var leaderId = [[${leaderId}]];
  var leaderName = [[${leaderName}]];
  var fzDeptName = [[${fzDeptName}]];
  var fzDeptCode = [[${fzDeptCode}]];
  var riskType = 0;

  function initEelement() {
    $('#backgroundAndSignificanceEdit').summernote({
      lang: 'zh-CN',
      minHeight: 300,
      focus: true
    });
    $("[data-toggle=popover]").popover();

    $("#form-groupPlanApply-add").validate({
      focusCleanup: true
    });

    var otherData = null;
    ToolBox.initToolBox("catalogue",null, otherData);
  }

  /**
   * 保存
   **/
  function saveHandler() {
    $("#isSubmit").val(false);
    //创值团队名称 验证
    var groupName = $("#groupName").val();
    if ($.common.isEmpty(groupName)) {
      $.modal.alertError("创值团队名称:不能为空")
      return;
    }
    $("#backgroundAndSignificance").val($('#backgroundAndSignificanceEdit').summernote('code'));
    $.operate.saveModal(prefix + "/saveAndSubmit", $('#form-groupPlanApply-add').serialize(), function (result) {
      goEditTab(prefix + "/editApply/" + result.data.planId);
    });
  }

  // 刷新iframe
  function goEditTab(url) {
    var topWindow = $(window.parent.document);
    var currentId = $('.page-tabs-content', topWindow).find('.active').attr('data-id');
    var target = $('.RuoYi_iframe[data-id="' + currentId + '"]', topWindow);
    //var url = target.attr('src');
    target.attr('src', url).ready();
  }

  /**
   * 提交
   **/
  function submitHandler() {
    var keyValue = $('#backgroundAndSignificanceEdit').summernote('code');
    $("#backgroundAndSignificance").val(keyValue);
    if ($('#backgroundAndSignificanceEdit').summernote('isEmpty') || $.common.isEmpty(keyValue)) {
      $.modal.alertError("背景及意义:不能为空")
      return;
    }
    var groupType = $("#groupType").val();
    if ($.common.equals("01", groupType)) {
      var productList = $("#product-table").bootstrapTable("getData");
      if (productList.length == 0) {
        $.modal.alertError('类型为"钢铁类"时, 创值团队涉及的产品 不能为空。');
        return;
      }
    }
    //每一期的 权重 合计 等于 100
    var stageTotal = $("#stageTotal").val(); //总期数
    var ratioSumError = "";
    for (var i = 0; i < stageTotal; i++) {
      var index = i + 1;
      var tableId = "yearTargetTab-" + index + "-table"
      var yearTargetList = $("#" + tableId).bootstrapTable("getData");
      if (yearTargetList.length == 0) {
        $.modal.alertError(getYearTargetLabel(index) + ' 指标 不能为空。');
        return;
      }
      var ratioSum = 0;
      if (i == 0) {
        var isError = false;
        yearTargetList.forEach(item => {
          if ($.common.isEmpty(item.valueBeforYear)) isError = true;
        });
        if (isError) {
          $.modal.alertError("第一期中，上年度实际 不能为空。");
          return;
        }
      }
      yearTargetList.forEach(item => {
        ratioSum += parseFloat(item.ratio||0);
      });
      if(ratioSum != 100) ratioSumError += "第 " + (i+1) + " 期，权重合计必须等于 100; <br /> ";
    }
    if(!$.common.isEmpty(ratioSumError)){
      $.modal.alertError(ratioSumError);
      return;
    }
    if ($.validate.form()) {
      $.modal.confirm("确认提交吗？", function () {
        $("#isSubmit").val(true);
        /*
         * 自定义验证
         *  1. 团队成员 工号不能重复；
         *  2. 成员最多只能是 两个创值团队中;
         */
        $.operate.saveTabAlert(prefix + "/saveAndSubmit", $('#form-groupPlanApply-add').serialize());
      });

    }
  }

  function choseUserCallBack(userCode, userName, orgCode, orgName, index) {
    $("#fzDeptCode").val(orgCode);
    getOrgPathName(orgCode, function (result) {
      $("#fzDeptName").val(result);
      //新增 成员
      var data = {
        id: 1,
        empCode: userCode,
        empName: userName,
        deptCode: orgCode,
        deptName: result,
        role: "ROLE_A",
        task: "",
        riskDetailAmt: 0
      };
      addMemberRoleA(data);
    });


  }

  function choseUserCallBackRow(userCode, userName, orgCode, orgName, index) {
    $($.common.sprintf("#groupMemberList_%s_deptCode", index)).val(orgCode);
    getOrgPathName(orgCode, function (result) {
      $($.common.sprintf("#groupMemberList_%s_deptName", index)).val(result);
      sub.editColumn();
    });
  }

  /**
   * 获取 机构全名称
   */
  function getOrgPathName(orgCode, fn) {
    var url = ctx + "kczg/orgName/" + orgCode;
    $.post(url, {}, fn);
  }

  var groupRoleSelect = []; //其它下拉项目
  var groupRoleASelect = []; //团队长


  $(function () {
    //初始页面组件
    initEelement();
    initProductTable("product-table");
    initGroupTable("groupMember-table");
    addMemberRoleA({
      id: 1,
      empCode: leaderId,
      empName: leaderName,
      deptCode: fzDeptCode,
      deptName: fzDeptName,
      role: "ROLE_A",
      task: "",
      riskDetailAmt: 0
    });
    // 初始化 第一期 年度目标
    addYearTarget();
    groupRoleList.forEach(item => {
      // 设置 禁止 选择
      if (item.dictValue === "ROLE_A") {
        groupRoleASelect.push(item);
      } else {
        groupRoleSelect.push(item);
      }
    });
  });

  // 选择抵押金类型
  function changeRiskType(e){
    riskType = $(e).val();
    // 抵押型，绩效指标细则为只读；非抵押保持现状
    // 设置 绩效指标细则为只读
    changeTargetRule(riskType == 1); //抵押为true
    // 抵押型和不抵押型团队成员上的计划抵押金要控制，不抵押时，抵押金合计不能大于0
    // 不抵押时： 团队成员 计划抵押金为只读
    changeGroupMember(riskType == 0);
  }
  function changeTargetRule(readonly){
    var stageTotal = $("#stageTotal").val();
    for (var i = 0; i < stageTotal; i++) {
      $("input[name='groupTargetDtoList["+(i+1)+"].groupTargetRules.jlxzX1']").attr("readonly",readonly);
      $("input[name='groupTargetDtoList["+(i+1)+"].groupTargetRules.jlxzX2']").attr("readonly",readonly);
      $("input[name='groupTargetDtoList["+(i+1)+"].groupTargetRules.jlxzX3']").attr("readonly",readonly);
      $("input[name='groupTargetDtoList["+(i+1)+"].groupTargetRules.jlxzX4']").attr("readonly",readonly);
      $("input[name='groupTargetDtoList["+(i+1)+"].groupTargetRules.jlxzX5']").attr("readonly",readonly);
    }
  }
  function changeGroupMember(readonly){
    var memberSize = $("#groupMember-table").bootstrapTable("getData").length;
    for (var i = 0; i < memberSize; i++) {
      $("input[name='groupMemberList["+i+"].riskDetailAmt']").attr("readonly",readonly);
    }
  }

  //获取 项目信息 TODO 废弃
  function getProjectInfo(e, index) {
    var projectName = 'productList_' + index + '_projectName';
    var projectStatus = 'productList_' + index + '_projectStatus';
    var projectCode = $(e).val();
    $.get(prefixProject + "ajax/" + projectCode, function (data) {
      if (data.code == 0) {
        var project = data.data;
        if (project != null) {
          $("#" + projectName + "_v").val(project.projectName);
          $("#" + projectName + "_a").text(project.projectName);
          $("#" + projectStatus + "_v").val(project.projectStatus);
          $("#" + projectStatus + "_a").text(project.projectStatus);
          sub.editColumn();
          refreshProductTableSelect2();
        }
      }
    });
  }

  /**
   * 显示 项目页面
   */
  function showProjectPage2(projectCode) {
    var url = prefixProject + projectCode;
    $.modal.openTab("项目详细", url);
  }

  function showProjectPage(projectCode) {
    var ky = "/bscdkj-ky/"
    var url = ky+"c/l?serviceName=KYXMLX02&methodName=query&pageNo=KYXMLX21&projectGuid=";
    var title = "项目一栏表";
    // 选卡页方式打开并刷新当前页 isRefresh 是否刷新
    $.get(prefixProject + "ajax/" + projectCode, function (data) {
      if (data.code == 0) {
        var project = data.data;
        if (project != null) {
          console.log(project);
          $.modal.openTab(title, url + project.recordGuid, true);
        }
      }
    });

  }



  /**
   * 初始化 产品 表格
   * @param {表格ID} id
   */
  function initProductTable(id) {
    var data = [];

    var options = {
      id: id,
      data: data,
      toolbar: 'product-toolbar',
      pagination: false,
      showSearch: false,
      showRefresh: false,
      showToggle: false,
      showColumns: false,
      sidePagination: "client",
      modalName: "产品",
      columns: [
        {checkbox: true},
        {
          field: 'index',
          align: 'center',
          title: "序号",
          width: '50',
          formatter: function (value, row, index) {
            var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
            var orderNo = $.common.sprintf("<input type='hidden' name='productList[%s].orderNo' value='%s'>", index, $.table.serialNumber(index));
            return columnIndex + orderNo + $.table.serialNumber(index);
          }
        },
        {
          field: 'brandNo',
          align: 'center',
          title: '产品牌号',
          formatter: function (value, row, index, field) {
            var html = $.common.sprintf("<input class='form-control' required fieldName='%s' type='text' id='productList_%s_brandNo' name='productList[%s].brandNo' value='%s' onclick='choseBrandNo(this)'>", field,index, index, value);
            return html;
          }
        },
        {
          field: 'zzBase',
          align: 'center',
          title: '制造基地',
          width: '150',
          formatter: function (value, row, index, field) {
            //下拉
            // var html = $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='productList[%s].zzBase' value='%s'>", field, index, value);
            var data = {
              fieldName: field,
              name: "productList["+ index+ "].zzBase",
              selectValue: value,
              selectItems: zzbase
            };
            return $("#select2Tpl").tmpl(data).html();
            //return html;
          }
        },
        {
          field: 'productLine',
          align: 'center',
          title: '产线/机组',
          formatter: function (value, row, index, field) {
            var html = $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='productList[%s].productLine' value='%s'>", field, index, value);
            return html;
          }
        },
        {
          field: 'isNew',
          align: 'center',
          title: '是否新产品',
          formatter: function (value, row, index, field) {
            var data = {
              fieldName: field,
              name: "productList[" + index + "].isNew",
              checkValue: value,
              radios: [{
                id: 'yes',
                value: '1',
                label: '是'
              },
                {
                  id: 'no',
                  value: '0',
                  label: '否'
                }
              ]
            };
            return $("#radiosTpl").tmpl(data).html();
          }
        },
        {
          field: 'projectCode',
          align: 'left',
          title: '项目编号',
          width: '200',
          formatter: function (value, row, index, field) {
            var html = `<input class='form-control' fieldName='%s' type='text' id="productList_%s_projectCode_v" name='productList[%s].projectCode' value='%s' onclick='choseProject(this,\"%s\")'">`;
            return $.common.sprintf(html, field,index, index, value,index);
          }
        },
        {
          field: 'projectName',
          align: 'center',
          title: '项目名称',
          width: '150',
          formatter: function (value, row, index, field) {
            var html = `
            <input class='form-control' type='hidden' id="productList_%s_projectName_v" name='productList[%s].projectName' value='%s'>
            <a class="a-no-btn" id="productList_%s_projectName_a" style="width: 150px;white-space: break-spaces;" href='javascript:showProjectPage(\"%s\")' >%s</a>
            `;
            return $.common.sprintf(html, index, index, value, index, row.projectCode, value);
          }
        },
        {
          field: 'projectStatus',
          align: 'center',
          title: '项目状态',
          width: '100',
          formatter: function (value, row, index) {
            var label = $.table.selectDictLabel(projectStatus, value) || "";
            var html = `
            <input class='form-control' type='hidden' id="productList_%s_projectStatus_v" name='productList[%s].projectStatus' value='%s'>
            `+label;
            return $.common.sprintf(html, index, index, value);
          }
        },
        {
          title: '操作',
          align: 'center',
          formatter: function (value, row, index) {
            var actions = [];
            actions.push('<a class="btn btn-danger btn-xs" href="javascript:deleteByInde(\'index\',\'' + (index+1) + '\')"><i class="fa fa-remove"></i>删除</a>');
            return actions.join('');
            return "";
          }
        }
      ]
    };
    $.table.init(options);
  }

  function deleteByInde(byIndex,index){
    sub.delColumnByIndex(byIndex,index);
    refreshProductTableSelect2();
  }


  function choseProject(el,index){
    var nameId = $(el).attr("id");
    $.modal.open("选择项目", ctx + "kczg/project/"+index, '1000', '500');
  }

  function choseProjectBack(index,rowData){
    var projectCode = 'productList_' + index + '_projectCode';
    var projectName = 'productList_' + index + '_projectName';
    var projectStatus = 'productList_' + index + '_projectStatus';
    $("#" + projectCode + "_v").val(rowData.projectCode);
    $("#" + projectName + "_v").val(rowData.projectName);
    $("#" + projectName + "_a").text(rowData.projectName);
    $("#" + projectStatus + "_v").val(rowData.projectStatus);
    //$("#" + projectStatus + "_a").text(rowData.projectStatus);
    sub.editColumn();
    refreshProductTableSelect2();
  }

  /**
   * 选择 号牌
   */
  function choseBrandNo(el){
    var nameId = $(el).attr("id");
    var ky = "/bscdkj-ky/";
    $.modal.open("选择牌号", ky + "kxcp/yyph/selectPh?selectType=S&codeId=shopsign&nameId="+nameId, '1000', '500');
  }

  /**
   * 初始化 团队成员 表格
   * @param {表格ID} id
   */
  function initGroupTable(id) {
    var data = [];
    var footerStyle = function (column) {
      return {
        price: {
          css: {
            color: 'black',
            'font-weight': 'normal'
          }
        },
        duty: {
          css: {
            color: 'red',
            'font-weight': 'normal'
          }
        },
      } [column.field]
    }

    var options = {
      id: id,
      data: data,
      toolbar: "member-toolbar",
      pagination: false,
      showSearch: false,
      showRefresh: false,
      showToggle: false,
      showColumns: false,
      showFooter: true,
      footerStyle: footerStyle,
      sidePagination: "client",
      columns: [{
        field: 'index',
        align: 'center',
        title: "序号",
        width: '30',
        formatter: function (value, row, index, field) {
          var columnIndex = $.common.sprintf("<input type='hidden' fieldName='index' name='index' value='%s'>", $.table.serialNumber(index));
          var orderNo = $.common.sprintf("<input type='hidden' fieldName='orderNo' name='groupMemberList[%s].orderNo' value='%s'>", index, $.table.serialNumber(index));
          var columnId = $.common.sprintf("<input type='hidden' fieldName='id' name='groupMemberList[%s].id' value='%s'>", index, row.id);
          return columnIndex + orderNo + columnId + $.table.serialNumber(index);
        }
      },
        {
          field: 'empName',
          align: 'center',
          title: '姓名',
          width: '100',
          formatter: function (value, row, index, field) {
            // 如果是 团队长，此处不可修改
            if (row.role === "ROLE_A") {
              return $.common.sprintf("<input readonly class='form-control' fieldName='%s' type='text' id='groupMemberList_%s_empName' name='groupMemberList[%s].empName' value='%s'>", field, index, index, value);
            }
            var codeId = $.common.sprintf("groupMemberList_%s_empCode", index),
              nameId = $.common.sprintf("groupMemberList_%s_empName", index),
              nameName = $.common.sprintf("groupMemberList[%s].empName", index);
            var data = {
              fieldName: field,
              id: nameId,
              name: nameName,
              inputValue: value,
              clickFun: $.common.sprintf('choiceUserAndOrg("%s","%s","S","","%s", "choseUserCallBackRow")', codeId, nameId, index)
            };
            return $("#inputTpl").tmpl(data).html();
          }
        },
        {
          field: 'empCode',
          align: 'center',
          title: '工号',
          width: '100',
          formatter: function (value, row, index, field) {
            return $.common.sprintf("<input readonly class='form-control' required fieldName='%s' type='text' id='groupMemberList_%s_empCode' name='groupMemberList[%s].empCode' value='%s'>", field, index, index, value);
          }
        },
        {
          field: 'deptCode',
          align: 'center',
          title: '',
          width: '1',
          formatter: function (value, row, index) {
            var deptCode = $.common.sprintf("<input type='hidden' id='groupMemberList_%s_deptCode' name='groupMemberList[%s].deptCode' value='%s'>", index, index, value);
            return deptCode;
          }
        },
        {
          field: 'deptName',
          align: 'center',
          title: '部门',
          width: '250',
          formatter: function (value, row, index, field) {
            var deptName = $.common.sprintf("<input type='hidden' id='groupMemberList_%s_deptName' name='groupMemberList[%s].deptName' value='%s'>", index, index, value);
            return deptName + value;
          }
        },
        {
          field: 'role',
          align: 'center',
          title: '角色',
          width: '130',
          formatter: function (value, row, index, field) {
            var name = "groupMemberList[" + index + "].role";
            var html = `
                    <select class='form-control' required name='` + name + `' readonly >
                        <option value="ROLE_A" selected>团队长</option>
                    </select>`;
            var data = {
              name: name,
              selectValue: value,
              selectItems: groupRoleSelect,
              readonly: false
            };
            return row.role === "ROLE_A" ? html : $("#selectDictTpl").tmpl(data).html();
          }
        },
        {
          field: 'task',
          align: 'center',
          title: '主要职责',
          formatter: function (value, row, index, field) {
            var html = $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupMemberList[%s].task' value='%s'>", field, index, value);
            return html;
          },
          footerFormatter: function (value) {
            return "抵押金合计（万元） ：";
          }
        },
        {
          field: 'riskDetailAmt',
          align: 'left',
          title: '抵押金（万元）',
          width: '50',
          formatter: function (value, row, index, field) {
            var readonly = (riskType == 0);
            var html = $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupMemberList[%s].riskDetailAmt' value='%s' onBlur='refreshRiskDetailAmtSum(\"" + id + "\",%s)' %s>", field, index, value, index,readonly?"readonly='readonly'":"");
            return html;
          },
          footerFormatter: function (value) {
            var riskDetailAmtSum = 0;
            for (var i in value) {
              riskDetailAmtSum += parseFloat(value[i].riskDetailAmt);
            }
            return riskDetailAmtSum.toFixed(4);
          }
        },
        {
          title: '操作',
          align: 'center',
          formatter: function (value, row, index) {
            if (row.role === "ROLE_A") {
              return '';
            }
            var actions = [];
            actions.push('<a class="btn btn-danger btn-xs" href="javascript:void(0)" onclick=\"sub.delColumnByIndex(\'index\',' + (index + 1) + ')\"><i class="fa fa-remove"></i>删除</a>');
            return actions.join('');
          }
        }
      ]
    };
    $.table.init(options);
  }

  /**
   * 删除 年度目标
   */
  function subYearTarget(){
    var yearTargetLi = $("#yearTargetIndex").children("li");
    var activeEl = $("#yearTargetIndex").children("li:last");
    var label = $(activeEl).children("a").text();
    var contentId = $(activeEl).children("a").attr("href");
    if(yearTargetLi.length == 1){
      $.modal.alertError('第一期的不可删除。');
      return ;
    }
    $.modal.confirm("确认要删除 " + label + " 吗？", function () {
      $(activeEl).remove();
      $(contentId).remove();
      $("#yearTargetIndex").children("li:first").addClass("active ");
      $("#yearTargetIndex").children("li:first").children("a").attr("aria-expanded",true);
      $("#yearTargetTab-1").addClass("active");
      var yearTargetLi = $("#yearTargetIndex").children("li");
      var index = yearTargetLi.length;
      $("#stageTotal").val(index);  //设置总期数
    });

  }

  /**
   * 增加年度目标
   */
  function addYearTarget() {

    var yearTargetLi = $("#yearTargetIndex").children("li");
    var index = yearTargetLi.length + 1;
    $("#stageTotal").val(index);  //设置总期数
    var data = {
      className: index == 1 ? "active" : "",
      tabIndex: "yearTargetTab-" + index,
      index: index,
      tabLabel: getYearTargetLabel(index),
      tableId: "yearTargetTab-" + index + "-table"
    };

    $("#yearTargetIndex").append($("#yearTargetLiTpl").tmpl(data).html());
    $("#yearTargetContent").append($("#yearTargetContentTpl").tmpl(data).html());

    // 渲染 table
    initPerformanceTable(data.tableId, index);

    setTimeout(function () {
      $("#yearTargetIndex").children("li:first").addClass("active ");
      $("#yearTargetIndex").children("li:first").children("a").attr("aria-expanded",true);
    }, 500);
  }

  /**
   * 初始 年度指标 表格
   * @param { 表格 ID } id
   * @param { 第几期 } stageYear
   */
  function initPerformanceTable(id, stageYear) {

    var data = [];
    var footerStyle = function (column) {
      return {
        price: {
          css: {
            color: 'black',
            'font-weight': 'normal'
          }
        },
        duty: {
          css: {
            color: 'red',
            'font-weight': 'normal'
          }
        },
      } [column.field]
    };

    var options = {
      id: id,
      data: data,
      toolbar: "yearTargetTab-" + stageYear + "-toolbar",
      pagination: false,
      showSearch: false,
      showRefresh: false,
      showToggle: false,
      showColumns: false,
      footerStyle: footerStyle,
      showFooter: true,
      sidePagination: "client",
      columns: [{
        checkbox: true
      }, {
        field: 'index',
        align: 'center',
        title: "序号",
        width: '40',
        formatter: function (value, row, index, field) {
          var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
          var orderNo = $.common.sprintf("<input type='hidden' name='groupTargetDtoList[%s].groupTargetList[%s].orderNo' value='%s'>", stageYear, index, $.table.serialNumber(index));
          var columnId = $.common.sprintf("<input type='hidden' name='groups[%s].id' value='%s'>", index, row.id);
          return columnIndex + orderNo + columnId + $.table.serialNumber(index);
        }
      },
        {
          field: 'orderNo',
          align: 'center',
          title: "",
          width: '1',
          formatter: function (value, row, index) {
            var orderNo = $.common.sprintf("<input type='hidden' name='groupTargetDtoList[%s].groupTargetList[%s].orderNo' value='%s'>", stageYear, index, $.table.serialNumber(index));
            return orderNo;
          }
        },
        {
          field: 'targetName',
          align: 'center',
          title: '指标名称',
          formatter: function (value, row, index, field) {
            return $.common.sprintf("<input class='form-control' required fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].targetName' value='%s'>", field, stageYear, index, value);
          }
        },
        {
          field: 'ratio',
          align: 'center',
          width: '70',
          title: '权重(%)',
          formatter: function (value, row, index, field) {
            return $.common.sprintf("<input class='form-control' required fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].ratio' value='%s' onblur='refreshRatio()' >", field, stageYear, index, value);
          },
          footerFormatter: function (value, row, index) {
            var totalRatio = 0;
            for (var i in value) {
              totalRatio += parseFloat(value[i].ratio);
            }
            if (totalRatio > 100) {
              $.modal.alertError('权重合计 不能为超过 100。');
              return;
            }
            return "";
          }
        },
        {
          field: 'valueBeforYear',
          align: 'center',
          title: '上年度实际',
          width: '80',
          formatter: function (value, row, index, field) {
            return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].valueBeforYear' value='%s' onblur='refreshRatio()'>", field, stageYear, index, value);
          },
          footerFormatter: function (value, row, index) {
            return "奖励额度（万元):";
          }
        },
        {
          field: 'gradeT1',
          align: 'center',
          title: 'T1',
          width: '80',
          formatter: function (value, row, index, field) {
            return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].gradeT1' value='%s'>", field, stageYear, index, value);
          },
          footerFormatter: function (value, row, index) {
            var htmlStr = `<input class='form-control' type='text' name='groupTargetDtoList[%s].groupTargetRules.jlxzX1' placeholder='%s' onblur='refreshJlxzDesc(this)'>
                           <input type='hidden' name='groupTargetDtoList[%s].groupTargetRules.x1Desc' value="扣抵押金">
                           <span class='help-block m-b-none'><i class='fa fa-info-circle'></i>扣抵押金</span>`;
            return $.common.sprintf(htmlStr, stageYear, -1, stageYear);
          }
        },
        {
          field: 'gradeT2',
          align: 'center',
          title: 'T2',
          width: '80',
          formatter: function (value, row, index, field) {
            return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].gradeT2' value='%s'>", field, stageYear, index, value);
          },
          footerFormatter: function (value, row, index) {
            var htmlStr = `<input class='form-control' type='text' name='groupTargetDtoList[%s].groupTargetRules.jlxzX2' placeholder='%s' onblur='refreshJlxzDesc(this)'>
                           <input type='hidden' name='groupTargetDtoList[%s].groupTargetRules.x2Desc' value="返还全部抵押金">
                           <span class='help-block m-b-none'><i class='fa fa-info-circle'></i>返还全部抵押金</span>`;
            return $.common.sprintf(htmlStr, stageYear, 0, stageYear);
          }
        },
        {
          field: 'gradeT3',
          align: 'center',
          title: 'T3',
          width: '80',
          formatter: function (value, row, index, field) {
            return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].gradeT3' value='%s'>", field, stageYear, index, value);
          },
          footerFormatter: function (value, row, index) {
            var htmlStr = `<input class='form-control' type='text' name='groupTargetDtoList[%s].groupTargetRules.jlxzX3' placeholder='%s' onblur='refreshJlxzDesc(this)'>
                           <input type='hidden' name='groupTargetDtoList[%s].groupTargetRules.x3Desc' value="奖励1倍抵押金">
                           <span class='help-block m-b-none'><i class='fa fa-info-circle'></i>奖励1倍抵押金</span>`;
            return $.common.sprintf(htmlStr, stageYear, 1, stageYear);
          }
        },
        {
          field: 'gradeT4',
          align: 'center',
          title: 'T4',
          width: '80',
          formatter: function (value, row, index, field) {
            return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].gradeT4' value='%s'>", field, stageYear, index, value);
          },
          footerFormatter: function (value, row, index) {
            var htmlStr = `<input class='form-control' type='text' name='groupTargetDtoList[%s].groupTargetRules.jlxzX4' placeholder='%s' onblur='refreshJlxzDesc(this)'>
                           <input type='hidden' name='groupTargetDtoList[%s].groupTargetRules.x4Desc' value="奖励2倍抵押金">
                           <span class='help-block m-b-none'><i class='fa fa-info-circle'></i>奖励2倍抵押金</span>`;
            return $.common.sprintf(htmlStr, stageYear, 2, stageYear);
          }
        },
        {
          field: 'gradeT5',
          align: 'center',
          title: 'T5',
          width: '80',
          formatter: function (value, row, index, field) {
            return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].gradeT5' value='%s'>", field, stageYear, index, value);
          },
          footerFormatter: function (value, row, index) {
            var htmlStr = `<input class='form-control' type='text' name='groupTargetDtoList[%s].groupTargetRules.jlxzX5' placeholder='%s' onblur='refreshJlxzDesc(this)'>
                           <input type='hidden' name='groupTargetDtoList[%s].groupTargetRules.x5Desc' value="奖励4倍抵押金">
                           <span class='help-block m-b-none'><i class='fa fa-info-circle'></i>奖励4倍抵押金</span>`;
            return $.common.sprintf(htmlStr, stageYear, 4, stageYear);
          }
        },
        {
          field: 'dataSource',
          align: 'center',
          title: '数据来源',
          formatter: function (value, row, index, field) {
            return $.common.sprintf("<input class='form-control' fieldName='%s' type='text' name='groupTargetDtoList[%s].groupTargetList[%s].dataSource' value='%s'>", field, stageYear, index, value);
          }
        }
      ]
    };
    $.table.init(options);
  }

  /**
   * 添加 团队长
   **/
  function addMemberRoleA(data) {
    var dataList = $("#groupMember-table").bootstrapTable("getData");
    // 判断团队长是否存在，如果不存在 添加，存在，需要修改
    var existRoleA = false;
    if (dataList.length > 0) {
      for (var i = 0; i < dataList.length; i++) {
        if (dataList[i].role == "ROLE_A") {
          existRoleA = true;
          dataList[i].empCode = data.empCode;
          dataList[i].empName = data.empName;
          dataList[i].deptCode = data.deptCode;
          dataList[i].deptName = data.deptName;
          $("#groupMember-table").bootstrapTable('updateRow', {index: dataList[i].index, row: dataList[i]});
        }
      }
      //如果列表中没有团队长，添加，完善后，应该添加到第一行
      if (!existRoleA) {
        var currentId = $.common.isEmpty("groupMember-table") ? table.options.id : "groupMember-table";
        table.set(currentId);
        sub.editColumn();
        $("#groupMember-table").bootstrapTable('insertRow', {index: 0, row: data});
      }
    } else {
      var currentId = $.common.isEmpty("groupMember-table") ? table.options.id : "groupMember-table";
      table.set(currentId);
      sub.editColumn();
      $("#groupMember-table").bootstrapTable('insertRow', {index: 0, row: data});
    }
  }

  /**
   * 刷新 表格，刷新汇总数据
   */
  function refreshRiskDetailAmtSum() {
    sub.editColumn();
  }

  function refreshRatio() {
    sub.editColumn();
    var currentRiskType = $("input[name='riskType']:checked").val();
    changeTargetRule(currentRiskType == 1);
  }

  function deleteProduct(){
    sub.delColumn();
    refreshProductTableSelect2();
  }

  /**
   * 删除 绩效方案
   */
  function deletePerformanceTable(){
    sub.delColumn();
    var currentRiskType = $("input[name='riskType']:checked").val();
    changeTargetRule(currentRiskType == 1);
  }

  /**
   * 刷新 奖励细节描述
   */
  function refreshJlxzDesc(e) {
    var defaultVal = $(e).attr("placeholder");
    var jlxz = $(e).val();
    var desc = "";
    if (jlxz == "") {
      if (defaultVal == -1) {
        desc = "扣抵押金";
      } else if (defaultVal == 0) {
        desc = "返还全部抵押金";
      } else {
        desc = "奖励" + defaultVal + "倍抵押金";
      }
    } else {
      desc = "奖励" + jlxz + "万元";
    }
    $(e).parent(".th-inner").find(".help-block").html("<i class='fa fa-info-circle'></i>" + desc);
    $(e).parent(".th-inner").find("input[type='hidden']").val(desc);
  }

  /**
   * 增加 行
   */
  function addColumnSub(id) {
    var row = {};
    var data = $("#" + id).bootstrapTable("getData");
    var count = data.length;
    var dataId = 100000 + count;
    if (id === "product-table") {
      row = {
        id: dataId,
        orderNo: "",
        brandNo: "",
        zzBase: "",
        productLine: "",
        isNew: "0",
        projectName: "",
        projectCode: "",
        projectStatus: ""
      };
    } else if (id.indexOf("yearTargetTab-") > -1) {
      row = {
        id: dataId,
        targetName: "",
        ratio: "",
        valueBeforYear: "",
        gradeT1: "",
        gradeT2: "",
        gradeT3: "",
        gradeT4: "",
        gradeT5: "",
        dataSource: ""
      };
    } else {
      row = {
        id: dataId,
        empCode: "",
        empName: "",
        deptCode: "",
        deptName: "",
        role: "",
        task: "",
        riskDetailAmt: "0"
      }
    }
    sub.addColumn(row, id);
    if (id === "product-table") {
      refreshProductTableSelect2();
    }else if (id.indexOf("yearTargetTab-") > -1) {
      var currentRiskType = $("input[name='riskType']:checked").val();
      changeTargetRule(currentRiskType == 1);
    }

  }
  /**
   * 刷新 产品 中的 select2 样式
   */
  function refreshProductTableSelect2(){
    var selectItems = $("#product-table .select");
    for(var i = 0; i < selectItems.length; i++){
      var data = $(selectItems[i]).attr("selectValue");
      $(selectItems[i]).val(data.split(","));
    }
    $("#product-table .select").select2();
  }

  /**
   * 获取 年度指标的 期数 名称
   * @param {年度指标 期数 序列} num
   * @returns
   */
  function getYearTargetLabel(num) {
    return '第' + toChinesNum(num) + "期";
  }

  /**
   * 数字 转 汉语 大写
   * @param {数字} num
   * @returns
   */
  function toChinesNum(num) {
    var changeNum = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九'];
    var unit = ["", "十", "百", "千", "万"];
    num = parseInt(num);
    var getWan = (temp) => {
      var strArr = temp.toString().split("").reverse();
      var newNum = "";
      for (var i = 0; i < strArr.length; i++) {
        newNum = (i == 0 && strArr[i] == 0 ? "" : (i > 0 && strArr[i] == 0 && strArr[i - 1] == 0 ? "" : changeNum[strArr[i]] + (strArr[i] == 0 ? unit[0] : unit[i]))) + newNum;
      }
      return newNum;
    }
    var overWan = Math.floor(num / 10000);
    var noWan = num % 10000;
    if (noWan.toString().length < 4) {
      noWan = "0" + noWan;
    }
    return overWan ? getWan(overWan) + "万" + getWan(noWan) : getWan(num);
  }

</script>
<style>

  a.a-no-btn{
    text-decoration: underline;
    color: #337ab7;
  }

  .bootstrap-table .fixed-table-container .table.top th {
    vertical-align: top;
  }

  .select2-container--bootstrap .select2-results__option[aria-selected=true] {
    background-color: #c8c8c8;
  }

  .ibox-tools a.btn {
    color: #fff;
  }
  ul.nav-pills {
    top: 60px;
    position: fixed;
  }

  .nav .nav-header {
    font-size: 18px;
    color: #92B901;
  }

  textarea.form-control {
    width: 99%;
  }

  /* 年度目标 Tab 里的样式 */
  .tabs-container .tabs-left > .nav-tabs > li.active > a, .tabs-container .tabs-left > .nav-tabs > li.active > a:hover, .tabs-container .tabs-left > .nav-tabs > li.active > a:focus {
    border-top: #e7eaec solid 1px;
    border-left: #1c6ac7 solid 4px;
  }


  .tabs-container .tabs-left > .nav-tabs .active > a, .tabs-container .tabs-left > .nav-tabs .active > a:hover, .tabs-container .tabs-left > .nav-tabs .active > a:focus {
    border-left-color: #1c6ac7 transparent #e7eaec #e7eaec;
    border-right-color: #fff;
  }

  .tabs-container .tabs-left > .nav-tabs > li.active > a, .tabs-container .tabs-left > .nav-tabs > li.active > a:hover, .tabs-container .tabs-left > .nav-tabs > li.active > a:focus {
    border-left: 4px solid #1c6ac7;
  }

  #row40 .tabs-left .tab-content .panel-body {
    width: 90%;
    margin-left: 10%;
  }

  #collapseFive .tabs-container .tabs-left > #yearTargetIndex.nav-tabs {
    width: 10%;
    margin-top: 40px;
    margin-right: 1px;
  }

  .operation {
    position: absolute;
  }

  #collapseFive .panel-body .form-group {
    margin: 15px 0px;
  }

  #collapseFive .panel-body .form-group label {
    margin: 0px 0px 10px;
  }

  #collapseTwo > .panel-body {
    padding: 0px;
  }

  #row7 .panel-body, #row5 .panel-body {
    padding: 0px;
  }

  #row7 .panel-body .select-table, #row5 .panel-body .select-table {
    margin-top: 0px;
    border: 0px solid #ddd;
    border-radius: 0px 0px 6px 6px;
  }

  #yearTargetContent .panel-body {
    padding: 10px;
    padding-top: 0px;
  }

  #yearTargetContent .select-table {
    border: 0px;
    margin-top: 0px;
    border-radius: 0px;
    padding-top: 0px;
    box-shadow: 0px 0px 0px;
  }

  .note-editor.note-frame.panel {
    border: 0px;
    margin-bottom: 0px;
  }

  .note-toolbar.panel-heading {
    border-top: #f6f7f9 solid 1px;
    background-color: #dfebfe;
  }

  .help-block.m-b-none {
    font-size: 12px;
    font-weight: normal;
  }
</style>
</body>
</html>

<!-- 年度目标 素引模板 -->
<script id="yearTargetLiTpl" type="text/x-jquery-tmpl">
  <div>
    <li class="${className}">
      <a data-toggle="tab" href="#${tabIndex}">
        ${tabLabel}
      </a>
    </li>
  </div>


</script>

<!-- 年度目标 内容模板-->
<script id="yearTargetContentTpl" type="text/x-jquery-tmpl">
  <div>
    <div id="${tabIndex}" class="tab-pane ${className}">
      <div class="panel-body">
        <div id="${tabIndex}-toolbar">
          <button type="button" class="btn btn-danger btn-xs" onclick="addColumnSub('${tableId}')"><i class="fa fa-plus"> 增加</i></button>
          <button type="button" class="btn btn-danger btn-xs" onclick="deletePerformanceTable()"><i class="fa fa-minus"> 删除</i></button>
        </div>
        <input readonly type="hidden" id="groupTargetDtoList_${index}_stageYear" name="groupTargetDtoList[${index}].stageYear" value="${index}">
        <div class="col-sm-12 select-table table-bordered table-hover">
          <table id="${tableId}" class="top"></table>
        </div>

      </div>
    </div>
  </div>


</script>
<!-- 单选 模板 -->
<script id="radiosTpl" type="text/x-jquery-tmpl">
  <div>
    <div class="radio">
      {{each(i,item) radios}}
        <label class="radio-inline">
          <input type="radio" fieldName="${fieldName}" name="${name}" id="${item.id}" value="${item.value}" {{if checkValue===item.value}}checked{{/if}}> ${item.label}
        </label>
      {{/each}}
    </div>
  </div>


</script>

<!--
  下拉选择：
    参数说明：
      name: 组件对应 name
      type: 需要选中的value
      selectItems: 数组
        value: 对应值
        label: 对应显示值
-->
<script id="selectTpl" type="text/x-jquery-tmpl">
  <div>
    <select class='form-control' name='${name}' fieldName="${fieldName}">
      <option value="">------ 请选择  -----</option>
      {{each(i,item) selectItems}}
          <option value="${item.value}" {{if selectValue===item.value}}selected{{/if}}>${item.label}</option>
      {{/each}}
    </select>
  </div>


</script>

<script id="select2Tpl" type="text/x-jquery-tmpl">
  <div>
    <select class='selectpicker select' name='${name}' fieldName="${fieldName}" multiple selectValue="${selectValue}">
      {{each(i,item) selectItems}}
          <option value="${item.dictValue}">${item.dictName}</option>
      {{/each}}
    </select>
  </div>


</script>

<script id="selectDictTpl" type="text/x-jquery-tmpl">
  <div>
    <select class='form-control' required name='${name}' {{if readonly===true}}readonly{{/if}} >
      <option value="">------ 请选择  -----</option>
      {{each(i,item) selectItems}}
          <option value="${item.dictValue}" {{if selectValue===item.dictValue}}selected{{/if}}>${item.dictName}</option>
      {{/each}}
    </select>
  </div>


</script>

<script id="inputTpl" type="text/x-jquery-tmpl">
<div>
  <input class='form-control' required type='text' id="${id}" name='${name}' value='${inputValue}' {{if clickFun !== null}} onclick="${clickFun}" {{/if}} >
</div>


</script>

<script id="choiceUserTpl" type="text/x-jquery-tmpl">
<div>
  <input class='form-control' required
    id='${userName_idEl}' name='${userName_nameEl}'
    onclick='choiceUserAndOrg("${userCode_idEl}","${userName_idEl}","S", "${orgCode}", "${index}", "choseUserCallBackRow")' type='text'  value='${userName}' >
</div>


</script>
