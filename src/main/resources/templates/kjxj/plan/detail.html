<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
  <th:block th:include="include :: header('科技先进方案-详细')"/>
  <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg" data-spy="scroll" data-target="#catalogueItem" data-offset="200">
<div class="wrapper wrapper-content fadeInRight ibox-content">
  <form class="m" id="form-plan-edit" th:object="${planDto}">
    <input name="guid" th:field="*{guid}" type="hidden">
    <input name="year" type="hidden" th:field="*{year}">
    <input name="title" type="hidden" th:field="*{title}">
    <input name="managementDept" type="hidden" th:field="*{managementDept}">
    <input name="managementDeptPath" th:field="*{managementDeptPath}" type="hidden">
    <input name="status" type="hidden" th:field="*{status}">


    <div class="row" style="text-align: center">
      <h2> [[${planDto.year}]]（本年度）年度技术创新先进评选初步方案</h2><br>
    </div>
    <!-- 基本信息 -->
    <div id="t1" class="panel panel-default scrollspy-item">
      <div class="panel-heading">
        <h4 class="panel-title">
          <a data-toggle="collapse" data-parent="#version" href="#P1" aria-expanded="false" class="collapsed">
            一、评选方案 <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
          </a>
        </h4>
      </div>
      <!--折叠区域-->
      <div id="P1" class="panel-collapse collapse in" aria-expanded="false">
        <div class="panel-body">
          <!-- 评选方案说明 -->
          <div class="form-control" style="min-height: 100px;" th:text="*{selectionPlan}"></div>

          <br>

          <div class="form-group">
            <label class="control-label"><b>（一）评选范围:</b></label>
            <!-- 评选范围说明 -->
            <br>
            <br>
            <div class="form-control" style="min-height: 100px;" th:text="*{scope}"></div>
            <br>
          </div>

          <div class="form-group">
            <label class="control-label"><b>（二）评选数量（前两年+本年度计划）:</b></label>
            <table id="num-table"></table>
          </div>

          <div class="form-group">
            <br>
            <label class="control-label"><b>（三）评审组及评选方式:</b></label>
            <br>
            <br>
            <div class="">
              <!-- 评选方式说明 -->
              <div class="form-control" style="min-height: 100px;" th:text="*{selectionMethod}"></div>
            </div>
          </div>

          <div class="form-group">
            <br>
            <label class="control-label"><b>（四）时间安排:</b></label>
            <table id="time-table"></table>
          </div>

          <div class="form-group">
            <br>
            <label class="control-label"><b>（五）激励额度:</b></label>
            <br>
            <br>
            <div class="">
              <div class="form-control" style="min-height: 100px;" th:text="*{reward}"></div>
            </div>
          </div>

        </div>
      </div>
      <!--折叠区域end-->
    </div>

    <!-- 二、各单位名额推荐初步方案 -->
    <div id="t2" class="panel panel-default scrollspy-item">
      <div class="panel-heading" title="">
        <h4 class="panel-title" toolbox-title="各单位名额推荐">
          <a data-toggle="collapse" data-parent="#version" href="#P2" aria-expanded="false" class="collapsed">二、各单位名额推荐初步方案
            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
        </h4>
      </div>
      <!--折叠区域-->
      <div id="P2" class="panel-collapse collapse in" aria-expanded="false">
        <div class="panel-body">
          <!-- 各单位名额推荐初步方案说明 -->
          <div class="form-control" style="min-height: 100px;" th:text="*{recommendedPlan}"></div>
          <br>
          <table id="dept-table" border="0" width="100%" class="table table-bordered">
          </table>
        </div>
      </div>
      <!--折叠区域end-->
    </div>
  </form>
</div>

<br>
<br>

<!--<div th:include="kczginclude :: toolbox"/>-->

<div class="toolbar toolbar-bottom">

  <button type="button" class="btn btn-danger"
          onclick="closeItem()">
    <i class="fa fa-reply-all"></i> 返 回
  </button>
</div>

<style>
  .input-50 {
    width: 50px;
    border-bottom: 1px solid #000;
    border-left: 0px;
    border-top: 0px;
    border-right: 0px;
    text-align: center;
  }

</style>

<script th:inline="javascript">
  var prefix = ctx + "kjxj/plan";
  var planDto = [[${planDto}]];
  var year = planDto.year;

  $("#form-plan-edit").validate({
    focusCleanup: true
  });

  $(function () {
    initDeptQuota("dept-table");
    initNumTable("num-table");
    initTimeTable("time-table");

    $("input.date").datetimepicker({
      format: 'yyyy-mm-dd',
      autoclose: true,
      minView: 2
    });
  });

  /**
   * 初始化 单位推荐名额
   * @param {表格ID} id
   */
  function initDeptQuota(id) {
    var data = planDto.deptQuotaList||[];

    var footerStyle = function (column) {
      return {
        price: {
          css: {
            color: 'black',
            'font-weight': 'normal'
          }
        },
        duty: {
          css: {
            color: 'red',
            'font-weight': 'normal'
          }
        },
      } [column.field]
    }

    var options = {
      id: id,
      data: data,
      toolbar: "member-toolbar",
      pagination: false,
      showSearch: false,
      showRefresh: false,
      showToggle: false,
      showColumns: false,
      showFooter: true,
      footerStyle: footerStyle,
      sidePagination: "client",
      columns: [{
        field: 'index',
        align: 'center',
        title: "序号",
        width: '30',
        formatter: function (value, row, index, field) {
          var columnIndex = $.common.sprintf("<input type='hidden' fieldName='index' name='index' value='%s'>", $.table.serialNumber(index));
          var orderNo = $.common.sprintf("<input type='hidden' name='deptQuotaList[%s].orderNo' value='%s'>", index, row.orderNo);
          var pid = $.common.sprintf("<input type='hidden' name='deptQuotaList[%s].pid' value='%s'>", index, row.pid);
          var columnId = $.common.sprintf("<input type='hidden' name='deptQuotaList[%s].guid' value='%s'>", index, row.guid);
          return columnIndex + orderNo + pid + columnId + $.table.serialNumber(index);
        }
      },
        {
          field: 'deptName',
          align: 'left',
          title: '单位',
          footerFormatter: function (value) {
            return "合计";
          }
        },
        {
          field: 'yxtuNum',
          align: 'center',
          title: '优秀团队',
          width: '70',
          formatter: formatterData,
          footerFormatter: function (value) {
            return footSum(value,"yxtuNum");
          }
        },
        {
          field: 'kjxxNum',
          align: 'center',
          title: '科技明星',
          width: '70',
          formatter: formatterData,
          footerFormatter: function (value) {
            return footSum(value,"kjxxNum");
          }
        },
        {
          field: 'kjmxNum',
          align: 'center',
          title: '科技新星',
          width: '70',
          formatter: formatterData,
          footerFormatter: function (value) {
            return footSum(value,"kjmxNum");
          }
        }
      ]
    };
    $.table.init(options);
  }

  function formatterData(value, row, index, field) {
    var showValue = value||0;
    var el = $.common.sprintf("<input type='text' class='form-control' name='deptQuotaList[%s].%s' value='%s' onblur='refreshData()'>", index,field, showValue);
    if(row.pid == null || row.pid === ""){
      showValue = subSum(field, row.guid);
      el = $.common.sprintf("<input type='text' readonly class='form-control' name='deptQuotaList[%s].%s' value='%s' onblur='refreshData()'>", index,field, showValue);
    }
    return showValue;
  }

  function footSum(value,field) {
    var sum = 0;
    for (var i in value) {
      sum += parseFloat(value[i][field]|0);
    }
    return sum.toFixed(0);
  }

  /**
   * 小计
   */
  function subSum(field,pid){
    var sum = 0;
    var data = $("#dept-table").bootstrapTable("getData");
    data.forEach(item => {
      if(item.pid === pid){
        sum += parseInt(item[field]|0);
      }
    });
    return sum;
  }

  function refreshData(){
    sub.editColumn();
  }

  function initNumTable(id) {
    // 阶段	任务	开始日期	结束日期
    var data = planDto.historyList||[];

    var options = {
      id: id,
      data: data,
      pagination: false,
      showSearch: false,
      showRefresh: false,
      showToggle: false,
      showColumns: false,
      showFooter: false,
      sidePagination: "client",
      columns: [{
        field: 'year',
        align: 'center',
        title: "",
        width: '30',
        formatter: function (value, row, index, field) {
          return value;
        }
      },
        {
          field: 'yxtuNum',
          align: 'center',
          title: '优秀团队',
          width: '100',
          formatter: formatter
        },
        {
          field: 'kjxxNum',
          align: 'center',
          title: '科技明星',
          width: '100',
          formatter: formatter
        },
        {
          field: 'kjmxNum',
          align: 'center',
          title: '科技新星',
          width: '100',
          formatter: formatter
        }
      ]
    };
    $.table.init(options);
  }

  function formatter(value, row, index, field) {
    var showValue = value;
    if(row.year === year){
      showValue = planDto[field];
    }
    return showValue + " 个";
  }

  function initTimeTable(id) {
    // 阶段	任务	开始日期	结束日期
    var data = planDto.planScheduleList||[];

    var planScheduleList = "";

    var options = {
      id: id,
      data: data,
      pagination: false,
      showSearch: false,
      showRefresh: false,
      showToggle: false,
      showColumns: false,
      showFooter: false,
      sidePagination: "client",
      columns: [{
        field: 'index',
        align: 'center',
        title: "序号",
        width: '30',
        formatter: function (value, row, index, field) {
          var columnIndex = $.common.sprintf("<input type='hidden' fieldName='index' name='index' value='%s'>", $.table.serialNumber(index));
          var orderNo = $.common.sprintf("<input type='hidden' name='planScheduleList[%s].orderNo' value='%s'>", index, $.table.serialNumber(index));
          var columnId = $.common.sprintf("<input type='hidden' name='planScheduleList[%s].guid' value='%s'>", index, row.guid);
          return columnIndex + orderNo + columnId + $.table.serialNumber(index);
        }
      },
        {
          field: 'stage',
          align: 'center',
          title: '阶段',
          width: '100'
        },
        {
          field: 'task',
          align: 'left',
          title: '任务'
        },
        {
          field: 'beginDate',
          align: 'center',
          title: '开始日期',
          width: '150'
        },
        {
          field: 'endDate',
          align: 'center',
          title: '结束日期',
          width: '150'
        }
      ]
    };
    $.table.init(options);
  }

  /**
   * 添加 计划 阶段
   */
  function addPlanSchedule(){
    var row =  {stage: "", task: "", beginDate: "", endDate: ""};
    sub.addColumn(row, "time-table");
    $("input.date").datetimepicker({
      format: 'yyyy-mm-dd',
      autoclose: true,
      minView: 2
    });
  }

</script>
</body>
</html>
