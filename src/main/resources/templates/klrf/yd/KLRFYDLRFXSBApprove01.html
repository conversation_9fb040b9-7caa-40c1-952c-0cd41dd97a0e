<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <!-- 移动利润分享申报审批页面   -->
    <th:block th:include="include :: header('利润分享申报审批')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<!--利润分享项目申报表首年-->
<div class="wrapper wrapper-content">

    <form role="form" class="form-horizontal m-t" id="form-applyBaseinfo-add" th:object="${applyBaseinfo}">


        <!--第一块-->
        <div class="panel-group" id="accordion1" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#1"
                                               aria-expanded="false"
                                               class="collapsed">基本信息&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h5>
                </div>

                <!--折叠区域-->
                <div id="1" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">


                        <table class="table table-bordered table-hover table-striped">

                            <tr>
                                <td width="20%" style="text-align: right">成果登记号:</td>
                                <td colspan="3" th:utext=" *{registerno}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: right">成果名称:</td>
                                <td width="30%" th:utext="*{fruitName}"></td>
                                <td width="20%" style="text-align: right">项目编号:</td>
                                <td width="30%" style="text-align: left"
                                    th:utext="*{projectNum}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: right">成果来源属性:</td>
                                <td width="30%"
                                    th:utext="*{@dict.getDictName('KLRF','KLRF_CGFromProper',fromProper)}"></td>
                                <td width="20%" style="text-align: right">成果技术水平:</td>
                                <td width="30%" style="text-align: left"
                                    th:utext="*{@dict.getDictName('KLRF','KLRF_CGTechnicalLevel',technicalLevel)}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: right">项目负责部门:</td>
                                <td colspan="3"
                                    th:utext="*{T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(extra2)}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: right">申请人（成果负责人）:</td>
                                <td width="30%" th:utext="*{fzrName}"></td>
                                <td width="20%" style="text-align: right">项目负责单位:</td>
                                <td width="30%" style="text-align: left"
                                    th:utext="*{deptName}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: right">申报单位:</td>
                                <td width="30%">宝山钢铁股份有限公司</td>
                                <td width="20%" style="text-align: right">分享年度:</td>
                                <td width="30%" style="text-align: left"
                                    th:utext="*{shareYear}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: right">分享起始日期:</td>
                                <td width="30%" th:utext="*{shareStart}"></td>
                                <td width="20%" style="text-align: right">分享截止日期:</td>
                                <td width="30%" style="text-align: left"
                                    th:utext="*{shareEnd}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: right">申请人手机号码:</td>
                                <td width="30%" th:utext="*{applyMobile}"></td>
                                <td width="20%" style="text-align: right">申请人座机号码:</td>
                                <td width="30%" style="text-align: left"
                                    th:utext="*{applyPhone}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: right">申请人邮箱:</td>
                                <td colspan="3" th:utext=" *{applyEmail}"></td>
                            </tr>

                        </table>

                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>

        <div class="panel-group" id="accordionCG" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#cg4"
                                               aria-expanded="false"
                                               class="collapsed">
                        成果形成的知识产权情况：
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                </div>


                <!--折叠区域-->
                <div id="cg4" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <!--内容-->
                        <table border="0" width="100%" class="table table-bordered table-hover">
                            <thead>
                            <tr>
                                <td></td>
                                <td>知识产权号</td>
                                <td>法律状态</td>
                                <td>名 称</td>
                            </tr>
                            </thead>
                            <tr th:each="obj,objStat : ${registerZscqList}">
                                <td>[[${obj.title}]]
                                </td>
                                <td>[[${obj.patentCode}]]
                                </td>

                                <td>[[${obj.patentStatus}]]
                                </td>

                                <td>[[${obj.propertyName}]]
                                </td>
                            </tr>

                        </table>


                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>

        <!--第三块-->
        <div class="panel-group" id="accordion3" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#3"
                                               aria-expanded="false"
                                               class="collapsed">转化的科技成果简述（成果来源、主要应用领域、技术水平分析）本栏字数要求（500-1000）：
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                </div>
                <!--折叠区域-->
                <div id="3" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <!--一列-->
                        <div class="form-group">
                            <div class="mnote-editor-title">1、成果来源：</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                                    <div class="form-control-static" style="white-space: break-spaces"
                                         th:utext=" *{ctFruitfrom}"></div>
                                </div>
                            </div>
                        </div>
                        <!--一列end-->

                        <!--一列-->
                        <div class="form-group">
                            <div class="mnote-editor-title">2、主要应用领域：</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                                    <div class="form-control-static" style="white-space: break-spaces"
                                         th:utext=" *{ctMainarea}"></div>
                                </div>
                            </div>
                        </div>
                        <!--一列end-->

                        <!--一列-->
                        <div class="form-group">
                            <div class="mnote-editor-title">3、技术水平分析：</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                                    <div class="form-control-static" style="white-space: break-spaces"
                                         th:utext=" *{ctJsfx}"></div>
                                </div>
                            </div>
                        </div>
                        <!--一列end-->

                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--第三块end-->

        <!--第四块-->
        <div class="panel-group" id="accordion4" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#4"
                                               aria-expanded="false"
                                               class="collapsed">科技成果转化项目简述（项目背景、必要性及重要程度等）本栏字数要求（500-1000）：
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                </div>
                <!--折叠区域-->
                <div id="4" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <!--一列-->
                        <div class="form-group">
                            <div class="mnote-editor-title">1、项目背景：</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                                    <div class="form-control-static" style="white-space: break-spaces"
                                         th:utext=" *{jsBackground}"></div>
                                </div>
                            </div>
                        </div>
                        <!--一列end-->

                        <!--一列-->
                        <div class="form-group">
                            <div class="mnote-editor-title">2、必要性及重要程度：</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                                    <div class="form-control-static" style="white-space: break-spaces"
                                         th:utext=" *{jsNecess}"></div>
                                </div>
                            </div>
                        </div>
                        <!--一列end-->

                        <!--一列-->
                        <div class="form-group">
                            <div class="mnote-editor-title">3、成效及意义：</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                                    <div class="form-control-static" style="white-space: break-spaces"
                                         th:utext=" *{jsCxyy}"></div>
                                </div>
                            </div>
                        </div>
                        <!--一列end-->
                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--第四块end-->


        <!--第五块-->
        <div class="panel-group" id="accordion5" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#5"
                                               aria-expanded="false"
                                               class="collapsed">具体转化实施方案（转化方式、途径、前景等）本栏字数要求（500-1000）：
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                </div>
                <!--折叠区域-->
                <div id="5" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <!--一列-->
                        <div class="form-group">
                            <div class="mnote-editor-title">实施方案：</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                                    <div class="form-control-static" style="white-space: break-spaces"
                                         th:utext=" *{impelementScheme}"></div>
                                </div>
                            </div>
                        </div>
                        <!--一列end-->

                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--第五块end-->


        <!--第六块-->
        <div class="panel-group" id="accordion6" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#6"
                                               aria-expanded="false"
                                               class="collapsed">经济效益预期（预期产生的经济效益，并明确计算基准、计算规则；以及其它需要约定的边界条件）：
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                </div>
                <!--折叠区域-->
                <div id="6" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">


                        <table class="table table-bordered table-hover table-striped">

                            <tr>
                                <td width="20%" style="text-align: left">1、当年申报的经济效益（万元）:</td>
                                <td colspan="3"
                                    th:utext="*{T(com.baosight.bscdkj.kl.rf.common.KlrfUtil).subZeroAndDot(sbxy)}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: left">2、具体经济效益计算公式:</td>
                                <td colspan="3" th:utext=" *{xygs}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: left">3、当年经财务确认的经济效益（万元）:</td>
                                <td colspan="3"
                                    th:utext="*{T(com.baosight.bscdkj.kl.rf.common.KlrfUtil).subZeroAndDot(confirmxy)}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: left">4、当年财务确认效益计算公式:</td>
                                <td colspan="3" th:utext=" *{extra6}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: left">5、第一年效益计算说明:</td>
                                <td colspan="3" th:utext=" *{xysmFirst}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: left">6、第二年效益预期计算说明:</td>
                                <td colspan="3" th:utext=" *{xysmSec}"></td>
                            </tr>

                            <tr>
                                <td width="20%" style="text-align: left">7、第三年效益预期计算说明:</td>
                                <td colspan="3" th:utext=" *{xysmThird}"></td>
                            </tr>

                        </table>
                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--第六块end-->

        <!--第七块-->
        <div class="panel-group" id="accordion7" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#7"
                                               aria-expanded="false"
                                               class="collapsed">项目成员情况：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                </div>

                <!--折叠区域-->
                <div id="7" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">


                        <div class="row">
                            <table border="0" width="100%" class="table table-bordered table-hover">
                                <thead>
                                <tr>
                                    <td>序号</td>
                                    <td>工号</td>
                                    <td>姓名</td>
                                    <td>职务</td>
                                    <td>职称</td>
                                    <td>学历</td>
                                    <td>所在单位</td>
                                    <td>角色分工</td>
                                    <td>贡献度（%）</td>
                                    <td>是否成果登记完成人</td>
                                </tr>
                                </thead>
                                <tr th:each="obj,objStat : ${personList}">
                                    <td>[[${objStat.index}+1]]
                                    </td>
                                    <td>[[${obj.empId}]]
                                    </td>

                                    <td>[[${obj.empName}]]
                                    </td>

                                    <td>[[${obj.job}]]
                                    </td>
                                    <!--                                        备注-->
                                    <td>[[${obj.title}]]
                                    </td>
                                    <td>[[${obj.education}]]
                                    </td>

                                    <td>[[${obj.deptName}]]
                                    </td>

                                    <td>[[${obj.role}]]
                                    </td>

                                    <td th:include="include :: formatDecimal(dataDecimalValue=${obj.gxxs})">
                                    </td>
                                    <td>[[${obj.isdjry}]]
                                    </td>

                                </tr>

                            </table>

                        </div>

                        <div class="row">
                            <div class="mnote-editor-title">贡献度统计</div>
                            <div class="mnote-editor-box">

                                <table  class="table table-hover table-striped">

                                    <tr>
                                        <td width="10%" style="text-align: right">项目负责人:</td>
                                        <td width="20%" th:utext="${T(com.baosight.bscdkj.kl.rf.common.KlrfUtil).subZeroAndDot(fzrGxxs)}"></td>
                                        <td width="10%" style="text-align: right">主要完成人:</td>
                                        <td width="20%" style="text-align: left"
                                            th:utext="${T(com.baosight.bscdkj.kl.rf.common.KlrfUtil).subZeroAndDot(wcrGxxs)}"></td>
                                        <td width="10%" style="text-align: right">其他参加人:</td>
                                        <td width="20%" style="text-align: left"
                                            th:utext="${T(com.baosight.bscdkj.kl.rf.common.KlrfUtil).subZeroAndDot(cjrGxxs)}"></td>
                                    </tr>

                                </table>

                                </div>
                            </div>


                        </div>


                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        <!--第七块end-->

    </form>
</div>

</body>
</html>
