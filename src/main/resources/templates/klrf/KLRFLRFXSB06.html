<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <!-- 非首年利润分享申报   -->
    <th:block th:include="include :: header('非首年利润分享申报')"/>

    <th:block th:include="include :: baseJs"/>
    <script th:src="@{/js/jquery.tmpl.js}"></script>
</head>
<body class="white-bg">
<!--利润分享项目申报表非首年-->
<div class="wrapper wrapper-content">
    <form role="form" class="form-horizontal m-t" id="form-applyBaseinfo-add" th:object="${applyBaseinfo}">

        <input id="lrfxId" name="lrfxId" type="hidden">
        <!--        往年利润分享申报主键-->
        <input id="wnLrfxId" name="wnLrfxId" th:value="*{lrfxId}" type="hidden">
        <!--第一块-->
        <div class="panel-group" id="accordion1" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#1"
                                               aria-expanded="false"
                                               class="collapsed">基本信息&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h5>
                </div>

                <!--折叠区域-->
                <div id="1" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">成果登记号：</label>
                                    <div class="col-sm-9 form-control-static" th:utext=" *{registerno}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">成果名称： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{fruitName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目编号： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{projectNum}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group"
                                     th:include="include :: initSelectBox(labelName='成果来源属性：',see=true,value=*{fromProper},businessType='KLRF', dictCode='KLRF_CGFromProper')"></div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group"
                                     th:include="include :: initSelectBox(labelName='成果技术水平：',value=*{technicalLevel},isrequired=true,businessType='KLRF', dictCode='KLRF_CGTechnicalLevel')"></div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">项目负责部门：</label>
                                    <div class="col-sm-9"
                                         th:include="/component/selectOrg::init(isrequired=true,orgCodeId='extra2',orgNameId='extra2Name',value=*{extra2})">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <!--        默认带出当前登录者,申请人和申请人手机样式在同一行-->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">申请人（成果负责人）： </label>
                                    <div class="col-sm-9"
                                         th:include="/component/selectUser::init(isrequired=true,userCodeId='fzr',userNameId='fzrName',value=*{fzr},selectType='M')">
                                    </div>
                                </div>
                            </div>

                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目负责单位： </label>
                                    <input id="deptCode" name="deptCode" th:value="*{deptCode}" type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{deptName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">申报单位： </label>
                                    <div class="col-sm-9 form-control-static">宝山钢铁股份有限公司</div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">分享年度： </label>
                                    <div class="col-sm-9">
                                        <input name="shareYear" th:value="${shareYear}" type="hidden">
                                        <div class="col-sm-9 form-control-static" th:utext="${shareYear}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <th:block
                                            th:include="include :: initDate(id='shareStart',name='shareStart',strValue=*{shareStart},labelName='分享起始日期：',see=true)"></th:block>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <th:block
                                            th:include="include :: initDate(id='shareStart',name='shareEnd',strValue=*{shareEnd},labelName='分享截止日期：',see=true)"></th:block>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">申请人手机号码： </label>

                                    <div class="col-sm-9">
                                        <input type="text" name="applyMobile" th:value="*{applyMobile}"
                                               class="form-control"
                                               id="applyMobile" required>
                                    </div>

                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">申请人座机号码： </label>
                                    <div class="col-sm-9">
                                        <input type="text" name="applyPhone" th:value="*{applyPhone}"
                                               class="form-control"
                                               id="applyPhone" required>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">申请人邮箱：</label>
                                    <div class="col-sm-9">
                                        <input type="text" name="applyEmail" th:value="*{applyEmail}"
                                               class="form-control"
                                               id="applyEmail" required>
                                    </div>

                                </div>
                            </div>
                        </div>


                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>

        <th:block th:include="klrf/klrfInclude:: klrf-CGZscq(registerno=*{registerno})"/>

        <!--第三块-->
        <div class="panel-group" id="accordion3" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#3"
                                               aria-expanded="false"
                                               class="collapsed">转化的科技成果简述（成果来源、主要应用领域、技术水平分析）本栏字数要求（500-1000）：
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                </div>
                <!--折叠区域-->
                <div id="3" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <ul class="sortable-list connectList agile-list ui-sortable">
                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label  p-title">1、成果来源：</label>
                                            <div class="col-sm-9">
                                                <textarea name="ctFruitfrom" class="form-control"
                                                          required>[[${applyBaseinfo.ctFruitfrom}]]</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label p-title ">2、主要应用领域：</label>
                                            <div class="col-sm-9">
                                                <textarea name="ctMainarea" class="form-control"
                                                          required>[[${applyBaseinfo.ctMainarea}]]</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label p-title ">3、技术水平分析：</label>
                                            <div class="col-sm-9">
                                                <textarea name="ctJsfx" class="form-control"
                                                          required>[[${applyBaseinfo.ctJsfx}]]</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>

                        </ul>
                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--第三块end-->

        <!--第四块-->
        <div class="panel-group" id="accordion4" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#4"
                                               aria-expanded="false"
                                               class="collapsed">科技成果转化项目简述（项目背景、必要性及重要程度等）本栏字数要求（500-1000）：
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                </div>
                <!--折叠区域-->
                <div id="4" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <ul class="sortable-list connectList agile-list ui-sortable">
                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label  p-title">1、项目背景：</label>
                                            <div class="col-sm-9">
                                                <textarea name="jsBackground" class="form-control"
                                                          required>[[${applyBaseinfo.jsBackground}]]</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label p-title ">2、必要性及重要程度：</label>
                                            <div class="col-sm-9">
                                                <textarea name="jsNecess" class="form-control"
                                                          required>[[${applyBaseinfo.jsNecess}]]</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label p-title ">3、成效及意义：</label>
                                            <div class="col-sm-9">
                                                <textarea name="jsCxyy" class="form-control"
                                                          required>[[${applyBaseinfo.jsCxyy}]]</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>

                        </ul>
                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--第四块end-->


        <!--第五块-->
        <div class="panel-group" id="accordion5" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#5"
                                               aria-expanded="false"
                                               class="collapsed">具体转化实施方案（转化方式、途径、前景等）本栏字数要求（500-1000）：
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                </div>
                <!--折叠区域-->
                <div id="5" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <ul class="sortable-list connectList agile-list ui-sortable">
                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label  p-title">实施方案：</label>
                                            <div class="col-sm-9">
                                                <textarea name="impelementScheme" class="form-control"
                                                          required>[[${applyBaseinfo.impelementScheme}]]</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                        </ul>
                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--第五块end-->


        <!--第六块-->
        <div class="panel-group" id="accordion6" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#6"
                                               aria-expanded="false"
                                               class="collapsed">经济效益预期（预期产生的经济效益，并明确计算基准、计算规则；以及其它需要约定的边界条件）：
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span
                                class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                </div>
                <!--折叠区域-->
                <div id="6" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <ul class="sortable-list connectList agile-list ui-sortable">
                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label  p-title">1、当年申报的经济效益（万元）：</label>
                                            <div class="col-sm-9">
                                                <input id="sbxy" name="sbxy" onchange="checkIsNumber('sbxy')"
                                                       class="form-control" required/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>
                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label p-title ">2、当年经财务确认的经济效益（万元）：</label>
                                            <div class="col-sm-9">
                                                <input readonly name="confirmxy" class="form-control"/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label p-title ">3、具体经济效益计算公式：</label>
                                            <div class="col-sm-9">
                                                <input name="xygs" class="form-control" required/>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label p-title ">4、第一年效益计算说明：</label>
                                            <div class="col-sm-9">
                                                <textarea name="xysmFirst" class="form-control"
                                                          required>[[${applyBaseinfo.xysmFirst}]]</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label p-title ">5、第二年效益预期计算说明：</label>
                                            <div class="col-sm-9">
                                                <textarea name="xysmSec" class="form-control"
                                                          required>[[${applyBaseinfo.xysmSec}]]</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>

                            <li class="info-element">
                                <div class="row ">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label class="col-sm-3 control-label p-title ">6、第三年效益预期计算说明：</label>
                                            <div class="col-sm-9">
                                                <textarea name="xysmThird" class="form-control"
                                                          required>[[${applyBaseinfo.xysmThird}]]</textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </li>

                        </ul>
                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--第六块end-->

        <!--第七块-->
        <div class="panel-group" id="accordion7" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#7"
                                               aria-expanded="false"
                                               class="collapsed">项目成员情况：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                </div>

                <!--折叠区域-->
                <div id="7" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="col-sm-12  table-striped">
                            <div class="bootstrap-table">
                                <div class="fixed-table-container" style="padding-bottom: 0px;">
                                    <div class="fixed-table-body">
                                        <table id="bootstrap-table-applyMembers" class="table table-hover">
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--第七块end-->

        <!--第八块-->
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#8"
                                               aria-expanded="false"
                                               class="collapsed">相关附件上传：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                </div>

                <!--折叠区域-->
                <div id="8" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="col-sm-12  table-striped">

                            <div class="form-group">
                                <label class="col-sm-3 control-label">相关附件：</label>
                                <div class="col-sm-8">
                                    <div class="form-group"
                                         th:include="/component/attachment :: init(name='lrfxsbXgfjId',id='lrfxsbXgfjId',sourceId=*{lrfxId},sourceModule='KLRF_LRFXSB_XGFJ')"></div>
                                </div>
                            </div>

                            <br>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">人员调整说明及审批附件：</label>
                                <div class="col-sm-8">
                                    <div class="form-group"
                                         th:include="/component/attachment :: init(name='memberAttachmentId',id='memberAttachmentId',sourceId=*{lrfxId},sourceModule='KLRF_RYTZFJ')"></div>
                                </div>
                            </div>

                            <br>

                            <div class="form-group">
                                <label class="col-sm-3 control-label">经济效益评审预审表：</label>
                                <div class="col-sm-8">
                                    <div class="form-group"
                                         th:include="/component/attachment :: init(name='benefitAttachmentId',id='benefitAttachmentId',sourceId=*{lrfxId},sourceModule='KLRF_JJXYFJ')"></div>
                                </div>
                            </div>


                        </div>
                        <!--内容end-->
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--第八块end-->


    </form>
</div>
<div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-primary"
                onclick="zcOtherApply()">
            <i class="fa fa-hdd-o"></i>&nbsp;暂存
        </button>

    <th:block th:include="component/wfSubmitOne :: init(taskId=${taskId},callback=submitHandler)"/>

        <button type="button" class="btn btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>&nbsp;返 回
        </button>

</div>


<script th:inline="javascript">
    var prefix = ctx + "klrf/applyBaseinfo";

    var roleDatas = [[${roleDatas}]];

    $("#form-applyBaseinfo-add").validate({
        focusCleanup: true
    });

    //暂存首年利润分享申报
    function zcOtherApply() {
        var config = {
            url: prefix + "/zcOtherApply",
            type: "post",
            dataType: "json",
            data: $('#form-applyBaseinfo-add').serialize(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                if (result.code == web_status.SUCCESS) {
                    $("#lrfxId").val(result.data.lrfxId);
                    $("#wnLrfxId").val(null);
                    console.log("主键：" + result.data.lrfxId);
                    $.modal.alertSuccess(result.msg);
                } else {
                    $.modal.alertError(result.msg);
                }
                $.modal.closeLoading();
            }
        };
        $.ajax(config)
    }

    function submitHandler() {
        if ($.validate.form()) {
            //检查当年申报的经济效益 申报效益：金苹果＜5000万、科研大项目＜2000万、普通科研项目＜500万的成果不能提交
            // 当年申报效益
            var sbxy = $("#sbxy").val();
            var fromProper = $("#fromProper").val();

            if (sbxy < 500) {
                $.modal.alertError("申报的经济效益不能小于500（万元）！");
                return;
            } else {
                // 4 金苹果 5 大项目
                if ("4" === fromProper && sbxy < 5000) {
                    $.modal.alertError("申报效益金苹果项目不能小于5000（万元）！");
                    return;
                } else if ("5" === fromProper && sbxy < 2000) {
                    $.modal.alertError("申报效益科研大项目不能小于2000（万元）！");
                    return;
                }
            }

            if (!checkGxxs()) { //项目成员贡献度不符合要求
                $.modal.alertError("项目负责人（含单、双负责人）≥30% 且≤60%，主要完成人≥40%且≤70%，其他参加人≤30%，合计100%。");
                return;
            }


            $.operate.saveTabAlert(prefix + "/sumbitOtherApply", $('#form-applyBaseinfo-add').serialize());

        }
    }

    //检查贡献度：贡献度分配比例规则：项目负责人≥30%、主要完成人≥40%，合计100%
    function checkGxxs() {

        var num = $('#bootstrap-table-applyMembers').bootstrapTable('getOptions').totalRows;

        var totalGxxs = 0;
        var xmfzrTotalGxxs = 0;
        var zywcrTotalGxxs = 0;
        var qtcjrTotalGxxs = 0;

        for (var i = 0; i < num; i++) {
            var gxxs = $("#contributeDegree" + i).val();
            var role = $("#role" + i).val();

            totalGxxs = dcmAdd(totalGxxs, gxxs);
            if (role == "1") { //项目负责人
                xmfzrTotalGxxs = dcmAdd(xmfzrTotalGxxs, gxxs);
            } else if (role == "2") { //主要完成人
                zywcrTotalGxxs = dcmAdd(zywcrTotalGxxs, gxxs);
            } else if (role == "3") { //其他参加人
                qtcjrTotalGxxs = dcmAdd(qtcjrTotalGxxs, gxxs);
            }
        }
        if (totalGxxs == 100 && xmfzrTotalGxxs >= 30 && xmfzrTotalGxxs <= 60) {
            if (zywcrTotalGxxs >= 40 && zywcrTotalGxxs <= 70 && qtcjrTotalGxxs <= 30) {
                return true;
            }
        }


        return false;
    }

    //项目成员情况
    $(function () {
        var options = {
            url: ctx + "klrf/applyPerson/listByLrfxIdSc?lrfxId=" + [[${applyBaseinfo.lrfxId}]],
            id: "bootstrap-table-applyMembers",
            modalName: "项目成员情况",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [
                {
                    field: 'index',
                    align: 'center',
                    title: `<button class="btn btn-success btn-circle" type="button" onclick="addColumn()"><i class="fa fa-plus"></i> </button>
                            <button class="btn btn-primary btn-circle" type="button" onclick="delAllMembers()"><i class="fa fa-minus"></i> </button>`,
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='applyMembers[%s].xh' value='%s'>", index, $.table.serialNumber(index));
                        return columnIndex + $.table.serialNumber(index);
                    }
                },
                {
                    field: 'empId',
                    align: 'center',
                    title: '工号',
                    formatter: function (value, row, index) {
                        var html = "";
                        if (value == null || value == "") {
                            html = $.common.sprintf("<input required id='applyMemberEmpId%s' onblur=\"addApplyMember('%s',this)\" class='form-control'  type='text' name='applyMembers[%s].empId' value='%s' >", index,index, index, value);
                        } else {
                            html = $.common.sprintf("<input readonly class='form-control'  type='text' name='applyMembers[%s].empId' value='%s' >", index, value);
                        }
                        return html;
                    }
                },
                {
                    field: 'empName',
                    align: 'center',
                    title: '姓名',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input readonly id='applyMemberEmpName%s' class='form-control'  type='text' name='applyMembers[%s].empName' value='%s'>", index, index, value);
                        return html;
                    }
                },

                {
                    field: 'job',
                    align: 'center',
                    title: '职务',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input readonly id='applyMemberJobLevel%s' class='form-control' type='text' name='applyMembers[%s].job' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'title',
                    align: 'center',
                    title: '职称',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input readonly id='applyMemberJobTitle%s' class='form-control' type='text' name='applyMembers[%s].title' value='%s' >", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'education',
                    align: 'center',
                    title: '学历',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input readonly id='applyMemberEducationHistory%s' class='form-control' type='text' name='applyMembers[%s].education' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'deptCodeAndName',
                    align: 'center',
                    title: '所在单位',
                    formatter: function (value, row, index) {

                        var value1 = "";
                        var value2 = "";
                        if (value != null && value != "") {
                            value1 = String(value).split(",")[0]; //deptCode
                            value2 = String(value).split(",")[1]; //deptName
                            // // var html = $.common.sprintf("<input class='form-control' required type='text' name='applyMembers[%s].deptCode' value='%s'>", index, value);

                        }
                        var html = $.common.sprintf("<input id='applyMemberDeptCodeAndName%s'  value='%s'  type=\"hidden\"\n" +
                            "                    /> <input id='applyMemberDeptCode%s' name=\"applyMembers[%s].deptCode\"   value='%s'  type=\"hidden\"\n" +
                            "                    /> <input readonly id='applyMemberDeptName%s' name=\"applyMembers[%s].deptName\"   value='%s' class=\"form-control\" type=\"text\">\n",
                            index, value, index, index, value1, index, index, value2);

                        return html;
                    }
                },
                {
                    field: 'role',
                    align: 'center',
                    title: '角色分工',
                    formatter: function (value, row, index) {
                        var applyMemberName = 'applyMembers[' + index + '].role';
                        var id = 'role' + index;
                        return dictToSelect(roleDatas, value, applyMemberName, id);
                    }
                },
                {
                    field: 'gxxs',
                    align: 'center',
                    title: '贡献度（%）',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input onchange=\"if(value<=0)value=''\"  id='contributeDegree%s' class='form-control' required type='number' name='applyMembers[%s].gxxs' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'isdjry',
                    align: 'center',
                    title: '是否成果登记完成人',
                    formatter: function (value, row, index) {
                        var data = [{index: index, value1: value}];
                        return $("#isCgdjMember").tmpl(data).html();
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        return `<button class="btn btn-primary btn-circle btn-sm" type="button" onclick="delMember(${index})"><i class="fa fa-minus"></i></button>`;
                    }
                }]
        }
        $.table.init(options);
    });

    //删除项目成员
    function delMember(index) {
        if ($.modal.confirm("确认删除当前行吗?", function () {
            sub.editColumn();
            $("#bootstrap-table-applyMembers").bootstrapTable('remove', {field: '$index', values: [index]});
        })) ;
    }
    //删除所有项目成员
    function delAllMembers() {
        if ($.modal.confirm("确认删除所有行吗?", function () {
            $("#bootstrap-table-applyMembers").bootstrapTable('removeAll');
        })) ;
    }

    function addColumn() {
        var row = {
            empId: "",
            empName: "",
            job: "",
            title: "",
            education: "",
            deptCodeAndName: "",
            role: "",
            gxxs: "",
            isdjry: ""
        }
        sub.addColumn(row, "bootstrap-table-applyMembers");
    }

    //检查输入数据是否为数字
    function checkIsNumber(id) {
        var value = $("#" + id).val();
        var reg = /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g;
        if (!reg.test(value)) { //正则验证是否为数字
            $.modal.alertError("当前输入的数字不符合规范，请检查！");
            $("#" + id).val(null);
            return;
        }
    }

    //新增项目成员带出项目成员信息
    function addApplyMember(index, obj) {
        var value = $(obj).val();
        if (value != "" || value != null) {
            $.ajax({
                url: prefix + "/getADUser",
                data: {userCode: value},
                datatype: "json",
                success: function (result) {
                    console.log(result);
                    if (result.code == web_status.SUCCESS) {
                        $("#applyMemberEmpName" + index).val(result.data.userName);
                        $("#applyMemberJobLevel" + index).val(result.data.userJob);
                        $("#applyMemberJobTitle" + index).val(result.userZhiCheng);
                        $("#applyMemberEducationHistory" + index).val(result.data.userEducational);
                        $("#applyMemberDeptCode" + index).val(result.data.deptCode);
                        $("#applyMemberDeptName" + index).val(result.data.deptName);
                        $("#applyMemberDeptCodeAndName" + index).val(result.data.deptCode + "," + result.data.deptName);
                    } else {
                        $("#applyMemberEmpId" + index).val(null);
                        $.modal.alertWarning(result.msg);
                    }
                }
            });

        }
    }


    // 数据字典转下拉框
    function dictToSelect(datas, value, name, id) {
        var actions = [];
        actions.push($.common.sprintf("<select id='%s' class='form-control' name='%s'>", id, name));
        $.each(datas, function (index, dict) {
            actions.push($.common.sprintf("<option value='%s'", dict.dictValue));
            if (dict.dictValue == ('' + value)) {
                actions.push(' selected');
            }
            actions.push($.common.sprintf(">%s</option>", dict.dictName));
        });
        actions.push('</select>');
        return actions.join('');
    }

    //两数相加
    function dcmAdd(arg1, arg2) {
        var r1, r2, m;
        try {
            r1 = arg1.toString().split(".")[1].length;
        } catch (e) {
            r1 = 0;
        }
        try {
            r2 = arg2.toString().split(".")[1].length;
        } catch (e) {
            r2 = 0;
        }
        m = Math.pow(10, Math.max(r1, r2));
        return ((arg1 * m + arg2 * m) / m).toFixed(Math.max(r1, r2));
    }

</script>
</body>
</html>

<!-- 是否成果登记人员 -->
<script id="isCgdjMember" type="text/x-jquery-tmpl">
<div class="radio-box">
          <label><input id="applyMembersYes${index}"  value="1" name="applyMembers[${index}].isdjry" {{if value1!="0"}}checked{{/if}}  type="radio">是</label>
          <label><input id="applyMembersNo${index}" value="0" name="applyMembers[${index}].isdjry" {{if value1=="0"}}checked{{/if}}  type="radio">否</label>
</div>


</script>