<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改1.7-01.01现场支撑费用明细')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-sceneFymx-edit" th:object="${sceneFymx}">
         <div class="panel-group" id="accordion" role="tablist"
                             aria-multiselectable="true">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#version"
                                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                                        aria-hidden="true"></i></span>
                                        </a>
                                    </h4>
                                </div>
                                <div id="jbxx" class="panel-collapse collapse in"
                                     aria-expanded="false">
                                    <div class="panel-body">
            <input name="sceneMxGuid" th:field="*{sceneMxGuid}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">关联主键：</label>
                <div class="col-sm-8">
                    <input name="guid" th:field="*{guid}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支撑人员工号：</label>
                <div class="col-sm-8">
                    <input name="sceneMxUserCode" th:field="*{sceneMxUserCode}" class="form-control" type="text">
                </div>
            </div>

      <div class="form-group" th:include="include :: initSelectBox(id='sceneMxGwType', name='sceneMxGwType',businessType=null, dictCode=null, value=${sceneFymx.sceneMxGwType} ,labelName='岗位类型')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">开始日期：</label>
                <div class="col-sm-8">
                    <input name="sceneMxBeginDate" th:field="*{sceneMxBeginDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">结束日期：</label>
                <div class="col-sm-8">
                    <input name="sceneMxEndDate" th:field="*{sceneMxEndDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">出差天数：</label>
                <div class="col-sm-8">
                    <input name="sceneMxDay" th:field="*{sceneMxDay}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">出差地点：</label>
                <div class="col-sm-8">
                    <input name="sceneMxPlace" th:field="*{sceneMxPlace}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">住宿费金额：</label>
                <div class="col-sm-8">
                    <input name="sceneMxZsfTotal" th:field="*{sceneMxZsfTotal}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">住宿费税额(元)：</label>
                <div class="col-sm-8">
                    <input name="sceneMxZsfSeTotal" th:field="*{sceneMxZsfSeTotal}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">市外交通费：</label>
                <div class="col-sm-8">
                    <input name="sceneMxSwjtfTotal" th:field="*{sceneMxSwjtfTotal}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">市内交通费：</label>
                <div class="col-sm-8">
                    <input name="sceneMxSnjtfTotal" th:field="*{sceneMxSnjtfTotal}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">车船：</label>
                <div class="col-sm-8">
                    <input name="sceneMxCcjbfTotal" th:field="*{sceneMxCcjbfTotal}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">出差补贴：</label>
                <div class="col-sm-8">
                    <input name="sceneMxCcbtTotal" th:field="*{sceneMxCcbtTotal}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">其他费金额：</label>
                <div class="col-sm-8">
                    <input name="sceneMxQtTotal" th:field="*{sceneMxQtTotal}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">其他费税额(元)：</label>
                <div class="col-sm-8">
                    <input name="sceneMxQtseTotal" th:field="*{sceneMxQtseTotal}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">合计：</label>
                <div class="col-sm-8">
                    <input name="sceneMxSumTotal" th:field="*{sceneMxSumTotal}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">费用说明：</label>
                <div class="col-sm-8">
                    <input name="sceneMxFyConent" th:field="*{sceneMxFyConent}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段3：</label>
                <div class="col-sm-8">
                    <input name="extra3" th:field="*{extra3}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段4：</label>
                <div class="col-sm-8">
                    <input name="extra4" th:field="*{extra4}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段5：</label>
                <div class="col-sm-8">
                    <input name="extra5" th:field="*{extra5}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',businessType=null, dictCode=null ,value=${sceneFymx.delStatus} ,labelName='删除状态')">
            </div>
</div>
                    </div>
                </div>
            </div>
        </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>

    <script th:inline="javascript">
        var prefix = ctx + "zzfy/sceneFymx";

        $("#form-sceneFymx-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-sceneFymx-edit').serialize());
            }
        }

    </script>
</body>
</html>