<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('1.7-01.01现场支撑费用明细列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>关联主键：</label>
                                <input type="text" name="guid"/>
                            </li>
                            <li>
                                <label>支撑人员工号：</label>
                                <input type="text" name="sceneMxUserCode"/>
                            </li>
                            <li>
                                <label>岗位类型：</label>
                                <select name="sceneMxGwType" th:with="type=${@dict.getDictList(null,null)}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>开始日期：</label>
                                <input type="text" name="sceneMxBeginDate"/>
                            </li>
                            <li>
                                <label>结束日期：</label>
                                <input type="text" name="sceneMxEndDate"/>
                            </li>
                            <li>
                                <label>出差天数：</label>
                                <input type="text" name="sceneMxDay"/>
                            </li>
                            <li>
                                <label>出差地点：</label>
                                <input type="text" name="sceneMxPlace"/>
                            </li>
                            <li>
                                <label>住宿费金额：</label>
                                <input type="text" name="sceneMxZsfTotal"/>
                            </li>
                            <li>
                                <label>住宿费税额(元)：</label>
                                <input type="text" name="sceneMxZsfSeTotal"/>
                            </li>
                            <li>
                                <label>市外交通费：</label>
                                <input type="text" name="sceneMxSwjtfTotal"/>
                            </li>
                            <li>
                                <label>市内交通费：</label>
                                <input type="text" name="sceneMxSnjtfTotal"/>
                            </li>
                            <li>
                                <label>车船：</label>
                                <input type="text" name="sceneMxCcjbfTotal"/>
                            </li>
                            <li>
                                <label>出差补贴：</label>
                                <input type="text" name="sceneMxCcbtTotal"/>
                            </li>
                            <li>
                                <label>其他费金额：</label>
                                <input type="text" name="sceneMxQtTotal"/>
                            </li>
                            <li>
                                <label>其他费税额(元)：</label>
                                <input type="text" name="sceneMxQtseTotal"/>
                            </li>
                            <li>
                                <label>合计：</label>
                                <input type="text" name="sceneMxSumTotal"/>
                            </li>
                            <li>
                                <label>费用说明：</label>
                                <input type="text" name="sceneMxFyConent"/>
                            </li>
                            <li>
                                <label>扩展字段1：</label>
                                <input type="text" name="extra1"/>
                            </li>
                            <li>
                                <label>扩展字段2：</label>
                                <input type="text" name="extra2"/>
                            </li>
                            <li>
                                <label>扩展字段3：</label>
                                <input type="text" name="extra3"/>
                            </li>
                            <li>
                                <label>扩展字段4：</label>
                                <input type="text" name="extra4"/>
                            </li>
                            <li>
                                <label>扩展字段5：</label>
                                <input type="text" name="extra5"/>
                            </li>
                            <li>
                                <label>删除状态：</label>
                                <select name="delStatus" th:with="type=${@dict.getDictList(null,null)}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzfy/sceneFymx";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "1.7-01.01现场支撑费用明细",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'sceneMxGuid',
                    title: '明细主键',
                    visible: false
                },
                {
                    field: 'guid',
                    title: '关联主键'
                },
                {
                    field: 'sceneMxUserCode',
                    title: '支撑人员工号'
                },
                {
                    field: 'sceneMxGwType',
                    title: '岗位类型'
                },
                {
                    field: 'sceneMxBeginDate',
                    title: '开始日期'
                },
                {
                    field: 'sceneMxEndDate',
                    title: '结束日期'
                },
                {
                    field: 'sceneMxDay',
                    title: '出差天数'
                },
                {
                    field: 'sceneMxPlace',
                    title: '出差地点'
                },
                {
                    field: 'sceneMxZsfTotal',
                    title: '住宿费金额'
                },
                {
                    field: 'sceneMxZsfSeTotal',
                    title: '住宿费税额(元)'
                },
                {
                    field: 'sceneMxSwjtfTotal',
                    title: '市外交通费'
                },
                {
                    field: 'sceneMxSnjtfTotal',
                    title: '市内交通费'
                },
                {
                    field: 'sceneMxCcjbfTotal',
                    title: '车船'
                },
                {
                    field: 'sceneMxCcbtTotal',
                    title: '出差补贴'
                },
                {
                    field: 'sceneMxQtTotal',
                    title: '其他费金额'
                },
                {
                    field: 'sceneMxQtseTotal',
                    title: '其他费税额(元)'
                },
                {
                    field: 'sceneMxSumTotal',
                    title: '合计'
                },
                {
                    field: 'sceneMxFyConent',
                    title: '费用说明'
                },
                {
                    field: 'extra1',
                    title: '扩展字段1'
                },
                {
                    field: 'extra2',
                    title: '扩展字段2'
                },
                {
                    field: 'extra3',
                    title: '扩展字段3'
                },
                {
                    field: 'extra4',
                    title: '扩展字段4'
                },
                {
                    field: 'extra5',
                    title: '扩展字段5'
                },
                {
                    field: 'delStatus',
                    title: '删除状态'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    field: 'deleteUserLabel',
                    title: '删除人'
                },
                {
                    field: 'deleteDate',
                    title: '删除时间'
                },
                {
                    field: 'recordVersion',
                    title: '版本号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.sceneMxGuid + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.sceneMxGuid + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>