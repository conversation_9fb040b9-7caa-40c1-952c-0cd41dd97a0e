<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增1.7-01现场支撑费用报销')"/>

    <th:block th:include="include :: baseJs"/>
    <script th:src="@{/js/jquery.tmpl.js}"></script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-scene-add" th:object="${tzzfySceneFymxEx}">
        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">报销明细
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <input name="type" id="type" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{type}">
                                    <div
                                         th:include="include :: choiceUser(labelName='支撑人员：',userCodeId='userCode',userNameId='userName',selectType='S',isrequired='true',value=*{sceneMxUserCode})">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">岗位名称：</label>
                                    <div class="col-sm-9">
                                        <input  type="text" name="sceneMxGwName"
                                                class="form-control"
                                                id="sceneMxGwName" required th:field="*{sceneMxGwName}" />
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <div class="col-md-6">
                                    <label class="col-sm-3 control-label is-required">岗位类型：</label>
                                    <div class="col-sm-9">
                                        <select class="form-control" id="sceneMxGwType" name="sceneMxGwType"
                                                th:with="dictData=${@maintain.getPostType()}"
                                                th:field="*{sceneMxGwType}">
                                            <option value="">请选择</option>
                                            <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                    th:value="${dict.dictCode}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">起始日期：</label>
                                    <div class="col-sm-9">
                                        <input id="sceneMxBeginDate" required th:field="*{sceneMxBeginDate}"
                                               class="form-control" type="date" onchange="setDate('sceneMxBeginDate')"/>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">结束日期：</label>
                                    <div class="col-sm-9">
                                        <input id="sceneMxEndDate" required th:field="*{sceneMxEndDate}"
                                               class="form-control" type="date" onchange="setDate('sceneMxEndDate')"/>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">出差天数： </label>
                                    <div class="col-sm-9">
                                        <input type="text" name="sceneMxDay" required
                                               class="form-control" disabled="disabled"
                                               id="sceneMxDay" th:field="*{sceneMxDay}" th:value="0">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">出差地点： </label>
                                    <div class="col-sm-9">
                                        <input type="text" name="sceneMxPlace" required
                                               class="form-control"
                                               id="sceneMxPlace" th:field="*{sceneMxPlace}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <span>相关费用 <span style="color: #F00">（注：住宿费为增值税普通发票或增值税电子普通发票时，住宿费金额为总金额，税额无需录入；住宿费为增值税专用发票时，不含税金额与税额须分别录入）</span></span>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">住宿费 <span style="color: #F00">金额</span>（元）：
                                    </label>
                                    <div class="col-sm-9">
                                        <input type="text" name="sceneMxZsfTotal"
                                               class="form-control"
                                               id="sceneMxZsfTotal" th:field="*{sceneMxZsfTotal}" placeholder="不含税"
                                               onchange="checkIsNumber('sceneMxZsfTotal')"
                                        >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">住宿费<span style="color: #F00">税额</span>（元）：
                                    </label>
                                    <div class="col-sm-9">
                                        <input type="text" name="sceneMxZsfSeTotal"
                                               class="form-control"
                                               id="sceneMxZsfSeTotal" th:field="*{sceneMxZsfSeTotal}"
                                               onchange="checkIsNumber('sceneMxZsfSeTotal')"
                                        >
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">市外交通费（元）： </label>
                                    <div class="col-sm-9">
                                        <input type="text" name="sceneMxSwjtfTotal"
                                               class="form-control"
                                               id="sceneMxSwjtfTotal" th:field="*{sceneMxSwjtfTotal}"
                                               onchange="checkIsNumber('sceneMxSwjtfTotal')"
                                        >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">市内交通费（元）： </label>
                                    <div class="col-sm-9">
                                        <input type="text" name="sceneMxSnjtfTotal"
                                               class="form-control"
                                               id="sceneMxSnjtfTotal" th:field="*{sceneMxSnjtfTotal}"
                                               onchange="checkIsNumber('sceneMxSnjtfTotal')"
                                        >
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">车船（机）票费（元）： </label>
                                    <div class="col-sm-9">
                                        <input type="text" name="sceneMxCcjbfTotal"
                                               class="form-control"
                                               id="sceneMxCcjbfTotal" th:field="*{sceneMxCcjbfTotal}"
                                               onchange="checkIsNumber('sceneMxCcjbfTotal')"
                                               placeholder="不含机票统结费">
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">出差补贴（元）： </label>
                                    <div class="col-sm-9">
                                        <input type="text" name="sceneMxCcbtTotal"
                                               class="form-control"
                                               id="sceneMxCcbtTotal" th:field="*{sceneMxCcbtTotal}"
                                               onchange="checkIsNumber('sceneMxCcbtTotal')"
                                        >
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">其他费<span style="color: #F00">金额</span>（元）：
                                    </label>
                                    <div class="col-sm-9">
                                        <input type="text" name="sceneMxQtTotal"
                                               class="form-control"
                                               id="sceneMxQtTotal" th:field="*{sceneMxQtTotal}"
                                               onchange="checkIsNumber('sceneMxQtTotal')"
                                        >
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">其他费<span style="color: #F00">税额</span>（元）：
                                    </label>
                                    <div class="col-sm-9">
                                        <input type="text" name="sceneMxQtseTotal"
                                               class="form-control"
                                               id="sceneMxQtseTotal" th:field="*{sceneMxQtseTotal}"
                                               onchange="checkIsNumber('sceneMxQtseTotal')"
                                        >
                                    </div>
                                </div>
                            </div>
                        </div>

                       <!-- <div class="form-group">
                            <label class="col-sm-2 control-label">合计（元）：</label>
                            <div class="col-sm-10">
                                <input name="sceneMxSumTotal" id="sceneMxSumTotal" class="form-control" type="text"
                                       readonly th:field="*{sceneMxSumTotal}">
                            </div>
                        </div>-->
                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">合计（元）：</label>
                                    <div class="col-sm-9">
                                        <input name="sceneMxSumTotal" id="sceneMxSumTotal" class="form-control" type="text"
                                               disabled="disabled" th:field="*{sceneMxSumTotal}">
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">

</div>


<script th:inline="javascript">
    var prefix = ctx + "zzfy/scene"

    $("#form-scene-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/addFymx", $('#form-scene-add').serialize());
        }
    }

    //检查输入数据是否为数字
    function checkIsNumber(id) {
        var value = $("#" + id).val();
        var reg = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
        if (!reg.test(value)) { //正则验证是否为数字
            $.modal.alertError("当前输入的数字不符合规范，请检查！");
            $("#" + id).val(null);
            return;
        } else {
            //计算价格
            calculatesceneMxSumTotal(id);
        }
    }

    function calculatesceneMxSumTotal() {
        //住宿金额
        var sceneMxZsfTotal = $("#sceneMxZsfTotal").val();
        //住宿税额
        var sceneMxZsfSeTotal = $("#sceneMxZsfSeTotal").val();
        //市外交通
        var sceneMxSwjtfTotal = $("#sceneMxSwjtfTotal").val();
        //市内交通
        var sceneMxSnjtfTotal = $("#sceneMxSnjtfTotal").val();
        //车船机票
        var sceneMxCcjbfTotal = $("#sceneMxCcjbfTotal").val();
        //出差补贴
        var sceneMxCcbtTotal = $("#sceneMxCcbtTotal").val();
        //其它费用
        var sceneMxQtTotal = $("#sceneMxQtTotal").val();
        //其它税额
        var sceneMxQtseTotal = $("#sceneMxQtseTotal").val();

        var sceneMxSumTotal = (+sceneMxZsfTotal) + (+sceneMxZsfSeTotal) + (+sceneMxSwjtfTotal) + (+sceneMxSnjtfTotal) + (+sceneMxCcjbfTotal) + (+sceneMxCcbtTotal) + (+sceneMxQtTotal) + (+sceneMxQtseTotal);
        document.getElementById("sceneMxSumTotal").value = sceneMxSumTotal;
    }


    $(function () {
        var type = $("#type").val();
        if (type == 'checkFymx') {
            document.getElementById("userName").disabled = "userName";//支撑人员
            document.getElementById("sceneMxGwName").disabled = "sceneMxGwName";//岗位名称
            document.getElementById("sceneMxGwType").disabled = "sceneMxGwType";//岗位类型
            document.getElementById("sceneMxBeginDate").disabled = "sceneMxBeginDate";//开始时间
            document.getElementById("sceneMxEndDate").disabled = "sceneMxEndDate";//结束时间
            document.getElementById("sceneMxPlace").disabled = "sceneMxPlace";//出差地点
            document.getElementById("sceneMxZsfTotal").disabled = "sceneMxZsfTotal";//住宿费
            document.getElementById("sceneMxZsfSeTotal").disabled = "sceneMxZsfSeTotal";//住宿税额
            document.getElementById("sceneMxSwjtfTotal").disabled = "sceneMxSwjtfTotal";//市外交通
            document.getElementById("sceneMxSnjtfTotal").disabled = "sceneMxSnjtfTotal";//市内交通
            document.getElementById("sceneMxCcjbfTotal").disabled = "sceneMxCcjbfTotal";//车船机票
            document.getElementById("sceneMxCcbtTotal").disabled = "sceneMxCcbtTotal";//出差补贴
            document.getElementById("sceneMxQtTotal").disabled = "sceneMxQtTotal";//其它费用
            document.getElementById("sceneMxQtseTotal").disabled = "sceneMxQtseTotal";//其它税费
        }
    })

    function setDate(id) {
        var sceneMxBeginDate = $("#sceneMxBeginDate").val();
        var sceneMxEndDate = $("#sceneMxEndDate").val();
        calculateDate(id);

        if (sceneMxBeginDate != '' && sceneMxEndDate != '') {
            //计算时间差
            var date_start = new Date(sceneMxBeginDate);
            var date_end = new Date(sceneMxEndDate);
            var dates = date_end.getTime() - date_start.getTime();
            var days = Math.floor(dates / (24 * 3600 * 1000)) + 1;
            document.getElementById("sceneMxDay").value = days;
        }
    }

    function calculateDate(id){
        var idDate = $("#"+id).val();
        var date_now = new Date(idDate);
        //得到当前年份
        var year = date_now.getFullYear();
        //得到当前月份
        //注：
        //  1：js中获取Date中的month时，会比当前月份少一个月，所以这里需要先加一
        //  2: 判断当前月份是否小于10，如果小于，那么就在月份的前面加一个 '0' ， 如果大于，就显示当前月份
        var month = date_now.getMonth() + 1 < 10 ? "0" + (date_now.getMonth() + 1) : (date_now.getMonth() + 1);
        // 得到当前选择时间
        var date = date_now.getDate() < 10 ? "0" + date_now.getDate() : date_now.getDate();
        if(id == 'sceneMxBeginDate'){
            $("#sceneMxEndDate").attr("min", year + "-" + month + "-" + date);
        }else if(id == 'sceneMxEndDate'){
            $("#sceneMxBeginDate").attr("max", year + "-" + month + "-" + date);
        }
    }

    $(function () {
        calculateDate('sceneMxBeginDate');
        calculateDate('sceneMxEndDate');
    })
</script>
</script>
</body>
</html>