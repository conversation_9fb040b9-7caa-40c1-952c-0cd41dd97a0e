<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('查看现场支撑费用报销')"/>

    <th:block th:include="include :: baseJs"/>
    <script th:src="@{/js/jquery.tmpl.js}"></script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-scene-add" th:object="${TzzfySceneEx}">
        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目编号： </label>
                                    <input id="sceneId" class="form-control" type="hidden"
                                           th:field="*{sceneId}">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.projectNum}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目名称： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.projectName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">受让单位部门： </label>
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.srfDwdeptName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">推广单位部门： </label>
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.tgfDwdeptName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目负责人： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.tgfFzrName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">受让方负责人： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.srfFzrName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">联系电话： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.srfTel}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">申请人： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneUserName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">申请日期：： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneDate}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <span>点击<a href="#" style="color: blue" th:onclick="detailMain([[*{tzzlxMain.mainId}]])">此处</a>查看项目详细信息</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="mnote-editor-title">现场支撑工作内容</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-9 form-control-static" th:utext="*{extra1}"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">报支发票张数：</label>
                            <div class="col-sm-6">
                                <div class="col-sm-9 form-control-static" th:utext="*{sceneCount}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#2"
                                               aria-expanded="false"
                                               class="collapsed">费用报销：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down"
                                                    aria-hidden="true"></i></span></a></h4>
                </div>
                <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="col-sm-12">
                            <div class="col-sm-12 select-table ">
                                <table id="bootstrap-table-applyMembers"></table>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">报支金额（元）： </label>
                                <div class="col-sm-9">
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneBzTotal}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6" id="tjje">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">统结金额（元）： </label>
                                <div class="col-sm-9">
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneTjTotal}"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">税额总计（元）： </label>
                                <div class="col-sm-3 form-control-static" th:utext="*{sceneSeTotal}"></div>

                                <label class="col-sm-3 control-label">费用总计（元）： </label>
                                <div class="col-sm-3 form-control-static" th:utext="*{sceneFyTotal}"></div>

                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">票据张数： </label>
                                <div class="col-sm-3 form-control-static" th:utext="*{billCount}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="director">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">报支单号： </label>
                                <div class="col-sm-9">
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneBzdh}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">付款日期： </label>
                                <div class="col-sm-9">
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneFkDate}"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
            <!--附件上传strat -->
            <div class="panel-group" id="accordion9" role="tablist"
                 aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version"
                               href="#jb09" aria-expanded="false" class="collapsed">附件<span
                                    class="pull-right"><i
                                    class="fa fa-chevron-down" aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jb09" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="row form-group">
                                <div class="col-sm-12">
                                    <label class="col-sm-2 control-label">附件上传：</label>
                                    <div class="col-sm-10">
                                        <div th:include="/component/attachment :: init(display='none',name='xgfjAttachmentId',id='xgfjAttachmentId',sourceId=*{sceneId},sourceModule='SCENE_FJ',see=true)">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--附件上传end-->
            <div class="panel-group" id="accordion10" role="tablist"
                 aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version"
                               href="#jb0f" aria-expanded="false" class="collapsed">备注<span
                                    class="pull-right"><i
                                    class="fa fa-chevron-down" aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jb0f" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="row form-group">
                                <div class="col-sm-12 form-control-static" th:text="*{extra3}">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <th:block th:include="component/wfCommentList3 :: init(businessId=*{sceneId})" />
    </form>
</div>

<div style="height: 30px"></div>


<div class="row">
    <div class="col-sm-offset-5 col-sm-10" id="btnHandler" th:if="${#strings.isEmpty(processInstanceId)}">
        <div class="toolbar toolbar-bottom">
            <button type="button" class="btn btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返 回
            </button>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzfy/scene"
    var postData = [[${@maintain.getPostType()}]];


    $("#form-scene-add").validate({
        focusCleanup: true
    });

    $(function () {
        var sceneUrl = "";
        var sceneId = $("#sceneId").val();
        if (sceneId != '') {
            sceneUrl = prefix + "/querySceneFyxm/" + sceneId;
        }
        var options = {
            url: sceneUrl,
            id: "bootstrap-table-applyMembers",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'sceneMxUserCode',
                    align: 'center',
                    title: "支撑人员工号"
                },
                {
                    field: 'sceneMxUserName',
                    align: 'center',
                    title: "支撑人员姓名"
                },
                {
                    field: 'sceneMxGwName',
                    align: 'center',
                    title: '岗位名称'
                },
                {
                    field: 'sceneMxGwType',
                    align: 'center',
                    title: '岗位类型',
                    formatter:function (value,row,index) {
                        return  $.table.selectDictLabel(postData,value,"dictCode","dictName")
                    }
                },
                {
                    field: 'sceneMxBeginDate',
                    align: 'center',
                    title: '开始日期'
                },
                {
                    field: 'sceneMxEndDate',
                    align: 'center',
                    title: '结束日期'
                },
                {
                    field: 'sceneMxPlace',
                    align: 'center',
                    title: '出差地点'
                },
                {
                    field: 'sceneMxSumTotal',
                    title: '费用（元）'
                },
                {
                    field: 'operate',
                    title: '操作',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="Details(\'' + row.sceneMxId  + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        return actions.join('');
                    }
                }
            ]
        }
        $.table.init(options);
    });


    function Details(sceneMxId) {
        var url = ctx + "zzfy/scene/queryFymxS?sceneMxId="+sceneMxId;
        var options = {
            title: '报销明细',
            width: "1000",
            height: '600',
            url: url,
            callBack: closureChoice
        };
        $.modal.openOptions(options);
    }

    function closureChoice(index, layero) {
        //关闭页面
        layer.close(index);
    }

    function detailMain(mainId) {
        $.modal.openTab("项目详细信息", prefix + "/detailMain?mainId="+mainId);
    }

    $(function () {
        var projectArea = $("#projectArea").val();
        if (projectArea == 'jtn') {
            $("#jtn").show();
        } else {
            $("#jtn").hide();
        }
    })

</script>
</body>
</html>