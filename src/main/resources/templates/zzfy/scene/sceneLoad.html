<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('【请填写功能名称】列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="row form-group">
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">项目编号：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="projectNum" required type="text">
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">项目名称：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="projectName" required type="text">
                        </div>
                    </div>
                </div>

                <div class="row form-group">
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">项目负责人：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="tgfFzrName" required type="text">
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">项目主管：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="tgfXmzgName" required type="text">
                        </div>
                    </div>
                </div>
                <div class="select-list" style="float: right">
                    <ul>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" >
                <i class="fa fa-plus"></i> 添加
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzfy/scene";

    $(function () {
        var options = {
            url: prefix + "/queryDraft",
            createUrl: prefix + "/projectList",
            updateUrl: prefix + "/editSceneLoad?sceneId={id}",
            detailUrl: prefix + "/detailSceneLoad?sceneId={id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "差旅费报支申请",
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'projectNum',
                    align: 'center',
                    title: "项目编号",
                },
                {
                    field: 'projectName',
                    align: 'center',
                    title: "项目名称",
                },
                {
                    field: 'tgfFzrName',
                    align: 'center',
                    title: "项目负责人",
                },
                {
                    field: 'tgfXmzgName',
                    align: 'center',
                    title: "项目主管",
                },
                /*{
                    field: 'status',
                    align: 'center',
                    title: "状态",
                },
                {
                    field: 'status',
                    align: 'center',
                    title: "操作人",
                }*/
                {
                    field: 'operate',
                    align: 'center',
                    title: '操作',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.sceneId + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.sceneId + '\')"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.sceneId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        /*actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="Details()"><i class="fa fa-remove"></i>测试</a>');*/
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });


   /* function Details() {
        var url = ctx + "zzjt/settle";
        $.modal.openTab("详情页", url + "/queryDetail/" + "20220109100601276585728");
    }*/

</script>
</body>
</html>