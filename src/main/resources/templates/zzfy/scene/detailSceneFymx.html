<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增1.7-01现场支撑费用报销')"/>

    <th:block th:include="include :: baseJs"/>
    <script th:src="@{/js/jquery.tmpl.js}"></script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-scene-add" th:object="${tzzfySceneFymxEx}">
        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">报销明细
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">支撑人员：</label>
                                    <input id="sceneMxGwType" class="form-control" type="hidden"
                                           th:field="*{sceneMxGwType}">
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxUserName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">岗位名称：</label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxGwName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <div class="col-md-6">
                                    <label class="col-sm-3 control-label">岗位类型：</label>
                                    <div class="col-sm-9 form-control-static" id="GwType"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">起始日期：</label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxBeginDate}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">结束日期：</label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxEndDate}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">出差天数： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxDay}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">出差地点： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxPlace}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-12">
                                <div class="form-group">
                                    <span>相关费用 <span style="color: #F00">（注：住宿费为增值税普通发票或增值税电子普通发票时，住宿费金额为总金额，税额无需录入；住宿费为增值税专用发票时，不含税金额与税额须分别录入）</span></span>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">住宿费 <span style="color: #F00">金额</span>（元）：
                                    </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxZsfTotal}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">住宿费<span style="color: #F00">税额</span>（元）：
                                    </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxZsfSeTotal}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">市外交通费（元）： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxSwjtfTotal}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">市内交通费（元）： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxSnjtfTotal}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">车船（机）票费（元）： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxCcjbfTotal}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">出差补贴（元）： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxCcbtTotal}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">其他费<span style="color: #F00">金额</span>（元）：
                                    </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxQtTotal}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">其他费<span style="color: #F00">税额</span>（元）：
                                    </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxQtseTotal}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">合计（元）：</label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneMxSumTotal}"></div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">

</div>


<script th:inline="javascript">
    var prefix = ctx + "zzfy/scene"

    $("#form-scene-add").validate({
        focusCleanup: true
    });

    var postData = [[${@maintain.getPostType()}]];
    $(function () {
        var v = $("#sceneMxGwType").val();
        var gw = $.table.selectDictLabel(postData,v,"dictCode","dictName");
        document.getElementById("GwType").innerHTML =  gw.replace(/[^\u4e00-\u9fa5]/gi, "");
    })

</script>
</script>
</body>
</html>