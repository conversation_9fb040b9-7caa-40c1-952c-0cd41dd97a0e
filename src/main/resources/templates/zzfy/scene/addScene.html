<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增1.7-01现场支撑费用报销')"/>

    <th:block th:include="include :: baseJs"/>
    <script th:src="@{/js/jquery.tmpl.js}"></script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div th:if="${taskId!=null && taskId!='' }" class="form-group"
         th:include="include :: step(approveKind='ZZZC_XCZCFYBX',currentNode=${activityCode})"></div>
    <form class="form-horizontal m" id="form-scene-add" th:object="${TzzfySceneEx}">
        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目编号： </label>
                                    <input id="taskId" name="taskId" th:value="${taskId}" type="hidden">
                                    <input th:value="${processInstanceId}" id="processInstanceId" type="hidden">
                                    <input th:value="${hideColumn}" id="hideColumn" type="hidden">
                                    <input id="type" th:field="*{type}" type="hidden">
                                    <input id="sceneId" class="form-control" type="hidden"
                                           th:field="*{sceneId}">
                                    <input id="bizId" class="form-control" type="hidden"
                                           th:field="*{tzzlxMain.mainId}">
                                    <input id="projectArea" class="form-control" type="hidden"
                                           th:field="*{tzzlxMain.projectArea}">
                                    <input id="projectNum" name="projectNum"
                                           th:field="*{tzzlxMain.projectNum}"
                                           type="hidden">
                                    <input id="tgfDwdeptCode" name="tgfDwdeptCode"
                                           th:field="*{tzzlxMain.tgfDwdeptCode}"
                                           type="hidden">
                                    <input id="role" name="role"
                                           th:field="*{role}"
                                           type="hidden">
                                    <input id="activityKey" name="activityKey" type="hidden">
                                    <input id="transitionKey" name="transitionKey" type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.projectNum}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目名称： </label>
                                    <input id="projectName" name="projectName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.projectName}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.projectName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">受让单位部门： </label>
                                    <input id="srfDwdeptName" name="srfDwdeptName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfDwdeptName}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.srfDwdeptName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">推广单位部门： </label>
                                    <input id="tgfDwdeptName" name="tgfDwdeptName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfDwdeptName}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.tgfDwdeptName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目负责人： </label>
                                    <input id="tgfFzrCode" name="tgfFzrCode" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfFzrCode}"
                                           type="hidden">
                                    <input id="tgfFzrName" name="tgfFzrName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfFzrName}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.tgfFzrName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">受让方负责人： </label>
                                    <input id="srfFzrName" name="srfFzrName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfFzrName}"
                                           type="hidden">
                                    <input id="srfFzrCode" name="srfFzrCode" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfFzrCode}"
                                           type="hidden">
                                    <input id="srfXmzgCode" name="srfXmzgCode" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfXmzgCode}"
                                           type="hidden">
                                    <input id="tgfXmzgCode" name="tgfXmzgCode" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfXmzgCode}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.srfFzrName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">联系电话： </label>
                                    <input id="srfTel" name="srfTel" onfocus=this.blur() th:field="*{tzzlxMain.srfTel}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.srfTel}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">申请人： </label>
                                    <input id="sceneUserName" name="sceneUserName" onfocus=this.blur()
                                           th:field="*{sceneUserName}" type="hidden">
                                    <input id="sceneUserCode" name="sceneUserCode" onfocus=this.blur()
                                           th:field="*{sceneUserCode}" type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{sceneUserName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <th:block
                                            th:include="include :: initDate(id='sceneDate',name='sceneDate',labelName='申请日期：',strValue=*{sceneDate})"></th:block>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <span>点击<a href="#" style="color: blue"
                                               th:onclick="detailMain([[*{tzzlxMain.mainId}]])">此处</a>查看项目详细信息</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="mnote-editor-title"><span class="txt-impt">*</span>现场支撑工作内容</div>
                            <div class="mnote-editor-box">
                                <textarea type="text" name="extra1" class="form-control" required placeholder="请输入文本"
                                          style=" width: 100%; height:180px;" th:field="*{extra1}"></textarea>
                            </div>
                        </div>

                        <!--   <div class="form-group" id="jtn">
                               <label class="col-sm-2 control-label">纸质确认单附件：</label>
                               <div class="col-sm-10">
                                   <div th:include="include :: layui-upload(display='none',name=*{zzzcConstants.XGFJ_ZZFYJT},id=*{zzzcConstants.XGFJ_ZZFYJT},sourceId=*{sceneId},sourceModule=*{zzzcConstants.BUSINESS_TYPE})"></div>
                               </div>
                           </div>

                           <div class="form-group" id='gfn'>
                               <div class="form-group">
                                   <div class="form-group" th:include="include :: layui-upload(id=*{zzzcConstants.XGFJ_ZZFYGF},name=*{zzzcConstants.XGFJ_ZZFYGF},labelName='票据（电子文档）及报支单文件附件：',
                                   labelClass='col-sm-3 control-label',sourceId=*{sceneId},sourceModule=*{zzzcConstants.BUSINESS_TYPE})"></div>
                               </div>
                              &lt;!&ndash; <label class="col-sm-2 control-label">票据（电子文档）及报支单文件附件：</label>
                               <div class="col-sm-8">
                                   <div class="gf" th:include="include :: layui-upload(display='none',name=*{zzzcConstants.XGFJ_ZZFYGF},id=*{zzzcConstants.XGFJ_ZZFYGF},sourceId=*{sceneId},sourceModule=*{zzzcConstants.BUSINESS_TYPE})"></div>
                               </div>&ndash;&gt;
                           </div>-->

                        <div class="form-group">
                            <label class="col-sm-2 control-label">报支发票张数：</label>
                            <div class="col-sm-6">
                                <input id="sceneCount" name="sceneCount" class="form-control" type="text"
                                       onchange="checkIsNumber('sceneCount')"
                                       th:field="*{sceneCount}">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#2"
                                               aria-expanded="false"
                                               class="collapsed">现场支撑人员情况：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down"
                                                    aria-hidden="true"></i></span></a></h4>
                </div>
                <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="col-sm-12">
                            <div id="btn">
                                <button class="btn btn-white btn-sm" onclick="addColumn()" type="button"><i
                                        class="fa fa-plus"> 增加</i></button>
                                <button class="btn btn-white btn-sm" onclick="delcolumn()" type="button"><i
                                        class="fa fa-minus"> 删除</i></button>
                            </div>
                            <div class="col-sm-12 select-table ">
                                <table id="bootstrap-table-applyMembers"></table>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="bzjr">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">报支金额（元）： </label>
                                <div class="col-sm-9">
                                    <input type="text" name="sceneBzTotal"
                                           class="form-control"
                                           id="sceneBzTotal" readonly th:field="*{sceneBzTotal}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6" id="tjje">
                            <div class="form-group">
                                <label class="col-sm-3 control-label is-required">统结金额（元）： </label>
                                <div class="col-sm-9">
                                    <input type="text" name="sceneTjTotal"
                                           class="form-control" onchange="checkIsNumbers('sceneTjTotal')"
                                           id="sceneTjTotal" required="required" th:field="*{sceneTjTotal}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="srzj">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">税额总计（元）： </label>
                                <div class="col-sm-9">
                                    <input type="text" name="sceneSeTotal"
                                           class="form-control"
                                           id="sceneSeTotal" readonly th:field="*{sceneSeTotal}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">费用总计（元）： </label>
                                <div class="col-sm-9">
                                    <input type="text" name="sceneFyTotal"
                                           class="form-control"
                                           id="sceneFyTotal" readonly th:field="*{sceneFyTotal}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label">票据张数： </label>
                                <div class="col-sm-9">
                                    <input type="text" name="billCount"
                                           class="form-control"
                                           id="billCount" th:field="*{billCount}">
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="row" id="director">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="col-sm-3 control-label is-required">报支单号： </label>
                                <div class="col-sm-9">
                                    <input type="text" name="sceneBzdh"
                                           class="form-control"
                                           id="sceneBzdh" th:field="*{sceneBzdh}">
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <div th:include="include :: initDate(id='sceneFkDate', name='sceneFkDate',labelName='付款日期：',strValue=*{sceneFkDate})"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row" style="margin-top: 30px">
            </div>
            <!--附件上传strat -->
            <div class="panel-group" id="accordion9" role="tablist"
                 aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version"
                               href="#jb09" aria-expanded="false" class="collapsed">附件<span
                                    class="pull-right"><i
                                    class="fa fa-chevron-down" aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jb09" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="row form-group">
                                <div class="col-sm-12">
                                    <label class="col-sm-2 control-label">附件上传：</label>
                                    <div class="col-sm-10">
                                        <div th:include="/component/attachment :: init(display='none',name='xgfjAttachmentId',id='xgfjAttachmentId',sourceId=*{sceneId},sourceModule='SCENE_FJ',isrequired=true)">
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!--附件上传end-->
            <div class="panel-group" id="accordion9" role="tablist"
                 aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version"
                               href="#jb0f" aria-expanded="false" class="collapsed">备注<span
                                    class="pull-right"><i
                                    class="fa fa-chevron-down" aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jb0f" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="row form-group">
                                <div class="col-sm-12">
                                    <textarea name="extra3" th:text="*{extra3}" class="form-control" rows="4"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 审批-->
            <div class="panel panel-default" th:if="${! #strings.isEmpty(processInstanceId)}"
                 th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=${processInstanceId},required='true',freeFlow='false')"></div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10" id="btnHandler" th:if="${#strings.isEmpty(processInstanceId)}">
        <div class="toolbar toolbar-bottom">
            <button type="button" class="btn  btn-primary"
                    onclick="delScene()">
                <i class="fa fa-file-movie-o"></i>&nbsp;删 除
            </button>

            <button type="button" class="btn  btn-primary"
                    onclick="temporarily()">
                <i class="fa fa-hdd-o"></i>&nbsp;暂 存
            </button>
            <button type="button" class="btn  btn-primary"
                    onclick="submitHandler()">
                <i class="fa fa-check"></i>提 交
            </button>
            <button type="button" class="btn btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返 回
            </button>
        </div>
    </div>

    <div class="col-sm-offset-4 col-sm-10" id="btnSave" th:if="${! #strings.isEmpty(processInstanceId)}">
        <div class="toolbar toolbar-bottom">
            <button class="btn btn-xl btn-primary" th:onclick="openProcessTrack([[${processInstanceId}]])"
                    type="button">
                <i class="fa fa-eye"></i>&nbsp;流程跟踪图
            </button>
            <button type="button" class="btn  btn-primary"
                    onclick="temporarily()">
                <i class="fa fa-hdd-o"></i>&nbsp;暂 存
            </button>
            <th:block th:if="${! #strings.isEmpty(taskId)}"
                      th:include="/component/wfSubmitOne :: init(taskId=${taskId},callback='submitSave')"></th:block>
            <th:block th:if="${! #strings.isEmpty(taskId)}"
                      th:include="/component/wfReturn :: init(taskId=${taskId},callback=submitReturn)"/>
            <button class="btn btn-xl btn-danger" onclick="closeItem()" type="button">
                <i class="fa fa-reply-all"></i>返 回
            </button>
        </div>
    </div>
</div>
<!-- 审批历史 -->
<div th:if="*{! #strings.isEmpty(processInstanceId)}"
     th:include="include :: includeTaskHistoryList(instanceId=*{processInstanceId})"></div>


<script th:inline="javascript">
    var prefix = ctx + "zzfy/scene"


    $("#form-scene-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        var projectArea = $("#projectArea").val();
        var type = $("#type").val();
        if (projectArea == 'jtn') {
            //判断是否添加附件
            var jtfj = $("#xgfjZzfyJt").val();
            if (jtfj.length == 0) {
                $.modal.alertWarning("集团项目必须上传附件");
                return;
            }
        }
        if ($.validate.form()) {
            $.operate.saveTabAlert(prefix + "/addSave", $('#form-scene-add').serialize());
        }
    }

    $(function () {
        var hideColumn = $("#hideColumn").val();
        var sceneUrl = "";
        var sceneId = $("#sceneId").val();
        if (sceneId != '') {
            sceneUrl = prefix + "/querySceneFyxm/" + sceneId;
        }
        var options = {
            url: sceneUrl,
            id: "bootstrap-table-applyMembers",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='tzzfySceneFymx[%s].sceneMxId' value='%s'>", index, index);
                        return columnIndex + $.table.serialNumber(index);
                    }
                },
                {
                    field: 'sceneMxUserCode',
                    align: 'center',
                    title: "支撑人员工号",
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input readonly id='sceneMxUserCode%s' class='form-control'  type='text'  value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'sceneMxUserName',
                    align: 'center',
                    title: "支撑人员姓名",
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input readonly id='sceneMxUserName%s' class='form-control'  type='text' name='tzzfySceneFymx[%s].sceneMxUserName' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'sceneMxGwName',
                    align: 'center',
                    title: '岗位名称',
                    width: '150',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input readonly id='sceneMxGwName%s' class='form-control'  type='text' name='tzzfySceneFymx[%s].sceneMxGwName' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'sceneMxGwType',
                    align: 'center',
                    title: '岗位类型',
                    width: '150',
                    formatter: function (value, row, index) {
                        var applyMemberName = 'tzzfySceneFymx[' + index + '].sceneMxGwType';
                        var id = 'sceneMxGwType' + index;
                        return dictToSelect(data, value, applyMemberName, id);
                    }
                },
                {
                    field: 'sceneMxBeginDate',
                    align: 'center',
                    title: '开始日期',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input readonly id='sceneMxBeginDate%s' class='form-control'  type='text' name='tzzfySceneFymx[%s].sceneMxBeginDate' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'sceneMxEndDate',
                    align: 'center',
                    title: '结束日期',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input readonly id='sceneMxEndDate%s' class='form-control'  type='text' name='tzzfySceneFymx[%s].sceneMxEndDate' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'sceneMxPlace',
                    align: 'center',
                    title: '出差地点',
                    width: '200',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input readonly id='sceneMxPlace%s' class='form-control'  type='text' name='tzzfySceneFymx[%s].sceneMxPlace' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'sceneMxSumTotal',
                    title: '费用（元）',
                    formatter: function (value, row, index) {
                        if (hideColumn == 'Y') {
                            $.form.reset();
                            $.table.hideColumn("sceneMxSumTotal");
                        }
                        var html = $.common.sprintf("<input readonly id='sceneMxSumTotal%s' class='form-control'  type='text' name='tzzfySceneFymx[%s].sceneMxSumTotal' value='%s'>", index, index, value);
                        html += $.common.sprintf("<input id='sceneMxDay%s' type='hidden' name='tzzfySceneFymx[%s].sceneMxDay' value='%s'>", index, index, row.sceneMxDay);
                        html += $.common.sprintf("<input type='hidden' id='sceneMxZsfTotal%s' name='tzzfySceneFymx[%s].sceneMxZsfTotal' value='%s'>", index, index, row.sceneMxZsfTotal);
                        html += $.common.sprintf("<input type='hidden' id='sceneMxZsfSeTotal%s' class='form-control'  name='tzzfySceneFymx[%s].sceneMxZsfSeTotal' value='%s'>", index, index, row.sceneMxZsfSeTotal);
                        html += $.common.sprintf("<input type='hidden' id='sceneMxSwjtfTotal%s' class='form-control'  name='tzzfySceneFymx[%s].sceneMxSwjtfTotal' value='%s'>", index, index, row.sceneMxSwjtfTotal);
                        html += $.common.sprintf("<input type='hidden' id='sceneMxSnjtfTotal%s' class='form-control'  name='tzzfySceneFymx[%s].sceneMxSnjtfTotal' value='%s'>", index, index, row.sceneMxSnjtfTotal);
                        html += $.common.sprintf("<input type='hidden' id='sceneMxCcjbfTotal%s' class='form-control'  name='tzzfySceneFymx[%s].sceneMxCcjbfTotal' value='%s'>", index, index, row.sceneMxCcjbfTotal);
                        html += $.common.sprintf("<input type='hidden' id='sceneMxCcbtTotal%s' class='form-control'  name='tzzfySceneFymx[%s].sceneMxCcbtTotal' value='%s'>", index, index, row.sceneMxCcbtTotal);
                        html += $.common.sprintf("<input type='hidden' id='sceneMxQtTotal%s' class='form-control'  name='tzzfySceneFymx[%s].sceneMxQtTotal' value='%s'>", index, index, row.sceneMxQtTotal);
                        html += $.common.sprintf("<input type='hidden' id='sceneMxQtseTotal%s' class='form-control'  name='tzzfySceneFymx[%s].sceneMxQtseTotal' value='%s'>", index, index, row.sceneMxQtseTotal);
                        html += $.common.sprintf("<input type='hidden' id='sceneMxSumTotal%s' class='form-control'  name='tzzfySceneFymx[%s].sceneMxSumTotal' value='%s'>", index, index, row.sceneMxSumTotal);
                        html += $.common.sprintf("<input type='hidden' id='sceneMxUserCode%s' class='form-control'  name='tzzfySceneFymx[%s].sceneMxUserCode' value='%s'>", index, index, row.sceneMxUserCode);
                        html += $.common.sprintf("<input type='hidden' id='Taxes%s' class='form-control'  value='%s'>", index, row.Taxes);
                        html += $.common.sprintf("<input type='hidden' id='Reported%s' class='form-control'  value='%s'>", index, row.Reported);
                        html += $.common.sprintf("<input type='hidden' id='SumTotal%s' class='form-control'  value='%s'>", index, row.SumTotal);
                        return html;
                    }
                },
                {
                    field: 'operate',
                    title: '操作',
                    formatter: function (value, row, index) {
                        if (hideColumn == 'Y') {
                            $.form.reset();
                            $.table.hideColumn("operate");
                        }
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="Details(\'' + index + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        return actions.join('');
                    }
                }
            ]
        }
        $.table.init(options);
    });

    function addColumn() {
        var type = "addFymx";
        var url = ctx + "zzfy/scene/FymxAdd?type=" + type;
        var options = {
            title: '报销明细',
            width: "1000",
            height: '660',
            url: url,
            callBack: choiceLegalNoCallback
        };
        $.modal.openOptions(options);
    }

    var rows = 0;

    function Details(index) {
        //支撑人员
        var sceneMxUserName = $("#sceneMxUserName" + index).val();
        //岗位名称
        var sceneMxGwName = $("#sceneMxGwName" + index).val();
        //岗位类型
        var sceneMxGwType = $("#sceneMxGwType" + index).val();
        //开始时间
        var sceneMxBeginDate = $("#sceneMxBeginDate" + index).val();
        //结束时间
        var sceneMxEndDate = $("#sceneMxEndDate" + index).val();
        //出差地点
        var sceneMxPlace = $("#sceneMxPlace" + index).val();
        //费用
        var sceneMxSumTotal = $("#sceneMxSumTotal" + index).val();
        //天
        var sceneMxDay = $("#sceneMxDay" + index).val();
        //
        var sceneMxZsfTotal = $("#sceneMxZsfTotal" + index).val();
        //
        var sceneMxZsfSeTotal = $("#sceneMxZsfSeTotal" + index).val();
        //
        var sceneMxSwjtfTotal = $("#sceneMxSwjtfTotal" + index).val();
        //
        var sceneMxSnjtfTotal = $("#sceneMxSnjtfTotal" + index).val();
        //
        var sceneMxCcjbfTotal = $("#sceneMxCcjbfTotal" + index).val();
        //
        var sceneMxCcbtTotal = $("#sceneMxCcbtTotal" + index).val();
        //
        var sceneMxCcbtTotal = $("#sceneMxCcbtTotal" + index).val();
        //
        var sceneMxQtTotal = $("#sceneMxQtTotal" + index).val();
        //
        var sceneMxQtseTotal = $("#sceneMxQtseTotal" + index).val();
        //
        var sceneMxSumTotal = $("#sceneMxSumTotal" + index).val();
        //
        var sceneMxSumTotal = $("#sceneMxSumTotal" + index).val();
        //
        var sceneMxUserCode = $("#sceneMxUserCode" + index).val();

        var mode = $("#type").val();
        var type = "checkFymx";
        if (mode == 'carry' || mode == 'returnFZR') {
            type = mode;
        }
        rows = index;
        var url = ctx + "zzfy/scene/FymxAdd?type=" + type + "&sceneMxUserName=" + sceneMxUserName + "&sceneMxGwName=" + sceneMxGwName + "&sceneMxGwType=" + sceneMxGwType + "&sceneMxBeginDate=" + sceneMxBeginDate + "&sceneMxEndDate=" + sceneMxEndDate + "&sceneMxPlace=" + sceneMxPlace + "&sceneMxDay=" + sceneMxDay + "&sceneMxZsfTotal=" + sceneMxZsfTotal + "&sceneMxZsfSeTotal=" + sceneMxZsfSeTotal + "&sceneMxSwjtfTotal=" + sceneMxSwjtfTotal + "&sceneMxSnjtfTotal=" + sceneMxSnjtfTotal + "&sceneMxCcjbfTotal=" + sceneMxCcjbfTotal + "&sceneMxCcbtTotal=" + sceneMxCcbtTotal + "&sceneMxQtTotal=" + sceneMxQtTotal + "&sceneMxQtseTotal=" + sceneMxQtseTotal + "&sceneMxSumTotal=" + sceneMxSumTotal + "&userCode=" + sceneMxUserCode;
        if (mode == 'carry' || mode == 'returnFZR') {
            var options = {
                title: '报销明细',
                width: "1000",
                height: '600',
                url: url,
                callBack: updateChoice
            };
        } else {
            var options = {
                title: '报销明细',
                width: "1000",
                height: '600',
                url: url,
                callBack: closureChoice
            };
        }
        $.modal.openOptions(options);
    }

    function choiceLegalNoCallback(index, layero) {
        //报支金额
        var sceneBzTotal = $("#sceneBzTotal").val();
        if (sceneBzTotal == '' || sceneBzTotal == null) {
            sceneBzTotal = 0;
        }
        //税额总计
        var sceneSeTotal = $("#sceneSeTotal").val();
        if (sceneSeTotal == '' || sceneSeTotal == null) {
            sceneSeTotal = 0;
        }
        //费用总计
        var sceneFyTotal = $("#sceneFyTotal").val();
        if (sceneFyTotal == '' || sceneFyTotal == null) {
            sceneFyTotal = 0;
        }
        //统结金额
        var sceneTjTotal = $("#sceneTjTotal").val();
        if (sceneTjTotal == '' || sceneTjTotal == null) {
            sceneTjTotal = 0;
        }

        var body = layer.getChildFrame('body', index);
        //姓名
        var userName = body.find('#userName').val();
        //工号
        var userCode = body.find('#userCode').val();
        if (userCode == '' || userCode == null) {
            $.modal.alertError("支撑人员不能为空!");
            return;
        }
        //岗位名称
        var sceneMxGwName = body.find('#sceneMxGwName').val();
        if (sceneMxGwName == '' || sceneMxGwName == null) {
            $.modal.alertError("岗位名称不能为空!");
            return;
        }
        //岗位类型
        var sceneMxGwType = body.find('#sceneMxGwType').val();
        if (sceneMxGwType == '' || sceneMxGwType == null) {
            $.modal.alertError("岗位类型不能为空!");
            return;
        }
        //起始日期
        var sceneMxBeginDate = body.find('#sceneMxBeginDate').val();
        if (sceneMxBeginDate == '' || sceneMxBeginDate == null) {
            $.modal.alertError("起始日期不能为空!");
            return;
        }
        //结束日期
        var sceneMxEndDate = body.find('#sceneMxEndDate').val();
        if (sceneMxEndDate == '' || sceneMxEndDate == null) {
            $.modal.alertError("结束日期不能为空!");
            return;
        }
        //出差天数
        var sceneMxDay = body.find('#sceneMxDay').val();
        //出差地点
        var sceneMxPlace = body.find('#sceneMxPlace').val();
        if (sceneMxPlace == '' || sceneMxPlace == null) {
            $.modal.alertError("出差地点不能为空!");
            return;
        }
        //住宿金额
        var sceneMxZsfTotal = body.find('#sceneMxZsfTotal').val();
        //住宿费税额
        var sceneMxZsfSeTotal = body.find('#sceneMxZsfSeTotal').val();
        //市外交通费
        var sceneMxSwjtfTotal = body.find('#sceneMxSwjtfTotal').val();
        //室内交通费
        var sceneMxSnjtfTotal = body.find('#sceneMxSnjtfTotal').val();
        //车船机票
        var sceneMxCcjbfTotal = body.find('#sceneMxCcjbfTotal').val();
        //出差补贴
        var sceneMxCcbtTotal = body.find('#sceneMxCcbtTotal').val();
        //其它金额
        var sceneMxQtTotal = body.find('#sceneMxQtTotal').val();
        //其它税额
        var sceneMxQtseTotal = body.find('#sceneMxQtseTotal').val();
        //费用
        var sceneMxSumTotal = (+sceneMxZsfTotal) + (+sceneMxZsfSeTotal) + (+sceneMxSwjtfTotal) + (+sceneMxSnjtfTotal) + (+sceneMxCcjbfTotal) + (+sceneMxCcbtTotal) + (+sceneMxQtTotal) + (+sceneMxQtseTotal);
        //税额
        var payTaxes = (+sceneMxZsfSeTotal) + (+sceneMxQtseTotal);
        //报支金额
        var amountReported = (+sceneMxZsfTotal) + (+sceneMxSwjtfTotal) + (+sceneMxSnjtfTotal) + (+sceneMxCcjbfTotal) + (+sceneMxCcbtTotal) + (+sceneMxQtTotal);
        //合计
        var sceneMxSumTotal = (+payTaxes) + (+amountReported);

        var row = {
            sceneMxUserName: userName,
            sceneMxGwName: sceneMxGwName,
            sceneMxGwType: sceneMxGwType,
            sceneMxBeginDate: sceneMxBeginDate,
            sceneMxEndDate: sceneMxEndDate,
            sceneMxPlace: sceneMxPlace,
            sceneMxSumTotal: sceneMxSumTotal,
            operate: "",
            sceneMxDay: sceneMxDay,
            sceneMxZsfTotal: sceneMxZsfTotal,
            sceneMxZsfSeTotal: sceneMxZsfSeTotal,
            sceneMxSwjtfTotal: sceneMxSwjtfTotal,
            sceneMxSnjtfTotal: sceneMxSnjtfTotal,
            sceneMxCcjbfTotal: sceneMxCcjbfTotal,
            sceneMxCcbtTotal: sceneMxCcbtTotal,
            sceneMxQtTotal: sceneMxQtTotal,
            sceneMxQtseTotal: sceneMxQtseTotal,
            sceneMxSumTotal: sceneMxSumTotal,
            sceneMxUserCode: userCode,
            Taxes: payTaxes,
            Reported: amountReported,
            SumTotal: sceneMxSumTotal,
        }
        sub.addColumn(row, "bootstrap-table-applyMembers");
        layer.close(index);

        //计算税额
        sceneSeTotal = (+sceneSeTotal) + (+payTaxes)
        document.getElementById("sceneSeTotal").value = sceneSeTotal;
        //费用总计 报支 + 统结
        sceneFyTotal = (+amountReported) + (+payTaxes) + (+sceneBzTotal) + (+sceneTjTotal);
        document.getElementById("sceneFyTotal").value = sceneFyTotal;
        //计算报支金额
        sceneBzTotal = (+sceneBzTotal) + (+amountReported) + (+payTaxes);
        document.getElementById("sceneBzTotal").value = sceneBzTotal;

    }

    function closureChoice(index, layero) {
        //关闭页面
        layer.close(index);
    }

    function updateChoice(index, layero) {
        //当前行费用
        var SumTotal = $("#sceneMxSumTotal" + rows).val();
        if (SumTotal == '' || SumTotal == null) {
            SumTotal = 0;
        }
        //其它税额
        var QtseTotal = $("#sceneMxQtseTotal" + rows).val();
        if (QtseTotal == '' || QtseTotal == null) {
            QtseTotal = 0;
        }
        //住宿税额
        var ZsfSeTotal = $("#sceneMxZsfSeTotal" + rows).val();
        if (ZsfSeTotal == '' || ZsfSeTotal == null) {
            ZsfSeTotal = 0;
        }
        //当前行报支金额
        var bz = (+SumTotal);// - (+QtseTotal) - (+ZsfSeTotal);

        //报支金额
        var sceneBzTotal = $("#sceneBzTotal").val();
        if (sceneBzTotal == '' || sceneBzTotal == null) {
            sceneBzTotal = 0;
        } else {
            sceneBzTotal = (+sceneBzTotal) - (+bz);
        }

        //税额总计
        var sceneSeTotal = $("#sceneSeTotal").val();
        if (sceneSeTotal == '' || sceneSeTotal == null) {
            sceneSeTotal = 0;
        } else {
            sceneSeTotal = (+sceneSeTotal) - (+QtseTotal) - (+ZsfSeTotal);
        }

        //费用总计
        var sceneFyTotal = $("#sceneFyTotal").val();
        if (sceneFyTotal == '' || sceneFyTotal == null) {
            sceneFyTotal = 0;
        } else {
            sceneFyTotal = (+sceneFyTotal) - (+bz);
        }

        //统结金额
        var sceneTjTotal = $("#sceneTjTotal").val();
        if (sceneTjTotal == '' || sceneTjTotal == null) {
            sceneTjTotal = 0;
        }

        var body = layer.getChildFrame('body', index);
        //姓名
        document.getElementById("sceneMxUserName" + rows).value = body.find('#userName').val();
        //工号
        document.getElementById("sceneMxUserCode" + rows).value = body.find('#userCode').val();
        //岗位名称
        document.getElementById("sceneMxGwName" + rows).value = body.find('#sceneMxGwName').val();
        //岗位类型
        document.getElementById("sceneMxGwType" + rows).value = body.find('#sceneMxGwType').val();
        //起始日期
        document.getElementById("sceneMxBeginDate" + rows).value = body.find('#sceneMxBeginDate').val();
        //结束日期
        document.getElementById("sceneMxEndDate" + rows).value = body.find('#sceneMxEndDate').val();
        //出差天数
        document.getElementById("sceneMxDay" + rows).value = body.find('#sceneMxDay').val();
        //出差地点
        document.getElementById("sceneMxPlace" + rows).value = body.find('#sceneMxPlace').val();
        //住宿金额
        var sceneMxZsfTotal = body.find('#sceneMxZsfTotal').val();
        document.getElementById("sceneMxZsfTotal" + rows).value = body.find('#sceneMxZsfTotal').val();
        //住宿费税额
        var sceneMxZsfSeTotal = body.find('#sceneMxZsfSeTotal').val();
        document.getElementById("sceneMxZsfSeTotal" + rows).value = body.find('#sceneMxZsfSeTotal').val();
        //市外交通费
        var sceneMxSwjtfTotal = body.find('#sceneMxSwjtfTotal').val();
        document.getElementById("sceneMxSwjtfTotal" + rows).value = body.find('#sceneMxSwjtfTotal').val();
        //室内交通费
        var sceneMxSnjtfTotal = body.find('#sceneMxSnjtfTotal').val();
        document.getElementById("sceneMxSnjtfTotal" + rows).value = body.find('#sceneMxSnjtfTotal').val();
        //车船机票
        var sceneMxCcjbfTotal = body.find('#sceneMxCcjbfTotal').val();
        document.getElementById("sceneMxCcjbfTotal" + rows).value = body.find('#sceneMxCcjbfTotal').val();
        //出差补贴
        var sceneMxCcbtTotal = body.find('#sceneMxCcbtTotal').val();
        document.getElementById("sceneMxCcbtTotal" + rows).value = body.find('#sceneMxCcbtTotal').val();
        //其它金额
        var sceneMxQtTotal = body.find('#sceneMxQtTotal').val();
        document.getElementById("sceneMxQtTotal" + rows).value = body.find('#sceneMxQtTotal').val();
        //其它税额
        var sceneMxQtseTotal = body.find('#sceneMxQtseTotal').val();
        document.getElementById("sceneMxQtseTotal" + rows).value = body.find('#sceneMxQtseTotal').val();
        //税额
        var payTaxes = (+sceneMxZsfSeTotal) + (+sceneMxQtseTotal);
        //报支金额
        var amountReported = (+sceneMxZsfTotal) + (+sceneMxSwjtfTotal) + (+sceneMxSnjtfTotal) + (+sceneMxCcjbfTotal) + (+sceneMxCcbtTotal) + (+sceneMxQtTotal);
        //合计
        var sceneMxSumTotal = (+payTaxes) + (+amountReported);
        document.getElementById("sceneMxSumTotal" + rows).value = body.find('#sceneMxSumTotal').val();

        //费用总计 报支 + 统结
        sceneFyTotal = (+sceneMxSumTotal) + (+sceneBzTotal) + (+sceneTjTotal);
        document.getElementById("sceneFyTotal").value = sceneFyTotal;
        //计算报支金额
        sceneBzTotal = (+sceneBzTotal) + (+sceneMxSumTotal);
        document.getElementById("sceneBzTotal").value = sceneBzTotal;
        //计算税额
        sceneSeTotal = (+sceneSeTotal) + (+payTaxes)
        document.getElementById("sceneSeTotal").value = sceneSeTotal;

        //关闭页面
        layer.close(index);
    }


    //删除行数据
    function delcolumn(column) {
        sub.editColumn();
        var subColumn = $.common.isEmpty(column) ? "index" : column;
        var ids = $.table.selectColumns(subColumn);
        if (ids.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        $("#" + table.options.id).bootstrapTable('remove', {field: subColumn, values: ids});

        var bzje = 0;
        var sezj = 0;
        var idsize = ids.toString().split(",");
        //总行数
        var tabtr = $("#bootstrap-table-applyMembers").find("tr").length;
        for (var i = 0; i < tabtr - 1; i++) {
            var id = i;
            var reg = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
            //住宿
            var sceneMxZsfTotal = $("#sceneMxZsfTotal" + id).val();
            console.log(sceneMxZsfTotal);
            if (reg.test(sceneMxZsfTotal)) {
                bzje = (+bzje) + (+sceneMxZsfTotal);
            }
            //市外交通
            var reg1 = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
            var sceneMxSwjtfTotal = $("#sceneMxSwjtfTotal" + id).val();
            if (reg1.test(sceneMxSwjtfTotal)) {
                bzje = (+bzje) + (+sceneMxSwjtfTotal);
            }
            //市内交通
            var reg2 = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
            var sceneMxSnjtfTotal = $("#sceneMxSnjtfTotal" + id).val();
            if (reg2.test(sceneMxSnjtfTotal)) {
                bzje = (+bzje) + (+sceneMxSnjtfTotal);
            }
            //车船机票
            var reg3 = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
            var sceneMxCcjbfTotal = $("#sceneMxCcjbfTotal" + id).val();
            if (reg3.test(sceneMxCcjbfTotal)) {
                bzje = (+bzje) + (+sceneMxCcjbfTotal);
            }
            //出差补贴
            var reg4 = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
            var sceneMxCcbtTotal = $("#sceneMxCcbtTotal" + id).val();
            if (reg4.test(sceneMxCcbtTotal)) {
                bzje = (+bzje) + (+sceneMxCcbtTotal);
            }
            //其它金额
            var reg5 = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
            var sceneMxQtTotal = $("#sceneMxQtTotal" + id).val();
            if (reg5.test(sceneMxQtTotal)) {
                bzje = (+bzje) + (+sceneMxQtTotal);
            }

            //税额
            var reg6 = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
            var sceneMxZsfSeTotal = $("#sceneMxZsfSeTotal" + id).val();
            if (reg6.test(sceneMxQtTotal)) {
                sezj = (+sezj) + (+sceneMxZsfSeTotal);
            }
            var reg7 = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
            var sceneMxQtseTotal = $("#sceneMxQtseTotal" + id).val();
            if (reg7.test(sceneMxQtseTotal)) {
                sezj = (+sezj) + (+sceneMxQtseTotal);
            }
        }
        var fy = (+bzje);
        document.getElementById("sceneBzTotal").value = bzje;
        document.getElementById("sceneSeTotal").value = sezj;
        document.getElementById("sceneFyTotal").value = fy;
    }

    $(function () {
        var hideColumn = $("#hideColumn").val();
        var projectArea = $("#projectArea").val();
        var type = $("#type").val();
        // var div = document.querySelectorAll('xgfjZzfyGf');
        // $("#xgfjZzfyGf").prop("see",'false');
        // $("#xgfjZzfyGf").remove("see");
        // div.removeAttribute('see');
        // document.getElementsByClassName('form-group').removeAttribute('see');

        /*if (projectArea == 'jtn') {
            $("#jtn").show();
        } else {
            $("#jtn").hide();
        }*/

        if (hideColumn == 'Y') {
            $("#srzj").hide();
            $("#bzjr").hide();
        }
        if (type == 'active') {
            $("#tjje").hide();//统结金额 项目主管输入
            $("#director").hide();//报支单和付款日期 项目主管输入
            $("#btnHandler").hide();//提交按钮
            $("#btn").hide();//表格按钮
            // btnSave
            //申请日期
            document.getElementById("sceneDate").disabled = "disabled";
            //现场支撑工作内容
            document.getElementById("extra1").disabled = "disabled";
            //发票张数
            document.getElementById("sceneCount").disabled = "disabled";
        } else if (type == 'activeTGF') {
            //推广方项目主管
            $("#tjje").show();//统结金额 项目主管输入
            $("#director").show();//报支单和付款日期 项目主管输入
            $("#btn").hide();//表格按钮
            //申请日期
            document.getElementById("sceneDate").disabled = "disabled";
            //现场支撑工作内容
            document.getElementById("extra1").disabled = "disabled";
            //发票张数
            document.getElementById("sceneCount").disabled = "disabled";

        } else if (type == 'returnFZR') {
            //退回到第一步
            //推广方项目主管
            $("#tjje").hide();//统结金额 项目主管输入
            $("#director").hide();//报支单和付款日期 项目主管输入
            $("#btnHandler").hide();//提交按钮
            document.getElementById("comment").disabled = "disabled";
        } else if (type == 'carry') {
            $("#tjje").hide();//统结金额 项目主管输入
            $("#director").hide();//报支单和付款日期 项目主管输入
            $("#btnSave").hide();//提交按钮
        }
    })


    /** 提交 */
    function submitSave(transitionKey) {
        $('#transitionKey').val(transitionKey);
        var type = $("#type").val();
        if (type == 'activeTGF') {
            var sceneBzdh = $("#sceneBzdh").val();
            var sceneFkDate = $("#sceneFkDate").val();
            if (sceneBzdh == null || sceneBzdh == '') {
                $.modal.msg("请填写报支单号！");
                return;
            }
            if (sceneFkDate == null || sceneFkDate == '') {
                $.modal.msg("请填写付款日期！");
                return;
            }
        }
        if ($.validate.form()) {
            $.operate.saveTabAlert(prefix + "/finish", $('#form-scene-add').serialize());
        }
    }

    /** 退回 */
    function submitReturn(activityKey) {
        $('#activityKey').val(activityKey);
        $("#opinions").show();
        var comment = $("#comment").val();
        if (comment == null || comment == "") {
            $.modal.msg("请填写退回意见！");
        } else {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/returnXczc", $('#form-scene-add').serialize());
            }
        }
    }

    /** 暂存*/
    /* function temporarily() {
         if ($.validate.form()) {
             $.operate.saveTabAlert(prefix + "/temporarySave", $('#form-scene-add').serialize());
         }
     }*/

    //暂存
    function temporarily() {
        var config = {
            url: prefix + "/temporarySave",
            type: "post",
            dataType: "json",
            data: $('#form-scene-add').serialize(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                $("#sceneId").val(result.data.sceneId);
                $.modal.alertSuccess(result.msg);
                $.modal.closeLoading();
            }
        };
        $.ajax(config)
    }

    /** 费用总计*/
    function checkIsNumbers(id) {
        var value = $("#" + id).val();
        var reg = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
        if (!reg.test(value)) { //正则验证是否为数字
            $.modal.alertError("当前输入的数字不符合规范，请检查！");
            $("#" + id).val(null);
            var sceneBzTotal = $("#sceneBzTotal").val();
            document.getElementById("sceneFyTotal").value = sceneBzTotal;
            return;
        } else {
            //费用总计 报支 + 统结
            var sceneBzTotal = $("#sceneBzTotal").val();
            var sceneFyTotal = (+value) + (+sceneBzTotal);
            document.getElementById("sceneFyTotal").value = sceneFyTotal;
        }
    }

    //检查输入数据是否为数字
    function checkIsNumber(id) {
        var value = $("#" + id).val();
        var reg = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
        if (!reg.test(value)) { //正则验证是否为数字
            $.modal.alertError("当前输入的数字不符合规范，请检查！");
            $("#" + id).val(null);
            return;
        }
    }

    //流程跟踪
    function openProcessTrack(processInstanceId) {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processInstanceId);
    }

    var data = [[${@maintain.getPostType()}]];

    // 数据字典转下拉框
    function dictToSelect(datas, value, name, id) {
        var actions = [];
        actions.push($.common.sprintf("<select id='%s' class='form-control' name='%s' readonly='readonly' style='pointer-events: none;'>", id, name));
        $.each(datas, function (index, dict) {//下标，值
            actions.push($.common.sprintf("<option value='%s'", dict.dictCode));
            if (dict.dictCode == ('' + value)) {
                actions.push(' selected');
            }
            actions.push($.common.sprintf(">%s</option>", dict.dictName));
        });
        actions.push('</select>');
        return actions.join('');
    }

    function detailMain(mainId) {
        $.modal.openTab("项目详细信息", prefix + "/detailMain?mainId=" + mainId);
    }

    function delScene(){
        var sceneId = $("#sceneId").val();
        $.modal.confirm("确认要删除吗？", function() {
            if(sceneId){
                var config = {
                    url: prefix + "/delScene/"+sceneId,
                    type: "post",
                    dataType: "json",
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.modal.alertSuccess(result.msg);
                        $.modal.closeLoading();
                        location.reload();
                        closeItem();
                    }
                };
                $.ajax(config)
            }else{
                closeItem();
            }
        })
    }

</script>
</body>
</html>