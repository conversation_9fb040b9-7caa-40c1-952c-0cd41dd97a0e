<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('查看远程支撑报备表')"/>

    <th:block th:include="include :: baseJs"/>
    <script th:src="@{/js/jquery.tmpl.js}"></script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-scene-add" th:object="${TzzfyLongEx}">
        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel panel-default"
                     aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目编号： </label>
                                    <input id="longId" class="form-control" type="hidden"
                                           th:field="*{longId}">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.projectNum}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目名称： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.projectName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">受让单位部门： </label>
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.srfDwdeptName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">推广单位部门： </label>
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.tgfDwdeptName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目负责人： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.tgfFzrName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">联系电话： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.srfTel}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">受让方负责人： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.srfFzrName}"></div>
                                </div>
                            </div>

                            <div class="col-sm-6">
                                <label class="col-sm-3 control-label ">编制日期： </label>
                                <div class="col-sm-9 form-control-static" th:utext="*{longDate}"></div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <span>点击<a href="#" style="color: blue" th:onclick="detailMain([[*{tzzlxMain.mainId}]])">此处</a>查看项目详细信息</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group" id="jtn">
                            <label class="col-sm-2 control-label">纸质确认单附件：</label>
                            <div class="col-sm-10">
                                <div th:include="include :: layui-upload(display='none',name=*{zzzcConstants.XGFJ_ZZFYJT},id=*{zzzcConstants.XGFJ_ZZFYJT},sourceId=*{longId},sourceModule=*{zzzcConstants.BUSINESS_TYPE},see=true)"></div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">人工费预算： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{laborBudget}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">已报人工费： </label>
                                    <div class="col-sm-9 form-control-static" th:utext="*{alreadyMoney}"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#2"
                                               aria-expanded="false"
                                               class="collapsed">远程支撑人员投入报备：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down"
                                                    aria-hidden="true"></i></span></a></h4>
                </div>
                <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="col-sm-12  table-striped">
                            <div class="bootstrap-table">
                                <div class="fixed-table-container" style="padding-bottom: 0px;">
                                    <div class="fixed-table-body">
                                        <table id="bootstrap-table-applyMembers"
                                               class="table table-hover">
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" id='xgfj'>
                        <label class="col-sm-2 control-label">上传远程支撑报告附件：</label>
                        <div class="col-sm-10">
                            <div th:include="include :: layui-upload(display='none',name=*{zzzcConstants.XGFJ_FILE},id=*{zzzcConstants.XGFJ_FILE},sourceId=*{longId},sourceModule=*{zzzcConstants.BUSINESS_TYPE},see=true)"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#3"
                                           aria-expanded="false"
                                           class="collapsed">其它费用报备：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                aria-hidden="true"></i></span></a></h4>
            </div>
            <div id="3" class="panel-collapse collapse in" aria-expanded="false">
                <div class="panel-body">
                    <!--内容-->
                    <div class="col-sm-12  table-striped">
                        <div class="bootstrap-table">
                            <div class="fixed-table-container" style="padding-bottom: 0px;">
                                <div class="fixed-table-body">
                                    <table id="bootstrap-table-other"
                                           class="table table-hover">
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row ">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-sm-3 control-label  p-title">合计：</label>
                            <div class="col-sm-9 form-control-static" th:utext="*{longQtFyhj}"></div>
                        </div>
                    </div>
                </div>
                <div class="row ">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-sm-6 control-label  p-title">备注：其他费用包括检测费和测试费</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <th:block th:include="component/wfCommentList3 :: init(businessId=*{longId})" />
    </form>

</div>

<div style="height: 30px"></div>


<div class="row">
    <div class="col-sm-offset-5 col-sm-10" id="btnHandler" th:if="${#strings.isEmpty(processInstanceId)}">
        <div class="toolbar toolbar-bottom">
            <button type="button" class="btn btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返 回
            </button>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzfy/long";
    var postData = [[${@maintain.getPostType()}]];

    $("#form-scene-add").validate({
        focusCleanup: true
    });


    $(function () {
        /** 远程支撑人员投入报备*/
        var longId = $("#longId").val();

        var longMxUrl = "";
        var qtfyMxUrl = "";
        if (longId != '') {
            longMxUrl = prefix + "/queryLongMx/" + longId;
            qtfyMxUrl = prefix + "/queryQtfyMx/" + longId;
        }
        var options = {
            url: longMxUrl,
            id: "bootstrap-table-applyMembers",
            //modalName: "远程支撑人员投入报备",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },{
                    field: 'longMxUserCode',
                    align: 'visible',
                    switchable :false,
                },
                {
                    field: 'longMxUserName',
                    align: 'center',
                    title: '支撑人员',
                },
                {
                    field: 'longMxGwmc',
                    align: 'center',
                    title: '岗位名称',
                },
                {
                    field: 'longMxGwtype',
                    align: 'center',
                    title: '岗位类型',
                    formatter:function (value,row,index) {
                        return  $.table.selectDictLabel(postData,value,"dictCode","dictName")
                    }
                },
                {
                    field: 'longMxBeginDate',
                    align: 'center',
                    title: '开始日期',
                },
                {
                    field: 'longMxEndDate',
                    align: 'center',
                    title: '结束日期',
                },
                {
                    field: 'longMxDay',
                    align: 'center',
                    title: '单位:天',
                },
                {
                    field: 'longMxContent',
                    align: 'center',
                    title: '远程支撑工作内容',
                }]
        }
        $.table.init(options);

        //其它费用报备
        var option = {
            url: qtfyMxUrl,
            id: "bootstrap-table-other",
            //modalName: "其它费用报备",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'qtfyMxContent',
                    align: 'center',
                    title: "内容",
                },
                {
                    field: 'qtfyMxDate',
                    align: 'center',
                    title: '日期',
                },
                {
                    field: 'qtfyMxPrice',
                    align: 'center',
                    title: '单价（元）',
                },
                {
                    field: 'qtfyMxCount',
                    align: 'center',
                    title: '数量',
                },
                {
                    field: 'qtfyMxFyprice',
                    align: 'center',
                    title: '费用（元）',
                },
                {
                    field: 'qtfyMxRemark',
                    align: 'center',
                    title: '备注',
                }]
        }
        $.table.init(option);
    });

    function detailMain(mainId) {
        $.modal.openTab("项目详细信息", prefix + "/detailMain?mainId="+mainId);
    }
</script>
</body>
</html>