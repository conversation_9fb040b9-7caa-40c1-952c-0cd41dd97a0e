<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('项目列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>项目编号：</label>
                                <input type="text" name="projectCode"/>
                            </li>
                            <li>
                                <label>项目名称：</label>
                                <input type="text" name="projectName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>


            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzfy/long";
        //var changeprefix = ctx + "zzgc/teachChangetask";

        $(function() {
            var options = {
                url: prefix + "/queryZzlx",
                singleSelect:true,
                queryParams:queryParams,
                modalName: "项目列表",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'mainId',
                    title: '主键',
                    visible: false
                },
                    {
                        field: 'projectNum',
                        title: '项目编号'
                    },
                    {
                        field: 'projectName',
                        title: '项目名称'
                    },
                    {
                        field: 'tgfFzrCode',
                        title: '项目负责人工号'
                    },
                    {
                        field: 'tgfFzrName',
                        title: '项目负责人姓名'
                    },
                    {
                        field: 'srfDeptName',
                        title: '受让单位部门'
                    },
                    {
                        field: 'tgfDeptName',
                        title: '推广单位部门'
                    }]
            };
            $.table.init(options);
        });

        function queryParams(params){
            var search = $.table.queryParams(params);
            search.tgfFzrCode=[[${T(com.baosight.iplat4j.core.web.threadlocal.UserSession).getLoginName()}]];
            //search.projectStatusLi="03,04";
            return search;
        }

        function submitHandler(){
            var selectColumns = $.table.selectColumns("mainId");
            var mainOrContact = $.table.selectColumns("mainOrContact");
            if (selectColumns.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            parent.$.modal.openTab("远程支撑报告申请",prefix + "/addlongLoad/"+selectColumns+"/"+mainOrContact);
            $.modal.close();
        }

    </script>
</body>
</html>