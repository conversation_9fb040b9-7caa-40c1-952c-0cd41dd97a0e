<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('远程支撑报备表')"/>

    <th:block th:include="include :: baseJs"/>
    <script th:src="@{/js/jquery.tmpl.js}"></script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div th:if="${taskId!=null && taskId!='' }" class="form-group"
         th:include="include :: step(approveKind='ZZZC_YCZCBB',currentNode=${activityCode})"></div>
    <form class="form-horizontal m" id="form-scene-add" th:object="${TzzfyLongEx}">
        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel panel-default"
                     aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目编号： </label>
                                    <input id="taskId" name="taskId" th:value="${taskId}" type="hidden">
                                    <input th:value="${processInstanceId}" id="processInstanceId" type="hidden">
                                    <input id="mainOrContact" name="mainOrContact" th:value="${mainOrContact}"
                                           type="hidden">
                                    <input id="type" th:field="*{type}" type="hidden">
                                    <input id="longId" type="hidden"
                                           th:field="*{longId}">
                                    <input id="bizId" type="hidden"
                                           th:field="*{tzzlxMain.mainId}">
                                    <input id="projectArea" type="hidden"
                                           th:field="*{tzzlxMain.projectArea}">
                                    <input id="otherBudget" th:field="*{otherBudget}" type="hidden">
                                    <input id="otherMoney" th:field="*{otherMoney}" type="hidden">
                                    <input id="projectNum" name="projectNum" onfocus=this.blur()
                                           th:field="*{tzzlxMain.projectName}" type="hidden">
                                    <input id="activityKey" name="activityKey" type="hidden">
                                    <input id="transitionKey" name="transitionKey" type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.projectNum}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目名称： </label>
                                    <input id="projectName" name="projectName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.projectName}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.projectName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">受让单位部门： </label>
                                    <input id="srfDwdeptName" name="srfDwdeptName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfDwdeptName}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.srfDwdeptName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">推广单位部门： </label>
                                    <input id="tgfDwdeptName" name="tgfDwdeptName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfDwdeptName}"
                                           type="hidden">
                                    <input id="tgfXmzgCode" name="tgfXmzgCode" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfXmzgCode}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.tgfDwdeptName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目负责人： </label>
                                    <input id="tgfFzrName" name="tgfFzrName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfFzrName}"
                                           type="hidden">

                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.tgfFzrName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">联系电话： </label>
                                    <input id="srfTel" name="srfTel" onfocus=this.blur() th:field="*{tzzlxMain.srfTel}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.srfTel}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">受让方负责人： </label>
                                    <input id="srfFzrName" name="srfFzrName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfFzrName}"
                                           type="hidden">
                                    <input id="srfFzrCode" name="srfFzrCode" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfFzrCode}"
                                           type="hidden">
                                    <input id="srfXmzgCode" name="srfXmzgCode" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfXmzgCode}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.srfFzrName}"></div>
                                </div>
                            </div>

                            <div class="col-sm-6">
                                <div th:include="include :: initDate(id='longDate', name='longDate',labelName='编制日期：', strValue=*{longDate})"></div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <span>点击<a href="#" style="color: blue"
                                               th:onclick="detailMain([[*{tzzlxMain.mainId}]])">此处</a>查看项目详细信息</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group" id="jtn">
                            <label class="col-sm-2 control-label">纸质确认单附件：</label>
                            <div class="col-sm-10">
                                <div th:include="include :: layui-upload(display='none',name=*{zzzcConstants.XGFJ_ZZFYJT},id=*{zzzcConstants.XGFJ_ZZFYJT},sourceId=*{longId},sourceModule=*{zzzcConstants.BUSINESS_TYPE})"></div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">人工费预算： </label>
                                    <div class="col-sm-9">
                                        <input id="laborBudget" name="laborBudget" onfocus=this.blur()
                                               th:field="*{laborBudget}"
                                               type="hidden">
                                        <div class="col-sm-9 form-control-static" th:utext="*{laborBudget}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">已报人工费： </label>
                                    <div class="col-sm-9">
                                        <input id="alreadyMoney" name="alreadyMoney" onfocus=this.blur()
                                               th:field="*{alreadyMoney}"
                                               type="hidden">
                                        <div class="col-sm-9 form-control-static" th:utext="*{alreadyMoney}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#2"
                                               aria-expanded="false"
                                               class="collapsed">远程支撑人员投入报备：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down"
                                                    aria-hidden="true"></i></span></a></h4>
                </div>
                <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="col-sm-12">
                            <div id="longMx">
                                <button class="btn btn-white btn-sm" onclick="addSupport()" type="button"><i
                                        class="fa fa-plus"> 增加</i></button>
                                <button class="btn btn-white btn-sm" onclick="sub.delColumn()" type="button"><i
                                        class="fa fa-minus"> 删除</i></button>
                            </div>
                            <div class="col-sm-12 select-table ">
                                <table id="bootstrap-table-applyMembers"></table>
                            </div>
                        </div>

                        <!-- <div class="col-sm-12  table-striped">
                             <div class="bootstrap-table">
                                 <div class="fixed-table-container" style="padding-bottom: 0px;">
                                     <div class="fixed-table-body">
                                         <table id="bootstrap-table-applyMembers"
                                                class="table table-hover">
                                         </table>
                                     </div>
                                 </div>
                                 <div class="bs-bars pull-left" id="longMx">
                                     <a class="btn btn-success btn-xs " onclick="addSupport()">
                                         <i class="fa fa-plus"></i> 增加
                                     </a>
                                     <a class="btn btn-success btn-xs " onclick="sub.delColumn()">
                                         <i class="fa fa-remove"></i> 删除
                                     </a>
                                 </div>
                             </div>
                         </div>-->
                    </div>

                    <div class="form-group" id='xgfj'>
                        <label class="col-sm-2 control-label">上传远程支撑报告附件：</label>
                        <div class="col-sm-10">
                            <div th:include="include :: layui-upload(display='none',name=*{zzzcConstants.XGFJ_FILE},id=*{zzzcConstants.XGFJ_FILE},sourceId=*{longId},sourceModule=*{zzzcConstants.BUSINESS_TYPE})"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#3"
                                           aria-expanded="false"
                                           class="collapsed">其它费用报备：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                aria-hidden="true"></i></span></a></h4>
            </div>
            <div id="3" class="panel-collapse collapse in" aria-expanded="false">
                <div class="panel-body">
                    <!--内容-->
                    <div class="col-sm-12">
                        <div id="qtfyMx">
                            <button class="btn btn-white btn-sm" onclick="addOther()" type="button"><i
                                    class="fa fa-plus"> 增加</i></button>
                            <button class="btn btn-white btn-sm" onclick="delcolumn()" type="button"><i
                                    class="fa fa-minus"> 删除</i></button>
                        </div>
                        <div class="col-sm-12 select-table ">
                            <table id="bootstrap-table-other"></table>
                        </div>
                    </div>

                    <!-- <div class="col-sm-12  table-striped">
                         <div class="bootstrap-table">
                             <div class="fixed-table-container" style="padding-bottom: 0px;">
                                 <div class="fixed-table-body">
                                     <table id="bootstrap-table-other"
                                            class="table table-hover">
                                     </table>
                                 </div>
                             </div>
                             <div class="bs-bars pull-left" id="qtfyMx">
                                 <a class="btn btn-success btn-xs " onclick="addOther()">
                                     <i class="fa fa-plus"></i> 增加
                                 </a>
                                 <a class="btn btn-success btn-xs " onclick="delcolumn()">
                                     <i class="fa fa-remove"></i> 删除
                                 </a>
                             </div>
                         </div>
                     </div>-->
                </div>
                <div class="row ">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-sm-3 control-label  p-title">合计：</label>
                            <div class="col-sm-9">
                                <input id="longQtFyhj" name="longQtFyhj" onfocus=this.blur()
                                       th:field="*{longQtFyhj}" disabled class="form-control"
                                       type="text">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row ">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-sm-6 control-label  p-title">备注：其他费用包括检测费和测试费</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div style="height: 10px"></div>
        <!-- 审批-->
        <!--<div class="panel panel-default" th:if="${! #strings.isEmpty(processInstanceId)}"
             th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=${processInstanceId},required='true',freeFlow='false')"></div>-->
    </form>

</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10" id="btnHandler">
        <div class="toolbar toolbar-bottom">
            <button type="button" class="btn btn-primary"
                    onclick="delLong()">
                <i class="fa fa-file-movie-o"></i>&nbsp;删 除
            </button>
            <button type="button" class="btn btn-primary"
                    onclick="temporarily()">
                <i class="fa fa-hdd-o"></i>&nbsp;暂 存
            </button>
            <button type="button" class="btn btn-primary"
                    onclick="submitHandler()">
                <i class="fa fa-check"></i>提 交
            </button>
            <button type="button" class="btn btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返 回
            </button>
        </div>
    </div>

    <div class="col-sm-offset-4 col-sm-10" id="btnSave">
        <div class="toolbar toolbar-bottom">
            <button class="btn btn-xl btn-primary" th:onclick="openProcessTrack([[${processInstanceId}]])"
                    type="button">
                <i class="fa fa-eye"></i>&nbsp;流程跟踪图
            </button>
            <th:block th:if="${! #strings.isEmpty(taskId)}"
                      th:include="/component/wfSubmitOne :: init(taskId=${taskId},callback='submitSave')"></th:block>
            <th:block th:if="${! #strings.isEmpty(taskId)}"
                      th:include="/component/wfReturn :: init(taskId=${taskId},callback=submitReturn)"/>
            <button class="btn btn-xl btn-danger" onclick="closeItem()" type="button">
                <i class="fa fa-reply-all"></i>返 回
            </button>
        </div>
    </div>
</div>
<!-- 审批历史 -->
<div th:if="*{! #strings.isEmpty(processInstanceId)}"
     th:include="include :: includeTaskHistoryList(instanceId=*{processInstanceId})"></div>

<script th:inline="javascript">
    var prefix = ctx + "zzfy/long";
    var data = [[${@maintain.getPostType()}]];

    $("#form-scene-add").validate({
        focusCleanup: true
    });

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTabAlert(prefix + "/startCourse", $('#form-scene-add').serialize());
        }
    }

    $(function () {
        /** 远程支撑人员投入报备*/
        var longId = $("#longId").val();

        var longMxUrl = "";
        var qtfyMxUrl = "";
        if (longId != '') {
            longMxUrl = prefix + "/queryLongMx/" + longId;
            qtfyMxUrl = prefix + "/queryQtfyMx/" + longId;
        }
        var options = {
            url: longMxUrl,
            id: "bootstrap-table-applyMembers",
            //modalName: "远程支撑人员投入报备",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='tzzfyLongMx[%s].longMxId' value='%s'>", index, row.longMxId);
                        columnIndex += $.common.sprintf("<input type='hidden' name='tzzfyLongMx[%s].orderNum' value='%s'>", index, index);
                        return columnIndex + $.table.serialNumber(index);
                    }
                }, {
                    field: 'longMxUserCode',
                    align: 'visible',
                    switchable: false,
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input required id='longMxUserCode%s' class='form-control'  name='tzzfyLongMx[%s].longMxUserCode' value='%s' type='hidden'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'longMxUserName',
                    align: 'center',
                    title: '支撑人员',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input required id='longMxUserName%s' class='form-control'  name='tzzfyLongMx[%s].longMxUserName' value='%s' onclick='chooer(" + index + ")' type=\"text\"\n" +
                            "/> ",
                            index, index, value);
                        return html;
                    }
                },
                {
                    field: 'longMxGwmc',
                    align: 'center',
                    title: '岗位名称',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input required id='longMxGwmc%s' class='form-control' type='text' name='tzzfyLongMx[%s].longMxGwmc' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'longMxGwtype',
                    align: 'center',
                    title: '岗位类型',
                    formatter: function (value, row, index) {
                        var applyMemberName = 'tzzfyLongMx[' + index + '].longMxGwtype';
                        var id = 'longMxGwtype' + index;
                        return dictToSelect(data, value, applyMemberName, id);
                    }
                },
                {
                    field: 'longMxBeginDate',
                    align: 'center',
                    title: '开始日期',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' id='longMxBeginDate%s' required type='date' name='tzzfyLongMx[%s].longMxBeginDate'  value='%s' onchange=\"setDate('longMxBeginDate','%s')\">", index, index, value, index);
                        return html;
                    }
                },
                {
                    field: 'longMxEndDate',
                    align: 'center',
                    title: '结束日期',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' id='longMxEndDate%s' required type='date' name='tzzfyLongMx[%s].longMxEndDate'  value='%s' onchange=\"setDate('longMxEndDate','%s')\">", index, index, value, index);
                        return html;
                    }
                },
                {
                    field: 'longMxDay',
                    align: 'center',
                    title: '单位:天',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input required id='longMxDay%s' class='form-control' type='number' step='0.1' name='tzzfyLongMx[%s].longMxDay' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'longMxContent',
                    align: 'center',
                    title: '远程支撑工作内容',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input required id='longMxContent%s' class='form-control' type='text' name='tzzfyLongMx[%s].longMxContent' value='%s'>", index, index, value);
                        return html;
                    }
                }]
        }
        $.table.init(options);

        //其它费用报备
        var option = {
            url: qtfyMxUrl,
            id: "bootstrap-table-other",
            //modalName: "其它费用报备",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", index);
                        return columnIndex + $.table.serialNumber(index);
                    }
                },
                {
                    field: 'qtfyMxContent',
                    align: 'center',
                    title: "内容",
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input required id='qtfyMxContent%s' class='form-control' type='text' name='tzzfyQtfyMx[%s].qtfyMxContent' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'qtfyMxDate',
                    align: 'center',
                    title: '日期',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' required type='date' name='tzzfyQtfyMx[%s].qtfyMxDate'  value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'qtfyMxPrice',
                    align: 'center',
                    title: '单价（元）',
                    formatter: function (value, row, index) {
                        var id = "qtfyMxPrice" + index;
                        var html = $.common.sprintf("<input required id='qtfyMxPrice%s' onchange=\"checkIsNumber('%s')\" class='form-control' type='text' name='tzzfyQtfyMx[%s].qtfyMxPrice' value='%s'>", index, id, index, value);
                        return html;
                    }
                },
                {
                    field: 'qtfyMxCount',
                    align: 'center',
                    title: '数量',
                    formatter: function (value, row, index) {
                        var id = "qtfyMxCount" + index;
                        var html = $.common.sprintf("<input required id='qtfyMxCount%s' onchange=\"checkIsNumber('%s')\" class='form-control' type='text' name='tzzfyQtfyMx[%s].qtfyMxCount' value='%s'>", index, id, index, value);
                        return html;
                    }
                },
                {
                    field: 'qtfyMxFyprice',
                    align: 'center',
                    title: '费用（元）',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input disabled id='qtfyMxFyprice%s' class='form-control' type='text' name='tzzfyQtfyMx[%s].qtfyMxFyprice' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'qtfyMxRemark',
                    align: 'center',
                    title: '备注',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input required id='qtfyMxRemark%s' class='form-control' type='text' name='tzzfyQtfyMx[%s].qtfyMxRemark' value='%s'>", index, index, value);
                        return html;
                    }
                }]
        }
        $.table.init(option);
    });


    //检查输入数据是否为数字
    function checkIsNumber(id) {
        var value = $("#" + id).val();
        var num = id.replace(/[^0-9]/ig, "");
        var reg = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
        if (!reg.test(value)) { //正则验证是否为数字
            qudateqtfyMxFyprice(id, num)
            $.modal.alertError("当前输入的数字不符合规范，请检查！");
            $("#" + id).val(null);
            return;
        } else {
            //计算价格
            calculateqtfyMxPrice(num);
        }
    }

    //计算价格
    function calculateqtfyMxPrice(num) {
        //单价
        var qtfyMxPrice = $("#qtfyMxPrice" + num).val();
        //数量
        var qtfyMxCount = $("#qtfyMxCount" + num).val();
        var qtfyMxFyprice = 0;
        if (qtfyMxCount != '') {
            qtfyMxFyprice = qtfyMxPrice * qtfyMxCount;
        } else {
            qtfyMxFyprice = qtfyMxPrice;
        }
        //费用
        document.getElementById("qtfyMxFyprice" + num).value = qtfyMxFyprice;
        qudateqtfyMxFyprice();
    }

    //修改行数据，费用
    function qudateqtfyMxFyprice(id, num) {
        var ll = id.replace(/\d+/g, '');
        var qtfyMxFyprice = 0;
        if (ll == 'qtfyMxCount') {
            qtfyMxFyprice = $("#qtfyMxPrice" + num).val();
        }
        //费用
        document.getElementById("qtfyMxFyprice" + num).value = qtfyMxFyprice;
        qudateqtfyMxFyprice();
    }

    //修改合计费用
    function qudateqtfyMxFyprice() {
        //其它费用合计
        var tab = document.getElementById("bootstrap-table-other");
        var rows = tab.rows.length;
        var cost = 0;
        for (var i = 0; i < (+rows) - 1; i++) {
            var longQtFyhj = $("#qtfyMxFyprice" + i).val();
            if (longQtFyhj != null) {
                cost = (+cost) + (+longQtFyhj);
            }
        }
        document.getElementById("longQtFyhj").value = cost;
    }

    //远程支撑人员投入报备
    function addSupport() {
        var row = {
            longMxUserCode: "",
            longMxUserName: "",
            longMxGwmc: "",
            longMxGwtype: "",
            longMxBeginDate: "",
            longMxEndDate: "",
            longMxDay: "",
            longMxContent: ""
        }
        sub.addColumn(row, "bootstrap-table-applyMembers");
    }

    //其它费用报备
    function addOther() {
        var row = {
            qtfyMxContent: "",
            qtfyMxDate: "",
            qtfyMxPrice: "",
            qtfyMxCount: "",
            qtfyMxFyprice: "",
            qtfyMxRemark: ""
        }
        sub.addColumn(row, "bootstrap-table-other");
    }

    $(function () {
        var type = $("#type").val();
        if (type == 'carry') {
            $("#btnHandler").show();//提交
            $("#btnSave").hide();//同意
            $("#opinions").hide();//意见
        } else if (type == 'active') {
            $("#btnHandler").hide();//提交
            $("#btnSave").show();//同意
            $("#opinions").hide();//意见
            $("#longMx").hide();//新增删除远程人员投入
            $("#qtfyMx").hide();//新增删除其它费用报备
            document.getElementById("longDate").disabled = "disabled";//编制日期

        } else if (type == 'return') {
            $("#btnHandler").hide();//提交
            $("#btnSave").show();//同意
            $("#opinions").show();//意见
            $("#longMx").hide();//新增删除远程人员投入
            $("#qtfyMx").hide();//新增删除其它费用报备
            document.getElementById("longDate").disabled = "disabled";//编制日期
        }
        var projectArea = $("#projectArea").val();
        if (projectArea == 'jtn') {
            $("#jtn").show();
        } else if (projectArea == 'gfn') {
            $("#jtn").hide();
        }
    })

    //提交
    function submitSave(transitionKey) {
        $('#transitionKey').val(transitionKey);
        if ($.validate.form()) {
            $.operate.saveTabAlert(prefix + "/finish", $('#form-scene-add').serialize());
        }
    }

    //退回
    function submitReturn(activityKey) {
        $('#activityKey').val(activityKey);
        $("#opinions").show();
        var comment = $("#comment").val();
        if (comment == null || comment == "") {
            $.modal.msg("请填写退回意见！");
        } else {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/returnXczc", $('#form-scene-add').serialize());
            }
        }
    }

    //删除行数据
    function delcolumn() {
        sub.delColumn();
        qudateqtfyMxFyprice();
    }

    // 数据字典转下拉框
    function dictToSelect(datas, value, name, id) {
        var actions = [];
        actions.push($.common.sprintf("<select id='%s' class='form-control' name='%s'>", id, name));
        $.each(datas, function (index, dict) {//下标，值
            actions.push($.common.sprintf("<option value='%s'", dict.dictCode));
            if (dict.dictCode == ('' + value)) {
                actions.push(' selected');
            }
            actions.push($.common.sprintf(">%s</option>", dict.dictName));
        });
        actions.push('</select>');
        return actions.join('');
    }

    function chooer(val) {
        var longMxUserName = "longMxUserName" + val;
        var longMxUserCode = "longMxUserCode" + val;
        choiceUserMM(longMxUserCode, longMxUserName, "S", null, null);
    }

    function choiceUserMM(userCodeInputId, userNameInputId, selectType, orgCode, callback) {
        userId = userCodeInputId;
        userNameId = userNameInputId;
        var url = ctx + 'mpad/user/selectUserList';
        if (selectType === undefined || selectType == null || selectType == '') {
            selectType = "S";
        }
        url += "?selectType=" + selectType;
        if (!(orgCode === undefined) && orgCode != null) {
            url += "&orgCode=" + orgCode;
        }
        var values = $("#" + userCodeInputId).val();
        if (!(values === undefined) && values != null) {
            url += "&values=" + values;
        }
        if (!(callback === undefined) && callback != null) {
            url += "&callback=" + callback;
        }
        url += "&userId=" + $("#" + userId).val() + "&userName=" + $("#" + userNameId).val();
        $.modal.open("选择用户", url, '1000', '500');
    }

    //暂存
    /*function temporarily() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/temporarySave", $('#form-scene-add').serialize());
        }
    }*/

    //暂存
    function temporarily() {
        var config = {
            url: prefix + "/temporarySave",
            type: "post",
            dataType: "json",
            data: $('#form-scene-add').serialize(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                $("#longId").val(result.data.longId);
                $.modal.alertSuccess(result.msg);
                $.modal.closeLoading();
            }
        };
        $.ajax(config)
    }

    //流程跟踪
    function openProcessTrack(processInstanceId) {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processInstanceId);
    }

    function detailMain(mainId) {
        $.modal.openTab("项目详细信息", prefix + "/detailMain?mainId=" + mainId);
    }

    function setDate(id, index) {
        var idDate = $("#" + id + index).val();
        var date_now = new Date(idDate);
        //得到当前年份
        var year = date_now.getFullYear();
        //得到当前月份
        //注：
        //  1：js中获取Date中的month时，会比当前月份少一个月，所以这里需要先加一
        //  2: 判断当前月份是否小于10，如果小于，那么就在月份的前面加一个 '0' ， 如果大于，就显示当前月份
        var month = date_now.getMonth() + 1 < 10 ? "0" + (date_now.getMonth() + 1) : (date_now.getMonth() + 1);
        // 得到当前选择时间
        var date = date_now.getDate() < 10 ? "0" + date_now.getDate() : date_now.getDate();
        if (id == 'longMxBeginDate') {
            $("#longMxEndDate" + index).attr("min", year + "-" + month + "-" + date);
        } else if (id == 'longMxEndDate') {
            $("#longMxBeginDate" + index).attr("max", year + "-" + month + "-" + date);
        }
    }

    function delLong(){
        var longId = $("#longId").val();
        $.modal.confirm("确认要删除吗？", function() {
            if(longId){
                var config = {
                    url: prefix + "/delLong/"+longId,
                    type: "post",
                    dataType: "json",
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.modal.alertSuccess(result.msg);
                        $.modal.closeLoading();
                        closeItem();
                    }
                };
                $.ajax(config)
            }else{
                closeItem();
            }
        })
    }

</script>
</body>
</html>