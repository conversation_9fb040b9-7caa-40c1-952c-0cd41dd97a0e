<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('远程支撑报告申请')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="row form-group">
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">项目编号：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="projectNum" required type="text">
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">项目名称：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="projectName" required type="text">
                        </div>
                    </div>
                </div>

                <div class="row form-group">
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">项目负责人：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="tgfFzrName" required type="text">
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">项目主管：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="tgfXmzgName" required type="text">
                        </div>
                    </div>
                </div>
                <div class="select-list" style="float: right">
                    <ul>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.add()" >
                <i class="fa fa-plus"></i> 添加
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>
</div>
<script th:inline="javascript">
    var prefix = ctx + "zzfy/long";

    $(function () {
        var options = {
            url: prefix + "/queryDraft",
            createUrl: prefix + "/projectList",
            updateUrl: prefix + "/editLong?longId={id}",
            detailUrl: prefix + "/detailLong?longId={id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            sidePagination: "client",
            modalName: "远程支撑申请",
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'projectNum',
                    align: 'center',
                    title: "项目编号",
                },
                {
                    field: 'projectName',
                    align: 'center',
                    title: "项目名称",
                },
                {
                    field: 'tgfFzrName',
                    align: 'center',
                    title: "项目负责人",
                },
                {
                    field: 'tgfXmzgName',
                    align: 'center',
                    title: "项目主管",
                },
                /*{
                    field: 'status',
                    align: 'center',
                    title: "状态",
                },
                {
                    field: 'status',
                    align: 'center',
                    title: "操作人",
                }*/
                {
                    field: 'operate',
                    align: 'center',
                    title: '操作',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.longId + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.longId + '\')"><i class="fa fa-edit"></i>修改</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.longId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        /*actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="Details(\'' + row.mainId + '\',\'' + row.mainOrContact + '\')">申请报支</a> ');*/
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
</script>

</body>
</html>