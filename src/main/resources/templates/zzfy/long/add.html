<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增1.9-01远程支撑报备')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-long-add">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">主键：</label>
                <div class="col-sm-8">
                    <input name="guid" class="form-control" type="text" required>
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">申请人工号：</label>
                <div class="col-sm-8">
                    <input name="longUserCode" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">申请日期：</label>
                <div class="col-sm-8">
                    <input name="longDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">人工费用：</label>
                <div class="col-sm-8">
                    <input name="longPeopleTotal" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">其他费用：</label>
                <div class="col-sm-8">
                    <input name="longQtTotal" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">现场支撑报销结束日期：</label>
                <div class="col-sm-8">
                    <input name="longSubmitDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="extra1" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="extra2" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段3：</label>
                <div class="col-sm-8">
                    <input name="extra3" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段4：</label>
                <div class="col-sm-8">
                    <input name="extra4" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段5：</label>
                <div class="col-sm-8">
                    <input name="extra5" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',businessType=null, dictCode=null ,labelName='删除状态')">
            </div>
                </div>
                    </div>
                </div>
            </div>
        </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>


    <script th:inline="javascript">
        var prefix = ctx + "zzfy/long"

        $("#form-long-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/add", $('#form-long-add').serialize());
            }
        }

    </script>
</body>
</html>