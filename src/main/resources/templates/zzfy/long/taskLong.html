<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('远程支撑报备表')"/>

    <th:block th:include="include :: baseJs"/>
    <script th:src="@{/js/jquery.tmpl.js}"></script>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div th:if="${taskId!=null && taskId!='' }" class="form-group"
         th:include="include :: step(approveKind='ZZZC_YCZCBB',currentNode=${activityCode})"></div>
    <form class="form-horizontal m" id="form-scene-add" th:object="${TzzfyLongEx}">
        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel panel-default"
                     aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目编号： </label>
                                    <input id="taskId" name="taskId" th:value="${taskId}" type="hidden">
                                    <input th:value="${processInstanceId}" id="processInstanceId" type="hidden">
                                    <input id="mainOrContact" name="mainOrContact" th:value="${mainOrContact}"
                                           type="hidden">
                                    <input id="type" th:field="*{type}" type="hidden">
                                    <input id="longId" type="hidden"
                                           th:field="*{longId}">
                                    <input id="bizId" type="hidden"
                                           th:field="*{tzzlxMain.mainId}">
                                    <input id="projectArea" type="hidden"
                                           th:field="*{tzzlxMain.projectArea}">
                                    <input id="otherBudget" th:field="*{otherBudget}" type="hidden">
                                    <input id="otherMoney" th:field="*{otherMoney}" type="hidden">
                                    <input id="projectNum" name="projectNum" onfocus=this.blur()
                                           th:field="*{tzzlxMain.projectName}" type="hidden">
                                    <input id="activityKey" name="activityKey" type="hidden">
                                    <input id="transitionKey" name="transitionKey" type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.projectNum}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目名称： </label>
                                    <input id="projectName" name="projectName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.projectName}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.projectName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">受让单位部门： </label>
                                    <input id="srfDwdeptName" name="srfDwdeptName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfDwdeptName}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.srfDwdeptName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">推广单位部门： </label>
                                    <input id="tgfDwdeptName" name="tgfDwdeptName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfDwdeptName}"
                                           type="hidden">
                                    <input id="tgfXmzgCode" name="tgfXmzgCode" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfXmzgCode}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.tgfDwdeptName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目负责人： </label>
                                    <input id="tgfFzrName" name="tgfFzrName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfFzrName}"
                                           type="hidden">

                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.tgfFzrName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">联系电话： </label>
                                    <input id="srfTel" name="srfTel" onfocus=this.blur() th:field="*{tzzlxMain.srfTel}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.srfTel}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">受让方负责人： </label>
                                    <input id="srfFzrName" name="srfFzrName" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfFzrName}"
                                           type="hidden">
                                    <input id="srfFzrCode" name="srfFzrCode" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfFzrCode}"
                                           type="hidden">
                                    <input id="srfXmzgCode" name="srfXmzgCode" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfXmzgCode}"
                                           type="hidden">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.srfFzrName}"></div>
                                </div>
                            </div>


                            <div class="col-sm-6">
                                <label class="col-sm-3 control-label ">编制日期： </label>
                                <div class="col-sm-9 form-control-static" th:utext="*{longDate}"></div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <span>点击<a href="#" style="color: blue"
                                               th:onclick="detailMain([[*{tzzlxMain.mainId}]])">此处</a>查看项目详细信息</span>
                                </div>
                            </div>
                        </div>

                        <div class="form-group" id="jtn">
                            <label class="col-sm-2 control-label">纸质确认单附件：</label>
                            <div class="col-sm-10">
                                <div th:include="include :: layui-upload(display='none',name=*{zzzcConstants.XGFJ_ZZFYJT},id=*{zzzcConstants.XGFJ_ZZFYJT},sourceId=*{longId},sourceModule=*{zzzcConstants.BUSINESS_TYPE},see=true)"></div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">人工费预算： </label>
                                    <div class="col-sm-9">
                                        <input id="laborBudget" name="laborBudget" onfocus=this.blur()
                                               th:field="*{laborBudget}"
                                               type="hidden">
                                        <div class="col-sm-9 form-control-static" th:utext="*{laborBudget}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">已报人工费： </label>
                                    <div class="col-sm-9">
                                        <input id="alreadyMoney" name="alreadyMoney" onfocus=this.blur()
                                               th:field="*{alreadyMoney}"
                                               type="hidden">
                                        <div class="col-sm-9 form-control-static" th:utext="*{alreadyMoney}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#2"
                                               aria-expanded="false"
                                               class="collapsed">远程支撑人员投入报备：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down"
                                                    aria-hidden="true"></i></span></a></h4>
                </div>
                <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <!--内容-->
                        <div class="col-sm-12">
                            <div class="col-sm-12 select-table ">
                                <table id="bootstrap-table-applyMembers"></table>
                            </div>
                        </div>
                    </div>

                    <div class="form-group" id='xgfj'>
                        <label class="col-sm-2 control-label">上传远程支撑报告附件：</label>
                        <div class="col-sm-10">
                            <div th:include="include :: layui-upload(display='none',name=*{zzzcConstants.XGFJ_FILE},id=*{zzzcConstants.XGFJ_FILE},sourceId=*{longId},sourceModule=*{zzzcConstants.BUSINESS_TYPE},see=true)"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading">
                <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#3"
                                           aria-expanded="false"
                                           class="collapsed">其它费用报备：&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                aria-hidden="true"></i></span></a></h4>
            </div>
            <div id="3" class="panel-collapse collapse in" aria-expanded="false">
                <div class="panel-body">
                    <!--内容-->
                    <div class="col-sm-12">
                        <div class="col-sm-12 select-table ">
                            <table id="bootstrap-table-other"></table>
                        </div>
                    </div>

                </div>
                <div class="row ">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-sm-3 control-label  p-title">合计：</label>
                            <div class="col-sm-9">
                                <input id="longQtFyhj" name="longQtFyhj" onfocus=this.blur()
                                       th:field="*{longQtFyhj}" disabled class="form-control"
                                       type="text">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row ">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="col-sm-6 control-label  p-title">备注：其他费用包括检测费和测试费</label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 审批-->
        <div class="panel panel-default" th:if="${! #strings.isEmpty(processInstanceId)}"
             th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=${processInstanceId},required='true',freeFlow='false')"></div>
        <div class="m">
            <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
        </div>
    </form>

</div>
<div class="row">
    <div class="col-sm-offset-4 col-sm-10" id="btnSave">
        <div class="toolbar toolbar-bottom">
            <button class="btn btn-xl btn-primary" th:onclick="openProcessTrack([[${processInstanceId}]])"
                    type="button">
                <i class="fa fa-eye"></i>&nbsp;流程跟踪图
            </button>
            <th:block th:if="${! #strings.isEmpty(taskId)}"
                      th:include="/component/wfSubmitOne :: init(taskId=${taskId},callback='submitSave')"></th:block>
            <th:block th:if="${! #strings.isEmpty(taskId)}"
                      th:include="/component/wfReturn :: init(taskId=${taskId},callback=submitReturn)"/>
            <button class="btn btn-xl btn-danger" onclick="closeItem()" type="button">
                <i class="fa fa-reply-all"></i>返 回
            </button>
        </div>
    </div>
</div>
<!-- 审批历史 -->
<div th:if="*{! #strings.isEmpty(processInstanceId)}"
     th:include="include :: includeTaskHistoryList(instanceId=*{processInstanceId})"></div>

<script th:inline="javascript">
    var prefix = ctx + "zzfy/long";
    var data = [[${@maintain.getPostType()}]];

    $("#form-scene-add").validate({
        focusCleanup: true
    });

    $(function () {
        /** 远程支撑人员投入报备*/
        var longId = $("#longId").val();

        var longMxUrl = "";
        var qtfyMxUrl = "";
        if (longId != '') {
            longMxUrl = prefix + "/queryLongMx/" + longId;
            qtfyMxUrl = prefix + "/queryQtfyMx/" + longId;
        }
        var options = {
            url: longMxUrl,
            id: "bootstrap-table-applyMembers",
            //modalName: "远程支撑人员投入报备",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='tzzfyLongMx[%s].longMxId' value='%s'>", index, row.longMxId);
                        columnIndex += $.common.sprintf("<input type='hidden' name='tzzfyLongMx[%s].orderNum' value='%s'>", index, index);
                        return columnIndex + $.table.serialNumber(index);
                    }
                }, {
                    field: 'longMxUserCode',
                    align: 'visible',
                    switchable: false,
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input required id='longMxUserCode%s' class='form-control'  name='tzzfyLongMx[%s].longMxUserCode' value='%s' type='hidden'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'longMxUserName',
                    align: 'center',
                    title: '支撑人员',
                },
                {
                    field: 'longMxGwmc',
                    align: 'center',
                    title: '岗位名称',
                },
                {
                    field: 'longMxGwtype',
                    align: 'center',
                    title: '岗位类型',
                    formatter: function (value, row, index) {
                        var applyMemberName = 'tzzfySceneFymx[' + index + '].sceneMxGwType';
                        var id = 'sceneMxGwType' + index;
                        return dictToSelect(data, value, applyMemberName, id);
                    }
                },
                {
                    field: 'longMxBeginDate',
                    align: 'center',
                    title: '开始日期',
                },
                {
                    field: 'longMxEndDate',
                    align: 'center',
                    title: '结束日期',
                },
                {
                    field: 'longMxDay',
                    align: 'center',
                    title: '单位:天',
                },
                {
                    field: 'longMxContent',
                    align: 'center',
                    title: '远程支撑工作内容',
                }]
        }
        $.table.init(options);

        //其它费用报备
        var option = {
            url: qtfyMxUrl,
            id: "bootstrap-table-other",
            //modalName: "其它费用报备",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", index);
                        return columnIndex + $.table.serialNumber(index);
                    }
                },
                {
                    field: 'qtfyMxContent',
                    align: 'center',
                    title: "内容",
                },
                {
                    field: 'qtfyMxDate',
                    align: 'center',
                    title: '日期',
                },
                {
                    field: 'qtfyMxPrice',
                    align: 'center',
                    title: '单价（元）',
                },
                {
                    field: 'qtfyMxCount',
                    align: 'center',
                    title: '数量',
                },
                {
                    field: 'qtfyMxFyprice',
                    align: 'center',
                    title: '费用（元）',
                },
                {
                    field: 'qtfyMxRemark',
                    align: 'center',
                    title: '备注',
                }]
        }
        $.table.init(option);
    });

    $(function () {
        var projectArea = $("#projectArea").val();
        if (projectArea == 'jtn') {
            $("#jtn").show();
        } else if (projectArea == 'gfn') {
            $("#jtn").hide();
        }
    })

    //退回
    function submitReturn(activityKey) {
        $('#activityKey').val(activityKey);
        $("#opinions").show();
        var comment = $("#comment").val();
        if (comment == null || comment == "") {
            $.modal.msg("请填写退回意见！");
        } else {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/returnXczc", $('#form-scene-add').serialize());
            }
        }
    }

    //提交
    function submitSave(transitionKey) {
        $('#transitionKey').val(transitionKey);
        if ($.validate.form()) {
            $.operate.saveTabAlert(prefix + "/finish", $('#form-scene-add').serialize());
        }
    }

    //流程跟踪
    function openProcessTrack(processInstanceId) {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processInstanceId);
    }

    function detailMain(mainId) {
        $.modal.openTab("项目详细信息", prefix + "/detailMain?mainId=" + mainId);
    }

    // 数据字典转下拉框
    function dictToSelect(datas, value, name, id) {
        var actions = [];
        actions.push($.common.sprintf("<select id='%s' class='form-control' name='%s' style='pointer-events: none;'>", id, name));
        $.each(datas, function (index, dict) {//下标，值
            actions.push($.common.sprintf("<option value='%s'", dict.dictCode));
            if (dict.dictCode == ('' + value)) {
                actions.push(' selected');
            }
            actions.push($.common.sprintf(">%s</option>", dict.dictName));
        });
        actions.push('</select>');
        return actions.join('');
    }

</script>
</body>
</html>