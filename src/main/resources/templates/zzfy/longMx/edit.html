<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改1.9-01.01远程支撑人员投入报备')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-longMx-edit" th:object="${longMx}">
         <div class="panel-group" id="accordion" role="tablist"
                             aria-multiselectable="true">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#version"
                                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                                        aria-hidden="true"></i></span>
                                        </a>
                                    </h4>
                                </div>
                                <div id="jbxx" class="panel-collapse collapse in"
                                     aria-expanded="false">
                                    <div class="panel-body">
            <input name="longMxGuid" th:field="*{longMxGuid}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">主键：</label>
                <div class="col-sm-8">
                    <input name="guid" th:field="*{guid}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">排序：</label>
                <div class="col-sm-8">
                    <input name="orderNum" th:field="*{orderNum}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">支撑人员工号：</label>
                <div class="col-sm-8">
                    <input name="longMxUserCode" th:field="*{longMxUserCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">岗位名称：</label>
                <div class="col-sm-8">
                    <input name="longMxGwmc" th:field="*{longMxGwmc}" class="form-control" type="text">
                </div>
            </div>

      <div class="form-group" th:include="include :: initSelectBox(id='longMxGwtype', name='longMxGwtype',businessType=null, dictCode=null, value=${longMx.longMxGwtype} ,labelName='岗位类型')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">开始日期：</label>
                <div class="col-sm-8">
                    <input name="longMxBeginDate" th:field="*{longMxBeginDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">结束日期：</label>
                <div class="col-sm-8">
                    <input name="longMxEndDate" th:field="*{longMxEndDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">单位：</label>
                <div class="col-sm-8">
                    <input name="longMxDay" th:field="*{longMxDay}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">远程支撑内容：</label>
                <div class="col-sm-8">
                    <input name="longMxContent" th:field="*{longMxContent}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段3：</label>
                <div class="col-sm-8">
                    <input name="extra3" th:field="*{extra3}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段4：</label>
                <div class="col-sm-8">
                    <input name="extra4" th:field="*{extra4}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段5：</label>
                <div class="col-sm-8">
                    <input name="extra5" th:field="*{extra5}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',businessType=null, dictCode=null ,value=${longMx.delStatus} ,labelName='删除状态')">
            </div>
</div>
                    </div>
                </div>
            </div>
        </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>

    <script th:inline="javascript">
        var prefix = ctx + "zzfy/longMx";

        $("#form-longMx-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-longMx-edit').serialize());
            }
        }

    </script>
</body>
</html>