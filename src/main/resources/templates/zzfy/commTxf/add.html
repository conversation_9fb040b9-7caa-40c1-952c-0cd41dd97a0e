<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增1.8-01现场支撑通讯费')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-commTxf-add" th:object="${ZzfyCommTxfEx}">
        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>

                <div id="jbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目编号： </label>
                                    <input id="taskId" name="taskId" th:value="${taskId}" type="hidden">
                                    <input th:value="${processInstanceId}" id="processInstanceId" type="hidden">
                                    <input name="commId" id="commId" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{commId}">
                                    <input name="mainId" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{tzzlxMain.mainId}">
                                    <input name="type" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{type}">
                                    <input name="projectNum" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{tzzlxMain.projectNum}">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.projectNum}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目名称： </label>
                                    <input name="projectName" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{tzzlxMain.projectName}">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.projectName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label  p-title">项目类型：</label>
                                    <div class="col-sm-9">
                                        <input name="projectType" class="form-control" type="hidden" onfocus=this.blur()
                                               th:field="*{tzzlxMain.projectType}">
                                        <div class="col-sm-9 form-control-static"
                                             th:utext="*{tzzlxMain.projectType}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">受让单位部门： </label>
                                    <input name="srfDwdeptName" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{tzzlxMain.srfDwdeptName}">
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.srfDeptName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">推广单位部门： </label>
                                    <input name="tgfDwdeptName" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfDwdeptName}">
                                    <input name="tgfXmzgCode" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfXmzgCode}">
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.tgfDeptName}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">项目负责人： </label>
                                    <input name="tgfDwdeptName" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfDwdeptName}">
                                    <div class="col-sm-9 form-control-static"
                                         th:utext="*{tzzlxMain.tgfDwdeptName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">联系电话： </label>
                                    <input name="tgfXmfzrTel" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{tzzlxMain.tgfXmfzrTel}">
                                    <div class="col-sm-9 form-control-static" th:utext="*{tzzlxMain.tgfXmfzrTel}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label  p-title">受让方负责人：</label>
                                    <div class="col-sm-9">
                                        <input name="srfFzrName" class="form-control" type="hidden" onfocus=this.blur()
                                               th:field="*{tzzlxMain.srfFzrName}">
                                        <div class="col-sm-9 form-control-static"
                                             th:utext="*{tzzlxMain.srfFzrName}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">申请人： </label>
                                    <input name="commUserCode" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{commUserCode}">
                                    <input name="commUserName" class="form-control" type="hidden" onfocus=this.blur()
                                           th:field="*{commUserName}">
                                    <div class="col-sm-9 form-control-static" th:utext="*{commUserName}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <th:block
                                            th:include="include :: initDate(id='commDate',name='commDate',labelName='申请日期：',strValue=*{commDate})"></th:block>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label  p-title">通讯费预算（万元）：</label>
                                    <div class="col-sm-9">
                                        <input name="srfFzrName" class="form-control" type="hidden" onfocus=this.blur() th:field="*{txfBudget}">
                                        <div class="col-sm-9 form-control-static" th:utext="*{txfBudget}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">已发生通讯费： </label>
                                    <input class="form-control" type="hidden" onfocus=this.blur() th:field="*{txfHappened}">
                                    <div class="col-sm-9 form-control-static" th:utext="*{txfHappened}"></div>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label ">报支中通讯费（元）： </label>
                                    <input class="form-control" type="hidden" onfocus=this.blur() th:field="*{txfReport}">
                                    <div class="col-sm-9 form-control-static" th:utext="*{txfReport}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <div class="col-sm-12">
                                <span>点击<a href="#" style="color: blue" th:onclick="detailMain([[*{tzzlxMain.mainId}]])">此处</a>查看项目详细信息</span>
                            </div>
                        </div>

                    </div>
                </div>
            </div>

            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#fbbx" aria-expanded="false" class="collapsed">费用报销 <span style="color: #F00">（请于5个工作日内递交报支票据）</span>
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="fbbx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body">


                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <th:block
                                            th:include="include :: initDate(id='commBeginDate',name='commBeginDate',labelName='起始日期：',isrequired=true,strValue=*{commBeginDate})"></th:block>
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-group">
                                    <th:block
                                            th:include="include :: initDate(id='commEndDate',name='commEndDate',labelName='截止日期：',isrequired=true,strValue=*{commEndDate})"></th:block>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label  p-title">通讯费用（元）：</label>
                                    <div class="col-sm-9">
                                        <input name="sceneTsfTotal" id="sceneTsfTotal" class="form-control" type="text" required
                                               th:field="*{sceneTsfTotal}">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-2 control-label">票据（电子文档）及报支单文件附件：</label>
                            <div class="col-sm-10">
                                <div th:include="include :: layui-upload(display='none',name=*{zzzcConstants.XGFJ_FILE},id=*{zzzcConstants.XGFJ_FILE},sourceId=*{commId},sourceModule=*{zzzcConstants.BUSINESS_TYPE})"></div>
                            </div>

                            <div class="row ">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label  p-title">报支发票张数：</label>
                                        <div class="col-sm-9">
                                            <input name="commCount" id="commCount" class="form-control" type="text" required
                                                   th:field="*{commCount}">
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row " id="reimburse">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="col-sm-3 control-label ">报支单号： </label>
                                        <div class="col-sm-9">
                                            <input name="extra1" id="extra1" class="form-control" type="text" th:field="*{extra1}">
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <div th:include="include :: initDate(id='extra3', name='extra3',labelName='付款日期：',strValue=*{extra3})"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 审批-->
            <div class="panel panel-default" th:if="${! #strings.isEmpty(processInstanceId)}" th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=${processInstanceId},required='true',freeFlow='false')"></div>
        </div>
    </form>
</div>
<div class="row" id="btnHandler">
    <div class="col-sm-offset-5 col-sm-10" >
        <button type="button" class="btn  btn-primary"
                onclick="delCommTxf()">
            <i class="fa fa-file-movie-o"></i>&nbsp;删 除
        </button>
        <button type="button" class="btn btn-primary"
                onclick="temporarily()">
            <i class="fa fa-hdd-o"></i>&nbsp;暂 存
        </button>

        <button type="button" class="btn btn-primary"
                onclick="submitHandler()">
            <i class="fa fa-check"></i>提 交
        </button>

        <button type="button" class="btn btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>

<div class="row" id="btnSave">
    <div class="col-sm-offset-4 col-sm-10">
        <button class="btn btn-xl btn-primary" th:onclick="openProcessTrack([[${processInstanceId}]])" type="button">
            <i class="fa fa-eye"></i>&nbsp;流程跟踪图
        </button>
        <button type="button" class="btn  btn-primary"
                onclick="delCommTxf()">
            <i class="fa fa-file-movie-o"></i>&nbsp;删 除
        </button>
        <th:block th:if="${! #strings.isEmpty(taskId)}" th:include="/component/wfSubmit :: init(taskId=${taskId},callback='submitSave')"></th:block>
        <th:block th:if="${! #strings.isEmpty(taskId)}" th:include="/component/wfReturn :: init(taskId=${taskId},callback=submitReturn)"/>
        <button class="btn btn-xl btn-danger" onclick="closeItem()" type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>

<!-- 审批历史 -->
<div th:if="*{! #strings.isEmpty(processInstanceId)}" th:include="include :: includeTaskHistoryList(instanceId=*{processInstanceId})"></div>


<script th:inline="javascript">
    var prefix = ctx + "zzfy/commTxf"

    $("#form-commTxf-add").validate({
        focusCleanup: true
    });

    /** 提交 */
    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTabAlert(prefix + "/startCourse", $('#form-commTxf-add').serialize());
        }
    }

    /** 提交 */
    function submitSave() {
        var type = $("#type").val();
        if (type == 'active') {
            var extra1 = $("#extra1").val();
            var extra3 = $("#extra3").val();
            if (extra1 == null || extra1 == '') {
                $.modal.msg("请填写报支单号！");
                return;
            }
            if (extra3 == null || extra3 == '') {
                $.modal.msg("请填写付款日期！");
                return;
            }
        }
        if ($.validate.form()) {
            $.operate.saveTabAlert(prefix + "/finish", $('#form-commTxf-add').serialize());
        }
    }

    /** 退回 */
    function submitReturn() {
        $("#opinions").show();
        var comment = $("#comment").val();
        if (comment == null || comment == "") {
            $.modal.msg("请填写退回意见！");
        } else {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/returnTxf", $('#form-commTxf-add').serialize());
            }
        }
    }
    function delCommTxf(){
        var commId = $("#commId").val();
        $.modal.confirm("确认要删除吗？", function() {
            if(commId){
                var config = {
                    url: prefix + "/delTxf/"+commId,
                    type: "post",
                    dataType: "json",
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.modal.alertSuccess(result.msg);
                        $.modal.closeLoading();
                        location.reload();
                        closeItem();
                    }
                };
                $.ajax(config)
            }else{
                closeItem();
            }
        })
    }


    $(function () {
        var type = $("#type").val();
        if (type == 'carry') {
            $("#btnSave").hide(); //审批按钮
            $("#btnHandler").show();//提交按钮
            $("#opinions").hide();//退回意见
            $("#reimburse").hide();//报支单
        } else if (type == 'active') {
            $("#btnSave").show(); //审批按钮
            $("#btnHandler").hide();//提交按钮
            $("#opinions").hide();//退回意见
            $("#reimburse").show();//报支单
            document.getElementById("commDate").disabled = "disabled";
            document.getElementById("commBeginDate").disabled = "disabled";
            document.getElementById("commEndDate").disabled = "disabled";
            document.getElementById("sceneTsfTotal").disabled = "disabled";
            document.getElementById("commCount").disabled = "disabled";
        }else if(type == 'return'){
            $("#btnSave").show(); //审批按钮
            $("#btnHandler").hide();//提交按钮
            $("#opinions").show();//退回意见
            $("#reimburse").hide();//报支单
            document.getElementById("comment").disabled = "disabled";
        }
    })

    /** 暂存*/
    /*function temporarily(){
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/temporarySave", $('#form-commTxf-add').serialize());
        }
    }*/

    //暂存
    function temporarily() {
        var config = {
            url: prefix + "/temporarySave",
            type: "post",
            dataType: "json",
            data: $('#form-commTxf-add').serialize(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                $("#commId").val(result.data.commId);
                $.modal.alertSuccess(result.msg);
                $.modal.closeLoading();
            }
        };
        $.ajax(config)
    }

    //流程跟踪
    function openProcessTrack(processInstanceId) {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processInstanceId);
    }

    function detailMain(mainId) {
        $.modal.openTab("项目详细信息", prefix + "/detail?mainId="+mainId);
    }

   /* function detailMain(mainId){
        var url = ctx + "zzlx/need";
        $.modal.openTab("项目详细信息", url + "/detail?needId="+mainId);
    }*/
</script>
</body>
</html>