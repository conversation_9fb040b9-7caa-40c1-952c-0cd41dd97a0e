<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('1.8-01现场支撑通讯费申请列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="row form-group">
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">项目名称：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="projectName" required type="text">
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">项目负责人：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="tgfFzrName" required type="text">
                        </div>
                    </div>
                </div>
                <div class="row form-group">
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">受让单位部门：</label>
                        <div class="col-sm-8"
                             th:include="/component/selectOrg :: init(orgCodeId='srfDwdeptCode',orgNameId='srfDwdeptName',selectType='S',value=*{srfDwdeptCode},isrequired=true)"></div>
                    </div>
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">推广单位部门：</label>
                        <div class="col-sm-8"
                             th:include="/component/selectOrg :: init(orgCodeId='tgfDwdeptCode',orgNameId='tgfDwdeptName',selectType='S',value=*{tgfDwdeptCode},isrequired=true)"></div>
                    </div>
                </div>
                <div class="row form-group">
                    <div class="col-sm-6">
                        <label class="col-sm-3 control-label">报支单号：</label>
                        <div class="col-sm-8">
                            <input autocomplete="off" class="form-control" name="sceneBzdh" required type="text">
                        </div>
                    </div>
                </div>
                <div class="select-list" style="float: right">
                    <ul>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzfy/commTxf";

    $(function() {
        var options = {
            url: prefix + "/commTxfLsit",
            pagination: true,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            //sidePagination: "client",
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' id='orderNum%s' name='tzzfyLongMx[%s].orderNum' value='%s'>", index, $.table.serialNumber(index));
                        return columnIndex + $.table.serialNumber(index);
                    }
                },
                {
                    field: 'projectNum',
                    align: 'center',
                    title: "项目编号",
                },
                {
                    field: 'projectName',
                    align: 'center',
                    title: "项目名称",
                },
                {
                    field: 'tgfFzrName',
                    align: 'center',
                    title: "项目负责人",
                },
                {
                    field: 'srfDwdeptName',
                    align: 'center',
                    title: "受让单位部门",
                },
                {
                    field: 'tgfDwdeptName',
                    align: 'center',
                    title: "推广单位部门",
                },
                {
                    field: 'extra1',
                    align: 'center',
                    title: "报支单号",
                },
                {
                    field: 'extra3',
                    align: 'center',
                    title: "付款日期",
                },
                {
                    field: 'commSubmitDate',
                    align: 'center',
                    title: "批准日期",
                },
                {
                    field: 'currentActivityName',
                    align: 'center',
                    title: "状态",
                },
                {
                    field: 'currentOperator',
                    align: 'center',
                    title: "当前处理人",
                }]
        };
        $.table.init(options);
    });
</script>
</body>
</html>