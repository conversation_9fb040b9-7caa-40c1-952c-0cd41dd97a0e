<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改1.6-02.01奖励人员分配奖励表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-rewardPeople-edit" th:object="${rewardPeople}">
         <div class="panel-group" id="accordion" role="tablist"
                             aria-multiselectable="true">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#version"
                                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                                        aria-hidden="true"></i></span>
                                        </a>
                                    </h4>
                                </div>
                                <div id="jbxx" class="panel-collapse collapse in"
                                     aria-expanded="false">
                                    <div class="panel-body">
            <input name="peopleFpGuid" th:field="*{peopleFpGuid}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">奖励分配主键：</label>
                <div class="col-sm-8">
                    <input name="fpGuid" th:field="*{fpGuid}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">奖励部门分配主键：</label>
                <div class="col-sm-8">
                    <input name="bmfpGuid" th:field="*{bmfpGuid}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">排序：</label>
                <div class="col-sm-8">
                    <input name="orderNum" th:field="*{orderNum}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">工号：</label>
                <div class="col-sm-8">
                    <input name="peopleFpUserId" th:field="*{peopleFpUserId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">姓名：</label>
                <div class="col-sm-8">
                    <input name="peopleFpUserName" th:field="*{peopleFpUserName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">单位组织代码：</label>
                <div class="col-sm-8">
                    <input name="peopleFpCode" th:field="*{peopleFpCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">单位组织名称：</label>
                <div class="col-sm-8">
                    <input name="peopleFpName" th:field="*{peopleFpName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">人员组织单位代码：</label>
                <div class="col-sm-8">
                    <input name="peopleFpDwcode" th:field="*{peopleFpDwcode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">人员组织单位名称：</label>
                <div class="col-sm-8">
                    <input name="peopleFpDwname" th:field="*{peopleFpDwname}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">金额：</label>
                <div class="col-sm-8">
                    <input name="peopleFpJlje" th:field="*{peopleFpJlje}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input name="peopleFpRemark" th:field="*{peopleFpRemark}" class="form-control" type="text">
                </div>
            </div>

      <div class="form-group" th:include="include :: initSelectBox(id='peopleFpType', name='peopleFpType',businessType=null, dictCode=null, value=${rewardPeople.peopleFpType} ,labelName='1:部门新增 0负责人分配')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发奖编号：</label>
                <div class="col-sm-8">
                    <input name="fjsNum" th:field="*{fjsNum}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段3：</label>
                <div class="col-sm-8">
                    <input name="extra3" th:field="*{extra3}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段4：</label>
                <div class="col-sm-8">
                    <input name="extra4" th:field="*{extra4}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段5：</label>
                <div class="col-sm-8">
                    <input name="extra5" th:field="*{extra5}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',businessType=null, dictCode=null ,value=${rewardPeople.delStatus} ,labelName='删除状态')">
            </div>
</div>
                    </div>
                </div>
            </div>
        </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>

    <script th:inline="javascript">
        var prefix = ctx + "zzjl/rewardPeople";

        $("#form-rewardPeople-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-rewardPeople-edit').serialize());
            }
        }

    </script>
</body>
</html>