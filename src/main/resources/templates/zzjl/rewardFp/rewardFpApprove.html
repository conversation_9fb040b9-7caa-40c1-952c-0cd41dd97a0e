<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增奖励审批表')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div  class="form-group" th:include="include :: step(approveKind='ZZZC_REWARD',currentNode=${activityCode})"></div>
    <form class="form-horizontal m" id="form-check-add" th:object="${rewardFp}">
        <input id="fpGuid" name="fpGuid"  th:value="*{fpGuid}" type="hidden">
        <input id="bizId" name="bizId"  th:value="*{bizId}" type="hidden">
        <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
        <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
        <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
        <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
        <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">奖励分配表
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body" th:if="${rewardFp.fpMethod == '1'}">
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-12">单位：<span  th:utext="*{fpDwName}" ></span></label>
                                <div class="col-sm-12">
                                    贵单位技术受让项目 <span  th:utext="*{extra1}" ></span><span  th:utext="*{extra2}" ></span>已履行完毕，按照规定的程序和标准，，推广方组织了对项目的评估，估计总分为<span  th:utext="*{extra3}" ></span>。根据公司《技术输出项目评估及奖励管理标准》规定，受让方可根据本单位实际情况，给予受让方项目组适当的奖励。<br>
                                    <label style="font-weight: bold">建议奖励额为：</label><span  th:utext="${#numbers.formatDecimal(rewardFp.fpJyjlje,1,2)}" ></span><br>
                                    <label style="font-weight: bold">实发励金额为：</label><input name="fpSjjlje" id="fpSjjlje"  th:value="${#numbers.formatDecimal(rewardFp.fpSjjlje,1,2)}" type="number" required  >万元<br>
                                    *注:请推广方单位信凭此单进行奖金分配，受让方按奖励授权范围审批。<br>
                                </div>
                                <label class="col-sm-10 control-label"><div  th:include="/component/select :: init(see=true,value=${rewardFp.fpDwCode},businessType='KTTG', dictCode='rewardNoticeDept')"></div></label>
                                <label class="col-sm-10 control-label">日期：<span  th:utext="*{fpShowDate}" ></span>
                                </label>
                            </div>
                        </div>
                    </div>
                        <div class="panel-body" th:if="${rewardFp.fpMethod == '2'}">
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">项目名称：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${main.projectName}"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">项目编号：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">受让单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.srfDeptName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">推广单位部门：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfDeptName}"></div>
                                        </div>

                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfFzrName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">联系电话：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.tgfXmfzrTel}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">受让方项目负责人：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.srfFzrName}"></div>
                                        </div>
                                    </div>
                                </div>
                                <div id="ck" class="panel-collapse collapse in" aria-expanded="false">
                                    <div class="panel-body">

                                        <div class="form-group">
                                            <label class="col-sm-1"></label>
                                            <label class="col-sm-11">点击<a class="form_list_a" th:onclick="$.modal.openTab('项目详细信息',ctx+'zzlx/main/detail?mainId='+[[${main.mainId}]])">此处</a>查看项目详细信息</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="panel-group" id="accordion7" role="tablist" aria-multiselectable="true">
                                    <div class="panel panel-default">
                                        <div class="panel-heading">
                                            <h4 class="panel-title">
                                                <a data-toggle="collapse" href="#zscq" class="collapsed">
                                                    已奖励信息
                                                    <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                </a>
                                            </h4>
                                        </div>
                                        <div id="zscq" class="panel-collapse collapse in">
                                            <div class="panel-body">
                                                <div class="form-group">
                                                    <div class="col-sm-12">
                                                        <div class="col-sm-12 select-table ">
                                                            <table id="bootstrap-table-key"></table>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">预奖申请理由</label>
                                        <div class="col-sm-10 "  >
                                            <div class="form-control-static" th:utext="*{extra4}"></div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">申请奖励金额</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${#numbers.formatDecimal(rewardFp.fpJyjlje,1,2)}"></div>
                                        </div>
                                        <label class="col-sm-2">(万元)</label>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label is-required">决定奖励金额</label>
                                        <div class="col-sm-4">
                                            <input name="fpSjjlje" id="fpSjjlje" th:value="*{fpSjjlje}" type="number" required class="form-control">
                                        </div>
                                        <label class="col-sm-2">(万元)</label>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="panel-body" th:if="${rewardFp.fpMethod == '3'}">
                            <div class="row">
                                <label class="col-sm-12">单位：<span  th:utext="*{fpDwName}" ></span></label>
                                <div class="col-sm-12">
                                    贵单位工作联络单受让项目 <span  th:utext="*{extra1}" ></span><span  th:utext="*{extra2}" ></span>已履行完毕，给予受让方项目组适当的奖励。<br>
                                    <label style="font-weight: bold">建议奖励额为：</label><span  th:utext="${#numbers.formatDecimal(rewardFp.fpJyjlje,1,2)}" ></span><br>
                                    <label style="font-weight: bold">实发励金额为：</label><input name="fpSjjlje" id="fpSjjlje"  th:value="${#numbers.formatDecimal(rewardFp.fpSjjlje,1,2)}" type="number" required  >万元<br>
                                    *注:请推广方单位信凭此单进行奖金分配，受让方按奖励授权范围审批。<br>
                                </div>
                                <label class="col-sm-10 control-label"><div  th:include="/component/select :: init(see=true,value=${rewardFp.fpDwCode},businessType='KTTG', dictCode='rewardNoticeDept')"></div></label>
                                <label class="col-sm-10 control-label">日期：<span  th:utext="*{fpShowDate}" ></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
        </div>
        <th:block th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=${processInstanceId},required='true',freeFlow='false')"></th:block>

    </form>
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
    </div>
<div class="toolbar toolbar-bottom" role="toolbar">

            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

            <th:block th:include="/component/wfReturn :: init(taskId=${taskId},callback=wfReturn)"/>
            <button type="button" class="btn  btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返 回
            </button>
        </div>

    <!--审批历史-->
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})">
    </div>
    <script th:inline="javascript">
        var prefix = ctx + "zzjl/rewardFp"


        $("input[name='jtysFinishEnd']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });


        $("#form-check-add").validate({
            focusCleanup: true
        });


        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/submitWF", $('#form-check-add').serialize());
            }
        }

        //流程跟踪
        function openProcessTrack(processId){
            window.open(ctxGGMK+"web/EWPI01?inqu_status-0-processInstanceId="+processId);
        }

        $(function() {
            var options = {
                url: ctx + "zzjl/rewardFp/listByBizId?bizId="+[[${rewardFp.bizId}]],
                detailUrl: prefix +"/queryDetail/{id}",
                id:"bootstrap-table-key",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: false
                },
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            return  $.table.serialNumber(index) ;
                        }
                    },
                    {
                        field: 'createUserLabel',
                        align: 'center',
                        title: '申请人'
                    },
                    {
                        field: 'fpShowDate',
                        align: 'center',
                        title: '申请日期'
                    },
                    {
                        field: 'fpSjjlje',
                        align: 'center',
                        title: '奖励金额'
                    },
                    {
                        field: 'jtawardRemark',
                        align: 'center',
                        title: '操作',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.fpGuid + '\')"><i class="fa fa-edit"></i>查看</a> ');
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });





        //退回流程
        function wfReturn(activityKey) {
            if ($.validate.form()) {
                var data = $('#form-check-add').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
            }
        }





    </script>
</body>
</html>