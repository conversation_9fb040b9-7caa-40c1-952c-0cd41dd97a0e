<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('1.6-02奖励分配表列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>奖励来源</label>
                                <select name="fpMethod"  class="form-control m-b"
                                        th:with="type=${@dict.getDictList('KTTG','rewardFpMethod')}" required>
                                    <option th:text="请选择" value=""></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>奖励分配日期：</label>
                                <input type="text" name="fpShowDate"/>
                            </li>
                            <li>
                                <label>项目编号：</label>
                                <input type="text" name="extra1"/>
                            </li>
                            <li>
                                <label>项目名称：</label>
                                <input type="text" name="extra2"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzjl/rewardFp";
        var  methodDatas =[[${@dict.getDictList('KTTG','rewardFpMethod')}]];

        $(function() {
            var options = {
                url: prefix + "/list",
                detailUrl: prefix +"/queryDetail/{id}",
                exportUrl: prefix + "/export",
                modalName: "1.6-02奖励分配表",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'extra1',
                    title: '项目编号'
                },
                {
                    field: 'extra2',
                    title: '项目名称'
                },
                {
                    field: 'fpDwName',
                    title: '奖励单位名称'
                },
                {
                    field: 'fpJyjlje',
                    title: '建议奖励金额'
                },
                {
                    field: 'fpSjjlje',
                    title: '实发奖励额'
                },
                {
                    field: 'fpMethod',
                    title: '奖励来源',
                    formatter:function (value,row,index){
                      return $.table.selectDictLabel(methodDatas,value);
                    }
                },
                {
                    field: 'fpShowDate',
                    title: '奖励分配日期'
                },
                {
                    field: 'fpFjDate',
                    title: '奖励审批日期'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.fpGuid + '\')"><i class="fa fa-edit"></i>查看</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>