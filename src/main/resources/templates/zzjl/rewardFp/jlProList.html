<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('结题报告列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
            <div class="btn-group-sm" id="toolbar" role="group">

                <a class="btn btn-success" onclick="$.operate.add()" >
                    <i class="fa fa-plus"></i> 新增预奖
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzjl/rewardFp";
        var  methodDatas =[[${@dict.getDictList('KTTG','rewardFpMethod')}]];
        $(function() {
            var options = {
                url: prefix + "/cglist",
                createUrl: prefix + "/projectList",
                detailUrl: prefix +"/queryDetail/{id}",
                removeUrl: prefix + "/remove",
                updateUrl: prefix + "/edit/{id}",
                modalName: "预奖申请列表",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'extra1',
                    title: '项目编号'
                },
                {
                    field: 'extra2',
                    title: '项目名称'
                },
                {
                    field: 'fpDwName',
                    title: '奖励单位名称'
                },
                {
                    field: 'fpMethod',
                    title: '奖励来源',
                    formatter:function (value,row,index){
                        return $.table.selectDictLabel(methodDatas,value);
                    }
                },
                {
                    field: 'fpJyjlje',
                    title: '建议奖励金额'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.fpGuid + '\')"><i class="fa fa-eye"></i>查看</a>');
                        actions.push('<a class="btn btn-primary btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.fpGuid + '\')"><i class="fa fa-edit"></i>修改</a>');
                        actions.push('<a class="btn btn-primary btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.fpGuid + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function startJLNotice(mainId){
            $.modal.confirm("确认要开启预奖审批流程吗？", function () {
                $.operate.post(prefix + "/startJLNotice/"+mainId, {});
            })
        }

    </script>
</body>
</html>