<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增预奖')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-check-add" th:object="${rewardFp}">
            <input id="fpGuid" name="fpGuid"  th:value="*{fpGuid}" type="hidden">
            <input id="bizId" name="bizId"  th:value="${main.mainId}" type="hidden">
            <input id="fpCode" name="fpCode"  th:value="${main.srfDeptCode}" type="hidden">
            <input id="fpName" name="fpName"  th:value="${main.srfDeptName}" type="hidden">
            <input id="fpDwCode" name="fpDwCode"  th:value="${main.srfDwdeptCode}" type="hidden">
            <input id="fpDwName" name="fpDwName"  th:value="${main.srfDwdeptName}" type="hidden">
            <input id="fpType" name="fpType"  th:value="1" type="hidden">
            <input id="fpMethod" name="fpMethod"  th:value="2" type="hidden">
            <input id="extra1" name="extra1"  th:value="${main.projectNum}" type="hidden">
            <input id="extra2" name="extra2"  th:value="${main.projectName}" type="hidden">
            <input id="comment" name="comment"   type="hidden">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">奖励分配表
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body" >
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目名称：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.projectName}"></div>
                                        </div>
                                        <label class="col-sm-2 control-label">项目编号：</label>
                                        <div class="col-sm-4">
                                            <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label">受让单位部门：</label>
                                            <div class="col-sm-4" th:include="/component/selectOrg :: init(orgCodeId='tgfDwdeptCode',orgNameId='tgfDwdeptName',value=${main.srfDeptCode},selectType='S',see=true)"></div>
                                        <label class="col-sm-2 control-label">推广单位部门：</label>
                                         <div class="col-sm-4" th:include="/component/selectOrg :: init(orgCodeId='tgfDwdeptCode',orgNameId='tgfDwdeptName',value=${main.tgfDeptCode},selectType='S',see=true)"></div>
                                    </div>
                                    </div>

                                    <div class="row">
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                            <div class="col-sm-4">
                                                <div class="form-control-static" th:utext="${main.tgfFzrName}"></div>
                                            </div>
                                            <label class="col-sm-2 control-label">联系电话：</label>
                                            <div class="col-sm-4">
                                                <div class="form-control-static" th:utext="${main.tgfXmfzrTel}"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label">受让方项目负责人：</label>
                                            <div class="col-sm-4">
                                                <div class="form-control-static" th:utext="${main.srfFzrName}"></div>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="ck" class="panel-collapse collapse in" aria-expanded="false">
                                        <div class="panel-body">

                                            <div class="form-group">
                                                <label class="col-sm-1"></label>
                                                <label class="col-sm-11">点击<a class="form_list_a" th:onclick="$.modal.openTab('项目详细信息',ctx+'zzlx/main/detail?mainId='+[[${main.mainId}]])">此处</a>查看项目详细信息</label>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="panel-group" id="accordion7" role="tablist" aria-multiselectable="true">
                                        <div class="panel panel-default">
                                            <div class="panel-heading">
                                                <h4 class="panel-title">
                                                    <a data-toggle="collapse" href="#zscq" class="collapsed">
                                                       已奖励信息
                                                        <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                                                    </a>
                                                </h4>
                                            </div>
                                            <div id="zscq" class="panel-collapse collapse in">
                                                <div class="panel-body">
                                                    <div class="form-group">
                                                        <div class="col-sm-12">
                                                            <div class="col-sm-12 select-table ">
                                                                <table id="bootstrap-table-key"></table>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label is-required">预奖申请理由</label>
                                            <div class="col-sm-10 control-label">
                                                <textarea name="extra4" th:utext="*{extra4}"  class="form-control" rows="8" required maxlength="500"></textarea>
                                            </div>
                                        </div>
                                     </div>



                                    <div class="row">
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label is-required">申请奖励金额</label>
                                            <div class="col-sm-4">
                                                <input name="fpJyjlje" id="fpJyjlje" th:field="*{fpJyjlje}" type="number" required class="form-control" min="0">
                                            </div>
                                            <label class="col-sm-2">(万元)</label>
                                        </div>
                                    </div>
                                </div>
                            </div>
        </div>
                    </div>
          </div>
        </form>

    <div class="toolbar toolbar-bottom" role="toolbar">

            <button type="button" class="btn  btn-primary"
                    onclick="saveHandler()">
                <i class="fa fa-check"></i>暂 存
            </button>

            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

          	<button type="button" class="btn  btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>

    <!--审批历史-->
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})">
    </div>
    <script th:inline="javascript">
        var prefix = ctx + "zzjl/rewardFp"


        $("input[name='jtysFinishEnd']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });


        $("#form-check-add").validate({
            focusCleanup: true
        });

        function saveHandler() {
            var config = {
                url: prefix + "/add",
                type: "post",
                dataType: "json",
                data: $('#form-check-add').serialize(),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $("#fpGuid").val(result.data.fpGuid);
                    $.modal.alertSuccess(result.msg);
                    $.modal.closeLoading();
                }
            };
            $.ajax(config)
        }

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/startWF", $('#form-check-add').serialize());
            }
        }



        $(function() {
            var options = {
                url: ctx + "zzjl/rewardFp/listByBizId?bizId="+[[${main.mainId}]],
                detailUrl: prefix +"/queryDetail/{id}",
                id:"bootstrap-table-key",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: false
                },
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            return  $.table.serialNumber(index) ;
                        }
                    },
                    {
                        field: 'createUserLabel',
                        align: 'center',
                        title: '申请人'
                    },
                    {
                        field: 'fpShowDate',
                        align: 'center',
                        title: '申请日期'
                    },
                    {
                        field: 'fpSjjlje',
                        align: 'center',
                        title: '奖励金额'
                    },
                    {
                        field: 'jtawardRemark',
                        align: 'center',
                        title: '操作',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.fpGuid + '\')"><i class="fa fa-edit"></i>查看</a> ');
                            return actions.join('');
                        }
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });








    </script>
</body>
</html>