<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('项目一览表查看奖励分配列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <input type="hidden" th:value="${bizId}">
                    <div class="select-list">
                        <ul>
                            <!--<li>
                                <label>奖励来源</label>
                                <select name="fpMethod"  class="form-control m-b"
                                        th:with="type=${@dict.getDictList('KTTG','rewardFpMethod')}" required>
                                    <option th:text="请选择" value=""></option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <label>奖励分配日期：</label>
                                <input type="text" name="fpShowDate"/>
                            </li>-->
                            <li>
                                <label>项目编号：</label>
                                <input type="text" name="projectNum"/>
                            </li>
                            <li>
                                <label>项目名称：</label>
                                <input type="text" name="projectName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <!--<a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>-->
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzjl/rewardFp";
        var  methodDatas =[[${@dict.getDictList('KTTG','rewardFpMethod')}]];
        var bizId = [[${bizId}]];
        $(function() {
            var options = {
                url: prefix + "/listByBizIdIng?bizId="+bizId,
                detailUrl: prefix +"/queryDetail/{id}",
                exportUrl: prefix + "/export",
                modalName: "奖励分配表",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'fpDwName',
                    title: '奖励单位名称'
                },
                {
                    field: 'fpJyjlje',
                    title: '建议奖励金额'
                },
                    /* {
                         field: 'fpSjjlje',
                         title: '实发奖励额'
                     },
                  /* {
                         field: 'fpMethod',
                         title: '奖励来源',
                         formatter:function (value,row,index){
                           return $.table.selectDictLabel(methodDatas,value);
                         }
                     },*/
                {
                    field: 'fpShowDate',
                    title: '奖励分配日期',
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
               /* {
                    field: 'fpFjDate',
                    title: '奖励审批日期'
                },*/
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="showDetail(\'' + row.jjfpId + '\')"><i class="fa fa-edit"></i>查看</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function showDetail(ywmainId) {
            url = ctxYWZT + "web/YWZTFJ02?jjfpId=" + ywmainId;
            $.modal.open("奖励分配详细", url,'900', '500');
        }

    </script>
</body>
</html>