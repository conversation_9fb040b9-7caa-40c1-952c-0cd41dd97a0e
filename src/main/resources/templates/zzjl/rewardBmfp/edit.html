<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改1.6-03部门奖励分配表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-rewardBmfp-edit" th:object="${rewardBmfp}">
         <div class="panel-group" id="accordion" role="tablist"
                             aria-multiselectable="true">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#version"
                                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                                        aria-hidden="true"></i></span>
                                        </a>
                                    </h4>
                                </div>
                                <div id="jbxx" class="panel-collapse collapse in"
                                     aria-expanded="false">
                                    <div class="panel-body">
            <input name="bmfpGuid" th:field="*{bmfpGuid}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label is-required">奖励分配主键：</label>
                <div class="col-sm-8">
                    <input name="fpGuid" th:field="*{fpGuid}" class="form-control" type="text" required>
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">奖励部门分配组织：</label>
                <div class="col-sm-8">
                    <input name="bmfpCode" th:field="*{bmfpCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">奖励部门分配组织名称：</label>
                <div class="col-sm-8">
                    <input name="bmfpName" th:field="*{bmfpName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">奖励部门确认人员工号：</label>
                <div class="col-sm-8">
                    <input name="bmfpUserCode" th:field="*{bmfpUserCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">奖励部门确认人员姓名：</label>
                <div class="col-sm-8">
                    <input name="bmfpUserName" th:field="*{bmfpUserName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">奖励部门确认意见：</label>
                <div class="col-sm-8">
                    <input name="bmfpOption" th:field="*{bmfpOption}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段3：</label>
                <div class="col-sm-8">
                    <input name="extra3" th:field="*{extra3}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段4：</label>
                <div class="col-sm-8">
                    <input name="extra4" th:field="*{extra4}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段5：</label>
                <div class="col-sm-8">
                    <input name="extra5" th:field="*{extra5}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',businessType=null, dictCode=null ,value=${rewardBmfp.delStatus} ,labelName='删除状态')">
            </div>
</div>
                    </div>
                </div>
            </div>
        </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>

    <script th:inline="javascript">
        var prefix = ctx + "zzjl/rewardBmfp";

        $("#form-rewardBmfp-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-rewardBmfp-edit').serialize());
            }
        }

    </script>
</body>
</html>