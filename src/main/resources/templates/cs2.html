<!DOCTYPE html>

<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('新增系统公告')" />
<th:block th:include="include :: baseJs" />
</head>
<body>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-notice-add">
			<div class="form-group">
				<label class="col-sm-3 control-label is-required">标题：</label>
				<div class="col-sm-8">
					<input name="title" class="form-control" required type="text">
				</div>
			</div>
			<div class="form-group">
				<label class="col-sm-3 control-label is-required">cs1</label>
				<div class="col-sm-8" th:include="/component/date :: init">

				</div>
			</div>

			<div class="form-group">
				<label class="col-sm-3 control-label is-required">cs2</label>
				<div class="col-sm-8" th:include="/component/checkbox :: init(id='cs1', name='cs1',businessType='MPTY', dictCode='KXCP_NPGDXQ')">

				</div>
			</div>

			<div class="form-group">
				<label class="col-sm-3 control-label is-required">cs3</label>
				<div class="col-sm-8" th:include="/component/select :: init(id='cs2', name='cs2',businessType='MPTY', dictCode='KXCP_NPGDXQ')">

				</div>
			</div>

			<div class="form-group">
				<label class="col-sm-3 control-label is-required">cs4</label>
				<div class="col-sm-8" th:include="/component/radio :: init(id='cs3', name='cs3',businessType='MPTY', dictCode='KXCP_NPGDXQ' )">

				</div>
			</div>


			<div class="form-group">
				<label class="col-sm-3 control-label is-required">cs5</label>
				<div class="col-sm-8" th:include="/component/select :: init(id='cs4', name='cs4',businessType='MPTY', dictCode='KXCP_NPGDXQ',multimultiple=true ,value='NPGDXQ1,NPGDXQ2')">

				</div>
			</div>







		</form>
	</div>
	<script th:inline="javascript">
    function  cs(a,b){
        console.log(a);
        console.log(b);

    }
</script>
</body>
</html>