<!DOCTYPE html>

<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('新增系统公告')" />
<th:block th:include="include :: baseJs" />
</head>
<body>
<body class="white-bg">
	<div class="wrapper wrapper-content animated fadeInRight ibox-content">
		<form class="form-horizontal m" id="form-notice-add">
			<div class="form-group">
				<label class="col-sm-3 control-label is-required">标题：</label>
				<div class="col-sm-8">
					<input name="title" class="form-control" required type="text">
				</div>
			</div>
			<!--单选框-->
			<div class="form-group" th:include="include :: initRadio(id='cs', name='cs',businessType='MPTY', dictCode='KXCP_NPGDXQ',callback='cs')"></div>
			<div class="form-group" th:include="include :: initRadio(id='cs2', name='cs2',isrequired=true,value='2',businessType='MPTY', dictCode='KXCP_NPGDXQ')"></div>
			<div class="form-group" th:include="include :: initRadio(see=true,value='1',businessType='MPTY', dictCode='KXCP_NPGDXQ')"></div>


			<!--多选框-->
			<div class="form-group" th:include="include :: initCheckBox(id='cs3', name='cs3',businessType='MPTY', dictCode='KXCP_NPGDXQ',callback='cs')"></div>
			<div class="form-group" th:include="include :: initCheckBox(id='cs4', name='cs4',isrequired=true,value='1,2',businessType='MPTY', dictCode='KXCP_NPGDXQ')"></div>
			<div class="form-group" th:include="include :: initCheckBox(see=true,value='1',businessType='MPTY', dictCode='KXCP_NPGDXQ')"></div>


			<!--下拉框-->
			<div class="form-group" th:include="include :: initSelectBox(id='cs5', name='cs5',businessType='MPTY', dictCode='KXCP_NPGDXQ')"></div>
			<div class="form-group" th:include="include :: initSelectBox(id='cs6', name='cs6',isrequired=true,value='2',businessType='MPTY', dictCode='KXCP_NPGDXQ')"></div>
			<div class="form-group" th:include="include :: initSelectBox(see=true,value='1',businessType='MPTY', dictCode='KXCP_NPGDXQ')"></div>


			<!--时间-->
			<div class="form-group" th:include="include :: initDate(id='cs7', name='cs7',endDate='2021-11-01')"></div>
			<div class="form-group" th:include="include :: initDate(id='cs8', name='cs8',isrequired=true,strValue='2021-09-26')"></div>
			<div class="form-group" th:include="include :: initDate(see=true,strValue='2021-09-26')"></div>

			<!-- 流程意见 -->
			<div class="form-group" th:include="include :: includeTaskHistoryList(instanceId='bf058109-28c5-11ec-9002-c63c7b64c85e')"></div>
            <!--引导图-->
            <div class="form-group" th:include="include :: step(approveKind='KYXM_KYLX',currentNode='ND_GS_PLAN_ADMIN')">
            </div>
			<!-- 附件 -->
			<div class="form-group" th:include="include :: layui-upload(id='layui-upload', name='layui-upload')"></div>

			<!--选择人-->
			<div class="form-group" th:include="include :: choiceUser(userCodeId='userCode',userNameId='userName',selectType='M')">

			</div>
			<!--回显名称-->
			<div class="form-group" th:include="include :: choiceUser(userCodeId='userCode2',userNameId='userName2',selectType='M' ,value='HK4385')">

			</div>
			<!--显示人员名称-->
			<div class="form-group" th:include="include :: choiceUser(value='HK4385',see=true)">

			</div>


			<!--选择组织-->

			<div class="form-group" th:include="include :: choiceOrg(orgCodeId='orgCode',orgNameId='orgName',selectType='S')">

			</div>
			<!--回显名称-->
			<div class="form-group" th:include="include :: choiceOrg(orgCodeId='orgCode2',orgNameId='orgName2',selectType='M' ,value='BSECUY00',showLevel=4)">

			</div>
			<!--显示组织名称-->
			<div class="form-group" th:include="include :: choiceOrg(value='BSECUY00',see=true)">

			</div>


		</form>
	</div>
	<script th:inline="javascript">
    function  cs(a,b){
        console.log(a);
        console.log(b);
       
    }
</script>
</body>
</html>