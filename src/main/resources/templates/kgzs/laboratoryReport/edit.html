<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改联合工作室总结')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="form-laboratoryReport-edit" th:object="${laboratoryReport}">
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <input name="guid" th:field="*{guid}" type="hidden">
                            <div class="form-group">
                                <label class="col-sm-4 control-label">联合工作室主键：</label>
                                <div class="col-sm-8">
                                    <input name="labGuid" th:field="*{labGuid}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">年份：</label>
                                <div class="col-sm-8">
                                    <input name="fyear" th:field="*{fyear}" class="form-control" type="text">
                                </div>
                            </div>

                            <div class="form-group" th:include="include :: initSelectBox(id='reportType', name='reportType',businessType=null, dictCode=null, value=${laboratoryReport.reportType} ,labelName='总结类型：半年总结，全年总结')">
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">工作室运行情况：</label>
                                <div class="col-sm-8">
                                    <input name="operation" th:field="*{operation}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">阶段目标完成情况：</label>
                                <div class="col-sm-8">
                                    <input name="completion" th:field="*{completion}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">工作室取得成效及亮点：</label>
                                <div class="col-sm-8">
                                    <input name="highlight" th:field="*{highlight}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">存在的问题及改进建议：</label>
                                <div class="col-sm-8">
                                    <input name="problem" th:field="*{problem}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">工作目标：</label>
                                <div class="col-sm-8">
                                    <input name="goal" th:field="*{goal}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">工作内容：</label>
                                <div class="col-sm-8">
                                    <input name="content" th:field="*{content}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">项目主管评价：</label>
                                <div class="col-sm-8">
                                    <input name="zgEvaluate" th:field="*{zgEvaluate}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">建议奖励金额：</label>
                                <div class="col-sm-8">
                                    <input name="rewardAmount" th:field="*{rewardAmount}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group" th:include="include :: initRadio(id='status', name='status',businessType=null, dictCode=null ,value=${laboratoryReport.status} ,labelName='状态')">
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">评审结果：同意、不同意：</label>
                                <div class="col-sm-8">
                                    <input name="reviewFlag" th:field="*{reviewFlag}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段1：</label>
                                <div class="col-sm-8">
                                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段2：</label>
                                <div class="col-sm-8">
                                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段3：</label>
                                <div class="col-sm-8">
                                    <input name="extra3" th:field="*{extra3}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段4：</label>
                                <div class="col-sm-8">
                                    <input name="extra4" th:field="*{extra4}" class="form-control" type="text">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-4 control-label">扩展字段5：</label>
                                <div class="col-sm-8">
                                    <input name="extra5" th:field="*{extra5}" class="form-control" type="text">
                                </div>
                            </div>
			            </div>
                    </div>
                </div>
            </div>
        </form>
        <!--按钮区-->
		<div class="toolbar toolbar-bottom" role="toolbar">
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
		<!--按钮区end-->
    </div>
    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "kgzs/laboratoryReport";

        $("#form-laboratoryReport-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-laboratoryReport-edit').serialize());
            }
        }

    </script>
</body>
</html>