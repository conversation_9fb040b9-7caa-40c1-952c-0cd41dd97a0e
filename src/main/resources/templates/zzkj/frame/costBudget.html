<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('框架协议费用实绩')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-frame-add" th:object="${frameEx}">
        <div aria-multiselectable="true" id="accordion" role="tablist">
            <!-- 基本信息 Start -->
            <div class="panel panel-default panel-group">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#1">基本信息
                            <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                        </a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                    <div class="panel-body">

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">协议号：</label>
                                <div class="col-sm-8">
                                    <div class="form-control-static" th:text="*{frameNum}"></div>
                                </div>
                            </div>

                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">项目范围：</label>
                                <div class="col-sm-8"
                                     th:include="/component/select :: init(id='frameArea', name='frameArea',businessType='KTTG', dictCode='projectArea',isfirst=true,value=*{frameArea},see=true)"></div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <label class="col-sm-2 control-label">协议名称：</label>
                            <div class="col-sm-10">
                                <div class="form-control-static" th:text="*{frameName}"></div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">协议受让方：</label>
                                <div class="col-sm-8"
                                     th:include="/component/selectOrg :: init(orgCodeId='frameSrfCode',orgNameId='frameSrfName',selectType='S',value=*{frameSrfCode},see=true)"></div>
                            </div>

                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">协议推广方：</label>
                                <div class="col-sm-8"
                                     th:include="/component/selectOrg :: init(orgCodeId='frameTgfCode',orgNameId='frameTgfName',selectType='S',value=*{frameTgfCode},see=true)"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 基本信息 End -->

            <!-- 项目费用列表 Start -->
            <div class="panel panel-default panel-group">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#2">一、项目费用
                            <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                        </a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                    <div class="panel-body">
                        <table id="bootstrap-tableCost"></table>
                    </div>
                </div>
            </div>
            <!-- 项目费用列表 End -->

            <!-- 联络单费用列表 Start -->
            <div class="panel panel-default panel-group">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#3">二、联络单费用
                            <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                        </a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="3">
                    <div class="panel-body">
                        <table id="bootstrap-tableContact"></table>
                    </div>
                </div>
            </div>
            <!-- 联络单费用列表 End -->

            <!-- 开票费用列表 Start -->
            <div class="panel panel-default panel-group">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#4">三、开票费用
                            <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                        </a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="4">
                    <div class="panel-body">
                        <table id="bootstrap-tableBilling"></table>
                    </div>
                </div>
            </div>
            <!-- 开票费用列表 End -->

            <!-- 费用结算 Start -->
            <div class="panel panel-default panel-group">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#5"> 费用结算（以下费用都不含税）
                            <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                        </a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="5">
                    <div class="panel-body">
                        <div class="row form-group">
                            <label class="col-sm-2 control-label" th:text="*{frameYear}-1+'年费用结余：'"></label>
                            <div class="col-sm-10">
                                <div class="form-control-static" th:text="*{frameLastTotal}"></div>
                            </div>
                        </div>
                        <div class="row form-group">
                            <label class="col-sm-2 control-label" th:text="*{frameYear}+'年已发生费用合计：'"></label>
                            <div class="col-sm-10">
                                <div class="form-control-static" id="frameHappenTotal"></div>
                            </div>
                        </div>
                        <div class="row form-group">
                            <label class="col-sm-2 control-label" th:text="*{frameYear}+'年已开票费用合计：'"></label>
                            <div class="col-sm-10">
                                <div class="form-control-static" id="frameOpenTotal"></div>
                            </div>
                        </div>
                        <div class="row form-group">
                            <label class="col-sm-2 control-label" th:text="*{frameYear}+'年费用结余：：'"></label>
                            <div class="col-sm-10">
                                <div class="form-control-static" id="frameTotal"></div>
                            </div>
                        </div>
                        <div class="row form-group">
                            <div class="col-sm-12">
                                <p>备注：</p>
                                <p>已发生费用合计 = 项目费用合计 + 联络单费用合计</p>
                                <p>费用结余 = 上一年费用结余 + 开票费用合计 - 已发生费用合计</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 费用结算 End -->
        </div>
        <div class="panel"></div>
    </form>
</div>
<div class="row">
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button class="btn btn-sm btn-danger" onclick="closeItem()" type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>
<!-- 审批历史 -->
<div th:if="${! #strings.isEmpty(frameEx.processInstanceId)}"
     th:include="component/wfCommentList::init(processInstanceId=${frameEx.processInstanceId})"></div>

<script th:inline="javascript">
    var prefix = ctx + "zzkj/frame";
    var frameLastTotal = parseFloat([[${frameEx.frameLastTotal}]]);
    var frameHappenTotal = parseFloat("0");
    var frameOpenTotal = parseFloat("0");
    var frameTotal = parseFloat("0");

    /**
     * 初始化数据
     */
    $(function () {
        var frameNum = [[${frameEx.frameNum}]];
        var options = {// 项目费用
            id: "bootstrap-tableCost",
            url: prefix + "/costBudget",
            detailUrl: ctx + "zzlx/main/detail?mainId={id}",
            showFooter: true, pagination: false,
            showSearch: false, showRefresh: false, showToggle: false, showColumns: false,
            modalName: "项目费用",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.year = frameNum.substring(0, 4);
                search.tgfDept = frameNum.substring(4, 8);
                search.srfDept = frameNum.substring(9, 13);
                return search;
            },
            columns: [
                [
                    {
                        field: 'index', valign: 'middle', rowspan: 2, title: "序号", formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {
                        field: 'projectNum', valign: 'middle', rowspan: 2, title: '项目号', formatter: function (value, row, index) {
                            return '<a onclick="$.operate.detailTab(\'' + row.mainId + '\')">'+value+'</a>';
                        }
                    },
                    {field: 'projectName', valign: 'middle', rowspan: 2, title: '项目名称'},
                    {field: 'tgfDeptName', valign: 'middle', rowspan: 2, title: '推广部门'},
                    {field: 'srfDeptName', valign: 'middle', rowspan: 2, title: '受让部门'},
                    {align: 'center', title: '差旅费', colspan: 3},
                    {align: 'center', title: '通讯费', colspan: 3},
                    {align: 'center', title: '人工费', colspan: 3},
                    {align: 'center', title: '其它费', colspan: 3},
                    {
                        field: 'managementCost', valign: 'middle', rowspan: 2, title: '管理费', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'subtotal', valign: 'middle', rowspan: 2, title: '小计', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }, footerFormatter: function (value) {
                            var total = 0;
                            for (var i in value) {
                                var subtotal = parseFloat(value[i].subtotal);
                                if (Number.isNaN(subtotal)) {
                                    subtotal = 0;
                                }
                                total += subtotal;
                            }
                            return '<span id="budget">' + total + '</span>';
                        }
                    }
                ],
                [
                    {
                        field: 'travelCostBudget', align: 'center', title: '项目预算', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'travelCostActual', align: 'center', title: '项目实绩', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'travelCostYear', align: 'center', title: '当年实绩', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'comCostBudget', align: 'center', title: '项目预算', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'comCostActual', align: 'center', title: '项目实绩', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'comCostYear', align: 'center', title: '当年实绩', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'laborCostBudget', align: 'center', title: '项目预算', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'laborCostActual', align: 'center', title: '项目实绩', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'laborCostYear', align: 'center', title: '当年实绩', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'otherCostBudget', align: 'center', title: '项目预算', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'otherCostActual', align: 'center', title: '项目实绩', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'otherCostYear', align: 'center', title: '当年实绩', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                ]
            ],onPostBody:function (data) {
                frameHappenTotal += parseInt($("#budget").text())
            }
        };
        $.table.init(options);


        var options1 = {// 联络单费用
            id: "bootstrap-tableContact",
            url: prefix + "/costContact",
            detailUrl: ctx + "zzll/contactList/detail/{id}",
            showFooter: true, pagination: false,
            showSearch: false, showRefresh: false, showToggle: false, showColumns: false,
            modalName: "联络单费用",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.year = frameNum.substring(0, 4);
                search.tgfDept = frameNum.substring(4, 8);
                search.srfDept = frameNum.substring(9, 13);
                return search;
            },
            columns:
                [
                    {
                        field: 'index', valign: 'middle', title: "序号", formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {
                        field: 'projectNum', valign: 'middle', title: '联络单号', formatter: function (value, row, index) {
                            return '<a onclick="$.operate.detailTab(\'' + row.listId + '\')">'+value+'</a>';
                        }
                    },
                    {field: 'projectName', valign: 'middle', title: '项目名称'},
                    {field: 'tgfDeptName', valign: 'middle', title: '推广部门'},
                    {field: 'srfDeptName', valign: 'middle', title: '受让部门'},
                    {
                        field: 'travelCostActual', align: 'center', title: '差旅费', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'comCostActual', align: 'center', title: '通讯费', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'laborCostActual', align: 'center', title: '人工费', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'otherCostActual', align: 'center', title: '其它费', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'managementCost', valign: 'middle', title: '管理费', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }
                    },
                    {
                        field: 'subtotal', valign: 'middle', title: '小计', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }, footerFormatter: function (value) {
                            var total = 0;
                            for (var i in value) {
                                var subtotal = parseFloat(value[i].subtotal);
                                if (Number.isNaN(subtotal)) {
                                    subtotal = 0;
                                }
                                total += subtotal;
                            }
                            return '<span id="contact">' + total + '</span>';
                        }
                    }
                ],onPostBody:function (data) {
                frameHappenTotal += parseFloat($("#contact").text());
                $("#frameHappenTotal").text(frameHappenTotal);
            }
        };
        $.table.init(options1);


        var options2 = {// 开票费用
            id: "bootstrap-tableBilling",
            url: ctx + "mpkt/kbInfo/list",
            showFooter: true, pagination: false,
            showSearch: false, showRefresh: false, showToggle: false, showColumns: false,
            modalName: "开票费用",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                var frameNum = [[${frameEx.frameNum}]];
                search.kbinfoXmbh = frameNum;
                return search;
            },
            columns: [
                [
                    {
                        field: 'index', valign: 'middle', title: "序号", formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {field: 'kbinfoHtbh', valign: 'middle', title: '清单号'},
                    {field: 'accountDate', valign: 'middle', title: '会计期'},
                    {
                        field: 'invoiceNoTax', valign: 'middle', title: '费用', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }, footerFormatter: function (value) {
                            var total = 0;
                            for (var i in value) {
                                var invoiceNoTax = parseFloat(value[i].invoiceNoTax);
                                if (Number.isNaN(invoiceNoTax)) {
                                    invoiceNoTax = 0;
                                }
                                total += invoiceNoTax;
                            }
                            return '<span id="billing">' + total + '</span>';
                        }
                    }
                ]
            ],onPostBody:function (data) {
                frameOpenTotal = parseFloat($("#billing").text());
                $("#frameOpenTotal").text(frameOpenTotal);
                frameTotal = frameLastTotal + frameOpenTotal - frameHappenTotal;
                $("#frameTotal").text(frameTotal)
            }
        };
        $.table.init(options2);

    });


</script>
</body>
</html>
