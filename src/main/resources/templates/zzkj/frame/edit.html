<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改框架协议')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-frame-add" th:object="${frameEx}">
        <input th:field="*{frameId}" type="hidden">
        <input th:field="*{processInstanceId}" type="hidden">
        <input th:field="*{activityCode}" type="hidden">
        <input id="taskId" name="taskId" th:value="${taskId}" type="hidden">
        <input id="businessGuid" name="businessGuid" th:value="${businessGuid}" type="hidden">
        <!-- 基本信息 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#1">基本信息
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                <div class="panel-body">

                    <div class="row form-group">
                        <label class="col-sm-2 control-label">协议号：</label>
                        <div class="col-sm-10">
                            <input class="form-control" th:field="*{frameNum}" type="hidden">
                            <div class="form-control-static" th:text="*{frameNum}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <label class="col-sm-2 control-label">合同号：</label>
                        <div class="col-sm-10">
                            <input class="form-control" th:field="*{extra2}" type="hidden">
                            <div class="form-control-static" th:if="*{not #strings.isEmpty(extra2)}"
                                 th:text="*{extra2}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <label class="col-sm-2 control-label is-required">协议名称：</label>
                        <div class="col-sm-10">
                            <div class="form-control-static" th:text="*{frameName}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">协议年度：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{frameYear}"></div>
                            </div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">项目范围：</label>
                            <div class="col-sm-8"
                                 th:include="/component/select :: init(id='frameArea', name='frameArea',businessType=*{constants.BUSINESS_TYPE_KTTG}, dictCode=*{constants.PROJECT_AREA},isfirst=true,value=*{frameArea},see=*{!#strings.isEmpty(processInstanceId)}?true:false)"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">协议受让方：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectOrg :: init(orgCodeId='frameSrfCode',orgNameId='frameSrfName',selectType='S',value=*{frameSrfCode},see=*{!#strings.isEmpty(processInstanceId)}?true:false,callback='selectSrf')"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">协议推广方：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectOrg :: init(orgCodeId='frameTgfCode',orgNameId='frameTgfName',selectType='S',value=*{frameTgfCode},see=*{!#strings.isEmpty(processInstanceId)}?true:false,callback='selectTgf')"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">起草人：</label>
                            <input class="form-control" th:field="*{editUserCode}" type="hidden">
                            <input class="form-control" th:field="*{editUserName}" type="hidden">
                            <div class="form-control-static" th:text="*{editUserName}"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">起草日期：</label>
                            <div class="col-sm-8"
                                 th:include="/component/date :: init(id='editDate', name='editDate',strValue=*{editDate} ,see=true)"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 基本信息 End -->

        <!-- 年度计划列表 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#2">年度计划列表
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                <div class="panel-body">
                    <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                        <i class="fa fa-download"></i> 导出
                    </a>
                    <table id="bootstrap-tableP"></table>
                </div>
            </div>
        </div>
        <!-- 年度计划列表 End -->

        <!-- 费用 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#3">费用（万元）
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="3">
                <div class="panel-body">

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">上一年期初：</label>
                            <input th:field="*{frameLastTotal}" type="hidden">
                            <div class="form-control-static" th:text="*{#numbers.formatDecimal((frameLastTotal/10000),1,2)}"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">上一年实际到账：</label>
                            <input th:field="*{frameOpenTotal}" type="hidden">
                            <div class="form-control-static" th:text="*{#numbers.formatDecimal((frameOpenTotal/10000),1,2)}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">上一年实际发生：</label>
                            <input th:field="*{frameHappenTotal}" type="hidden">
                            <div class="form-control-static" th:text="*{#numbers.formatDecimal((frameHappenTotal/10000),1,2)}"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">当前期末：</label>
                            <input th:field="*{frameTotal}" type="hidden">
                            <div class="form-control-static" th:text="*{#numbers.formatDecimal((frameTotal/10000),1,2)}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">框架协议总估算：</label>
                            <div class="col-sm-8">
                                <input th:field="*{extra1}" type="hidden">
                                <div class="form-control-static" th:text="*{extra1}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 费用 End -->

        <div th:if="*{#strings.substring(activityCode,6).matches('^(([7-9])|([2-9]\d)|([1-9]\d{1,}))$')}">
            <div class="panel panel-default panel-group">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                           href="#4">框架协议草稿版附件
                            <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                        </a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="4">
                    <div class="panel-body">
                        <div class="row row form-group">
                            <div th:include="/component/attachment :: init(display='none',name=*{constants.ATT_FRAME_DRAFT},id=*{constants.ATT_FRAME_DRAFT},sourceId=*{frameId},sourceModule=*{constants.BUSINESS_TYPE},sourceLabel1=*{constants.ATT_FRAME_DRAFT},see=*{#strings.substring(activityCode,6).matches('^(([8-9])|([2-9]\\d)|([1-9]\\d{1,}))$')})">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div th:if="*{activityCode eq 'Manual8'}">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                               href="#5">框架协议正式版附件
                                <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                            </a>
                        </h4>
                    </div>
                </div>
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="5">
                    <div class="panel-body">
                        <div class="row row form-group">
                            <div th:include="/component/attachment :: init(display='none',name=*{constants.ATT_FRAME_FORMAL},id=*{constants.ATT_FRAME_FORMAL},sourceId=*{frameId},sourceModule=*{constants.BUSINESS_TYPE},sourceLabel1=*{constants.ATT_FRAME_FORMAL})">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 审批-->
        <div th:if="*{! #strings.isEmpty(processInstanceId)}"
             th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=*{processInstanceId},required='true',freeFlow='false')"></div>
        <!-- 审批历史 -->
        <div th:if="*{! #strings.isEmpty(processInstanceId)}"
             th:include="component/wfCommentList::init(processInstanceId=*{processInstanceId})"></div>
        <div class="m">
            <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
        </div>
    </form>
</div>
<div class="row">
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button class="btn btn-sm btn-primary" th:onclick="openProcessTrack([[${frameEx.processInstanceId}]])"
                type="button">
            <i class="fa fa-eye"></i>&nbsp;流程跟踪图
        </button>
        <button th:if="${activityCode eq 'Manual7' || activityCode eq 'Manual8'}" class="btn btn-sm btn-primary" onclick="exportFrame()"
                type="button">
            <i class="fa fa-download"></i>&nbsp;合同导出
        </button>
        <th:block th:if="${! #strings.isEmpty(taskId)}"
                  th:include="/component/wfSubmitOne :: init(taskId=${taskId},callback='submitProcess')"/>
        <th:block th:if="${! #strings.isEmpty(taskId)}"
                  th:include="/component/wfReturn :: init(taskId=${taskId},callback=wfReturn)"/>
        <!--        <button class="btn btn-sm btn-primary" th:onclick="openProcessTrack([[${frameEx.processInstanceId}]])" type="button">-->
        <!--            <i class="fa fa-eye"></i>&nbsp;查看协议项目清单-->
        <!--        </button>-->
        <button class="btn btn-sm btn-danger" onclick="closeItem()" type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzkj/frame";
    var projectTypeData = [[${@dict.getDictList(frameEx.constants.BUSINESS_TYPE_KTTG,frameEx.constants.PROJECT_TYPE)}]];

    $("#form-frame-edit").validate({
        focusCleanup: true
    });

    /**
     * 初始化数据
     */
    $(function () {
        var options = {// 目前技术经济指标、产品、质量等情况
            id: "bootstrap-tableP",
            url: prefix + "/planNeedPage",
            detailUrl: ctxKY + "web/KYKTTGPD02?needId={id}",
            exportUrl: prefix+"/exportPlanNeed/"+[[${frameEx.frameId}]],
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            modalName: "年度计划",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.frameId = [[${frameEx.frameId}]];
                return search;
            },
            columns: [
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'projectName',
                    align: 'center',
                    title: '项目名称'
                },
                {
                    field: 'projectType',
                    align: 'center',
                    title: '项目类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(projectTypeData, value);
                    }
                },
                {
                    field: 'tcrUserName',
                    align: 'center',
                    title: '提出人'
                },
                {
                    field: 'srfDeptName',
                    align: 'center',
                    title: '受让单位部门'
                },
                {
                    field: 'promotionDeptName',
                    align: 'center',
                    title: '推广单位部门'
                },
                {
                    align: 'center',
                    title: '操作',
                    formatter: function (value, row, index) {
                        var actions = [];
                        // actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.frameId + '\')"><i class="fa fa-eye"></i>查看明细</a> ');
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.modal.openTab(\'推广需求\', \'' + ctxKY + 'web/KYKTTGPD02?needId=' + row.needId + '\')"><i class="fa fa-eye"></i>查看明细</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function exportNeed(frameId) {
        $.ajax({
            url: prefix+"/exportPlanNeed/frameId="+frameId,
            dataType: "json",
            contentType: "application/json",
            type: 'POST',
            success: function (result) {
                $.modal.closeLoading();
            }
        });
    }

    /**
     * 提交流程
     */
    function submitProcess() {
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function () {
                var data = $('#form-frame-add').serialize();
                $.operate.saveTabAlert(prefix + "/submitProcess", data);
            });
        }
    }

    //流程跟踪
    function openProcessTrack(processInstanceId) {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processInstanceId);
    }

    function exportFrame() {
        var frameId = $("#frameId").val();
        var url = prefix + "/exportFrame/"+frameId;
        window.open(url);
    }

    //退回流程
    function wfReturn(activityKey) {
        if ($.validate.form()) {
            var data = $('#form-frame-add').serialize() + "&activityKey=" + activityKey;
            $.operate.saveTabAlert(ctx + "mpwf/flowInfo/returnFlow", data);
        }
    }

</script>
</body>
</html>
