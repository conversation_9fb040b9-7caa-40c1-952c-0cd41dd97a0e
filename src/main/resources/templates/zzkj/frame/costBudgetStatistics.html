<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('框架协议费用明细统计')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">起始日期：</label>
                        <div class="col-sm-8"
                             th:include="/component/date :: init(id='applicationDateStar', name='applicationDateStar',isrequired=true)"></div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">截止日期：</label>
                        <div class="col-sm-8"
                             th:include="/component/date :: init(id='applicationDateEnd', name='applicationDateEnd',isrequired=true)"></div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">项目范围：</label>
                        <div class="col-sm-8"
                             th:include="/component/radio :: init(id='projectArea',name='projectArea',businessType='KTTG',dictCode='projectArea')"></div>
                    </div>
                </div>

                <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">协议受让方：</label>
                        <div class="col-sm-8"
                             th:include="/component/selectOrg :: init(orgCodeId='frameSrfCode',orgNameId='frameSrfName',selectType='S')"></div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">协议推广方：</label>
                        <div class="col-sm-8"
                             th:include="/component/selectOrg :: init(orgCodeId='frameTgfCode',orgNameId='frameTgfName',selectType='S')"></div>
                    </div>
                </div>
                <div class="select-list">
                    <ul>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="searchList()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-primary btn-rounded" onclick="exportList()">
                                <i class="fa fa-download"></i> 导出
                            </a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzkj/frame";

    /**
     * 初始化数据
     */
    $(function () {
        var options = {// 联络单费用
            id: "bootstrap-table",
            url: prefix + "/costBudgetStatistics",
            detailUrl: ctx + "zzlx/main/detail?mainId={id}",
            showFooter: true, pagination: false,firstLoad:false,
            showSearch: false, showRefresh: false, showToggle: false, showColumns: false,
            modalName: "框架协议费用明细统计",
            columns:
                [
                    {
                        field: 'index', valign: 'middle', title: "序号", formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {field: 'tgfDWdeptName', valign: 'middle', title: '推广方'},
                    {field: 'srfDWdeptName', valign: 'middle', title: '受让方'},
                    {field: 'year', valign: 'middle', title: '费用年度'},
                    {
                        field: 'projectNum', valign: 'middle', title: '项目号', formatter: function (value, row, index) {
                            return '<a onclick="$.operate.detailTab(\'' + row.mainId + '\')">'+value+'</a>';
                        }
                    },
                    {field: 'projectName', valign: 'middle', title: '项目名称'},
                    {field: 'type', valign: 'middle', title: '费用发生来源'},
                    {field: 'tgfFzr', valign: 'middle', title: '申请人'},
                    {field: 'tgfDeptName', valign: 'middle', title: '推广部门'},
                    {field: 'srfDeptName', valign: 'middle', title: '受让部门'},
                    {field: 'bzdh', valign: 'middle', title: '报支单号'},
                    {field: 'applicationDate', valign: 'middle', title: '申请日期'},
                    {field: 'submitDate', valign: 'middle', title: '批准日期'},
                    {
                        field: 'travelCostActual', align: 'center', title: '差旅费', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }, footerFormatter: function (value) {
                            var total = 0;
                            for (var i in value) {
                                var travelCostActual = parseFloat(value[i].travelCostActual);
                                if (Number.isNaN(travelCostActual)) {
                                    travelCostActual = 0;
                                }
                                total += travelCostActual;
                            }
                            return '<span>' + total + '</span>';
                        }
                    },
                    {
                        field: 'laborCostActual', align: 'center', title: '人工费', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }, footerFormatter: function (value) {
                            var total = 0;
                            for (var i in value) {
                                var laborCostActual = parseFloat(value[i].laborCostActual);
                                if (Number.isNaN(laborCostActual)) {
                                    laborCostActual = 0;
                                }
                                total += laborCostActual;
                            }
                            return '<span>' + total + '</span>';
                        }
                    },
                    {
                        field: 'otherCostActual', align: 'center', title: '其它费', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }, footerFormatter: function (value) {
                            var total = 0;
                            for (var i in value) {
                                var otherCostActual = parseFloat(value[i].otherCostActual);
                                if (Number.isNaN(otherCostActual)) {
                                    otherCostActual = 0;
                                }
                                total += otherCostActual;
                            }
                            return '<span>' + total + '</span>';
                        }
                    },
                    {
                        field: 'managementCost', valign: 'middle', title: '管理费', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }, footerFormatter: function (value) {
                            var total = 0;
                            for (var i in value) {
                                var managementCost = parseFloat(value[i].managementCost);
                                if (Number.isNaN(managementCost)) {
                                    managementCost = 0;
                                }
                                total += managementCost;
                            }
                            return '<span>' + total + '</span>';
                        }
                    },
                    {
                        field: 'subtotal', valign: 'middle', title: '小计', formatter: function (value, row, index) {
                            return $.common.isEmpty(value) ? 0 : value;
                        }, footerFormatter: function (value) {
                            var total = 0;
                            for (var i in value) {
                                var subtotal = parseFloat(value[i].subtotal);
                                if (Number.isNaN(subtotal)) {
                                    subtotal = 0;
                                }
                                total += subtotal;
                            }
                            return '<span>' + total + '</span>';
                        }
                    }
                ]
        };
        $.table.init(options);
    });

function searchList() {
    if($.common.isEmpty($("#applicationDateStar").val())){
        $.modal.msgWarning("请选择起始日期！")
        return;
    }
    if($.common.isEmpty($("#applicationDateEnd").val())){
        $.modal.msgWarning("请选择截止日期！")
        return;
    }
    $.table.search();
}
    function exportList() {
        if($.common.isEmpty($("#applicationDateStar").val())){
            $.modal.msgWarning("请选择起始日期！")
            return;
        }
        if($.common.isEmpty($("#applicationDateEnd").val())){
            $.modal.msgWarning("请选择截止日期！")
            return;
        }
        $.modal.confirm("确定导出所有数据吗？", function () {
            $.modal.loading("正在导出数据，请稍后...");
            $.ajax({
                url: ctx + "zzkj/export/project",
                data: $("#formId").serialize() + "&fileName=框架协议费用明细统计",
                dataType: "json",
                type: 'POST',
                success: function (result) {
                    if (result.code === web_status.SUCCESS) {
                        window.location.href = ctx + "common/download?fileName=" + encodeURI(result.msg) + "&delete=" + true;
                    } else if (result.code === web_status.WARNING) {
                        $.modal.alertWarning(result.msg)
                    } else {
                        $.modal.alertError(result.msg);
                    }
                    $.modal.closeLoading();
                }
            });

        });
    }
</script>
</body>
</html>
