<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('框架协议项目清单')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-frame-add" th:object="${frameEx}">
        <div aria-multiselectable="true" id="accordion" role="tablist">
            <!-- 基本信息 Start -->
            <div class="panel panel-default panel-group">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#1">基本信息
                            <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                        </a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                    <div class="panel-body">

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">协议号：</label>
                                <div class="col-sm-8">
                                    <div class="form-control-static" th:text="*{frameNum}"></div>
                                </div>
                            </div>

                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">项目范围：</label>
                                <div class="col-sm-8"
                                     th:include="/component/select :: init(id='frameArea', name='frameArea',businessType='KTTG', dictCode='projectArea',isfirst=true,value=*{frameArea},see=true)"></div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <label class="col-sm-2 control-label">协议名称：</label>
                            <div class="col-sm-10">
                                <div class="form-control-static" th:text="*{frameName}"></div>
                            </div>
                        </div>

                        <div class="row form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">协议受让方：</label>
                                <div class="col-sm-8"
                                     th:include="/component/selectOrg :: init(orgCodeId='frameSrfCode',orgNameId='frameSrfName',selectType='S',value=*{frameSrfCode},see=true)"></div>
                            </div>

                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">协议推广方：</label>
                                <div class="col-sm-8"
                                     th:include="/component/selectOrg :: init(orgCodeId='frameTgfCode',orgNameId='frameTgfName',selectType='S',value=*{frameTgfCode},see=true)"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 基本信息 End -->

            <!-- 项目费用列表 Start -->
            <div class="panel panel-default panel-group">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#2">一、项目费用
                            <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                        </a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                    <div class="panel-body">
                        <table id="bootstrap-tableCost"></table>
                    </div>
                </div>
            </div>
            <!-- 项目费用列表 End -->

            <!-- 联络单费用列表 Start -->
            <div class="panel panel-default panel-group">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#3">二、联络单费用
                            <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                        </a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="3">
                    <div class="panel-body">
                        <table id="bootstrap-tableContact"></table>
                    </div>
                </div>
            </div>
            <!-- 联络单费用列表 End -->
        </div>
        <div class="panel"></div>
    </form>
</div>
<div class="row">
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button class="btn btn-sm btn-danger" onclick="closeItem()" type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzkj/frame";

    /**
     * 初始化数据
     */
    $(function () {
        var frameNum = [[${frameEx.frameNum}]];
        var options = {// 项目费用
            id: "bootstrap-tableCost",
            url: prefix + "/costBudget",
            detailUrl: ctx + "zzlx/main/detail?mainId={id}",
            showFooter: false, pagination: false,
            showSearch: false, showRefresh: false, showToggle: false, showColumns: false,
            modalName: "项目费用",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.year = frameNum.substring(0, 4);
                search.tgfDept = frameNum.substring(4, 8);
                search.srfDept = frameNum.substring(9, 13);
                return search;
            },
            columns:
                [
                    {
                        field: 'index', valign: 'middle', title: "序号", formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {
                        field: 'projectNum', valign: 'middle', title: '项目号', formatter: function (value, row, index) {
                            return '<a onclick="$.operate.detailTab(\'' + row.mainId + '\')">'+value+'</a>';
                        }
                    },
                    {field: 'projectName', valign: 'middle', title: '项目名称'},
                    {field: 'tgfDeptName', valign: 'middle', title: '推广部门'},
                    {field: 'tgfFzr', valign: 'middle', title: '推广方负责人'},
                    {field: 'tgfXmzg', valign: 'middle', title: '推广方项目主管'},
                    {field: 'srfDeptName', valign: 'middle', title: '受让部门'},
                    {field: 'srfFzr', valign: 'middle', title: '受让方负责人'},
                    {field: 'srfXmzg', valign: 'middle', title: '受让方项目主管'}
                ]
        };
         $.table.init(options);

        var options1 = {// 联络单费用
            id: "bootstrap-tableContact",
            url: prefix + "/costContact",
            detailUrl: ctx + "zzll/contactList/detail/{id}",
            showFooter: false, pagination: false,
            showSearch: false, showRefresh: false, showToggle: false, showColumns: false,
            modalName: "联络单费用",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.year = frameNum.substring(0, 4);
                search.tgfDept = frameNum.substring(4, 8);
                search.srfDept = frameNum.substring(9, 13);
                return search;
            },
            columns:
                [
                    {
                        field: 'index', valign: 'middle', title: "序号", formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {
                        field: 'projectNum', valign: 'middle', title: '联络单号', formatter: function (value, row, index) {
                            return '<a onclick="$.operate.detailTab(\'' + row.listId + '\')">'+value+'</a>';
                        }
                    },
                    {field: 'projectName', valign: 'middle', title: '项目名称'},
                    {field: 'tgfDeptName', valign: 'middle', title: '推广部门'},
                    {field: 'tgfFzr', valign: 'middle', title: '推广方负责人'},
                    {field: 'tgfXmzg', valign: 'middle', title: '推广方项目主管'},
                    {field: 'srfDeptName', valign: 'middle', title: '受让部门'},
                    {field: 'srfFzr', valign: 'middle', title: '受让方负责人'},
                    {field: 'srfXmzg', valign: 'middle', title: '受让方项目主管'}
                ]

        };
        $.table.init(options1);
    });


</script>
</body>
</html>
