<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('修改框架协议')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-frame-add" th:object="${frameEx}">
        <!-- 基本信息 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#1">基本信息
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                <div class="panel-body">

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-2 control-label">协议号：</label>
                            <div class="col-sm-10">
                                <div class="form-control-static" th:text="*{frameNum}"></div>
                            </div>
                        </div>
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">项目范围：</label>
                            <div class="col-sm-8"
                                 th:include="/component/select :: init(id='frameArea', name='frameArea',businessType=*{constants.BUSINESS_TYPE_KTTG}, dictCode=*{constants.PROJECT_AREA},isfirst=true,value=*{frameArea},see=*{!#strings.isEmpty(processInstanceId)}?true:false)"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <label class="col-sm-2 control-label is-required">协议名称：</label>
                        <div class="col-sm-10">
                            <div class="form-control-static" th:text="*{frameName}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">协议受让方：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectOrg :: init(orgCodeId='frameSrfCode',orgNameId='frameSrfName',selectType='S',value=*{frameSrfCode},see=*{!#strings.isEmpty(processInstanceId)}?true:false,callback='selectSrf')"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">协议推广方：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectOrg :: init(orgCodeId='frameTgfCode',orgNameId='frameTgfName',selectType='S',value=*{frameTgfCode},see=*{!#strings.isEmpty(processInstanceId)}?true:false,callback='selectTgf')"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 基本信息 End -->

        <!-- 年度计划列表 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#2">年度计划列表
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                <div class="panel-body">
                    <table id="bootstrap-tableP"></table>
                </div>
            </div>
        </div>
        <!-- 年度计划列表 End -->

    </form>
</div>
<div class="row">
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button class="btn btn-sm btn-danger" onclick="closeItem()" type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzkj/frame";
    var projectTypeData = [[${@dict.getDictList(frameEx.constants.BUSINESS_TYPE_KTTG,frameEx.constants.PROJECT_TYPE)}]];

    /**
     * 初始化数据
     */
    $(function () {
        var options = {// 目前技术经济指标、产品、质量等情况
            id: "bootstrap-tableP",
            url: prefix + "/planNeedPage",
            detailUrl: ctxKY + "web/KYKTTGPD02?needId={id}",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            modalName: "年度计划",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.frameId = [[${frameEx.frameId}]];
                return search;
            },
            columns: [
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'projectName',
                    align: 'center',
                    title: '项目名称'
                },
                {
                    field: 'projectType',
                    align: 'center',
                    title: '项目类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(projectTypeData, value);
                    }
                },
                {
                    field: 'tcrUserName',
                    align: 'center',
                    title: '提出人'
                },
                {
                    field: 'srfDeptName',
                    align: 'center',
                    title: '受让单位部门'
                },
                {
                    field: 'promotionDeptName',
                    align: 'center',
                    title: '推广单位部门'
                },
                {
                    align: 'center',
                    title: '操作',
                    formatter: function (value, row, index) {
                        var actions = [];
                        // actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.frameId + '\')"><i class="fa fa-eye"></i>查看明细</a> ');
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.modal.openTab(\'推广需求\', \'' + ctxKY + 'web/KYKTTGPD02?needId=' + row.needId + '\')"><i class="fa fa-eye"></i>查看明细</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });


</script>
</body>
</html>