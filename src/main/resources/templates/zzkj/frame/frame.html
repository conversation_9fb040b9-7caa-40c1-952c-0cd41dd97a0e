<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('框架协议列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <input th:field="${queryType}" type="hidden">
                <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">协议号：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="frameNum" type="text">
                        </div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">协议名称：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="frameName" type="text">
                        </div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">项目范围：</label>
                        <div class="col-sm-8"
                             th:include="/component/select :: init(labelName='项目范围：',id='frameArea', name='frameArea',businessType=${constants.BUSINESS_TYPE_KTTG}, dictCode=${constants.PROJECT_AREA},isfirst=true)"></div>
                    </div>
                </div>

                <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">协议受让方：</label>
                        <div class="col-sm-8"
                             th:include="/component/selectOrg :: init(orgCodeId='frameSrfCode',orgNameId='frameSrfName',selectType='S')"></div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">协议推广方：</label>
                        <div class="col-sm-8"
                             th:include="/component/selectOrg :: init(orgCodeId='frameTgfCode',orgNameId='frameTgfName',selectType='S')"></div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">协议年度：</label>
                        <div class="col-sm-8"
                             th:include="/component/date :: init(id='editDate', name='editDate',minView=4 ,isrequired=true)"></div>
                    </div>
                </div>

                <div class="row form-group">

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">编制人：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="editUserName" type="text">
                        </div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">编制日期：</label>
                        <div class="col-sm-8"
                             th:include="/component/date :: init(id='editDate', name='editDate',isrequired=true)"></div>
                    </div>

                </div>
                <div class="select-list">
                    <ul>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group" th:if="${queryType} eq 'list'">
            <a class="btn btn-success" onclick="$.operate.addTab()">
                <i class="fa fa-plus"></i> 添加
            </a>
            <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()">
                <i class="fa fa-remove"></i> 删除
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzkj/frame";
    var projectArea = [[${@dict.getDictList(constants.BUSINESS_TYPE_KTTG,constants.PROJECT_AREA)}]];

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/add?frameId={id}",
            detailUrl: prefix + "/detail/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "框架协议",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.queryType = [[${queryType}]];
                return search;
            },
            columns: [{
                checkbox: true
            },
                {
                    field: 'frameId',
                    title: '框架协议主键',
                    visible: false
                },
                {
                    field: 'frameYear',
                    title: '协议年度'
                },
                {
                    field: 'frameNum',
                    title: '协议号'
                },
                {
                    field: 'frameName',
                    title: '协议名称'
                },
                {
                    field: 'frameArea',
                    title: '项目范围',
                    formatter: function (value, row, index) {
                        return $("#frameArea option[value='" + value + "']").text();
                    }
                },
                {
                    field: 'frameTgfName',
                    title: '推广单位部门'
                },
                {
                    field: 'frameSrfName',
                    title: '受让单位部门'
                },
                {
                    field: 'activityName',
                    title: '状态'
                },
                {
                    field: 'currentOperator',
                    title: '操作人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.frameId + '\')"><i class="fa fa-edit"></i>查看</a> ');
                        if ([[${queryType}]] === 'list') {
                            actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.frameId + '\')"><i class="fa fa-remove"></i>编辑</a>');
                            actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.frameId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);

        if ([[${queryType}]] === 'list') {
            $.table.hideColumn('activityName')
            $.table.hideColumn('currentOperator')
        }
    });

</script>
</body>
</html>