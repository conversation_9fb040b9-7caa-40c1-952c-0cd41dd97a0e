<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增框架协议')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form id="form"></form>
    <form class="form-horizontal m" id="form-frame-add" th:object="${frameEx}">
        <input th:field="*{frameId}" type="hidden">
        <input th:field="*{processInstanceId}" type="hidden">
        <input th:field="*{activityCode}" type="hidden">
        <input id="taskId" name="taskId" th:value="${taskId}" type="hidden">
        <input id="businessGuid" name="businessGuid" th:value="${businessGuid}" type="hidden">
        <input id="transitionKey" name="transitionKey" type="hidden">
        <!-- 基本信息 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#1">基本信息
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                <div class="panel-body">

                    <div class="row form-group">
                        <label class="col-sm-2 control-label">协议号：</label>
                        <div class="col-sm-10">
                            <input th:field="*{frameNum}" type="hidden">
                            <div class="form-control-static" th:if="*{#strings.isEmpty(frameNum)}"
                                 th:text="协议提交后系统自动生成"></div>
                            <div class="form-control-static" th:if="*{not #strings.isEmpty(frameNum)}"
                                 th:text="*{frameNum}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <label class="col-sm-2 control-label">合同号：</label>
                        <div class="col-sm-10">
                            <input class="form-control" th:field="*{extra2}" type="hidden">
                            <div class="form-control-static" th:if="*{#strings.isEmpty(extra2)}"
                                 th:text="协议提交后系统自动生成"></div>
                            <div class="form-control-static" th:if="*{not #strings.isEmpty(extra2)}"
                                 th:text="*{extra2}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <label class="col-sm-2 control-label is-required">协议名称：</label>
                        <div class="col-sm-10">
                            <input autocomplete="off" class="form-control width100" required th:field="*{frameName}"
                                   type="text">
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">协议年度：</label>
                            <div class="col-sm-8">
                                <select class="form-control" id="frameYear" name="frameYear"
                                        th:if="*{#strings.isEmpty(processInstanceId)}">
                                    <option value="">请选择</option>
                                </select>
                                <div class="form-control-static" th:if="*{!#strings.isEmpty(processInstanceId)}"
                                     th:text="*{frameYear}"></div>
                            </div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">项目范围：</label>
                            <div class="col-sm-8"
                                 th:include="/component/select :: init(id='frameArea', name='frameArea',businessType=*{constants.BUSINESS_TYPE_KTTG}, dictCode=*{constants.PROJECT_AREA},isfirst=true,value=*{frameArea},see=*{!#strings.isEmpty(processInstanceId)}?true:false)"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">协议受让方：</label>
                            <div class="col-sm-8" th:if="*{#strings.isEmpty(processInstanceId)}">
                                <input class="form-control" th:field="*{frameSrfName}" type="hidden">
                                <select class="form-control" id="frameSrfCode" name="frameSrfCode" th:with="dictData=${T(com.baosight.bscdkj.mp.ad.utils.RoleUtil).getOrgByRoleCode(frameEx.constants.ZZZC_XMZG)}">
                                    <option value="">请选择</option>
                                    <option th:each="data : ${dictData}" th:selected="${frameEx.frameSrfCode eq data.orgCode}" th:text="${data.orgName}"
                                            th:value="${data.orgCode}"></option>
                                </select>
                            </div>
                            <div class="col-sm-8" th:if="*{!#strings.isEmpty(processInstanceId)}">
                                <input th:field="*{frameSrfCode}" type="hidden">
                                <div class="form-control-static" th:text="*{frameSrfName}"></div>
                            </div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">协议推广方：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectOrg :: init(orgCodeId='frameTgfCode',orgNameId='frameTgfName',selectType='S',value=*{frameTgfCode},see=*{!#strings.isEmpty(processInstanceId)}?true:false,see=true)"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">起草人：</label>
                            <input class="form-control" th:field="*{editUserCode}" type="hidden">
                            <input class="form-control" th:field="*{editUserName}" type="hidden">
                            <div class="form-control-static" th:text="*{editUserName}"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">起草日期：</label>
                            <div class="col-sm-8"
                                 th:include="/component/date :: init(id='editDate', name='editDate',strValue=*{editDate} ,isrequired=true)"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 基本信息 End -->

        <!-- 年度计划列表 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#2">年度计划列表
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                <div class="panel-body">
                    <table id="bootstrap-tableP"></table>
                </div>
            </div>
        </div>
        <!-- 年度计划列表 End -->

        <!-- 费用 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#3">费用（万元）
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="3">
                <div class="panel-body">

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">上一年期初：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{#numbers.formatDecimal((frameLastTotal/10000),1,2)}"></div>
<!--                                <input autocomplete="off" class="form-control" min="0" required th:field="*{frameLastTotal}"-->
<!--                                       type="number">-->
                            </div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">上一年实际到账：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{#numbers.formatDecimal((frameOpenTotal/10000),1,2)}"></div>
<!--                                <input autocomplete="off" class="form-control" min="0" required th:field="*{frameOpenTotal}"-->
<!--                                       type="number">-->
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">上一年实际发生：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{#numbers.formatDecimal((frameHappenTotal/10000),1,2)}"></div>
<!--                                <input autocomplete="off" class="form-control" min="0" required th:field="*{frameHappenTotal}"-->
<!--                                       type="number">-->
                            </div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">当前期末：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{#numbers.formatDecimal((frameTotal/10000),1,2)}"></div>
<!--                                <input autocomplete="off" class="form-control" min="0" required th:field="*{frameTotal}"-->
<!--                                       type="number">-->
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">框架协议总估算：</label>
                            <div class="col-sm-8">
                                <input autocomplete="off" class="form-control" min="0" required th:field="*{extra1}"
                                       type="number">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 费用 End -->
    </form>
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
    </div>
</div>
<div class="row">
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button class="btn btn-sm btn-primary" onclick="saveHandler()" th:if="${#strings.isEmpty(taskId)}"
                type="button">
            <i class="fa fa-check"></i>保 存
        </button>
        <button class="btn btn-sm btn-primary" onclick="submitHandler()" th:if="${#strings.isEmpty(taskId)}"
                type="button">
            <i class="fa fa-check"></i>提 交
        </button>
        <th:block th:if="${! #strings.isEmpty(taskId)}"
                  th:include="/component/wfSubmitOne :: init(taskId=${taskId},callback='submitProcess')"></th:block>
        <button class="btn btn-sm btn-danger" onclick="closeItem()"
                type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>


<script th:src="@{/zzzc/js/common.js}"></script>
<script th:inline="javascript">
    var prefix = ctx + "zzkj/frame"
    var frameTgfCode = [[${frameEx.frameTgfCode}]];
    var frameSrfCode = [[${frameEx.frameSrfCode}]];
    var frameId = [[${frameEx.frameId}]];
    var projectTypeData = [[${@dict.getDictList('KTTG','projectType')}]];

    $("#form-frame-add").validate({
        focusCleanup: true
    });

    $('#frameYear').select2({
        data: [new Date().getFullYear(), new Date().getFullYear() + 1]
    });
    if ($.common.isNotEmpty([[${frameEx.frameYear}]])) {
        console.log([[${frameEx.frameYear}]].split(","))
        $('#frameYear').select2("val", [[${frameEx.frameYear}]].split(","));
    }
    /**
     * 初始化数据
     */
    $(function () {
        var options = {// 目前技术经济指标、产品、质量等情况
            id: "bootstrap-tableP",
            url: prefix + "/planNeedPage",
            detailUrl: ctxKY + "web/KYKTTGPD02?needId={id}",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            modalName: "年度计划",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                if ($.common.isNotEmpty([[${frameEx.processInstanceId}]])) {
                    search.frameId = frameId;
                } else if ($.common.isNotEmpty(frameTgfCode) && $.common.isNotEmpty(frameSrfCode)) {
                    search.promotionDeptCode = frameTgfCode;
                    search.srfDeptCode = frameSrfCode;
                }
                return search;
            },
            columns: [
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnId = $.common.sprintf("<input type='hidden' name='needIds[%s]' value='%s'>", index, row.needId);
                        return $.table.serialNumber(index) + columnId;
                    }
                },
                {
                    field: 'needId',
                    visible: false
                },
                {
                    field: 'projectName',
                    align: 'center',
                    title: '项目名称'
                },
                {
                    field: 'projectType',
                    align: 'center',
                    title: '项目类型',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(projectTypeData, value);
                    }
                },
                {
                    field: 'tcrUserName',
                    align: 'center',
                    title: '提出人'
                },
                {
                    field: 'srfDeptName',
                    align: 'center',
                    title: '受让单位部门'
                },
                {
                    field: 'promotionDeptName',
                    align: 'center',
                    title: '推广单位部门'
                },
                {
                    align: 'center',
                    title: '操作',
                    formatter: function (value, row, index) {
                        var actions = [];
                        // actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.needId + '\')"><i class="fa fa-eye"></i>查看明细</a> ');
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.modal.openTab(\'推广需求\', \'' + ctxKY + 'web/KYKTTGPD02?needId=' + row.needId + '\')"><i class="fa fa-eye"></i>查看明细</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    $('#frameSrfCode').on('select2:select', function (e) {
        $("#frameSrfName").val(e.params.data.text);
    });
    /**
     * 暂存
     */
    function saveHandler() {
        if ($.validate.form()) {
            saveAjax(prefix + "/saveHandler", $('#form-frame-add').serialize(), '暂存', true);
        }
    }

    function submitHandler() {
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function () {
                $.operate.saveTab(prefix + "/saveHandler", $('#form-frame-add').serialize() + "&status=" + [[${frameEx.constants.STATUS_TJ}]]);
            });
        }
    }

    function selectSrf(orgCode, orgName) {
        $("#srfDeptCode").val(orgCode);
        if ($.common.isNotEmpty($("#promotionDeptCode").val())) {
            $.table.search('form');
        }
    }


    /**
     * 提交流程
     */
    function submitProcess(transitionKey) {
        $('#transitionKey').val(transitionKey);
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function () {
                var data = $('#form-frame-add').serialize() + "&status=" + [[${frameEx.constants.STATUS_TJ}]];
                $.operate.saveTabAlert(prefix + "/saveHandler", data);
            });
        }
    }
</script>
</body>
</html>
