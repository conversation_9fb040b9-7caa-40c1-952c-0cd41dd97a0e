<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('后评估部门审核')" />
	<th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">

<div class="wrapper wrapper-content">
<div class="form-group" th:include="include :: step(approveKind='KIZL_Hpg',currentNode=${activityCode})"></div><br/>
	<form class="form-horizontal m" id="from01" th:object="${data}">
	<!--框-->
	<div class="panel-group" role="tablist" aria-multiselectable="true">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
					&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
					<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
				</h4>
			</div>
			<!--折叠区域-->
			<div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
				<div class="panel-body">
					<div class="row">
						<div class="col-sm-6">
							<div class="form-group">
								<input id="hpgId" name="hpgId" th:value="*{hpgId}" type="hidden">
								<input name="taskId" th:value="${taskId}" type="hidden">
								<input name="activityCode" th:value="${activityCode}" type="hidden">
								<label class="col-sm-3 control-label">专利信息：</label>
								<div class="col-sm-6">
									<div class="form-control-static"><a onClick="onOpenDetail()" style="color: blue">点击查看</a></div>
								</div>
							</div>
						</div>
					</div>
					<div class="row ">
						<div class="col-sm-6">
                           	<div class="form-group">
								<label class="col-sm-3 control-label">清能编号：</label>
								<div class="col-sm-9">
									<div class="form-control-static" th:utext="${patentData.bgbh}"></div>
								</div>
							</div>
						</div>
						<div class="col-sm-6">
                           	<div class="form-group">
								<label class="col-sm-3 control-label">接收编号：</label>
								<div class="col-sm-9">
									<div class="form-control-static" th:utext="${patentData.jsbh}"></div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-6">
							<div class="form-group">
								<label class="col-sm-3 control-label">申请号：</label>
								<div class="col-sm-9">
									<div class="form-control-static" th:utext="${patentData.patentNo}"></div>
								</div>
							</div>
						</div>
						<div class="col-sm-6">
                           	<div class="form-group">
								<label class="col-sm-3 control-label">申请日：</label>
								<div class="col-sm-9">
									<div class="form-control-static" th:utext="${patentData.slrq}"></div>
								</div>
							</div>
						</div>
					</div>
					<div class="row ">
						<div class="col-sm-6">
                           	<div class="form-group">
								<label class="col-sm-3 control-label">专利申报单位：</label>
								<div class="col-sm-9">
  										<div class="form-control-static" th:utext="${patentData.firstDeptName}"></div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-6">
                           	<div class="form-group">
								<label class="col-sm-3 control-label">专利名称：</label>
								<div class="col-sm-9">
  										<div class="form-control-static" th:utext="${patentData.applyName}"></div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-6">
                           	<div class="form-group">
								<label class="col-sm-3 control-label">发明设计人：</label>
								<div class="col-sm-9">
  										<div class="form-control-static" th:utext="${patentData.fml}"></div>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="col-sm-6">
							<div class="form-group">
								<label class="col-sm-3 control-label">来源类型：</label>
	                            <div class="col-sm-8" th:include="/component/radio :: init(id='fromType',name='fromType',dictCode='KI_FROM_TYPE',
	                            businessType='KIZL',see=true,value=${patentData.fromType})"></div>
							</div>
						</div>
					</div>
					<th:block th:if="${patentData.fromType!='08'&&patentData.fromType!='99'}">
					<div class="row">
						<div class="form-group">
							<div class="col-sm-6">
								<label class="col-sm-3 control-label">来源编号：</label>
								<div class="col-sm-9">
									<th:block th:if="${patentData.fromType!='01'}">
										<div class="form-control-static" th:utext="${patentData.fromNo}"></div>
									</th:block>
									<th:block th:if="${patentData.fromType=='01'}">
										<div class="form-control-static"><a onClick="queryKYDT()" style="color: blue">[[${patentData.fromNo}]]</a></div>
									</th:block>
								</div>
							</div>
						</div>
					</div>
					<div class="row">
						<div class="form-group">
							<div class="col-sm-6">
								<label class="col-sm-3 control-label">来源名称：</label>
								<div class="col-sm-9">
									<div class="form-control-static" th:utext="${patentData.fromName}"></div>
								</div>
							</div>
						</div>
					</div>
					</th:block>
					<th:block th:if="${patentData.fromType=='08'}">
					<div class="row">
						<div class="form-group">
							<div class="col-sm-6">
								<label class="col-sm-3 control-label">其他来源情况说明：</label>
								<div class="col-sm-9">
									<div class="form-control-static" th:utext="${patentData.fromContent}"></div>
								</div>
							</div>
						</div>
					</div>
					</th:block>
				</div>
			</div>
			</div>
			<!--折叠区域end-->
		</div>
		<!--框end-->
		<!--框-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#biaoq" href="#jsxx" aria-expanded="false" class="collapsed">
						标签
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="biaoq" class="panel-collapse collapse in">
					<div class="panel-body">
						<div class="row">
							<div class="col-sm-6">
	                           	<div class="form-group" style="margin-left:12px;">
									<th:block th:if="${patentData.label!=null&&patentData.label!=''}">
										<div class="col-sm-8" th:include="/component/checkbox :: init(id='label',name='label',dictCode='techLabel',
										businessType='KYMM',see=true,value=${patentData.label})"></div>
									</th:block>
									<th:block th:if="${patentData.label==null||patentData.label==''}">
										无
									</th:block>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--框end-->
		<!--框-->
		<th:block th:if="${gjrq==''&&gjgj==''}">
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jsxx" aria-expanded="false" class="collapsed">
						境外专利信息
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="jsxx" class="panel-collapse collapse in">
					<div class="panel-body">
						<div class="row">
							<div class="col-sm-6">
	                           	<div class="form-group">
									<label class="col-sm-3 control-label">国际申请日期：</label>
									<div class="col-sm-6">
										<div class="form-control-static" th:utext="${gjrq}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
	                           	<div class="form-group">
									<label class="col-sm-3 control-label">已授权国家：</label>
									<div class="col-sm-6">
										<div class="form-control-static" th:utext="${gjgj}"></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		</th:block>
		<!--框end-->
		<!--框-->
		<div id="lchpgxx" class="panel-group" role="tablist" aria-multiselectable="true" style="display: none">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#lyxx" aria-expanded="false" class="collapsed">
						历次后评估信息
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="lyxx" class="panel-collapse collapse in">
					<div class="form-group">
                            <div class="col-sm-12">
                                <table class="table table-bordered table-hover table-striped">
                                    <tbody id="hpgHistory">
									</tbody>
                                </table>
                            </div>
                        </div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--框end-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#scfx" class="collapsed">
							专利是否应用
							<span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
						</a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="scfx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
                        <div class="row">
							<div class="col-sm-10">
								<div class="form-group">
									<div th:include="include :: initRadio(id='yytype',name='yytype',dictCode='YY_TYPE',businessType='KIZL',
										labelName='应用类型：',value=*{yytype},see=true)"></div>
								</div>
							</div>
						</div>
						<div id="source01" class="row" style="display: none">
							<div class="col-sm-10">
	                           	<div class="form-group" style="margin-left:260px">
	                           		<input id="isbdwnbss" name="isbdwnbss" type="checkbox" onclick="return false" style="width:15px;height:15px;">
	                           		<label class="check-box">A.本单位内部实施</label>
								</div>
							</div>
						</div>
						<div id="source02" class="row" style="display: none">
							<div class="col-sm-10">
	                           	<div class="form-group" style="margin-left:260px">
	                           		<input id="isgftg" name="isgftg" type="checkbox" onclick="return false" style="width:15px;height:15px;">
	                           		<label class="check-box">B.该专利已在股份内推广</label>
								</div>
							</div>
						</div>
						<div id="source03" class="row" style="display: none">
							<div class="col-sm-10">
								<div class="form-group">
									<label class="col-sm-3 control-label">推广情况说明：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{contentGftg}"></div>
									</div>
								</div>
							</div>
						</div>
						<div id="source04" class="row" style="display: none">
							<div class="col-sm-10">
	                           	<div class="form-group" style="margin-left:260px">
	                           		<input id="isgswmy" name="isgswmy" type="checkbox" onclick="return false" style="width:15px;height:15px;">
	                           		<label class="check-box">C.该专利已在公司外贸易</label>
								</div>
							</div>
						</div>
						<div id="source05" class="row" style="display: none">
							<div class="col-sm-10">
								<div class="form-group">
									<label class="col-sm-3 control-label">贸易情况说明：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{contentGswmy}"></div>
									</div>
								</div>
							</div>
						</div>
						<div id="source06" class="row" style="display: none">
							<div class="col-sm-10">
								<div class="form-group">
									<label class="col-sm-3 control-label">曾经应用描述(重点描述曾经应用效果)：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{onceUseDesc}"></div>
									</div>
								</div>
							</div>
						</div>
						<div id="source07" class="row" style="display: none">
							<div class="col-sm-10">
								<div class="form-group">
									<div th:include="include :: initRadio(id='contentNouse',name='contentNouse',dictCode='CONTENT_NOUSE',businessType='KIZL',
										labelName='弃用原因',value=*{contentNouse},see=true)"></div>
								</div>
							</div>
						</div>
						<div id="source08" class="row" style="display: none">
							<div class="col-sm-10">
								<div class="form-group">
									<label class="col-sm-3 control-label">弃用其他原因说明(如无市场前景、后续无订货等)：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{contentQtyy}"></div>
									</div>
								</div>
							</div>
						</div>
						<div id="source09" class="row" style="display: none">
							<div class="col-sm-10">
								<div class="form-group">
									<div th:include="include :: initRadio(id='reasonCwyy',name='reasonCwyy',dictCode='REASON_CWYY',businessType='KIZL',
										labelName='从未应用原因',value=*{reasonCwyy},see=true)"></div>
								</div>
							</div>
						</div>
						<div id="source11" class="row" style="display: none">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">未应用其他原因说明：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{reasonWyyqt}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="form-group" style="margin-left:-85px;">
							<label class="col-sm-3 control-label is-required">同来源专利：</label>
                            <br/><br/>
                            <div class="col-sm-12" style="margin-left:25%;width:50%">
                                <table class="table table-bordered table-hover table-striped">
                                    <thead>
                                    <tr>
                                    	<th style="text-align:center">接收编号</th>
		                                <th style="text-align:center">最新状态</th>
                                        <th style="text-align:center">专利名称</th>
                                        <th style="text-align:center;width:51px">操作</th>
                                    </tr>
                                    </thead>
                                    <tbody id="tllzl">
									</tbody>
                                </table>
                            </div>
                        </div>
						<div id="source10" class="row" style="display: none">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">为保护已实施同类专利而申请的相关专利情况说明：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{contentXgzl}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-10">
								<div class="form-group">
									<div th:include="include :: initRadio(id='importantPj',name='importantPj',dictCode='IMPORTANT_PJ',businessType='KIZL',
										labelName='专利的领先性、实际应用及重要性评价：',value=*{importantPj},see=true)"></div>
								</div>
							</div>
						</div>
						<div class="row" style="margin-left:100px">
							<div class="col-sm-10">
								<div class="form-group">
									<label class="col-sm-3 control-label">情况说明：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{contentZlzypj}"></div>
									</div>
								</div>
							</div>
						</div>
						<div id="source12" class="row" style="display: none">
							<div class="col-sm-10">
	                           	<div class="form-group" style="margin-left:260px">
	                           		<input id="isxclh" name="isxclh" type="checkbox" onClick="return false" style="width:15px;height:15px;">
	                           		<label class="check-box">D.现场应用效果良好，可在宝钢内持续应用，有明显的经济收益</label>
								</div>
							</div>
						</div>
						<!--框-->
						<div id="source13" class="row" style="display: none;width:50%;margin-left:260px">
							<div th:include="KIZL/PA/paInclude :: initSsjHistoryXy(patentId=*{patentId},ssjcs=99)"></div>
						</div>
						<div id="source14" class="row" style="display: none">
							<div class="col-sm-10">
	                           	<div class="form-group" style="margin-left:260px">
	                           		<input id="isyjxy" name="isyjxy" type="checkbox" onClick="return false" style="width:15px;height:15px;">
	                           		<label class="check-box">有预计效益(请填写情况说明及具体预计效益金额)</label>
								</div>
							</div>
						</div>
						<div id="source15" class="row" style="display: none;margin-left:130px">
							<div class="col-sm-10">
								<div class="form-group">
									<label class="col-sm-3 control-label">预计金额：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{yjxyJe}" style="width:100px;display: inline;"></div>万元
									</div>
								</div>
							</div>
						</div>
						<div id="source16" class="row" style="display: none;margin-left:260px">
							<div class="col-sm-10">
								<div class="form-group">
									<label class="col-sm-3 control-label">情况说明(重点描述专利技术对经济效益起到的作用)：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{yjxyDesc}"></div>
									</div>
								</div>
							</div>
						</div>
						<div id="source20" class="row" style="display: none">
							<div class="col-sm-10">
	                           	<div class="form-group" style="margin-left:260px">
	                           		<input id="isnrxgbz" name="isnrxgbz" type="checkbox" onClick="return false" style="width:15px;height:15px;">
	                           		<label class="check-box">E.纳入相关标准</label>
								</div>
							</div>
						</div>
						<div id="source21" class="row" style="display: none;margin-left:100px">
							<div class="col-sm-10">
								<div class="form-group">
									<label class="col-sm-3 control-label">情况说明：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{contentXgbz}"></div>
									</div>
								</div>
							</div>
						</div>
						<!-- <div id="source17" class="row" style="display: none">
							<div class="col-sm-6">
	                           	<div class="form-group" style="margin-left:158px">
	                           		<input id="isproduct" name="isproduct" type="checkbox" onClick="return false" style="width:15px;height:15px;">
	                           		<label class="check-box">E.是产品类专利，若是产品类专利，请补填写钢种牌号</label>
								</div>
							</div>
						</div>
						<div id="source18" class="row" style="display: none">
							<div class="col-sm-6">
								<div class="form-group">
									<div th:include="include :: initRadio(id='productSel',name='productSel',dictCode='PRODUCT_SEL',businessType='KIZL',
										labelName='',value=*{productSel},see=true)"></div>
								</div>
							</div>
						</div>
						<div id="source19" class="row" style="display: none">
							<div class="col-sm-6">
	                           	选择牌号
							</div>
						</div> -->
					</div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--专家评审信息-->
		<div class="panel-body">
			<div class="form-group" th:include="/component/expertReview :: init(bizId=${data.hpgId},moduleCode='kizl_hpg')">
			</div>
		</div>
		<!--专家评审信息end-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#jsfx" class="collapsed">
							专利的前景评估
							<span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
						</a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="jsfx" class="panel-collapse collapse in">
					<div class="panel-body">
						<div class="row">
							<div class="col-sm-10">
	                           	<div class="form-group" style="margin-left:260px">
	                           		<input id="isyqGftg" name="isyqGftg" type="checkbox" onClick="return false" style="width:15px;height:15px;">
	                           		<label class="check-box">A.该专利具有在股份内推广的预期</label>
								</div>
							</div>
						</div>
						<div id="source22" class="row" style="display: none;margin-left:230px">
							<div class="col-sm-10">
								<div class="form-group" th:include="include :: choiceOrg(labelName='可推广单位：',orgCodeId='extra1',
									orgNameId='deptMaybetg',selectType='S',value=*{extra1},see=true)"></div>
							</div>
						</div>
						<div id="source23" class="row" style="display: none;margin-left:230px">
							<div class="col-sm-10">
								<div class="form-group">
									<label class="col-sm-3 control-label">情况说明：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{yqsmGftg}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-10">
	                           	<div class="form-group" style="margin-left:260px">
	                           		<input id="isyqJtyz" name="isyqJtyz" type="checkbox" onClick="return false" style="width:15px;height:15px;">
	                           		<label class="check-box">B.该专利具有在集团内移植的预期</label>
								</div>
							</div>
						</div>
						<div id="source24" class="row" style="display: none;margin-left:230px">
							<div class="col-sm-10">
								<div class="form-group">
									<label class="col-sm-3 control-label">情况说明(如可移植到哪里？)：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{yqsmJtyz}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-10">
	                           	<div class="form-group" style="margin-left:260px">
	                           		<input id="isyqDwmy" name="isyqDwmy" type="checkbox" onClick="return false" style="width:15px;height:15px;">
	                           		<label class="check-box">C.该专利具有对外贸易的预期</label>
								</div>
							</div>
						</div>
						<div id="source25" class="row" style="display: none;margin-left:230px">
							<div class="col-sm-10">
								<div class="form-group">
									<label class="col-sm-3 control-label">情况说明(如可贸易的市场在哪里？)：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:text="*{yqsmDwmy}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-10">
	                           	<div class="form-group" style="margin-left:260px">
	                           		<input id="isyqNomay" name="isyqNomay" type="checkbox" onClick="return false" style="width:15px;height:15px;">
	                           		<label class="check-box">D.没有股份内推广、集团内移植、公司外贸易的可能性</label>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--折叠区域end-->
				</div>
				<div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                   href="#approveComment2">审批信息
                    <span class="pull-right"><i aria-hidden="true" class="fa fa-chevron-down"></i></span></a>
            </h4>
        </div>
        <!--折叠区域-->
		        <div aria-expanded="false" class="panel-collapse collapse in" id="approveComment2">
			            <div class="panel-body">
			                <!--一列-->
			                <th:block th:if="${activityCode=='Manual4'||activityCode=='Manual7'||activityCode=='Manual8'||activityCode=='Manual9'||
			                activityCode=='Manual10'||activityCode=='Manual12'||activityCode=='Manual13'}">
			                <div class="row">
								<div class="col-sm-6">
									<div th:include="include :: initRadio(id='extra4',name='extra4',dictCode='KI_FQZT',businessType='KIZL',
												labelName='您是否同意放弃：',isrequired=true)"></div>
								</div>
							</div><br/>
							</th:block>
	                    	<div class="row">
								<div class="col-sm-6">
									<div class="form-group">
										<label class="col-sm-3 control-label">填写意见：</label>
									</div>
								</div>
							</div>
							<div class="panel-body">
								<div class="form-group">
									<div class="col-sm-12">
										<textarea id="comment" name="comment" class="form-control" style="white-space: break-spaces;" rows="8"></textarea>
									</div>
								</div>
							</div>
			            </div>
			        </div>
			    </div>
				<th:block th:include="component/wfCommentList4 :: init(businessId=${data.hpgId})" />
				<p th:if="${processInstanceId}">
					<th:block th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})"></th:block>
				</p>
		</div>
	</form>
</div>
<div class="toolbar toolbar-bottom" role="toolbar">
	<th:block th:if="${activityCode=='Manual3'||activityCode=='Manual4'||activityCode=='Manual6'||activityCode=='Manual11'}">
		<button type="button" class="btn btn-primary" onclick="startZJPS()"><i class="fa fa-check"></i>启动专家评审</button>
		<!-- <button type="button" class="btn btn-primary" onclick="showZJPS()"><i class="fa fa-eye"></i>查看专家评审</button> -->
	</th:block>
	<button type="button" class="btn btn-primary" onclick="saveHandler()">
		<i class="fa fa-save"></i>暂 存
	</button>
	<th:block th:if="${processInstanceId}">
		<th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>
	</th:block>
	<th:block th:include="include :: wfReturn(taskId=${taskId},callback=wfReturnHandler)"/>
	<button type="button" class="btn btn-danger" onclick="closeItem()">
		<i class="fa fa-reply-all"></i>返 回
	</button>
</div>
<br/><br/>
<script type="text/javascript" th:src="@{/product/js/kizlfw.js}"></script>
<script th:inline="javascript">
	var prefix = ctx + "KIZL/FW";
	//获取流程意见
	if([[${comment}]]!=null){
		document.getElementById("comment").value=[[${comment}]];
	}
	
	//验证
	$("#from01").validate({
		ignore: ":hidden",
		focusCleanup : true
	});
	
	loadEditData([[${data.isbdwnbss}]],[[${data.isgftg}]],[[${data.isgswmy}]],
			[[${data.isxclh}]],[[${data.isyjxy}]],
			[[${data.isproduct}]],[[${data.isnrxgbz}]],[[${data.isyqGftg}]],
			[[${data.isyqJtyz}]],[[${data.isyqDwmy}]],[[${data.isyqNomay}]],
			[[${data.contentNouse}]],[[${data.reasonCwyy}]],[[${data.yytype}]]);
	
	//暂存
	function saveHandler() {
		var config = {
			url: prefix + "/doSaveComment",
			type: "post",
			dataType: "json",
			data: $('#from01').serialize(),
			beforeSend: function () {
				$.modal.loading("正在处理中，请稍后...");
			},
			success: function (result) {
				$("#hpgId").val(result.data.hpgId);
				$.modal.alertSuccess(result.msg);
				$.modal.closeLoading();
			}
		};
		$.ajax(config)
	}
	
	/*提交启动流程*/
	function submitHandler(transitionKey) {
    	if ($.validate.form()) {
	      	$.modal.confirm("确认提交吗？", function () {
				var config = {
	                    url: prefix + "/submitWF",
	                    type: "post",
	                    dataType: "json",
	                    data: $('#from01').serialize() + (transitionKey ? ("&transitionKey=" + transitionKey) : ""),
	                    beforeSend: function () {
	                        $.modal.loading("正在处理中，请稍后...");
	                    },
	                    success: function (result) {
	                    	var str = result.msg;
	                    	if(str.indexOf("专家评审未结束或未启动专家评审")!= -1){
	                    		$.modal.alertError(result.msg);
	                    		$.modal.closeLoading();
	                    	}else if(str.indexOf("请联系系统管理员")!= -1){
	                    		$.modal.alertError(result.msg);
	                    		$.modal.closeLoading();
	                    	}else{
	                    		$.operate.alertSuccessTabCallback(result);
	                    	}
	                    }
	                };
	                $.ajax(config)
			})
		}
	}
	
	/*退回流程*/
    function wfReturnHandler(activityKey) {
        var comment = $("#comment").val();
        if ($.common.isEmpty(comment)) {
            $.modal.alertWarning("请输入退回意见");
            return;
        }
        $.modal.confirm("确认要退回吗？", function () {
            $.ajax({
                url: prefix + "/returnWF",
                type: "post",
                dataType: "json",
                data: $("#from01").serialize() + (activityKey ? ("&activityKey=" + activityKey) : ""),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $.operate.alertSuccessTabCallback(result);
                }
            });
        })
    }
	
	function onOpenDetail(){
		var applyId = [[${patentData.applyId}]];
		var taskId = [[${taskId}]];
		var processId = [[${processInstanceId}]];
		var title = "专利申请详细信息";
        var id = applyId+","+taskId+","+processId;
        var url = ctx+"KIZL/PA/AB/queryDT?id="+id;
        $.modal.openTab(title, url, true);
	}
	
	/* 启动专家评审 */
	function startZJPS(){
		var activityCode = [[${activityCode}]];
		$.modal.open("启动专家评审", ctx +"KIZL/PA/AB/toPS?bizGuid="+$("#hpgId").val()+
				"&moduleCode=kizl_hpg&approveKind=MPPS_members_review&processNode="+activityCode, '1000', '500');
    }
    function showZJPS() {
        $.modal.open("查看评审信息", ctxGGMK +"mpps/reviewInfo/showPsByBizGuid?bizGuid="+$("#hpgId").val(), '1000', '500');
    }
    
  	//获取实施奖信息
	/* getSsjHistory();
    function getSsjHistory() {
		var patentId = $("#patentId").val();
		$("#ssjHistory").empty();
        if(patentId!=''){
        	$.ajax({
		    	url: ctx + "KIZL/PA/AB/getSsjHistory/" + patentId,
	  			type:"GET",
	  			dataType:"json",
	  			success:function(res){
	  				var ssjHistory = res.ssjHistory;
	  				if(ssjHistory.length>0){
	  					for(var i=0;i<ssjHistory.length;i++){
		  					var tr = "<tr id='c" + i + "'>";
		  					tr = tr + "<td style='text-align:center'>"+ssjHistory[i].bgbh+"</td>";
		  					if(ssjHistory[i].ssjlx=='XY'){
		  						tr = tr + "<td style='text-align:center'>效益奖</td>";
		  					}else{
		  						tr = tr + "<td style='text-align:center'>水平奖</td>";
		  					}
		  				    tr = tr + "<td style='text-align:center'>"+ssjHistory[i].ssjcs+"</td>";
		  				  	tr = tr + "<td style='text-align:center'>"+ssjHistory[i].deptName+"</td>";
		  				    tr = tr + '<td style="text-align:center"><a onClick="hpgHistoryDetail('+"'"+ssjHistory[i].ssjId+"'"+')">'+"<font color='blue'>查看</font></a></td>";
		  				    tr = tr + "</tr>";
		  				    $("#ssjHistory").append(tr);
		  				}
	  				}else{
	  					$("#source13").hide();
	  				}
	  			},
	  			error:function(jqXHR, textStatus, errorThrown) {// 处理错误情况
	  	    	}
			});
        }
    } */
    
  	//获取历次后评估信息
	getHpgHistory();
    function getHpgHistory() {
		var patentId = $("#patentId").val();
		$("#hpgHistory").empty();
        if(patentId!=''){
        	$.ajax({
		    	url: ctx + "KIZL/PA/AB/getHpgHistory/" + patentId,
	  			type:"GET",
	  			dataType:"json",
	  			success:function(res){
	  				var hpgHistory = res.hpgHistory;
	  				if(hpgHistory.length>0){
	  					$("#lchpgxx").show();
	  					for(var i=0;i<hpgHistory.length;i++){
		  					var tr = "<tr id='c" + i + "'>";
		  					tr = tr + "<td style='text-align:center'>"+hpgHistory[i].year+"</td>";
		  				    tr = tr + "<td style='text-align:center'>"+hpgHistory[i].hpgName+"</td>";
		  				  	tr = tr + "<td style='text-align:center'>"+hpgHistory[i].startDate+"</td>";
		  				    tr = tr + '<td style="text-align:center"><a onClick="hpgHistoryDetail('+"'"+hpgHistory[i].hpgId+"'"+')">'+"<font color='blue'>查看</font></a></td>";
		  				    tr = tr + "</tr>";
		  				    $("#hpgHistory").append(tr);
		  				}
	  				}
	  				
	  			},
	  			error:function(jqXHR, textStatus, errorThrown) {// 处理错误情况
	  	    	}
			});
        }
    }
  	//获取后评估详细信息
	function hpgHistoryDetail(id){
		var title = "后评估详细信息";
        var url = ctx+"KIZL/FW/queryDT?id="+id;
        $.modal.openTab(title, url, true);
	}
  	
  	//获取同来源信息
	getSource();
    function getSource() {
    	var fromNo = [[${patentData.fromNo}]];
		$("#tllzl").empty();
        if(fromNo!=''){
        	$.ajax({
		    	url: ctx + "KIZL/PA/AB/getTll/" + fromNo,
	  			type:"GET",
	  			dataType:"json",
	  			success:function(res){
	  				var zllist = res.zllist;
	  				if(zllist!=undefined){
	  					for(var i=0;i<zllist.length;i++){
		  					var tr = "<tr id='c" + i + "'>";
		  					tr = tr + "<td style='text-align:center'>"+zllist[i].jsbh+"</td>";
		  					tr = tr + "<td style='text-align:center'>"+zllist[i].flzt+"</td>";
		  				    tr = tr + "<td style='text-align:center'>"+zllist[i].applyName+"</td>";
		  				    tr = tr + '<td style="text-align:center"><a onClick="tllzlDetail('+"'"+zllist[i].applyId+"'"+')">'+"<font color='blue'>查看</font></a></td>";
		  				    tr = tr + "</tr>";
		  				    $("#tllzl").append(tr);
		  				}
	  				}
	  			},
	  			error:function(jqXHR, textStatus, errorThrown) {// 处理错误情况
	  	    	}
			});
        }
    }
  	//同来源专利详细信息
	function tllzlDetail(id){
		var title = "专利申请详细信息";
        var url = ctx+"KIZL/PA/AB/queryDT?id="+id;
        $.modal.openTab(title, url, true);
	}
	
	//科研一览表
    function queryKYDT(){
    	var projectCode = [[${patentData.fromNo}]];
		var url = ctxKY+"web/KYXMLXYL?serviceName=KYXMLX02&methodName=query&pageNo=KYXMLX21&projectCode="+projectCode;
		var title = "项目一栏表";
     	// 选卡页方式打开并刷新当前页 isRefresh 是否刷新
     	$.modal.openTab(title, url, true);
    }
</script>
</body>
</html>