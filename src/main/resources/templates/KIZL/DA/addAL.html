<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境内专利本室档案信息')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-kjbcqsArchive-edit" th:object="${kjbcqsArchive}">
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false"
                           class="collapsed">基本信息
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <input name="archiveId" th:field="*{archiveId}" type="hidden">
                        <input name="firstType" th:field="*{firstType}" type="hidden">

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">类型：</label>
                            <div class="col-lg-4">
                                <div th:include="/component/select :: init(required =true,id='secondType', name='secondType',businessType='KIZL', dictCode='KI_BSDA_AL_TYPE',value=${secondType})"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">事项开始时间：</label>
                            <div class="col-sm-2">
                                <div th:include="/component/date :: init(id='startDate', name='startDate',isrequired='true')"></div>
                            </div>

                            <label class="col-sm-2 control-label is-required">事项结束时间</label>
                            <div class="col-sm-2">
                                <div th:include="/component/date :: init(id='endDate', name='endDate',isrequired='true')"></div>
                            </div>
                        </div>

                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">项目名称：</label>
                            <div class="col-sm-8">
                                <input name="subject" th:field="*{subject}" class="form-control" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">简要内容：</label>
                            <div class="col-sm-8">

                                <textarea required th:text="${contentBody}" class="form-control"
                                          style="height: 100px;white-space: break-spaces;" name="contentBody" width="100%"></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">附件：</label>
                            <div class="col-lg-9"
                                 th:include="include::layui-upload(display='none' ,isrequired='true',sourceId=${archiveId},sourceModule='KIZL',sourceLabel1='KIZL_FILE001',id='KIZL_FILE001',name='KIZL_FILE001', labelClass='col-sm-1 control-label')"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-sm btn-primary"
                onclick="submitHandler()">
            <i class="fa fa-check"></i>保 存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
    <!--按钮区end-->
</div>
<script th:inline="javascript">
    var prefix = ctx + "KIZL/DA";

    $("#form-kjbcqsArchive-edit").validate({
        focusCleanup: true
    });

    var kjbcqsArchive = {}//基本信息
    var jsonObject = {}//附件信息

    function submitHandler() {
        if ($.validate.form()) {
            submit();
        }
    }

    function urlToObject(data) {
        var jsonData = {};
        for (var item in data) {
            var value = data[item].value;
            if (value != null && value != '') {
                if (jsonData[data[item].name] != null && jsonData[data[item].name] != '' && jsonData[data[item].name] != undefined) {
                    jsonData[data[item].name] += ',' + data[item].value
                } else {
                    jsonData[data[item].name] = data[item].value;
                }

            }
        }
        return jsonData;
    };


    function submit() {
        var htmlData = urlToObject($('#form-kjbcqsArchive-edit').serializeArray())
        for (const key in htmlData) {
            if ('KIZL_FILE001' === key) {
                jsonObject[key] = htmlData[key]
            } else {
                kjbcqsArchive[key] = htmlData[key]
            }
        }
        var data = {
            "kjbcqsArchive": kjbcqsArchive,
            "jsonObject": jsonObject,
        }
        $.ajax({
            url: prefix + "/addAndEdit",
            type: "post",
            dataType: "json",
            contentType: 'application/json;charset=UTF-8',
            data: JSON.stringify(data),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                if (typeof callback == "function") {
                    callback(result);
                }
                $.operate.successTabCallback(result);
            }
        })
    }
</script>
</body>
</html>