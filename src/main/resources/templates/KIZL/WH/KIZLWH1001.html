<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('费用管理')" />
	<th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content">
	<form class="form-horizontal m" id="from01" th:object="${data}">
	<input name="moneyId" id="moneyId" th:value="*{moneyId}" type="hidden">
		<!--框-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">费用名称：</label>
									<div class="col-sm-6">
										<input name="moneyName" id="moneyName" class="form-control" th:value="*{moneyName}" required/>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">金额：</label>
									<div class="col-sm-6">
										<input name="je" id="je" class="form-control" th:value="*{je}" required/>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">费用序号：</label>
									<div class="col-sm-6">
										<input name="moneySort" id="moneySort" class="form-control" th:value="*{moneySort}"/>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group" th:include="include :: initRadio(id='patentType',name='patentType',dictCode='KI_PATENT_TYPEGN',businessType='KIZL',
									labelName='专利类型：',value=*{patentType})"></div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group" th:include="include :: choiceOrg(labelName='组织名称：',orgCodeId='deptCode',
								orgNameId='deptName',selectType='S',value=*{deptCode},isrequired='true')"></div>
							</div>
						</div>
					</div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--框end-->
	</form>
</div>
<div class="row">
	<div class="col-sm-offset-5 col-sm-10">
		<button type="button" class="btn btn-sm btn-primary"
			onclick="submitHandler()">
		<i class="fa fa-check"></i>保 存
		</button>
		<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
			<i class="fa fa-reply-all"></i>返 回
		</button>
	</div>
</div><br/><br/>
<script th:inline="javascript">
	var prefix = ctx + "KIZL/WH";
	//验证
	$("#from01").validate({
		focusCleanup : true
	});
	
	/*保存*/
	function submitHandler() {
		if ($.validate.form()) {
			var config = {
                    url: prefix + "/addMoney",
                    type: "post",
                    dataType: "json",
                    data: $('#from01').serialize(),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                    	var str = result.msg;
                    	if(str.indexOf("重复")!= -1){
                    		$.modal.alertError(result.msg);
                    		$.modal.closeLoading();
                    	}else{
                    		$.operate.alertSuccessTabCallback(result);
                    	}
                    }
                };
                $.ajax(config)
		}
	}
</script>
</body>
</html>