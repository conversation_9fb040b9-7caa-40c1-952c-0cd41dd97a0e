<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('月度代理费管理信息')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form-moneyMonthdlf-edit" th:object="${moneyMonthdlf}">
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <input name="yddlfId" th:field="*{yddlfId}" type="hidden">
                        <div class="form-group">
                            <div class="col-sm-12">
                                <div class="col-sm-12 select-table">
                                    <button class="btn btn-success btn-circle" type="button" onclick="addColumn()"><i class="fa fa-plus"></i> </button>
                                    <button class="btn btn-primary btn-circle" type="button" onclick="delFkfp()"><i class="fa fa-minus"></i> </button>
                                    <table id="bootstrap-table"></table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-sm btn-primary"
                onclick="submitHandler()">
            <i class="fa fa-check"></i>保 存
        </button>&nbsp;
        <button type="button" class="btn btn-sm btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>关 闭
        </button>
    </div>
    <!--按钮区end-->
</div>
<th:block th:include="include :: datetimepicker-js" />
<script th:inline="javascript">
    var prefix = ctx + "kizl/moneyMonthdlf";

    $("#form-moneyMonthdlf-edit").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/save", $('#form-moneyMonthdlf-edit').serialize());
        }
    }


    function verificationPayBillList(){
        var length = $("#bootstrap-table").bootstrapTable('getData').length;
        if(length == 0){
            $.modal.alertWarning("请填写付款发票信息");
            return true;
        }
        var total = 0;
        for (let i = 0; i < length; i++) {
            total = dcmAdd(total,$("#totalCost"+i).val());
        }
        if(total!=Number([[${data?.payMoney}]])){
            $.modal.alertWarning("发票总金额不等于付款金额");
            return true;
        }
        return false;
    }

    var billTypeData = [[${@dict.getDictList('KNXH','billType')}]];
    var reportTypeData = [[${@dict.getDictList('KNXH','reportType')}]];
    var taxRateData = [[${@dict.getDictList('KNXH','taxRate')}]];
    $(function() {
        // 初始化数据, 可以由后台传过来
        var data = [[${data?.payBillList}]];
        var options = {
            data: data,
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            onPostBody: calculationTaxCostAll,
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", $.table.serialNumber(index));
                        //序号
                        var html = $.common.sprintf("<input type='hidden' id='orderNum%s' name='payBillList[%s].orderNum' value='%s'>", index, index, index);
                        return columnIndex + $.table.serialNumber(index) + html;
                    }
                },
                {
                    field: 'dlfFpje',
                    align: 'center',
                    title: '发票金额',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' required type='text' id='totalCost%s' name='payBillList[%s].totalCost' value='%s' onblur=calculationTaxCost(this)>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'bzrq',
                    align: 'center',
                    title: '报支日期',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' required type='text' id='billDate%s' name='payBillList[%s].billDate' value='%s' placeholder='yyyy-MM-dd'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'swsGuid',
                    align: 'center',
                    title: '代理事务所',
                    formatter: function(value, row, index) {
                        return dictToSelect(taxRateData,value,'taxRate',index,"taxRateClick");
                    }
                },
                {
                    field: 'gssjzlFms',
                    align: 'center',
                    title: '该所设计专利发明数',
                    formatter: function(value, row, index) {
                        return $.common.sprintf("<input class='form-control' required type='text' id='taxCost%s' name='payBillList[%s].taxCost' value='%s' onblur=calculationTaxCost1(this)>", index, index, value);
                    }
                },
                {
                    field: 'syxxs',
                    align: 'center',
                    title: '实用新型数',
                    formatter: function(value, row, index) {
                        return dictToSelect(taxRateData,value,'taxRate',index,"taxRateClick");
                    }
                },
                {
                    field: 'kjze',
                    align: 'center',
                    title: '扣减总额',
                    formatter: function(value, row, index) {
                        return dictToSelect(taxRateData,value,'taxRate',index,"taxRateClick");
                    }
                },
                {
                    field: 'wdwfpRemark',
                    align: 'center',
                    title: '外单位发票备注',
                    formatter: function(value, row, index) {
                        return dictToSelect(taxRateData,value,'taxRate',index,"taxRateClick");
                    }
                },
                {
                    field: 'dlf',
                    align: 'center',
                    title: '代理费',
                    formatter: function(value, row, index) {
                        return dictToSelect(taxRateData,value,'taxRate',index,"taxRateClick");
                    }
                },
                {
                    field: 'remark',
                    align: 'center',
                    title: '备注',
                    formatter: function(value, row, index) {
                        return dictToSelect(taxRateData,value,'taxRate',index,"taxRateClick");
                    }
                }]
        };
        $.table.init(options);
    });


    function delFkfp() {
        if ($.modal.confirm("确认删除当前行吗?", function () {
            sub.delColumn();
        })) ;
    }

    function addColumn() {
        var row = {
            billType: "",
            reportType: "",
            billNumber: "",
            totalCost: "",
            billDate: "",
            taxRate: "",
            taxCost: "",
            billNo: "",
        }
        sub.addColumn(row);
    }

    $("#bootstrap-table").on("post-body.bs.table", function (e, args) {
        $("input[name$='Date']").datetimepicker({
            format: "yyyy-mm-dd",
            minView: "month",
            autoclose: true,
            pickerPosition:'top-right'
        });
    });

    // 数据字典转下拉框
    function dictToSelect(datas, value, name, index, callback) {
        var actions = [];
        if($.common.isEmpty(callback)){
            actions.push($.common.sprintf("<select required class='form-control' id='%s' name='%s'>", name+index, "payBillList["+index+"]."+name));
        }else{
            actions.push($.common.sprintf("<select required class='form-control' id='%s' name='%s' onchange='%s'>", name+index, "payBillList["+index+"]."+name, callback+"(this)"));
        }
        actions.push('<option value="">请选择</option>');
        $.each(datas, function (index, dict) {
            actions.push($.common.sprintf("<option value='%s'", dict.dictValue));
            if (dict.dictValue == ('' + value)) {
                actions.push(' selected');
            }
            actions.push($.common.sprintf(">%s</option>", dict.dictName));
        });
        actions.push('</select>');
        return actions.join('');
    }

    //格式化数字
    function onlyNumber(obj){
        obj.value = obj.value.replace(/[^\d.]/g,''); //只能输入数字和点
        obj.value = obj.value.replace(/^\./g,'');//必须保证第一个为数字而不是.
        obj.value = obj.value.replace(/\.{2,}/g,'.');//保证只有出现一个.而没有多个.
        obj.value = obj.value.replace('.','$#$').replace(/\./g,'').replace('$#$','.'); //保证.只出现一次，而不能出现两次以上
        if(obj.value.indexOf(".") > 0){
            obj.value = obj.value.replace(new RegExp("0+?$","gm"), "");//去掉多余的0
            obj.value = obj.value.replace(new RegExp("[.]$","gm"), "");//如最后一位是.则去掉
        }
        if($.common.isNotEmpty(obj.value)){
            obj.value = Number(obj.value); //去除开头的0
        }
    }

    //计算单个发票税额,更新总税额(税额改变)
    function taxRateClick(obj){
        var index = obj.id.replace(/[^0-9]/ig, ""); //当前行序号

        var m=0;
        var s1=$("#totalCost"+index).val().toString();
        var s2=$("#taxRate"+index).val().toString();
        var s3="0.01";

        try{m+=s1.split(".")[1].length;}catch(e){}
        try{m+=s2.split(".")[1].length;}catch(e){}
        try{m+=s3.split(".")[1].length;}catch(e){}

        $("#taxCost"+index).val(Number(s1.replace(".",""))*Number(s2.replace(".",""))*Number(s3.replace(".",""))/Math.pow(10,m));
        calculationTaxCostAll();
    }

    //计算单个发票税额,更新总税额(发票金额改变)
    function calculationTaxCost(obj){
        onlyNumber(obj);
        taxRateClick(obj);
    }

    //发票税额调整
    function calculationTaxCost1(obj){
        onlyNumber(obj);
        calculationTaxCostAll();
    }

    //计算总税额
    function calculationTaxCostAll(){
        var total = 0;
        var length = $("#bootstrap-table").bootstrapTable('getData').length;
        for (let i = 0; i < length; i++) {
            total = dcmAdd(total,$("#taxCost"+i).val());
        }
        $("#taxAmounts").val(total);
        $("#taxAmounts").next().text(total);
    }

    //两数相加
    function dcmAdd(arg1,arg2){
        var r1,r2,m;
        try{r1=arg1.toString().split(".")[1].length;}catch(e){r1=0;}
        try{r2=arg2.toString().split(".")[1].length;}catch(e){r2=0;}
        m=Math.pow(10,Math.max(r1,r2));
        return ((arg1*m+arg2*m)/m).toFixed(Math.max(r1,r2));
    }


</script>
</body>
</html>