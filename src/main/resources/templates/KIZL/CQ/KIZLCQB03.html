<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('专利按月数据统计')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
     	<form class="form-horizontal m" id="from01">
		<!--框-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">单位申请授权信息
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body"><br/><br/>
						<div id="title01" style="display:block;text-align:center;font-size: 25px"></div>
						<div class="panel-body">
				            <div class="form-group">
								<label class="col-sm-3 control-label"></label>
				                <div class="col-sm-13" style="display: none" id="table01">
					                <table class="table table-bordered table-hover table-striped">
						                <thead>
							                <tr>
							                    <th style="text-align:center;width:5%">序号</th>
							                    <th style="text-align:center;width:15%">接收编号</th>
							                    <th style="text-align:center;width:15%">清能编号</th>
							                    <th style="text-align:center;width:15%">申请号</th>
							                    <th style="text-align:center;width:25%">专利名称</th>
							                    <th style="text-align:center;width:15%">贡献系数</th>
							                </tr>
						                </thead>
						                <tbody id="tllzl01">
										</tbody>
					                </table>
				                </div>
				                <div class="col-sm-13" style="display: none" id="table02">
					                <table class="table table-bordered table-hover table-striped">
						                <thead>
							                <tr>
							                	<th style="text-align:center;width:5%">序号</th>
							                    <th style="text-align:center;width:25%">申报单位全称</th>
							                    <th style="text-align:center;width:8%">清能编号</th>
							                    <th style="text-align:center;width:8%">接收编号</th>
							                    <th style="text-align:center;width:10%">申请号</th>
							                    <th style="text-align:center;width:8%">申请日</th>
							                    <th style="text-align:center;width:18%">专利名称</th>
							                    <th style="text-align:center;width:10%">放弃时间</th>
							                    <th style="text-align:center;width:10%">申请人数</th>
							                </tr>
						                </thead>
						                <tbody id="tllzl02">
										</tbody>
					                </table>
				                </div>
				            </div>
			            </div>
			        </div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--框end-->
		</form>
    </div>

    <script th:inline="javascript">
      	//获取统计表格
      	doSearch();
        function doSearch() {
        		var startDate = [[${ksrq}]];
        		var endDate = [[${jsrq}]];
        		var deptCode = [[${deptCode}]];
        		var zllx = [[${zllx}]];
        		var tjlx = [[${tjlx}]];
        		var tjsqlx = [[${tjsqlx}]];
        		var sclx = [[${sclx}]];
        		var yf = [[${yf}]];
        		var isMonth = [[${isMonth}]];
        		
        		if(tjlx=='01'){//申请 
        			$("#table01").show();
        			$("#table02").hide();
        		}else if(tjlx=='02'||tjlx=='04'){//授权 授权累计
        			$("#table01").show();
        			$("#table02").hide();
        		}else if(tjlx=='03'){//放弃 
        			$("#table01").hide();
        			$("#table02").show();
        		}
        		if(yf==0){
        			$("#title01").html(startDate+"到"+endDate+" "+[[${title02}]]);
        		}else{
        			$("#title01").html(startDate+"到"+endDate+" "+yf+"月 "+[[${title02}]]);
        		}
        		
        		
        		var url = ctx + "KIZL/CQ/AA/dwsqsqList/KIZLCQB03";
        		url = url+"?startDate=" + startDate + "&endDate=" + endDate + "&deptCode=" + deptCode + "&zllx=" 
        				+ zllx + "&tjlx=" + tjlx + "&tjsqlx=" + tjsqlx + "&sclx=" + sclx + "&yf=" + yf + "&isMonth=" 
        				+ isMonth + "&msFlag="+[[${msFlag}]]+"&inCompanyNode="+[[${inCompanyNode}]];
                	$.ajax({
        		    	url: url,
        	  			type:"GET",
        	  			dataType:"json",
        	  			data: "",
        	  			success:function(res){
        	  				var zllist = res.data;
        	  				if(zllist!=undefined){
        	  					for(var i=0;i<zllist.length;i++){
        		  					var tr = "<tr id='c" + i + "'>";
	      		  					if(tjlx=='03'){//放弃
	      		  						tr = tr + "<td style='text-align:center'>"+eval(i+1)+"</td>";
	      		  						if(zllist[i].deptPathName!=null){
	      		  							tr = tr + "<td style='text-align:center'>"+zllist[i].deptPathName+"</td>";
	      		  						}else{
			      		  					tr = tr + "<td style='text-align:center'></td>";
		      		  					}
		      		  					if(zllist[i].bgbh!=null){
			      		  					tr = tr + "<td style='text-align:center'>"+zllist[i].bgbh+"</td>";
		      		  					}else{
			      		  					tr = tr + "<td style='text-align:center'></td>";
		      		  					}
		      		  					if(zllist[i].jsbh!=null){
			      		  					tr = tr + "<td style='text-align:center'>"+zllist[i].jsbh+"</td>";
			      		  				}else{
			      		  					tr = tr + "<td style='text-align:center'></td>";
		      		  					}
		      		  					if(zllist[i].patentNo!=null){
				      		  				tr = tr + "<td style='text-align:center'>"+zllist[i].patentNo+"</td>";
		        	  					}else{
			      		  					tr = tr + "<td style='text-align:center'></td>";
		      		  					}
					      		  		if(zllist[i].sqrq!=null){
								      		tr = tr + "<td style='text-align:center'>"+zllist[i].sqrq+"</td>";
			        	  				}else{
			      		  					tr = tr + "<td style='text-align:center'></td>";
			  		  					}
						      		  	if(zllist[i].applyName!=null){
					      		  			tr = tr + "<td style='text-align:center'>"+'<a style="color:blue" href="javascript:void(0)" onclick="onOpenDetail(\'' +
					      		  			zllist[i].applyId+'\')">'+zllist[i].applyName+'</a>'+"</td>";
					      		  		}else{
			      		  					tr = tr + "<td style='text-align:center'></td>";
		      		  					}
						      		  	if(zllist[i].zzrq!=null){
					      		  			tr = tr + "<td style='text-align:center'>"+zllist[i].zzrq+"</td>";
				        	  			}else{
				  		  					tr = tr + "<td style='text-align:center'></td>";
						  				}
						      			if(zllist[i].sqrId!=null){
					      		  			tr = tr + "<td style='text-align:center'>"+zllist[i].sqrId+"</td>";
				                		}else{
						  					tr = tr + "<td style='text-align:center'></td>";
					  					}
	      		  						tr = tr + "</tr>";
	      		  						$("#tllzl02").append(tr);
	      		  					}else{
	      		  						if(i+1==zllist.length){
	      		  							tr = tr + "<td style='text-align:center'>合计</td>";
	      		  						}else{
	      		  							tr = tr + "<td style='text-align:center'>"+eval(i+1)+"</td>";
	      		  						}
		      		  					if(zllist[i].jsbh!=null){
		      		  						tr = tr + "<td style='text-align:center'>"+zllist[i].jsbh+"</td>";
		      		  					}else{
		      		  						tr = tr + "<td style='text-align:center'></td>";
		      		  					}
			      		  				if(zllist[i].bgbh!=null){
			      		  					tr = tr + "<td style='text-align:center'>"+zllist[i].bgbh+"</td>";
			      		  				}else{
			      		  					tr = tr + "<td style='text-align:center'></td>";
		      		  					}
				      		  			if(zllist[i].patentNo!=null){
				      		  				tr = tr + "<td style='text-align:center'>"+zllist[i].patentNo+"</td>";
			      		  				}else{
			      		  					tr = tr + "<td style='text-align:center'></td>";
		      		  					}
					      		  		if(zllist[i].applyName!=null){
					      		  			tr = tr + "<td style='text-align:center'>"+'<a style="color:blue" href="javascript:void(0)" onclick="onOpenDetail(\'' +
					      		  			zllist[i].applyId+'\')">'+zllist[i].applyName+'</a>'+"</td>";
					      		  		}else{
			      		  					tr = tr + "<td style='text-align:center'></td>";
		      		  					}
						      		  	if(zllist[i].gxxs!=null){
						      		  		tr = tr + "<td style='text-align:center'>"+zllist[i].gxxs+"</td>";
					      		  		}else{
			      		  					tr = tr + "<td style='text-align:center'></td>";
		      		  					}
	        		  					tr = tr + "</tr>";
	        		  					$("#tllzl01").append(tr);
	      		  					}
        		  				}
        	  				}
        	  			},
        	  			error:function(jqXHR, textStatus, errorThrown) {// 处理错误情况
        	  	    	}
        			});
        }
        
        function onOpenDetail(applyId){
        	var title = "专利申请详细信息";
        	var url = ctx+"KIZL/PA/AB/queryALL?id="+applyId;
            $.modal.openTab(title, url, true);
        }
    </script>
</body>
</html>