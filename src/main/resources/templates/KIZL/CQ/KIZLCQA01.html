<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('知识产权当年实绩')" />
    <th:block th:include="include :: baseJs" />
</head>
<style> .abc{ color:red} </style>
<body class="gray-bg">
     <div class="container-div">
     	<form class="form-horizontal m" id="formId">
		<!--框-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">知识产权当年实绩
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div style="text-align:center;font-size: 25px;" class="form-control-static" th:utext="${title}"></div>
						<div class="panel-body">
				            <div class="form-group">
								<label class="col-sm-3 control-label"></label>
				                <div class="col-sm-13">
					                <table class="table table-bordered table-hover table-striped">
						                <thead>
							                <tr>
							                    <th style="text-align:center;width:5%">序号</th>
							                    <th style="text-align:center;width:10%">单位</th>
							                    <th style="text-align:center;width:5%">专利申请数</th>
							                    <th style="text-align:center;width:5%">发明专利申请数</th>
							                    <th style="text-align:center;width:5%">发明比例%</th>
							                    <th style="text-align:center;width:5%">专利授权数</th>
							                    <th style="text-align:center;width:5%">发明专利授权数</th>
							                    <th style="text-align:center;width:5%">授权有效专利数（累计值）</th>
							                    <th style="text-align:center;width:5%">授权有效发明专利数（累计值）</th>
							                    <th style="text-align:center;width:5%">技术秘密认定数</th>
							                </tr>
						                </thead>
						                <tbody id="tllzl">
										</tbody>
					                </table>
				                </div>
				            </div>
			            </div>
			        </div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--框end-->
		</form>
    </div>

    <script th:inline="javascript">
      	//获取统计表格
      	queryTable();
        function queryTable() {
    		var url = ctx + "KIZL/CQ/AA/zscqdnsjList";
    		$("#tllzl").empty();
            	$.ajax({
    		    	url: url,
    	  			type:"GET",
    	  			dataType:"json",
    	  			success:function(res){
    	  				var zllist = res.data;
    	  				if(zllist!=undefined){
    	  					for(var i=0;i<zllist.length;i++){
    		  					var tr = "<tr id='c" + i + "'>";
    		  				  	tr = tr + "<td style='text-align:center'>"+eval(i+1)+"</td>";
    		  					tr = tr + "<td style='text-align:center'>"+zllist[i].shortName+"</td>";
    		  					if(zllist[i].SQS==0){
    		  						tr = tr + "<td style='text-align:center'>0</td>";
    		  					}else{
    		  						tr = tr + "<td style='text-align:center'>"+ '<a style="color:blue" href="javascript:void(0)" onclick="openZlTab(\'' +
    		  						"1,"+"APPLY,"+zllist[i].deptId + ",QB"+ '\')">'+zllist[i].SQS+'</a>'+"</td>";
    		  					}
								if(zllist[i].FMS==0){
									tr = tr + "<td style='text-align:center'>0</td>";
    		  					}else{
    		  						tr = tr + "<td style='text-align:center'>"+ '<a style="color:blue" href="javascript:void(0)" onclick="openZlTab(\'' +
    		  						"1,"+"APPLY,"+zllist[i].deptId + ",FM"+ '\')">'+zllist[i].FMS+'</a>'+"</td>";
    		  					}
    		  					tr = tr + "<td style='text-align:center'>"+zllist[i].proportion+"%</td>";
								if(zllist[i].YQS==0){
									tr = tr + "<td style='text-align:center'>0</td>";				
								}else{
									tr = tr + "<td style='text-align:center'>"+ '<a style="color:blue" href="javascript:void(0)" onclick="openZlTab(\'' +
									"1,"+"AUTH,"+zllist[i].deptId + ",QB"+ '\')">'+zllist[i].YQS+'</a>'+"</td>";						
								}
								if(zllist[i].FMYQS==0){
									tr = tr + "<td style='text-align:center'>0</td>";
								}else{
									tr = tr + "<td style='text-align:center'>"+ '<a style="color:blue" href="javascript:void(0)" onclick="openZlTab(\'' +
									"1,"+"AUTH,"+zllist[i].deptId + ",FM"+ '\')">'+zllist[i].FMYQS+'</a>'+"</td>";
								}
								if(zllist[i].LJYQS==0){
									tr = tr + "<td style='text-align:center'>0</td>";
								}else{
									tr = tr + "<td style='text-align:center'>"+ '<a style="color:blue" href="javascript:void(0)" onclick="openZlTab(\'' +
									"0,"+"AUTH,"+zllist[i].deptId + ",QB"+ '\')">'+zllist[i].LJYQS+'</a>'+"</td>";
								}
								if(zllist[i].LJFMYQS==0){
									tr = tr + "<td style='text-align:center'>0</td>";
								}else{
									tr = tr + "<td style='text-align:center'>"+ '<a style="color:blue" href="javascript:void(0)" onclick="openZlTab(\'' +
									"0,"+"AUTH,"+zllist[i].deptId + ",FM"+ '\')">'+zllist[i].LJFMYQS+'</a>'+"</td>";
								}
								if(zllist[i].JSMMRD==0){
									tr = tr + "<td style='text-align:center'>0</td>";
								}else{
									tr = tr + "<td style='text-align:center'>"+ '<a style="color:blue" href="javascript:void(0)" onclick="openJsmmTab(\'' +
									zllist[i].deptId + '\')">'+zllist[i].JSMMRD+'</a>'+"</td>";
								}
    		  				    tr = tr + "</tr>";
    		  				    $("#tllzl").append(tr);
    		  				}
    	  				}
    	  			},
    	  			error:function(jqXHR, textStatus, errorThrown) {// 处理错误情况
    	  	    	}
    			});
        }
        
        function openZlTab(startDate){
    		var title = "专利数据统计";
    		var url = ctx + "KIZL/CQ/AA/KIZLCQA02";
    		url = url+"?startDate="+startDate;
            $.modal.openTab(title, url, true);
      	}
        
        function openJsmmTab(startDate){
        	var title = "技术秘密数据统计";
    		var url = ctx + "KIZL/CQ/AA/KIZLCQA03";
    		url = url+"?startDate="+startDate;
            $.modal.openTab(title, url, true);
        }
        
    </script>
</body>
</html>