<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('领域统计条件页面')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="formData">
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version" href="#jb01" aria-expanded="false"
                           class="collapsed">领域统计
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jb01" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <fieldset>
                            <legend style="padding:3px;">查询信息:</legend>
                            <div class="form-group">
                                <div class="col-sm-6">
                                    <label class="col-sm-3 control-label">起止日期：</label>
                                    <div class="col-sm-4">
                                        <th:block
                                                th:include="/component/date::init(name='applyDateStart',id='applyDateStart')"/>
                                    </div>
                                    <div class="col-sm-4">
                                        <th:block
                                                th:include="/component/date::init(name='applyDateEnd',id='applyDateEnd')"/>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-6">
                                    <label class="col-sm-3 control-label is-required">组织:</label>
                                    <div class="col-sm-8">
                                        <select name="orgCode" class="form-control select2-hidden-accessible" required>
                                            <option value=""></option>
                                            <option th:each="adOrg : ${adOrgResult}" th:text="${adOrg.orgName}"
                                                    th:value="${adOrg.orgCode}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-6">
                                    <label class="col-sm-3 control-label">专利类型：</label>
                                    <div class="col-sm-8">
                                        <select name="patentType"
                                                th:with="dictData=${@dict.getDictList('KIZL','KI_PATENT_TYPEGN')}"
                                                class="form-control select2-hidden-accessible">
                                            <option value="">全部</option>
                                            <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-6">
                                    <label class="col-sm-3 control-label">是否实施：</label>
                                    <div class="col-sm-8">
                                        <select name="isImplementation" id="isImplementation"
                                                th:with="dictData=${@dict.getDictList('MPTY','is_yes_no')}"
                                                class="form-control select2-hidden-accessible">
                                            <option value="">全部</option>
                                            <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-6">
                                    <label class="col-sm-3 control-label">专利有效性：</label>
                                    <div class="col-sm-8">
                                        <select name="patentValidity" id="patentValidity"
                                                class="form-control select2-hidden-accessible">
                                            <option value="">全部</option>
                                            <option value="0">受理有效专利</option>
                                            <option value="1">授权有效专利</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </fieldset>
                    </div>
                </div>
            </div>
        </div>
    </form>

    <!--按钮区-->
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-primary"
                onclick="submitHandler()">
            技术领域统计
        </button>&nbsp;
        <button type="button" class="btn btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>返回
        </button>
    </div>
</div>
<script th:inline="javascript">

    var prefix = ctx + "KIZL/TJ";

    function submitHandler() {
        if ($.validate.form()) {
            var formData = $("#formData").serialize();
            $.modal.openTab("技术领域统计列表", prefix + "/KIZLTJC02/?" + formData, true);
        }
    }
</script>
</body>
</html>