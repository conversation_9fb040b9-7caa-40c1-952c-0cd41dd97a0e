<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('专利答复审查选择发明人')" />
	<th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">

<div class="wrapper wrapper-content">
<div class="form-group" th:include="include :: step(approveKind='KIZL_OtherDfsc',currentNode=${activityCode})"></div><br/>
	<form class="form-horizontal m" id="from01" th:object="${patentData}">
		<!--框-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<p style="font-size:16px">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;现收到国家知识产权局专利局审查员作出的审查意见通知书。
						请确认或更改“发明联系人”及“联系电话”，并发送。<br/>
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;需要提醒的是，根据通知书规定，向专利局提出答复的期限截止日期为
						<input id="dt" style="display:inline-block;width:100px;" th:value="${data.dtFmrjz}" readonly="true"/>，
						同时还需保证代理人有足够的时间与您沟通并准备正式答复文本。逾期答复将视为撤回该专利申请，因此，请发明人认真对待，
						以保证该专利的顺利授权，谢谢！</p><br/>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<input name="dfscId" id="dfscId" th:value="${data.dfscId}" type="hidden">
									<input name="applyId" id="applyId" th:value="*{applyId}" type="hidden">
									<input name="patentId" id="patentId" th:value="*{patentId}" type="hidden">
									<input name="taskId" th:value="${taskId}" type="hidden">
									<input name="activityCode" th:value="${activityCode}" type="hidden">
									<label class="col-sm-3 control-label">清能编号：</label>
									<div class="col-sm-6">
										<div class="form-control-static" th:utext="*{bgbh}"></div>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">申请号：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="*{patentNo}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">专利名称：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="*{applyName}"></div>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">答复次数：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="${data.extra2}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">申请日：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="*{slrq}"></div>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">接收编号：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="*{jsbh}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">第一申报部门：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="*{firstDeptName}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
                            	<div class="form-group">
                            		<label class="col-sm-3 control-label is-required">发明联系人：</label>
                            		<div class="col-sm-4">
		                                <th:block th:include="/component/selectUser :: init(isrequired=true,value=${data.fmr},isrequired=true,userCodeId='fmr',userNameId='fmrName')"></th:block>
		                            </div>
                            		<!-- <div class="form-group" th:include="KIZL/PA/paInclude :: choiceDfFmr(labelName='发明联系人：',userCodeId='fmr',
									userNameId='fmrName',selectType='S',applyId=*{applyId},value=${data.fmr},isrequired=true)"></div> -->
									<!-- <label class="col-sm-3 control-label is-required">发明联系人：</label>
									<div class="col-sm-9">
										<input name="fmr" id="fmr" th:value="${data.fmr}" type="hidden">
										<input name="fmrName" id="fmrName" class="form-control" th:value="${data.fmrName}" required/>
									</div> -->
								</div>
							</div>
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label is-required">联系电话：</label>
									<div class="col-sm-9">
										<input name="fmrPhone" id="fmrPhone" class="form-control" th:value="${data.fmrPhone}" required/>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label is-required">发明人：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="${data.fmsjrAll}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">管理员：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="${data.glyName}"></div>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label is-required">管理员联系电话：</label>
									<div class="col-sm-9">
										<input name="adminPhone" id="adminPhone" class="form-control" th:value="${data.adminPhone}" required/>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group" th:include="include :: initDate(id='dtScyj',name='dtScyj',labelName='审查意见发文日：',
								isrequired='true',strValue=${data.dtScyj})"></div>
							</div>
							<div class="col-sm-6">
								<div class="form-group" th:include="include :: initDate(id='dtFmrjz',name='dtFmrjz',labelName='答复截止日期：',
								isrequired='true',strValue=${data.dtFmrjz})"></div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
								<div class="form-group">
									<div th:include="KIZL/PA/paInclude :: initSwsinfo(id='swsId',name='swsId',divClass='col-sm-6',
									isPct='0',isfirst='true',labelName='代理事务所：',value=${data.swsId},see=true)"></div>
								</div>
							</div>
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">代理人邮箱：</label>
									<div class="col-sm-9">
										<input name="swsdlrEmail" id="swsdlrEmail" class="form-control" th:value="${data.swsdlrEmail}" readonly="true"/>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
                            	<div class="form-group">
                            		<input name="swsdlrName" id="swsdlrName" th:value="${data.swsdlrName}" type="hidden">
                            		<div th:include="KIZL/PA/paInclude :: initSwsperson(id='dlrNo', name='dlrNo',divClass='col-sm-6',
									labelName='代理人：',isfirst='true',swsGuid=*{swsGuid},value=${data.dlrNo},userType='1')"></div>
								</div>
							</div>
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label is-required">代理人联系电话：</label>
									<div class="col-sm-9">
										<input name="swsdlrPhone" id="swsdlrPhone" class="form-control" th:value="${data.swsdlrPhone}" readonly="true"/>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--框end-->
		<!--框-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#jsfx" class="collapsed">
							附件信息
							<span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
						</a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="jsfx" class="panel-collapse collapse in">
					<div class="panel-body">
						<div class="row">
							<label class="col-sm-2 control-label">申请相关文件：</label>
                            <div class="col-sm-10">
                                <div th:include="/component/attachment :: init(id='xgwjId',name='xgwjId',
                                sourceId=*{applyId},sourceModule='KIZL_XGWJ',see=true)"></div>
                            </div>
						</div>
						<div class="row">
							<label class="col-sm-2 control-label">共同申请协议：</label>
                            <div class="col-sm-10">
                                <div th:include="/component/attachment :: init(id='zscqId',name='zscqId',
                                sourceId=*{applyId},sourceModule='KIZL_ZSCQ',see=true)"></div>
                            </div>
						</div>
						<div class="row">
							<label class="col-sm-2 control-label">审查意见通知书：</label>
                            <div class="col-sm-10">
                                <div th:include="/component/attachment :: init(id='scyjtzsId',name='scyjtzsId',
                                sourceId=${data.dfscId},sourceModule='KIZL_SCYJ',see=true)"></div>
                            </div>
						</div>
						<div class="row">
							<label class="col-sm-2 control-label">答复审查意见正式文本：</label>
                            <div class="col-sm-10">
                                <div th:include="/component/attachment :: init(id='dfscyjzswbId',name='dfscyjzswbId',
                                sourceId=${data.dfscId},sourceModule='KIZL_ZSWB',see=true)"></div>
                            </div>
						</div>
					</div>
				</div>
				<!--折叠区域end-->
			</div><br/><br/>
			<th:block th:include="KIZL/PA/paInclude :: approveComment2"></th:block><br/>
			<th:block th:include="component/wfCommentList4 :: init(businessId=${data.dfscId})" />
			<p th:if="${processInstanceId}">
				<th:block th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})"></th:block>
			</p>
		</div>
		<!--框end-->
	</form>
</div>
<div class="toolbar toolbar-bottom" role="toolbar">
	<th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>
	<th:block th:include="include :: wfReturn(taskId=${taskId},callback=wfReturnHandler)"/>
	<button type="button" class="btn btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>返 回</button>
</div>
<br/><br/>
<!-- <script type="text/javascript" th:src="@{/product/js/kizl.js}"></script> -->
<script th:inline="javascript">
	var prefix = ctx + "KIZL/SQ/QT";
	
	_change();
	function _change(){
		var swsId = [[${data.swsId}]];
		if(swsId!=''){
			$.ajax({
		    	url: ctx + "KIZL/PA/AB/getSwsDlr/"+swsId+"/1",//根据代理所主键，用户类型获取代理人
	  			type:"GET",
	  			dataType:"json",
	  			success:function(result){
	  				$("#dlrNo").html("");
	  				for(var i=0;i<result.length;i++){
	  					$("#dlrNo").append('<option value="'+result[i].swspersonId+'">'+result[i].userName+'</option>');
	  				}
	  				_dlrchange();
	  			},
	  			error:function(jqXHR, textStatus, errorThrown) {
	  	    	}
			});
		}else{
			$("#dlrNo").html("");
		}
	}
	
	document.getElementById("dlrNo").onchange=_dlrchange;
	function _dlrchange(){
		if($("#dlrNo").val()!=''){
			$.ajax({
		    	url: ctx + "KIZL/PA/AB/getSwsPersonById/"+$("#dlrNo").val()+"/1",//根据人员编号，用户类型获取代理人信息
	  			type:"GET",
	  			dataType:"json",
	  			success:function(result){
	  				$("#swsdlrPhone").val(result.userLxrPhone);
	  				$("#swsdlrEmail").val(result.userEmail);
	  				$("#swsdlrName").val(result.userName);
	  			},
	  			error:function(jqXHR, textStatus, errorThrown) {
	  	    	}
			});
		}else{
			$("#swsdlrPhone").val("");
			$("#swsdlrEmail").val("");
			$("#swsdlrName").val("");
		}
	}
	
	document.getElementById("dtFmrjz").onchange=_changeDt;
	function _changeDt(){
  		$("#dt").val($("#dtFmrjz").val());
	}
	
	/*提交启动流程*/
	function submitHandler() {
		if ($.validate.form()) {
			var wf = "/submitDFSC";
	      	$.modal.confirm("确认提交吗？", function () {
					var config = {
		                    url: prefix + wf,
		                    type: "post",
		                    dataType: "json",
		                    data: $('#from01').serialize(),
		                    beforeSend: function () {
		                        $.modal.loading("正在处理中，请稍后...");
		                    },
		                    success: function (result) {
		                    	var str = result.msg;
		                    	if(str.indexOf("请联系系统管理员")!= -1){
		                    		$.modal.alertError(result.msg);
		                    		$.modal.closeLoading();
		                    	}else{
		                    		$.operate.alertSuccessTabCallback(result);
		                    	}
		                    }
		                };
		                $.ajax(config)
			})
		}
	}
	
	/*退回流程*/
    function wfReturnHandler(activityKey) {
        var comment = $("#comment").val();
        if ($.common.isEmpty(comment)) {
            $.modal.alertWarning("请输入退回意见");
            return;
        }
        $.modal.confirm("确认要退回吗？", function () {
            $.ajax({
                url: prefix + "/returnWF",
                type: "post",
                dataType: "json",
                data: $("#from01").serialize() + (activityKey ? ("&activityKey=" + activityKey) : ""),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $.operate.alertSuccessTabCallback(result);
                }
            });
        })
    }
</script>
</body>
</html>