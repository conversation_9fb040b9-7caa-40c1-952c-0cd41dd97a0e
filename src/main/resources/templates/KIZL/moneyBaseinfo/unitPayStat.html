<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境内专利_费用_缴费信息列表')"/>
    <th:block th:include="include :: baseJs"/>
    <script type="text/javascript" th:src="@{/product/js/kizl.js}"></script>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <th:block th:if="${#maps.containsKey(queryParams,'mergeOrg')}">
            <input id="year" type="hidden" th:value="${year}">
            <input id="statType" type="hidden" th:value="${statType}">
        </th:block>
        <div class="col-sm-12 search-collapse" th:unless="${#maps.containsKey(queryParams,'mergeOrg')}">
            <form id="formId" class="form-horizontal">
                <div class="row form-group">
                    <label class="col-sm-1 control-label">年度：</label>
                    <div class="col-sm-3">
                        <th:block
                                th:replace="/component/date :: init(id='year', name='year', format='yyyy', minView='4',strValue=${year},isrequired=true)"></th:block>
                    </div>
                    <label class="col-sm-1 control-label">统计类型：</label>
                    <div class="col-sm-3">
                        <select id="statType" name="statType" class="form-control" required>
                            <option value="gf">官费</option>
                            <option value="dlf">代理费</option>
                        </select>
                    </div>
                </div>

                <div class="row form-group">
                    <div class="col-sm-offset-8">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                class="fa fa-search"></i>&nbsp;搜索</a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="reset()"><i
                                class="fa fa-refresh"></i>&nbsp;重置</a>
                    </div>
                </div>

            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "kizl/moneyBaseinfo";

    function reset() {
        $.form.reset();
        $("#statType").trigger('change');
    }

    function queryParams(params) {
        let queryParams = $.table.queryParams(params);
        let backendParams = [[${queryParams}]]
        return Object.assign(queryParams, backendParams);
    }

    function unitPayStat(dwCode, dwName, mergeOrg) {
        var year = $("#year").val();
        var statType = $("#statType").val();
        var title = `${dwName}${year}年${statType == 'gf' ? '官费' : '代理费'}信息统计`
        $.modal.openTab(title, prefix + "/unitPayStat?dwCode=" + dwCode + "&year=" + year + "&statType=" + statType + "&mergeOrg=" + mergeOrg);
    }


    function departPayStat(dwCode, dwName) {
        var year = $("#year").val();
        var statType = $("#statType").val();
        var title = `${dwName}${year}年${statType == 'gf' ? '官费' : '代理费'}信息统计`
        $.modal.openTab(title, prefix + "/departPayStat?dwCode=" + dwCode + "&year=" + year + "&statType=" + statType);
    }

    function monthPayStat(dwCode, dwName, month, mergeOrg) {
        var year = $("#year").val();
        var statType = $("#statType").val();
        var title = `${dwName ?? ''}${year}年${month ? month + '月' : ''}${statType == 'gf' ? '官费' : '代理费'}信息统计`;
        var url = prefix + "/monthPayStat?year=" + year + "&statType=" + statType
        if (dwCode) {
            url += "&gldwCode=" + dwCode;
        }
        if (month) {
            url += "&month=" + month;
        }
        if (mergeOrg) {
            url += "&mergeOrg=" + mergeOrg;
        }
        $.modal.openTab(title, url);
    }

    $(function () {

        $("#gldwIn").select2({closeOnSelect: false})

        var options = {
            url: prefix + "/unitPayStat",
            pagination: false,
            showFooter: true,
            modalName: "各单位缴费信息",
            queryParams: queryParams,
            columns: [{
                checkbox: true
            },
                {
                    field: 'dwName',
                    title: '组织',
                    formatter: function (value, row, index) {
                        if (row.mergeOrg) {
                            return '<a href="javascript:void(0)" onclick="unitPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>';
                        } else {
                            return '<a href="javascript:void(0)" onclick="departPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\')"><span style="color: blue">' + value + '</span></a>';
                        }
                    },
                    footerFormatter: function (value) {
                        return "合计";
                    }
                },
                {
                    field: '01',
                    title: '1月',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'01\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'01\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['01']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, \'01\')"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
                {
                    field: '02',
                    title: '2月',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'02\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'02\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['02']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, \'02\')"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
                {
                    field: '03',
                    title: '3月',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'03\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'03\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['03']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, \'03\')"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
                {
                    field: '04',
                    title: '4月',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'04\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'04\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['04']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, \'04\')"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
                {
                    field: '05',
                    title: '5月',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'05\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'05\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['05']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, \'05\')"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
                {
                    field: '06',
                    title: '6月',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'06\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'06\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['06']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, \'06\')"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
                {
                    field: '07',
                    title: '7月',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'07\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'07\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['07']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, \'07\')"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
                {
                    field: '08',
                    title: '8月',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'08\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'08\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['08']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, \'08\')"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
                {
                    field: '09',
                    title: '9月',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'09\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'09\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['09']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, \'09\')"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
                {
                    field: '10',
                    title: '10月',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'10\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'10\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['10']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, \'10\')"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
                {
                    field: '11',
                    title: '11月',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'11\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'11\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['11']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, \'11\')"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
                {
                    field: '12',
                    title: '12月',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'12\', \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'12\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['12']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, \'12\')"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
                {
                    field: 'totalJe',
                    title: '累计',
                    formatter: function (value, row, index) {
                        if (value) {
                            if (row.mergeOrg) {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\', \'\', \'' + row.mergeOrg + '\')"><span style=", \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            } else {
                            value = '<a href="javascript:void(0)" onclick="monthPayStat(\'' + row.dwCode + '\', \'' + row.dwName + '\')"><span style=", \'' + row.mergeOrg + '\')"><span style="color: blue">' + value + '</span></a>'
                            }
                        }
                        return value
                    },
                    footerFormatter: function (value) {
                        var sum = 0;
                        for (var i in value) {
                            sum = sumFloat(sum, value[i]['totalJe']);
                        }
                        return '<a href="javascript:void(0)" onclick="monthPayStat(null, null, null)"><span style="color: blue">' + sum + '</span></a>'
                    }
                },
            ]
        };
        $.table.init(options);
    });
</script>
</body>
</html>