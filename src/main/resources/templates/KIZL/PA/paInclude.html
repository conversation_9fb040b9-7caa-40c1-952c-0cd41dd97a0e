<!--  弹框通用CSS -->
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="keywords" content="">
    <meta name="description" content="">
</head>

<!--选择参与部门  
orgCodeInputId 组织 input  ID 必填
orgNameInputId:组织名称 input  ID 必填
selectType 单选S 多选M 必填
level 最高层级 空是查询所有
orgCode 根组织  空是集团
-->
<div th:fragment="selectInventorOrg">
    <script type="text/javascript" th:inline="javascript">
        var ctx = [[@{/}]];
        var orgId = "orgId";
        var orgNameId = "deptName";

        function choiceInventorOrg(orgCodeInputId, orgNameInputId, selectType, level, orgCode, showLevel, callback) {
        	orgId = orgCodeInputId;
            orgNameId = orgNameInputId;
            if (selectType === undefined || selectType == null || selectType == '') {
                selectType = "S";
            }
            var url = ctx + "mpad/org/selectOrgList?selectType=" + selectType;
            if (!(level === undefined) && level != null) {
                url += "&level=" + level;
            }
            if (!(orgCode === undefined) && orgCode != null) {
                url += "&orgCode=" + orgCode;
            }
            if (!(showLevel === undefined) && showLevel != null) {
                url += "&showLevel=" + showLevel;
            }
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            url += "&values=" + $("#" + orgId).val();
            var options = {
                title: '选择组织',
                width: "380",
                height: '500',
                url: url,
                callBack: choiceInventorOrgCallback
            };
            $.modal.openOptions(options);
        }

        function choiceInventorOrgCallback(index, layero) {
            var tree = layero.find("iframe")[0].contentWindow.$._tree;
            var body = layer.getChildFrame('body', index);
            layero.find("iframe")[0].contentWindow.saveCheck();
            $("#" + orgId).val(body.find('#treeId').val());
            $("#" + orgNameId).val(body.find('#treeName').val());
            layer.close(index);
        }
    </script>
</div>


<!--选择发明人员  
userCodeInputId:工号input id 必填
userNameInputId:名称input id 必填
selectType 单选S 多选M 必填
orgCode  组织 默认是空 默认是集团所有组织
-->
<div th:fragment="selectInventorUser">
    <script type="text/javascript" th:inline="javascript">
        var ctx = [[@{/}]];
        var userId = "userId";
        var userNameId = "userName";

        function choiceInventorUser(userCodeInputId, userNameInputId, selectType, orgCode, callback) {
            userId = userCodeInputId;
            userNameId = userNameInputId;
            var url = ctx + 'mpad/user/selectUserList';
            if (selectType === undefined || selectType == null || selectType == '') {
                selectType = "S";
            }
            url += "?selectType=" + selectType;
            if (!(orgCode === undefined) && orgCode != null) {
                url += "&orgCode=" + orgCode;
            }
            var  values=$("#"+userCodeInputId).val();
            if(!(values=== undefined)  && values!=null){
                url+="&values="+values;
            }
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            url += "&userId=" + $("#" + userId).val() + "&userName" + $("#" + userNameId).val();
            $.modal.open("选择用户", url, '1000', '500');
        }

        function choiceUserCallback(userCode, userName) {
            $("#" + userId).val(userCode);
            $("#" + userNameId).val(userName);
        }
    </script>
</div>

<!--展示发明设计人员列表  
applyId:专利申请表主键 applyId 必填  seeDetail 0不显示操作列
-->
<div th:fragment="initInventorUser">
	<th:block th:with="inventorUserList=${T(com.baosight.bscdkj.ki.zl.utils.PatentApplyUtil).getInventorUserByApplyId(applyId)}">
	<th:block th:if="${inventorUserList.size()}>0">
	<div class="panel-group" role="tablist" aria-multiselectable="true">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">
					<a data-toggle="collapse" href="#fmsjr" class="collapsed">
						发明设计人
						<span class="pull-right">
	                   		<i class="fa fa-chevron-down"></i>
	                   	</span>
					</a>
				</h4>
			</div>
			<div id="fmsjr" class="panel-collapse collapse in">
				<div class="panel-body">
                    <div class="form-group">
                        <div class="col-sm-12">
                        	<table class="table table-bordered table-hover table-striped">
	   							<tr style="font-size: 16px; line-height: 40px;">
	   								<th style="width: 7%">序号</th>
	   								<th style="width: 11%">姓名</th>
	   								<th style="width: 11%">工号</th>
	   								<th style="width: 22%">单位</th>
	   								<th style="width: 12%">贡献系数(%)</th>
	   								<th style="width: 10%">岗位</th>
	   								<th style="width: 10%">备注</th>
	   								<th:block th:if="${seeDetail}!=0">
	   									<th style="width: 10%">操作</th>
	   								</th:block>
	   							</tr>
	   							<tbody id="patentCreatePersonTr">
   									<th:block th:each="fmr : ${inventorUserList}">
		   								<tr style="font-size: 16px; line-height: 40px;">
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${fmrStat.index+1}"></div></td>				
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${fmr.empName}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${fmr.empId}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${fmr.deptName}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${fmr.gxxs}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${fmr.postName}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${fmr.contentMemo}"></div></td>
											<th:block th:if="${seeDetail}!=0">
												<td><span class="btn btn-sm btn-primary" type="button" onclick="editRow(this)" th:value="${fmr.ryxxId}">详情</span></td>
											</th:block>
										</tr>
									</th:block>
	   							</tbody>
	   						</table>
                        </div>
                    </div>
				</div>
			</div>
		</div>
	</div>
	</th:if>
	</th:block>
    <script type="text/javascript" th:inline="javascript">
    function editRow(obj){
		var url = ctx+"KIZL/PA/AB/KIZLPABINXX/"+$(obj).attr("value");
        $.modal.open("发明人详情", url, '1000', '590');
	}
    </script>
</div>

<!--展示技术推广列表  
sqh:申请号 必填
-->
<div th:fragment="initJstg">
	<th:block th:with="inventorUserList=${T(com.baosight.bscdkj.ki.zl.utils.PatentApplyUtil).getJstgBySqh(sqh)}">
	<th:block th:if="${inventorUserList.size()}>0">
	<div class="panel-group" role="tablist" aria-multiselectable="true">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">
					<a data-toggle="collapse" href="#fmsjr" class="collapsed">
						技术推广
						<span class="pull-right">
	                   		<i class="fa fa-chevron-down"></i>
	                   	</span>
					</a>
				</h4>
			</div>
			<div id="fmsjr" class="panel-collapse collapse in">
				<div class="panel-body">
                    <div class="form-group">
                        <div class="col-sm-12">
                        	<table class="table table-bordered table-hover table-striped">
	   							<tr style="font-size: 16px; line-height: 40px;">
	   								<th style="width: 20%">序号</th>
	   								<th style="width: 35%">项目编号</th>
	   								<th style="width: 35%">项目名称</th>
	   							</tr>
	   							<tbody id="patentCreatePersonTr">
   									<th:block th:each="fmr : ${inventorUserList}">
		   								<tr style="font-size: 16px; line-height: 40px;">
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${fmrStat.index+1}"></div></td>				
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${fmr.projectNum}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${fmr.projectName}"></div></td>
										</tr>
									</th:block>
	   							</tbody>
	   						</table>
                        </div>
                    </div>
				</div>
			</div>
		</div>
	</div>
	</th:if>
	</th:block>
    <script type="text/javascript" th:inline="javascript">
    </script>
</div>

<!--展示实质审查列表  
patentId:专利信息表主键 patentId 必填
-->
<div th:fragment="initOtherDfsc">
	<th:block th:with="otherDfscList=${T(com.baosight.bscdkj.ki.zl.utils.PatentApplyUtil).getOtherDfscByPatentId(patentId)}">
	<th:block th:if="${otherDfscList.size()}>0">
	<div class="panel-group" role="tablist" aria-multiselectable="true">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">
					<a data-toggle="collapse" href="#otherdfsc1" class="collapsed">
						专利答复信息
						<span class="pull-right">
	                   		<i class="fa fa-chevron-down"></i>
	                   	</span>
					</a>
				</h4>
			</div>
			<div id="otherdfsc1" class="panel-collapse collapse in">
				<div class="panel-body">
                    <div class="form-group">
                        <div class="col-sm-12">
                        	<table class="table table-bordered table-hover table-striped">
	   							<tr style="font-size: 16px; line-height: 40px;">
	   								<th style="width: 18%">发明人意见</th>
	   								<th style="width: 18%">主管部门意见</th>
	   								<th style="width: 15%">答复次数</th>
	   								<th style="width: 15%">答复截止日期</th>
	   								<th style="width: 20%">流程状态</th>
	   								<th style="width: 20%">递交日期</th>
	   								<th style="width: 15%">操作</th>
	   							</tr>
	   							<tbody>
   									<th:block th:each="odfsc : ${otherDfscList}">
		   								<tr style="font-size: 16px; line-height: 40px;">
		   									<input type="hidden" th:id="'otherDfsclist['+${odfscStat.index}+'].dfscId'" th:name="'otherDfsclist['+${odfscStat.index}+'].dfscId'" th:value="${odfsc.dfscId}">
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${odfsc.finFmryj}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${odfsc.finZgbmyj}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${odfsc.extra2}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${odfsc.dtFmrjz}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${odfsc.extra1}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${odfsc.dtSubmit}"></div></td>
											<td><span class="btn btn-sm btn-primary" type="button" onclick="toOtherDfscDetail(this)" th:value="${odfscStat.index}">详情</span></td>
										</tr>
									</th:block>
	   							</tbody>
	   						</table>
                        </div>
                    </div>
				</div>
			</div>
		</div>
	</div>
	</th:if>
	</th:block>
    <script type="text/javascript" th:inline="javascript">
    function toOtherDfscDetail(div){
    	var i = $(div).attr("value");
		var id = $('input[name="otherDfsclist['+i+'].dfscId"]').val();
    	var title = "专利答复审查详细信息";
        var url = ctx+"KIZL/SQ/QT/queryDT?id="+id;
        $.modal.openTab(title, url, true);
	}
    </script>
</div>

<!--展示复审列表  
patentId:专利信息表主键 patentId 必填
-->
<div th:fragment="initOtherFs">
	<th:block th:with="otherFsList=${T(com.baosight.bscdkj.ki.zl.utils.PatentApplyUtil).getOtherFsByPatentId(patentId)}">
	<th:block th:if="${otherFsList.size()}>0">
	<div class="panel-group" role="tablist" aria-multiselectable="true">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">
					<a data-toggle="collapse" href="#otherfs1" class="collapsed">
						专利复审
						<span class="pull-right">
	                   		<i class="fa fa-chevron-down"></i>
	                   	</span>
					</a>
				</h4>
			</div>
			<div id="otherfs1" class="panel-collapse collapse in">
				<div class="panel-body">
                    <div class="form-group">
                        <div class="col-sm-12">
                        	<table class="table table-bordered table-hover table-striped">
	   							<tr style="font-size: 16px; line-height: 40px;">
	   								<th style="width: 15%">发明人意见</th>
	   								<th style="width: 15%">主管部门意见</th>
	   								<th style="width: 15%">复审次数</th>
	   								<th style="width: 15%">答复截止日期</th>
	   								<th style="width: 15%">流程状态</th>
	   								<th style="width: 15%">操作</th>
	   							</tr>
	   							<tbody>
   									<th:block th:each="ofs : ${otherFsList}">
		   								<tr style="font-size: 16px; line-height: 40px;">
		   									<input type="hidden" th:id="'otherFslist['+${ofsStat.index}+'].fsId'" th:name="'otherFslist['+${ofsStat.index}+'].fsId'" th:value="${ofs.fsId}">
		   									<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ofs.finFmryj}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ofs.finZgbmyj}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ofs.extra2}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ofs.dtFmrjz}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ofs.extra1}"></div></td>
											<td><span class="btn btn-sm btn-primary" type="button" onclick="toOtherFsDetail(this)" th:value="${ofsStat.index}">详情</span></td>
										</tr>
									</th:block>
	   							</tbody>
	   						</table>
                        </div>
                    </div>
				</div>
			</div>
		</div>
	</div>
	</th:if>
	</th:block>
    <script type="text/javascript" th:inline="javascript">
    function toOtherFsDetail(div){
    	var i = $(div).attr("value");
		var id = $('input[name="otherFslist['+i+'].fsId"]').val();
    	var title = "专利复审详细信息";
        var url = ctx+"KIZL/SQ/QU/queryDT?id="+id;
        $.modal.openTab(title, url, true);
	}
    </script>
</div>

<!--展示历次效益奖申报情况列表  
ssjId:专利实施奖主键 patentId ssjcs 必填 seeDetail 0不显示操作列
-->
<div th:fragment="initSsjHistory">
	<th:block th:with="ssjHistoryList=${T(com.baosight.bscdkj.ki.zl.utils.PatentApplyUtil).getSsjHistoryByPatentId(patentId,ssjcs)}">
	<th:block th:if="${ssjHistoryList.size()}>0">
	<div class="panel-group" role="tablist" aria-multiselectable="true">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">
					<a data-toggle="collapse" href="#ssjHistory1" class="collapsed">
						历次效益奖申报情况
						<span class="pull-right">
	                   		<i class="fa fa-chevron-down"></i>
	                   	</span>
					</a>
				</h4>
			</div>
			<div id="ssjHistory1" class="panel-collapse collapse in">
				<div class="panel-body">
                    <div class="form-group">
                        <div class="col-sm-12">
                        	<table class="table table-bordered table-hover table-striped">
	   							<tr style="font-size: 16px; line-height: 40px;">
	   								<th style="width: 15%">清能编号</th>
	   								<th style="width: 15%">实施奖类型</th>
	   								<th style="width: 13%">实施奖申报次数</th>
	   								<th style="width: 28%">申报单位</th>
	   								<th style="width: 13%">申报人</th>
	   								<th style="width: 13%">申报日期</th>
	   								<th:block th:if="${seeDetail}!=0">
	   									<th style="width: 8%">操作</th>
	   								</th:block>
	   							</tr>
	   							<tbody>
   									<th:block th:each="ssjHis : ${ssjHistoryList}">
		   								<tr style="font-size: 16px; line-height: 40px;">
		   									<input type="hidden" th:id="'ssjlist['+${ssjHisStat.index}+'].extra1'" th:name="'ssjlist['+${ssjHisStat.index}+'].extra1'" th:value="${ssjHis.ssjId}">
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ssjHis.bgbh}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ssjHis.ssjlx}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ssjHis.ssjcs}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ssjHis.deptName}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ssjHis.sbrName}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ssjHis.sbrDate}"></div></td>
											<th:block th:if="${seeDetail}!=0">
												<td><span class="btn btn-sm btn-primary" type="button" onclick="toDetailSSJ(this)" th:value="${ssjHisStat.index}">详情</span></td>
											</th:block>
										</tr>
									</th:block>
	   							</tbody>
	   						</table>
                        </div>
                    </div>
				</div>
			</div>
		</div>
	</div>
	</th:if>
	</th:block>
    <script type="text/javascript" th:inline="javascript">
    function toDetailSSJ(div){
		var i = $(div).attr("value");
		var id = $('input[name="ssjlist['+i+'].extra1"]').val();
		var title = "专利实施奖详细信息";
        var url = ctx+"KIZL/PA/AC/queryDT?id="+id;
        $.modal.openTab(title, url, true);
	}
    </script>
</div>

<!--展示实施奖效益清单  
ssjId:专利实施奖主键 patentId ssjcs 必填
-->
<div th:fragment="initSsjHistoryXy">
	<th:block th:with="ssjHistoryListXy=${T(com.baosight.bscdkj.ki.zl.utils.PatentApplyUtil).getSsjHistoryByPatentId(patentId,ssjcs)}">
	<th:block th:if="${ssjHistoryListXy.size()}>0">
	实施奖效益清单：<br/><br/>
	<div class="panel-group" role="tablist" aria-multiselectable="true">
		<div class="panel panel-default">
                    <div class="form-group">
                        <div class="col-sm-12">
                        	<table class="table table-bordered table-hover table-striped">
	   							<tr style="font-size: 16px; line-height: 40px;">
	   								<th style="width: 13%">实施奖次数</th>
	   								<th style="width: 15%">实施奖效益（万元）</th>
	   								<th style="width: 8%">操作</th>
	   							</tr>
	   							<tbody>
   									<th:block th:each="ssjXyHis : ${ssjHistoryListXy}">
		   								<tr style="font-size: 16px; line-height: 40px;">
		   									<input type="hidden" th:id="'ssjlist['+${ssjXyHisStat.index}+'].extra1'" th:name="'ssjlist['+${ssjXyHisStat.index}+'].extra1'" th:value="${ssjXyHis.ssjId}">
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ssjXyHis.ssjcs}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${ssjXyHis.extra2}"></div></td>
											<td><span class="btn btn-sm btn-primary" type="button" onclick="toDetailSSJXy(this)" th:value="${ssjXyHisStat.index}">详情</span></td>
										</tr>
									</th:block>
	   							</tbody>
	   						</table>
                        </div>
                    </div>
		</div>
	</div>
	</th:if>
	</th:block>
    <script type="text/javascript" th:inline="javascript">
    function toDetailSSJXy(div){
		var i = $(div).attr("value");
		var id = $('input[name="ssjlist['+i+'].extra1"]').val();
		var title = "专利实施奖详细信息";
        var url = ctx+"KIZL/PA/AC/queryDT?id="+id;
        $.modal.openTab(title, url, true);
	}
    </script>
</div>

<!--展示同来源专利技术秘密分配占比情况列表  
ssjId:专利实施奖主键 ssjId 必填
-->
<div th:fragment="initZbHistory">
	<th:block th:with="zbHistoryList=${T(com.baosight.bscdkj.ki.zl.utils.PatentApplyUtil).getZbHistoryBySsjId(ssjId)}">
	<th:block th:if="${zbHistoryList.size()}>0">
	<div class="panel-group" role="tablist" aria-multiselectable="true">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">
					<a data-toggle="collapse" href="#zbHistory1" class="collapsed">
						同来源专利技术秘密分配占比情况
						<span class="pull-right">
	                   		<i class="fa fa-chevron-down"></i>
	                   	</span>
					</a>
				</h4>
			</div>
			<div id="zbHistory1" class="panel-collapse collapse in">
				<div class="panel-body">
                    <div class="form-group">
                        <div class="col-sm-12">
                        	<table class="table table-bordered table-hover table-striped">
	   							<tr style="font-size: 16px; line-height: 40px;">
	   								<th style="width: 10%">类型</th>
		   							<th style="width: 10%">编号</th>
		   							<th style="width: 10%">状态</th>
		   							<th style="width: 35%">名称</th>
		   							<th style="width: 25%">单位</th>
		   							<th style="width: 21%">占比(%)</th>
		   							<th style="width: 10%">操作</th>
	   							</tr>
	   							<tbody>
   									<th:block th:each="zbHis : ${zbHistoryList}">
		   								<tr style="font-size: 16px; line-height: 40px;">
		   									<input type="hidden" th:id="'zblist['+${zbHisStat.index}+'].extra1'" th:name="'zblist['+${zbHisStat.index}+'].extra1'" th:value="${zbHis.extra1}">
		   									<input type="hidden" th:id="'zblist['+${zbHisStat.index}+'].extra2'" th:name="'zblist['+${zbHisStat.index}+'].extra2'" th:value="${zbHis.extra2}">
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${zbHis.extra2}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${zbHis.deptCode}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${zbHis.deptName}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${zbHis.extra3}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${zbHis.extra4}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${zbHis.extra5}"></div></td>
											<td><span class="btn btn-sm btn-primary" type="button" onclick="toDetailZLMM(this)" th:value="${zbHisStat.index}">详情</span></td>
										</tr>
									</th:block>
	   							</tbody>
	   						</table>
                        </div>
                    </div>
				</div>
			</div>
		</div>
	</div>
	</th:if>
	</th:block>
    <script type="text/javascript" th:inline="javascript">
    function toDetailZLMM(div){
		var i = $(div).attr("value");
		var id = $('input[name="zblist['+i+'].extra1"]').val();
		var extra2 = $('input[name="zblist['+i+'].extra2"]').val();
		if(extra2=="技术秘密"){
			var title = "技术秘密认定信息";
			var url = ctx + "kymm/technology/detail/"+id;
			$.modal.openTab(title, url, true);
		}else{
			var title = "专利申请详细信息";
	        var url = ctx+"KIZL/PA/AB/queryDT?id="+id;
	        $.modal.openTab(title, url, true);
		}
	}
    </script>
</div>


<!--展示实施奖子流程实施部门打分情况  
ssjId:专利实施奖主键 ssjId 必填 seeDetail 0不显示操作列
-->
<div th:fragment="initSsbmDfHistory">
	<th:block th:with="ssbmDfHistoryList=${T(com.baosight.bscdkj.ki.zl.utils.PatentApplyUtil).getSsbmDfHistoryBySsjId(ssjId)}">
	<th:block th:if="${ssbmDfHistoryList.size()}>0">
	<div class="panel-group" role="tablist" aria-multiselectable="true">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">
					<a data-toggle="collapse" href="#ssbmDfHistory1" class="collapsed">
						实施部门打分情况
						<span class="pull-right">
	                   		<i class="fa fa-chevron-down"></i>
	                   	</span>
					</a>
				</h4>
			</div>
			<div id="ssbmDfHistory1" class="panel-collapse collapse in">
				<div class="panel-body">
                    <div class="form-group">
                        <div class="col-sm-12">
                        	<table class="table table-bordered table-hover table-striped">
	   							<tr style="font-size: 16px; line-height: 40px;">
	   								<th width="20%" align="center" valign="top">实施部门</th>
									<th width="17%" align="center" valign="top">解决问题重要性</th>
									<th width="17%" align="center" valign="top">技术突破程度</th>
									<th width="17%" align="center" valign="top">实际运用效果</th>
									<th width="12%" align="center" valign="top">总分</th>
									<th:block th:if="${seeDetail}!=0">
									<th width="12%" align="center" valign="top">操作</th>
									</th:block>
	   							</tr>
	   							<tbody>
   									<th:block th:each="ssbmDfHis : ${ssbmDfHistoryList}">
   										<tr style="font-size: 16px; line-height: 40px;">
   											<input type="hidden" th:id="'ssbmDflist['+${ssbmDfHisStat.index}+'].extra1'" th:name="'ssbmDflist['+${ssbmDfHisStat.index}+'].extra1'" th:value="${ssbmDfHis.ssbmId}">
											<td class="control-group controls">
												<div class="form-control-static" th:utext="${ssbmDfHis.deptName}"></div>
											</td>
											<td class="control-group controls">
												<div class="form-control-static" th:utext="${ssbmDfHis.ldZy}"></div>
											</td>
											<td class="control-group controls">
												<div class="form-control-static" th:utext="${ssbmDfHis.ldJs}"></div>
											</td>
											<td class="control-group controls">
												<div class="form-control-static" th:utext="${ssbmDfHis.ldSj}"></div>
											</td>
											<td class="control-group controls">
												<div class="form-control-static" th:utext="${ssbmDfHis.ldZf}"></div>
											</td>
											<th:block th:if="${seeDetail}!=0">
											<td><span class="btn btn-sm btn-primary" type="button" onclick="toDeptScoreDetail(this)" th:value="${ssbmDfHisStat.index}">详情</span></td>
											</th:block>
										</tr>
									</th:block>
	   							</tbody>
	   						</table>
                        </div>
                    </div>
				</div>
			</div>
		</div>
	</div>
	</th:if>
	</th:block>
    <script type="text/javascript" th:inline="javascript">
    function toDeptScoreDetail(div){
		var i = $(div).attr("value");
		var id = $('input[name="ssbmDflist['+i+'].extra1"]').val();
		var title = "实施部门打分情况详情";
        var url = ctx+"KIZL/PA/AC/queryChildDF?id="+id;
        $.modal.openTab(title, url, true);
	}
    </script>
</div>

<!--展示专利申请贡献系数确认列表  
applyId:专利申请主键  必填
-->
<div th:fragment="initGxxsqrHistory">
	<th:block th:with="gxxsqrHistoryList=${T(com.baosight.bscdkj.ki.zl.utils.PatentApplyUtil).getGxxsqrHistoryByApplyId(applyId)}">
	<th:block th:if="${gxxsqrHistoryList.size()}>0">
	<div class="panel-group" role="tablist" aria-multiselectable="true">
		<div class="panel panel-default">
			<div class="panel-heading">
				<h4 class="panel-title">
					<a data-toggle="collapse" href="#gxxsqrHistory1" class="collapsed">
						贡献系数确认情况
						<span class="pull-right">
	                   		<i class="fa fa-chevron-down"></i>
	                   	</span>
					</a>
				</h4>
			</div>
			<div id="gxxsqrHistory1" class="panel-collapse collapse in">
				<div class="panel-body">
                    <div class="form-group">
                        <div class="col-sm-12">
                        	<table class="table table-bordered table-hover table-striped">
	   							<tr style="font-size: 16px; line-height: 40px;">
	   								<th style="width: 20%">部门</th>
	   								<th style="width: 20%">确认人</th>
	   								<th style="width: 20%">确认日期</th>
	   								<th style="width: 20%">贡献系数确认结果</th>
	   								<th style="width: 20%">意见</th>
	   							</tr>
	   							<tbody>
   									<th:block th:each="gxxsqrHis : ${gxxsqrHistoryList}">
		   								<tr style="font-size: 16px; line-height: 40px;">
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${gxxsqrHis.deptName}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${gxxsqrHis.submitName}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${gxxsqrHis.submitDate}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${gxxsqrHis.isQr}"></div></td>
											<td align="left" style="padding: 5px;"><div class="form-control-static" th:utext="${gxxsqrHis.submitYj}"></div></td>
										</tr>
									</th:block>
	   							</tbody>
	   						</table>
                        </div>
                    </div>
				</div>
			</div>
		</div>
	</div>
	</th:if>
	</th:block>
    <script type="text/javascript" th:inline="javascript">
    </script>
</div>

<!--信息所检索单列表
searchNo:检索单编号 input  ID 必填
-->
<div th:fragment="selectJsd">
    <script type="text/javascript" th:inline="javascript">
        var ctx = [[@{/}]];
        var searchVarCode = "searchNo";

        function choiceSearchNo(code, callback) {
        	searchVarCode = code;
            var url = ctx + "KIZL/PA/AB/selectSearchNoList";
            url += "?values=" + $("#" + searchVarCode).val();
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            $.modal.open("选择信息所检索单", url, '756', '392');
        }
        
        function choiceSearchNoCallback(searchNo) {
        	$("#" + searchVarCode).val(searchNo);
        }
    </script>
</div>

<!--项目表金苹果团队名称列表
jpgteam:金苹果团队名称 input  ID 必填
-->
<div th:fragment="selectJpgProject">
    <script type="text/javascript" th:inline="javascript">
        var ctx = [[@{/}]];
        var teamVarCode = "xmbh";
        var teamVarName = "xmmc";

        function choiceJpgProject(code, name) {
        	teamVarCode = code;teamVarName = name;
            var url = ctx + "KIZL/PA/AB/selectJpgProjectList";
            url += "?values=" + $("#" + teamVarCode).val()+"&name="+ $("#" + teamVarName).val();
            $.modal.open("选择金苹果团队", url, '780', '410');
        }
        
        function choiceJpgProjectCallback(xmbh,xmmc) {
        	$("#" + teamVarCode).val(xmbh);
        	$("#" + teamVarName).val(xmmc);
        }
    </script>
</div>

<!--金苹果检索人员维护-团队名称列表
jpgteam:金苹果团队名称 input  ID 必填
-->
<div th:fragment="selectJpgTeam">
    <script type="text/javascript" th:inline="javascript">
        var ctx = [[@{/}]];
        var teamVarCode = "jpgteam";

        function choiceJpgTeam(code, callback) {
        	teamVarCode = code;
            var url = ctx + "KIZL/PA/AB/selectJpgTeamList";
            url += "?values=" + $("#" + teamVarCode).val();
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            $.modal.open("选择金苹果团队", url, '756', '392');
        }
        
        function choiceJpgTeamCallback(jpgteam) {
        	$("#" + teamVarCode).val(jpgteam);
        }
    </script>
</div>

<!--申请人组织树
legalVarCode:编码 input  ID 必填
legalVarName:名称 input  ID 必填
-->
<div th:fragment="selectLegal">
    <script type="text/javascript" th:inline="javascript">
        var ctx = [[@{/}]];
        var legalVarCode = "legalVarCode";
        var legalVarName = "legalVarName";
        var legalMailAddress = "legalMailAddress";
        var legalPostOffice = "legalPostOffice";
        var legalLxrMoney = "legalLxrMoney";
        var legalLxrPhone = "legalLxrPhone";

        function choiceLegalNo(code, name, mailAddress, postOffice, lxrMoney, lxrPhone, callback) {
        	$('input[id="'+name+'"]').attr("readonly",true);
        	legalVarCode = code;
        	legalVarName = name;
        	legalMailAddress = mailAddress;
            legalPostOffice = postOffice;
            legalLxrMoney = lxrMoney;
            legalLxrPhone = lxrPhone;
            var url = ctx + "KIZL/PA/AB/selectList?selectType=S";
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            url += "&values=" + $("#" + legalVarCode).val();
            var options = {
                title: '选择申请人',
                width: "380",
                height: '500',
                url: url,
                callBack: choiceLegalNoCallback
            };
            $.modal.openOptions(options);
        }

        function choiceLegalNoCallback(index, layero) {
            var tree = layero.find("iframe")[0].contentWindow.$._tree;
            var body = layer.getChildFrame('body', index);
            layero.find("iframe")[0].contentWindow.saveCheck();
            $('input[id="'+legalVarCode+'"]').val(body.find('#treeId').val());
            $('input[id="'+legalVarName+'"]').val(body.find('#treeName').val());
            $('input[id="'+legalMailAddress+'"]').val(body.find('#mailAddress').val());
            $('input[id="'+legalPostOffice+'"]').val(body.find('#postOffice').val());
            $('input[id="'+legalLxrMoney+'"]').val(body.find('#lxrMoney').val());
            $('input[id="'+legalLxrPhone+'"]').val(body.find('#lxrPhone').val());
            layer.close(index);
        }
    </script>
</div>

<!--技术领域和用途树
techAreaVarCode:编码 input  ID 必填
techAreaVarName:名称 input  ID 必填
typeCode:JS技术领域YT用途  必填
-->
<div th:fragment="selectTechArea">
    <script type="text/javascript" th:inline="javascript">
        var ctx = [[@{/}]];
        var techAreaVarCode = "techAreaVarCode";
        var techAreaVarName = "techAreaVarName";

        function choiceTechArea(code, name, selectType, typeCode, callback) {
        	$('input[id="'+name+'"]').attr("readonly",true);
        	techAreaVarCode = code;
        	techAreaVarName = name;
            var url = ctx + "KIZL/PA/AB/selectTechAreaTree?selectType="+selectType+"&typeCode="+typeCode;
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            url += "&values=" + $("#" + techAreaVarCode).val();
            var options = {
                title: '选择',
                width: "380",
                height: '550',
                url: url,
                callBack: choiceTechAreaCallback
            };
            $.modal.openOptions(options);
        }
        
        function choiceTechAreaCallback(index, layero) {
            var tree = layero.find("iframe")[0].contentWindow.$._tree;
            var body = layer.getChildFrame('body', index);
            layero.find("iframe")[0].contentWindow.saveCheck();
            $("#" + techAreaVarCode).val(body.find('#treeId').val());
            $("#" + techAreaVarName).val(body.find('#treeName').val());
            layer.close(index);
        }
    </script>
</div>

<!--
name:input name 默认 selectbox
id:input id 默认 name值
labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认选择
divClass:div 的class 默认col-sm-8

see:回显(true,false) 默认false
isrequired:是否必填 默认false
value: 回显值
isPct  : 国内-0 国外-1 必填
isfirst: 是否添加首选项
firstName 首选项名称 默认 请选择
firstValue 首选项名称 默认 ''
gldwCode: 根据管理单位获取代理事务所
-->
<div th:fragment="initSwsinfo">
	    <th:block th:with="
	    				see=${see!=null&&see?true:false},
	               	 	name=${name==null?'selectbox':name},
	               	 	id=${id==null?name:id},
	               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
	               	 	labelName=${labelName==null?'选择':labelName},
	               	 	divClass=${divClass==null?'col-sm-8':divClass},
	               	 	firstName=${firstName==null?'请选择':firstName},
	               	 	firstValue=${firstValue==null?'':firstValue},
	               	 	gldwCode=${gldwCode==null?'':gldwCode}
	               	 	">
	
	            <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}">选择：</label>
	            <div  th:class="${divClass}"  th:if="${isPct!=null}">
	                <select th:name="${name}" th:class="form-control" th:if="${!see}" th:id="${id}"
	                        th:with="swsinfoData=${@swsinfo.getSwsList(isPct,gldwCode)}">
	                    <option th:if="${isfirst!=null && isfirst}" th:value="${firstValue}" th:text="${firstName}"></option>
	                    <option th:each="swsinfo : ${swsinfoData}" th:text="${swsinfo.swsName}"
	                            th:value="${swsinfo.swsId}" th:selected="${value eq swsinfo.swsId}"></option>
	                </select>
	            <div th:unless="${!see}"  class="form-control-static" th:utext="${@swsinfo.getSwsName(value)}"></div>
	        </div>
			<script th:inline="javascript" th:if="${!see}">
			    $('#'+[[${id}]]).select2();
			    if ([[${isrequired!=null && isrequired}]]) {
			        $("#" + [[${id}]]).attr("required", "");
			    }
			</script>
		</th:block>
</div>

<!-- 区分申请还是答复代理所 -->
<div th:fragment="initSwsinfoPlus">
	    <th:block th:with="
	    				see=${see!=null&&see?true:false},
	               	 	name=${name==null?'selectbox':name},
	               	 	id=${id==null?name:id},
	               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
	               	 	labelName=${labelName==null?'选择':labelName},
	               	 	divClass=${divClass==null?'col-sm-8':divClass},
	               	 	firstName=${firstName==null?'请选择':firstName},
	               	 	firstValue=${firstValue==null?'':firstValue},
	               	 	gldwCode=${gldwCode==null?'':gldwCode},
	               	 	extra1=${extra1==null?'':extra1}
	               	 	">
	
	            <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}">选择：</label>
	            <div  th:class="${divClass}"  th:if="${isPct!=null}">
	                <select th:name="${name}" th:class="form-control" th:if="${!see}" th:id="${id}"
	                        th:with="swsinfoData=${@swsinfo.getSwsListPlus(isPct,gldwCode,extra1)}">
	                    <option th:if="${isfirst!=null && isfirst}" th:value="${firstValue}" th:text="${firstName}"></option>
	                    <option th:each="swsinfo : ${swsinfoData}" th:text="${swsinfo.swsName}"
	                            th:value="${swsinfo.swsId}" th:selected="${value eq swsinfo.swsId}"></option>
	                </select>
	            <div th:unless="${!see}"  class="form-control-static" th:utext="${@swsinfo.getSwsName(value)}"></div>
	        </div>
			<script th:inline="javascript" th:if="${!see}">
			    $('#'+[[${id}]]).select2();
			    if ([[${isrequired!=null && isrequired}]]) {
			        $("#" + [[${id}]]).attr("required", "");
			    }
			</script>
		</th:block>
</div>

<!--
name:input name 默认 selectbox
id:input id 默认 name值
labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认选择
divClass:div 的class 默认col-sm-8

see:回显(true,false) 默认false
isrequired:是否必填 默认false
value: 回显值
swsGuid  : 事务所主键 必填
userType  : 1-代理人 2-联系人  必填
isfirst: 是否添加首选项
firstName 首选项名称 默认 请选择
firstValue 首选项名称 默认 ''
-->
<div th:fragment="initSwsperson">
	    <th:block th:with="
	    				see=${see!=null&&see?true:false},
	               	 	name=${name==null?'selectbox':name},
	               	 	id=${id==null?name:id},
	               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
	               	 	labelName=${labelName==null?'选择':labelName},
	               	 	divClass=${divClass==null?'col-sm-8':divClass},
	               	 	firstName=${firstName==null?'请选择':firstName},
	               	 	firstValue=${firstValue==null?'':firstValue}
	               	 	">
	
	            <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}">选择：</label>
	            <div  th:class="${divClass}">
	                <select th:name="${name}" th:class="form-control" th:if="${!see}" th:id="${id}"
	                        th:with="swsinfoData=${@swsinfo.getSwsDlrPlus(swsGuid,userType)}">
	                    <option th:if="${isfirst!=null && isfirst}" th:value="${firstValue}" th:text="${firstName}"></option>
	                    <option th:each="swsinfo : ${swsinfoData}" th:text="${swsinfo.userName}"
	                            th:value="${swsinfo.swspersonId}" th:selected="${value eq swsinfo.swspersonId}"></option>
	                </select>
	            <div th:unless="${!see}"  class="form-control-static" th:utext="${@swsinfo.getSwsDlrNamePlus(swsGuid,value,userType)}"></div>
	        </div>
			<script th:inline="javascript" th:if="${!see}">
			    $('#'+[[${id}]]).select2();
			    if ([[${isrequired!=null && isrequired}]]) {
			        $("#" + [[${id}]]).attr("required", "");
			    }
			</script>
		</th:block>
</div>

<!-- 审批意见 必填 -->
<div th:fragment="approveComment1">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                   href="#approveComment1">审批信息
                    <span class="pull-right"><i aria-hidden="true" class="fa fa-chevron-down"></i></span></a>
            </h4>
        </div>
        <!--折叠区域-->
        <div aria-expanded="false" class="panel-collapse collapse in" id="approveComment1">
            <div class="panel-body">
                <!--一列-->
                    	<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">填写意见：</label>
								</div>
							</div>
						</div>
						<div class="panel-body">
							<div class="form-group">
								<div class="col-sm-12">
									<textarea id="comment" name="comment" class="form-control" rows="8" style="white-space: break-spaces;" required></textarea>
								</div>
							</div>
						</div>
            </div>
        </div>
    </div>
</div>

<!-- 审批意见 非必填 -->
<div th:fragment="approveComment2">
    <div class="panel panel-default">
        <div class="panel-heading">
            <h4 class="panel-title">
                <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                   href="#approveComment2">审批信息
                    <span class="pull-right"><i aria-hidden="true" class="fa fa-chevron-down"></i></span></a>
            </h4>
        </div>
        <!--折叠区域-->
        <div aria-expanded="false" class="panel-collapse collapse in" id="approveComment2">
            <div class="panel-body">
                <!--一列-->
                    	<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">填写意见：</label>
								</div>
							</div>
						</div>
						<div class="panel-body">
							<div class="form-group">
								<div class="col-sm-12">
									<textarea id="comment" name="comment" class="form-control" style="white-space: break-spaces;" rows="8"></textarea>
								</div>
							</div>
						</div>
            </div>
        </div>
    </div>
</div>

<!--选择发明人
userCodeInputId:工号input id 必填
userNameInputId:名称input id 必填
selectType 单选S 多选M 必填
-->
<div th:fragment="choiceDfFmr">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	userCodeId=${userCodeId==null?'userCodeId':userCodeId},
               	 	userNameId=${userNameId==null?'userNameId':userNameId},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择人员：':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
                    selectType=${selectType==null?'S':selectType}
               	 	">
        <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}"></label>
        <div th:class="${divClass}" th:if="${see!=null && !see}">
            <div class="input-group">
                <input th:id="${userCodeId}" th:name="${userCodeId}" th:value="${value}" type="hidden"/> <input
                    class="form-control" th:id="${userNameId}" th:name="${userNameId}" th:onclick="choiceFmr([[${userCodeId}]],[[${userNameId}]],[[${selectType}]],[[${applyId}]])" th:required="${isrequired!=null && isrequired}"
                    th:value="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(value)}" type="text">
                <span class="input-group-addon detailOrgOrUser"><i
                        class="fa fa-search "></i></span>
            </div>
        </div>
        <div th:class="${divClass!=null?divClass+' form-control-static':'form-control-static'}" th:unless="${!see}" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(value)}"></div>
        <script type="text/javascript" th:inline="javascript">
        var ctx = [[@{/}]];
        var userId = "userId";
        var userNameId = "userName";

        function choiceFmr(userCode, userName, selectType, applyId) {
            userId = userCode;
            userNameId = userName;
            var url = ctx + 'KIZL/PA/AB/selectFmr'
            if (selectType === undefined || selectType == null || selectType == '') {
                selectType = "S";
            }
            url += "?selectType=" + selectType;
            if (!(applyId === undefined) && applyId != null) {
                url += "&applyId=" + applyId;
            }
            url += "&userId=" + $("#" + userId).val() + "&userName" + $("#" + userNameId).val();
            $.modal.open("选择人员", url, '1000', '500');
        }

        function choiceUserCallback(userCode, userName) {
            $("#" + userId).val(userCode);
            $("#" + userNameId).val(userName);
        }
    </script>
    </th:block>
</div>
