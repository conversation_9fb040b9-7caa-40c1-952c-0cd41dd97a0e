<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" xmlns:shiro="http://www.pollix.at/thymeleaf/shiro">
<head>
    <th:block th:include="include :: header('实施奖计算金额待办')"/>
</head>
<style>

    .form-control{
         border: 1px solid #ddd;
         border-radius: 4px;
         background: transparent;
         outline: 0;
         height: 30px;
         width: 60px;
         padding-left: 5px;
    }

    .KIZLFWG01 {
        background: #fff none;
        border: 1px solid #e5e6e7;
        border-radius: 4px;
        color: inherit;
        display: block;
        padding: 3px 6px 4px;
        -webkit-transition: border-color .15s ease-in-out 0s, box-shadow .15s ease-in-out 0s;
        transition: border-color .15s ease-in-out 0s, box-shadow .15s ease-in-out 0s;
        width: 80px;
        height: 31px;
        font-size: 8px
    }

    #login {
        display: none;
        position: absolute; /*让节点脱离文档流,我的理解就是,从页面上浮出来,不再按照文档其它内容布局*/
        top: 10%; /*节点脱离了文档流,如果设置位置需要用top和left,right,bottom定位*/
        left: 20%;
        z-index: 2; /*个人理解为层级关系,由于这个节点要在顶部显示,所以这个值比其余节点的都大*/
        background: white;
    }

    #over {
        width: 100%;
        height: 100%;
        opacity: 0.8; /*设置背景色透明度,1为完全不透明,IE需要使用filter:alpha(opacity=80);*/
        filter: alpha(opacity=80);
        display: none;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 1;
        background: silver;
    }
</style>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label style="width: 100px;">名称：</label>
                            <input type="hidden" name="businessType" id="businessType" value="KIZL"/>
                            <input type="text" name="businessNameLike" id="businessNameLike"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>

        <div id="login" class="layui-layer layui-layer-dialog layer-ext-moon" type="dialog" times="2" showtime="0"
             contype="string" style="z-index: 2; position: fixed; top: 20%; left: 29%;">
            <div class="layui-layer-title">更新</div>
            <div id="" class="layui-layer-content layui-layer-padding"><i class="layui-layer-ico layui-layer-ico3">
            </i>批量更新系数：范围(0-3)
            </div>
            <div class="form-group" style="margin-left:12%"><input id="popValue" class="form-control" placeholder="0-3"
                                                                   oninput="value=value.match(/[0-9]{1,10}\.?[0-9]{0,6}/)"
                                                                   style="width:200px"/></div>
            <span class="layui-layer-setwin"></span>
            <div class="layui-layer-btn layui-layer-btn-">
                <button type="button" class="btn btn-sm btn-primary" onclick="ok()">
                    <i class="fa fa-check"></i>确定
                </button>
                <button type="button" class="btn btn-sm btn-danger" onclick="hide()">
                    <i class="fa fa-reply-all"></i>关闭
                </button>
            </div>
            <span class="layui-layer-resize"></span>
        </div>

        <div id="login1">
            <div class="panel-collapse collapse in" aria-expanded="false">
                <div class="panel-body">
                    <div class="row">
                        <div>

                        </div>
                    </div>
                </div>
            </div>

        </div>
        <div id="over"></div>

        <div class="toolbar toolbar-bottom" role="toolbar">
            <button type="button" class="btn btn-danger" onclick="show()">
                <i class="fa fa-edit"></i>批量更新系数
            </button>
            <button type="button" class="btn btn-danger" onclick="batchSubmit()">
                <i class="fa fa-check"></i>提交到主管部门主管审批
            </button>
        </div>
    </div>
</div>
<th:block th:include="include :: baseJs"/>
<script th:inline="javascript">
    console.log("DB07");

    /* 弹出层操作start */
    function show() {
        var index = $('#bootstrap-table').bootstrapTable('getSelections');
        if (index.length == 0) {
            $.modal.alertWarning("请至少选择一条记录");
            return;
        }
        var login = document.getElementById('login');
        var over = document.getElementById('over');
        login.style.display = "block";
        over.style.display = "block";
    }

    function hide() {
        var login = document.getElementById('login');
        var over = document.getElementById('over');
        login.style.display = "none";
        over.style.display = "none";
    }

    function ok() {
        var index = $('#bootstrap-table').bootstrapTable('getSelections');
        if (index.length == 0) {
            hide();
            return;
        }
        var popValue = $("#popValue").val();
        if (popValue != '' && (popValue > 3 || popValue < 0)) {
            $("#popValue").val("");
            alert("系数范围：0-3");
            return;
        }
        var ids = getIdSelections();
        for (let i of ids) {
            var ssjlx = $("#m-" + i + "-ssjlx").val();
            if (ssjlx == 'XY') {
                $("#m-" + i + "-sjxs").val(popValue);
                onLoad(i, '2');
            }
        }
        hide();
    }

    /* 弹出层操作end */

    $(function () {
        var options = {
            url: ctx + "KIZL/PA/AA/queryDB07",
            uniqueId: "rowId",
            queryParams: queryParams,
            pageList: [100, 200, 300],
            pageSize: 100,
            columns: [{
                checkbox: true
            },
                {
                    field: 'businessName',
                    title: '名称'
                    // formatter: function (value, row, index) {
                    // 	var patentname = row.businessName;
                    // 	if(row.ssjlx=='SP'){
                    // 		patentname = patentname + '&nbsp;<span class="label label-success">水平奖</span>'
                    // 	}else if(row.ssjlx=='XY'){
                    // 		patentname = patentname + '&nbsp;<span class="label label-primary">效益奖</span>'
                    // 	}
                    //     return patentname;
                    // }
                },
                {
                    field: 'jsbh',
                    title: '接收编号'
                },
                {
                    field: 'firstDeptName',
                    title: '申报部门'
                },
                {
                    field: 'SBR',
                    title: '申请人'
                },
                {
                    field: 'ssjlx',
                    title: '申报奖项',
                    formatter: function (value, row, index) {
                        if (row.ssjlx == 'SP') {
                            return '水平奖';
                        } else if (row.ssjlx == 'XY') {
                            return '效益奖';
                        } else {
                            return '-';
                        }
                    }
                },
                {
                    field: 'glyZy',
                    title: '解决问题重要性',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<input id="m-' + index + '-glyZy" type="number" class="form-control" placeholder="0-30" onchange="toChange(' + index + ')" type="text" onkeyup="this.value=this.value.replace(/[^0-9-]+/,' + "''" + ');" value="' + row.glyZy + '">');
                        actions.push('<input id="m-' + index + '-glyZf" type="hidden" value="' + row.glyZf + '">');
                        actions.push('<input id="m-' + index + '-businessId" type="hidden" value="' + row.businessId + '">');
                        actions.push('<input id="m-' + index + '-jsbh" type="hidden" value="' + row.jsbh + '">');
                        actions.push('<input id="m-' + index + '-ssjlx" type="hidden" value="' + row.ssjlx + '">');
                        actions.push('<input id="m-' + index + '-patentType" type="hidden" value="' + row.patentType + '">');
                        actions.push('<input id="m-' + index + '-gfxs" type="hidden" value="' + row.gfxs + '">');
                        return actions.join('');
                    }
                },
                {
                    field: 'glyJs',
                    title: '技术突破程度',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<input id="m-' + index + '-glyJs" type="number" class="form-control" placeholder="0-30" onchange="toChange(' + index + ')" type="text" onkeyup="this.value=this.value.replace(/[^0-9-]+/,' + "''" + ');" value="' + row.glyJs + '">');
                        return actions.join('');
                    }
                },
                {
                    field: 'glySj',
                    title: '实际运用效果',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<input id="m-' + index + '-glySj" type="number" class="form-control" placeholder="0-40" onchange="toChange(' + index + ')" type="text" onkeyup="this.value=this.value.replace(/[^0-9-]+/,' + "''" + ');" value="' + row.glySj + '">');
                        return actions.join('');
                    }
                },
                {
                    field: 'glyZf',
                    title: '总分',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<input id="m-' + index + '-zf" class="form-control" readonly type="text" value="' + row.glyZf + '">');
                        return actions.join('');
                    }
                },
                {
                    title: '系数(%)',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.ssjlx == 'XY') {
                            actions.push('<input id="m-' + index + '-sjxs" class="form-control" placeholder="0-3" onchange="toChangeXs(' + index + ')" type="text" oninput="value=value.match(/[0-9]{1,10}\.?[0-9]{0,6}/)" value="' + row.sjxs + '">');
                        } else if (row.ssjlx == 'SP') {
                            actions.push('-');
                        }
                        return actions.join('');
                    }
                },
                {
                    title: '是否留成',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<select class="KIZLFWG01" id="m-' + index + '-isLc" onchange="toChangeLc(' + index + ')">');
                        if (row.isLc == '1') {
                            actions.push('<option value="1" selected >留成</option>');
                            actions.push('<option value="0" >不留成</option>');
                        } else {
                            actions.push('<option value="1">留成</option>');
                            actions.push('<option value="0" selected >不留成</option>');
                        }
                        actions.push('</select>');
                        return actions.join('');
                    }
                },
                {
                    field: 'totalQe',
                    title: '计算全额(元)',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<input id="m-' + index + '-jsqe" class="form-control" readonly type="text" value="' + row.jsqe + '">');
                        return actions.join('');
                    }
                },
                {
                    field: 'totalSpe',
                    title: '审批额(元)',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<input id="m-' + index + '-spje" class="form-control" readonly type="text" value="' + row.spje + '">');
                        return actions.join('');
                    }
                },
                {
                    field: 'totalSfj',
                    title: '实发金额(元)',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<input id="m-' + index + '-sfje" class="form-control" readonly type="text" value="' + row.sfje + '">');
                        return actions.join('');
                    }
                },
                {
                    title: '经济效益(万元)',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.ssjlx == 'XY') {
                            actions.push('<input id="m-' + index + '-extra1" class="form-control" readonly type="text" value="' + row.extra1 + '">');
                        } else if (row.ssjlx == 'SP') {
                            actions.push('-');
                        }
                        return actions.join('');
                    }
                },
                {
                    title: '计奖效益(万元)',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        if (row.ssjlx == 'XY') {
                            actions.push('<input id="m-' + index + '-extra1" class="form-control" readonly type="text" value="' + row.extra2 + '">');
                        } else if (row.ssjlx == 'SP') {
                            actions.push('-');
                        }
                        return actions.join('');
                    }
                },
                {
                    field: 'SBR',
                    title: '申请人'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-primary btn-xs " href="javascript:void(0)" onclick="doHandle(\'' + row.processCode + '\',\'' + row.currentActivity + '\',\'' + row.pageNo + '\',\'' + row.businessId + '\',\'' + row.flowId + '\',\'' + row.taskId + '\',\'' + row.currentActivityName + '\',\'' + row.departmentNo + '\')"><i class="fa fa-edit"></i> 处理</a>');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function queryParams(params) {
        var search = $.table.queryParams(params);
        search.businessNameLike = $("#businessNameLike").val();
        search.processCode = [[${query.processCode}]];
        search.currentActivity = [[${query.activityCode}]];
        return search;
    }

    function toChange(index) {
        var a = $("#m-" + index + "-glyZy").val();
        var b = $("#m-" + index + "-glyJs").val();
        var c = $("#m-" + index + "-glySj").val();
        if (a != '' && (a > 30 || a < 0)) {
            $("#m-" + index + "-glyZy").val("");
            $.modal.alertWarning("解决问题重要性：打分范围0-30分");
            return;
        }
        if (b != '' && (b > 30 || b < 0)) {
            $("#m-" + index + "-glyJs").val("");
            $.modal.alertWarning("技术突破程度：打分范围0-30分");
            return;
        }
        if (c != '' && (c > 40 || c < 0)) {
            $("#m-" + index + "-glySj").val("");
            $.modal.alertWarning("实际运用效果：打分范围0-40分");
            return;
        }
        if (a != '' && b != '' && c != '') {
            $("#m-" + index + "-glyZf").val(eval(a) + eval(b) + eval(c));
            $("#m-" + index + "-zf").val(eval(a) + eval(b) + eval(c));
            onLoad(index, '1');
        }
    }

    function toChangeXs(index) {
        var changeXs = $("#m-" + index + "-sjxs").val();
        if (changeXs != '' && (changeXs > 3 || changeXs < 0)) {
            $("#m-" + index + "-sjxs").val("");
            alert("系数范围：0-3");
            return;
        }
        onLoad(index, '2');
    }

    function toChangeLc(index) {
        onLoad(index, '2');
    }

    function onLoad(index, xstype) {
        var tableData = $("#bootstrap-table").bootstrapTable('getData');
        var islc = $("#m-" + index + "-isLc option:selected").val();
        var gfxs = tableData[index].gfxs;//股份系数
        var patentType = tableData[index].patentType;
        if ('SP' == tableData[index].ssjlx) {
            var a = $("#m-" + index + "-glyZy").val();
            var b = $("#m-" + index + "-glyJs").val();
            var c = $("#m-" + index + "-glySj").val();
            var zf = eval(a) + eval(b) + eval(c);
            if (zf != 'undefined' && !isNaN(zf) && patentType != 'undefined' && islc != 'undefined' && gfxs != 'undefined') {
                if (zf != '' && patentType != '' && islc != '' && gfxs != '') {
                    $.ajax({
                        url: ctx + "KIZL/PA/AC/getSpje/" + zf + "," + patentType + "," + islc + "," + gfxs,
                        type: "GET",
                        dataType: "json",
                        success: function (res) {
                            var totalQe = res.totalQe;
                            var totalSpe = res.totalSpe;
                            var totalSfj = res.totalSfj;
                            if (totalQe != undefined) {
                                $("#m-" + index + "-jsqe").val(totalQe);
                            }
                            if (totalSpe != undefined) {
                                $("#m-" + index + "-spje").val(totalSpe);
                            }
                            if (totalSfj != undefined) {
                                $("#m-" + index + "-sfje").val(totalSfj);
                            }
                        },
                        error: function (jqXHR, textStatus, errorThrown) {// 处理错误情况
                        }
                    });
                }
            }
        } else {
            var sjxs = $("#m-" + index + "-sjxs").val();//奖励系数
            var jsqe = tableData[index].extra2;//计奖效益（元）
            var glyZf = $("#m-" + index + "-glyZf").val();
            if (jsqe != 'undefined' && islc != 'undefined' && gfxs != 'undefined') {
                if (jsqe != '' && islc != '' && gfxs != '') {
                    if (glyZf == null || glyZf == '' || isNaN(glyZf)) {
                        glyZf == 'undefined';
                    }
                    if (sjxs == null || sjxs == '') {
                        sjxs == 'undefined';
                    }
                    $.ajax({
                        url: ctx + "KIZL/PA/AC/getXyje/" + jsqe + "," + islc + "," + gfxs + "," + sjxs + "," + patentType + "," + glyZf + "," + xstype,
                        type: "GET",
                        dataType: "json",
                        success: function (res) {
                            console.log("res",res);
                            a = res.zy;
                            b = res.js;
                            c = res.sj;
                            zf = res.glyzf;
                            if(a!=undefined){
                                $("#m-" + index + "-glyZy").val(a);
                            }
                            if(b!=undefined){
                                $("#m-" + index + "-glyJs").val(b);
                            }
                            if(c!=undefined){
                                $("#m-" + index + "-glySj").val(c);
                            }
                            if(zf!=undefined){
                                $("#m-" + index + "-glyZf").val(zf);
                                $("#m-" + index + "-zf").val(zf);
                            }
                            var totalQe = res.totalQe;
                            var totalSpe = res.totalSpe;
                            var totalSfj = res.totalSfj;
                            if (totalQe != undefined) {
                                $("#m-" + index + "-jsqe").val(totalQe);
                            }
                            if (totalSpe != undefined) {
                                $("#m-" + index + "-spje").val(totalSpe);
                            }
                            if (totalSfj != undefined) {
                                $("#m-" + index + "-sfje").val(totalSfj);
                            }

                            var totalSjxs = res.totalSjxs;
                            if (totalSjxs != undefined) {
                                $("#m-" + index + "-sjxs").val(totalSjxs);
                            }
                        },
                        error: function (jqXHR, textStatus, errorThrown) {// 处理错误情况
                        }
                    });
                }
            }
        }
    }

    function getIdSelections() {
        return $.map($('#bootstrap-table').bootstrapTable('getSelections'), function (row) {
            return row.serialNumber;
        });
    }

    /* 批量提交流程 */
    function batchSubmit() {
        if ($.validate.form()) {
            var index = $('#bootstrap-table').bootstrapTable('getSelections');
            if (index.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            /* 表单给bootstrap-table赋值 */
            var arrys = [];
            var ids = getIdSelections();
            var j = 0;
            for (let i of ids) {
                var data = {};
                var ssjlx = $("#m-" + i + "-ssjlx").val();
                data["ssjId"] = $("#m-" + i + "-businessId").val();
                data["jsbh"] = $("#m-" + i + "-jsbh").val();
                data["ssjlx"] = ssjlx;
                data["patentType"] = $("#m-" + i + "-patentType").val();
                data["gfxs"] = $("#m-" + i + "-gfxs").val();

                data["glyZy"] = $("#m-" + i + "-glyZy").val();
                data["glyJs"] = $("#m-" + i + "-glyJs").val();
                data["glySj"] = $("#m-" + i + "-glySj").val();
                data["glyZf"] = $("#m-" + i + "-glyZf").val();

                if (ssjlx == 'SP') {
                    if ($("#m-" + i + "-glyZy").val() == '') {
                        $.modal.alertWarning("水平奖打分栏不能为空！");
                        return;
                    }
                    if ($("#m-" + i + "-glyJs").val() == '') {
                        $.modal.alertWarning("水平奖打分栏不能为空！");
                        return;
                    }
                    if ($("#m-" + i + "-glySj").val() == '') {
                        $.modal.alertWarning("水平奖打分栏不能为空！");
                        return;
                    }
                    data["sjxs"] = 100;
                } else {
                    if ($("#m-" + i + "-sjxs").val() == '') {
                        $.modal.alertWarning("效益奖系数不能为空！");
                        return;
                    }
                    data["sjxs"] = $("#m-" + i + "-sjxs").val();
                }

                data["islc"] = $("#m-" + i + "-isLc").val();

                data["jsqe"] = $("#m-" + i + "-jsqe").val();
                data["spje"] = $("#m-" + i + "-spje").val();
                data["sfje"] = $("#m-" + i + "-sfje").val();
                arrys[j] = data;
                j++;
            }
            $.modal.confirm("确认提交吗？", function () {
                var config = {
                    url: ctx + "KIZL/PA/AC/batchSubmitWFDF",
                    type: "post",
                    dataType: "json",
                    data: JSON.stringify({"ssjBaseinfo": arrys}),
                    contentType: 'application/json;charset=UTF-8',
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        var str = result.msg;
                        if (str.indexOf("请联系系统管理员") != -1) {
                            $.modal.alertError(result.msg);
                            $.modal.closeLoading();
                        } else {
                            $.modal.alert(result.msg);
                            $.modal.closeLoading();
                            $.table.search();
                        }
                    }
                };
                $.ajax(config)
            })
        }
    }
</script>
<script type="text/javascript" th:src="@{/product/js/kizldb.js}"></script>
</body>
</html>