<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('专利实施奖_分配占比审批')" />
	<th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">

<div class="wrapper wrapper-content">
	<form class="form-horizontal m" id="from01" th:object="${data}">
		<!--框-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<input name="ssjId" id="ssjId" th:value="*{ssjId}" type="hidden">
									<input name="patentId" id="patentId" th:value="*{patentId}" type="hidden">
									<input name="applyId" id="applyId" th:value="${patentData.applyId}" type="hidden">
									<input name="taskId" th:value="${taskId}" type="hidden">
									<input name="activityCode" th:value="${activityCode}" type="hidden">
									<label class="col-sm-3 control-label">专利实施奖信息：</label>
									<div class="col-sm-9">
										<div class="form-control-static"><a onClick="onOpenDetailSsj()" style="color: blue">点击查看</a></div>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--框end-->
		
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#fmsjr" class="collapsed">
							同来源专利技术秘密
							<span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
						</a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="fmsjr" class="panel-collapse collapse in">
					<div class="panel-body">
	                    <div class="form-group">
	                        <div class="col-sm-12">
	                        	<table class="table table-bordered table-hover table-striped">
		   							<tr style="font-size: 16px; line-height: 40px;">
		   								<th style="width: 10%">类型</th>
		   								<th style="width: 10%">编号</th>
		   								<th style="width: 10%">状态</th>
		   								<th style="width: 35%">名称</th>
		   								<th style="width: 25%">单位</th>
		   								<th style="width: 21%">占比(%)</th>
		   								<th style="width: 10%">操作</th>
		   							</tr>
		   							<tbody id="patentCreatePersonTr">
		   								<th:block th:each="fmr : ${patentData.proportionlist}">
			   								<tr style="font-size: 16px; line-height: 40px;">
			   									<input type="hidden" th:id="'alist['+${fmrStat.index}+'].extra1'" th:name="'alist['+${fmrStat.index}+'].extra1'" th:value="${fmr.extra1}">
												<td align="left" style="padding: 5px;">
													<input type="text" th:id="'alist['+${fmrStat.index}+'].extra2'" th:name="'alist['+${fmrStat.index}+'].extra2'" th:value="${fmr.extra2}" class="form-control" style="width: 90%; padding: 5px;" readonly="readonly">
												</td>
												<td align="left" style="padding: 5px;">
													<input type="text" th:value="${fmr.deptCode}" class="form-control" style="width: 90%; padding: 5px;" readonly="readonly">
												</td>
												<td align="left" style="padding: 5px;">
													<input type="text" th:value="${fmr.deptName}" class="form-control" style="width: 90%; padding: 5px;" readonly="readonly">
												</td>
												<td align="left" style="padding: 5px;">
													<input type="text" th:id="'alist['+${fmrStat.index}+'].extra3'" th:name="'alist['+${fmrStat.index}+'].extra3'" th:value="${fmr.extra3}" class="form-control" style="width: 90%; padding: 5px;" readonly="readonly">
												</td>
												<td align="left" style="padding: 5px;">
													<input type="text" th:id="'alist['+${fmrStat.index}+'].extra4'" th:name="'alist['+${fmrStat.index}+'].extra4'" th:value="${fmr.extra4}" class="form-control" style="width: 90%; padding: 5px;" readonly="readonly">	
												</td>
												<td align="left" style="padding: 5px;">
													<input type="text" th:id="'alist['+${fmrStat.index}+'].extra5'" th:name="'alist['+${fmrStat.index}+'].extra5'" th:value="${fmr.extra5}" class="form-control" style="width: 90%; padding: 5px;" readonly="readonly">	
												</td>
												<td><span class="btn btn-sm btn-primary" type="button" onclick="toDetail(this)" th:value="${fmrStat.index}">详情</span></td>
											</tr>
										</th:block>
		   							</tbody>
		   						</table>
	                        </div>
	                    </div>
					</div>
				</div>
				<!--折叠区域end-->
			</div>
			<th:block th:include="KIZL/PA/paInclude :: approveComment2"></th:block><br/>
			<th:block th:include="component/wfCommentList4 :: init(businessId=${data.ssjId})" />
			<p th:if="${processInstanceId}">
				<th:block th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})"></th:block>
			</p>
		</div>
		
	</form>
</div>
<div class="toolbar toolbar-bottom" role="toolbar">
	<th:block th:include="component/wfSubmit:: init(taskId=${taskId},callback=submitHandler)"/>
	<th:block th:include="include :: wfReturn(taskId=${taskId},callback=wfReturnHandler)"/>
	<button type="button" class="btn btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>返 回</button>
</div>
<br/><br/>
<script th:inline="javascript">
	var prefix = ctx + "KIZL/PA/AC";
	
	//验证
	$("#from01").validate({
		ignore: ":hidden",
		focusCleanup : true
	});
	
	/*提交启动流程*/
	function submitHandler(transitionKey) {
    	if ($.validate.form()) {
	      	$.modal.confirm("确认提交吗？", function () {
				var config = {
	                    url: prefix + "/submitWF",
	                    type: "post",
	                    dataType: "json",
	                    data: $('#from01').serialize() + (transitionKey ? ("&transitionKey=" + transitionKey) : ""),
	                    beforeSend: function () {
	                        $.modal.loading("正在处理中，请稍后...");
	                    },
	                    success: function (result) {
	                    	var str = result.msg;
	                    	if(str.indexOf("请联系系统管理员")!= -1){
	                    		$.modal.alertError(result.msg);
	                    		$.modal.closeLoading();
	                    	}else{
	                    		$.operate.alertSuccessTabCallback(result);
	                    	}
	                    }
	                };
	                $.ajax(config)
			})
		}
	}
	
	/*退回流程*/
    function wfReturnHandler(activityKey) {
        var comment = $("#comment").val();
        if ($.common.isEmpty(comment)) {
            $.modal.alertWarning("请输入退回意见");
            return;
        }
        $.modal.confirm("确认要退回吗？", function () {
            $.ajax({
                url: prefix + "/returnWF",
                type: "post",
                dataType: "json",
                data: $("#from01").serialize() + (activityKey ? ("&activityKey=" + activityKey) : ""),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $.operate.alertSuccessTabCallback(result);
                }
            });
        })
    }
	
	function onOpenDetailSsj(){
		var ssjId = $("#ssjId").val();
		var taskId = [[${taskId}]];
		var processId = [[${processInstanceId}]];
		var title = "专利实施奖详细信息";
        var id = ssjId+","+taskId+","+processId;
        var url = ctx+"KIZL/PA/AC/queryDT?id="+id;
        $.modal.openTab(title, url, true);
	}
	
	function toDetail(div){
		var i = $(div).attr("value");
		var id = $('input[name="alist['+i+'].extra1"]').val();
		var extra2 = $('input[name="alist['+i+'].extra2"]').val();
		if(extra2=="技术秘密"){
			var title = "技术秘密认定信息";
			var url = ctx + "kymm/technology/detail/"+id;
			$.modal.openTab(title, url, true);
		}else{
			var title = "专利申请详细信息";
	        var url = ctx+"KIZL/PA/AB/queryDT?id="+id;
	        $.modal.openTab(title, url, true);
		}
	}
</script>
</body>
</html>