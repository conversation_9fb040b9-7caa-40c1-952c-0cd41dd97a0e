<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('已受理-不用了')" />
	<th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content">
<div class="form-group" th:include="include :: step(approveKind='KIZL_ApplyBaseinfo',currentNode=${activityCode})"></div><br/>
	<form class="form-horizontal m" id="from01" th:object="${patentData}">

		<th:block th:replace="/KIZL/kizlInclude :: process-values"></th:block>

		<!--框-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
		<div aria-multiselectable="true" class="panel-group" id="logo" role="tablist">
		    <span style="font-weight:bold;border: solid;padding: 10px;border-color: #00a0e9">普通商密</span>
		</div>
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<input name="patentId" id="patentId" th:value="*{patentId}" type="hidden">
									<input name="applyId" id="applyId" th:value="*{applyId}" type="hidden">
									<label class="col-sm-3 control-label">专利申报信息：</label>
									<div class="col-sm-6">
										<div class="form-control-static"><a onClick="onOpenDetail()" style="color: blue">点击查看</a></div>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">受理信息：</label>
									<div class="col-sm-6">
										<div class="form-control-static"><a onClick="onOpenSL()" style="color: blue">点击查看</a></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">清能编号：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="*{bgbh}"></div>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">接收编号：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="*{jsbh}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">专利申报名称：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="*{applyName}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">申请日：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="*{slrq}"></div>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
                            	<div class="form-group">
									<label class="col-sm-3 control-label">申请号：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="*{patentNo}"></div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<th:block th:include="component/wfCommentList4 :: init(businessId=${patentData.applyId})" />
					<p th:if="${processInstanceId}">
						<th:block th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})"></th:block>
					</p>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--框end-->
		<!-- 实质审查列表 -->
		<div th:include="KIZL/PA/paInclude :: initOtherDfsc(patentId=*{patentId})"></div>
		<!-- 专利复审列表 -->
		<!-- <div th:include="KIZL/PA/paInclude :: initOtherFs(patentId=*{patentId})"></div> -->
	</form>
</div>

<!-- moban start -->
<div style="display: none">
	<table id="patentApplyPersonMoban">
		<tbody>
			<tr style="display: block;margin: 2px 0;">
				<td align="right" style="width: 90px; padding: 5px;">
				第displayOrderReplace申请人
				<input type="hidden" id="m-0-xh" name="m-0-xh" value="xhReplace">
				</td>
				<td align="left" colspan="6" style="width: 700px; padding: 5px;">
					<input name="m-0-legalId" id="m-0-legalId" type="hidden">
					<input name="m-0-legalName" id="m-0-legalName" class="form-control" style="display:inline;" type="text" 
					onclick="choiceLegalNo('m-0-legalId','m-0-legalName','m-0-mailAddress','m-0-postOffice','m-0-lxrMoney','m-0-lxrPhone',null)">
					<span class="input-group-addon" style="display:inline;"><i class="fa fa-search"></i></span>
				</td>
			</tr>
			<tr style="display: block;margin: 2px 0;">
				<td align="right" colspan="1" style="width: 90px; padding: 5px;">通信地址</td>
				<td align="left" colspan="5" style="width: 700px; padding: 5px;"><input type="text" id="m-0-mailAddress" name="m-0-mailAddress" class="form-control"></td>
			</tr>
			<tr style="display: block;margin: 2px 0;">
				<td align="right" style="width: 90px; padding: 5px;">邮政编码</td>
				<td align="left" style="width: 300px; padding: 5px;"><input type="text" id="m-0-postOffice" name="m-0-postOffice" class="form-control"></td>
				<td align="right" style="width: 90px; padding: 5px;">付费联系人</td>
				<td align="left" style="width: 300px; padding: 5px;"><input type="text" id="m-0-lxrMoney" name="m-0-lxrMoney" class="form-control"></td>
				<td align="right" style="width: 90px; padding: 5px;">付费联电话</td>
				<td align="left" style="width: 300px; padding: 5px;"><input type="text" id="m-0-lxrPhone" name="m-0-lxrPhone" class="form-control"></td>
			</tr>
		</tbody>
	</table>
</div>
<!-- moban end -->
<div class="toolbar toolbar-bottom" role="toolbar">
	<!-- <button type="button" class="btn btn-danger" onclick="szsc()"><i class="fa fa-reply-all"></i>实质审查</button> -->
	<th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>
	<th:block th:include="include :: wfReturn(taskId=${taskId},callback=wfReturnHandler)"/>
	<button type="button" class="btn btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>返 回</button>
</div>
<br/><br/>
<th:block th:include="KIZL/PA/paInclude :: selectLegal" />
<script th:inline="javascript">
	var prefix = ctx + "KIZL/PA/AB";
	
	//验证
	$("#from01").validate({
		ignore: ":hidden",
		focusCleanup : true
	});
	
	function szsc(){
		var url = ctx+"KIZL/SQ/QT/KIZLSQK01";
        $.modal.openTab("实质审查", url);
	}
	
	/*提交启动流程*/
	function submitHandler(transitionKey) {
    	if ($.validate.form()) {
	      	$.modal.confirm("确认提交吗？", function () {
				var config = {
	                    url: prefix + "/submitWFS",
	                    type: "post",
	                    dataType: "json",
	                    data: $('#from01').serialize() + (transitionKey ? ("&transitionKey=" + transitionKey) : ""),
	                    beforeSend: function () {
	                        $.modal.loading("正在处理中，请稍后...");
	                    },
	                    success: function (result) {
	                    	var str = result.msg;
	                    	if(str.indexOf("请联系系统管理员")!= -1){
	                    		$.modal.alertError(result.msg);
	                    		$.modal.closeLoading();
	                    	}else{
	                    		$.operate.alertSuccessTabCallback(result);
	                    	}
	                    }
	                };
	                $.ajax(config)
			})
		}
	}
	
	/*提交1专利答复子流程2专利复审子流程*/
	function reviewHandler(str) {
    	if ($.validate.form()) {
    		var confirmMsg = "确认提交专利答复审查吗？";
    		var url = prefix + "/startDFSC";
    		if(str==2){
    			confirmMsg = "确认提交专利复审吗？";
    			url = prefix + "/startFS";
    		}
	      	$.modal.confirm(confirmMsg, function () {
				var config = {
						url: url,
	                    type: "post",
	                    dataType: "json",
	                    data: $('#from01').serialize(),
	                    beforeSend: function () {
	                        $.modal.loading("正在处理中，请稍后...");
	                    },
	                    success: function (result) {
	                    	var str = result.msg;
	                    	if(str.indexOf("请联系系统管理员")!= -1){
	                    		$.modal.alertError(result.msg);
	                    		$.modal.closeLoading();
	                    	}else{
	                    		$.operate.alertSuccessTabCallback(result);
	                    	}
	                    }
	                };
	                $.ajax(config)
			})
		}
	}
	
	/*退回流程*/
    function wfReturnHandler(activityKey) {
        var comment = $("#comment").val();
        $.modal.confirm("确认要退回吗？", function () {
            /* if ($.common.isEmpty(comment)) {
                $.modal.alertWarning("请输入退回意见");
                return;
            } */
            $.ajax({
                url: prefix + "/returnWF",
                type: "post",
                dataType: "json",
                data: $("#from01").serialize() + (activityKey ? ("&activityKey=" + activityKey) : ""),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $.operate.alertSuccessTabCallback(result);
                }
            });
        })
    }
	
	function onOpenDetail(){
		var applyId = $("#applyId").val()
		var taskId = [[${taskId}]];
		var processId = [[${processInstanceId}]];
		var title = "专利申请详细信息";
        var id = applyId+","+taskId+","+processId;
        var url = ctx+"KIZL/PA/AB/queryDT?id="+id;
        $.modal.openTab(title, url, true);
	}
	
	function onOpenSL(){
		var applyId = $("#applyId").val()
		var taskId = [[${taskId}]];
		var processId = [[${processInstanceId}]];
		var title = "专利受理详细信息";
        var id = applyId+","+taskId+","+processId;
        var url = ctx+"KIZL/PA/AB/queryQT?id="+id+"&pageType=SL";
        $.modal.openTab(title, url, true);
	}
</script>
</body>
</html>