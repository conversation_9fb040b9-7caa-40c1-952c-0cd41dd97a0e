<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('授权通知信息')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">

<div class="wrapper wrapper-content">
    <!-- <div class="form-group" th:include="include :: step(approveKind='KIZL_ApplyBaseinfo',currentNode='Manual1')"></div><br/> -->
    <form class="form-horizontal m" id="from01" th:object="${patentData}">
        <!--框-->
        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx"
                                               aria-expanded="false" class="collapsed">
                        <th:block th:if="${patentData.jpgFlag == '1'}">
                            <img height="24" th:src=@{/img/apple_small.gif} width="24"/>
                        </th:block>
                        授权通知信息
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">清能编号：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="*{bgbh}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">接收编号：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${data.jsbh}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">专利申报名称：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="*{applyName}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">申请日：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="*{slrq}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">申请号：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="*{patentNo}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">印花税：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="*{moneyYhs}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">发文日：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="*{sqtzFwdate}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">第一次缴费：</label>
                                    <div class="col-sm-9">
                                        第&nbsp;&nbsp;
                                        <div class="form-control-static" style="display:inline-block;"
                                             th:utext="*{moneyFirst}"></div>
                                        &nbsp;&nbsp;年年费
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row ">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">授权通知书：</label>
                                    <div class="col-sm-9">
                                        <th:block
                                                th:replace="/component/attachment :: init(id='field1', name='field1', sourceId=*{applyId}, sourceModule='KIZL', sourceLabel1='KIZL_ApplyBaseinfo', sourceLabel2='xg',see=true)"></th:block>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
            <th:block th:include="component/wfCommentList4 :: init(businessId=${patentData.applyId})"/>
            <p th:if="${processInstanceId}">
                <th:block th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})"></th:block>
            </p>
        </div>
        <!--框end-->
    </form>
</div>
<div class="toolbar toolbar-bottom" role="toolbar">
    <button type="button" class="btn btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>返 回</button>
</div>
<br/><br/>
<th:block th:include="KIZL/PA/paInclude :: selectLegal"/>
<script th:inline="javascript">
    var prefix = ctx + "KIZL/PA/AB";

    //验证
    $("#from01").validate({
        ignore: ":hidden",
        focusCleanup: true
    });
</script>
</body>
</html>