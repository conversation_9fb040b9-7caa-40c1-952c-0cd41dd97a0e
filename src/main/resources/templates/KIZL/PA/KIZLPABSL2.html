<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
	<th:block th:include="include :: header('受理信息管理员审查')"/>
	<th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content">
	<!-- 金苹果、大项目 -->
	<th:block th:if="${patentData.jpgFlag=='1'}">
		<div style="margin-left:1200px;"><img height="24" th:src=@{/img/apple_small.gif} width="24"/></div>
	</th:block>
	<th:block th:if="${patentData.isbigxm=='1'}">
		<div style="margin-left:1200px;"><b>大项目</b></div>
	</th:block>
	<form class="form-horizontal m" id="from01" th:object="${patentData}">

		<th:block th:replace="/KIZL/kizlInclude :: process-values"></th:block>

		<!--框-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div aria-multiselectable="true" class="panel-group" id="logo" role="tablist">
				<span style="font-weight:bold;border: solid;padding: 10px;border-color: #00a0e9">普通商密</span>
			</div>
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#jbxx"
											   aria-expanded="false" class="collapsed">基本信息
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<input name="patentId" id="patentId" th:value="*{patentId}" type="hidden">
									<input name="applyId" id="applyId" th:value="*{applyId}" type="hidden">
									<input name="gldwCode" id="gldwCode" th:value="*{gldwCode}" type="hidden">
									<label class="col-sm-3 control-label">流水号：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="${data.serialNum}"></div>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">填表日期：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="${data.applyDate}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">第一申报部门：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="${data.firstDeptName}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">标签：</label>
									<div class="col-sm-8" th:include="/component/checkbox :: init(id='label',name='label',dictCode='techLabel',
									businessType='KYMM',see=true,value=${data.label})"></div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">技术领域：</label>
									<div class="col-sm-9">
										<input id="techArea" th:value="${data.techArea}" type="hidden">
										<div class="form-control-static" th:utext="${data.techAreaName}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row" id="source13" style="display: none">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">用途：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="${data.useProposeName}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">技术知识分类：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="${data.knowledgeClassName}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">清能编号：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="${bgbh}"></div>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">接收编号：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="*{jsbh}"></div>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">发明名称：</label>
									<div class="col-sm-9">
										<div class="form-control-static" th:utext="${data.applyName}"></div>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">权属：</label>
									<div class="col-sm-9">
										<input name="ownership" id="ownership" class="form-control"
											   th:value="${data.ownership}"
											   oninput="value=value.match(/[0-9]{1,10}\.?[0-9]{0,6}/)"/>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">专利名称：</label>
									<div class="col-sm-9">
										<input name="applyName" id="applyName" class="form-control"
											   th:value="*{applyName}" required/>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
								<div class="form-group">
									<label id="mylabel2" class="col-sm-3 control-label is-required">申请日：</label>
									<div class="col-sm-4" th:include="/component/date::init(id='slrq',name='slrq',
								isrequired=true,strValue=*{slrq})"></div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label id="mylabel" class="col-sm-3 control-label is-required" style="">申请号：</label>
									<div class="col-sm-9">
										<input name="patentNo" id="patentNo" class="form-control" th:value="*{patentNo}"
											   required/>
									</div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">专利类型：</label>
									<div class="col-sm-9">
										<input name="patentType" id="patentType" th:value="*{patentType}" type="hidden"
											   required>
										<input name="patentTypeName" id="patentTypeName" class="form-control"
											   readonly="true" required/>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">优先权日：</label>
									<div class="col-sm-4"
										 th:include="/component/date::init(id='yxqr',name='yxqr',strValue=*{yxqr})"></div>
								</div>
							</div>
						</div>
						<div class="row ">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">权利要求数量：</label>
									<div class="col-sm-9">
										<input name="qlyqsl" id="qlyqsl" class="form-control" th:value="*{qlyqsl}"
											   oninput="value=value.match(/[0-9]{1,10}\.?[0-9]{0,6}/)"/>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">说明书页数：</label>
									<div class="col-sm-9">
										<input name="smsys" id="smsys" class="form-control" th:value="*{smsys}"
											   oninput="value=value.match(/[0-9]{1,10}\.?[0-9]{0,6}/)"/>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--框end-->
		<!--框-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#swsxx"
											   aria-expanded="false" class="collapsed">
						事务所信息
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="swsxx" class="panel-collapse collapse in">
					<div class="panel-body">
						<div class="row ">
							<div class="col-sm-6">
								<div class="form-group">
									<div th:include="KIZL/PA/paInclude :: initSwsinfo(id='swsGuid', name='swsGuid',isrequired=true,divClass='col-sm-6',
									isPct='0',isfirst='true',labelName='代理事务所：',value=*{swsGuid},gldwCode=${swsGldw})"></div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">代理费（元）：</label>
									<div class="col-sm-9">
										<input name="moneyDlf" id="moneyDlf" class="form-control" th:value="*{moneyDlf}"
											   oninput="value=value.match(/[0-9]{1,10}\.?[0-9]{0,6}/)"/>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">权附：</label>
									<div class="col-sm-9">
										<input name="moneyQf" id="moneyQf" class="form-control" th:value="*{moneyQf}"
											   oninput="value=value.match(/[0-9]{1,10}\.?[0-9]{0,6}/)"/>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">申请费（元）：</label>
									<div class="col-sm-9">
										<input name="moneySqf" id="moneySqf" class="form-control" th:value="*{moneySqf}" required/>
									</div>
								</div>
							</div>
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label">说明书附加费（元）：</label>
									<div class="col-sm-9">
										<input name="moneySmsfjf" id="moneySmsfjf" class="form-control"
											   th:value="*{moneySmsfjf}"
											   oninput="value=value.match(/[0-9]{1,10}\.?[0-9]{0,6}/)"/>
									</div>
								</div>
							</div>
						</div>
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label class="col-sm-3 control-label is-required">公布印刷费（元）：</label>
									<div class="col-sm-9">
										<input name="moneyGbysf" id="moneyGbysf" class="form-control" th:value="*{moneyGbysf}" required/>
									</div>
								</div>
							</div>
						</div>
						<div class="row" style="margin-left:-110px">
							<label class="col-sm-2 control-label">申请相关文件：</label>
							<div class="col-sm-10">
								<div th:include="/component/attachment :: init(id='xgwjId',name='xgwjId',
                                sourceId=${data.applyId},sourceModule='KIZL_XGWJ')"></div>
							</div>
						</div>
						<div class="row" style="margin-left:-110px">
							<label class="col-sm-2 control-label">共同申请协议：</label>
							<div class="col-sm-10">
								<div th:include="/component/attachment :: init(id='zscqId',name='zscqId',
                                sourceId=${data.applyId},sourceModule='KIZL_ZSCQ')"></div>
							</div>
						</div>
					</div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--框end-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title">
						<a data-toggle="collapse" href="#scfx" class="collapsed">
							申请人
							<span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
						</a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="scfx" class="panel-collapse collapse in" aria-expanded="false">
					<div class="panel-body">
						<div class="form-group">
							<div class="col-sm-12">
								<table class="table table-bordered table-hover table-striped">
									<tr>
										<td colspan="4" align="left" style="font-size: 16px; line-height: 40px;">
											选择申请单位数
											<select id="patentApplyPersonNum"
													onchange="insertPatentApplyPersonTr('patentApplyPersonMoban','patentApplyPersonTr','m-0-','alist')">
												<option th:each="station : ${stationList}" th:text="${station.name}"
														th:value="${station.id}"></option>
											</select>
										</td>
									</tr>
									<tbody id="patentApplyPersonTr">
									<th:block th:each="sqr : ${data.alist}">
										<tr style="display: block;margin: 2px 0;">
											<td align="right" style="width: 90px; padding: 5px;">
												第
												<th:block th:text="${sqrStat.index+1}"></th:block>
												申请人
												<input type="hidden" th:id="'alist['+${sqrStat.index}+'].xh'"
													   th:name="'alist['+${sqrStat.index}+'].xh'"
													   th:value="${sqrStat.index+1}">
											</td>
											<td align="left" colspan="6" style="width: 700px; padding: 5px;">
												<input th:id="'alist['+${sqrStat.index}+'].legalId'"
													   th:name="'alist['+${sqrStat.index}+'].legalId'" type="hidden"
													   th:value="${sqr.legalId}">
												<input th:id="'alist['+${sqrStat.index}+'].legalName'"
													   th:name="'alist['+${sqrStat.index}+'].legalName'"
													   class="form-control" style="display:inline;width: 466px;"
													   type="text" th:value="${sqr.legalName}" required="true" readonly>
												<span class="btn btn-sm btn-primary" style="display:inline;"
													  th:onclick="choiceLegalNo('alist['+[[${sqrStat.index}]]+'].legalId','alist['+[[${sqrStat.index}]]+'].legalName','alist['+[[${sqrStat.index}]]+'].mailAddress','alist['+[[${sqrStat.index}]]+'].postOffice','alist['+[[${sqrStat.index}]]+'].lxrMoney','alist['+[[${sqrStat.index}]]+'].lxrPhone',null)">
													选择内部单位</span>
												<span class="btn btn-sm btn-primary" style="display:inline;"
													  th:onclick="doClearLegal('alist['+[[${sqrStat.index}]]+'].legalId','alist['+[[${sqrStat.index}]]+'].legalName')">
													填写外部单位</span>
											</td>
										</tr>
										<tr style="display: block;margin: 2px 0;">
											<td align="right" colspan="1" style="width: 90px; padding: 5px;">通信地址
											</td>
											<td align="left" colspan="5" style="width: 700px; padding: 5px;"><input
													type="text" th:id="'alist['+${sqrStat.index}+'].mailAddress'"
													th:name="'alist['+${sqrStat.index}+'].mailAddress'"
													class="form-control" th:value="${sqr.mailAddress}"></td>
										</tr>
										<tr style="display: block;margin: 2px 0;">
											<td align="right" style="width: 90px; padding: 5px;">邮政编码</td>
											<td align="left" style="width: 300px; padding: 5px;"><input type="text"
																										th:id="'alist['+${sqrStat.index}+'].postOffice'"
																										th:name="'alist['+${sqrStat.index}+'].postOffice'"
																										class="form-control"
																										th:value="${sqr.postOffice}">
											</td>
											<td align="right" style="width: 90px; padding: 5px;">付费联系人</td>
											<td align="left" style="width: 300px; padding: 5px;"><input type="text"
																										th:id="'alist['+${sqrStat.index}+'].lxrMoney'"
																										th:name="'alist['+${sqrStat.index}+'].lxrMoney'"
																										class="form-control"
																										th:value="${sqr.lxrMoney}">
											</td>
											<td align="right" style="width: 90px; padding: 5px;">付费联电话</td>
											<td align="left" style="width: 300px; padding: 5px;"><input type="text"
																										th:id="'alist['+${sqrStat.index}+'].lxrPhone'"
																										th:name="'alist['+${sqrStat.index}+'].lxrPhone'"
																										class="form-control"
																										th:value="${sqr.lxrPhone}">
											</td>
										</tr>
									</th:block>
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--框-->
		<div class="panel-group" role="tablist" aria-multiselectable="true">
			<div class="panel panel-default">
				<div class="panel-heading">
					<h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#fmrxx"
											   aria-expanded="false" class="collapsed">
						发明设计人信息
						&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
						<span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
					</h4>
				</div>
				<!--折叠区域-->
				<div id="fmrxx" class="panel-collapse collapse in">
					<div class="panel-body">
						<div class="row">
							<div class="col-sm-6">
								<div class="form-group">
									<label id="mylabel3" class="col-sm-3 control-label is-required">发明设计人：</label>
									<div class="col-sm-9">
										<input name="fml" id="fml" class="form-control" th:value="*{fml}" required/>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
				<!--折叠区域end-->
			</div>
		</div>
		<!--框end-->
		<!-- 展示专利申请贡献系数确认列表   -->
		<div th:include="KIZL/PA/paInclude :: initGxxsqrHistory(applyId=*{applyId})"></div>
		<th:block th:include="component/wfCommentList4 :: init(businessId=${patentData.applyId})"/>
		<p th:if="${processInstanceId}">
			<th:block th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})"></th:block>
		</p>
	</form>
</div>
<!-- moban start -->
<div style="display: none">
	<table id="patentApplyPersonMoban">
		<tbody>
		<tr style="display: block;margin: 2px 0;">
			<td align="right" style="width: 90px; padding: 5px;">
				第displayOrderReplace申请人
				<input type="hidden" id="m-0-xh" name="m-0-xh" value="xhReplace">
			</td>
			<td align="left" colspan="6" style="width: 700px; padding: 5px;">
				<input name="m-0-legalId" id="m-0-legalId" type="hidden">
				<input name="m-0-legalName" id="m-0-legalName" class="form-control" style="display:inline;width: 466px;"
					   type="text" required="true" readonly>
				<span class="btn btn-sm btn-primary" style="display:inline;"
					  onclick="choiceLegalNo('m-0-legalId','m-0-legalName','m-0-mailAddress','m-0-postOffice','m-0-lxrMoney','m-0-lxrPhone',null)">
					选择内部单位</span>
				<span class="btn btn-sm btn-primary" style="display:inline;"
					  onclick="doClearLegal('m-0-legalId','m-0-legalName')">
					填写外部单位</span>
			</td>
		</tr>
		<tr style="display: block;margin: 2px 0;">
			<td align="right" colspan="1" style="width: 90px; padding: 5px;">通信地址</td>
			<td align="left" colspan="5" style="width: 700px; padding: 5px;"><input type="text" id="m-0-mailAddress"
																					name="m-0-mailAddress"
																					class="form-control"></td>
		</tr>
		<tr style="display: block;margin: 2px 0;">
			<td align="right" style="width: 90px; padding: 5px;">邮政编码</td>
			<td align="left" style="width: 300px; padding: 5px;"><input type="text" id="m-0-postOffice"
																		name="m-0-postOffice" class="form-control"></td>
			<td align="right" style="width: 90px; padding: 5px;">付费联系人</td>
			<td align="left" style="width: 300px; padding: 5px;"><input type="text" id="m-0-lxrMoney"
																		name="m-0-lxrMoney" class="form-control"></td>
			<td align="right" style="width: 90px; padding: 5px;">付费联电话</td>
			<td align="left" style="width: 300px; padding: 5px;"><input type="text" id="m-0-lxrPhone"
																		name="m-0-lxrPhone" class="form-control"></td>
		</tr>
		</tbody>
	</table>
</div>
<!-- moban end -->
<script type="text/javascript" th:src="@{/product/js/kizl.js}"></script>
<div class="toolbar toolbar-bottom" role="toolbar">
	<button type="button" class="btn btn-primary" onclick="saveHandler()"><i class="fa fa-save"></i>保存</button>
	<button type="button" class="btn btn-danger" onclick="closeItem()"><i class="fa fa-reply-all"></i>返 回</button>
</div>
<br/><br/>
<script th:inline="javascript">

	$("#mylabel").css("color", "red");
	$("#mylabel2").css("color", "red");
	$("#mylabel3").css("color", "red");


	var formId = "from01"
	var prefix = ctx + "KIZL/PA/AB";
	//申请人数量加载
	var x = [[${data.alist}]].length;
	console.log("权属" + x);
	$('#ownership').val(x);//自动计算权属
	var xOption = document.getElementById("patentApplyPersonNum");
	if (xOption != null) {
		for (var i = 0; i < xOption.length; i++) {
			if (xOption[i].value == x) {
				xOption[i].selected = true;
			}
		}
	}

	//内部 外部单位文本框处理
	function doClearLegal(code, name) {
		$('input[id="' + code + '"]').val("");
		$('input[id="' + name + '"]').attr("readonly", false);
	}

	//验证
	$("#from01").validate({
		ignore: ":hidden",
		focusCleanup: true
	});

	//新增 专利申请人
	function insertPatentApplyPersonTr(sourceId, targetId, sourceReplace, targetBlockId) {
		var mobanHtml = jQuery("#" + sourceId + " tbody").html();//取得模板html
		var patentApplyPersonNum = $("#patentApplyPersonNum").val();//选择
		var personLenTr = $("#patentApplyPersonTr").find("tr").length;//实际
		if (patentApplyPersonNum == 0) {
			$("#" + targetId).html("");
		} else {
			personLen = personLenTr / 3;
			if (patentApplyPersonNum < personLen) {
				var trList = $("#patentApplyPersonTr").find("tr:gt(" + eval(patentApplyPersonNum * 3 - 1) + ")");
				$.each(trList, function (index, item) {
					$(item).remove();
				})
			} else {
				for (var i = personLen; i < patentApplyPersonNum; i++) {
					var curMobanHtml = mobanHtml.replace(new RegExp(sourceReplace, "gm"), targetBlockId + "[" + i + "].");
					curMobanHtml = curMobanHtml.replace(new RegExp("displayOrderReplace", "gm"), i + 1);
					$("#" + targetId).append(curMobanHtml);
				}
			}
		}

		$('#ownership').val(patentApplyPersonNum);//自动计算权属
		//添加change事件
	}

	if ([[${patentData.patentNo}]] != null && [[${patentData.patentNo}]] != '') {
		setPatentType();
	}
	document.getElementById("patentNo").onchange = _setPatentType;

	function _setPatentType() {
		if (checkSqh()) {
			setPatentType();
		} else {
			$.modal.alertError("申请号错误!");
			$("#patentNo").val("");
		}
		//校验申请号是否重复
		var patentId = [[${patentData.patentId}]];
		var patentNo = $("#patentNo").val();
		if (patentId != null && patentId != '' && patentNo != null && patentNo != '') {
			$.ajax({
				url: prefix + "/checkPatentNo/" + patentId + "/" + patentNo,
				type: "GET",
				dataType: "json",
				success: function (res) {
					var isRepeat = res.isRepeat;
					if (isRepeat == false) {
						$.modal.alertError("该申请号已存在!");
						$("#patentNo").val("");
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {// 处理错误情况
				}
			});
		} else {
			$("#patentNo").val("");
		}
	}

	function checkSqh() {
		//校验规则：
		//申请号的组成：12位＋“.”＋1位。其中最后一位为校验位。
		//和=第1位*2+第2位*3+第3位*4+第4位*5+第5位*6+第6位*7+第7位*8+第8位*9+第9位*2+第10位*3+第11位*4+第12位*5
		//和除以11的余数就是校验位  （10=X）
		var t = $("#patentNo").val();
		var i = 0;
		var j = 0;
		var s = "";
		var u = "";
		var l = 0;

		s = t.substring(0, 1);
		i = parseInt(s) * 2;
		s = t.substring(1, 2);
		i = parseInt(s) * 3 + parseInt(i);

		s = t.substring(2, 3);
		i = parseInt(s) * 4 + parseInt(i);
		s = t.substring(3, 4);
		i = parseInt(s) * 5 + parseInt(i);

		s = t.substring(4, 5);
		i = parseInt(s) * 6 + parseInt(i);
		s = t.substring(5, 6);
		i = parseInt(s) * 7 + parseInt(i);
		s = t.substring(6, 7);
		i = parseInt(s) * 8 + parseInt(i);
		s = t.substring(7, 8);
		i = parseInt(s) * 9 + parseInt(i);

		s = t.substring(8, 9);
		i = parseInt(s) * 2 + parseInt(i);
		s = t.substring(9, 10);
		i = parseInt(s) * 3 + parseInt(i);
		s = t.substring(10, 11);
		i = parseInt(s) * 4 + parseInt(i);
		s = t.substring(11, 12);
		i = parseInt(s) * 5 + parseInt(i);

		j = parseInt(parseInt(i) / 11);
		l = i - j * 11;

		u = t.substring(13, 14);

		if (l == 10) {
			if (u.replace(/(^\s*)|(\s*$)/g, '') != "x" && u.replace(/(^\s*)|(\s*$)/g, '') != "X") {
				console.log("X");
				return false;
			}
		} else {
			s = (l + "").replace(/(^\s*)|(\s*$)/g, '');
			if (s.replace(/(^\s*)|(\s*$)/g, '') != u.replace(/(^\s*)|(\s*$)/g, '')) {
				console.log(s);
				return false;
			}
		}
		return true;
	}

	function setPatentType() {//根据申请号的第五位（1 为发明 2为实用新型 3为外观设计 8 PCT发明 9 PCT实用新型）
		var patentNo = $("#patentNo").val();
		if (patentNo.length >= 5) {
			var index5 = patentNo.substring(4, 5);
			var patentType = "";
			if (index5 == '1') {
				patentType = "FM";
				$("#patentType").val("FM");
				$("#patentTypeName").val("发明");
			} else if (index5 == '2') {
				patentType = "SYXX";
				$("#patentType").val("SYXX");
				$("#patentTypeName").val("实用新型");
			} else if (index5 == '3') {
				patentType = "WGSJ";
				$("#patentType").val("WGSJ");
				$("#patentTypeName").val("外观设计");
			} else if (index5 == '8') {
				patentType = "PCTF";
				$("#patentType").val("PCTF");
				$("#patentTypeName").val("PCT发明");
			} else if (index5 == '9') {
				patentType = "PCTS";
				$("#patentType").val("PCTS");
				$("#patentTypeName").val("PCT实用新型");
			} else {
				$("#patentType").val("");
				$("#patentTypeName").val("");
				$.modal.alertError("申请号错误!");
				return;
				//$("#patentNo").focus();
			}

			//ajax去费用报查询申请费和公布印刷费
			if (patentType != "") {
				$.ajax({
					url: prefix + "/getSqf/" + patentType,
					type: "GET",
					dataType: "json",
					success: function (result) {
						if (result.sqf == undefined || result.ysf == undefined) {
							$.modal.alertError("申请费或公布印刷费配置错误!");
							return;
						}
						$("#moneySqf").val(result.sqf);
						$("#moneyGbysf").val(result.ysf);
					},
					error: function (jqXHR, textStatus, errorThrown) {
					}
				});
			} else {
				$("#dlrNo").html("");
			}

		} else {
			$("#patentType").val("");
			$("#patentTypeName").val("");
			$.modal.alertError("申请号错误!");

		}
	}

	//暂存
	function saveHandler() {
		var config = {
			url: prefix + "/addPatentPlus",
			type: "post",
			dataType: "json",
			data: $('#from01').serialize(),
			beforeSend: function () {
				$.modal.loading("正在处理中，请稍后...");
			},
			success: function (result) {
				$.modal.alertSuccess(result.msg);
				$.modal.closeLoading();
			}
		};
		$.ajax(config)
	}


	/*提交启动流程*/
	function submitHandler(transitionKey) {
		if ($.validate.form()) {
			//校验申请人-宝山钢铁股份有限公司其他信息可以不填，且申请人里面必须有宝山钢铁股份有限公司
			var len1 = $("#patentApplyPersonTr").find("tr").length;
			if (len1 > 0) {
			} else {
				$.modal.alertError("申请人不能为空！请选择申请人");
				return;
			}
			$.modal.confirm("确认提交吗？", function () {
				var config = {
					url: ctx + "KIZL/sjhy/submitWF",
					type: "post",
					dataType: "json",
					data: $('#from01').serialize() + (transitionKey ? ("&transitionKey=" + transitionKey) : ""),
					beforeSend: function () {
						$.modal.loading("正在处理中，请稍后...");
					},
					success: function (result) {
						var str = result.msg;
						if (str.indexOf("提交异常,") != -1) {
							$.modal.alertError(result.msg);
							$.modal.closeLoading();
						} else if (str.indexOf("不能为空") != -1) {
							$.modal.alertError(result.msg);
							$.modal.closeLoading();
						} else if (str.indexOf("请联系系统管理员") != -1) {
							$.modal.alertError(result.msg);
							$.modal.closeLoading();
						} else {
							$.operate.alertSuccessTabCallback(result);
						}
					}
				};
				$.ajax(config)
			})
		}
	}

	function onOpenDetail() {
		var applyId = $("#applyId").val()
		var taskId = [[${taskId}]];
		var processId = [[${processInstanceId}]];
		var title = "专利申请详细信息";
		var id = applyId + "," + taskId + "," + processId;
		var url = ctx + "KIZL/PA/AB/queryDT?id=" + id;
		$.modal.openTab(title, url, true);
	}

	function onOpenJsDetail() {
		var id = [[${data.searchNo}]];
		var title = "检索信息";
		var url = ctx + "KIZL/PA/AA/queryBySearchNo?id=" + id;
		$.modal.openTab(title, url, true);
	}

	//获取同来源信息
	getSource();

	function getSource() {
		var fromNo = $("#fromNo").val();
		var applyId = $("#applyId").val();
		var fromType = $("#fromType").val();
		$("#tllzl").empty();
		$("#tllmm").empty();
		if (fromNo != '') {
			$.ajax({
				url: ctx + "KIZL/PA/AB/getTllPlus/" + fromNo + "/" + applyId + "/" + fromType,
				type: "GET",
				dataType: "json",
				success: function (res) {
					var zllist = res.zllist;
					var mmlist = res.mmlist;
					if (zllist != undefined) {
						for (var i = 0; i < zllist.length; i++) {
							var tr = "<tr id='c" + i + "'>";
							tr = tr + "<td style='text-align:center'>" + zllist[i].jsbh + "</td>";
							tr = tr + "<td style='text-align:center'>" + zllist[i].flzt + "</td>";
							tr = tr + "<td style='text-align:center'>" + zllist[i].applyName + "</td>";
							tr = tr + '<td style="text-align:center"><a onClick="tllzlDetail(' + "'" + zllist[i].applyId + "'" + ')">' + "<font color='blue'>查看</font></a></td>";
							tr = tr + "</tr>";
							$("#tllzl").append(tr);
						}
					}
					if (mmlist != undefined) {
						for (var i = 0; i < mmlist.length; i++) {
							var tr = "<tr id='c" + i + "'>";
							tr = tr + "<td style='text-align:center'>" + mmlist[i].confirmNum + "</td>";
							tr = tr + "<td style='text-align:center'>" + mmlist[i].status + "</td>";
							tr = tr + "<td style='text-align:center'>" + mmlist[i].technologyName + "</td>";
							tr = tr + '<td style="text-align:center"><a onClick="tllmmDetail(' + "'" + mmlist[i].technologyId + "'" + ')">' + "<font color='blue'>查看</font></a></td>";
							tr = tr + "</tr>";
							$("#tllmm").append(tr);
						}
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {// 处理错误情况
				}
			});
		}
	}

	//同来源专利、同来源技术秘密
	function tllzlDetail(id) {
		var title = "专利申请详细信息";
		var url = ctx + "KIZL/PA/AB/queryDT?id=" + id;
		$.modal.openTab(title, url, true);
	}

	function tllmmDetail(id) {
		var title = "技术秘密详细信息";
		var url = ctx + "kymm/technology/detail/" + id;
		$.modal.openTab(title, url, true);
	}

	//获取技术领域是否显示用途
	getTechAreaUseFlag();

	function getTechAreaUseFlag() {
		var techArea = $("#techArea").val();
		if (techArea != '') {
			$.ajax({
				url: ctx + "KIZL/PA/AB/getTechAreaUseFlag/" + techArea,
				type: "GET",
				dataType: "json",
				success: function (res) {
					var useFlag = res.useFlag;
					if (useFlag == 1) {
						$("#source13").show();
						$("#source14").show();
					}
				},
				error: function (jqXHR, textStatus, errorThrown) {// 处理错误情况
				}
			});
		}
	}

	//科研一览表
	function queryKYDT() {
		var projectCode = [[${data.fromNo}]];
		var url = ctxKY + "web/KYXMLXYL?serviceName=KYXMLX02&methodName=query&pageNo=KYXMLX21&projectCode=" + projectCode;
		var title = "项目一栏表";
		// 选卡页方式打开并刷新当前页 isRefresh 是否刷新
		$.modal.openTab(title, url, true);
	}

	//牌号无的时候显示未实施的原因
	var ishaveph = [[${data.ishaveph}]];
	if (ishaveph == '0') {
		$("#source01").show();
		$("#sourcePh").hide();
	} else if (ishaveph == '1') {
		$("#sourcePh").show();
		$("#source01").hide();
	}

</script>
<th:block th:include="KIZL/PA/paInclude :: selectLegal"/>
</body>
</html>