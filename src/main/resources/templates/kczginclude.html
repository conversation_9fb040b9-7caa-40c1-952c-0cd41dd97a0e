
<div th:fragment="toolbox-css">
  <link rel="stylesheet" th:href="@{/kczg/css/toolbox.css}"/>
</div>

<div th:fragment="toolbox-js">
  <script th:src="@{/kczg/js/toolbox.js}"></script>
</div>

<div th:fragment="toolbox">
  <div id="catalogue" class="toolbox toolbox-right toolbox-open">
    <div class="toolbox-content">
      <div class="toolbox-heading">
      <span class="toolbox-icon">
        <i class="fa fa-gear text-primary"></i>
      </span>
        <span class="toolbox-title"> 目录</span>
      </div>
      <div class="toolbox-body" id="catalogueItem">
        <ul class="toolbox-list nav" data-nav-animate="zoomIn" data-smoothscroll="-125" data-spy="scroll"
            data-offset-top="240">

        </ul>
      </div>
    </div>
  </div>
</div>

<!--
kczg 模块下 引入 css
-->
<div th:fragment="kczgCss(cssName)">
  <th:block th:with="url=@{/kczg/css/}">
    <link rel="stylesheet" th:href="${url+cssName + '.css'}"/>
  </th:block>
</div>

<!--
kczg 模块下 引入 js
-->
<div th:fragment="kczgJS(jsName)">
  <th:block th:with="url=@{/kczg/js/}">
    <script th:src="${url+jsName + '.js'}"></script>
  </th:block>
</div>


