<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('推广附件维护列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>

                            <li>
                                <label>附件名称：</label>
                                <input type="text" name="fileName"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
               <!-- <a class="btn btn-success" onclick="updateDate()" >
                    <i class="fa fa-plus"></i> 验证（勿点）
                </a>-->
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzfj/fileTemplate";

        var type = [[${@dict.getDictList('KTTG','fileType')}]];
        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "附件维护",
                columns: [
                {
                    field: 'guid',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'businessType',
                    title: '模板类型',
                    formatter: function(value, row, index) {
                        if(value=='FJ_QT'){
                            return "其他附件";
                        }else{
                            return $.table.selectDictLabel(type,value);
                        }
                    }
                },
                {
                    field: 'fileName',
                    title: '附件名称',
                    formatter: function(value, row, index) {
                        var html = $.common.sprintf('<a class="form_list_a" href="javascript:void(0)" onclick="downLoad(\'' + row.fileId + '\')">%s</a>&nbsp;<span>(%s)</span>',value,row.fileSize);
                        return html;
                    }
                },
                {
                    field: 'createDate',
                    title: '上传时间'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.guid + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.guid + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function downLoad(key) {
            window.open(ctx + "attachment/download/" + key);
        }

        function updateDate() {
            $.operate.saveTabAlert(ctx+"zzlx/main/updateDate");
        }
    </script>
</body>
</html>