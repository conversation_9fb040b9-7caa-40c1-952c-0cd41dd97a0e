<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改推广附件维护')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-fileTemplate-edit" th:object="${fileTemplate}">
            <input name="guid" th:field="*{guid}" type="hidden">
            <input name="fileName" id="fileName" type="hidden">
            <input name="fileSize" id="fileSize" type="hidden">



            <div class="form-group">
                <label class="col-sm-3 control-label is-required">选择模板类型：</label>
                <div class="col-sm-8">
                    <div th:include="/component/radio :: init(id='businessType',name='businessType',value=*{businessType},
                                        businessType='KTTG',dictCode='fileType',isrequired=true)"></div>

                </div>
            </div>


            <div class="form-group">
                <label class="col-sm-3 control-label is-required">模板上传：</label>
                <div class="col-sm-8">
                    <div th:include="/component/attachment :: init(display='none',sourceId=*{guid},sourceModule='DICT_FILE',id='fileId',name='fileId',isreqired=true)">
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>

    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "zzfj/fileTemplate";

        $("#form-fileTemplate-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            var trs = $('tr[id^="upload-"]');
            console.log(trs);
            var td1 = trs.find("td").eq(0).text();
            var td2 = trs.find("td").eq(1).text();
            $("#fileName").val(td1);
            $("#fileSize").val(td2);
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-fileTemplate-edit').serialize());
            }
        }

    </script>
</body>
</html>