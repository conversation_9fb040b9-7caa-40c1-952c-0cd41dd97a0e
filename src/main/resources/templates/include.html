<!--  弹框通用CSS -->
<head th:fragment=header(title)>
    <meta charset="utf-8">
    <meta content="width=device-width, initial-scale=1.0" name="viewport">
    <meta content="IE=edge" http-equiv="X-UA-Compatible">
    <meta content="" name="keywords">
    <meta content="" name="description">
    <title th:text="${title}"></title>
    <link href="favicon.ico" rel="shortcut icon">
    <link rel="stylesheet" th:href="@{/css/bootstrap.min.css}"/>
    <link rel="stylesheet" th:href="@{/css/font-awesome.min.css}"/>
    <!-- bootstrap-table 表格插件样式 -->
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-table/bootstrap-table.min.css?v=20210602}"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-table/bootstrap-table-pagejump.css}"/>
    <link rel="stylesheet" th:href="@{/css/animate.min.css}"/>
    <link rel="stylesheet" th:href="@{/css/style.min.css?v=20200903}"/>

    <link rel="stylesheet" th:href="@{/ruoyi/css/ry-ui.css?v=4.6.1}"/>
    <!--时间-->
    <th:block th:include="include :: datetimepicker-css"/>

    <th:block th:include="include :: lay-ui-css"/>
    <th:block th:include="include :: select2-css"/>


    <link rel="stylesheet" th:href="@{/css/mystyle.css}"/>
    <link rel="stylesheet" th:href="@{/css/custom.css}"/>
    <!--引导图-->
    <link rel="stylesheet" th:href="@{/css/liulist.css}"/>
	<!--模块待办-->
    <link th:href="@{/css/wfdb.css}" rel="stylesheet"/>
</head>
<!-- 弹框通用js  -->
<div th:fragment="baseJs">
    <script th:inline="javascript">
        var ctx = [[@{/}]];
        var ctxGGMK = [[${@environment.getProperty("app-context.ctxGGMK")}]];
        var ctxYWZT = [[${@environment.getProperty("app-context.ctxYWZT")}]];
        var ctxKY = [[${@environment.getProperty("app-context.ctxKY")}]];
        var ctxZZZC = [[${@environment.getProperty("app-context.ctxZZZC")}]];
    </script>

    <a class="btn btn-sm display" href="#" id="scroll-up"><i class="fa fa-angle-double-up"></i></a>
    <script th:src="@{/js/jquery.min.js}"></script>
    <script th:src="@{/js/bootstrap.min.js}"></script>
    <!-- bootstrap-table 表格插件 -->
    <script th:src="@{/ajax/libs/bootstrap-table/bootstrap-table.min.js?v=20210602}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/bootstrap-table-pagejump.js}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/locale/bootstrap-table-zh-CN.min.js?v=20210602}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/mobile/bootstrap-table-mobile.js?v=20210602}"></script>
    <!-- jquery-validate 表单验证插件 -->
    <script th:src="@{/ajax/libs/validate/jquery.validate.min.js}"></script>
    <script th:src="@{/ajax/libs/validate/messages_zh.min.js}"></script>
    <script th:src="@{/ajax/libs/validate/jquery.validate.extend.js}"></script>
    <!-- jquery-validate 表单树插件 -->
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/tree/bootstrap-table-tree.min.js?v=20210602}"></script>
    <!-- 遮罩层 -->
    <script th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
    <script th:src="@{/ajax/libs/iCheck/icheck.min.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js?v=20210516}"></script>
    <script th:src="@{/ajax/libs/layui/layui.js?v=20210516}"></script>
    <script th:src="@{/ruoyi/js/common.js?v=4.6.1}"></script>
    <script th:src="@{/ruoyi/js/ry-ui.js?v=4.6.1}"></script>

    <!--附件-->
    <th:block th:include="include :: lay-upload-js"/>
    <!--时间-->
    <th:block th:include="include :: datetimepicker-js"/>

    <th:block th:include="include :: select2-js"/>
    <!--选择组织-->
    <th:block th:include="include :: selectOrg"/>
    <!--选择人-->
    <th:block th:include="include :: selectUser"/>
    <!--富文本-->
    <th:block th:include="/component/richText :: js" />
    <!--折叠图标-->
    <script th:src="@{/js/cc.js}"></script>

    <script th:inline="javascript">


        var sbxhsjTime
        $(document).on('mouseenter', '.input-group .detailOrgOrUser',function(){
            var input =$(this).val();
            sbxhsjTime=setTimeout(function(){
                if (input!=null && input.length > 40) {
                    layer.alert(input, {
                        title : "信息内容",
                        shadeClose : true,
                        btn : [ '确认' ],
                        btnclass : [ 'btn btn-primary' ],
                    });
                }
            },2000)
        });
        $(document).on('mouseleave', '.input-group .detailOrgOrUser',function(){
            if(sbxhsjTime){
                clearInterval(sbxhsjTime);
            }
        });


        $(document).ready(function(){
        	//右边浮动导航
        	var aDiv = $('.wrapper-content .panel-group:visible');//楼层
        	if(aDiv&&aDiv.length>=3){//大于等于3块显示
        		initNavigator(aDiv);
        		titleSq();
        	}
       	});
        
        function initNavigator(aDiv) {
            //动态新增浮动导航
            $("#LoutiNav").remove();
            var rightNavHtml = "<ul id=\"LoutiNav\">";
            rightNavHtml+='<div id="sqDiv" class="pack-up-button"><a onclick="titleSq()"><i class="fa fa-chevron-circle-right">收起</i> </a></div>';
            aDiv.each(function(index,element) {
                if(index<10){//最多显示10个
                    var title = $(element).find(".panel-title").find("a").text();
                    title = title.replace(/[^\u4e00-\u9fa5a-zA-Z]/gi, "");//获取汉字英文
                    if(title&&title.length>5){
                        title = title.substring(0,5);//截取前5个汉字
                    }
                    if(index==0){
                        rightNavHtml += "<li class=\"active\">";
                    }else{
                        rightNavHtml += "<li>";
                    }
                    rightNavHtml += "<i>";
                    rightNavHtml += "<div class=\"fa fa-caret-right\" aria-hidden=\"true\"></div>";
                    rightNavHtml += title;
                    rightNavHtml += "</i>";
                    rightNavHtml += "<span>";
                    rightNavHtml += "<div class=\"fa fa-caret-right\" aria-hidden=\"true\"></div>";
                    rightNavHtml += title;
                    rightNavHtml += "</span>";
                    rightNavHtml += "</li>";
                }
            })
            rightNavHtml += "</ul>"
            $("body").prepend(rightNavHtml);

            var oNav = $('#LoutiNav');//导航壳
            var aNav = oNav.find('li');//导航
            var oTop = $('#goTop');
            //回到顶部
            $(window).scroll(function() {
                var winH = $(window).height();//可视窗口高度
                var iTop = $(window).scrollTop();//鼠标滚动的距离
                if (iTop >= $('.wrapperheader').height()) {
                    oNav.fadeIn();
                    oTop.fadeIn();
                    //鼠标滑动式改变
                    aDiv.each(function() {
                        if (iTop + 40 - $(this).offset().top > 0) {//2022-04-15 韩学闯修改（右导航定位不准确问题）panel-group同层级不要添加其他元素
                            // if (winH + iTop - $(this).offset().top > winH) {
                            aNav.removeClass('active');
                            aNav.eq($(this).index()).addClass('active');
                        }
                    })
                } else {
                    oNav.fadeOut();
                    oTop.fadeOut();
                }
            })
            //点击top回到顶部
            oTop.click(function() {
                $('body,html').animate({
                    "scrollTop" : 0
                }, 500)
            })
            //点击回到当前楼层
            aNav.click(function() {
                var t = aDiv.eq($(this).index()-1).offset().top;
                $('body,html').animate({
                    "scrollTop" : t
                }, 500);
                $(this).addClass('active').siblings().removeClass('active');
            });
        }

        function titleSq(){
            var data= $("#LoutiNav").attr("data");
            if(data==null || "1"==data){
                $("#LoutiNav").find("li").css("right","-83px");
                $("#sqDiv").css("margin-left","25px");
                $("#LoutiNav").css("right","-30px");
                $("#LoutiNav").attr("data","2");
                $("#sqDiv .fa").removeClass("fa-chevron-circle-right");
                $("#sqDiv .fa").addClass("fa-chevron-circle-left");
            }else {
                $("#sqDiv").css("margin-left","0px");
                $("#LoutiNav").css("right","0px");
                $("#LoutiNav").find("li").css("right","0px");
                $("#LoutiNav").find("li").css("visibility","inherit");
                $("#LoutiNav").attr("data","1");
                $("#sqDiv .fa").removeClass("fa-chevron-circle-left");
                $("#sqDiv .fa").addClass("fa-chevron-circle-right");
            }
         }


        //放大表格列
        function fdTableL(){
            var bgl=$(".dropdown-menu").eq(0);
            var checkedArr=[];
            var dataFieldArr=[];
            var dataFieldNameArr=[];

            bgl.find("li").each(function(data){
                var lAttr= $(this).find("input");
                console.log();
                if($(lAttr).prop('checked')){
                    checkedArr.push('1');
                }else{
                    checkedArr.push('0');
                }
                dataFieldArr.push($(lAttr).attr("data-field"));
                dataFieldNameArr.push($(this).find("label").text());
            });
            layer.open({
                type: 2,
                area: ['800px', '600px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: '显示隐藏列',
                content: ctx +"common/fdl?checkedArr="+checkedArr+"&dataFieldArr="+dataFieldArr+"&dataFieldNameArr="+dataFieldNameArr,
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true
            });
        }
    </script>

</div>


<div th:fragment="jquery-tmpl">
  <script th:src="@{/js/jquery.tmpl.js}"></script>
</div>

<div th:fragment="sub-tab-commons">
  <script th:src="@{/kczg/js/subCommons.js}"></script>
</div>
<!-- ztree树插件 -->
<div th:fragment="ztree-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/jquery-ztree/3.5/css/metro/zTreeStyle.css}"/>
</div>
<div th:fragment="ztree-js">
    <script th:src="@{/ajax/libs/jquery-ztree/3.5/js/jquery.ztree.all-3.5.js}"></script>
</div>


<!--

name:input name 默认 radio
id:radio  每一个id为 id+选择value   默认 name 值
labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认选择
divClass:div 的class 默认col-sm-8
callback： 点击时回调函数名
see:回显(true,false) 默认false
isrequired:是否必填 默认false
value: 回显值
dictCode  businessType: 数据字典key

-->


<div th:fragment="initRadio">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	name=${name==null?'radio':name},
               	 	id=${id==null?name:id},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass}
               	 	">

        <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}">选择：</label>
        <div th:class="${divClass}" th:if="${businessType!=null && dictCode!=null}">
            <div class="radio-box" th:each="dict : ${@dict.getDictList(businessType,dictCode)}"
                 th:if="${!see}">
                <input th:attr="checked=${value==dict.dictValue?true:false}" th:id="${id+dict.dictValue}" th:name="${name}"
                       th:value="${dict.dictValue}" type="radio"> <label
                    th:for="${'type_'+dictCode}" th:text="${dict.dictName}"></label>
            </div>
            <div class="form-control-static" th:unless="${!see}" th:utext="${@dict.getDictName(businessType,dictCode,value)}"></div>
        </div>

        <script th:if="${!see}" th:inline="javascript">
            if ([[${isrequired!=null && isrequired}]]) {
                $("input:radio[name='" + [[${name}]] + "']").attr("required", "");
            }

        </script>
        <script th:if="${callback!=null}" th:inline="javascript">

            $('input:radio[name="' + [[${name}]] + '"]').on('ifClicked', function (event) {
                var callback = [[${callback}]]

                eval(callback + '("' + $(this).val() + '")');
            });

        </script>
    </th:block>

</div>


<!--

name:checkbox name 默认 checkbox

id  每一个选项id为id+选项value   id默认为name值
labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认选择
divClass:div 的class 默认col-sm-8
callback： 点击时回调函数名
see:回显(true,false) 默认false
isrequired:是否必填 默认false
value: 回显值
dictCode businessType: 数据字典key

-->


<div th:fragment="initCheckBox">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
					notShowValue=${notShowValue==null?'':notShowValue},
               	 	name=${name==null?'checkbox':name},
               	 	id=${id==null?name:id},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
               	 	arrayValue=${value==null?null:#strings.arraySplit(value, ',')}
               	 	">

        <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}">选择：</label>
        <div th:class="${divClass}" th:if="${businessType!=null && dictCode!=null}">

            <label class="check-box" th:each="dict : ${@dict.getDictList(businessType,dictCode)}" th:if="${!see && notShowValue.indexOf(dict.dictValue)==-1}">
                <input th:attr="checked=${arrayValue!=null && #lists.contains(arrayValue,dict.dictValue)}" th:id="${id+dict.dictValue}" th:name="${name}"
                       th:text="${dict.dictName}" th:value="${dict.dictValue}" type="checkbox"></label>

            <div class="form-control-static" th:unless="${!see}" th:utext="${@dict.getDictName(businessType,dictCode,value)}"></div>
            <label th:for="${name}" class="error"></label>
        </div>
        <script th:if="${!see}" th:inline="javascript">
            if ([[${isrequired!=null && isrequired}]]) {
                $("input:checkbox[name='" + [[${name}]] + "']").attr("required", "");
            }

        </script>
        <script th:if="${callback!=null}" th:inline="javascript">

            $('input:checkbox[name="' + [[${name}]] + '"]').on('ifClicked', function (event) {
                var callback = [[${callback}]]
                eval(callback + '("' + $(this).val() + '",' + !$(this).is(":checked") + ')');
            });

        </script>
    </th:block>

</div>


<!--

name:input name 默认 selectbox
id:input id 默认 name值
labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认选择
divClass:div 的class 默认col-sm-8

see:回显(true,false) 默认false
isrequired:是否必填 默认false
value: 回显值
businessType,dictCode  : 数据字典key
isfirst: 是否添加首选项
firstName 首选项名称 默认 全部
firstValue 首选项名称 默认 ''




-->


<div th:fragment="initSelectBox">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	name=${name==null?'selectbox':name},
               	 	id=${id==null?name:id},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
               	 	firstName=${firstName==null?'请选择':firstName},
               	 	firstValue=${firstValue==null?'':firstValue}
               	 	">

        <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}">选择：</label>
        <div th:class="${divClass}" th:if="${businessType!=null && dictCode!=null}">
            <select th:class="form-control" th:id="${id}" th:if="${!see}" th:name="${name}"
                    th:with="dictData=${@dict.getDictList(businessType,dictCode)}">

                <option th:if="${isfirst!=null && isfirst}" th:text="${firstName}" th:value="${firstValue}"></option>
                <option th:each="dict : ${dictData}" th:selected="${value eq dict.dictValue}"
                        th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
            </select>

            <div class="form-control-static" th:unless="${!see}" th:utext="${@dict.getDictName(businessType,dictCode,value)}"></div>
        </div>

        <script th:if="${!see}" th:inline="javascript">
            $('#' + [[${id}]]).select2();
            if ([[${isrequired!=null && isrequired}]]) {
                $("#" + [[${id}]]).attr("required", "");
            }

        </script>

    </th:block>

</div>


<!--

name:input name 默认 dateInput
id:input id 默认 name值
labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认时间
divClass:div 的class 默认col-sm-8
callback：回调函数名
see:回显(true,false) 默认false
isrequired:是否必填 默认false
value: 回显值（Date型）
strValue: 回显值（String型）,
format : 时间格式 默认'yyyy-mm-dd',
minView: 时间视图  默认'2'

startDate: false  开始时间
endDate : false   结束时间


-->


<div th:fragment="initDate">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},

               	 	name=${name==null?'dateInput':name},
               	 	id=${id==null?name:id},
               	 	format=${format==null?'yyyy-mm-dd':format},
               	 	minView=${minView==null?2:minView},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'时间':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
               	 	value=${value==null?null:#dates.format(value,'yyyy-MM-dd')},
               	 	strValue=${strValue!=null?strValue:value},
               	 	minDate=${minDate!=null?minDate:false},
               	 	maxDate=${maxDate!=null?maxDate:false},

                    startDate=${startDate!=null?startDate:false},
                    endDate=${endDate!=null?endDate:false}

               	 	">

        <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}">提交时间：</label>
        <div th:class="${divClass}">
            <div class="input-group date" th:if="${!see}">
                <input class="form-control" th:id="${id}" th:name="${name}"
                       th:placeholder="${format}" th:value="${strValue}" type="text" readonly> <span
                    class="input-group-addon"><i class="fa fa-calendar"></i></span>
            </div>
            <div class="form-control-static" th:unless="${!see}" th:utext="${strValue}"></div>

        </div>

        <script th:if="${!see}" th:inline="javascript">
            if ([[${isrequired!=null && isrequired}]]) {
                $("#" + [[${id}]]).attr("required", "");
            }
            $("input[name='" + [[${name}]] + "']").datetimepicker({
                format: [[${format}]],
                startView: [[${minView}]],
                minView: [[${minView}]],
                autoclose: true,
                startDate: [[${startDate}]],
                endDate: [[${endDate}]]
            });
        </script>
    </th:block>

</div>


<!-- select2下拉框插件 -->
<div th:fragment="select2-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/select2/select2.min.css}"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/select2/select2-bootstrap.css}"/>
</div>
<div th:fragment="select2-js">
    <script th:src="@{/ajax/libs/select2/select2.min.js}"></script>
</div>

<!-- bootstrap-select下拉框插件 -->
<div th:fragment="bootstrap-select-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-select/bootstrap-select.css}"/>
</div>
<div th:fragment="bootstrap-select-js">
    <script th:src="@{/ajax/libs/bootstrap-select/bootstrap-select.js}"></script>
</div>

<!-- datetimepicker日期和时间插件 -->
<div th:fragment="datetimepicker-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/datapicker/bootstrap-datetimepicker.min.css}"/>
</div>
<div th:fragment="datetimepicker-js">
    <script th:src="@{/ajax/libs/datapicker/bootstrap-datetimepicker.min.js}"></script>
</div>

<!-- ui布局插件 -->
<div th:fragment="layout-latest-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/jquery-layout/jquery.layout-latest.css}"/>
</div>
<div th:fragment="layout-latest-js">
    <script th:src="@{/ajax/libs/jquery-layout/jquery.layout-latest.js}"></script>
</div>

<!-- layui插件 -->
<div th:fragment="lay-ui-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/layui/css/layui.css}"/>
</div>

<!-- summernote富文本编辑器插件 -->
<div th:fragment="summernote-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/summernote/summernote.css}"/>
    <link rel="stylesheet" th:href="@{/ajax/libs/summernote/summernote-bs3.css}"/>
</div>
<div th:fragment="summernote-js">
    <script th:src="@{/ajax/libs/summernote/summernote.min.js}"></script>
    <script th:src="@{/ajax/libs/summernote/summernote-zh-CN.js}"></script>
</div>
<div th:fragment="jsPdf">
    <script th:src="@{/ajax/libs/jsPdf/jsPdf.debug.js}"></script>
</div>
<!--canvas画图-->
<div th:fragment="html2canvas">
    <script th:src="@{/ajax/libs/html2canvas/html2canvas.js}"></script>
</div>
<div th:fragment="ycl-js">
    <script th:src="@{/js/ycl.js}"></script>
</div>


<div th:fragment="sendFile">
    <script th:inline="javascript">
        function sendFile(val, files, label) {
            data = new FormData();
            data.append("file", files);
            $.ajax({
                data: data,
                dataType: 'json',
                type: "POST",
                url: ctx + "attachment/upload",
                cache: false,
                contentType: false,
                processData: false,
                responseType: "json",
                success: function (data) {
                    $(label).summernote('insertImage', ctx + "attachment/download/" + data.fileid);
                },
                error: function (XMLHttpRequest, textStatus, errorThrown) {
                    alert(XMLHttpRequest.status);
                    alert(XMLHttpRequest.readyState);
                    alert(textStatus);
                }
            });
        }
    </script>

</div>

<!-- 附件上传

labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认附件
divClass:指定附件的div 的class 默认col-sm-8
name:附件input name 默认att
id:附件input id 默认att
isrequired:附件是否必须上传 默认false

see:是否查看
回显数据：
	sourceId 业务id,
	sourceModule 模块
	sourceLabel1 标签
	sourceLabel2 标签
	sourceLabel3

上传属性：
maxFileCount:最大限制上传附件 默认是10
-->
<div th:fragment="layui-upload">
    <th:block
            th:with="attachmentMap=${T(com.baosight.bscdkj.mp.ty.utils.SAttachmentUtil).getAttachmentBySourceIdAndSourceLabelToMap(sourceId, sourceModule,sourceLabel1, sourceLabel2, sourceLabel3)},thisId=${#numbers.formatDecimal(T(java.lang.Math).floor(T(java.lang.Math).random()*10000),1,0)}">
        <label th:class="${labelClass==null?'col-sm-3 control-label':labelClass}"
               th:id="${'fjLabel'+thisId}"
               th:style="'display:'+${display==null ?'false':display}" th:text="${labelName==null?'附件：':labelName}"></label>
        <div th:class="${divClass==null?'col-sm-8':divClass}">
            <!--  -->
            <input class="form-control" th:id="${id==null?'att':id}" th:name="${name==null?'att':name}"
                   th:value="${attachmentMap.attachmentIds!=null?attachmentMap.attachmentIds:''}" type="hidden">
            <div class="layui-upload">
                <div th:id="${'div'+thisId}">
                    <button class="layui-btn layui-btn-normal" th:if="${see==null  || (see!=null && !see)}"
                            type="button">选择多文件
                    </button>
                    <!--拖拽-->
                    <div class="drag-box" style="border: 1px  dashed  #ddd;width: 200px; height: 33px;background-color:  #fff; display:  inline-block;
			    font-size:  12px;
			    color:  #666;
			    line-height: 33px;
			    cursor:  pointer;" th:if="${see==null  || (see!=null && !see)}">
                        <img height="24" th:src="@{/img/line-drag.png}" width="24"/>可将附件拖拽到框中
                    </div>
                    <!--拖拽-->
                </div>
                <div class="layui-upload-list" style="max-width: 1000px;">
                    <table class="layui-table">
                        <colgroup>
                            <col>
                            <col width="500">
                            <col width="300">
                            <col width="350">
                        </colgroup>
                        <thead>
                        <tr>
                            <th width="500">文件名</th>
                            <th width="160">大小</th>
                            <th>上传进度</th>
                            <th width="500">操作</th>
                        </tr>
                        </thead>
                        <tbody th:id="${'list'+thisId}">
                        <th:block th:if="${attachmentMap!=null && not #lists.isEmpty(attachmentMap.attachmentList)}">
                            <tr class="upload" th:each="attachment: ${attachmentMap.attachmentList} "
                                th:id="${'upload-'+thisId+'-'+attachmentStat.index}">
                                <td th:utext="${attachment.attachmentName}"></td>
                                <td th:utext="${attachment.attachmentSize+'kb'}"></td>
                                <td>已上传</td>
                                <td><input class="key" th:id="${'attachment-id-'+attachmentStat.index}" th:value="${attachment.attachmentId}"
                                           type="hidden">
                                    <button class="btn btn-sm btn-primary"
                                            th:onclick="download([[${attachment.attachmentId}]]);"
                                            type="button">下载
                                    </button>
                                    <!--                                    <button  type="button"-->
                                    <!--                                             class="btn btn-sm btn-primary"-->
                                    <!--                                            th:onclick="preview([[${attachment.attachmentId}]]);">预览-->
                                    </button>
                                    <button class="btn btn-sm btn-primary" th:if="${see==null  || (see!=null && !see)}"
                                            th:onclick="deleteFile([[${attachment.attachmentId}]],[[${attachmentStat.index}]],[[${thisId}]],[[${id}]]);"
                                            type="button">
                                        删除
                                    </button>
                                </td>
                            </tr>
                        </th:block>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


        <script th:if="${see==null  || (see!=null && !see)}" th:inline="javascript" type="text/javascript">
            //初始化页面信息
            var isrequired = [[${isrequired}]];
            var id = [[${id==null?'att':id}]]
            if (isrequired != null && isrequired) {
                $("#" + [[${'fjLabel'+thisId}]]).addClass("is-required")
                $("#" + id).attr("required", "");
            }
            layui.use(['upload', 'element', 'layer'], function () {
                var $ = layui.jquery
                    , upload = layui.upload
                    , element = layui.element
                    , layer = layui.layer;
                var uploadListIns = upload.render({
                    elem: '#' + [[${'div'+thisId}]]
                    , elemList: $('#' + [[${'list'+thisId}]]) //列表元素对象
                    , url: ctx + 'attachment/upload' //此处用的是第三方的 http 请求演示，实际使用时改成您自己的上传接口即可。
                    , accept: 'file'
                    , multiple: true
                    , number: [[${maxFileCount==null?30:maxFileCount}]]
                    , auto: true
                    , choose: function (obj) {
                        var that = this;
                        var files = this.files = obj.pushFile(); //将每次选择的文件追加到文件队列
                        //读取本地文件
                        obj.preview(function (index, file, result) {
                            var tr = $(['<tr id="upload-' + index + '">'
                                , '<td>' + file.name + '</td>'
                                , '<td>' + (file.size / 1014).toFixed(1) + 'kb</td>'
                                , '<td><div class="layui-progress" lay-filter="progress-demo-' + index + '"><div class="layui-progress-bar" lay-percent=""></div></div></td>'
                                , '<td> <input class="key" id="attachment-id-' + index + '"  type="hidden">'
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-reload layui-hide">重传</button>'
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-download">下载</button>'
                                /* , '<button  type="button"  class="btn btn-sm btn-primary demo-preview">预览</button>'*/
                                , '<button  type="button"  class="btn btn-sm btn-primary demo-delete">删除</button>'
                                , '</td>'
                                , '</tr>'].join(''));

                            //单个重传
                            tr.find('.demo-reload').on('click', function () {
                                obj.upload(index, file);
                            });

                            //删除
                            tr.find('.demo-delete').on('click', function () {
                                var key = $(this).parent().find("#attachment-id-" + index).val();
                                var attachmentIds = $("#" + [[${id==null?'att':id}]]).val();
                                if (attachmentIds != null && attachmentIds != '') {
                                    var arr = attachmentIds.split(",");
                                    if (arr.includes(key)) {
                                        arr.splice(arr.indexOf(key), 1);
                                        $("#" + [[${id==null?'att':id}]]).val(arr.join(","));
                                    }
                                }
                                delete files[index]; //删除对应的文件
                                tr.remove();
                                uploadListIns.config.elem.next()[0].value = ''; //清空 input file 值，以免删除后出现同名文件不可选
                            });
                            //下载
                            tr.find('.demo-download').on('click', function () {
                                var key = $(this).parent().find("#attachment-id-" + index).val();
                                window.open(ctx + "attachment/download/" + key);
                            });
                            //预览
                            tr.find('.demo-preview').on('click', function () {
                                var key = $(this).parent().find("#attachment-id-" + index).val();
                                window.open(ctx + "attachment/preview/" + key);
                            });

                            that.elemList.append(tr);
                            element.render('progress'); //渲染新加的进度条组件
                        });
                    }
                    , done: function (res, index, upload) { //成功的回调
                        var that = this;
                        if (res.status == 1) { //上传成功
                            var that = this;
                            var tr = that.elemList.find('tr#upload-' + index);
                            tr.find("#attachment-id-" + index).val(res.fileid);
                            console.log(index)
                            console.log(this.files)
                            delete this.files[index]; //删除文件队列已经上传成功的文件
                            var attachmentIds = $("#" + [[${id==null?'att':id}]]).val();
                            var arr = attachmentIds.split(",");
                            if (attachmentIds == null || attachmentIds == '') {
                                arr = [];
                            }
                            arr.push(res.fileid);
                            $("#" + [[${id==null?'att':id}]]).val(arr.join(","));
                            tr.find("td").eq(2).html("已上传");
                            console.log("1");
                            return;
                        }
                        this.error(index, upload);
                    }
                    , allDone: function (obj) { //多文件上传完毕后的状态回调
                        console.log(obj)
                    }
                    , error: function (index, upload) { //错误回调
                        var that = this;
                        var tr = that.elemList.find('tr#upload-' + index)
                            , tds = tr.children();
                        tds.eq(3).find('.demo-reload').removeClass('layui-hide'); //显示重传
                    }
                    , progress: function (n, elem, e, index) { //注意：index 参数为 layui 2.6.6 新增
                        console.log(index)
                        element.progress('progress-demo-' + index, n + '%'); //执行进度条。n 即为返回的进度百分比
                    }
                });

            });
        </script>
    </th:block>
</div>
<div th:fragment="lay-upload-js">

    <script th:inline="javascript" type="text/javascript">
        function download(id) {
            window.open(ctx + "attachment/download/" + id);
        }

        function preview(id) {
            window.open(ctx + "attachment/preview/" + id);
        }


        function deleteFile(attId, index, listId, id) {
            $("#upload-" + listId + "-" + index).remove();
            var attachmentIds = $("#" + id).val();
            if (attachmentIds != null && attachmentIds != '') {
                var arr = attachmentIds.split(",");
                if (arr.includes(attId)) {
                    arr.splice(arr.indexOf(attId), 1);
                    $("#" + id).val(arr.join(","));
                }
            }
        }
    </script>

</div>

<!-- cropper图像裁剪插件 -->
<div th:fragment="cropper-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/cropper/cropper.min.css}"/>
</div>
<div th:fragment="cropper-js">
    <script th:src="@{/ajax/libs/cropper/cropper.min.js}"></script>
</div>

<!-- jasny功能扩展插件 -->
<div th:fragment="jasny-bootstrap-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/jasny/jasny-bootstrap.min.css}"/>
</div>
<div th:fragment="jasny-bootstrap-js">
    <script th:src="@{/ajax/libs/jasny/jasny-bootstrap.min.js}"></script>
</div>

<!-- fileinput文件上传插件 -->
<div th:fragment="bootstrap-fileinput-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-fileinput/fileinput.min.css?v=20201202}"/>
</div>
<div th:fragment="bootstrap-fileinput-js">
    <script th:src="@{/ajax/libs/bootstrap-fileinput/fileinput.min.js?v=20201202}"></script>
</div>

<!-- duallistbox双列表框插件 -->
<div th:fragment="bootstrap-duallistbox-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/duallistbox/bootstrap-duallistbox.min.css}"/>
</div>
<div th:fragment="bootstrap-duallistbox-js">
    <script th:src="@{/ajax/libs/duallistbox/bootstrap-duallistbox.min.js}"></script>
</div>

<!-- suggest搜索自动补全 -->
<div th:fragment="bootstrap-suggest-js">
    <script th:src="@{/ajax/libs/suggest/bootstrap-suggest.min.js}"></script>
</div>

<!-- typeahead搜索自动补全 -->
<div th:fragment="bootstrap-typeahead-js">
    <script th:src="@{/ajax/libs/typeahead/bootstrap3-typeahead.min.js}"></script>
</div>

<!-- 多级联动下拉 -->
<div th:fragment="jquery-cxselect-js">
    <script th:src="@{/ajax/libs/cxselect/jquery.cxselect.min.js}"></script>
</div>

<!-- jsonview格式化和语法高亮JSON格式数据查看插件 -->
<div th:fragment="jsonview-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/jsonview/jquery.jsonview.css}"/>
</div>
<div th:fragment="jsonview-js">
    <script th:src="@{/ajax/libs/jsonview/jquery.jsonview.js}"></script>
</div>

<!-- jquery.smartwizard表单向导插件 -->
<div th:fragment="jquery-smartwizard-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/smartwizard/smart_wizard_all.min.css}"/>
</div>
<div th:fragment="jquery-smartwizard-js">
    <script th:src="@{/ajax/libs/smartwizard/jquery.smartWizard.min.js}"></script>
</div>

<!-- ECharts百度统计图表插件 -->
<div th:fragment="echarts-js">
    <script th:src="@{/ajax/libs/report/echarts/echarts-all.min.js}"></script>
</div>

<!-- peity图表组合插件 -->
<div th:fragment="peity-js">
    <script th:src="@{/ajax/libs/report/peity/jquery.peity.min.js}"></script>
</div>

<!-- sparkline线状图插件 -->
<div th:fragment="sparkline-js">
    <script th:src="@{/ajax/libs/report/sparkline/jquery.sparkline.min.js}"></script>
</div>

<!-- 表格行拖拽插件 -->
<div th:fragment="bootstrap-table-reorder-rows-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-rows/bootstrap-table-reorder-rows.js?v=20210602}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-rows/jquery.tablednd.js}"></script>
</div>

<!-- 表格列拖拽插件 -->
<div th:fragment="bootstrap-table-reorder-columns-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-columns/jquery.dragtable.js}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/reorder-columns/bootstrap-table-reorder-columns.js?v=20210602}"></script>
</div>

<!-- 表格列宽拖动插件 -->
<div th:fragment="bootstrap-table-resizable-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/resizable/jquery.resizableColumns.min.js}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/resizable/bootstrap-table-resizable.js?v=20210602}"></script>
</div>

<!-- 表格行内编辑插件 -->
<div th:fragment="bootstrap-editable-css">
    <link rel="stylesheet" th:href="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-editable.css}"/>
</div>
<div th:fragment="bootstrap-table-editable-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-editable.min.js}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/editable/bootstrap-table-editable.js?v=20210602}"></script>
</div>

<!-- 表格导出插件 -->
<div th:fragment="bootstrap-table-export-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/export/bootstrap-table-export.js?v=20210602}"></script>
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/export/tableExport.min.js}"></script>
</div>

<!-- 表格冻结列插件 -->
<div th:fragment="bootstrap-table-fixed-columns-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/columns/bootstrap-table-fixed-columns.js?v=20210602}"></script>
</div>

<!-- 表格自动刷新插件 -->
<div th:fragment="bootstrap-table-auto-refresh-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/auto-refresh/bootstrap-table-auto-refresh.js?v=20210602}"></script>
</div>

<!-- 表格打印插件 -->
<div th:fragment="bootstrap-table-print-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/print/bootstrap-table-print.js?v=20210602}"></script>
</div>

<!-- 表格视图分页插件 -->
<div th:fragment="bootstrap-table-custom-view-js">
    <script th:src="@{/ajax/libs/bootstrap-table/extensions/custom-view/bootstrap-table-custom-view.js?v=20210602}"></script>
</div>

<!--选择人员
userCodeInputId:工号input id 必填
userNameInputId:名称input id 必填
selectType 单选S 多选M 必填
orgCode  组织 默认是空 默认是集团所有组织
-->
<div th:fragment="selectUser">
    <script th:inline="javascript" type="text/javascript">
        var ctx = [[@{/}]];
        var userId = "userId";
        var userNameId = "userName";

        function choiceUser(userCodeInputId, userNameInputId, selectType, orgCode, callback) {
            userId = userCodeInputId;
            userNameId = userNameInputId;
            var url = ctx + 'mpad/user/selectUserList';
            if (selectType === undefined || selectType == null || selectType == '') {
                selectType = "S";
            }
            url += "?selectType=" + selectType;
            if (!(orgCode === undefined) && orgCode != null) {
                url += "&orgCode=" + orgCode;
            }
            var values = $("#" + userCodeInputId).val();
            if (!(values === undefined) && values != null) {
                url += "&values=" + values;
            }
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            url += "&userId=" + $("#" + userId).val() + "&userName" + $("#" + userNameId).val();
            $.modal.open("选择用户", url, '1000', '500');
        }

        /**
         * 回调 中返回 人员信息及机构信息
         * @param userCodeInputId
         * @param userNameInputId
         * @param selectType
         * @param orgCode
         * @param callback
         */
        function choiceUserAndOrg(userCodeInputId, userNameInputId, selectType, orgCode, indexVal, callback) {
          userId = userCodeInputId;
          userNameId = userNameInputId;
          var url = ctx + 'mpad/user/selectUserAndOrgList';
          if (selectType === undefined || selectType == null || selectType == '') {
            selectType = "S";
          }
          url += "?selectType=" + selectType;
          if (!(orgCode === undefined) && orgCode != null) {
            url += "&orgCode=" + orgCode;
          }
          var values = $("#" + userCodeInputId).val();
          if (!(values === undefined) && values != null) {
            url += "&values=" + values;
          }
          if (!(callback === undefined) && callback != null) {
            url += "&callback=" + callback;
          }
          url += "&userId=" + $("#" + userId).val() + "&userName=" + $("#" + userNameId).val() +"&indexVal="+indexVal;
          $.modal.open("选择用户", url, '1000', '500');
        }

        function choiceUserCallback(userCode, userName) {
            $("#" + userId).val(userCode);
            $("#" + userNameId).val(userName);

            $("#" + userId).change(); //别删，别删，有用！
        }
    </script>
</div>
<!-- 选择组织封装

labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认附件
divClass:指定附件的div 的class 默认col-sm-8
userCodeId:userCode name id
userNameId:userName name id
selectType:S 单选 M 多选 默认S
callback: 回调函数
isrequired:是否必填 默认false
value: 当前值
see:是否查看

-->
<div th:fragment="choiceUser">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	userCodeId=${userCodeId==null?'userCodeId':userCodeId},
               	 	userNameId=${userNameId==null?'userNameId':userNameId},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择人员：':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
                    selectType=${selectType==null?'S':selectType}

               	 	">

        <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}"></label>
        <div th:class="${divClass}" th:if="${see!=null && !see}">
            <div class="input-group" th:onclick="choiceUser([[${userCodeId}]],[[${userNameId}]],[[${selectType}]],[[${orgCode}]],[[${callback}]])">
                <input th:id="${userCodeId}" th:name="${userCodeId}" th:value="${value}" type="hidden"/> <input
                    class="form-control detailOrgOrUser" th:id="${userNameId}" th:name="${userNameId}"  th:required="${isrequired!=null && isrequired}"
                    th:value="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(value)}" type="text" readonly>
                <span class="input-group-addon "><i
                        class="fa fa-search "></i></span>
            </div>
        </div>

        <div th:class="${divClass!=null?divClass+' form-control-static':'form-control-static'}" th:unless="${!see}" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(value)}"></div>
    </th:block>
</div>


<!-- 选择人员  回调中 返回人员和机构信息

labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认附件
divClass:指定附件的div 的class 默认col-sm-8
userCodeId:userCode name id
userNameId:userName name id
selectType:S 单选 M 多选 默认S
callback: 回调函数
isrequired:是否必填 默认false
value: 当前值
see:是否查看

-->
<div th:fragment="choiceUserAndOrg">
  <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	userCodeId=${userCodeId==null?'userCodeId':userCodeId},
               	 	userNameId=${userNameId==null?'userNameId':userNameId},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择人员：':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
                    selectType=${selectType==null?'S':selectType}
               	 	">

    <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}"></label>
    <div th:class="${divClass}" th:if="${see!=null && !see}">
      <div class="input-group" th:onclick="choiceUserAndOrg([[${userCodeId}]],[[${userNameId}]],[[${selectType}]],[[${orgCode}]],[[${index}]],[[${callback}]])">
        <input th:id="${userCodeId}" th:name="${userCodeId}" th:value="${value}" type="hidden"/>
        <input
          class="form-control detailOrgOrUser" th:id="${userNameId}" th:name="${userNameId}"  th:required="${isrequired!=null && isrequired}"
          th:value="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(value)}" type="text" readonly>
        <span class="input-group-addon "><i
          class="fa fa-search "></i></span>
      </div>
    </div>

    <div th:class="${divClass!=null?divClass+' form-control-static':'form-control-static'}" th:unless="${!see}" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(value)}"></div>
  </th:block>
</div>


<!--组织
orgCodeInputId 组织 input  ID 必填
orgNameInputId:组织名称 input  ID 必填
selectType 单选S 多选M 必填
level 最高层级 空是查询所有
orgCode 根组织  空是集团
-->
<div th:fragment="selectOrg">
    <script th:inline="javascript" type="text/javascript">
        var orgId = "orgId";
        var orgNameId = "deptName";

        function choiceOrg(orgCodeInputId, orgNameInputId, selectType, level, orgCode, showLevel, callback) {
            orgId = orgCodeInputId;
            orgNameId = orgNameInputId;
            if (selectType === undefined || selectType == null || selectType == '') {
                selectType = "S";
            }
            var url = ctx + "mpad/org/selectOrgList?selectType=" + selectType;
            if (!(level === undefined) && level != null) {
                url += "&level=" + level;
            }
            if (!(orgCode === undefined) && orgCode != null) {
                url += "&orgCode=" + orgCode;
            }
            if (!(showLevel === undefined) && showLevel != null) {
                url += "&showLevel=" + showLevel;
            }
            if (!(callback === undefined) && callback != null) {
                url += "&callback=" + callback;
            }
            url += "&values=" + $("#" + orgId).val();
            debugger
            var options = {
                title: '选择组织',
                width: "380",
                height: '500',
                url: url,
                callBack: choiceOrgCallback
            };
            $.modal.openOptions(options);
        }

        function choiceOrgCallback(index, layero) {
            var tree = layero.find("iframe")[0].contentWindow.$._tree;
            var body = layer.getChildFrame('body', index);
            layero.find("iframe")[0].contentWindow.saveCheck();
            $("#" + orgId).val(body.find('#treeId').val());
            $("#" + orgNameId).val(body.find('#treeName').val());
            layer.close(index);
        }
    </script>
</div>
<!-- 选择组织封装

labelClass:label的class 默认col-sm-3 control-label
labelName:label的text 默认附件
divClass:指定附件的div 的class 默认col-sm-8
orgCodeId:orgCode name id
orgName:orgName name id
selectType:S 单选 M 多选 默认S
level:组织层级
showLevel:显示组织层级
callback: 回调函数
isrequired:是否必填 默认false
value: 当前值
see:是否查看

-->
<div th:fragment="choiceOrg">
    <th:block th:with="
               	 	see=${see!=null&&see?true:false},
               	 	orgCodeId=${orgCodeId==null?'orgCodeId':orgCodeId},
               	 	orgNameId=${orgNameId==null?'orgNameId':orgNameId},
               	 	labelClass=${labelClass==null?'col-sm-3 control-label':labelClass},
               	 	labelName=${labelName==null?'选择组织：':labelName},
               	 	divClass=${divClass==null?'col-sm-8':divClass},
                    selectType=${selectType==null?'S':selectType}

               	 	">

        <label th:class="${isrequired!=null && isrequired?labelClass+' is-required':labelClass}" th:text="${labelName}"></label>
        <div th:class="${divClass}" th:if="${see!=null && !see}">
            <div class="input-group" th:onclick="choiceOrg([[${orgCodeId}]],[[${orgNameId}]],[[${selectType}]],[[${level}]],[[${orgCode}]],[[${showLevel}]],[[${callback}]])">
                <input th:id="${orgCodeId}" th:name="${orgCodeId}" th:value="${value}" type="hidden" /> <input
                    class="form-control detailOrgOrUser" th:id="${orgNameId}" th:name="${orgNameId}"
                    th:value="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(value)}" type="text"  autocomplete="off" th:required="${isrequired!=null && isrequired}" readonly>
                <span class="input-group-addon "><i
                        class="fa fa-search "></i></span>
            </div>
        </div>

        <div class="form-control-static" th:unless="${!see}" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(value)}"></div>
    </th:block>
</div>

<!-- 查看流程历史记录  -->
<div th:fragment="includeTaskHistoryList">
	<th:block th:include="component/wfCommentList :: init(processInstanceId=${instanceId})" />
</div>

<div th:fragment="step">
    <th:block th:with="stepData=${@step.initStep(approveKind,currentNode)}">


        <div class="for-liucheng" th:if="${not #lists.isEmpty(stepData.dictList)}">
            <div class="liulist for-cur" th:class="${map.sortNo<=stepData.sortNo?'liulist for-cur':'liulist'}" th:each="map:${stepData.dictList}"></div>

            <div class="liutextbox">
                <div th:class="${map.sortNo<=stepData.sortNo?'liutext for-cur':'liutext'}" th:each="map:${stepData.dictList}"><em>[[${map.sortNo}]]</em><br>
                    <strong>[[${map.dictName}]]</strong></div>
            </div>
        </div>

        <script th:inline="javascript" type="text/javascript">

        </script>

    </th:block>
</div>


<!-- 格式化数字 -->
<div th:fragment="formatDecimal">
    <script th:inline="javascript" type="text/javascript">
        var dataDecimalValue = [[${dataDecimalValue}]];
        if (dataDecimalValue != null) {
            document.write(dataDecimalValue);
        } else {
            document.write(0);
        }

    </script>
</div>

<!-- 流程退回按钮 -->
<div th:fragment="wfReturn">
    <th:block th:with="returnActS=${@SWorkFlowUtil.getReturnsActivities(taskId)}">
        <th:block th:if="${returnActS.size()==1}">
            <button class="btn btn-primary" th:onclick="doWorkFlowReturn([[${returnActS[0].nodeKey}]],[[${callback}]])" type="button"><i class="fa fa-reply-all"></i>&nbsp;退回</button>
        </th:block>
        <th:block th:if="${returnActS.size()>1}">
            <div class="btn-group dropup">
                <button aria-expanded="false" aria-haspopup="true" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" type="button">
                    &nbsp;退回
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li th:each="returnAct : ${returnActS}">
                        <a href="javascript:void(0);" th:onclick="doWorkFlowReturn([[${returnAct.nodeKey}]],[[${callback}]])" th:text="${returnAct.nodeName}"></a>
                    </li>
                </ul>
            </div>
        </th:block>
    </th:block>
    <script th:inline="javascript" type="text/javascript">
        function doWorkFlowReturn(nodeKey, callback) {
            eval(callback + '("' + nodeKey + '")');
        }
    </script>
</div>

<!-- 流程退回按钮 -->
<div th:fragment="wfReturnByCode">
    <th:block th:with="returnActS=${@SWorkFlowUtil.getReturnsActivities(processCode,currentActivity)}">
        <th:block th:if="${returnActS.size()==1}">
            <button class="btn btn-primary" th:onclick="doWorkFlowReturn([[${returnActS[0].nodeKey}]],[[${callback}]])"
                    type="button"><i class="fa fa-reply-all"></i>[[${buttonName==null?'退回':buttonName}]]
            </button>
        </th:block>
        <th:block th:if="${returnActS.size()>1}">
            <div class="btn-group dropup">
                <button aria-expanded="false" aria-haspopup="true" class="btn btn-primary dropdown-toggle" data-toggle="dropdown" type="button">
                    退回
                    <span class="caret"></span>
                </button>
                <ul class="dropdown-menu">
                    <li th:each="returnAct : ${returnActS}">
                        <a href="javascript:void(0);" th:onclick="doWorkFlowReturn([[${returnAct.nodeKey}]],[[${callback}]])" th:text="${returnAct.nodeName}"></a>
                    </li>
                </ul>
            </div>
        </th:block>
    </th:block>
    <script th:inline="javascript" type="text/javascript">
        function doWorkFlowReturn(nodeKey, callback) {
            eval(callback + '("' + nodeKey + '")');
        }
    </script>
</div>
