<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('境外专利_单个缴费规则')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<style>
    .kwzlStyle {
        background: #fff none;
        border: 1px solid #e5e6e7;
        border-radius: 4px;
        color: inherit;
        display: block;
        padding: 3px 6px 4px;
        -webkit-transition: border-color .15s ease-in-out 0s, box-shadow .15s ease-in-out 0s;
        transition: border-color .15s ease-in-out 0s, box-shadow .15s ease-in-out 0s;
        width: 100%;
        height: 31px;
        font-size: 14px
    }
</style>
<body class="white-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="from01" th:object="${data}">
        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#1"
                                               aria-expanded="false" class="collapsed">基本信息
                        <span class="pull-right"><i
                                class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h5>
                </div>
                <!--折叠区域-->
                <div id="1" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <input name="gwSqh" id="gwSqh" th:value="*{gwSqh}" type="hidden"/>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">缴费次数：</label>
                                    <div class="col-sm-6">
                                        <select class="form-control" name="cs"
                                                th:with="type=${@dict.getDictList('KWZL','KWZL_JFCS')}" id="cs"
                                                onchange="gradeChange('jfcsMoban','jfcsTr','m-0-','alist')">
                                            <option value="">请选择</option>
                                            <option th:each="dict : ${type}" th:text="${dict.dictName}"
                                                    th:value="${dict.dictValue}"></option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <table class="table table-bordered table-hover table-striped">
                            <tbody id="jfcsTr">
                            <th:block th:data-index='${defitionStat.index}' th:each="defition:${data.alist}">
                                <tr>
                                    <td>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label is-required">缴费日期：</label>
                                                    <div class="col-sm-6">
                                                        <th:block
                                                                th:replace="/component/date :: init(id='alist['+${defitionStat.index}+'].payDate', name='alist['+${defitionStat.index}+'].payDate',format='yyyy-mm',strValue=${defition.payDate},minView=3)"></th:block>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="row">
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">第
                                                        <th:block th:text="${defitionStat.index+1}"></th:block>
                                                        次缴费额：</label>
                                                    <div class="col-sm-6">
                                                        <input class="form-control"
                                                               th:name="'alist['+${defitionStat.index}+'].payMoney'"
                                                               th:id="'alist['+${defitionStat.index}+'].payMoney'"
                                                               th:value="${defition.payMoney}" type="number">
                                                    </div>
                                                </div>
                                            </div>
                                            <div class="col-sm-6">
                                                <div class="form-group">
                                                    <label class="col-sm-3 control-label">第
                                                        <th:block th:text="${defitionStat.index+1}"></th:block>
                                                        次代缴事务所：</label>
                                                    <div class="form-group"
                                                         th:include="KIZL/PA/paInclude :: initSwsinfo(id='alist['+${defitionStat.index}+'].swsId', name='alist['+${defitionStat.index}+'].swsId',divClass='col-sm-6', isPct='1',isfirst='true',labelName=' ',value=${defition.swsId})"></div>
                                                </div>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </th:block>
                            </tbody>
                        </table>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">国家阶段子文档：</label>
                                    <div class="col-sm-6">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-1 control-label"></label>
                            <div class="col-sm-9">
                                <table class="table table-bordered table-hover table-striped" id="mytable">
                                    <thead>
                                    <tr>
                                        <th style="text-align: center;">国外申请号</th>
                                        <th style="text-align: center;">所在国</th>
                                        <th style="text-align: center;">缴费日期</th>
                                        <th style="text-align: center;">缴费额</th>
                                        <th style="text-align: center;">资助日期</th>
                                        <th style="text-align: center;">资助额</th>
                                        <th style="text-align: center;">代缴事务所</th>
                                    </tr>
                                    </thead>
                                    <tbody id="tabletBody">
                                    <tr th:data-index='${defitionStat.index}' th:each="defition:${data.supList}">
                                        <td style='text-align: center; width: 50px;'>
                                            <a th:onClick="onOpenJFDetail([[${defition.gjjdId}]])"
                                               style="text-decoration: underline; color: #0000cc">[[${defition.gjSqh}]]</a>
                                        </td>
                                        <td style='text-align: center; width: 50px;' th:utext="${defition.stateName}">
                                        </td>
                                        <td style='text-align: center; width: 50px;' th:utext="${defition.payDate}">
                                        </td>
                                        <td style='text-align: center; width: 50px;' th:utext="${defition.payMoney}">
                                        </td>
                                        <td style='text-align: center; width: 50px;' th:utext="${defition.supportDate}">
                                        </td>
                                        <td style='text-align: center; width: 50px;'
                                            th:utext="${defition.supportMoney}">
                                        </td>
                                        <td style='text-align: center; width: 50px;' th:utext="${@swsinfo.getSwsName(defition.swsId)}">
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <div th:include="include :: layui-upload(id='dgjfxgfjId',name='dgjfxgfjId',labelName='相关附件：',
								labelClass='col-sm-3 control-label',sourceId=*{gwSqh},sourceModule='KWZL',sourceLabel1='KWZL_DGJFXGFJ')"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div style="display: none">
    <table id="jfcsMoban">
        <tbody>
        <tr>
            <td>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-3 control-label is-required">缴费日期：</label>
                            <div class="col-sm-6">
                                <th:block
                                        th:replace="/component/date :: init(id='m-0-payDate', name='m-0-payDate',format='yyyy-mm',minView=3)"></th:block>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">第displayOrderReplace次缴费额：</label>
                            <div class="col-sm-6">
                                <input type="number" id="m-0-payMoney" name="m-0-payMoney" required="true"
                                       class="form-control">
                            </div>
                        </div>
                    </div>
                    <div class="col-sm-6">
                        <div class="form-group">
                            <label class="col-sm-3 control-label">第displayOrderReplace次代缴事务所：</label>
                            <div class="col-sm-6">
                                <select class="kwzlStyle" name="m-0-swsId" id="m-0-swsId"
                                        th:with="swsinfoData=${@swsinfo.getSwsList('1','')}">
                                    <option value="">请选择</option>
                                    <option th:each="swsinfo : ${swsinfoData}" th:text="${swsinfo.swsName}"
                                            th:value="${swsinfo.swsId}"></option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </td>
        </tr>
        </tbody>
    </table>
</div>
<div class="toolbar toolbar-bottom" role="toolbar">
    <button type="button" class="btn btn-primary"
            onclick="submitHandler()">
        <i class="fa fa-check"></i>保存
    </button>
    &nbsp;
    <button type="button" class="btn btn-danger"
            onclick="closeItem()">
        <i class="fa fa-reply-all"></i>返回
    </button>
</div>
<script th:inline="javascript">
    var prefix = ctx + "KWZL/JF/DG"

    $("#form-moneyPayinfo-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTab(prefix + "/doSave", $('#from01').serialize());
        }
    }

    function onOpenJFDetail(value) {
        $.modal.openTab('国际申请阶段录入缴费及资助表', prefix + '/KWZLJFDG04?jwzlId=' + value, false);
    }


    //申请人数量加载
    var x = [[${data.alist}]].length;
    var xOption = document.getElementById("cs");
    if (xOption != null) {
        for (var i = 0; i < xOption.length; i++) {
            if (xOption[i].value == x) {
                xOption[i].selected = true;
            }
        }
    }

    function gradeChange(sourceId, targetId, sourceReplace, targetBlockId) {
        var mobanHtml = jQuery("#" + sourceId + " tbody").html();//取得模板html
        var cs = $("#cs").val();
        var jfcsTr = $("#jfcsTr").find("tr").length;//实际
        if (cs == 0) {
            $("#" + targetId).html("");
        } else {
            var personLen = jfcsTr;
            if (cs < personLen) {
                var trList = $("#jfcsTr").find("tr:gt(" + eval(cs - 1) + ")");
                $.each(trList, function (index, item) {
                    $(item).remove();
                })
            } else {
                for (var i = personLen; i < cs; i++) {
                    var curMobanHtml = mobanHtml.replace(new RegExp(sourceReplace, "gm"), targetBlockId + "[" + i + "].");
                    curMobanHtml = curMobanHtml.replace(new RegExp("displayOrderReplace", "gm"), i + 1);
                    $("#" + targetId).append(curMobanHtml);
                }
            }
        }
    }
</script>
</body>
</html>