<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('国际阶段答复_进行答复')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: summernote-js"/>
    <th:block th:include="include :: sendFile"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content">
    <div class="form-group" th:include="include :: step(approveKind='KWZL_GJJS',currentNode=${activityCode})"></div><br/>
    <form class="form-horizontal m" id="from01">
        <!--第一块-->
        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#1"
                                               aria-expanded="false" class="collapsed">详情<span class="pull-right"><i
                            class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h5>
                </div>
                <!--折叠区域-->
                <div id="1" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">总序号：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyBaseInfo.zxh}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">国际专利申请编号：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyBaseInfo.gwSqh}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">专利申请名称：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyBaseInfo.inZlmc}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">申请人：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyBaseInfo.sqr}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">宝钢内主要申请单位：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyBaseInfo.sbbmName}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">原国内申请号：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyBaseInfo.inSqh}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">优先权日：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyBaseInfo.priorityDate}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label is-required">国际申请日期：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyBaseInfo.gjsqrq}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">管理员：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyDfsc.adminName}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">管理员联系电话：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyDfsc.adminPhone}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">发明人：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyDfsc.fmrName}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">发明人收到日：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyDfsc.fmrDate}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">审查意见发文日：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyDfsc.sendDate}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">答复截止日期：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyDfsc.endDate}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">PCT申请号：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static" th:utext="${applyBaseInfo.pctsqh}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <div th:include="include :: layui-upload(id='gjsjId',name='gjsjId',labelName='国际检索报告：',
								labelClass='col-sm-3 control-label',sourceId=${applyBaseInfo.jwsqId},sourceModule='KWZL',sourceLabel1='KWZL_GJSJ',see='true')"></div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="mnote-editor-title"><span class="txt-impt"></span>主管部门启动答复相关问题：</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                                    <textarea class="form-control" style="white-space: break-spaces;" name="contentStart" rows="8" cols="200" minlength="10" th:text="${applyDfsc.contentStart}" readonly></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <div th:include="include :: layui-upload(id='dfxgId',name='dfxgId',labelName='相关附件：',
								labelClass='col-sm-3 control-label',sourceId=${applyDfsc.jwdfscId},sourceModule='KWZL',sourceLabel1='KWZL_DFXG',see='true')"></div>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="mnote-editor-title"><span class="txt-impt"></span>发明人答复相关问题：</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                                    <textarea class="form-control" style="white-space: break-spaces;" name="contentFmr" rows="8" cols="200" minlength="10" th:text="${applyDfsc.contentFmr}"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <div th:include="include :: layui-upload(id='fmrdfxgId',name='fmrdfxgId',labelName='附件上传：',
								labelClass='col-sm-3 control-label',sourceId=${applyDfsc.jwdfscId},sourceModule='KWZL',sourceLabel1='KWZL_FMRDFXG')"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <div th:include="KIZL/PA/paInclude :: initSwsinfo(id='swsGuid', name='swsGuid',isrequired=true,divClass='col-sm-6',
									isPct='1',isfirst='true',labelName='代理机构：',value=${applyBaseInfo.swsGuid},see='true')"></div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">代理人邮箱：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static"
                                             th:utext="${applyBaseInfo.swsDlremail}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">代理人：</label>
                                    <div class="col-sm-6">
                                        <div class="form-control-static" th:utext="${applyBaseInfo.swsDlrxm}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-sm-6">
                                <div class="form-group">
                                    <label class="col-sm-3 control-label">代理人联系电话：</label>
                                    <div class="col-sm-9">
                                        <div class="form-control-static"
                                             th:utext="${applyBaseInfo.swsDlrphone}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <input name="jwdfscId" id="jwdfscId" th:value="${applyDfsc.jwdfscId}" type="hidden">
                        <input name="taskId" type="hidden" th:value="${taskId}">
                        <input name="activityCode" type="hidden" th:value="${activityCode}">
                        <input name="processInstanceId" type="hidden" th:value="${processInstanceId}">
                        <input name="businessGuid" type="hidden" th:value="${businessGuid}">
                    </div>
                </div>
            </div>
        </div>
        <!--审批历史-->
        <p th:if="${processInstanceId}">
            <th:block th:include="component/wfCommentList :: init(processInstanceId=${processInstanceId},businessId=${businessGuid})" />
        </p>
    </form>
</div>
<th:block th:include="KWZL/kwzl :: buttonKWZL(zcId='true',wfId='true',id='from01',submitUrl='KWZL/GJJS/doSubmit',doSaveUrl='KWZL/GJJS/doSave',processInstanceId=${processInstanceId})"/>
<script th:inline="javascript">
    $(".contentStart").summernote({
        lang: 'zh-CN',
        height: 200,
        callbacks: {
            onImageUpload: function (files) {
                sendFile(this, files[0], this);
            }
        }
    });
    $(".contentFmr").summernote({
        lang: 'zh-CN',
        height: 200,
        callbacks: {
            onImageUpload: function (files) {
                sendFile(this, files[0], this);
            }
        }
    });
</script>
</body>
</html>