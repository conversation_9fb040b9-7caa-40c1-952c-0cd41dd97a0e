<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('境外专利_资助_基本信息')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content">
        <form class="form-horizontal m" id="from01" th:object="${data}">
            <div class="panel-group" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h5 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#1"
                                                   aria-expanded="false" class="collapsed">基本信息
                            <span class="pull-right"><i
                                    class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h5>
                    </div>
                    <!--折叠区域-->
                    <div id="1" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <h1 style="text-align: center">境外专利资助清单</h1>
                            <input name="jwzzId" id="jwzzId" th:value="*{jwzzId}" type="hidden"/>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">总份数：</label>
                                <div class="col-sm-6">
                                    <div class="form-control-static" th:utext="*{fs}"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label">生成日期：</label>
                                <div class="col-sm-6">
                                    <div th:include="component/date :: init(id='createDate',name='createDate',isrequired=true, strValue=*{createDate},see=true)"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">反馈日期：</label>
                                <div class="col-sm-4">
                                    <div th:include="component/date :: init(id='supportDate',name='supportDate',isrequired=true, strValue=*{supportDate},see='true')"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion" role="tablist"
                 aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" data-parent="#version"
                               href="#fymx" aria-expanded="false" class="collapsed">明细
                                &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                <span class="pull-right"><i class="fa fa-chevron-down"
                                                            aria-hidden="true"></i></span>
                            </a>
                        </h4>
                    </div>
                    <div id="fymx" class="panel-collapse collapse in"
                         aria-expanded="false">
                        <div class="panel-body">
                            <div class="btn-group-sm" id="toolbar" role="group">
                                <a class="btn btn-success"
                                   th:onclick="exportExcel('export1')">
                                    <i class="fa fa-download"></i>清单1
                                </a>
                                <a class="btn btn-success"
                                   th:onclick="exportExcel2('export2')">
                                    <i class="fa fa-download"></i>清单2
                                </a>
                            </div>
                            <div class="col-sm-12 select-table table-striped">
                                <table id="bootstrap-table"></table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="toolbar toolbar-bottom">
        <button type="button" class="btn btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>关闭
        </button>
    </div>
    <script th:inline="javascript">
        var prefix = ctx + "KWZL/ZZ"

        $("#from01").validate({
            focusCleanup: true
        });

        $(function () {
            var options = {
                data: [[${data.supportMemos}]],
                createUrl: prefix + "/addEdit?jwzzId={id}" ,
                removeUrl: prefix + "/removeEdit",
                showRefresh: false,
                showSearch: false,
                showColumns: false,
                showToggle: false,
                pagination: false,
                clickToSelect: false,
                columns: [
                    {
                        field: 'jwzzmemoId',
                        title: '主键',
                        visible: false
                    },
                    {
                        title: '序号',
                        formatter: function (value, row, index) {
                            return $.table.serialNumber(index);
                        }
                    },
                    {
                        field: 'inZlmc',
                        align : 'center',
                        title: '专利名称',
                    },
                    {
                        field: 'yzzgj',
                        align : 'center',
                        title: '已资助国家',
                    },
                    {
                        field: 'syzzgj',
                        align : 'center',
                        title: '所有资助国家',
                    },
                    {
                        field: 'sqzzgj',
                        align : 'center',
                        title: '申请资助国家',
                    },
                    {
                        field: 'inSqh',
                        align : 'center',
                        title: '专利号',
                    },
                    {
                        field: 'pctSqh',
                        align : 'center',
                        title: 'PCT申请号',
                    },
                    {
                        field: 'zlqr',
                        align : 'center',
                        title: '专利权人',
                    },
                    {
                        field: 'dq',
                        align : 'center',
                        title: '第一申请人地址所属区',
                    },
                    {
                        field: 'stateSqrq',
                        align : 'center',
                        title: '颁证日',
                    },
                    {
                        field: 'supportMoney',
                        align : 'center',
                        title: '资助金额',
                    },
                    {
                        field: 'stateSqh',
                        align : 'center',
                        title: '所在国专利号',
                    }
                ]
            };
            $.table.init(options);
        });

        function exportExcel(path) {
            var fileName = '境外专利资助清单';
            window.location.href = ctx + "KWZL/ZZ/" + path + '?jwzzId=' + $('#jwzzId').val();
        }

        function exportExcel2(path){
            var title = "境外专利资助清单";
            var url = ctx + "KWZL/ZZ/" + path + '?jwzzId=' + $('#jwzzId').val();
            $.modal.openTab(title, url, false)
        }

    </script>
</body>
</html>