<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('境外专利_发奖_下发列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <input name="jwzlId" th:value="${jwsqId}" th:type="hidden">
                        </ul>
                    </div>
                </form>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "KWZL/FJ/mx";
        var bonusType = [[${@dict.getDictList('KWZL','KWZL_FJTYPE')}]];
        $(function () {
            var options = {
                url: prefix + "/list",
                detailUrl: ctxYWZT + "web/YWZTFJ03?ywlxId={id}",
                modalName: "奖励分配详情",
                columns: [
                    {
                        field: 'downzlId',
                        title: '主键',
                        visible: false
                    },
                    {
                        field: 'bonusType',
                        title: '发奖类别',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(bonusType, value);
                        }
                    },
                    {
                        field: 'totalJe',
                        title: '总奖金额(元)'
                    },
                    {
                        field: 'isWhfmr',
                        title: '是否维护发明人',
                        formatter: function (value, row, index) {
                            if ('1' == row.isWhfmr) {
                                return "是";
                            } else {
                                return "否";
                            }
                        }
                    },
                    {
                        field: 'firstLxr',
                        title: '第一联系人'
                    },
                    {
                        title: '操作',
                        align: 'center',
                        formatter: function(value, row, index) {
                            var actions = [];
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.downzlId + '\')"><i class="fa fa-eye"></i>详情</a>');
                            return actions.join('');
                        }
                    }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>