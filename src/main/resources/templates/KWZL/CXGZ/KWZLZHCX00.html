<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('综合查询')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="KWZL/kwzl :: selectFmr"/>
    <th:block th:include="KWZL/kwzl :: selectCountry"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="form01">
        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title"><a data-toggle="collapse" href="#1"
                                               aria-expanded="false" class="collapsed">境内专利<span
                            class="pull-right"><i
                            class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h5>
                </div>
                <!--折叠区域-->
                <div id="1" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">境内专利公司编号：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="inBgbh" type="text">
                            </div>
                            <label class="col-sm-2 control-label">原国内专利申请号：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="inSqh" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">申请部门：</label>
                            <div class="col-sm-8">
                                <th:block
                                        th:include="/component/selectOrg::init(orgCodeId='sbbmCode',orgNameId='sbbmName',selectType='S')"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">境内专利名称：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="inZlmc" type="text">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title"><a data-toggle="collapse" href="#2"
                                               aria-expanded="false" class="collapsed">国际阶段<span
                            class="pull-right"><i
                            class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h5>
                </div>
                <!--折叠区域-->
                <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">总序号：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="zxh" type="text">
                            </div>
                            <label class="col-sm-2 control-label">当前状态：</label>
                            <div class="col-sm-4">
                                <th:block
                                        th:include="/component/radio :: init(id='isvalid', name='isvalid',businessType='KWZL',isfirst='true', dictCode='KWZL_ISVALID')"></th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">申请状态：</label>
                            <div class="col-sm-4">
                                <th:block
                                        th:include="/component/radio :: init(id='paStatus', name='paStatus',businessType='KWZL',isfirst='true', dictCode='KWZL_PA_STATUS')"></th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">国际专利申请编号：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="gwSqh" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">国际专利申请名称：</label>
                            <div class="col-sm-8">
                                <input class="form-control" name="zlmc" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">申请人：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="sqr" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">宝钢内申报单位：</label>
                            <div class="col-sm-8">
                                <input class="form-control" name="bgSbbmName" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">发明人：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/selectUser :: init(userCodeId='fmrCode',userNameId='fmrName',selectType='S')"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">来源类型：</label>
                            <div class="col-sm-4">
                                <th:block th:include="/component/radio :: init(id='fromType', name='fromType',businessType='KIZL',isfirst='true', dictCode='KI_FROM_TYPE')"></th:block>
                            </div>
                            <label class="col-sm-2 control-label">是否全球首发：</label>
                            <div class="col-sm-4">
                                <th:block
                                        th:include="/component/radio :: init(id='isqqsf', name='isqqsf',businessType='KWZL',isfirst='true', dictCode='KWZL_ISQQSF')"></th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">是否金苹果项目：</label>
                            <div class="col-sm-4">
                                <th:block
                                        th:include="/component/radio :: init(id='isjpg', name='isjpg',businessType='KWZL',isfirst='true', dictCode='KWZL_ISJPG')"></th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">金苹果轮次：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="jpgXh" type="number">
                            </div>
                            <label class="col-sm-2 control-label">金苹果团队：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="teamName" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">金苹果项目名称：</label>
                            <div class="col-sm-6">
                                <input class="form-control" name="jpgXmName" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">优先权日：</label>
                            <div class="col-sm-7">
                                <div style="width: 30%;display:inline-block;">
                                    <th:block
                                            th:include="/component/date::init(name='priorityDateMin',id='priorityDateMin')"/>
                                </div>
                                <span>到</span>
                                <div style="width: 30%;display:inline-block;">
                                    <th:block
                                            th:include="/component/date::init(name='priorityDateMax',id='priorityDateMax')"/>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">国际申请日期：</label>
                            <div class="col-sm-7">
                                <div style="width: 30%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='gjsqrqMin',id='gjsqrqMin')"/>
                                </div>
                                <span>到</span>
                                <div style="width: 30%;display:inline-block;">
                                    <th:block
                                            th:include="/component/date::init(name='gjsqrqMax',id='gjsqrqMax')"/>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">国际专利申请编号日期：</label>
                            <div class="col-sm-7">
                                <div style="width: 30%;display:inline-block;">
                                    <th:block
                                            th:include="/component/date::init(name='jrgjcsrqMin',id='jrgjcsrqMin')"/>
                                </div>
                                <span>到</span>
                                <div style="width: 30%;display:inline-block;">
                                    <th:block
                                            th:include="/component/date::init(name='jrgjcsrqMax',id='jrgjcsrqMax')"/>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">PCT申请号：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="pctsqh" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">是否要求国际初审：</label>
                            <div class="col-sm-4">
                                <th:block
                                        th:include="/component/radio :: init(id='isGjcs', name='isGjcs',businessType='KWZL',isfirst='true', dictCode='KWZL_ISGJCS')"></th:block>
                            </div>
                            <label class="col-sm-2 control-label">是否选择进入欧洲专利局：</label>
                            <div class="col-sm-4">
                                <th:block
                                        th:include="/component/radio :: init(id='extra2', name='extra2',businessType='KWZL',isfirst='true', dictCode='KWZL_OZZLJ')"></th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-8">
                                <div th:include="KWZL/kwzl :: choiceCountry(labelName='建议申请国家：',userCodeId='sqgjTcr',userNameId='sqgjTcrName',selectType='S',value=*{sqgjTcr})"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-8">
                                <div th:include="KWZL/kwzl :: choiceCountry(labelName='实际申请国家：',userCodeId='sqgjFinal',
									userNameId='sqgjFinalName',selectType='M',value=*{sqgjFinal})"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">第一联系人：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/selectUser :: init(userCodeId='firstPerson',userNameId='firstPersonName',selectType='S')"></div>
                            </div>
                            <label class="col-sm-2 control-label">第一联系人电话：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="firstMobile" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">第一联系人手机：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="firstPhone" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">第二联系人：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/selectUser :: init(userCodeId='secondPerson',userNameId='secondPersonName',selectType='S')"></div>
                            </div>
                            <label class="col-sm-2 control-label">第二联系人电话：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="secondPhone" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">第二联系人手机：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="secondMobile" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-1 control-label"></label>
                            <div class="col-sm-4">
                                <div th:include="KIZL/PA/paInclude :: initSwsinfo(id='swsGuid', name='swsGuid',
									isPct='1',isfirst='true',labelName='代理事务所：')"
                                     onclick="sws()"></div>
                            </div>
                            <label class="col-sm-3 control-label">代理事务所联系人：</label>
                            <div class="col-sm-3">
<!--                                <div th:include="KIZL/PA/paInclude :: initSwsperson(id='swsLxr', name='swsLxr',-->
<!--									labelName='代理事务所联系人：',swsGuid=*{swsGuid},userType='2')"></div>-->
                                <input class="form-control" name="swsLxr" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">代理人姓名：</label>
                            <div class="col-sm-4">
<!--                                <div th:include="KIZL/PA/paInclude :: initSwsperson(id='swsDlrxm', name='swsDlrxm',-->
<!--									labelName='代理人姓名：',swsGuid=*{swsGuid},userType='1')"-->
<!--                                     onclick="dlr()"></div>-->
                                <input class="form-control" name="swsDlrxm" type="text">
                            </div>
                            <label class="col-sm-2 control-label">代理人联系电话：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="swsDlrphone" id="swsdlrPhone">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h5 class="panel-title"><a data-toggle="collapse" href="#3"
                                               aria-expanded="false" class="collapsed">国家阶段<span
                            class="pull-right"><i
                            class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h5>
                </div>
                <!--折叠区域-->
                <div id="3" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">进入国家时间：</label>
                            <div class="col-sm-7">
                                <div style="width: 30%;display:inline-block;">
                                    <th:block
                                            th:include="/component/date::init(name='jrgjjdrqMin',id='jrgjjdrqMin')"/>
                                </div>
                                <span>到</span>
                                <div style="width: 30%;display:inline-block;">
                                    <th:block
                                            th:include="/component/date::init(name='jrgjjdrqMax',id='jrgjjdrqMax')"/>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="col-sm-8">
                                <div class="form-group" th:include="KWZL/kwzl :: choiceCountry(labelName='申请国家和地区：',userCodeId='stateCode',
									userNameId='stateName',selectType='M',divClass='col-sm-8')"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">是否实际进入欧洲专利局：</label>
                            <div class="col-sm-4">
                                <th:block
                                        th:include="/component/radio :: init(id='isEp', name='isEp',businessType='KWZL',isfirst='true', dictCode='KWZL_OZZLJ')"></th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">国外专利申请编号：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="gjSqh" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">当前法律状态：</label>
                            <div class="col-sm-4">
                                <th:block
                                        th:include="/component/radio :: init(id='gjIsvalid', name='gjIsvalid',businessType='KWZL',isfirst='true', dictCode='KWZL_ISVALID')"></th:block>
                            </div>
                            <label class="col-sm-2 control-label">申请状态：</label>
                            <div class="col-sm-4">
                                <th:block
                                        th:include="/component/radio :: init(id='sqStatus', name='sqStatus',businessType='KWZL',isfirst='true', dictCode='KWZL_SQ_STATUS')"></th:block>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">该国专利外文名称：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="nameEng" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">所在国申请号：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="stateSqh" type="text">
                            </div>
                            <label class="col-sm-2 control-label">所在国专利号：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="stateZlh" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">授权日期：</label>
                            <div class="col-sm-7">
                                <div style="width: 30%;display:inline-block;">
                                    <th:block
                                            th:include="/component/date::init(name='stateSqrqMin',id='stateSqrqMin')"/>
                                </div>
                                <span>到</span>
                                <div style="width: 30%;display:inline-block;">
                                    <th:block
                                            th:include="/component/date::init(name='stateSqrqMax',id='stateSqrqMax')"/>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">专利自然终止日期：</label>
                            <div class="col-sm-7">
                                <div style="width: 30%;display:inline-block;">
                                    <th:block
                                            th:include="/component/date::init(name='zrzzrqMin',id='zrzzrqMin')"/>
                                </div>
                                <span>到</span>
                                <div style="width: 30%;display:inline-block;">
                                    <th:block
                                            th:include="/component/date::init(name='zrzzrqMax',id='zrzzrqMax')"/>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">授权文献号：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="docCode" type="text">
                            </div>
                            <label class="col-sm-2 control-label">是否主动放弃：</label>
                            <div class="col-sm-4">
                                <th:block
                                        th:include="/component/radio :: init(id='iszdfq', name='iszdfq',businessType='KWZL',isfirst='true', dictCode='KWZL_FINISFQ')"></th:block>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row" style="padding: 15px 0;">
            <div class="toolbar toolbar-bottom" role="toolbar">
                <button type="button" class="btn btn-primary"
                        onclick="doReset()"><i
                        class="fa fa-refresh"></i>重置
                </button>
                <button type="button" class="btn btn-primary"
                        onclick="sumbitCK()"><i
                        class="fa fa-check"></i>查询
                </button>
                <button type="button" class="btn btn-danger"
                        onclick="closeItem()">
                    <i class="fa fa-reply-all"></i>返回
                </button>
            </div>
        </div>
    </form>
</div>
<script th:inline="javascript">
    var prefix = ctx + "KWZL/CXGZ";

    function doReset() {
        location.reload();
    }

    function sumbitCK() {
        var formData = $("#form01").serialize();
        $.modal.openTab("综合查询列表", prefix + "/zhcx/KWZLZHCX01?" + formData, true);
    }

    function sws() {
        document.getElementById("swsGuid").onchange = _change;

        function _change() {
            if ($("#swsGuid").val() != '') {
                $.ajax({
                    url: ctx + "KIZL/PA/AB/getSwsDlr/" + $("#swsGuid").val() + "/1",//根据代理所主键，用户类型获取代理人
                    type: "GET",
                    dataType: "json",
                    success: function (result) {
                        $("#swsDlrxm").html("");
                        for (var i = 0; i < result.length; i++) {
                            $("#swsDlrxm").append('<option value="' + result[i].swspersonId + '">' + result[i].userName + '</option>');
                        }
                        $.ajax({
                            url: ctx + "KIZL/PA/AB/getSwsPerson/" + $("#swsDlrxm").val() + "/1",//根据人员编号，用户类型获取代理人信息
                            type: "GET",
                            dataType: "json",
                            success: function (result) {
                                $("#swsdlrPhone").val(result.userLxrPhone);
                            },
                            error: function (jqXHR, textStatus, errorThrown) {
                            }
                        });
                        $.ajax({
                            url: ctx + "KIZL/PA/AB/getSwsDlr/" + $("#swsGuid").val() + "/2",//根据代理所主键，用户类型获取代理人
                            type: "GET",
                            dataType: "json",
                            success: function (result) {
                                $("#swsLxr").html("");
                                for (var i = 0; i < result.length; i++) {
                                    $("#swsLxr").append('<option value="' + result[i].swspersonId + '">' + result[i].userName + '</option>');
                                }
                            },
                            error: function (jqXHR, textStatus, errorThrown) {
                            }
                        });
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                    }
                });
            } else {
                $("#swsDlrxm").html("");
                $("#swsLxr").html("");
            }
        }
    }

    function dlr() {
        document.getElementById("swsDlrxm").onchange = _change;

        function _change() {
            $.ajax({
                url: ctx + "KIZL/PA/AB/getSwsPerson/" + $("#swsDlrxm").val() + "/1",//根据人员编号，用户类型获取代理人信息
                type: "GET",
                dataType: "json",
                success: function (result) {
                    $("#swsdlrPhone").val(result.userLxrPhone);
                },
                error: function (jqXHR, textStatus, errorThrown) {
                }
            });
        }
    }
</script>
</body>
</html>