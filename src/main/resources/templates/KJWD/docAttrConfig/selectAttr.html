<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
<th:block th:include="include :: header('属性选择')" />
<th:block th:include="include :: ztree-css" />
</head>
<style>
body {
	height: auto;
	font-family: "Microsoft YaHei";
}

button {
	font-family: "SimSun", "Helvetica Neue", Helvetica, Arial;
}
</style>
<body class="hold-transition box box-main">
	<input id="treeId" name="treeId" type="hidden" />
	<input id="treeName" name="treeName" type="hidden" />
	<div class="wrapper">
		<div class="treeExpandCollapse" style="float: none;">
			   <label class="check-box">
				        <input type="checkbox" value="1" >展开/折叠</label>
		</div>

	
		<div id="tree" class="ztree ztree-border"></div>
	</div>


	<th:block th:include="include :: baseJs" />
	<th:block th:include="include :: ztree-js" />
	<script th:inline="javascript">
	
	$('.treeExpandCollapse input').on('ifChanged', function(obj){
		var type = $(this).val();
		var checked = obj.currentTarget.checked;
		if (type == 1) {
		    if (checked) {
		        $._tree.expandAll(true);
		    } else {
		        $._tree.expandAll(false);
		    }
		} else if (type == "2") {
		    if (checked) {
		        $._tree.checkAllNodes(true);
		    } else {
		        $._tree.checkAllNodes(false);
		    }
		} else if (type == "3") {
		    if (checked) {
		        $._tree.setting.check.chkboxType = { "Y": "ps", "N": "ps" };
		    } else {
		        $._tree.setting.check.chkboxType = { "Y": "", "N": "" };
		    }
		}
	})
		$(function() {
			var url= ctx + "kjwd/docAttrConfig/attrTreeData?code="+[[${code}]];
			var values=[[${values}]];
			if(values!=null && values!=''){
				url+="&values="+values;
			}
			var topCode=[[${topCode}]];
			if(topCode!=null && topCode!=''){
				url+="&topCode="+topCode;
			}
			var selectedMulti=false;
			var enable=false;
			var options = {
		        url: url,
		        expandLevel: 1,
		        onClick: zOnClick,
		        view:{selectedMulti:selectedMulti},
				check:{enable:enable,chkboxType:  { "Y": "", "N": "" }},
				async:{
					enable: true,
					type: "get",//根据请求类型自己定义
					url: ctx + "kjwd/docAttrConfig/attrTreeData",
					autoParam: ["id=parentCode"],//这个是会自动加上的参数，这里的参数，可以用别名，例如：id=Path,传参的时候就是ids = '1'；但是需要注意的是，这里的参数只支持ztree设置的数据属性，例如我们想传递Path字段，就不能在这里自动匹配了，需要另外写方法了
					otherParam:{"values":function(){return values}},
					dataFilter: function(treeId, parentNode, resData){
						//这里要过滤你的数据，把请求回来的数据组装成你想要的格式,resData就是请求接口返回的数据
						//我们假装这里的数据就是我们自己想要的
						return resData

					}

				},

				onAsyncSuccess : zTreeOnAsyncSuccess
		    };
			$.tree.init(options);
		});
		//点击名称
		function zOnClick(event, treeId, treeNode) {
			//组织编码

		    var treeId = treeNode.code;
		    var treeName = treeNode.name;
		    $("#treeId").val(treeId);
			var name=getFilePath(treeNode);
			var nameList=name.split("/")
			var showName="";
			for(var i=0;i<nameList.length;i++){
					if(nameList[i]!=treeName){
						showName+=nameList[i]+"/";
					}
			}
			showName+=treeName
			var showLevel=[[${showLevel!=null?showLevel:1}]]
			showName=showName.substring(find(showName,"/",showLevel)+1,showName.length);
		    $("#treeName").val(showName);
			if([[${selectType=='M'}]]){
				if(treeNode.checked){
					$._tree.checkNode(treeNode);
				}else{
					$._tree.checkNode(treeNode, true, true);
				}

			}

		}
		function getFilePath(treeObj){
			if(treeObj==null)return "";
			var filename = treeObj.name;
			var pNode = treeObj.getParentNode();
			if(pNode!=null){
				filename = getFilePath(pNode) +"/"+ filename;
			}
			return filename;
		}
		function zTreeOnAsyncSuccess(event, treeId, treeNode, msg) {
			if (!$._tree) {
				alert("error!");
				return
			}
			var nodes = $._tree.getNodes();
			$._tree.expandNode(nodes[0], true);
		}
		//复选框
		function zonCheck(event, treeId, treeNode){
			console.log("1")
		}
		
		function saveCheck(){
			var callback= [[${callback}]]
			if(callback!=null && callback!=''){
				parent.eval(callback+'("'+$("#treeId").val()+'","'+$("#treeName").val()+'")');
			}
		}


		function find(str,cha,num){
			var x=str.indexOf(cha);
			for(var i=0;i<num;i++){
				x=str.indexOf(cha,x+1);
			}
			return x;
		}
	</script>
</body>
</html>
