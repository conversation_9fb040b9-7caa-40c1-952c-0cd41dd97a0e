<html lang="zh" xmlns:th="http://www.thymeleaf.org">

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>宝武碳业知识库</title>
    <th:block th:include="include :: baseJs" />

    <link th:href="@{/bscdkjIndex/css/css.css}" rel="stylesheet" type="text/css"/>
    <link th:href="@{/bscdkjIndex/css/style.css}" rel="stylesheet" type="text/css"/>
    <link th:href="@{/bscdkjIndex/css/iconfont.css}" rel="stylesheet" type="text/css"/>
    <link th:href="@{/css/font-awesome.min.css}" rel="stylesheet" />

    <script type="text/javascript" th:src="@{/bscdkjIndex/js/jquery-1.10.2.js}"></script>

    <script th:src="@{/ruoyi/js/ry-ui.js}"></script>
    <script th:src="@{/ruoyi/js/common.js}"></script>
    <script th:src="@{/ajax/libs/blockUI/jquery.blockUI.js}"></script>
    <script th:src="@{/ajax/libs/layer/layer.min.js?v=20210516}"></script>
    <script th:src="@{/ajax/libs/layui/layui.js?v=20210516}"></script>

    <script th:src="@{/ajax/libs/pager/pager.js}"></script>
    <link rel="stylesheet" th:href="@{/ajax/libs/pager/pager.css}">

    <link rel="stylesheet" th:href="@{/css/bootstrap.min.css}">

    <script type="text/javascript" th:inline="javascript">
        var ctx = [[@{/}]];
    </script>
</head>

<body class="greybg">

<!-- 中间区域 -->
<div class="met-news flex">
    <!--导航-->
    <div class="met-news-bar">
        <div class="navbar-header-title" id="pageTitle">
            技术知识分类</div>
        <div class="sidenav">
            <ul th:with="nodeData=${@KJWDUtil.getNodeList('JSZS')}">
                <li th:each="node : ${nodeData}"><a href="javascript:void(0)" th:utext="${node.attrName}" th:onclick="reloadPageType(this,[[${node.attrCode}]])"></a></li>
            </ul>
        </div>

        <div class="navbar-header-title" id="pageTitle2">
            文档属性分类</div>
        <div class="sidenav">
            <ul th:with="nodeData=${@KJWDUtil.getNodeList('WDSX')}">
                <li th:each="node : ${nodeData}"><a href="javascript:void(0)" th:utext="${node.attrName}" th:onclick="reloadPageType(this,[[${node.attrCode}]])"></a></li>
            </ul>
        </div>

        <div class="navbar-header-title" id="pageTitle3">
            组织知识分类</div>
        <div class="sidenav">
            <ul th:with="nodeData=${@KJWDUtil.getNodeList('ZZZS')}">
                <li th:each="node : ${nodeData}"><a href="javascript:void(0)" th:utext="${node.attrName}" th:onclick="reloadPageType(this,[[${node.attrCode}]])"></a></li>
            </ul>
        </div>

        <div class="navbar-header-title" id="pageTitle4">
            文档类型分类</div>
        <div class="sidenav">
            <ul th:with="nodeData=${@KJWDUtil.getNodeList('WDLX')}">
                <li th:each="node : ${nodeData}"><a href="javascript:void(0)" th:utext="${node.attrName}" th:onclick="reloadPageType(this,[[${node.attrCode}]])"></a></li>
            </ul>
        </div>

    </div>
    <!--导航end-->

    <!--列表-->
    <style>
        .recommend a{
            border-bottom: white;
            float: none;
        }

        .recommend .detailed{
            float: none;
        }
        .recommend .title-box{
            float: none;
        }
    </style>
    <div class="met-news-list">
        <div id="newsList">
            <!--信息-->
            <div class="service_box" id="pageList">
                <!--关键词搜索-->
                <!--一列-->
                <div class="row ">
                    <div class="col-sm-12 form-group">
                        <label class="col-sm-3 control-label"></label>
                        <label class="col-sm-6 control-label">关键词检索：</label>
                    </div>
                </div>
                <div class="row ">
                    <div class="col-sm-12 form-group">
                        <label class="col-sm-3 control-label"></label>
                        <div class="col-sm-6 input-group">
                            <input id="keywordSC" name="keywordSC" class="form-control" type="text" th:value="${keyword}">
                            <span class="input-group-addon" style="background-color: blue; color: white;" onclick="queryKeyword()"><i class="fa fa-search "></i></span>
                        </div>
                    </div>
                </div>

                <div class="page-info fn-clear">
                    <i class="fa fa-home"></i><span>首页</span> <span id="docTypeSpan0">></span> <span id="docTypeSpan"></span>
                    <input id="docType" name="docType" type="hidden">
                </div>

                <div id="docList">

                </div>

                <div class="runway-pager" data-type="pager"></div>

            </div>

            <!--信息end-->
        </div>
    </div>
    <!--列表end-->
    <script th:inline="javascript">
        var prefix = ctx + "kjwd/docFile";

        $(function(){
            var docType = [[${attrCode}]];
            var docTypeName = [[${attrName}]];
            if(!docType){
                docType = '';
                $("#docTypeSpan0").html('');
            }else{
                $("#docTypeSpan0").html('>');
                $("#docTypeSpan").html(docTypeName);
            }
            $("#docType").val(docType);

            reloadPage(1);
        });

        function reloadPageType(node, attrCode) {
            var attrName = $(node).html();
            // alert(attrName + attrCode);
            $("#docTypeSpan0").html('>');
            $("#docTypeSpan").html(attrName);
            $("#docType").val(attrCode);

            $("#keywordSC").val('');

            reloadPage(1);
        }

        function queryKeyword() {
            reloadPage(1);
        }

        function reloadPage(pageNum){

            //查询
            var config = {
                url: prefix + "/mainPageList",
                type: "post",
                data: {"pageNum" : pageNum, "pageSize" : 10, 'attrCode' : $("#docType").val(), 'keyword' : $("#keywordSC").val()},
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $.modal.closeLoading();

                    var rows = result.rows;
                    $("#docList").empty();

                    for(var i = 0; i < rows.length; i++){
                        var row = rows[i];
                        var recordGuid = row.recordGuid;
                        var title = row.title;
                        var authorName = row.authorName;
                        var docDesc = row.docDesc;

                        var html = '<div class="recommend">' +
                            '<div class="title-box" style="text-align: center;height: 50px;color: blue;"><a href="javascript:void(0)" onclick="jumpDetail(\''+recordGuid+'\')">' + title + '</a></div>' +
                            '<div class="detailed"><a href="javascript:void(0)" onclick="jumpDetail(\''+recordGuid+'\')">作者：' + authorName + ' </a></div>' +
                            '<p class="recommend-item">' + docDesc + '</p>' +
                            '</div>'
                        $("#docList").append(html);

                    }

                    $("#docList").append('<div class=" clear"></div>');

                    $(".runway-pager").paging({ pageNo: pageNum,        //需要选中的页码
                        totalPage: Math.ceil(result.total/10),  //总页数
                        totalSize: result.total,  //数据总数
                        callback: function(page) { //每次点击页码执行的回调函数，page为当前选中的页码数字
                            reloadPage(page);
                        } });
                }
            };
            $.ajax(config)
        }

        function jumpDetail(id) {
            $.modal.openTab("知识详情", prefix + "/detail/" + id);
        }
    </script>

</div>


</body>
</html>
