<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增开票信息')"/>

    <th:block th:include="include :: baseJs"/>
    <script th:src="@{/js/jquery.tmpl.js}"></script>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-kbInfo-add" th:object="${TmpktKbInfoEx}">
        <input type="hidden" id="extra2" name="extra2" th:field="*{extra2}">
        <input type="hidden" id="extra5" name="extra5" th:value="*{extra5}">
        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">开票信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">开票来源：</label>
                                <input id="kbinfoId" type="hidden"
                                       th:field="*{kbinfoId}">
                                <div class="col-sm-4">
                                    <input name="kbSource" id="kbSource" class="form-control" type="text"
                                           required="required"
                                           th:field="*{kbSource}">
                                </div>
                                <label class="col-sm-2 control-label is-required">单据类型：</label>
                                <div class="col-sm-4">
                                    <select class="form-control" id="kbinfoType" name="kbinfoType" required
                                            th:with="dictData=${@ktUtil.getdocumentType()}"
                                            th:field="*{kbinfoType}">
                                        <option value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                th:value="${dict.dictCode}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">业务单据号：</label>
                                <div class="col-sm-4">
                                    <input name="kbinfoBillNum" id="kbinfoBillNum" class="form-control" type="text"
                                           readonly="readonly" th:field="*{kbinfoBillNum}">
                                </div>
                                <label class="col-sm-2 control-label is-required">项目号：</label>
                                <div class="col-sm-4">
                                    <input name="kbinfoXmbh" id="kbinfoXmbh" class="form-control" type="text"
                                           th:field="*{kbinfoXmbh}" required style="width: 75%;float: left">
                                    <button type="button" class="btn btn-sm btn-primary"
                                            onclick="queryTab()" style="float: right;align-content: center">
                                        选择项目
                                    </button>
                                </div>
                            </div>

                        </div>


                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目名称：</label>
                                <div class="col-sm-4">
                                    <input name="kbinfoXmmc" id="kbinfoXmmc" class="form-control" type="text"
                                           th:value="*{kbinfoXmmc}" readonly="readonly">
                                </div>
                                <label class="col-sm-2 control-label is-required">合同号：</label>
                                <div class="col-sm-4">
                                    <input name="kbinfoHtbh" id="kbinfoHtbh" class="form-control" type="text"
                                           required
                                           th:value="*{kbinfoHtbh}">
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">客商代码：</label>
                                <div class="col-sm-4">
                                    <input name="kbinfoCustomid" id="kbinfoCustomid" class="form-control"
                                           th:value="*{kbinfoCustomid}"
                                           type="text" style="width: 75%;float: left">
                                    <button type="button" class="btn btn-sm btn-primary"
                                            onclick="chooseKS()" style="float: right;align-content: center">选择客商</button>
                                </div>
                                <label class="col-sm-2 control-label">客户名称：</label>
                                <div class="col-sm-4">
                                    <input name="kbinfoCustomUnitname" id="kbinfoCustomUnitname"
                                           th:value="*{kbinfoCustomUnitname}"
                                           class="form-control" type="text">
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">开户银行：</label>
                                <div class="col-sm-4">
                                    <input name="kbinfoCustomBank" id="kbinfoCustomBank" class="form-control"
                                           th:value="*{kbinfoCustomBank}"
                                           type="text">
                                </div>

                                <label class="col-sm-2 control-label">账号：</label>
                                <div class="col-sm-4">
                                    <input name="kbinfoCustomAccount" id="kbinfoCustomAccount" class="form-control"
                                           th:value="*{kbinfoCustomAccount}"
                                           type="text">
                                </div>
                            </div>


                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">税号：</label>
                                <div class="col-sm-4">
                                    <input name="kbinfoCustomTax" id="kbinfoCustomTax" class="form-control"
                                           th:value="*{kbinfoCustomTax}"
                                           type="text">
                                </div>

                                <label class="col-sm-2 control-label">地址：</label>
                                <div class="col-sm-4">
                                    <input name="kbinfoCustomAddress" id="kbinfoCustomAddress" class="form-control"
                                           th:field="*{kbinfoCustomAddress}"
                                           type="text">
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">电话：</label>
                                <div class="col-sm-4">
                                    <input name="kbinfoCustomUnitphone" id="kbinfoCustomUnitphone"
                                           th:value="*{kbinfoCustomUnitphone}"
                                           class="form-control" type="text">
                                </div>

                                <label class="col-sm-2 control-label">账套：</label>
                                <div class="col-sm-4">
                                    <input id="kbinfoAccountBook" name="kbinfoAccountBook" class="form-control"
                                           type="text" th:value="*{kbinfoAccountBook}" readonly="readonly">
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">责任中心：</label>
                                <div class="col-sm-4">
                                    <input id="kbinfoDutyCenter" name="kbinfoDutyCenter" class="form-control"
                                           type="text" th:value="*{kbinfoDutyCenter}" readonly="readonly">
                                </div>

                                <label class="col-sm-2 control-label is-required">会计期：</label>
                                <div class="col-sm-4">
                                    <input id="accountDate" required th:field="*{accountDate}"
                                           class="form-control" type="date"/>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">费用类型：</label>
                                <div class="col-sm-4">
                                    <input name="kindName" id="kindName" class="form-control" type="hidden"
                                           th:value="*{kindName}">
                                    <select class="form-control" id="kindNameS" name="kindNameS" onchange="fylx()"
                                            required
                                            th:with="dictData=${@ktUtil.getTypesOfFee()}"
                                            th:field="*{kindCode}">
                                        <option value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                th:value="${dict.dictCode}"></option>
                                    </select>
                                </div>

                                <label class="col-sm-2 control-label is-required">产副品代码：</label>
                                <div class="col-sm-4">
                                    <input name="kindCode" id="kindCode" class="form-control" type="text" required
                                           th:field="*{kindCode}">
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">财税种类：</label>
                                <div class="col-sm-4">
                                    <input name="accountBigkind" id="accountBigkind" class="form-control"
                                           th:value="*{accountBigkind}"
                                           type="hidden">
                                    <select class="form-control" id="accountBigkindS" name="accountBigkindS"
                                            required
                                            onchange="cszl()"
                                            th:with="dictData=${@ktUtil.getFinanceTaxation()}"
                                            th:field="*{accountSubkind}">
                                        <option value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                th:value="${dict.dictCode}"></option>
                                    </select>
                                </div>

                                <label class="col-sm-2 control-label">财税编码：</label>
                                <div class="col-sm-4">
                                    <input name="accountSubkind" id="accountSubkind" class="form-control"
                                           th:field="*{accountSubkind}" readonly="readonly"
                                           type="text">
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">结算方式：</label>
                                    <div class="col-sm-4">
                                        <select class="form-control" id="accountNethod" name="accountNethod" required
                                                th:with="dictData=${@ktUtil.getSettlement()}"
                                                th:field="*{accountNethod}">
                                            <option value="">请选择</option>
                                            <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                    th:value="${dict.dictCode}"></option>
                                        </select>
                                    </div>

                                    <label class="col-sm-2 control-label is-required">结算用户类型：</label>
                                    <div class="col-sm-4">
                                        <select class="form-control" id="accountUserType" name="accountUserType"
                                                required
                                                th:with="dictData=${@ktUtil.getUserType()}"
                                                th:field="*{accountUserType}">
                                            <option value="">请选择</option>
                                            <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                    th:value="${dict.dictCode}"></option>
                                        </select>
                                    </div>
                                </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">结算币种：</label>
                                <div class="col-sm-4">
                                    <select class="form-control" id="accountCurrency" name="accountCurrency"
                                            required
                                            th:with="dictData=${@ktUtil.getCurrencyLittle()}"
                                            th:field="*{accountCurrency}">
                                        <option value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                th:value="${dict.dictCode}"></option>
                                    </select>
                                </div>

                                <label class="col-sm-2 control-label is-required">发票种类：</label>
                                <div class="col-sm-4">
                                    <select class="form-control" id="invoiceType" name="invoiceType" required
                                            th:with="dictData=${@ktUtil.getBillTypeLittle()}"
                                            th:field="*{invoiceType}">
                                        <option value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                th:value="${dict.dictCode}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">税率：</label>
                                <div class="col-sm-4">
                                    <select class="form-control" id="rate" name="rate" onchange="calculation()"
                                            required
                                            th:with="dictData=${@ktUtil.getTaxRate()}"
                                            th:field="*{rate}">
                                        <option value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                th:value="${dict.dictCode}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="zbqk" class="panel-collapse collapse in">
                            <div class="panel-body" >
                                <div class="col-sm-12">
                                    <button class="btn btn-white btn-sm" onclick="addSupport()" type="button"><i
                                            class="fa fa-plus"> 增加</i></button>
                                    <button class="btn btn-white btn-sm" onclick="delcolumn()" type="button"><i
                                            class="fa fa-minus"> 删除</i></button>
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-kbinfo">
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">发票金额(含税)：</label>
                                <div class="col-sm-4">
                                    <input name="invoiceAmount" id="invoiceAmount" class="form-control" type="text"
                                           readonly="readonly"
                                           th:field="*{invoiceAmount}">
                                </div>

                                <label class="col-sm-2 control-label">发票税额：</label>
                                <div class="col-sm-4">
                                    <input name="invoiceTax" id="invoiceTax" class="form-control" type="text"
                                           readonly="readonly"
                                           th:field="*{invoiceTax}">
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">不含税金额：</label>
                                <div class="col-sm-4">
                                    <input name="invoiceNoTax" id="invoiceNoTax" class="form-control" type="text"
                                           readonly="readonly"
                                           th:field="*{invoiceNoTax}">
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">摘要：</label>
                                <div class="col-sm-10">
                                     <textarea type="text" name="kbinfoSummary" class="form-control"
                                               th:field="*{kbinfoSummary}"
                                               placeholder="请输入文本"></textarea>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">备注：</label>
                                <div class="col-sm-10">
                                     <textarea type="text" name="kbinfoRemark" class="form-control"
                                               th:field="*{kbinfoRemark}"
                                               placeholder="请输入文本"></textarea>
                                </div>
                            </div>
                        </div>


                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">邮寄标志：</label>
                                <div class="col-sm-4">
                                    <select class="form-control" id="sign" name="sign" onchange="sendByPost()"
                                            required
                                            th:with="dictData=${@ktUtil.getsendByPost()}"
                                            th:field="*{sign}">
                                        <option value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                th:value="${dict.dictCode}"></option>
                                    </select>
                                </div>

                                <label class="col-sm-2 control-label is-required">收票人：</label>
                                <div class="col-sm-4">
                                    <input name="payee" id="payee" class="form-control" required
                                           th:value="*{payee}"
                                           type="text">
                                </div>
                            </div>
                        </div>


                        <div class="row " >
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">手机号：</label>
                                <div class="col-sm-4">
                                    <input name="phone" id="phone" class="form-control isPhone" required
                                           th:value="*{phone}"
                                           type="text">
                                </div>

                                <label class="col-sm-2 control-label is-required">制单人：</label>
                                <div class="col-sm-4">
                                    <input name="settleId" id="settleId" class="form-control" required
                                           th:value="*{settleId}"
                                           type="text">
                                </div>
                            </div>
                        </div>


                        <div class="row " >
                            <div class="form-group" id="address1">
                                <label class="col-sm-2 control-label">收票地址：</label>
                                <div class="col-sm-10">
                                    <input name="address" class="form-control" id="address"
                                           th:value="*{address}"
                                           type="text">
                                </div>
                            </div>

                            <div class="form-group" id="email1">
                                <label class="col-sm-2 control-label">邮箱：</label>
                                <div class="col-sm-10">
                                    <input name="email" id="email" class="form-control email"
                                           th:value="*{email}"
                                           type="text">
                                </div>
                            </div>
                        </div>


                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">附件：</label>
                                <div class="col-sm-10">
                                    <div th:include="include :: layui-upload(display='none',name=*{zzzcConstants.XGFJ_FILE},id=*{zzzcConstants.XGFJ_FILE},sourceId=*{kbinfoId},sourceModule=*{zzzcConstants.BUSINESS_TYPE_KTTG})"></div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row">
    <div class="col-sm-offset-5 col-sm-10">
        <button type="button" class="btn btn-sm btn-primary"
                onclick="temporarily()">
            <i class="fa fa-check"></i>暂 存
        </button>

        <button type="button" class="btn btn-sm btn-primary"
                onclick="submitKp()">
            <i class="fa fa-check"></i>提 交
        </button>
    </div>
</div>
</body>

<script th:inline="javascript">
    var prefix = ctx + "mpkt/kbInfo"

    $(document).ready(function(){	
    	querybkinfo();
	});
    
    $("#form-kbInfo-add").validate({
        focusCleanup: true
    });

    //暂存
    function temporarily() {
        var config = {
            url: prefix + "/addbkinfo",
            type: "post",
            dataType: "json",
            data: $('#form-kbInfo-add').serialize(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                $("#kbinfoId").val(result.data.kbinfoId);
                $("#kbinfoBillNum").val(result.data.kbinfoBillNum);
                $.modal.alertSuccess(result.msg);
                $.modal.closeLoading();
            }
        };
        $.ajax(config)
    }

    //提交
    function submitKp() {
        if ($.validate.form()) {
            $.operate.saveTabAlert(prefix + "/submitbkinfo", $('#form-kbInfo-add').serialize());
        }
    }


    $(function () {
        var kbinfoId = $("#kbinfoId").val();
        var url = "";
        if (kbinfoId != '') {
            url = prefix + "/queryTmpktKbInfoDetail/" + kbinfoId;
        }
        var options = {
            url: url,
            createUrl: prefix + "/choiceProject",
            id: "bootstrap-table-kbinfo",
            modalName: "项目",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", index, index);
                        return columnIndex + $.table.serialNumber(index);
                    }
                }, {
                    field: 'goods',
                    align: 'center',
                    title: '品名',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input required id='goods%s' class='form-control' type='text' name='tmpktKbInfoDetaillist[%s].goods' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'model',
                    align: 'center',
                    title: '规格型号',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input id='model%s' class='form-control' type='text' name='tmpktKbInfoDetaillist[%s].model' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'kbinfoUnit',
                    align: 'center',
                    title: '计量单位',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input required id='kbinfoUnit%s' class='form-control' type='text' name='tmpktKbInfoDetaillist[%s].kbinfoUnit' value='%s'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'kbinfoPrice',
                    align: 'center',
                    title: '单价（元,不含税）',
                    formatter: function (value, row, index) {
                        var id = "kbinfoPrice" + index;
                        var html = $.common.sprintf("<input required id='kbinfoPrice%s' class='form-control' type='number' name='tmpktKbInfoDetaillist[%s].kbinfoPrice' value='%s'  onchange=\"checkIsNumber('%s')\">", index, index, value, id);
                        return html;
                    }
                },
                {
                    field: 'kbinfoCount',
                    align: 'center',
                    title: '数量',
                    formatter: function (value, row, index) {
                        var id = "kbinfoCount" + index;
                        var html = $.common.sprintf("<input required id='kbinfoCount%s' class='form-control' type='number' name='tmpktKbInfoDetaillist[%s].kbinfoCount' value='%s' onchange=\"checkIsNumber('%s')\">", index, index, value, id);
                        return html;
                    }
                },
                {
                    field: 'invoiceNoTax',
                    align: 'center',
                    title: '发票不含税金额（元）',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input required id='invoiceNoTax%s' class='form-control' type='text' name='tmpktKbInfoDetaillist[%s].invoiceNoTax' value='%s' disabled='disabled'>", index, index, value);
                        return html;
                    }
                },
                {
                    field: 'invoiceTax',
                    align: 'center',
                    title: '发票税额（元）',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input required id='invoiceTax%s' class='form-control' type='text' name='tmpktKbInfoDetaillist[%s].invoiceTax' value='%s' disabled='disabled'>", index, index, value);
                        return html;
                    }
                }],
        }
        $.table.init(options);
    })

    function addSupport() {
        var row = {
            goods: "",
            model: "",
            kbinfoUnit: "",
            kbinfoPrice: "",
            kbinfoCount: "",
            invoiceTax: "",
            invoiceNoTax: "",
        }
        sub.addColumn(row, "bootstrap-table-kbinfo");
    }

    function querybkinfo() {
    	var kbinfoHtbh = $("#kbinfoHtbh").val();
        if (kbinfoHtbh.length > 7) {
            var config = {
                url: prefix + "/querybkinfo/KTMY",
                type: "post",
                dataType: "json",
                data: $('#form-kbInfo-add').serialize(),
                success: function (result) {
                    //项目名称
                    $("#kbinfoXmmc").attr("value", result.data.kbinfoXmmc);
                    //合同号
                    $("#kbinfoHtbh").attr("value", result.data.kbinfoHtbh);
                    //客商代码
                    $("#kbinfoCustomid").attr("value", result.data.kbinfoCustomid);
                    //客户名称
                    $("#kbinfoCustomUnitname").attr("value", result.data.kbinfoCustomUnitname);
                    //开户银行
                    $("#kbinfoCustomBank").val(result.data.kbinfoCustomBank);
                    //账号
                    $("#kbinfoCustomAccount").attr("value", result.data.kbinfoCustomAccount);
                    //税号
                    $("#kbinfoCustomTax").attr("value", result.data.kbinfoCustomTax);
                    //电话
                    $("#kbinfoCustomUnitphone").attr("value", result.data.kbinfoCustomUnitphone);
                    //账套
                    $("#kbinfoAccountBook").attr("value", result.data.kbinfoAccountBook);
                    //责任中心
                    $("#kbinfoDutyCenter").attr("value", result.data.kbinfoDutyCenter);
                    //地址
                    $("#kbinfoCustomAddress").attr("value", result.data.kbinfoCustomAddress);

                    /*$.modal.closeLoading();*/
                }
            };
            $.ajax(config)
        } else {
            //$.modal.alertError("请输入格式正确的项目号！");
        }
    }

    //检查输入数据是否为数字
    function checkIsNumber(id) {
        var value = $("#" + id).val();
        var num = id.replace(/[^0-9]/ig, "");
        var reg = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
        if (!reg.test(value)) { //正则验证是否为数字
            $.modal.alertError("当前输入的数字不符合规范，请检查！");
            $("#" + id).val(null);
            return;
        } else {
            if (id.indexOf("kbinfoUnit") == -1) {
                //计算价格
                price(num);
            }
        }
    }

    //计算发票税额、发票不含税额金额
    function price(num) {
        //税率
        var rate = $("#rate").val();
        /*rateS = rateS.replace("%", "");
        rateS = rateS / 100;*/
        //单价
        var kbinfoPrice = $("#kbinfoPrice" + num).val();
        //数量
        var kbinfoCount = $("#kbinfoCount" + num).val();
        //发票金额
        var money = 0;
        //发票税额
        var invoiceTax = 0;
        //发票不含税金额
        var invoiceNoTax = 0;

        if (kbinfoCount != "") {
            //发票金额
            money = (+kbinfoPrice) * (+kbinfoCount);
            //计算税额
            invoiceTax = (+money) * (+rate);
            invoiceNoTax = (+money);
        } else {
            //发票金额
            money = kbinfoPrice;
            //计算税额
            invoiceTax = (+money) * (+rate);
            invoiceNoTax = (+money);
        }
        document.getElementById("invoiceTax" + num).value = invoiceTax;
        document.getElementById("invoiceNoTax" + num).value = invoiceNoTax;
        tariff()
    }

    //计算发票金额、发票税额、不含税金额
    function tariff() {
        var table = document.getElementById("bootstrap-table-kbinfo");
        var rows = table.rows.length;
        //发票金额
        var invoiceAmount = 0;
        //发票税额
        var invoiceTax = 0;
        //不含税金额
        var invoiceNoTax = 0;
        var invoiceNoTaxs = 0;
        for (var i = 0; i < (+rows) - 1; i++) {
            //发票税额
            var invoiceTaxs = $("#invoiceTax" + i).val()
            if (invoiceTaxs == null) {
                continue;
            }
            if (invoiceTaxs != '') {
                invoiceTax = (+invoiceTax) + (+invoiceTaxs);
            }
            //发票不含税金额
            invoiceNoTaxs = $("#invoiceNoTax" + i).val()
            if (invoiceNoTaxs != '') {
                invoiceNoTax = (+invoiceNoTax) + (+invoiceNoTaxs);

            }
        }
        //发票金额
        invoiceAmount = (+invoiceNoTaxs) + (invoiceTax) ;//(+invoiceTax) + (+invoiceNoTax);
        document.getElementById("invoiceAmount").value = invoiceAmount;
        document.getElementById("invoiceTax").value = invoiceTax;
        document.getElementById("invoiceNoTax").value = invoiceNoTax;
    }

    function delcolumn() {
        sub.delColumn();
        tariff();
    }

    function calculation() {
        //税率
        var rate = $("#rate").val();
        /*rateS = rateS.replace("%", "");
        rateS = rateS / 100;*/
        document.getElementById("rate").value = rate;
        //行数
        var table = document.getElementById("bootstrap-table-kbinfo");
        var rows = table.rows.length;
        //发票金额
        var money = 0;
        //发票税额
        var invoiceTax = 0;
        //发票不含税金额
        var invoiceNoTax = 0;

        //发票金额
        var invoiceAmount = 0;
        //发票税额
        var invoice = 0;
        //不含税金额
        var invoiceNo = 0;
        for (var i = 0; i < (+rows) - 1; i++) {
            //单价
            var kbinfoPrice = $("#kbinfoPrice" + i).val();
            if (kbinfoPrice == null) {
                continue;
            }
            //数量
            var kbinfoCount = $("#kbinfoCount" + i).val();
            if (kbinfoCount != "") {
                //发票金额
                money = (+kbinfoPrice) * (+kbinfoCount);
            } else {
                //发票金额
                money = (+kbinfoPrice);
            }
            //计算税额
            invoiceTax = +(+money) * (+rate);
            invoice = (+invoice) + (+invoiceTax);
            //不含税金额
            invoiceNoTax = (+money);
            invoiceNo = (+invoiceNo) + (+invoiceNoTax);

            invoiceAmount = (+invoiceAmount) + (+money) + (+invoiceTax);

            //发票税额
            document.getElementById("invoiceTax" + i).value = invoiceTax;
            //发票不含税金额
            document.getElementById("invoiceNoTax" + i).value = invoiceNoTax;
        }
        document.getElementById("invoiceAmount").value = invoiceAmount;
        document.getElementById("invoiceTax").value = invoice;
        document.getElementById("invoiceNoTax").value = invoiceNo;
    }

    function fylx() {
        var kindName = $("#kindNameS").val();
        document.getElementById("kindCode").value = kindName;

        var myType = document.getElementById("kindNameS");
        var index = myType.selectedIndex;
        document.getElementById("kindName").value = myType.options[index].text;
    }

    function cszl() {
        var accountBigkind = $("#accountBigkindS").val();
        document.getElementById("accountSubkind").value = accountBigkind;

        var myType = document.getElementById("accountBigkindS");
        var index = myType.selectedIndex;
        document.getElementById("accountBigkind").value = myType.options[index].text;
    }

    $(function () {
        var kindName = $("#kindNameS").val();
        document.getElementById("kindCode").value = kindName;

        var myType = document.getElementById("kindNameS");
        var index = myType.selectedIndex;
        document.getElementById("kindName").value = myType.options[index].text;
    })

    function sendByPost() {
        //邮寄方式
        var sbp = $("#sign").val();
        if (sbp == 'Y') { //邮寄
            $("#address1").show();
            $("#email1").hide();
            $(".email").removeAttr("required");
            document.getElementById("address").setAttribute("required","required");
        }else if (sbp == 'E') {//电邮
            $("#address1").hide();
            $("#email1").show();
            $(".address").removeAttr("required");
            document.getElementById("email").setAttribute("required","required");
        }else{
            $("#address1").hide();
            $("#email1").hide();
        }
    }

    $(function () {
        sendByPost();
    })

    function iphone() {
        var phone = $("#phone").val();
        var reg = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
        if (!reg.test(phone)) { //正则验证是否为数字
            $.modal.alertError("当前输入的数字不符合规范，请检查！");
            $("#phone").val(null);
            return;
        }
    }

    function queryTab() {
        var url = ctx + "mpkt/kbInfo/toPage/queryProject?kbSource=KTMY";
        var options = {
            title: '选择项目',
            url: url,
            callBack: callbackSubmit
        };
        $.modal.openOptions(options);
    }

    function callbackSubmit(index, layero){
    	var body = layer.getChildFrame('body', index);
        var kbinfoXmbh = $(body.find("tr[class='selected'] td"))[1].innerHTML;
        var kbinfoHtbh = $(body.find("tr[class='selected'] td"))[2].innerHTML;
        $('#kbinfoXmbh').val(kbinfoXmbh);
        $('#kbinfoHtbh').val(kbinfoHtbh);
        $.modal.closeAll();
        querybkinfo();
    }
    
  	//选择客商
	function chooseKS(){
		var url = ctx + "ktmy/project/toPage/selectKSBank";
        $.modal.open('选择客商', url, "1000", "500", chooseKSCallback);
	}
	//选择客商回调
	function chooseKSCallback(index,layero){
		var rows = layero.find("iframe")[0].contentWindow.submitHandler();
		if(rows != null && rows != ""){
            //客商代码
            $("#kbinfoCustomid").attr("value", rows[0].userNum);
            //客户名称
            $("#kbinfoCustomUnitname").attr("value", rows[0].chineseUserName);
            //开户银行
            $("#kbinfoCustomBank").val(rows[0].bankBranchName);
            //账号
            $("#kbinfoCustomAccount").attr("value", rows[0].accountNum);
            //税号
            $("#kbinfoCustomTax").attr("value", rows[0].taxNum);
            //地址
            $("#kbinfoCustomAddress").attr("value", rows[0].chineseAddress);
		}
		$.modal.closeAll();//关闭遮罩层
	}

</script>
</html>