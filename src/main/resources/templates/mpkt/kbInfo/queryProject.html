<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('项目选择')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>项目编号：</label>
                            <input type="text" name="projectNumLike"/>
                        </li>
                        <li>
                            <label>项目名称：</label>
                            <input type="text" name="projectNameLike"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>


        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<!--查询满足添条件的科研项目信息-->
<script th:inline="javascript">
    $(function () {
    	var kbSource = [[${kbSource}]];
        var options = {
            url: ctx + "mpkt/kbInfo/queryProject/"+kbSource,
            modalName: "选择项目",
            singleSelect:true,
            clickToSelect:true,
            columns: [{
                checkbox: true
            },
                {
                    field: 'skId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'skHtbh',
                    title: '合同编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'xmzgName',
                    title: '项目主管'
                }]
        };
        $.table.init(options);
    });

</script>
</body>
</html>