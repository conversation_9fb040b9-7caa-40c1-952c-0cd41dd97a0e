<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('26.0_01开票信息列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId" th:object="${TmpktKbInfo}">
                <input type="hidden" id="kbSource"   th:field="*{kbSource}">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>合同号：</label>
                            <input type="text" name="kbinfoHtbh"/>
                        </li>
                        <li>
                            <label>项目号：</label>
                            <input type="text" name="kbinfoXmbh"/>
                        </li>
                        <li>
                            <label>项目名称：</label>
                            <input type="text" name="kbinfoXmmc"/>
                        </li>
                        <li>
                            <label>供应商代码：</label>
                            <input type="text" name="kbinfoCustomid"/>
                        </li>
                        <li>
                            <label>客户名称：</label>
                            <input type="text" name="kbinfoCustomUnitnameLike"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.addTab()">
                <i class="fa fa-plus"></i> 添加
            </a>
            <!--<a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                <i class="fa fa-edit"></i> 修改
            </a>
            <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                <i class="fa fa-remove"></i> 删除
            </a>-->
           <!-- <a class="btn btn-warning" onclick="$.table.exportExcel()">
                <i class="fa fa-download"></i> 导出
            </a>-->
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "mpkt/kbInfo";

    $(function () {
        var kbSource = $("#kbSource").val();
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/bkinfoAdd/" + kbSource,
            detailUrl: prefix + "/detail/{id}",
            updateUrl: prefix + "/editquery/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "开票信息",
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'kbinfoHtbh',
                    align: 'center',
                    title: '合同号'
                },

                {
                    field: 'kbinfoXmbh',
                    align: 'center',
                    title: '项目号'
                },
                {
                    field: 'kbinfoXmmc',
                    align: 'center',
                    title: '项目名称'
                },
                {
                    field: 'kbinfoCustomid',
                    align: 'center',
                    title: '供应商代码'
                },
                {
                    field: 'invoiceNoTax',
                    align: 'center',
                    title: '货款'
                },
                {
                    field: 'kbinfoCustomUnitname',
                    align: 'center',
                    title: '客户名称'
                },
                {
                    field: 'kbinfoStatus',
                    align: 'center',
                    title: '开票状态'
                },
                {
                    field: 'accountDate',
                    align: 'center',
                    title: '会计期'
                },
                {
                    field: 'extra4',
                    align: 'center',
                    title: '备注',
                    formatter: function(value, row, index) {
                        return $.table.tooltip(value);
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        //提交状态
                        if (row.extra2 == 'refer' || row.kbinfoStatus == '回撤中') {
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="revoke(\'' + row.kbinfoId + '\')"></i>回撤</a> ');
                        } else if (row.extra2 == 'draft' || row.extra2 == 'fail') {
                            //草稿、失败状态
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.kbinfoId + '\')"></i>编辑</a> ');
                        }else if(row.extra2 == 'success'){
                            //开票成功
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="BillInfo(\'' + row.kbinfoBillNum + '\')"></i>发票</a> ');
                        }
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.kbinfoId + '\')">查看</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });
    //业务单据号
    function BillInfo(kbinfoBillNum){
        var url = prefix + "/billInfo/" + kbinfoBillNum;
        var options = {
            title: '发票信息',
            width: "1000",
            height: '600',
            url: url,
            callBack:closureChoice
        };
        $.modal.openOptions(options);
    }
    function closureChoice(index){
        //关闭页面
        layer.close(index);
    }

   /* function query111(){
        $.modal.openTab("详情页", prefix + "/Send8PBCS1/" + "20211224152349997319488");
    }*/


    /*function queryKbInfo(){
        $.modal.openTab("测试", prefix + "/queryKbInfo/" + "J8P22000015");
    }*/


    function revoke(kbinfoId) {
    	$.modal.confirm("确认回撤吗？", function () {
	        var config = {
	            url: prefix + "/revoke/" + kbinfoId,
	            type: "post",
	            dataType: "json",
	            //data: {},
	            beforeSend: function () {
	                $.modal.loading("正在处理中，请稍后...");
	            },
	            success: function (result) {
	                $.modal.alertSuccess(result.msg);
	                $.modal.closeLoading();
                    $.table.refresh();
	            }
	        };
	        $.ajax(config)
    	});	
    }

</script>
</body>
</html>