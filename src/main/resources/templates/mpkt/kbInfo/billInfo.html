<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('发票信息列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId" th:object="${TmpktBillInfo}">
                <input id="kbinfoBillNum" type="hidden"
                       th:field="*{kbinfoBillNum}">
                <div class="select-list">
                   <ul>
                        <li>
                            <label>发票代码：</label>
                            <input type="text" name="billCode"/>
                        </li>
                        <!--<li>
                            <label>开票金额：</label>
                            <input type="text" name="billAmount"/>
                        </li>-->
                       <!-- <li>
                            <label>开票状态：</label>
                            <select name="billStatus" th:with="type=${@dict.getDictList(null,null)}">
                                <option value="">所有</option>
                                <option th:each="dict : ${type}" th:text="${dict.dictName}"
                                        th:value="${dict.dictValue}"></option>
                            </select>
                        </li>-->
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

       <!-- <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-warning" onclick="$.table.exportExcel()">
                <i class="fa fa-download"></i> 导出
            </a>
        </div>-->
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "mpkt/billInfo";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/edit/{id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "发票信息",
            columns: [{
                checkbox: true
            },
                {
                    field: 'billNum',
                    title: '发票号'
                },
                {
                    field: 'billCode',
                    title: '发票代码'
                },
              /*  {
                    field: 'billAmount',
                    title: '开票金额'
                },*/
                {
                    field: 'billDate',
                    title: '开票日期'
                },
                {
                    field: 'billStatus',
                    title: '开票状态'
                }]
        };
        $.table.init(options);
    });

    options.callBack
</script>
</body>
</html>