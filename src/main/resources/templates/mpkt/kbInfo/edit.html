<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改26.0_01开票信息')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-kbInfo-edit" th:object="${kbInfo}">
         <div class="panel-group" id="accordion" role="tablist"
                             aria-multiselectable="true">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#version"
                                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                                        aria-hidden="true"></i></span>
                                        </a>
                                    </h4>
                                </div>
                                <div id="jbxx" class="panel-collapse collapse in"
                                     aria-expanded="false">
                                    <div class="panel-body">
            <input name="kbinfoId" th:field="*{kbinfoId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">开票来源(技术贸易，推广移植)：</label>
                <div class="col-sm-8">
                    <input name="kbSource" th:field="*{kbSource}" class="form-control" type="text">
                </div>
            </div>

      <div class="form-group" th:include="include :: initSelectBox(id='kbinfoType', name='kbinfoType',businessType=null, dictCode=null, value=${kbInfo.kbinfoType} ,labelName='单据类型')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">业务单据号：</label>
                <div class="col-sm-8">
                    <input name="kbinfoBillNum" th:field="*{kbinfoBillNum}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">合同号：</label>
                <div class="col-sm-8">
                    <input name="kbinfoHtbh" th:field="*{kbinfoHtbh}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">客户代码：</label>
                <div class="col-sm-8">
                    <input name="kbinfoCustomid" th:field="*{kbinfoCustomid}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">客户名称：</label>
                <div class="col-sm-8">
                    <input name="kbinfoCustomUnitname" th:field="*{kbinfoCustomUnitname}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">开户银行：</label>
                <div class="col-sm-8">
                    <input name="kbinfoCustomBank" th:field="*{kbinfoCustomBank}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">帐号：</label>
                <div class="col-sm-8">
                    <input name="kbinfoCustomAccount" th:field="*{kbinfoCustomAccount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">税号：</label>
                <div class="col-sm-8">
                    <input name="kbinfoCustomTax" th:field="*{kbinfoCustomTax}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">地址：</label>
                <div class="col-sm-8">
                    <input name="kbinfoCustomAddress" th:field="*{kbinfoCustomAddress}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">电话：</label>
                <div class="col-sm-8">
                    <input name="kbinfoCustomUnitphone" th:field="*{kbinfoCustomUnitphone}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">项目号：</label>
                <div class="col-sm-8">
                    <input name="kbinfoXmbh" th:field="*{kbinfoXmbh}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">项目名称：</label>
                <div class="col-sm-8">
                    <input name="kbinfoXmmc" th:field="*{kbinfoXmmc}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">帐套：</label>
                <div class="col-sm-8">
                    <input name="kbinfoAccountBook" th:field="*{kbinfoAccountBook}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">责任中心：</label>
                <div class="col-sm-8">
                    <input name="kbinfoDutyCenter" th:field="*{kbinfoDutyCenter}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">会计期：</label>
                <div class="col-sm-8">
                    <input name="accountDate" th:field="*{accountDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">费用类型：</label>
                <div class="col-sm-8">
                    <input name="kindName" th:field="*{kindName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">产副品代码：</label>
                <div class="col-sm-8">
                    <input name="kindCode" th:field="*{kindCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">财税大类：</label>
                <div class="col-sm-8">
                    <input name="accountBigkind" th:field="*{accountBigkind}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">财税明细编码：</label>
                <div class="col-sm-8">
                    <input name="accountSubkind" th:field="*{accountSubkind}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">结算方式：</label>
                <div class="col-sm-8">
                    <input name="accountNethod" th:field="*{accountNethod}" class="form-control" type="text">
                </div>
            </div>

      <div class="form-group" th:include="include :: initSelectBox(id='accountUserType', name='accountUserType',businessType=null, dictCode=null, value=${kbInfo.accountUserType} ,labelName='结算用户类型')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">结算币种：</label>
                <div class="col-sm-8">
                    <input name="accountCurrency" th:field="*{accountCurrency}" class="form-control" type="text">
                </div>
            </div>

      <div class="form-group" th:include="include :: initSelectBox(id='invoiceType', name='invoiceType',businessType=null, dictCode=null, value=${kbInfo.invoiceType} ,labelName='发票类型')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">税率：</label>
                <div class="col-sm-8">
                    <input name="rate" th:field="*{rate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发票金额(含税)：</label>
                <div class="col-sm-8">
                    <input name="invoiceAmount" th:field="*{invoiceAmount}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">发票税额：</label>
                <div class="col-sm-8">
                    <input name="invoiceTax" th:field="*{invoiceTax}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">不含税金额：</label>
                <div class="col-sm-8">
                    <input name="invoiceNoTax" th:field="*{invoiceNoTax}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">摘要：</label>
                <div class="col-sm-8">
                    <input name="kbinfoSummary" th:field="*{kbinfoSummary}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">备注：</label>
                <div class="col-sm-8">
                    <input name="kbinfoRemark" th:field="*{kbinfoRemark}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">交接人工号：</label>
                <div class="col-sm-8">
                    <input name="kbinfoPeopleCode" th:field="*{kbinfoPeopleCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">交接人姓名：</label>
                <div class="col-sm-8">
                    <input name="kbinfoPeopleName" th:field="*{kbinfoPeopleName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">出单号：</label>
                <div class="col-sm-8">
                    <input name="kbinfoNumber" th:field="*{kbinfoNumber}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" th:include="include :: initRadio(id='kbinfoStatus', name='kbinfoStatus',businessType=null, dictCode=null ,value=${kbInfo.kbinfoStatus} ,labelName='开票状态')">
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段3：</label>
                <div class="col-sm-8">
                    <input name="extra3" th:field="*{extra3}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段4：</label>
                <div class="col-sm-8">
                    <input name="extra4" th:field="*{extra4}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段5：</label>
                <div class="col-sm-8">
                    <input name="extra5" th:field="*{extra5}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',businessType=null, dictCode=null ,value=${kbInfo.delStatus} ,labelName='删除状态')">
            </div>
</div>
                    </div>
                </div>
            </div>
        </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>

    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "mpkt/kbInfo";

        $("#form-kbInfo-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-kbInfo-edit').serialize());
            }
        }

    </script>
</body>
</html>