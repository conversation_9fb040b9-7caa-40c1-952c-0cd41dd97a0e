<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('查看开票信息')"/>

    <th:block th:include="include :: baseJs"/>
    <script th:src="@{/js/jquery.tmpl.js}"></script>

    <style>
        #trimSelect {
            /*去除select边框*/
            border: 0;
            background: transparent;
            /*去除下拉框的三角下标*/
            appearance:none;
            -moz-appearance:none; /* Firefox */
            -webkit-appearance:none; /* Safari 和 Chrome */
            color: black;
            text-align: justify;
        }
    </style>

</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-kbInfo-add" th:object="${TmpktKbInfoEx}">
        <div class="panel-group" id="accordion" role="tablist"
             aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version"
                           href="#jbxx" aria-expanded="false" class="collapsed">开票信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                        aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in"
                     aria-expanded="false">
                    <div class="panel-body">

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">开票来源：</label>
                                <input id="kbinfoId" type="hidden"
                                       th:field="*{kbinfoId}">
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbSource}"></label>
                                </div>
                                <label class="col-sm-2 control-label">单据类型：</label>
                                <div class="col-sm-4">
                                    <select style="margin-top: 10px; border: 0;background: transparent;appearance:none; -moz-appearance:none;-webkit-appearance:none;color: black;" id="kbinfoType" name="kbinfoType" disabled
                                            th:with="dictData=${@ktUtil.getdocumentType()}"
                                            th:field="*{kbinfoType}">
                                        <option value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                th:value="${dict.dictCode}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">业务单据号：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoBillNum}"></label>
                                </div>
                                <label class="col-sm-2 control-label">项目号：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoXmbh}"></label>
                                </div>
                            </div>

                        </div>


                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目名称：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoXmmc}"></label>
                                </div>
                                <label class="col-sm-2 control-label">合同号：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoHtbh}"></label>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">客商代码：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoCustomid}"></label>
                                </div>
                                <label class="col-sm-2 control-label">客户名称：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoCustomUnitname}"></label>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">开户银行：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoCustomBank}"></label>
                                </div>

                                <label class="col-sm-2 control-label">账号：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoCustomAccount}"></label>
                                </div>
                            </div>


                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">税号：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoCustomTax}"></label>
                                </div>

                                <label class="col-sm-2 control-label">地址：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoCustomAddress}"></label>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">电话：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoCustomUnitphone}"></label>
                                </div>

                                <label class="col-sm-2 control-label">账套：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoAccountBook}"></label>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">责任中心：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoDutyCenter}"></label>
                                </div>

                                <label class="col-sm-2 control-label">会计期：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{accountDate}"></label>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">费用类型：</label>
                                <div class="col-sm-4">
                                    <input name="kindName" id="kindName" class="form-control" type="hidden"
                                           th:value="*{kindName}">
                                    <select style="margin-top: 10px; border: 0;background: transparent;appearance:none; -moz-appearance:none;-webkit-appearance:none;color: black;" id="kindNameS" name="kindNameS" onchange="fylx()" disabled
                                            required
                                            th:with="dictData=${@ktUtil.getTypesOfFee()}"
                                            th:field="*{kindCode}">
                                        <option value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                th:value="${dict.dictCode}"></option>
                                    </select>
                                </div>

                                <label class="col-sm-2 control-label">产副品代码：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kindCode}"></label>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">财税种类：</label>
                                <div class="col-sm-4">
                                    <input name="accountBigkind" id="accountBigkind" class="form-control"
                                           th:value="*{accountBigkind}"
                                           type="hidden">
                                    <select style="margin-top: 10px; border: 0;background: transparent;appearance:none; -moz-appearance:none;-webkit-appearance:none;color: black;" id="accountBigkindS" name="accountBigkindS" disabled
                                            required
                                            onchange="cszl()"
                                            th:with="dictData=${@ktUtil.getFinanceTaxation()}"
                                            th:field="*{accountSubkind}">
                                        <option value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                th:value="${dict.dictCode}"></option>
                                    </select>
                                </div>

                                <label class="col-sm-2 control-label">财税编码：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{accountSubkind}"></label>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">结算方式：</label>
                                    <div class="col-sm-4">
                                       <select style="margin-top: 10px; border: 0;background: transparent;appearance:none; -moz-appearance:none;-webkit-appearance:none;color: black;" id="accountNethod" name="accountNethod" disabled
                                                th:with="dictData=${@ktUtil.getSettlement()}"
                                                th:field="*{accountNethod}">
                                            <option value="">请选择</option>
                                            <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                    th:value="${dict.dictCode}"></option>
                                        </select>
                                    </div>

                                    <label class="col-sm-2 control-label">结算用户类型：</label>
                                    <div class="col-sm-4">
                                        <!--<div th:include="/component/select :: init(id='accountUserType', name='accountUserType',businessType='KYGJ', value=*{accountUserType},dictCode='userType' ,see=true)"></div>-->
                                         <select  style="margin-top: 10px; border: 0;background: transparent;appearance:none; -moz-appearance:none;-webkit-appearance:none;color: black;" name="accountUserType" disabled
                                                th:with="dictData=${@ktUtil.getUserType()}"
                                                th:field="*{accountUserType}">
                                            <option value="">请选择</option>
                                            <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                    th:value="${dict.dictCode}"></option>
                                        </select>
                                    </div>
                                </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">结算币种：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/radio :: init(id='accountCurrency', name='accountCurrency',businessType='KYGJ', value=*{accountCurrency},dictCode='currency' ,see=true)"></div>
                                </div>

                                <label class="col-sm-2 control-label">发票种类：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/radio :: init(id='invoiceType', name='invoiceType',businessType='KYWX', value=*{invoiceType},dictCode='billType' ,see=true)"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">税率：</label>
                                <div class="col-sm-4">
                                    <select style="margin-top: 10px; border: 0;background: transparent;appearance:none; -moz-appearance:none;-webkit-appearance:none;color: black;" id="rate" name="rate" onchange="calculation()" disabled
                                            required
                                            th:with="dictData=${@ktUtil.getTaxRate()}"
                                            th:field="*{rate}">
                                        <option value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                th:value="${dict.dictCode}"></option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <div id="zbqk" class="panel-collapse collapse in">
                            <div class="panel-body" >
                                <div class="col-sm-12">
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-kbinfo">
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">发票金额(含税)：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{invoiceAmount}"></label>
                                </div>

                                <label class="col-sm-2 control-label">发票税额：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{invoiceTax}"></label>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">不含税金额：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{invoiceNoTax}"></label>
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">摘要：</label>
                                <div class="col-sm-10">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoSummary}"></label>
                                    <!-- <textarea type="text" name="kbinfoSummary" class="form-control"
                                               th:field="*{kbinfoSummary}"
                                               placeholder="请输入文本"></textarea>-->
                                </div>
                            </div>
                        </div>

                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">备注：</label>
                                <div class="col-sm-10">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{kbinfoRemark}"></label>
                                     <!--<textarea type="text" name="kbinfoRemark" class="form-control"
                                               th:field="*{kbinfoRemark}"
                                               placeholder="请输入文本"></textarea>-->
                                </div>
                            </div>
                        </div>


                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">邮寄标志：</label>
                                <div class="col-sm-4">
                                    <select style="margin-top: 10px; border: 0;background: transparent;appearance:none; -moz-appearance:none;-webkit-appearance:none;color: black;" id="sign" name="sign" onchange="sendByPost()" disabled
                                            required
                                            th:with="dictData=${@ktUtil.getsendByPost()}"
                                            th:field="*{sign}">
                                        <option value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}"
                                                th:value="${dict.dictCode}"></option>
                                    </select>
                                </div>

                                <label class="col-sm-2 control-label">收票人：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{payee}"></label>
                                </div>
                            </div>
                        </div>


                        <div class="row " >
                            <div class="form-group">
                                <label class="col-sm-2 control-label">手机号：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{phone}"></label>
                                </div>

                                <label class="col-sm-2 control-label">制单人：</label>
                                <div class="col-sm-4">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{settleId}"></label>
                                </div>
                            </div>
                        </div>


                        <div class="row " >
                            <div class="form-group" id="address1">
                                <label class="col-sm-2 control-label">收票地址：</label>
                                <div class="col-sm-10">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{address}"></label>
                                </div>
                            </div>

                            <div class="form-group" id="email1">
                                <label class="col-sm-2 control-label">邮箱：</label>
                                <div class="col-sm-10">
                                    <label class=" col-sm-9 form-control-static" th:utext="*{email}"></label>
                                </div>
                            </div>
                        </div>


                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">附件：</label>
                                <div class="col-sm-10">
                                    <div th:include="include :: layui-upload(display='none',name=*{zzzcConstants.XGFJ_FILE},id=*{zzzcConstants.XGFJ_FILE},sourceId=*{kbinfoId},sourceModule=*{zzzcConstants.BUSINESS_TYPE_KTTG},see=true)"></div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </form>
</div>

<div style="height: 30px;"></div>

<div class="row">
        <div class="toolbar toolbar-bottom">
            <button type="button" class="btn btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返 回
            </button>
        </div>
</div>

</body>

<script th:inline="javascript">
    var prefix = ctx + "mpkt/kbInfo"

    $("#form-kbInfo-add").validate({
        focusCleanup: true
    });

    //暂存
    function temporarily() {
        var config = {
            url: prefix + "/addbkinfo",
            type: "post",
            dataType: "json",
            data: $('#form-kbInfo-add').serialize(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                $("#kbinfoId").val(result.data.kbinfoId);
                $("#kbinfoBillNum").val(result.data.kbinfoBillNum);
                $.modal.alertSuccess(result.msg);
                $.modal.closeLoading();
            }
        };
        $.ajax(config)
    }

    //提交
    function submitHandler() {
        if ($.validate.form()) {
            $.operate.saveTabAlert(prefix + "/submitbkinfo", $('#form-kbInfo-add').serialize());
        }
    }


    $(function () {
        var kbinfoId = $("#kbinfoId").val();
        var url = "";
        if (kbinfoId != '') {
            url = prefix + "/queryTmpktKbInfoDetail/" + kbinfoId;
        }
        var options = {
            url: url,
            createUrl: prefix + "/choiceProject",
            id: "bootstrap-table-kbinfo",
            modalName: "项目",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            columns: [
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='index' value='%s'>", index, index);
                        return columnIndex + $.table.serialNumber(index);
                    }
                }, {
                    field: 'goods',
                    align: 'center',
                    title: '品名',
                },
                {
                    field: 'model',
                    align: 'center',
                    title: '规格型号',
                },
                {
                    field: 'kbinfoUnit',
                    align: 'center',
                    title: '计量单位',
                },
                {
                    field: 'kbinfoPrice',
                    align: 'center',
                    title: '单价（元,不含税）',
                },
                {
                    field: 'kbinfoCount',
                    align: 'center',
                    title: '数量',
                },
                {
                    field: 'invoiceNoTax',
                    align: 'center',
                    title: '发票不含税金额（元）',
                },
                {
                    field: 'invoiceTax',
                    align: 'center',
                    title: '发票税额（元）',
                }],
        }
        $.table.init(options);
    })

    function addSupport() {
        var row = {
            goods: "",
            model: "",
            kbinfoUnit: "",
            kbinfoPrice: "",
            kbinfoCount: "",
            invoiceTax: "",
            invoiceNoTax: "",
        }
        sub.addColumn(row, "bootstrap-table-kbinfo");
    }

    function querybkinfo() {

        var kbinfoXmbh = $("#kbinfoXmbh").val();
        if (kbinfoXmbh.length > 7) {
            var config = {
                url: prefix + "/querybkinfoTwo",
                type: "post",
                dataType: "json",
                data: $('#form-kbInfo-add').serialize(),
                success: function (result) {
                    //项目名称
                    $("#kbinfoXmmc").attr("value", result.data.kbinfoXmmc);
                    //合同号
                    $("#kbinfoHtbh").attr("value", result.data.kbinfoHtbh);
                    //客商代码
                    $("#kbinfoCustomid").attr("value", result.data.kbinfoCustomid);
                    //客户名称
                    $("#kbinfoCustomUnitname").attr("value", result.data.kbinfoCustomUnitname);
                    //开户银行
                    $("#kbinfoCustomBank").val(result.data.kbinfoCustomBank);
                    //账号
                    $("#kbinfoCustomAccount").attr("value", result.data.kbinfoCustomAccount);
                    //税号
                    $("#kbinfoCustomTax").attr("value", result.data.kbinfoCustomTax);
                    //电话
                    $("#kbinfoCustomUnitphone").attr("value", result.data.kbinfoCustomUnitphone);
                    //账套
                    $("#kbinfoAccountBook").attr("value", result.data.kbinfoAccountBook);
                    //责任中心
                    $("#kbinfoDutyCenter").attr("value", result.data.kbinfoDutyCenter);
                    //地址
                    $("#kbinfoCustomAddress").attr("value", result.data.kbinfoCustomAddress);

                    /*$.modal.closeLoading();*/
                }
            };
            $.ajax(config)
        } else {
            //$.modal.alertError("请输入格式正确的项目号！");
        }
    }

    //检查输入数据是否为数字
    function checkIsNumber(id) {
        var value = $("#" + id).val();
        var num = id.replace(/[^0-9]/ig, "");
        var reg = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
        if (!reg.test(value)) { //正则验证是否为数字
            $.modal.alertError("当前输入的数字不符合规范，请检查！");
            $("#" + id).val(null);
            return;
        } else {
            if (id.indexOf("kbinfoUnit") == -1) {
                //计算价格
                price(num);
            }
        }
    }

    //计算发票税额、发票不含税额金额
    function price(num) {
        //税率
        var rate = $("#rate").val();
        /*rateS = rateS.replace("%", "");
        rateS = rateS / 100;*/
        //单价
        var kbinfoPrice = $("#kbinfoPrice" + num).val();
        //数量
        var kbinfoCount = $("#kbinfoCount" + num).val();
        //发票金额
        var money = 0;
        //发票税额
        var invoiceTax = 0;
        //发票不含税金额
        var invoiceNoTax = 0;

        if (kbinfoCount != "") {
            //发票金额
            money = (+kbinfoPrice) * (+kbinfoCount);
            //计算税额
            invoiceTax = (+money) * (+rate);
            invoiceNoTax = (+money);
        } else {
            //发票金额
            money = kbinfoPrice;
            //计算税额
            invoiceTax = (+money) * (+rate);
            invoiceNoTax = (+money);
        }
        document.getElementById("invoiceTax" + num).value = invoiceTax;
        document.getElementById("invoiceNoTax" + num).value = invoiceNoTax;
        tariff()
    }

    //计算发票金额、发票税额、不含税金额
    function tariff() {
        var table = document.getElementById("bootstrap-table-kbinfo");
        var rows = table.rows.length;
        //发票金额
        var invoiceAmount = 0;
        //发票税额
        var invoiceTax = 0;
        //不含税金额
        var invoiceNoTax = 0;
        var invoiceNoTaxs = 0;
        for (var i = 0; i < (+rows) - 1; i++) {
            //发票税额
            var invoiceTaxs = $("#invoiceTax" + i).val()
            if (invoiceTaxs == null) {
                continue;
            }
            if (invoiceTaxs != '') {
                invoiceTax = (+invoiceTax) + (+invoiceTaxs);
            }
            //发票不含税金额
            invoiceNoTaxs = $("#invoiceNoTax" + i).val()
            if (invoiceNoTaxs != '') {
                invoiceNoTax = (+invoiceNoTax) + (+invoiceNoTaxs);

            }
        }
        //发票金额
        invoiceAmount = (+invoiceNoTaxs) + (invoiceTax) ;//(+invoiceTax) + (+invoiceNoTax);
        document.getElementById("invoiceAmount").value = invoiceAmount;
        document.getElementById("invoiceTax").value = invoiceTax;
        document.getElementById("invoiceNoTax").value = invoiceNoTax;
    }

    function delcolumn() {
        sub.delColumn();
        tariff();
    }

    function calculation() {
        //税率
        var rate = $("#rate").val();
        /*rateS = rateS.replace("%", "");
        rateS = rateS / 100;*/
        document.getElementById("rate").value = rate;
        //行数
        var table = document.getElementById("bootstrap-table-kbinfo");
        var rows = table.rows.length;
        //发票金额
        var money = 0;
        //发票税额
        var invoiceTax = 0;
        //发票不含税金额
        var invoiceNoTax = 0;

        //发票金额
        var invoiceAmount = 0;
        //发票税额
        var invoice = 0;
        //不含税金额
        var invoiceNo = 0;
        for (var i = 0; i < (+rows) - 1; i++) {
            //单价
            var kbinfoPrice = $("#kbinfoPrice" + i).val();
            if (kbinfoPrice == null) {
                continue;
            }
            //数量
            var kbinfoCount = $("#kbinfoCount" + i).val();
            if (kbinfoCount != "") {
                //发票金额
                money = (+kbinfoPrice) * (+kbinfoCount);
            } else {
                //发票金额
                money = (+kbinfoPrice);
            }
            //计算税额
            invoiceTax = +(+money) * (+rate);
            invoice = (+invoice) + (+invoiceTax);
            //不含税金额
            invoiceNoTax = (+money);
            invoiceNo = (+invoiceNo) + (+invoiceNoTax);

            invoiceAmount = (+invoiceAmount) + (+money) + (+invoiceTax);

            //发票税额
            document.getElementById("invoiceTax" + i).value = invoiceTax;
            //发票不含税金额
            document.getElementById("invoiceNoTax" + i).value = invoiceNoTax;
        }
        document.getElementById("invoiceAmount").value = invoiceAmount;
        document.getElementById("invoiceTax").value = invoice;
        document.getElementById("invoiceNoTax").value = invoiceNo;
    }

    function fylx() {
        var kindName = $("#kindNameS").val();
        document.getElementById("kindCode").value = kindName;

        var myType = document.getElementById("kindNameS");
        var index = myType.selectedIndex;
        document.getElementById("kindName").value = myType.options[index].text;
    }

    function cszl() {
        var accountBigkind = $("#accountBigkindS").val();
        document.getElementById("accountSubkind").value = accountBigkind;

        var myType = document.getElementById("accountBigkindS");
        var index = myType.selectedIndex;
        document.getElementById("accountBigkind").value = myType.options[index].text;
    }

    $(function () {
        var kindName = $("#kindNameS").val();
        document.getElementById("kindCode").value = kindName;

        var myType = document.getElementById("kindNameS");
        var index = myType.selectedIndex;
        document.getElementById("kindName").value = myType.options[index].text;
    })

    function sendByPost() {
        //邮寄方式
        var sbp = $("#sign").val();
        if (sbp == 'Y') { //邮寄
            $("#address1").show();
            $("#email1").hide();
            $(".email").removeAttr("required");
            document.getElementById("address").setAttribute("required","required");
        }else if (sbp == 'E') {//电邮
            $("#address1").hide();
            $("#email1").show();
            $(".address").removeAttr("required");
            document.getElementById("email").setAttribute("required","required");
        }else{
            $("#address1").hide();
            $("#email1").hide();
        }
    }

    $(function () {
        sendByPost();
    })

    function iphone() {
        var phone = $("#phone").val();
        var reg = /^[+]?(0|([1-9]\d*))(\.\d+)?$/g;
        if (!reg.test(phone)) { //正则验证是否为数字
            $.modal.alertError("当前输入的数字不符合规范，请检查！");
            $("#phone").val(null);
            return;
        }
    }

    function queryTab() {
        var url = ctx + "mpkt/kbInfo/choiceProject";
        var options = {
            title: '选择项目',
            url: url,
            callBack: callbackSubmit
        };
        $.modal.openOptions(options);
    }

    function callbackSubmit(index, layero){
        var body = layer.getChildFrame('body', index);
        var id = $(body.find("tr[class='selected'] td"))[1].innerHTML;
        $('#kbinfoXmbh').val(id);
        $.modal.closeAll();
        querybkinfo();
    }

</script>
</html>