<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改26.0_03收款信息')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-receiptInfo-edit" th:object="${receiptInfo}">
         <div class="panel-group" id="accordion" role="tablist"
                             aria-multiselectable="true">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#version"
                                           href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                                        aria-hidden="true"></i></span>
                                        </a>
                                    </h4>
                                </div>
                                <div id="jbxx" class="panel-collapse collapse in"
                                     aria-expanded="false">
                                    <div class="panel-body">
            <input name="receiptId" th:field="*{receiptId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">收款收条号：</label>
                <div class="col-sm-8">
                    <input name="receiptCode" th:field="*{receiptCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">子收条号：</label>
                <div class="col-sm-8">
                    <input name="subReceiptCode" th:field="*{subReceiptCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">公司代码：</label>
                <div class="col-sm-8">
                    <input name="companyCode" th:field="*{companyCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">财务责任中心：</label>
                <div class="col-sm-8">
                    <input name="finaCostCenter" th:field="*{finaCostCenter}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">票据号：</label>
                <div class="col-sm-8">
                    <input name="paperNo" th:field="*{paperNo}" class="form-control" type="text">
                </div>
            </div>

      <div class="form-group" th:include="include :: initSelectBox(id='userType', name='userType',businessType=null, dictCode=null, value=${receiptInfo.userType} ,labelName='用户类型')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">结算用户代码：</label>
                <div class="col-sm-8">
                    <input name="settleUserCode" th:field="*{settleUserCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">结算用户名称：</label>
                <div class="col-sm-8">
                    <input name="settleUserName" th:field="*{settleUserName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">出票用户代码：</label>
                <div class="col-sm-8">
                    <input name="builtUserCode" th:field="*{builtUserCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">出票用户名称：</label>
                <div class="col-sm-8">
                    <input name="builtUserName" th:field="*{builtUserName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">票据帐号：</label>
                <div class="col-sm-8">
                    <input name="paperAcct" th:field="*{paperAcct}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">出票银行简称：</label>
                <div class="col-sm-8">
                    <input name="builtBankAbbr" th:field="*{builtBankAbbr}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">银行代码：</label>
                <div class="col-sm-8">
                    <input name="bankCode" th:field="*{bankCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">银行名称：</label>
                <div class="col-sm-8">
                    <input name="bankName" th:field="*{bankName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">票面金额：</label>
                <div class="col-sm-8">
                    <input name="paperAmt" th:field="*{paperAmt}" class="form-control" type="text">
                </div>
            </div>

      <div class="form-group" th:include="include :: initSelectBox(id='expenseType', name='expenseType',businessType=null, dictCode=null, value=${receiptInfo.expenseType} ,labelName='费用类型')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">折扣金额：</label>
                <div class="col-sm-8">
                    <input name="rebateAmt" th:field="*{rebateAmt}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">总金额：</label>
                <div class="col-sm-8">
                    <input name="totalAmt" th:field="*{totalAmt}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">子收条收款收条金额：</label>
                <div class="col-sm-8">
                    <input name="receiptAmt" th:field="*{receiptAmt}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">到期日：</label>
                <div class="col-sm-8">
                    <input name="endDate" th:field="*{endDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">收票日期：</label>
                <div class="col-sm-8">
                    <input name="receiveDate" th:field="*{receiveDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">出票日期：</label>
                <div class="col-sm-8">
                    <input name="builtDate" th:field="*{builtDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">收款方式代码：</label>
                <div class="col-sm-8">
                    <input name="incomeTypeCode" th:field="*{incomeTypeCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">收款方式名称：</label>
                <div class="col-sm-8">
                    <input name="incomeTypeName" th:field="*{incomeTypeName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">收款性质代码：</label>
                <div class="col-sm-8">
                    <input name="incomeKindCode" th:field="*{incomeKindCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">收款性质名称：</label>
                <div class="col-sm-8">
                    <input name="incomeKindName" th:field="*{incomeKindName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">责任中心：</label>
                <div class="col-sm-8">
                    <input name="costCenter" th:field="*{costCenter}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">录入人工号：</label>
                <div class="col-sm-8">
                    <input name="inputId" th:field="*{inputId}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">录入日期：</label>
                <div class="col-sm-8">
                    <input name="inputDate" th:field="*{inputDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">票据备注：</label>
                <div class="col-sm-8">
                    <input name="paperRemark" th:field="*{paperRemark}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">应收业务大类代码：</label>
                <div class="col-sm-8">
                    <input name="asBusiMainCode" th:field="*{asBusiMainCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">应收业务细类代码：</label>
                <div class="col-sm-8">
                    <input name="asBusiDetlCode" th:field="*{asBusiDetlCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">币种代码：</label>
                <div class="col-sm-8">
                    <input name="currencyCode" th:field="*{currencyCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">凭证号码：</label>
                <div class="col-sm-8">
                    <input name="voucherNo" th:field="*{voucherNo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">凭证日期：</label>
                <div class="col-sm-8">
                    <input name="voucherDate" th:field="*{voucherDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">记录创建者：</label>
                <div class="col-sm-8">
                    <input name="recCreator" th:field="*{recCreator}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">记录创建时间：</label>
                <div class="col-sm-8">
                    <input name="recCreateTime" th:field="*{recCreateTime}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">项目编号：</label>
                <div class="col-sm-8">
                    <input name="projectNo" th:field="*{projectNo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">合同号：</label>
                <div class="col-sm-8">
                    <input name="orderNo" th:field="*{orderNo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">核销标记：</label>
                <div class="col-sm-8">
                    <input name="apportFlag" th:field="*{apportFlag}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">瑕疵标记：</label>
                <div class="col-sm-8">
                    <input name="flawFlag" th:field="*{flawFlag}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" th:include="include :: initRadio(id='cancelStatus', name='cancelStatus',businessType=null, dictCode=null ,value=${receiptInfo.cancelStatus} ,labelName='核销状态')">
            </div>

      <div class="form-group" th:include="include :: initSelectBox(id='cancelType', name='cancelType',businessType=null, dictCode=null, value=${receiptInfo.cancelType} ,labelName='核销类型')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">核销金额总计：</label>
                <div class="col-sm-8">
                    <input name="cancelTotal" th:field="*{cancelTotal}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">核销人工号：</label>
                <div class="col-sm-8">
                    <input name="cancelUserCode" th:field="*{cancelUserCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">核销人姓名：</label>
                <div class="col-sm-8">
                    <input name="cancelUserName" th:field="*{cancelUserName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">核销日期：</label>
                <div class="col-sm-8">
                    <input name="cancelDate" th:field="*{cancelDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段1：</label>
                <div class="col-sm-8">
                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段2：</label>
                <div class="col-sm-8">
                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段3：</label>
                <div class="col-sm-8">
                    <input name="extra3" th:field="*{extra3}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段4：</label>
                <div class="col-sm-8">
                    <input name="extra4" th:field="*{extra4}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">扩展字段5：</label>
                <div class="col-sm-8">
                    <input name="extra5" th:field="*{extra5}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',businessType=null, dictCode=null ,value=${receiptInfo.delStatus} ,labelName='删除状态')">
            </div>
</div>
                    </div>
                </div>
            </div>
        </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>

    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "mpkt/receiptInfo";

        $("#form-receiptInfo-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-receiptInfo-edit').serialize());
            }
        }

    </script>
</body>
</html>