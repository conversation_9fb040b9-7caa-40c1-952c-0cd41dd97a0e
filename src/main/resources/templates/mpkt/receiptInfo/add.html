<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增26.0_03收款信息')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-receiptInfo-add">
          <div class="panel-group" id="accordion" role="tablist"
                     aria-multiselectable="true">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h4 class="panel-title">
                                <a data-toggle="collapse" data-parent="#version"
                                   href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                                    &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                    <span class="pull-right"><i class="fa fa-chevron-down"
                                                                aria-hidden="true"></i></span>
                                </a>
                            </h4>
                        </div>
                        <div id="jbxx" class="panel-collapse collapse in"
                             aria-expanded="false">
                            <div class="panel-body">
                                <input name="cancelStatus" value="2" type="hidden">
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">收款收条号：</label>
                                        <div class="col-sm-4">
                                            <input name="receiptCode" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">子收条号：</label>
                                        <div class="col-sm-4">
                                            <input name="subReceiptCode" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">公司代码（账套）：</label>
                                        <div class="col-sm-4">
                                            <input name="companyCode" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">财务责任中心：</label>
                                        <div class="col-sm-4">
                                            <input name="finaCostCenter" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">项目号：</label>
                                        <div class="col-sm-4">
                                            <input name="projectNo" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">合同号：</label>
                                        <div class="col-sm-4">
                                            <input name="orderNo" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">票据号：</label>
                                        <div class="col-sm-4">
                                            <input name="paperNo" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">用户类型：</label>
                                        <div class="col-sm-4">
                                            <div th:include="/component/radio :: init(id='userType',name='userType',
                    businessType='',dictCode='userType',see=true)"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">结算用户代码：</label>
                                        <div class="col-sm-4">
                                            <input name="settleUserCode"  class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">结算用户名称：</label>
                                        <div class="col-sm-4">
                                            <input name="settleUserName" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">出票用户代码：</label>
                                        <div class="col-sm-4">
                                            <input name="builtUserCode" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">出票用户名称：</label>
                                        <div class="col-sm-4">
                                            <input name="builtUserName" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">票据帐号：</label>
                                        <div class="col-sm-4">
                                            <input name="paperAcct" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">出票银行简称：</label>
                                        <div class="col-sm-4">
                                            <input name="builtBankAbbr" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">银行代码：</label>
                                        <div class="col-sm-4">
                                            <input name="bankCode" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">银行名称：</label>
                                        <div class="col-sm-4">
                                            <input name="bankName" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">票面金额：</label>
                                        <div class="col-sm-4">
                                            <input name="paperAmt" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">折扣金额：</label>
                                        <div class="col-sm-4">
                                            <input name="rebateAmt" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>


                                <!--<div class="form-group" th:include="include :: initSelectBox(id='expenseType', name='expenseType',businessType=null, dictCode=null, value=${receiptInfo.expenseType} ,labelName='费用类型')">
                                 </div>-->
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">总金额：</label>
                                        <div class="col-sm-4">
                                            <input name="totalAmt" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">子收条收款收条金额：</label>
                                        <div class="col-sm-4">
                                            <input name="receiptAmt" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">到期日：</label>
                                        <div class="col-sm-4">
                                            <input name="endDate" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">收票日期：</label>
                                        <div class="col-sm-4">
                                            <input name="receiveDate" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">出票日期：</label>
                                        <div class="col-sm-4">
                                            <input name="builtDate" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">收款方式名称：</label>
                                        <div class="col-sm-4">
                                            <input name="incomeTypeName" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">收款性质名称：</label>
                                        <div class="col-sm-4">
                                            <input name="incomeKindName" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">责任中心：</label>
                                        <div class="col-sm-4">
                                            <input name="costCenter" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">录入人工号：</label>
                                        <div class="col-sm-4">
                                            <input name="inputId"  class="form-control" type="text">
                                        </div> <label class="col-sm-2 control-label">录入日期：</label>
                                        <div class="col-sm-4">
                                            <input name="inputDate" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">票据备注：</label>
                                        <div class="col-sm-8">
                                            <input name="paperRemark"  class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">应收业务大类代码：</label>
                                        <div class="col-sm-4">
                                            <input name="asBusiMainCode" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">应收业务细类代码：</label>
                                        <div class="col-sm-4">
                                            <input name="asBusiDetlCode" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="form-group">
                                        <label class="col-sm-2 control-label">币种代码：</label>
                                        <div class="col-sm-4">
                                            <input name="currencyCode" class="form-control" type="text">
                                        </div>
                                        <label class="col-sm-2 control-label">凭证号码：</label>
                                        <div class="col-sm-4">
                                            <input name="voucherNo" class="form-control" type="text">
                                        </div>
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label class="col-sm-2 control-label">凭证日期：</label>
                                    <div class="col-sm-8">
                                        <input name="voucherDate"  class="form-control" type="text">
                                    </div>
                                </div>
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">是否核销：</label>
                                    <div class="col-sm-4">
                                        <div th:include="/component/radio :: init(id='apportFlag',name='apportFlag',
                                                businessType='MPTY',dictCode='is_yes_no')"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">核销类型：</label>
                                    <div class="col-sm-4">
                                        <div th:include="/component/select :: init(id='cancelType',name='cancelType',
                        businessType='KJFY',dictCode='cancelType',isfirst=true)"></div>
                                    </div>
                                </div>
                </div>
                    </div>
                </div>
            </div>
        </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>


    <script th:inline="javascript">
        var prefix = ctx + "mpkt/receiptInfo"

        $("#form-receiptInfo-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/doStart", $('#form-receiptInfo-add').serialize());
            }
        }

    </script>
</body>
</html>