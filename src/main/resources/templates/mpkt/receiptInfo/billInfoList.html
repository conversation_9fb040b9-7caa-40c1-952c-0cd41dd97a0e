<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('已开票发票列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <input id="searchNo" type="hidden">
                <input id="kbinfoAccountBook" type="hidden" th:value="${kbinfoAccountBook}"/>
                <input id="kbinfoCustomid" type="hidden" th:value="${kbinfoCustomid}"/>
                <input name="flowStatus" id="flowStatus" value="end" type="hidden">
                <div class="select-list">
                    <ul>
                        <li>
                            <label>业务单据号：</label>
                            <input type="text" name="kbinfoBillNum"/>
                        </li>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "mpkt/kbInfo";

    $(function() {
        var options = {
            url: prefix + "/yiKaiList",
            detailUrl: prefix + "/queryDT?id={id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/export",
            modalName: "发票列表",
            queryParams:queryParams,
            onCheck: onCheck,
            onUncheck: onUncheck,
            singleSelect : false,
            columns: [{
                checkbox: true
            },
            {
                field: 'billId',
                title: '发票信息主键',
                visible: false
            },
                {
                    field: 'kbSource',
                    title: '开票来源',
                    formatter: function (value, row, index) {
                        if(value=='KTTG'){
                            return "推广移植";
                        }else{
                            return "技术贸易";
                        }
                    }
                },
               /* {
                    field: 'kbinfoType',
                    title: '单据类型'
                },*/
                {
                    field: 'kbinfoBillNum',
                    title: '业务单据号'
                },
                {
                    field: 'billDate',
                    title: '开票日期'
                },
                {
                    field: 'kbinfoHtbh',
                    title: '合同号'
                },
                {
                    field: 'kbinfoXmbh',
                    title: '项目号'
                },
                {
                    field: 'billNum',
                    title: '发票号'
                },
                {
                    field: 'billAmount',
                    title: '发票金额'
                },
                {
                    field: 'billRemain',
                    title: '剩余可核销金额'
                },

                {
                    field: 'billStatus',
                    title: '开票状态',
                    // 待财务开票、已开票、已核销
                    formatter: function (value, row, index) {
                        if(value=="2"){
                            return "已开票";
                        }
                    }
                }]
        };
        $.table.init(options);
    });
    function queryParams(params){
        var search = $.table.queryParams(params);
        // 开票账套，客商代码
        search.kbinfoAccountBook=$("#kbinfoAccountBook").val();
        search.kbinfoCustomid=$("#kbinfoCustomid").val();
        return search;
    }
    function submitHandler() {
        var searchNo = $("#searchNo").val();
        parent.addColumn(searchNo);
        var index = parent.layer.getFrameIndex(window.name); //先得到当前iframe层的索引
        parent.layer.close(index);
    }
    function onCheck(row, $element){
        var arrSearchNo=[];
        var searchNoS=$("#searchNo").val();
        if(searchNoS==""||searchNoS==null){
            arrSearchNo = [row.billId];
        }else {
            if(searchNoS.indexOf(row.billId)!=-1){    //如果重复
                arrSearchNo = searchNoS;
            }else{
                arrSearchNo = searchNoS + "," + [row.billId];
            }
        }
        $("#searchNo").val(arrSearchNo);
    }
    function onUncheck(row) {
        var searchNoS=$("#searchNo").val();
        var arrSearchNo=[];
        if(searchNoS!=null && searchNoS!=''){
            arrSearchNo=searchNoS.split(",");
        }
        if(arrSearchNo.length>0){
            for (var i = 0; i < arrSearchNo.length; i++) {
                if(arrSearchNo[i]==row.kbinfoId){
                    arrSearchNo.splice(i,1);
                }
            }
        }
        $("#searchNo").val(arrSearchNo);
    }
</script>
</body>
</html>