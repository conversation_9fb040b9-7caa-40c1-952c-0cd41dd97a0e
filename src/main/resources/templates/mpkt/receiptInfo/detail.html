<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('收款信息详情')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-receiptInfo-edit" th:object="${receiptInfo}">
         <div class="panel-group" id="accordion" role="tablist"
                             aria-multiselectable="true">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#version"
                                           href="#jbxx" aria-expanded="false" class="collapsed">收款核销信息
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                                        aria-hidden="true"></i></span>
                                        </a>
                                    </h4>
                                </div>
                                <div id="jbxx" class="panel-collapse collapse in"
                                     aria-expanded="false">
                                    <div class="panel-body">
            <input name="receiptId" th:field="*{receiptId}" type="hidden">

                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">收款收条号：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{receiptCode}"></div>
                        </div>
                        <label class="col-sm-2 control-label">子收条号：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{subReceiptCode}"></div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">公司代码（账套）：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{companyCode}"></div>
                        </div>
                        <label class="col-sm-2 control-label">财务责任中心：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{finaCostCenter}"></div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">票据号：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{paperNo}"></div>
                        </div>
                        <label class="col-sm-2 control-label">用户类型：</label>
                        <div class="col-sm-4">
                            <!--<div th:include="/component/radio :: init(id='userType',name='userType',
                        businessType='',dictCode='userType',value=${receiptInfo.userType} ,see=true)"></div>-->
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">结算用户代码：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{settleUserCode}"></div>
                        </div>
                        <label class="col-sm-2 control-label">结算用户名称：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{settleUserName}"></div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">出票用户代码：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{builtUserCode}"></div>
                        </div>
                        <label class="col-sm-2 control-label">出票用户名称：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{builtUserName}"></div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">票据帐号：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{paperAcct}"></div>
                        </div>
                        <label class="col-sm-2 control-label">出票银行简称：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{builtBankAbbr}"></div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">银行代码：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{bankCode}"></div>
                        </div>
                        <label class="col-sm-2 control-label">银行名称：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{bankName}"></div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">票面金额：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{paperAmt}"></div>
                        </div>
                        <label class="col-sm-2 control-label">折扣金额：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{rebateAmt}"></div>
                        </div>
                    </div>
                </div>


                <!--<div class="form-group" th:include="include :: initSelectBox(id='expenseType', name='expenseType',businessType=null, dictCode=null, value=${receiptInfo.expenseType} ,labelName='费用类型')">
                 </div>-->
                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">总金额：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{totalAmt}"></div>
                        </div>
                        <label class="col-sm-2 control-label">子收条收款收条金额：</label>
                        <div class="col-sm-4">
                            <input type="hidden" id="receiptAmt" th:field="*{receiptAmt}"/>
                            <div class="form-control-static" th:text="*{receiptAmt}"></div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">到期日：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{endDate}"></div>
                        </div>
                        <label class="col-sm-2 control-label">收票日期：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{receiveDate}"></div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">出票日期：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{builtDate}"></div>
                        </div>
                        <label class="col-sm-2 control-label">收款方式名称：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{incomeTypeCode}"></div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">收款性质名称：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{incomeKindCode}"></div>
                        </div>
                        <label class="col-sm-2 control-label">责任中心：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{costCenter}"></div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">录入人工号：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{inputId}"></div>
                        </div>
                        <label class="col-sm-2 control-label">录入日期：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{inputDate}"></div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">票据备注：</label>
                        <div class="col-sm-8">
                            <div class="form-control-static" th:text="*{paperRemark}"></div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">应收业务大类代码：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{asBusiMainCode}"></div>
                        </div>
                        <label class="col-sm-2 control-label">应收业务细类代码：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{asBusiDetlCode}"></div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">币种代码：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{currencyCode}"></div>
                        </div>
                        <label class="col-sm-2 control-label">凭证号码：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{voucherNo}"></div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">凭证日期：</label>
                        <div class="col-sm-4">
                            <div class="form-control-static" th:text="*{voucherDate}"></div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">是否核销：</label>
                        <div class="col-sm-4">
                            <div th:include="/component/radio :: init(id='apportFlag',name='apportFlag',
                                                businessType='MPTY',dictCode='is_yes_no',value=${receiptInfo.apportFlag},see=true)"></div>
                        </div>
                        <label class="col-sm-2 control-label">核销类型：</label>
                        <div class="col-sm-4">
                             <div th:include="/component/select :: init(id='cancelType',name='cancelType',
                                 businessType='KJFY',dictCode='cancelType',value=${receiptInfo.cancelType},see=true)"></div>
                        </div>
                    </div>
                </div>

                    </div>
                </div>
            </div>
        </div>
            <div class="panel-group" id="accordion10" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jdjh" class="collapsed">
                                核销发票信息
                                <span class="pull-right">
                <i class="fa fa-chevron-down"></i>
            </span>
                            </a>
                        </h4>
                    </div>
                    <div id="jdjh" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input type="hidden" id="kbinfoId" name="extra1" th:field="*{extra1}">
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-bill"></table>
                                        <table class="table table-bordered table-hover">
                                            <tfoot style="margin-right: 0px;">
                                            <tr>
                                                <th style="text-align: center; width: 200px;">
                                                    <div class="form-control-static">核销金额总计：</div>
                                                    <div class="fht-cell"style="width: 150px;"></div>
                                                </th>
                                                <th style="text-align: left; ">
                                                    <input name="cancelTotal" type="hidden" th:field="*{cancelTotal}" />
                                                    <div class="form-control-static" id="cancelTotalDiv" th:utext="*{cancelTotal}"></div>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th style="text-align: center;">
                                                    <div class="form-control-static">核销人：</div>
                                                </th>
                                                <th style="text-align: left;">
                                                    <input name="cancelUserCode" type="hidden" th:value="${loginCode}" />
                                                    <input name="cancelUserName" type="hidden" th:value="${loginName}" />
                                                    <div class="form-control-static" th:utext="*{cancelUserName}">核销人</div>
                                                </th>
                                                <th style="text-align: center;">
                                                    <div class="form-control-static">核销日期：</div>
                                                </th>
                                                <th style="text-align: left;">
                                                    <div class="form-control-static" th:utext="*{cancelDate}"></div>
                                                </th>
                                            </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>

    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "mpkt/receiptInfo";

        $("#form-receiptInfo-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-receiptInfo-edit').serialize());
            }
        }

        /***********************************发票信息*******************************************/
        $(function () {
            var options = {
                url: ctx + "mpkt/receiptCancelInfo/queryList?receiptId="+[[${receiptInfo.receiptId}]],
                id: "bootstrap-table-bill",
                toolbar:"toolbar-xmfz",
                modalName: "发票信息",
                uniqueId: "billId",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        witdh:'5%',
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='tgfPeople[%s].index' value='%s'>", index, $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='tgfPeople[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                            var html = $.common.sprintf("<input type='hidden' name='receipts[%s].billId' value='%s'>", index,row.billId);
                            return columnIndex + $.table.serialNumber(index) + columnId+html;
                        }
                    },
                    {
                        field: 'cancelId',
                        title: '核销信息主键',
                        visible: false
                    },
                    {
                        field: 'billId',
                        title: '发票信息主键',
                        visible: false
                    },
                    {
                        field: 'kbinfoBillNum',
                        align: 'center',
                        title: '业务单据号'
                    },
                    {
                        field: 'billDate',
                        align: 'center',
                        title: '开票日期'
                    },
                    {
                        field: 'kbinfoXmbh',
                        align: 'center',
                        title: '项目编号'
                    },{
                        field: 'kbinfoHtbh',
                        align: 'center',
                        title: '合同号'
                    },
                    {
                        field: 'billNum',
                        align: 'center',
                        title: '发票号'
                    },
                    {
                        field: 'billCode',
                        align: 'center',
                        title: '发票代码'
                    },
                    {
                        field: 'billAmount',
                        align: 'center',
                        title: '发票金额'
                    },
                    {
                        field: 'billRemain',
                        align: 'center',
                        title: '可核销金额'
                    },
                    {
                        field: 'billCancel',
                        align: 'center',
                        title: '核销金额（元）'
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });

    </script>
</body>
</html>