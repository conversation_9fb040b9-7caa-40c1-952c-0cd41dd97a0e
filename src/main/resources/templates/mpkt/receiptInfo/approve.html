<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('收款信息核销')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-receiptInfo-edit" th:object="${receiptInfo}">
         <div class="panel-group" id="accordion" role="tablist"
                             aria-multiselectable="true">
                            <div class="panel panel-default">
                                <div class="panel-heading">
                                    <h4 class="panel-title">
                                        <a data-toggle="collapse" data-parent="#version"
                                           href="#jbxx" aria-expanded="false" class="collapsed">收款核销信息
                                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                                            <span class="pull-right"><i class="fa fa-chevron-down"
                                                                        aria-hidden="true"></i></span>
                                        </a>
                                    </h4>
                                </div>
                                <div id="jbxx" class="panel-collapse collapse in"
                                     aria-expanded="false">
                                    <div class="panel-body">
            <input name="receiptId" th:field="*{receiptId}" type="hidden">
            <input name="processCode" th:value="${processCode}" type="hidden">
            <input name="activityCode" th:value="${activityCode}" type="hidden">
            <input name="businessGuid" th:value="${businessGuid}" type="hidden">
            <input name="processInstanceId" th:value="${processInstanceId}" type="hidden">
            <input name="taskId" th:value="${taskId}" type="hidden">

            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">收款收条号：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{receiptCode}"></div>
                    </div>
                    <label class="col-sm-2 control-label">子收条号：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{subReceiptCode}"></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">公司代码（账套）：</label>
                    <div class="col-sm-4">
                        <input type="hidden" id="companyCode" th:value="*{companyCode}"/>
                        <div class="form-control-static" th:text="*{companyCode}"></div>
                    </div>
                    <label class="col-sm-2 control-label">财务责任中心：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{finaCostCenter}"></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">票据号：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{paperNo}"></div>
                    </div>
                    <label class="col-sm-2 control-label">用户类型：</label>
                    <div class="col-sm-4">
                        <!--<div th:include="/component/radio :: init(id='userType',name='userType',
                    businessType='',dictCode='userType',value=${receiptInfo.userType} ,see=true)"></div>-->
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">结算用户代码：</label>
                    <input id="settleUserCode" type="hidden" th:value="*{settleUserCode}"/>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{settleUserCode}"></div>
                    </div>
                    <label class="col-sm-2 control-label">结算用户名称：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{settleUserName}"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">出票用户代码：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{builtUserCode}"></div>
                    </div>
                    <label class="col-sm-2 control-label">出票用户名称：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{builtUserName}"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">票据帐号：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{paperAcct}"></div>
                    </div>
                    <label class="col-sm-2 control-label">出票银行简称：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{builtBankAbbr}"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">银行代码：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{bankCode}"></div>
                    </div>
                    <label class="col-sm-2 control-label">银行名称：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{bankName}"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">票面金额：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{paperAmt}"></div>
                    </div>
                    <label class="col-sm-2 control-label">折扣金额：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{rebateAmt}"></div>
                    </div>
                </div>
            </div>


      <!--<div class="form-group" th:include="include :: initSelectBox(id='expenseType', name='expenseType',businessType=null, dictCode=null, value=${receiptInfo.expenseType} ,labelName='费用类型')">
       </div>-->
            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">总金额：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{totalAmt}"></div>
                    </div>
                    <label class="col-sm-2 control-label">子收条收款收条金额：</label>
                    <div class="col-sm-4">
                        <input type="hidden" id="receiptAmt" th:field="*{receiptAmt}"/>
                        <div class="form-control-static" th:text="*{receiptAmt}"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">到期日：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{endDate}"></div>
                    </div>
                    <label class="col-sm-2 control-label">收票日期：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{receiveDate}"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">出票日期：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{builtDate}"></div>
                    </div>
                    <label class="col-sm-2 control-label">收款方式名称：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{incomeTypeCode}"></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">收款性质名称：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{incomeKindCode}"></div>
                    </div>
                    <label class="col-sm-2 control-label">责任中心：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{costCenter}"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">录入人工号：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{inputId}"></div>
                    </div>
                    <label class="col-sm-2 control-label">录入日期：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{inputDate}"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">票据备注：</label>
                    <div class="col-sm-8">
                        <div class="form-control-static" th:text="*{paperRemark}"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">应收业务大类代码：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{asBusiMainCode}"></div>
                    </div>
                    <label class="col-sm-2 control-label">应收业务细类代码：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{asBusiDetlCode}"></div>
                    </div>
                </div>
            </div>

            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">币种代码：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{currencyCode}"></div>
                    </div>
                    <label class="col-sm-2 control-label">凭证号码：</label>
                    <div class="col-sm-4">
                        <div class="form-control-static" th:text="*{voucherNo}"></div>
                    </div>
                </div>
            </div>

             <div class="row">
            <div class="form-group">    
                <label class="col-sm-2 control-label">凭证日期：</label>
                <div class="col-sm-4">
                    <div class="form-control-static" th:text="*{voucherDate}"></div>
                </div>
            </div>
             </div>
             <div class="row">
            <div class="form-group">    
                <label class="col-sm-2 control-label">是否核销：</label>
                <div class="col-sm-4">
                    <div th:include="/component/radio :: init(id='apportFlag',name='apportFlag',
                                                businessType='MPTY',dictCode='is_yes_no',value=${receiptInfo.apportFlag},see=true)"></div>
                </div>
                <label class="col-sm-2 control-label is-required">核销类型：</label>
                <div class="col-sm-4">
                    <div th:include="/component/select :: init(id='cancelType',name='cancelType',
                                 businessType='KJFY',dictCode='cancelType',value=${receiptInfo.cancelType},isfirst=true,isrequired=true)"></div>
                </div>
            </div>
             </div>


                    </div>
                </div>
            </div>
        </div>

            <div class="panel-group" id="accordion10" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jdjh" class="collapsed">
                                核销发票信息
                                <span class="pull-right">
                <i class="fa fa-chevron-down"></i>
            </span>
                            </a>
                        </h4>
                    </div>
                    <div id="jdjh" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <input type="hidden" id="kbinfoId" name="extra1" th:field="*{extra1}">
                                    <button type="button" class="btn btn-white btn-sm" onclick="selectBillInfoList()"><i class="fa fa-plus">选择</i></button>
                                    <button type="button" class="btn btn-white btn-sm" onclick="delColnum()"><i class="fa fa-plus">删除所有</i></button>
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-bill"></table>
                                        <table class="table table-bordered table-hover">
                                            <tfoot style="margin-right: 0px;">
                                            <tr>
                                                <th style="text-align: center; width: 200px;">
                                                    <div class="form-control-static">核销金额总计：</div>
                                                    <div class="fht-cell"style="width: 150px;"></div>
                                                </th>
                                                <th style="text-align: left; ">
                                                    <input name="cancelTotal" type="hidden" th:field="*{cancelTotal}" />
                                                    <div class="form-control-static" id="cancelTotalDiv" th:utext="*{cancelTotal}"></div>
                                                </th>
                                            </tr>
                                            <tr>
                                                <th style="text-align: center;">
                                                    <div class="form-control-static">核销人：</div>
                                                </th>
                                                <th style="text-align: left;">
                                                    <input name="cancelUserCode" type="hidden" th:value="${loginCode}" />
                                                    <input name="cancelUserName" type="hidden" th:value="${loginName}" />
                                                    <div th:if="${#strings.isEmpty(receiptInfo.cancelUserName)}" class="form-control-static" th:utext="${loginName}">核销人</div>
                                                    <div th:if="${not #strings.isEmpty(receiptInfo.cancelUserName)}" class="form-control-static" th:utext="${loginName}">核销人</div>
                                                </th>
                                                <th style="text-align: center;">
                                                    <div class="form-control-static">核销日期：</div>
                                                </th>
                                                <th style="text-align: left;">
                                                    <div th:if="${#strings.isEmpty(receiptInfo.cancelDate)}" class="form-control-static" th:utext="${downDate}"></div>
                                                    <div th:if="${not #strings.isEmpty(receiptInfo.cancelDate)}" class="form-control-static" th:utext="*{cancelDate}"></div>

                                                </th>
                                            </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        </form>
    </div>
    <div class="row">
		<div class="toolbar toolbar-bottom" role="toolbar" >
			<button type="button" class="btn btn-primary"
				onclick="saveHandler()">
				<i class="fa fa-check"></i>保 存
			</button>

            &nbsp;<th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

            <button type="button" class="btn btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>关 闭
			</button>
		</div>
	</div>

    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "mpkt/receiptInfo";

        $("#form-receiptInfo-edit").validate({
            focusCleanup: true
        });

        function saveHandler() {
            if ($.validate.form()) {
                var config = {
                    url: prefix + "/addEx",
                    type: "post",
                    dataType: "json",
                    data: $('#form-receiptInfo-edit').serialize(),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $("#receiptId").val(result.data.receiptId);
                        $.modal.alertSuccess(result.msg);
                        $.modal.closeLoading();
                    }
                };
                $.ajax(config)
            }
        }
        function submitHandler(){
            $.modal.confirm("确认提交吗？", function () {
                var total = $("input[name='cancelTotal']").val();
                var receiptAmt = $("#receiptAmt").val();
                if(parseInt(total)>parseInt(receiptAmt)){
                    $.modal.alertError("核销金额总计大于子收条金额！");
                    return false;
                }else if(parseInt(total)<parseInt(receiptAmt)){
                    $.modal.alertError("核销金额总计小于子收条金额！");
                    return false;
                }
                if ($.validate.form()) {
                    $.operate.saveTabAlert(prefix + "/submitWF",$('#form-receiptInfo-edit').serialize());
                }
            })
        }

        function selectBillInfoList() {
            var settleUserCode = $("#settleUserCode").val();
            var companyCode = $("#companyCode").val();
            var url =prefix+"/selectBillInfoList?kbinfoAccountBook="+companyCode+"&kbinfoCustomid="+settleUserCode;
            $.modal.open("选择发票信息", url, '1000', '500');
        }

        /***********************************发票信息*******************************************/
        $(function () {
            var options = {
                url: ctx + "mpkt/receiptCancelInfo/queryList?receiptId="+[[${receiptInfo.receiptId}]],
                id: "bootstrap-table-bill",
                toolbar:"toolbar-xmfz",
                modalName: "发票信息",
                uniqueId: "billId",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        witdh:'5%',
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='tgfPeople[%s].index' value='%s'>", index, $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='tgfPeople[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                            var html = $.common.sprintf("<input type='hidden' name='receipts[%s].billId' value='%s'>", index,row.billId);
                            return columnIndex + $.table.serialNumber(index) + columnId+html;
                        }
                    },
                    {
                        field: 'cancelId',
                        title: '核销信息主键',
                        visible: false
                    },
                    {
                        field: 'billId',
                        title: '发票信息主键',
                        visible: false
                    },
                    {
                        field: 'kbinfoBillNum',
                        align: 'center',
                        title: '业务单据号',
                        formatter: function(value, row, index) {
                            var ss = value;
                            var html = $.common.sprintf(""+ss+"<input type='hidden' name='receipts[%s].kbinfoBillNum' value='%s'>", index,value);
                            return html;
                        }
                    },
                    {
                        field: 'billDate',
                        align: 'center',
                        title: '开票日期',
                        formatter: function(value, row, index) {
                            var ss = value;
                            var html = $.common.sprintf(""+ss+"<input type='hidden' name='receipts[%s].billDate' value='%s'>", index,value);
                            return html;
                        }
                    },
                    {
                        field: 'kbinfoXmbh',
                        align: 'center',
                        title: '项目编号',
                        formatter: function(value, row, index) {
                            var ss = value;
                            var html = $.common.sprintf(""+ss+"<input type='hidden' name='receipts[%s].kbinfoXmbh' value='%s' >", index,value);
                            return html;
                        }
                    },{
                        field: 'kbinfoHtbh',
                        align: 'center',
                        title: '合同号',
                        formatter: function(value, row, index) {
                            var ss = value;
                            var html = $.common.sprintf(""+ss+"<input type='hidden' name='receipts[%s].kbinfoHtbh' value='%s'>", index,value);
                            return html;
                        }
                    },
                    {
                        field: 'billNum',
                        align: 'center',
                        title: '发票号',
                        formatter: function(value, row, index) {
                            var ss = value;
                            var html = $.common.sprintf(""+ss+"<input type='hidden' name='receipts[%s].billNum' value='%s'>", index,value);
                            return html;
                        }
                    },
                    {
                        field: 'billCode',
                        align: 'center',
                        title: '发票代码',
                        formatter: function(value, row, index) {
                            var ss = value;
                            var html = $.common.sprintf(""+ss+"<input  type='hidden' name='receipts[%s].billCode' value='%s'>", index,value);
                            return html;
                        }
                    },
                    {
                        field: 'billAmount',
                        align: 'center',
                        title: '发票金额',
                        formatter: function(value, row, index) {
                            var ss = value;
                            var html = $.common.sprintf("<input type='hidden' name='receipts[%s].billAmount' value='%s'>"+ss+"<input type='hidden' id='%sbillAmount' value='%s'>", index, value,index,value);
                            return html;
                        }
                    },
                    {
                        field: 'billRemain',
                        align: 'center',
                        title: '可核销金额',
                        formatter: function(value, row, index) {
                            var ss = value;
                            var html = $.common.sprintf("<span id='billRemain%s'>"+ss+"</span><input type='hidden' name='receipts[%s].billRemain' value='%s'><input type='hidden' id='%sbillRemain' value='%s'>", index, index, value,index,value);
                            return html;
                        }
                    },
                    {
                        field: 'billCancel',
                        align: 'center',
                        title: '核销金额（元）',
                         formatter: function(value, row, index) {
                            var i = index;
                             var html = $.common.sprintf("<input class='form-control' required min='0'  type='number' onblur='changeCancel("+i+")' name='receipts[%s].billCancel' value='%s' onkeyup=\"this.value=this.value.replace(/[^\\d]/g,'')\">", index,value);
                             return html;
                         }
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });
        
        function addColumn(kbinfoId) {
            var kbinfoId2 = $("#kbinfoId").val();
            var kbinfoId1 = kbinfoId.split(",");
            var kbinfoId3 = [];
            var kbinfoId4 = [];
            for (var x = 0; x < kbinfoId1.length; x++) {
                if (kbinfoId2.indexOf(kbinfoId1[x])!=-1) {
                    kbinfoId3.push(kbinfoId1[x]);   //重复的
                }else{
                    kbinfoId4.push(kbinfoId1[x]);   //不重复的
                }
            }
            if(kbinfoId4.length>0){
                var arrSearchNo=[];
                $(".no-records-found").remove();
                for (var i = 0; i < kbinfoId4.length; i++) {
                    if(kbinfoId2!=""&&kbinfoId2!=null){
                        arrSearchNo = kbinfoId2 +","+kbinfoId4[i];
                    }else{
                        arrSearchNo = kbinfoId4[i];
                    }
                    $("#kbinfoId").val(arrSearchNo);
                    var config = {
                        url: ctx + "mpkt/billInfo/loadList?billId=" +kbinfoId4[i]+"&billStatus=2" ,   //已开票
                        type: "post",
                        dataType: "json",
                        success: function (result) {
                            var data = result.data;
                            var attr = data.attr;
                            var list = attr.list;
                            if(list.length>0){
                                for (var j = 0; j < list.length; j++) {
                                    var orderNum = $("#bootstrap-table-bill").find("tr").length;
                                    var l =$("#bootstrap-table-bill").find("tr").length;
                                    var len = parseInt(l-1)
                                    // var tr = '   <tr class="bs-checkbox " style="width: 36px; " data-field="0"><div class="th-inner "><label><input name="btSelectAll" type="checkbox"><span></span></label></div><div class="fht-cell"></div></tr>';
                                    var tr = "<tr data-index='" + len + "'>";
                                    tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='receipts[" + len + "].kbinfoId' value='" + list[j].kbinfoId + "'><input type='hidden' name='receipts[" + len + "].billId' value='" + list[j].billId + "'>" + orderNum + "</td>";
                                    tr = tr + "<td style='text-align: center;width: 10%;'><input type='hidden' name='receipts[" + len + "].kbinfoBillNum' value=" + list[j].kbinfoBillNum + ">"+list[j].kbinfoBillNum+"</td>";
                                    tr = tr + "<td style='text-align: center;width: 10%;'><input type='hidden' name='receipts[" + len + "].billDate' value=" + list[j].billDate + ">"+list[j].billDate+"</td>";
                                    tr = tr + "<td style='text-align: center;width: 10%;'><input type='hidden' name='receipts[" + len + "].kbinfoXmbh' value=" + list[j].kbinfoXmbh + " >"+list[j].kbinfoXmbh+"</td>";
                                    tr = tr + "<td style='text-align: center;width: 10%;'><input type='hidden' name='receipts[" + len + "].kbinfoHtbh' value=" + list[j].kbinfoHtbh + ">"+list[j].kbinfoHtbh+"</td>";
                                    tr = tr + "<td style='text-align: center;width: 10%;'><input type='hidden' name='receipts[" + len + "].billNum' value=" + list[j].billNum + ">"+list[j].billNum+"</td>";
                                    tr = tr + "<td style='text-align: center;width: 10%;'><input  type='hidden' name='receipts[" + len + "].billCode' value=" + list[j].billCode + ">"+list[j].billCode+"</td>";
                                    tr = tr + "<td style='text-align: center;width: 10%;' id='billAmount" + len + "'><input type='hidden' name='receipts[" + len + "].billAmount' value=" + list[j].billAmount + ">"+list[j].billAmount+"<input type='hidden' id='" + len + "billAmount' value=" + list[j].billAmount + "></td>";
                                    tr = tr + "<td style='text-align: center;width: 10%;'><span id='billRemain" + len + "'>"+list[j].billRemain+"</span><input type='hidden' name='receipts[" + len + "].billRemain' value=" + list[j].billRemain + "><input type='hidden' id='" + len + "billRemain' value=" + list[j].billRemain + "></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' required type='number' onblur='changeCancel(" + len + ")' name='receipts[" + len + "].billCancel'></td>";
                                    tr = tr + "</tr>";
                                    $(".no-records-found").remove();
                                    $("#bootstrap-table-bill tbody").append(tr);
                                }
                            }
                        }
                    };
                    $.ajax(config)
                }
            }
        }

        function changeCancel(e){
            var billAmount = $("#"+e+"billAmount").val();
            var billRemain = $("#"+e+"billRemain").val();
            var billCancel = $("input[name='receipts["+e+"].billCancel']").val();
           if(billCancel!=""&&billCancel!=null){
               if(parseInt(billCancel)>parseInt(billRemain)){
                   $.modal.alertError("请输入"+billRemain+"以内的金额！");
                   return false;
               }
               var bill = parseInt(billAmount)-parseInt(billCancel);
               $("#billRemain"+e).html(bill);
               $("input[name='receipts["+e+"].billRemain']").val(bill);

           }
            var len =$("#bootstrap-table-bill").find("tr").length;
            var cancelTotal = 0;
            var total = 0;
            for (var i = 0; i <len ; i++) {
                var billCancel = $("input[name='receipts["+i+"].billCancel']").val();
                if(billCancel==null||billCancel==''){
                    billCancel=0;
                }
                total+=parseInt(billCancel);
            }
            cancelTotal = total;
            $("#cancelTotalDiv").html(cancelTotal);
            $("input[name='cancelTotal']").val(cancelTotal);
        }

        /**
         * 计算总额
         */
        function cal(){
            var len =$("#bootstrap-table-bill").find("tr").length;
            var cancelTotal = 0;
            var total = 0;
            for (var i = 0; i <len ; i++) {
                var billCancel = $("input[name='receipts["+i+"].billCancel']").val();
                if(billCancel==null||billCancel==''){
                    billCancel=0;
                }
                total+=parseInt(billCancel);
            }
            cancelTotal = total;
            $("#cancelTotalDiv").html(cancelTotal);
            var total = $("input[name='cancelTotal']").val();
            var receiptAmt = $("#receiptAmt").val();
            if(parseInt(total)>parseInt(receiptAmt)){
                $.modal.alertError("核销金额总计大于子收条金额！");
                return false;
            }else if(parseInt(total)<parseInt(receiptAmt)){
                $.modal.alertError("核销金额总计小于子收条金额！");
                return false;
            }
            $("input[name='cancelTotal']").val(cancelTotal);
        }

        function delColnum() {
            var  tabel = document.getElementById( "bootstrap-table-bill" );
            var rowCount = tabel.rows.length;
            for(var i=rowCount-1;i>=1;i--){
                tabel.deleteRow(i);
            }
            $("#kbinfoId").val("");
            $("#cancelTotalDiv").html("0");
            $("input[name='cancelTotal']").val("");
        }


    </script>
</body>
</html>