<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('26.0_03收款信息列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>收款收条号：</label>
                                <input type="text" name="receiptCode"/>
                            </li>
                            <li>
                                <label>子收条号：</label>
                                <input type="text" name="subReceiptCode"/>
                            </li>
                            <li>
                                <label>票据号：</label>
                                <input type="text" name="paperNo"/>
                            </li>
                            <li>
                                <label>结算用户名称：</label>
                                <input type="text" name="settleUserName"/>
                            </li>
                            <li>
                                <label>出票用户名称：</label>
                                <input type="text" name="builtUserName"/>
                            </li>
                            <li>
                                <label>收票日期：</label>
                                <input type="text" name="receiveDate"/>
                            </li>
                            <li>
                                <label>出票日期：</label>
                                <input type="text" name="builtDate"/>
                            </li>
                            <li>
                                <label>收款方式名称：</label>
                                <input type="text" name="incomeTypeName"/>
                            </li>
                            <li>
                                <label>收款性质名称：</label>
                                <input type="text" name="incomeKindName"/>
                            </li>
                            <li>
                                <label>项目编号：</label>
                                <input type="text" name="projectNo"/>
                            </li>
                            <li>
                                <label>合同号：</label>
                                <input type="text" name="orderNo"/>
                            </li>
                            <li>
                                <label>核销类型：</label>
                                <div th:include="/component/select :: init(id='cancelType',name='cancelType',
                                 businessType='KJFY',dictCode='cancelType',isfirst=true)"></div>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <!--<a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>-->
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
             <!--   <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>-->
                <a class="btn btn-primary single disabled" onclick="cancelHX()">
                    <i class="fa fa-edit"></i> 红冲
                </a>
<!--                <a class="btn btn-warning" onclick="doStart()">-->
<!--                    <i class="fa fa-download"></i> 触发-->
<!--                </a>-->
            </div>

            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "mpkt/receiptInfo";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                detailUrl: prefix + "/detail/{id}",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "收款信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'receiptId',
                    title: '收款信息主键',
                    visible: false
                },
                {
                    field: 'receiptCode',
                    title: '收款收条号'
                },
                {
                    field: 'subReceiptCode',
                    title: '子收条号'
                },
                {
                    field: 'projectNo',
                    title: '项目编号'
                },
                {
                    field: 'orderNo',
                    title: '合同号'
                },

                {
                    field: 'paperNo',
                    title: '票据号'
                },
                {
                    field: 'settleUserName',
                    title: '结算用户名称'
                },
                {
                    field: 'builtUserName',
                    title: '出票用户名称'
                },
                {
                    field: 'paperAcct',
                    title: '票据帐号'
                },
                {
                    field: 'receiveDate',
                    title: '收票日期'
                },
                {
                    field: 'builtDate',
                    title: '出票日期'
                },
                {
                    field: 'cancelStatus',
                    title: '核销状态',
                    formatter: function(value, row, index) {
                        var str = "";
                        if(value=="2"){
                            str = "未核销";
                        }else if(value=="3"){
                            str = "已核销";
                        }else if(value=="4"){
                            str = "待财务核销"
                        }
                         return str;
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.receiptId + '\')"><i class="fa fa-eye"></i>详情</a> ');
                        var cancelStatus=row.cancelStatus;
                        if(cancelStatus=='2'){
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="chuli(\'' + row.receiptId + '\')"><i class="fa fa-edit"></i>处理</a> ');
                        }
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function doStart(){
            $.modal.confirm("确认提交吗？", function () {
                if ($.validate.form()) {
                    $.operate.saveTabAlert(prefix + "/startWF");
                }
            })
        }

        function chuli(id){
            $.modal.openTab("发票核销",prefix+"/approve?businessGuid="+id);
        }

        function cancelHX(){
            var selectColumns = $.table.selectColumns("receiptId");
            if (selectColumns.length == 0) {
                $.modal.alertWarning("请至少选择一条记录");
                return;
            }
            var cancelStatus=$.table.selectColumns("cancelStatus")[0];
            if(cancelStatus=='3'||cancelStatus=='4'){
                $.modal.confirm("确定红冲该条" + table.options.modalName + "信息吗？", function () {
                    var url = prefix + "/cancelHX";
                    var data = {"receiptId": selectColumns[0]};
                    $.operate.submit(url, "post", "json", data);
                });
            }else{
                $.modal.alertWarning("未核销的记录不能发送红冲");
                return;
            }
        }
    </script>
</body>
</html>