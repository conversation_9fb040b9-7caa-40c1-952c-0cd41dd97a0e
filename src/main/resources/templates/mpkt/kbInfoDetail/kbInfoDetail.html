<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('26.0_01.01开票明细信息列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>开票信息主键：</label>
                                <input type="text" name="kbinfoId"/>
                            </li>
                            <li>
                                <label>品名：</label>
                                <input type="text" name="goods"/>
                            </li>
                            <li>
                                <label>规格型号：</label>
                                <input type="text" name="model"/>
                            </li>
                            <li>
                                <label>计量单位：</label>
                                <input type="text" name="kbinfoUnit"/>
                            </li>
                            <li>
                                <label>单价：</label>
                                <input type="text" name="kbinfoPrice"/>
                            </li>
                            <li>
                                <label>数量：</label>
                                <input type="text" name="kbinfoCount"/>
                            </li>
                            <li>
                                <label>税率：</label>
                                <input type="text" name="rate"/>
                            </li>
                            <li>
                                <label>不含税金额：</label>
                                <input type="text" name="invoiceNoTax"/>
                            </li>
                            <li>
                                <label>发票税额：</label>
                                <input type="text" name="invoiceTax"/>
                            </li>
                            <li>
                                <label>备注：</label>
                                <input type="text" name="kbinfoRemark"/>
                            </li>
                            <li>
                                <label>扩展字段1：</label>
                                <input type="text" name="extra1"/>
                            </li>
                            <li>
                                <label>扩展字段2：</label>
                                <input type="text" name="extra2"/>
                            </li>
                            <li>
                                <label>扩展字段3：</label>
                                <input type="text" name="extra3"/>
                            </li>
                            <li>
                                <label>扩展字段4：</label>
                                <input type="text" name="extra4"/>
                            </li>
                            <li>
                                <label>扩展字段5：</label>
                                <input type="text" name="extra5"/>
                            </li>
                            <li>
                                <label>删除状态：</label>
                                <select name="delStatus" th:with="type=${@dict.getDictList(null,null)}">
                                    <option value="">所有</option>
                                    <option th:each="dict : ${type}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
                                </select>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="$.table.exportExcel()" >
                    <i class="fa fa-download"></i> 导出
                </a>
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "mpkt/kbInfoDetail";

        $(function() {
            var options = {
                url: prefix + "/list",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                exportUrl: prefix + "/export",
                modalName: "26.0_01.01开票明细信息",
                columns: [{
                    checkbox: true
                },
                {
                    field: 'detailId',
                    title: '开票明细信息主键',
                    visible: false
                },
                {
                    field: 'kbinfoId',
                    title: '开票信息主键'
                },
                {
                    field: 'goods',
                    title: '品名'
                },
                {
                    field: 'model',
                    title: '规格型号'
                },
                {
                    field: 'kbinfoUnit',
                    title: '计量单位'
                },
                {
                    field: 'kbinfoPrice',
                    title: '单价'
                },
                {
                    field: 'kbinfoCount',
                    title: '数量'
                },
                {
                    field: 'rate',
                    title: '税率'
                },
                {
                    field: 'invoiceNoTax',
                    title: '不含税金额'
                },
                {
                    field: 'invoiceTax',
                    title: '发票税额'
                },
                {
                    field: 'kbinfoRemark',
                    title: '备注'
                },
                {
                    field: 'extra1',
                    title: '扩展字段1'
                },
                {
                    field: 'extra2',
                    title: '扩展字段2'
                },
                {
                    field: 'extra3',
                    title: '扩展字段3'
                },
                {
                    field: 'extra4',
                    title: '扩展字段4'
                },
                {
                    field: 'extra5',
                    title: '扩展字段5'
                },
                {
                    field: 'delStatus',
                    title: '删除状态'
                },
                {
                    field: 'createUserLabel',
                    title: '创建人'
                },
                {
                    field: 'createDate',
                    title: '创建时间'
                },
                {
                    field: 'updateUserLabel',
                    title: '更新人'
                },
                {
                    field: 'updateDate',
                    title: '更新时间'
                },
                {
                    field: 'deleteUserLabel',
                    title: '删除人'
                },
                {
                    field: 'deleteDate',
                    title: '删除时间'
                },
                {
                    field: 'recordVersion',
                    title: '版本号'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.detailId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                        actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.detailId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });
    </script>
</body>
</html>