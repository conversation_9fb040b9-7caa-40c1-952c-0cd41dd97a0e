<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('网络学会入会申请信息')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <form class="form-horizontal m" id="from01" th:object="${data}">
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version" href="#jbxx" aria-expanded="false" class="collapsed">基本信息
                            <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="jbxx" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <h1 style="text-align: center;margin-bottom: 20px;">宝钢股份公司参加社会组织申请表</h1>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">流水号：</label>
                            <div class="col-sm-10">
                                <div class="form-control-static" th:utext="*{serialNo}"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">申请单位：</label>
                            <div class="col-sm-10">
                                <div class="form-control-static"
                                     th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(data.deptCode)}"></div>
                                <input class="form-control" name="deptCode" th:value="*{deptCode}" type="hidden">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">申请人姓名：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(data.jbrId)}"></div>
                                <input class="form-control" name="jbrId" th:value="*{jbrId}" type="hidden">
                            </div>
                            <label class="col-sm-2 control-label is-required">联系电话：</label>
                            <div class="col-sm-2">
                                <input class="form-control" name="tel" th:value="*{tel}" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">参加组织名称：</label>
                            <div class="col-sm-8">
                                <input class="form-control" oninput="onSocietyName(this.value)" id="societyName" name="societyName" th:value="*{societyName}"
                                       type="text" required>
                            </div>
                            <div class="col-sm-1">
                                <th:block th:include="knxh/mySociety :: init(societyName='societyName',socialCreditCode='socialCreditCode', callback=doSocietyCallback)"/>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">统一社会信用代码：</label>
                            <div class="col-sm-6">
                                <input class="form-control" oninput="onCreditCode(this.value)" id="socialCreditCode" name="socialCreditCode" th:value="*{socialCreditCode}"
                                       type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">主管（办）单位：</label>
                            <div class="col-sm-9">
                                <input class="form-control" oninput="onOrganizer(this.value)" name="organizer" id="organizer" th:value="*{organizer}" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">国内/外组织：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/radio :: init(id='societyType',name='societyType',businessType='KNXH',dictCode='societyType',value=*{societyType},isrequired='true')"></div>
                            </div>
                            <label class="col-sm-2 control-label is-required">任职：</label>
                            <div class="col-sm-2">
                                <select name="duty" class="form-control" id="duty" th:with="dictData=${@dict.getDictList('KNXH','duty')}" onchange="doDuty(this.value)" required>
                                    <option value="">请选择</option>
                                    <option th:each="dict : ${dictData}" th:selected="${data.duty eq dict.dictValue}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">年费金额：</label>
                            <div class="col-sm-3">
                                <input class="form-control" name="costRmb" id="costRmb" th:value="*{costRmb}" type="number" required>
                            </div>
                            <div class="col-sm-1"><label class="control-label">元/年</label></div>
                            <label class="col-sm-2 control-label is-required">年费外汇金额：</label>
                            <div class="col-sm-2">
                                <input class="form-control" name="cost" id="cost" th:value="*{cost}" type="number">
                            </div>
                            <div class="col-sm-2">
                                <div th:include="/component/select :: init(id='currency',name='currency',businessType='KYGJ',dictCode='currency',isfirst=true,value=*{currency})"><label class="control-label">/年</label></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">费用出资方：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/radio :: init(id='extra1',name='extra1',businessType='KNXH',dictCode='isFyczf',value=*{extra1},isrequired='true')"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="mnote-editor-title"><span class="txt-impt">*</span>协会(网络)简介</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                                    <textarea class="form-control" style="white-space: break-spaces;" name="summary" rows="5" cols="150" th:text="*{summary}" required="required"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="mnote-editor-title"><span class="txt-impt">*</span>入会（网）必要性和重要性</div>
                            <div class="mnote-editor-box">
                                <div class="col-sm-12">
                                    <textarea class="form-control" style="white-space: break-spaces;" name="necessity" rows="5" cols="150" th:text="*{necessity}" required="required"></textarea>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">相关附件：</label>
                            <div class="col-sm-8">
                                <div class="form-group"
                                     th:include="/component/attachment :: init(name='rhxgfj',id='rhxgfj',sourceId=*{applyId},sourceModule='KNXH_RHXGFJ')"></div>
                            </div>
                        </div>
                        <input name="applyId" id="applyId" th:value="*{applyId}" type="hidden">
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group" role="tablist" aria-multiselectable="true" th:object="${data.society}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version" href="#2" aria-expanded="false" class="collapsed">社会组织信息登记表
                            <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">统一社会信用代码：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.socialCreditCode" id="society_socialCreditCode" th:field="*{socialCreditCode}" type="text" required="required">
                            </div>
                            <label class="col-sm-2 control-label">开始年份：</label>
                            <div class="col-sm-2">
                                <input class="form-control" name="society.startYear" id="society_startYear" th:field="*{startYear}" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">组织名称：</label>
                            <div class="col-sm-8">
                                <input class="form-control" name="society.societyName" id="society_societyName" th:field="*{societyName}" type="text" required="required">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">主办单位：</label>
                            <div class="col-sm-8">
                                <input class="form-control" name="society.organizer" id="society_organizer" th:field="*{organizer}" type="text" required="required">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">地址：</label>
                            <div class="col-sm-10">
                                <input class="form-control" name="society.address" id="society_address" th:field="*{address}" type="text" required="required">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">国内/外组织：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/radio :: init(id='society_societyType',name='society.societyType',businessType='KNXH',dictCode='societyType',value=${data.society.societyType},isrequired='true')"></div>
                            </div>
                            <label class="col-sm-2 control-label">邮编：</label>
                            <div class="col-sm-2">
                                <input class="form-control" name="society.zipCode" id="society_zipCode" th:field="*{zipCode}" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">联系人：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.linkman" id="society_linkman" th:field="*{linkman}" type="text">
                            </div>
                            <label class="col-sm-2 control-label">电话：</label>
                            <div class="col-sm-2">
                                <input class="form-control" name="society.linkmanTel" id="society_linkmanTel" th:field="*{linkmanTel}" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">E-mail：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.linkmanEmail" id="society_linkmanEmail" th:field="*{linkmanEmail}" type="text">
                            </div>
                            <label class="col-sm-2 control-label">传真：</label>
                            <div class="col-sm-2">
                                <input class="form-control" name="society.linkmanFax" id="society_linkmanFax" th:field="*{linkmanFax}" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">网址：</label>
                            <div class="col-sm-8">
                                <input class="form-control" name="society.homepage" id="society_homepage" th:field="*{homepage}" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">电子信息：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/radio :: init(id='society_ifElec',name='society.ifElec',businessType='KNXH',dictCode='ifElec',value=${data.society.ifElec},isrequired='true')"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">名称/网址：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.intUrl" id="society_intUrl" th:field="*{intUrl}" type="text">
                            </div>
                            <label class="col-sm-2 control-label">电子信息频率：</label>
                            <div class="col-sm-2">
                                <input class="form-control" name="society.frequency" id="society_frequency" th:field="*{frequency}" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">用户名：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.userName" id="society_userName" th:field="*{userName}" type="text">
                            </div>
                            <label class="col-sm-2 control-label">密码：</label>
                            <div class="col-sm-2">
                                <input class="form-control" name="society.passwoerd" id="society_passwoerd" th:field="*{passwoerd}" type="password">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">期刊名称：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.weekly" id="society_weekly" th:field="*{weekly}" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">纸介信息：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/radio :: init(id='society_ifPaper',name='society.ifPaper',businessType='KNXH',dictCode='ifPaper',value=${data.society.ifPaper},isrequired='true')"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">名称：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.paName" id="society_paName" th:field="*{paName}" type="text">
                            </div>
                            <label class="col-sm-2 control-label">频率：</label>
                            <div class="col-sm-2">
                                <input class="form-control" name="society.paFrequency2" id="society_paFrequency2" th:field="*{paFrequency2}" type="text">
                            </div>
                        </div>
                        <div class="form-group">
                            <!--                                <label class="col-sm-2 control-label">其他纸介信息：</label>-->
                            <!--                                <div class="col-sm-4">-->
                            <!--                                    <input class="form-control" name="society.pJournal" id="pJournal" th:field="*{pJournal}" type="text">-->
                            <!--                                </div>-->
                            <label class="col-sm-2 control-label">期刊名称：</label>
                            <div class="col-sm-2">
                                <input class="form-control" name="society.paJournal" id="society_paJournal" th:field="*{paJournal}" type="text">
                            </div>
                        </div>
                        <div class="form-group">
<!--                            <label class="col-sm-2 control-label is-required">费用：</label>-->
<!--                            <div class="col-sm-4">-->
<!--                                <input class="form-control" name="society.cost" id="society_cost" th:field="*{cost}" type="number" required>-->
<!--                            </div>-->
                            <label class="col-sm-2 control-label is-required">缴费频率：</label>
                            <div class="col-sm-2">
                                <input class="form-control" name="society.pay" id="society_pay" th:field="*{pay}" type="text" required>
                            </div>
                        </div>
<!--                        <div class="form-group">-->
<!--                            <label class="col-sm-2 control-label">外汇专项：税：</label>-->
<!--                            <div class="col-sm-4">-->
<!--                                <input class="form-control" name="society.tax" id="society_tax" th:field="*{tax}" type="text">-->
<!--                            </div>-->
<!--                            <label class="col-sm-2 control-label">外汇专项：手续费：</label>-->
<!--                            <div class="col-sm-2">-->
<!--                                <input class="form-control" name="society.charge" id="society_charge" th:field="*{charge}" type="text">-->
<!--                            </div>-->
<!--                        </div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">开户银行：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.societyAccount.bankName" id="society_bankName" th:field="${data.society.societyAccount.bankName}" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">账户名称：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.societyAccount.extra1" id="society_extra1" th:field="${data.society.societyAccount.extra1}" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">账号：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.societyAccount.bankNumber" id="society_bankNumber" th:field="${data.society.societyAccount.bankNumber}" type="text" required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="panel-group" role="tablist" aria-multiselectable="true" th:object="${data.society}">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a data-toggle="collapse" data-parent="#version" href="#3" aria-expanded="false" class="collapsed">宝钢对口联系单位
                            <span class="pull-right"><i class="fa fa-chevron-down"  aria-hidden="true"></i></span>
                        </a>
                    </h4>
                </div>
                <div id="3" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">单位名称：</label>
                            <div class="col-sm-10">
                                <div class="form-control-static"
                                     th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(data.deptCode)}"></div>
                                <input class="form-control" name="society.baosteelDept" th:value="${data.deptCode}" type="hidden">
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">费用所属：</label>
                            <div class="col-sm-4">
                                <!--                                    <div th:include="/component/select :: init(id='society_costType',name='society.costType',businessType='KNXH',dictCode='costType',isfirst=true,value=${data.society.costType},isrequired='true')"></div>-->
                                <div th:include="/component/radio :: init(id='society_costType',name='society.costType',businessType='KNXH',dictCode='costType',value=${data.society.costType},isrequired='true')"></div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">任职：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.extra2" id="society_dutyName" th:field="*{extra2}" type="text" disabled="disabled">
                                <input class="form-control" name="society.duty" id="society_duty" th:field="*{duty}" type="hidden">
                            </div>
                            <label class="col-sm-2 control-label is-required">成员代表：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.dbr" id="society_dbr" th:field="*{dbr}" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">联系人/经办人：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.UserUtil).getUserName(data.society.jbr)}"></div>
                                <input class="form-control" name="society.jbr" id="society_jbr" th:value="*{jbr}" type="hidden">
                            </div>
                            <label class="col-sm-2 control-label is-required">电话：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.jbrTel" id="society_jbrTel" th:field="*{jbrTel}" type="text" required>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-2 control-label">E-mail：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.jbrEmail" id="society_jbrEmail" th:field="*{jbrEmail}" type="text">
                            </div>
                            <label class="col-sm-2 control-label">传真：</label>
                            <div class="col-sm-4">
                                <input class="form-control" name="society.jbrFax" id="society_jbrFax" th:field="*{jbrFax}" type="text">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <th:block th:include="component/wfWorkFlow :: init(workFlow=${data.workFlow})" />
        <th:block th:include="include :: includeTaskHistoryList(instanceId=${data.workFlow.processInstanceId})"></th:block>
    </form>
</div>
<div class="row" style="padding: 15px 0;">
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button type="button" class="btn btn-primary" th:onclick="societyDetail()"><i
                class="fa fa-eye"></i>查看网络学会库
        </button>
        <button type="button" class="btn btn-primary" th:onclick="workFlowProcess([[${data.workFlow.processInstanceId}]])"><i
                class="fa fa-eye"></i>流程跟踪图
        </button>
        <button type="button" class="btn btn-primary"
                onclick="saveHandler()"><i
                class="fa fa-hdd-o"></i>暂存
        </button>
        <th:block th:include="component/wfSubmit:: init(taskId=${data.workFlow.taskId},callback=wfSubmitHandler)"/>
        <button type="button" class="btn btn-danger"
                onclick="closeItem()">
            <i class="fa fa-reply-all"></i>返回
        </button>
    </div>
</div>


<script th:inline="javascript">
    var prefix = ctx + "knxh/societyApply";

    /*流程跟踪图*/
    function workFlowProcess(processInstanceId) {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processInstanceId);
    }

    function societyDetail() {
        $.modal.openTab('网络学会库', ctx + 'knxh/society/KNXHCC', false);
    }

    //暂存
    function saveHandler() {
        var config = {
            url: prefix + "/doSave",
            type: "post",
            dataType: "json",
            data: $("#from01").serialize(),
            beforeSend: function () {
                $.modal.loading("正在处理中，请稍后...");
            },
            success: function (result) {
                if (0 === result.code) {
                    if ($.common.isNotEmpty(result.data)) {
                        if ($.common.isNotEmpty(result.data.applyId)) {
                            $("#applyId").val(result.data.applyId);
                        }
                    }
                    $.modal.alertSuccess('保存成功');
                } else {
                    $.modal.alertError(result.msg)
                }
                $.modal.closeLoading();
            }
        };
        $.ajax(config)
    }

    /*提交流程*/
    function wfSubmitHandler() {
        if ($.validate.form()) {
            var duty = $("#duty").val();
            if ($.common.isEmpty(duty)) {
                $.modal.alertError("请选择任职");
                return;
            }
            var societyType = $("input[name='societyType']:checked").val();
            if ("GY" == societyType) {
                var cost =  $("#cost").val();
                var currency =  $("#currency").val();
                if ($.common.isEmpty(cost)) {
                    $.modal.alertError("请填写年费外汇金额");
                    return;
                }
                if ($.common.isEmpty(currency)) {
                    $.modal.alertError("请选择币种");
                    return;
                }
            } else {
                var costRmb =  $("#costRmb").val();
                if ($.common.isEmpty(costRmb)) {
                    $.modal.alertError("请填写年费金额");
                    return;
                }
            }
            var ifElec = $("input[name='society.ifElec']:checked").val();
            if ("1" == ifElec) {
                var society_intUrl =  $("#society_intUrl").val();
                var society_weekly = $("#society_weekly").val();
                if ($.common.isEmpty(society_intUrl) && $.common.isEmpty(society_weekly)) {
                    $.modal.alertError("电子信息名称网址和期刊至少一个必填");
                    return;
                }
            }
            var ifPaper = $("input[name='society.ifPaper']:checked").val();
            if ("1" == ifPaper) {
                var society_paName =  $("#society_paName").val();
                var society_paJournal = $("#society_paJournal").val();
                if ($.common.isEmpty(society_paName) && $.common.isEmpty(society_paJournal)) {
                    $.modal.alertError("纸介信息名称和期刊至少一个必填");
                    return;
                }
            }
            $.modal.confirm("确认提交吗？", function () {
                $.ajax({
                    url: prefix + "/submitWF",
                    type: "post",
                    dataType: "json",
                    data: $("#from01").serialize(),
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                });

            })
        }
    }

    function doSocietyCallback(society){
        console.log(society);
        $('#society_xhCode').val(society.xhCode);
        $('#society_startYear').val(society.startYear);
        $('#society_socialCreditCode').val(society.socialCreditCode);
        $('#society_societyName').val(society.societyName);
        $('#society_organizer').val(society.organizer);
        $('#society_address').val(society.address);
        $('#society_zipCode').val(society.zipCode);
        $('#society_linkman').val(society.linkman);
        $('#society_linkmanTel').val(society.linkmanTel);
        $('#society_linkmanEmail').val(society.linkmanEmail);
        $('#society_linkmanFax').val(society.linkmanFax);
        $('#society_homepage').val(society.homepage);
        $('#society_intUrl').val(society.intUrl);
        $('#society_weekly').val(society.weekly);
        $('#society_userName').val(society.userName);
        $('#society_passwoerd').val(society.passwoerd);
        $('#society_frequency').val(society.frequency);
        $('#society_paName').val(society.paName);
        $('#society_paJournal').val(society.paJournal);
        $('#society_paFrequency2').val(society.paFrequency2);
        $('#society_cost').val(society.cost);
        $('#society_currency').val(society.currency);
        $('#society_pay').val(society.pay);
        $('#society_tax').val(society.tax);
        $('#society_charge').val(society.charge);
        $('#society_baosteelDept').val(society.baosteelDept);
        $('#society_duty').val(society.duty);
        $('#society_dbr').val(society.dbr);
        $('#society_jbr').val(society.jbr);
        $('#society_jbrTel').val(society.jbrTel);
        $('#society_jbrEmail').val(society.jbrEmail);
        $('#society_jbrFax').val(society.jbrFax);
        // $('#society_societyType').val(society.societyType);
        // $('#society_ifElec').val(society.ifElec);
        // $('#society_ifPaper').val(society.ifPaper);
        // $("#society_costType").val(society.costType);

        $('input:radio[name="society.costType"]').on('ifClicked', function (event) {
            console.log(event);
            console.log("================================================");
        });

        //银行信息
        $('#society_bankName').val(society.societyAccount.bankName);
        $('#society_bankNumber').val(society.societyAccount.bankNumber);
        $('#society_extra1').val(society.societyAccount.extra1);

        $('#societyName').val(society.societyName);
        $('#socialCreditCode').val(society.xhCode);
        $('#organizer').val(society.organizer);
    }



    function doDuty(value) {
        var value2 = $('#duty').find("option:selected").text()
        $('#society_duty').val(value);
        $('#society_dutyName').val(value2);
    }
    function doJbrId(value,value2) {
        $('#society_jbr').val(value);
        $('#society_jbrName').val(value2);
    }

    function onCreditCode(value) {
        $('#society_socialCreditCode').val(value);
    }

    function onSocietyName(value) {
        $('#society_societyName').val(value);
    }

    function onOrganizer(value) {
        $('#society_organizer').val(value);
    }

</script>
</body>
</html>