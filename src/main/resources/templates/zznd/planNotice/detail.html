<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('年度计划启动信息')"/>

    <th:block th:include="include :: baseJs"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-planNotice-add" th:object="${notice}">

        <!--框-->
        <div aria-multiselectable="true" class="panel-group" id="accordion" role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title"><a aria-expanded="false" class="collapsed" data-parent="#version"
                                               data-toggle="collapse" href="#2">基本信息
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i aria-hidden="true" class="fa fa-chevron-down"></i></span></a>
                    </h4>
                </div>

                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                    <div class="panel-body">

                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">流水号：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${need.serialNo}"></div>
                                </div>

                                <label class="col-sm-2 control-label">年度计划号：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${need.yearPlanNo}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">计划年度：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${need.planYear}"></div>
                                </div>

                                <label class="col-sm-2 control-label">计划启动月份：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${need.planMonth}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目名称：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${need.projectName}"></div>
                                </div>
                                <label class="col-sm-2 control-label">项目范围：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/radio :: init(id='projectArea',name='projectArea',
                            businessType='KTTG',dictCode='projectArea',value=${need.projectArea} ,see=true)"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目类型：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/radio :: init(id='projectType',name='projectType',
                            businessType='KTTG',dictCode='projectType',value=${need.projectType} ,see=true)"></div>
                                </div>
                                <label class="col-sm-2 control-label">技术受让方：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${need.srfDeptName}"></div>
                                </div>

                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">受让单位部门：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${need.srfDwdeptName}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">受让方项目负责人：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${need.srfFzrName}"></div>
                                </div>

                                <label class="col-sm-2 control-label">联系电话：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:text="${need.srfTel}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">推广单位部门：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${need.tgfDwdeptName}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">推广方方项目负责人：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${need.tgfFzrName}"></div>
                                </div>

                                <label class="col-sm-2 control-label">联系电话：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:text="${need.tgfXmfzrTel}"></div>
                                </div>
                            </div>
                        </div>
<!--                        <div class="form-group">-->
<!--                            <h4 class="col-sm-2  control-label ">点击<a th:onclick="projectDetail()">此处</a>查看年度计划需求表</h4>-->
<!--                            <div class="col-sm-8">-->
<!--                            </div>-->
<!--                        </div>-->

                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--框end-->
        <div aria-multiselectable="true" class="panel-group" role="tablist" th:if="${!#lists.isEmpty(noticeList)}">
            <div class="panel panel-default">
                <!--1.o头部标签-->
                <div class="panel-heading">
                    <h4 class="panel-title"><a aria-expanded="false" class="collapsed" data-parent="#version"
                                               data-toggle="collapse" href="#jbxx">延期启动年度计划通知
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i aria-hidden="true" class="fa fa-chevron-down"></i></span></a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div aria-expanded="false" class="panel-collapse collapse in" id="jbxx">
                    <div class="panel-body" style="margin-left: 50px;">
                        <table class="layui-table" id="mytable">
                            <thead>
                            <tr>
                                <th style="text-align:center">序号</th>
                                <th style="text-align:center">延期月份</th>
                                <th style="text-align:center">延期或终止原因</th>
                                <th style="text-align:center">操作</th>
                            </tr>
                            </thead>
                            <tbody id="tbody_zj">
                            <tr th:each="cy,iterStat : ${noticeList}">
                                <td align="center" th:text="${iterStat.index+1}"></td>
                                <td align="center" th:text="${cy.changeMonth}"></td>
                                <td align="center" th:text="${cy.changeContent}"></td>
                                <td align="center"><a style="color: #337ab7;"
                                                      th:onclick="showDetail([[${cy.noticeId}]])">查看</a></td>
                            </tr>
                            </tbody>

                        </table>
                    </div>
                </div>
                <!--折叠区域结束-->
            </div>
        </div>
        <div aria-multiselectable="true" class="panel-group" id="accordion2" role="tablist">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        <a class="collapsed" data-toggle="collapse" href="#xmgx">
                            操作选项
                            <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                        </a>
                    </h4>
                </div>
                <div class="panel-collapse collapse in" id="xmgx">
                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">提交选项：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/radio :: init(id='submitChoose',name='submitChoose',callback='showReason',
                                businessType='KTTG',dictCode='submitChoose',value=*{submitChoose} ,see=true)"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row" id="yuefen" style="display: none">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">延期启动月份：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/date::init(id='changeMonth',name='changeMonth',strValue=${notice.changeMonth},format='mm',minView='3',see=true)"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group" id="yanqi" style="display: none">
                            <label class="col-sm-2 control-label">延期启动原因：</label>
                            <div class="col-sm-9">
                                <div class="form-control-static" th:text="${notice.changeContent}"></div>
                            </div>
                        </div>
                        <div class="form-group" id="close" style="display: none">
                            <label class="col-sm-2 control-label">关闭项目原因：</label>
                            <div class="col-sm-9">
                                <div class="form-control-static" th:text="${notice.changeAfterContent}"></div>
                            </div>
                        </div>
                    </div>

                </div>

            </div>
        </div>
        <div class="form-group">

        </div>
    </form>
</div>


<script th:inline="javascript">
    var prefix = ctx + "zznd/planNotice"

    function showReason(str) {
        if (str == '2') {   //延期
            $("#yuefen").show();
            $("#yanqi").show();
            $("#close").hide();
        } else if (str == '3') {  //关闭
            $("#close").show();
            $("#yuefen").hide();
            $("#yanqi").hide();
        } else {
            $("#close").hide();
            $("#yuefen").hide();
            $("#yanqi").hide();
        }
    }

    $(function () {
        if ([[${notice.submitChoose}]] != null && [[${notice.submitChoose}]] != "") {
            if ([[${notice.submitChoose}]] == '2') {
                $("#yuefen").show();
                $("#yanqi").show();
                $("#close").hide();
            } else if ([[${notice.submitChoose}]] == '3') {
                $("#close").show();
                $("#yuefen").hide();
                $("#yanqi").hide();
            }
        }
    })
    function showDetail(guid) {
        var url = ctx+"zznd/planNotice/detail/"+guid;
        $.modal.openTab("年度计划通知信息", url);
    }
</script>
</body>
</html>