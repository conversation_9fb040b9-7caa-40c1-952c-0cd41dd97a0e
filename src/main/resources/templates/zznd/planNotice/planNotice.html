<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('年度计划启动通知列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <div class="row form-group">
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label">项目编号：</label>
                            <div class="col-sm-8">
                                <input class="form-control width100" name="projectNum" type="text">
                            </div>
                        </div>

                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label">项目名称：</label>
                            <div class="col-sm-8">
                                <input class="form-control width100" name="projectName" type="text">
                            </div>
                        </div>

                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label">项目负责人：</label>
                            <div class="col-sm-8">
                                <input class="form-control width100" name="tgfFzrName" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="select-list">
                        <ul>
                            <li style="float: right;">
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>

            <div class="btn-group-sm" id="toolbar" role="group">
               <!-- <a class="btn btn-success" onclick="$.operate.addTab()" >
                    <i class="fa fa-plus"></i> 添加
                </a>
                <a class="btn btn-primary single disabled" onclick="$.operate.editTab()">
                    <i class="fa fa-edit"></i> 修改
                </a>
                <a class="btn btn-danger multiple disabled" onclick="$.operate.removeAll()" >
                    <i class="fa fa-remove"></i> 删除
                </a>
                <a class="btn btn-warning" onclick="doStart()" >
                    <i class="fa fa-download"></i> 触发
                </a>-->
            </div>
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table"></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zznd/planNotice";
        var statusDatas = [[${@dict.getDictList('KTTG','projectStatus')}]];
        var choose = [[${@dict.getDictList('KTTG','submitChoose')}]];
        $(function() {
            var options = {
                url: prefix + "/queryList",
                createUrl: prefix + "/add",
                updateUrl: prefix + "/edit/{id}",
                removeUrl: prefix + "/remove",
                detailUrl: prefix + "/detail/{id}",
                modalName: "年度计划启动通知",
                columns: [
                    {
                        field: 'noticeId',
                        visible: false
                    },
                    {
                        field: 'projectNum',
                        title: '项目编号'
                    },
                    {
                        field: 'projectName',
                        title: '项目名称'
                    },
                    {
                        field: 'tgfFzrName',
                        title: '项目负责人'
                    },
                    {
                        field: 'srfDeptName',
                        title: '受让单位部门'
                    },
                    {
                        field: 'tgfDeptName',
                        title: '推广单位部门'
                    },
                    {
                        field: 'projectStatus',
                        title: '项目状态',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(statusDatas,value);
                        }
                    },
                    {
                        field: 'submitChoose',
                        title: '提交选项',
                        formatter: function (value, row, index) {
                            return $.table.selectDictLabel(choose,value);
                        }
                    },
                    {
                        field: 'currentActivityName',
                        title: '状态'
                    },
                    {
                        field: 'currentOperator',
                        title: '当前操作人'
                    },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function(value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.noticeId + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        return actions.join('');
                    }
                }]
            };
            $.table.init(options);
        });

        function doStart(){
            $.modal.confirm("确认提交吗？", function () {
                if ($.validate.form()) {
                    $.operate.saveTabAlert(ctx+"zznd/planNotice/doStart");
                }
            })
        }
    </script>
</body>
</html>