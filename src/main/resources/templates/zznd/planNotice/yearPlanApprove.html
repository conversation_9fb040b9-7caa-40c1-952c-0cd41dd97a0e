<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('年度计划启动通知审批')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-planNotice-add" th:object="${notice}">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="noticeId" name="noticeId"  th:value="${notice.noticeId}" type="hidden">
            <input id="planId" name="planId"  th:value="${notice.planId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">

            <!--框-->
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#2" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">

            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">流水号：</label>
                    <div class="col-sm-4">
                        <div  class="form-control-static" th:utext="${need.serialNo}"></div>
                    </div>

                    <label class="col-sm-2 control-label">年度计划号：</label>
                    <div class="col-sm-4">
                        <div  class="form-control-static" th:utext="${need.yearPlanNo}"></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">计划年度：</label>
                    <div class="col-sm-4">
                        <div  class="form-control-static" th:utext="${need.planYear}"></div>
                    </div>

                    <label class="col-sm-2 control-label">计划启动月份：</label>
                    <div class="col-sm-4">
                        <div  class="form-control-static" th:utext="${need.planMonth}"></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">项目名称：</label>
                    <div class="col-sm-4">
                        <div  class="form-control-static" th:utext="${need.projectName}"></div>
                    </div>
                    <label class="col-sm-2 control-label">项目范围：</label>
                    <div class="col-sm-4">
                        <div th:include="/component/radio :: init(id='projectArea',name='projectArea',
                            businessType='KTTG',dictCode='projectArea',value=${need.projectArea} ,see=true)"></div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="form-group">
                    <label class="col-sm-2 control-label">项目类型：</label>
                    <div class="col-sm-4">
                        <div th:include="/component/radio :: init(id='projectType',name='projectType',
                            businessType='KTTG',dictCode='projectType',value=${need.projectType} ,see=true)"></div>
                    </div>
                    <label class="col-sm-2 control-label">技术受让方：</label>
                    <div class="col-sm-4">
                        <div  class="form-control-static" th:utext="${need.srfDeptName}"></div>
                    </div>

                </div>
            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">受让单位部门：</label>
                                    <div class="col-sm-4">
                                        <div  class="form-control-static" th:utext="${need.srfDwdeptName}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">受让方项目负责人：</label>
                                    <div class="col-sm-4">
                                        <div  class="form-control-static" th:utext="${need.srfFzrName}"></div>
                                    </div>

                                    <label class="col-sm-2 control-label">联系电话：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:text="${need.srfTel}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">推广单位部门：</label>
                                    <div class="col-sm-4">
                                        <div  class="form-control-static" th:utext="${need.tgfDwdeptName}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">推广方方项目负责人：</label>
                                    <div class="col-sm-4">
                                        <div  class="form-control-static" th:utext="${need.tgfFzrName}"></div>
                                    </div>

                                    <label class="col-sm-2 control-label">联系电话：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:text="${need.tgfXmfzrTel}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="form-group">
                                <h4 class="col-sm-2  control-label ">点击<a th:onclick="projectDetail()">此处</a>查看年度计划需求表</h4>
                                <div class="col-sm-8">
                                </div>
                            </div>

                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->
            <div aria-multiselectable="true" class="panel-group" role="tablist" th:if="${!#lists.isEmpty(noticeList)}">
                <div class="panel panel-default">
                    <!--1.o头部标签-->
                    <div class="panel-heading">
                        <h4 class="panel-title"><a aria-expanded="false" class="collapsed" data-parent="#version"
                                                   data-toggle="collapse" href="#jbxx">延期启动年度计划通知
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i aria-hidden="true" class="fa fa-chevron-down"></i></span></a>
                        </h4>
                    </div>
                    <!--折叠区域-->
                    <div aria-expanded="false" class="panel-collapse collapse in" id="jbxx">
                        <div class="panel-body" style="margin-left: 50px;">
                            <table class="layui-table" id="mytable">
                                <thead>
                                <tr>
                                    <th style="text-align:center">序号</th>
                                    <th style="text-align:center">延期月份</th>
                                    <th style="text-align:center">延期或终止原因</th>
                                    <th style="text-align:center">操作</th>
                                </tr>
                                </thead>
                                <tbody id="tbody_zj">
                                <tr th:each="cy,iterStat : ${noticeList}">
                                    <td align="center" th:text="${iterStat.index+1}"></td>
                                    <td align="center" th:text="${cy.changeMonth}"></td>
                                    <td align="center" th:text="${cy.changeContent}"></td>
                                    <td align="center"><a style="color: #337ab7;"
                                                          th:onclick="showDetail([[${cy.noticeId}]])">查看</a></td>
                                </tr>
                                </tbody>

                            </table>
                        </div>
                    </div>
                    <!--折叠区域结束-->
                </div>
            </div>
            <div class="panel-group" id="accordion2"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmgx" class="collapsed">
                                操作选项
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmgx" class="panel-collapse collapse in">
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">提交选项：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/radio :: init(id='submitChoose',name='submitChoose',callback='showReason',
                                businessType='KTTG',dictCode='submitChoose',value=*{submitChoose} ,see=true)"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row" id="yuefen" style="display: none">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">延期启动月份：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/date::init(id='changeMonth',name='changeMonth',strValue=${notice.changeMonth},format='mm',minView='3',see=true)"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-group" id="yanqi" style="display: none">
                                <label class="col-sm-2 control-label">延期启动原因：</label>
                                    <div class="form-control-static" th:text="${notice.changeContent}"></div>
                            </div>
                            <div class="form-group" id="close" style="display: none">
                                <label class="col-sm-2 control-label">关闭项目原因：</label>
                                    <div class="form-control-static" th:text="${notice.changeAfterContent}"></div>
                            </div>
                        </div>

                    </div>

                </div>
            </div>
            <!-- 审批-->
            <th:block th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=${processInstanceId},required='true',freeFlow='false')"></th:block>

        </form>
        <div class="m">
            <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
        </div>
    </div>
    <div class="row">
		<div class="toolbar toolbar-bottom" role="toolbar" >

            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

            <th:block th:include="component/wfReturn:: init(taskId=${taskId},callback=wfReturn)"/>
			&nbsp;
			<button type="button" class="btn btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返回
			</button>
		</div>
	</div>
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="component/wfCommentList::init(processInstanceId=${processInstanceId})"></div>


    <script th:inline="javascript">
        var prefix = ctx + "zznd/planNotice"

        $("#form-planNotice-add").validate({
            focusCleanup: true
        });

        function saveHandler() {
            $.operate.saveTab(prefix + "/addNotice", $('#form-planNotice-add').serialize());
        }
        function submitHandler(){
            $.modal.confirm("确认提交吗？", function () {
                if ($.validate.form()) {
                    $.operate.saveTabAlert(prefix + "/submitWF",$('#form-planNotice-add').serialize());
                }
            })
        }

        //流程跟踪
        function workFlowProcess() {
            window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + [[${processInstanceId}]]);
        }

        function showReason(str) {
            if(str=='2'){   //延期
                $("#yuefen").show();
                $("#yanqi").show();
                $("#close").hide();
            }else if(str=='3'){  //关闭
                $("#close").show();
                $("#yuefen").hide();
                $("#yanqi").hide();
            }else{
                $("#close").hide();
                $("#yuefen").hide();
                $("#yanqi").hide();
            }
        }
        $(function () {
            if([[${notice.submitChoose}]]!=null&&[[${notice.submitChoose}]]!="") {
                if ([[${notice.submitChoose}]] == '2') {
                    $("#yuefen").show();
                    $("#yanqi").show();
                    $("#close").hide();
                } else if ([[${notice.submitChoose}]] == '3') {
                    $("#close").show();
                    $("#yuefen").hide();
                    $("#yanqi").hide();
                }
            }
        })

        //退回流程
        function wfReturn(activityKey) {
            if ($.validate.form()) {
                var data = $('#form-planNotice-add').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
            }
        }

        function showDetail(guid) {
            var url = ctx+"zznd/planNotice/detail/"+guid;
            $.modal.openTab("年度计划通知信息", url);
        }

    </script>
</body>
</html>