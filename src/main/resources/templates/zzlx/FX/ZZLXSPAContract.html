<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增项目合同')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="form-group" th:include="include :: step(approveKind='ZZLX_XMLX',currentNode=${activityCode})"></div><br/>

        <form class="form-horizontal m" id="form-teachContract-add" th:object="${contract}">
            <input id="bizId" name="bizId"  th:value="${businessGuid}" type="hidden">
            <input id="conId" name="conId"  th:value="${contract.conId}" type="hidden">
            <input id="mainId" name="mainId"  th:value="${main.mainId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
            <input id="comment" name="comment"  type="hidden">


            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#2" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">

                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">合同名称：</label>
                                    <div class="col-sm-4">
                                        <input th:if="${contract.conHtName==null||contract.conHtName==''}" name="conHtName" th:value="${main.projectName}" class="form-control" type="text" required>
                                        <input th:if="${contract.conHtName!=null&&contract.conHtName!=''}" name="conHtName" th:value="${contract.conHtName}" class="form-control" type="text" required>
                                    </div>
                                    <label class="col-sm-2 control-label">合同编号：</label>
                                    <div class="col-sm-4">
                                        <input name="conHtNum"  th:value="${main.projectHtNum}" type="hidden">
                                        <div class="form-control-static" th:text="${main.projectHtNum}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">项目范围：</label>
                                    <div class="col-sm-4">
                                        <div th:include="/component/radio :: init(id='projectArea',name='projectArea',
                                        businessType='KTTG',dictCode='projectArea',value=${main.projectArea} ,see=true)"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">项目名称：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${main.projectName}"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">项目编号：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">受让单位部门：</label>
                                    <div class="col-sm-4">
                                        <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.srfDeptCode)}"></div>
                                    </div>

                                    <label class="col-sm-2 control-label">推广单位部门：</label>
                                    <div class="col-sm-4">
                                        <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.tgfDeptCode)}"></div>
                                    </div>

                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">签订地点：</label>
                                    <div class="col-sm-4">
                                        <input name="conSite" th:value="${contract.conSite}" class="form-control" type="text" required>
                                    </div>
                                    <label class="col-sm-2 control-label is-required">签订日期：</label>
                                    <div class="col-sm-4" th:include="/component/date :: init(name='conDate',id='conDate',strValue=${contract.conDate})"></div>

                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">有效期限：</label>
                                    <div class="col-sm-4" th:if="${contract.expiryDate==null || contract.expiryDate==''}">
                                        <div th:include="/component/date :: init(name='expiryDate',id='expiryDate',strValue=${teachAtt.prostartDate})">
                                        </div>
                                    </div>
                                    <div class="col-sm-4" th:if="${contract.expiryDate!=null && contract.expiryDate!=''}">
                                        <div th:include="/component/date :: init(name='expiryDate',id='expiryDate',strValue=${contract.expiryDate})">
                                        </div>
                                    </div>
                                    <label class="col-sm-2 control-label is-required">至</label>
                                    <div class="col-sm-4" th:if="${contract.beforeDate==null || contract.beforeDate==''}">
                                        <div th:include="/component/date :: init(name='beforeDate',id='beforeDate',strValue=${teachAtt.proendDate})">
                                        </div>
                                    </div>
                                    <div class="col-sm-4" th:if="${contract.beforeDate!=null && contract.beforeDate!=''}">
                                        <div th:include="/component/date :: init(name='beforeDate',id='beforeDate',strValue=${contract.beforeDate})">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">项目所属分类：</label>
                                    <div class="col-sm-4">
                                        <div th:include="/component/radio :: init(id='payKind',name='payKind',
                                        businessType='KTTG',dictCode='payKind',value=${teachFix.payKind} ,see=true)"></div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>

            <!--框-->
            <div class="panel-group" id="accordion2" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#fj" aria-expanded="false" class="collapsed">项目材料
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="fj" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="form-group">
                                <div th:if="${main.projectSource eq 'gdxqsb'}" class="col-sm-2 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',ctx+'zzlx/need/detailM?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                                <div th:if="${main.projectSource eq 'ndjh'}" class="col-sm-2 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',[[${@environment.getProperty('app-context.ctxKY')}]] +'web/KYKTTGPD01?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 ">点击<a class="form_list_a" th:onclick="$.modal.openTab('技术附件通知书',ctx+'zzlx/teachAtt/openAtt/'+[[${main.mainId}]])">此处</a>查看技术附件</label>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-12">点击<a class="form_list_a" th:onclick="$.modal.openTab('定价评审表',ctx+'zzlx/teachFix/openFix/'+[[${main.mainId}]])">此处</a>定价审批表</label>
                            </div>
                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->

            <!--框-->
            <div class="panel-group" id="accordion3" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#htfj" aria-expanded="false" class="collapsed">合同文本
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="htfj" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">附件上传：</label>
                                <div class="col-sm-10">
                                    <div th:include="/component/attachment :: init(display='none',name='htfjId',id='htfjId',sourceId=${contract.conId},sourceModule='HTFJ')">
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->
        </form>
        <div class="m">
            <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
        </div>
    </div>
    <div class="row">
		<div class="toolbar toolbar-bottom" role="toolbar" >

			<button type="button" class="btn btn-primary"
				onclick="saveHandler()">
				<i class="fa fa-check"></i>暂 存
			</button>

			<th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

            <th:block th:include="/component/wfReturn :: init(taskId=${taskId},callback=wfReturn)"/>

            <button type="button" class="btn btn-primary"
                    onclick="exportContract()">
                <i class="fa fa-check"></i>合同打印
            </button>
			<button type="button" class="btn btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返回
			</button>
		</div>
	</div>
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="component/wfCommentList::init(processInstanceId=${processInstanceId})"></div>


    <script th:inline="javascript">
        var prefix = ctx + "zzlx/teachContract"

        $("#form-teachContract-add").validate({
            focusCleanup: true
        });
        $("input[name='conDate']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
        $("input[name='expiryDate']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
        $("input[name='beforeDate']").datetimepicker({
            format : "yyyy-mm-dd",
            minView : "month",
            autoclose : true
        }).on('keypress paste', function (e) {
            e.preventDefault();
            return false;
        });
        function saveHandler() {
            var config = {
                url: prefix + "/insert",
                type: "post",
                dataType: "json",
                data: $('#form-teachContract-add').serialize(),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $("#conId").val(result.data.conId);
                    $.modal.alertSuccess(result.msg);
                    $.modal.closeLoading();
                }
            };
            $.ajax(config)
        }
        function submitHandler() {
            $.modal.confirm("确认提交吗？", function () {
                if ($.validate.form()) {
                    $.operate.saveTabAlert(prefix + "/submitWF", $('#form-teachContract-add').serialize());
                }
            })
        }

        /**
         * 打印合同
         */
        function exportContract() {
            var bizId = $("#bizId").val();
            var url = prefix + "/exportContract/"+bizId;
            window.open(url);
        }
        //流程跟踪
        function workFlowProcess() {
            window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + [[${processInstanceId}]]);
        }


        //点击查看技术附件
        function attDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "技术附件",
                content: ctx + "zzlx/teachAtt/openAtt/"+[[${businessGuid}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }

        //点击查看定价评审表
        function fixDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "定价评审",
                content: ctx + "zzlx/teachFix/openFix/"+[[${businessGuid}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }

        //退回流程
        function wfReturn(activityKey) {
            var data = $('#form-teachContract-add').serialize();
            data += "&activityKey=" + activityKey;
            var config = {
                url: ctx + "mpwf/flowInfo/returnFlow",
                type: "post",
                dataType: "json",
                data: data,
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $.operate.alertSuccessTabCallback(result);
                }
            };
            $.ajax(config)
        }

    </script>
</body>
</html>