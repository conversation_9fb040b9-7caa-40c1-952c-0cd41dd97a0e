<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增关键技术输出')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: summernote-js"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <form class="form-horizontal m" id="form-kettechJs-add" th:object="${kettechJsEx}">
        <input th:field="*{needId}" type="hidden">
        <input th:field="*{mainId}" type="hidden">
        <input th:field="*{tgfFzrCode}" type="hidden">
        <input id="tgfXmzgCode" name="tgfXmzgCode" th:value="${needEx.tgfXmzgCode}" type="hidden">
        <input id="srfXmzgCode" name="srfXmzgCode" th:value="${needEx.srfXmzgCode}" type="hidden">
        <input id="srfDwdeptCode" name="srfDwdeptCode" th:value="${needEx.srfDwdeptCode}" type="hidden">
        <input id="tgfDwdeptCode" name="tgfDwdeptCode" th:value="${needEx.tgfDwdeptCode}" type="hidden">
        <input id="processInstanceId" name="processInstanceId" th:value="${processInstanceId}" type="hidden">
        <input id="activityCode" name="activityCode" th:value="${activityCode}" type="hidden">
        <input id="taskId" name="taskId" th:value="${taskId}" type="hidden">
        <input id="businessGuid" name="businessGuid" th:value="${businessGuid}" type="hidden">
        <!-- 基本信息 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#1">基本信息
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                <div class="panel-body">

                    <div class="row form-group">
                        <label class="col-sm-2 control-label">项目名称：</label>
                        <div class="form-control-static col-sm-10" th:text="*{projectName}"></div>
                    </div>

                    <div class="row form-group">
                        <label class="col-sm-2 control-label">受让单位部门：</label>
                        <div class="col-sm-10" th:include="/component/selectOrg :: init(orgCodeId='srfDeptCode',orgNameId='srfDeptName',value=*{srfDeptCode},see=true)"></div>
                    </div>

                    <div th:if="${activityCode} eq 'Manual11'">
                        <div class="row form-group">
                            <label class="col-sm-2 control-label">推广单位部门：</label>
                            <div class="col-sm-10" th:include="/component/selectOrg :: init(orgCodeId='tgfDeptCode',orgNameId='tgfDeptName',value=*{tgfDeptCode},see=true)"></div>
                        </div>

                        <div class="row form-group">
                            <label class="col-sm-2 control-label">推广方项目负责人：</label>
                            <div th:include="/component/selectUser :: init(userCodeId='fzrCode',userNameId='fzrName',value=*{fzrCode},divClass='col-sm-10',see=true)"></div>
                        </div>

                        <div class="row form-group">
                            <label class="col-sm-2 control-label">联系电话：</label>
                            <div class="form-control-static col-sm-10" th:text="${needEx.tgfXmfzrTel}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <label class="col-sm-2 control-label">技术类别：</label>
                        <div class="col-sm-10" th:include="/component/checkbox :: init(id='techKind',name='techKind',businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='techKind',value=*{techKind},isrequired=true,see=true)"></div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-2"></div>
                        <label class="col-sm-10">点击<a class="form_list_a" th:onclick="$.modal.openTab('查看推广需求详情',ctxZZZC+'zzlx/need/detail?needId='+[[*{needId}]])">此处</a>查看推广需求表</label>
                    </div>
                </div>
            </div>
        </div>
        <!-- 该技术知识产权状况 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#2">该技术知识产权状况
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">是否申请专利：</label>
                            <div th:include="/component/radio :: init(id='isSqzl', name='isSqzl',businessType=*{constants.BUSINESS_TYPE_MPTY}, dictCode=*{constants.IS_YES_NO},value=*{isSqzl},isrequired=true,see=true)"></div>
                        </div>
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">是否审定技术秘密：</label>
                            <div th:include="/component/radio :: init(id='isJsmm', name='isJsmm',businessType=*{constants.BUSINESS_TYPE_MPTY}, dictCode=*{constants.IS_YES_NO},value=*{isJsmm},isrequired=true,see=true)"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="mnote-editor-title"><span class="txt-impt">*</span>具体专利、技术秘密名称 （分别表明）</div>
                        <div class="mnote-editor-box">
                            <div class="jtzljsmmmc" th:utext="*{jtzljsmmmc}"></div>
                        </div>
                    </div>


                    <div class="row form-group">
                        <label class="col-sm-2 control-label">现有技术水平：</label>
                        <div class="col-sm-10" th:include="/component/radio :: init(id='xyjssp',name='xyjssp',businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode=*{constants.XYJSSP},value=*{xyjssp},isrequired=true,see=true)"></div>
                    </div>

                    <div class="row form-group">
                        <div class="mnote-editor-title"><span class="txt-impt">*</span>应用情况（包括应用地点 及效果）</div>
                        <div class="mnote-editor-box">
                            <div class="yyqk" th:utext="*{yyqk}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <label class="col-sm-2 control-label">国内外是否具有同类技术：</label>
                        <div th:include="/component/radio :: init(id='isSametech', name='isSametech',businessType=*{constants.BUSINESS_TYPE_MPTY}, dictCode=*{constants.IS_YES_NO},value=*{isSametech},isrequired=true,see=true)"></div>
                    </div>

                    <div class="row form-group">
                        <div class="mnote-editor-title"><span class="txt-impt">*</span>预输出技术与国内外同类技术比较</div>
                        <div class="mnote-editor-box">
                            <div class="yscjsgnwbj" th:utext="*{yscjsgnwbj}"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 该项技术的储备情况 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#3">该项技术的储备情况
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="3">
                <div class="panel-body">

                    <div class="row form-group">
                        <label class="col-sm-2 control-label">该项技术的储备情况：</label>
                        <div th:include="/component/radio :: init(id='techsave', name='techsave',businessType=*{constants.BUSINESS_TYPE_MPTY}, dictCode=*{constants.IS_YES_NO},value=*{techsave},isrequired=true,see=true)"></div>
                    </div>

                    <div class="row form-group">
                        <div class="mnote-editor-title"><span class="txt-impt">*</span>新的技术储备情况</div>
                        <div class="mnote-editor-box">
                            <div class="xjscbqk" th:utext="*{xjscbqk}"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 需求方产品定位、产线规划情况 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#4">需求方产品定位、产线规划情况
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="4">
                <div class="panel-body">

                    <div class="row form-group">
                        <div class="mnote-editor-title"><span class="txt-impt">*</span>产品定位情况</div>
                        <div class="mnote-editor-box">
                            <div class="cpdyqk" th:utext="*{cpdyqk}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="mnote-editor-title"><span class="txt-impt">*</span>产线规划情况</div>
                        <div class="mnote-editor-box">
                            <div class="cpghqk" th:utext="*{cpghqk}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <label class="col-sm-3 control-label">输出是否能构成对公司的竞争威胁：</label>
                        <div th:include="/component/radio :: init(id='isDanger', name='isDanger',businessType=*{constants.BUSINESS_TYPE_KTTG}, dictCode=*{constants.IS_DANGER},value=*{isDanger},isrequired=true,see=true)"></div>
                    </div>

                    <div class="row form-group">
                        <div class="mnote-editor-title"><span class="txt-impt">*</span>具体理由</div>
                        <div class="mnote-editor-box">
                            <div class="jtly" th:utext="*{jtly}"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 综合评审意见 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#5">综合评审意见
                        <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="5">
                <div class="panel-body">

                    <div class="row form-group">
                        <label class="col-sm-2 control-label">是否准预输出：</label>
                        <input th:field="*{isAgreeput}" type="hidden">
                        <div th:include="/component/radio :: init(id='isAgreeput', name='isAgreeput',businessType=*{constants.BUSINESS_TYPE_KTTG}, dictCode=*{constants.IS_AGREE_PUT},value=*{isAgreeput},isrequired=true,see=true)"></div>
                    </div>

                    <div class="row form-group">
                        <div class="mnote-editor-title"><span class="txt-impt">*</span>结论陈述</div>
                        <div class="mnote-editor-box">
                            <div class="jlcs" th:utext="*{jlcs}"></div>
                        </div>
                    </div>
                    <div class="row form-group">
                        <div class="mnote-editor-title"><span class="txt-impt">*</span>相关附件</div>
                        <div class="mnote-editor-box">
                            <div th:include="/component/attachment :: init(display='none',name=*{constants.ATT_KET_TECH},id=*{constants.ATT_KET_TECH},sourceId=*{jsssId},sourceModule=*{constants.BUSINESS_TYPE},sourceLabel1=*{constants.ATT_KET_TECH},see=true)">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group" th:if="*{! #strings.isEmpty(isGsld)}">
                        <label class="col-sm-2 control-label">是否请示公司领导：</label>
                        <div th:include="/component/radio :: init(id='isGsld', name='isGsld',businessType=*{constants.BUSINESS_TYPE_MPTY}, dictCode=*{constants.IS_YES_NO},value=*{isGsld},isrequired=true,see=true)"></div>
                    </div>
                </div>
            </div>
        </div>
        <!--专家评审信息-->
        <div class="panel-body" th:if="${showPS ne 'false'}">
            <div class="form-group" th:include="/component/expertReview :: init(bizId=*{jsssId},moduleCode='zzlx_kettechJs')">
            </div>
        </div>
        <!--专家评审信息end-->
        <!-- 审批-->
        <div th:if="${! #strings.isEmpty(processInstanceId)}" th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=${processInstanceId},required='true',freeFlow='false')"></div>
    </form>
</div>
<div class="row">
    <div class="toolbar toolbar-bottom" role="toolbar" >
        <button class="btn btn-primary" th:onclick="openProcessTrack([[${kettechJsEx.processInstanceId}]])" type="button">
            <i class="fa fa-eye"></i>&nbsp;流程跟踪图
        </button>
<!--        <th:block th:include="/component/wfSubmit :: init(taskId=${taskId},callback='submitProcess')"></th:block>-->
<!--        <th:block th:if="${! #strings.isEmpty(taskId) && activityCode ne 'Manual13'}" th:include="/component/wfReturn :: init(taskId=${taskId},callback=wfReturn)"/>-->
        <button class="btn btn-danger" onclick="closeItem()" type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>
<!-- 审批历史 -->
<div th:if="${! #strings.isEmpty(processInstanceId)}" th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})"></div>

<script th:inline="javascript">
    var prefix = ctx + "zzlx/kettechJs"

    $("#form-kettechJs-add").validate({
        focusCleanup: true
    });


    /**
     * 提交流程
     */
    function submitProcess() {
        if ($.validate.form()) {
            $.modal.confirm("确认提交吗？", function () {
                var data = $('#form-kettechJs-add').serialize();
                $.operate.saveTabAlert(ctx + "zzlx/need/submitProcess", data);
            });
        }
    }

    //退回流程
    function wfReturn(activityKey) {
        if ($.validate.form()) {
            var data = $('#form-kettechJs-add').serialize() + "&activityKey=" + activityKey;
            $.operate.saveTabAlert(ctx + "mpwf/flowInfo/returnFlow", data);
        }
    }

    //流程跟踪
    function openProcessTrack(processInstanceId) {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processInstanceId);
    }

</script>
</body>
</html>