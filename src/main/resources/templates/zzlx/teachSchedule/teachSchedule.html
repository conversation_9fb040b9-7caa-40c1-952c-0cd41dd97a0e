<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('【知识产权推广应用统计】列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="search-collapse">
            <form id="formId"  class="form-horizontal" style="width: 99%;">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目编号:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectNumLike" placeholder="支持模糊查询"/>
                        </div>
                        <label class="col-sm-2 control-label">项目名称:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectNameLike" placeholder="支持模糊查询"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">知识产权分类:</label>
                        <div class="col-sm-4">
                            <th:block
                                      th:include="/component/select :: init(id='deType', name='deType',businessType='KTTG', dictCode='deType',isfirst=true)"></th:block>
                        </div>
                        <label class="col-sm-2 control-label">知识产权编号:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="numberLike" placeholder="支持模糊查询"/>
                        </div>
                    </div>


                <div class="form-group">
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-4">
                    </div>
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-4">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                            <i class="fa fa-search"></i>
                            &nbsp;搜索
                        </a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                            <i class="fa fa-refresh"></i>
                            &nbsp;重置
                        </a>
                    </div>
                </div>
            </form>
        </div>

                <div class="btn-group-sm" id="toolbar" role="group">
                    <a class="btn btn-warning" onclick="$.table.exportExcel()">
                        <i class="fa fa-download"></i> 导出
                    </a>
                </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzlx/teachSchedule";

    $(function () {
        var options = {
            url: prefix + "/reportList",
            createUrl: prefix + "/add",
            exportUrl: prefix + "/exportExcel",
            modalName: "知识产权推广应用统计",
            columns: [
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'deType',
                    title: '知识产权分类'
                },
                {
                    field: 'number',
                    title: '知识产权编号'
                },
                {
                    field: 'deName',
                    title: '知识产权名称'
                },
                {
                    field: 'ratio',
                    title: '知识产权贡献度'
                }
                ]
        };
        $.table.init(options);
    });

    function shuchu(obj){
        if ($(obj).prop("checked")) {
            $.table.showColumn(obj.id);
        } else {
            $.table.hideColumn(obj.id);
        }
    }

</script>
</body>
</html>