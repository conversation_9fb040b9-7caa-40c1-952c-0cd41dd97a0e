<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('【推广项目新增知识产权统计】列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="search-collapse">
            <form id="formId"  class="form-horizontal" style="width: 99%;">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目编号:</label>
                        <div class="col-sm-4">
                            <input class="form-control" type="text" name="fromNo" placeholder="支持模糊查询"/>
                        </div>
                    </div>
                <div class=" form-group">
                    <label class="col-sm-2 control-label">项目立项日期：</label>
                    <div class="col-sm-8">
                        <div style="width: 45%;display:inline-block;">
                            <th:block th:include="/component/date::init(name='projectLxDateMin',id='projectLxDateMin')"/>
                        </div>
                        <span>~</span>
                        <div style="width: 45%;display:inline-block;">
                            <th:block th:include="/component/date::init(name='projectLxDateMax',id='projectLxDateMax')"/>
                        </div>
                    </div>
                </div>
                <div class=" form-group">
                    <label class="col-sm-2 control-label">产权生成日期：</label>
                    <div class="col-sm-8">
                        <div style="width: 45%;display:inline-block;">
                            <th:block th:include="/component/date::init(name='confirmTimeMin',id='confirmTimeMin')"/>
                        </div>
                        <span>~</span>
                        <div style="width: 45%;display:inline-block;">
                            <th:block th:include="/component/date::init(name='confirmTimeMax',id='confirmTimeMax')"/>
                        </div>
                    </div>
                </div>


                <div class="form-group">
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-4">
                    </div>
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-4">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                            <i class="fa fa-search"></i>
                            &nbsp;搜索
                        </a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                            <i class="fa fa-refresh"></i>
                            &nbsp;重置
                        </a>
                    </div>
                </div>
            </form>
        </div>

                <div class="btn-group-sm" id="toolbar" role="group">
                    <a class="btn btn-warning" onclick="$.table.exportExcel()">
                        <i class="fa fa-download"></i> 导出
                    </a>
                </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzlx/teachSchedule";

    $(function () {
        var options = {
            url: prefix + "/report",
            createUrl: prefix + "/add",
            exportUrl: prefix + "/exportSchedule",
            firstLoad: false,
            modalName: "推广项目新增知识产权统计",
            columns: [
                {
                    field: 'fromNo',
                    title: '来源编号'
                },
                {
                    field: 'fromName',
                    title: '来源名称'
                },
                {
                    field: 'confirmTime',
                    title: '认定/受理时间'
                },
                {
                    field: 'tecType',
                    title: '知识产权类别'
                },
                {
                    field: 'patentNo',
                    title: '知识产权编号'
                },
                {
                    field: 'applyName',
                    title: '知识产权名称'
                }
                ]
        };
        $.table.init(options);
    });

    function shuchu(obj){
        if ($(obj).prop("checked")) {
            $.table.showColumn(obj.id);
        } else {
            $.table.hideColumn(obj.id);
        }
    }

</script>
</body>
</html>