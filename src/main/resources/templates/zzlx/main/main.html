<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('【请填写功能名称】列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="search-collapse">
            <form id="formId"  class="form-horizontal" style="width: 99%;">
                <div th:if="${queryType eq 'comprehensive'}">
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目编号:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectNumLike" placeholder="支持模糊查询"/>
                        </div>
                        <label class="col-sm-2 control-label">合同编号:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectHtNumLike" placeholder="支持模糊查询"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">提出人:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="createUserLabelLike" placeholder="支持模糊查询"/>
                        </div>
                        <label class="col-sm-2 control-label">项目类型:</label>
                        <div class="col-sm-4">
                            <div th:include="/component/select :: init(name='projectType',businessType='KTTG',dictCode='projectType',isfirst=true)"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目范围:</label>
                        <div class="col-sm-4">
                            <div th:include="/component/radio :: init(name='projectArea',businessType='KTTG',dictCode='projectArea')"></div>
                        </div>
                        <label class="col-sm-2 control-label">项目来源:</label>
                        <div class="col-sm-4">
                            <div th:include="/component/radio :: init(name='projectSource',businessType='KTTG',dictCode='projectSource')"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目名称:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectNameLike" placeholder="支持模糊查询"/>
                        </div>
                        <label class="col-sm-2 control-label">合同名称:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectHtNameLike" placeholder="支持模糊查询"/>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目负责人:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="tgfFzrNameLike" placeholder="支持模糊查询"/>
                        </div>
                        <label class="col-sm-2 control-label">项目负责单位:</label>
                        <div class="col-sm-4">
                            <div th:include="/component/selectOrg :: init(orgCodeId='tgfDwdeptCode',orgNameId='tgfDwdeptName',selectType='S')"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">受让方负责人:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="srfFzrNameLike" placeholder="支持模糊查询"/>
                        </div>
                        <label class="col-sm-2 control-label">技术受让单位:</label>
                        <div class="col-sm-4">
                            <div th:include="/component/selectOrg :: init(orgCodeId='srfDwdeptCode',orgNameId='srfDwdeptName',selectType='S')"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目主管人:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="projectZgNameLike" placeholder="支持模糊查询"/>
                        </div>
                        <label class="col-sm-2 control-label">流水号:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="serialNoLike" placeholder="支持模糊查询"/>
                        </div>
                    </div>
                   <!-- <div class="form-group">
                        <label class="col-sm-2 control-label">推广方成员:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="tgfMember"/>
                        </div>
                        <label class="col-sm-2 control-label">受让方成员:</label>
                        <div class="col-sm-4">
                            <input type="text" class="form-control" name="srfMember"/>
                        </div>
                    </div>-->
                    <div class="row col-sm-12">
                        <div class="col-sm-6 form-group">
                            <label class="col-sm-4 control-label">项目开始日期：</label>
                            <div class="col-sm-8">
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectStartDateMin',id='projectStartDateMin')"/>
                                </div>
                                <span>~</span>
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectStartDateMax',id='projectStartDateMax')"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 form-group">
                            <label class="col-sm-4 control-label">项目结束时间：</label>
                            <div class="col-sm-8">
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectEndDateMin',id='projectEndDateMin')"/>
                                </div>
                                <span>~</span>
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectEndDateMax',id='projectEndDateMax')"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row col-sm-12">
                        <div class="col-sm-6 form-group">
                            <label class="col-sm-4 control-label">定价日期：</label>
                            <div class="col-sm-8">
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectDjSpDateMin',id='projectDjSpDateMin')"/>
                                </div>
                                <span>~</span>
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectDjSpDateMax',id='projectDjSpDateMax')"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row col-sm-12">
                        <div class="col-sm-6 form-group">
                            <label class="col-sm-4 control-label">效益统计日期：</label>
                            <div class="col-sm-8">
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectJjxyDateMin',id='projectJjxyDateMin')"/>
                                </div>
                                <span>~</span>
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectJjxyDateMax',id='projectJjxyDateMax')"/>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-6 form-group">
                            <label class="col-sm-4 control-label">项目结题日期：</label>
                            <div class="col-sm-8">
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectJtDateMin',id='projectJtDateMin')"/>
                                </div>
                                <span>~</span>
                                <div style="width: 45%;display:inline-block;">
                                    <th:block th:include="/component/date::init(name='projectJtDateMax',id='projectJtDateMax')"/>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">效益金额:</label>
                        <div class="col-sm-4">
                                <span>从</span>
                                <input style="width: 35%;display:inline-block;" type="text" class="form-control" name="xyMoneyMin"/>
                                <span>到</span>
                                <input style="width: 35%;display:inline-block;" type="text" class="form-control" name="xyMoneyMax"/>
                                <span>（万元）</span>
                        </div>
                        <label class="col-sm-2 control-label">状态:</label>
                        <div class="col-sm-4">
                            <div th:include="/component/select :: init(id='projectStatus', name='projectStatus',businessType=${constants.BUSINESS_TYPE_KTTG}, dictCode=${constants.PROJECT_STATUS},isfirst=true)"></div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目范围:</label>
                        <div class="col-sm-6">
                            <th:block th:include="/component/radio :: init(name='projectKind',businessType='KTTG',dictCode='payKind',labelClass='col-sm-2 control-label',divClass='col-sm-4')"></th:block>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">输出字段:</label>
                        <div class="checkbox col-sm-10">
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectType"/>项目类型
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="tgfDwdeptName"/>项目推广单位
                            </label>
                            <!--<label>
                                <input type="checkbox" onclick="shuchu(this)" id="srfMembers"/>受让方成员
                            </label>-->
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="tgfFzrName" checked="checked"/>项目负责人
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectVisDate"/>合同签订日
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectName" checked="checked"/>项目名称
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="srfFzrName" />受让方负责人
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectJtDate"/>项目结题日期
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectDjSpDate" />定价日期
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectJjxy" />效益金额
                            </label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-2 control-label"></label>
                        <div class="checkbox col-sm-10">
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="srfDwdeptName"/>项目受让单位
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectEndDate"/>项目结束日期
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="tgfXmzgName"/>项目主管
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectKind"/>所属分类
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectStartDate"/>项目开始日期
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="serialNo" />流水号
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectStatus" checked="checked"/>当前状态
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="xmzgJjxyDate"/>项目主管确认效益日期
                            </label>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label"></label>
                        <div class="checkbox col-sm-10">
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectJjxyDate"/>效益审批日期
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectSource"/>项目来源
                            </label>
                            <!--<label>
                                <input type="checkbox" onclick="shuchu(this)" id="tgfMembers" />推广方成员
                            </label>-->
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectNum" checked="checked"/>项目编号
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectSpDate" />需求审批通过日期
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="projectAttSpDate" />技术附件审批日期
                            </label>
                            <label>
                                <input type="checkbox" onclick="shuchu(this)" id="tcrUserName" />提出人
                            </label>
                        </div>
                    </div>

                </div>
                <div th:if="${queryType ne 'comprehensive'}">
                    <div class="row form-group">
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label">项目编号：</label>
                            <div class="col-sm-8">
                                <input class="form-control width100" name="projectNum" type="text">
                            </div>
                        </div>

                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label">项目名称：</label>
                            <div class="col-sm-8">
                                <input class="form-control width100" name="projectName" type="text">
                            </div>
                        </div>

                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label">项目负责人：</label>
                            <div class="col-sm-8">
                                <input class="form-control width100" name="tgfFzrName" type="text">
                            </div>
                        </div>
                    </div>
                    <div class="row form-group">
                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label">受让单位部门：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectOrg :: init(orgCodeId='srfDeptCode',orgNameId='srfDeptName',selectType='S')"></div>
                        </div>

                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label">推广单位部门：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectOrg :: init(orgCodeId='tgfDeptCode',orgNameId='tgfDeptName',selectType='S')"></div>
                        </div>

                        <div class="col-sm-4">
                            <label class="col-sm-4 control-label">状态：</label>
                            <div class="col-sm-8"
                                 th:include="/component/select :: init(id='projectStatus', name='projectStatus',businessType=${constants.BUSINESS_TYPE_KTTG}, dictCode=${constants.PROJECT_STATUS},isfirst=true)"></div>
                        </div>
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-4">
                    </div>
                    <label class="col-sm-2 control-label"></label>
                    <div class="col-sm-4">
                        <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()">
                            <i class="fa fa-search"></i>
                            &nbsp;搜索
                        </a>
                        <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()">
                            <i class="fa fa-refresh"></i>
                            &nbsp;重置
                        </a>
                    </div>
                </div>
            </form>
        </div>

                <div class="btn-group-sm" id="toolbar" role="group">
                    <a class="btn btn-warning" onclick="$.table.exportExcel()">
                        <i class="fa fa-download"></i> 导出
                    </a>
                </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzlx/main";

    var projectTypeList = [[${@dict.getDictList('KTTG','projectType')}]];
    var projectAreaList = [[${@dict.getDictList('KTTG','projectArea')}]];
    var projectKindList = [[${@dict.getDictList('KTTG','payKind')}]];
    var projectSource = [[${@dict.getDictList('KTTG','projectSource')}]];

    $(function () {
        var options = {
            url: prefix + "/myList",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/add?mainId={id}",
            detailUrl: prefix + "/detail?mainId={id}",
            removeUrl: prefix + "/remove",
            exportUrl: prefix + "/exportZH",
            // firstLoad: false,
            modalName: "项目信息",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.queryType = [[${queryType}]];
                return search;
            },
            columns: [{
                checkbox: true
            },
                {
                    field: 'mainId',
                    visible: false
                },
                {
                    field: 'projectNum',
                    title: '项目编号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'tgfFzrName',
                    title: '项目负责人'
                },
                {
                    field: 'srfDeptName',
                    title: '受让单位部门'
                },
                {
                    field: 'tgfDeptName',
                    title: '推广单位部门'
                },
                {
                    field: 'tgfDwdeptName',
                    title: '项目推广单位',
                    visible: false
                },
                {
                    field: 'srfDwdeptName',
                    title: '项目受让单位',
                    visible: false
                },
                {
                    field: 'projectType',
                    title: '项目类型',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(projectTypeList, value);
                    },
                    visible: false
                },
                {
                    field: 'projectArea',
                    title: '项目范围',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(projectAreaList, value);
                    },
                    visible: false
                },
                {
                    field: 'projectKind',
                    title: '所属分类',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(projectKindList, value);
                    },
                    visible: false
                },
                {
                    field: 'projectSource',
                    title: '项目来源',
                    formatter: function(value, row, index) {
                        return $.table.selectDictLabel(projectSource, value);
                    },
                    visible: false
                },
                {
                    field: 'projectVisDate',
                    title: '合同签订日期',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'srfFzrName',
                    title: '受让方负责人',
                    visible: false
                },
                {
                    field: 'projectJtDate',
                    title: '项目结题日期',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'projectDjSpDate',
                    title: '定价日期',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'projectJjxy',
                    title: '效益金额',
                    visible: false
                },
                {
                    field: 'projectStartDate',
                    title: '项目开始日期',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'projectEndDate',
                    title: '项目结束日期',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'serialNo',
                    title: '流水号',
                    visible: false
                },
                {
                    field: 'tgfXmzgName',
                    title: '项目主管',
                    visible: false
                },
                /*{
                    field: 'tgfMembers',
                    title: '推广方成员',
                    visible: false
                },
                {
                    field: 'srfMembers',
                    title: '受让方成员',
                    visible: false
                },*/
                {
                    field: 'projectJjxyDate',
                    title: '效益审批日期',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'xmzgJjxyDate',
                    title: '项目主管确认效益日期',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'projectSpDate',
                    title: '需求审批通过日期',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'projectAttSpDate',
                    title: '技术附件审批日期',
                    visible: false,
                    formatter: function(value, row, index) {
                        return $.common.dateFormat(value, "yyyy-MM-dd");
                    },
                },
                {
                    field: 'tcrUserName',
                    title: '提出人',
                    visible: false
                },
                {
                    field: 'projectStatus',
                    title: '状态',
                    formatter: function (value, row, index) {
                        return $("#projectStatus option[value='" + value + "']").text();
                    }
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.mainId + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        // if(row.projectStatus == [[${constants.STATUS_ZC}]]){
                        //     actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.mainId + '\')"><i class="fa fa-edit"></i>处理</a> ');
                        //     actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.mainId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        // }
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

    function shuchu(obj){
        if ($(obj).prop("checked")) {
            $.table.showColumn(obj.id);
        } else {
            $.table.hideColumn(obj.id);
        }
    }

</script>
</body>
</html>