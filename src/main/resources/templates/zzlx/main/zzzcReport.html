<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('推广项目运行实绩统计列表')" />
    <th:block th:include="include :: baseJs" />
</head>
<body class="gray-bg">
     <div class="container-div">
        <div class="row">
            <div class="col-sm-12 search-collapse">
                <form id="formId">
                    <input type="hidden" name="queryType" th:value="${queryType}">
                    <div class="select-list">
                        <ul>
                            <li>
                                <label>年度：</label>
                                <input type="text"  name="year" th:value="${year}"/>
                            </li>
                            <li>
                                <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i class="fa fa-search"></i>&nbsp;搜索</a>
                                <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i class="fa fa-refresh"></i>&nbsp;重置</a>
                            </li>
                        </ul>
                    </div>
                </form>
            </div>
<!--
            <div class="btn-group-sm" id="toolbar" role="group">
                <a class="btn btn-success" onclick="startNotice()" >
                    <i class="fa fa-plus"></i> 下发结题通知(定时)
                </a>
            </div>-->
            <div class="col-sm-12 select-table table-striped">
                <table id="bootstrap-table" data-mobile-responsive></table>
            </div>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzlx/main";

        $(function() {
            var options = {
                url: prefix + "/pageZZReport",
                createUrl: prefix + "/add",
                detailUrl: ctx + "zzlx/main/detail?mainId={id}",
                modalName: "项目列表",
                firstLoad: false,
                showFooter: true,
                escape:true,
                columns: [[
                {
                    field: 'deptName',
                    title: '单位',
                    align: "center",
                    rowspan:2,
                    formatter: function(value, row, index) {
                        return value;
                    },
                    footerFormatter: function (value) {
                        return "年度累计";
                    }
                },
                {
                    field: '',
                    align: "center",
                    title: '已立项'
                },
                {
                    field: '',
                    align: "center",
                    title: '运行中'
                },
                {
                    field: '',
                    align: "center",
                    title: '已结题'
                },
                {
                    field: '',
                    align:'center',
                    title: '经济效益（万元）',
                    colspan:4
                }
                ],
                [
                {
                    field: 'ylx',
                    align: "center",
                    title: '（项）' ,
                    footerFormatter: function (value) {
                        var sumBalance = 0;
                        for (var i in value) {
                            sumBalance += parseFloat(value[i].ylx);
                        }
                        return  sumBalance;
                    }
                },
                {
                    field: 'yunx',
                    align: "center",
                    title: '（项）',
                    footerFormatter: function (value) {
                        var sumBalance = 0;
                        for (var i in value) {
                            sumBalance += parseFloat(value[i].yunx);
                        }
                        return  sumBalance;
                    }
                },
                {
                    field: 'yjt',
                    align: "center",
                    title: '（项）',
                    footerFormatter: function (value) {
                        var sumBalance = 0;
                        for (var i in value) {
                            sumBalance += parseFloat(value[i].yjt);
                        }
                        return  sumBalance;
                    }
                },
                    {
                        field: 'gyyh',
                        align: "center",
                        title: '工艺优化',
                        footerFormatter: function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                var sums = parseFloat(value[i].gyyh);
                                if (Number.isNaN(sums)) {
                                    sums = 0;
                                }
                                sumBalance += sums;
                            }
                            return  sumBalance;
                        }
                    },
                    {
                        field: 'cpyz',
                        align: "center",
                        title: '产品移植',
                        footerFormatter: function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                var sums = parseFloat(value[i].cpyz);
                                if (Number.isNaN(sums)) {
                                    sums = 0;
                                }
                                sumBalance += sums;
                            }
                            return  sumBalance;
                        }
                    },
                    {
                        field: 'wdzz',
                        align: "center",
                        title: '稳定制造',
                        footerFormatter: function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                var sums = parseFloat(value[i].wdzz);
                                if (Number.isNaN(sums)) {
                                    sums = 0;
                                }
                                sumBalance += sums;
                            }
                            return  sumBalance;
                        }
                    },
                    {
                        field: 'hj',
                        align: "center",
                        title: '合计',
                        footerFormatter: function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                var sums = parseFloat(value[i].hj);
                                if (Number.isNaN(sums)) {
                                    sums = 0;
                                }
                                sumBalance += sums;
                            }
                            return  sumBalance;
                        }
                    }

                ]
            ]
            };
            $.table.init(options);
        });

    </script>
</body>
</html>