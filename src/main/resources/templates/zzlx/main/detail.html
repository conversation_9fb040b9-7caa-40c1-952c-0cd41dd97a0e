<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增滚动需求')"/>

    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: summernote-js"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <!--    <div class="form-group" th:include="include :: step(approveKind=${main.constants.ZZLX_GDXQSB},currentNode='MANUAL1')"></div>-->
    <!--    <div aria-multiselectable="true" class="panel-group" id="accordion" role="tablist"></div>-->

    <form class="form-horizontal m" id="form-need-addEdit" th:object="${main}">
        <input th:field="*{mainId}" type="hidden">
        <!-- 基本信息 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#1">基本信息
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">项目流水号：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{serialNo}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">项目编号：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{projectNum}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">项目名称：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{projectName}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">项目范围：</label>
                            <div class="col-sm-8 form-control-static"
                                 th:utext="${@dict.getDictName(main.constants.BUSINESS_TYPE_KTTG,'projectArea',main.projectArea)}"></div>
                        </div>
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">项目类型：</label>
                            <div class="col-sm-8 form-control-static"
                                 th:utext="${@dict.getDictName(main.constants.BUSINESS_TYPE_KTTG,'projectType',main.projectType)}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">推广单位部门：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectOrg :: init(orgCodeId='tgfDwdeptCode',orgNameId='tgfDwdeptName',value=*{tgfDeptCode},selectType='S',see=true)"></div>
                        </div>
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">受让单位部门：</label>
                            <div class="col-sm-8" th:if="*{projectArea eq constants.PROJECT_AREA_GFN}"
                                 th:include="/component/selectOrg :: init(orgCodeId='srfDwdeptCode',orgNameId='srfDwdeptName',value=*{srfDeptCode},selectType='S',see=true)"></div>
                            <div class="col-sm-8 form-control-static"
                                 th:if="*{projectArea eq constants.PROJECT_AREA_JTN}"
                                 th:utext="${@maintain.getJtUnitName(main.srfDeptCode)}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">推广方项目负责人：</label>
                            <div th:if="*{!#strings.isEmpty(tgfFzrName)}" class="col-sm-8 form-control-static" th:utext="*{tgfFzrName}"></div>
                            <div th:if="*{#strings.isEmpty(tgfFzrName)}" th:include="/component/selectUser :: init(userCodeId='tgfFzrCode',userNameId='tgfFzrCode',value=*{tgfFzrCode},selectType='S',see=true)"></div>
                        </div>
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">联系电话：</label>
                            <div class="col-sm-8 form-control-static" th:utext="*{tgfXmfzrTel}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">受让方项目负责人：</label>
                            <div th:if="*{!#strings.isEmpty(srfFzrName)}" class="col-sm-8 form-control-static" th:utext="*{srfFzrName}"></div>
                            <div th:if="*{#strings.isEmpty(srfFzrName)}" th:include="/component/selectUser :: init(userCodeId='srfFzrCode',userNameId='srfFzrCode',value=*{srfFzrCode},selectType='S',see=true)"></div>
                        </div>
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">联系电话：</label>
                            <div class="col-sm-8 form-control-static" th:utext="*{srfTel}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">项目开始日期：</label>
                            <div class="col-sm-8"
                                 th:include="/component/date :: init(id='projectStartDate', name='projectStartDate',strValue=*{projectStartDate} ,see=true)"></div>
                        </div>
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">项目结束日期：</label>
                            <div class="col-sm-8"
                                 th:include="/component/date :: init(id='projectEndDate', name='projectEndDate',strValue=*{projectEndDate} ,see=true)"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">项目来源：</label>
                            <div class="col-sm-8 form-control-static"
                                 th:utext="${@dict.getDictName(main.constants.BUSINESS_TYPE_KTTG,'projectSource',main.projectSource)}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">当前状态：</label>
                            <div class="col-sm-8 form-control-static"
                                 th:utext="${@dict.getDictName(main.constants.BUSINESS_TYPE_KTTG,'projectStatus',main.projectStatus)}"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-3 control-label">当前操作人 ：</label>
                            <div class="col-sm-8 form-control-static" th:utext="${currentOperator}"></div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
        <!-- 基本信息 End -->

        <!-- 项目内容 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#2">项目内容
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                <div class="panel-body">
                    <div th:if="${#strings.substring(main.projectStatus,1) gt '0'}">
                        <label class="col-sm-12" th:if="*{projectSource eq constants.PROJECT_SOURCE_GDXQSB}">点击<a
                                class="form_list_a"
                                th:onclick="$.modal.openTab('推广需求',ctxZZZC+'zzlx/need/detailM?mainId='+[[*{mainId}]])">此处</a>查看推广需求表</label>
                        <label class="col-sm-12" th:if="*{projectSource eq constants.PROJECT_SOURCE_NDJH}">点击<a
                                class="form_list_a"
                                th:onclick="$.modal.openTab('推广需求',ctxKY +'web/KYKTTGPD01?mainId='+[[*{mainId}]])">此处</a>查看推广需求表</label>
                    </div>
                    <div th:if="${#strings.substring(main.projectStatus,1) gt '1'}">
                        <label class="col-sm-12" th:if="${teachAttsend}">点击<a class="form_list_a"
                                                                              th:onclick="$.modal.openTab('技术附件通知书',ctxZZZC+'zzlx/teachAttsend/query/'+[[*{mainId}]])">此处</a>查看技术附件通知书</label>
                        <label class="col-sm-12" th:if="${teachAtt}">点击<a class="form_list_a"
                                                                          th:onclick="$.modal.openTab('技术附件',ctxZZZC+'zzlx/teachAtt/openAtt/'+[[*{mainId}]])">此处</a>查看技术附件</label>
                        <label class="col-sm-12" th:if="${teachFix}">点击<a class="form_list_a"
                                                                          th:onclick="$.modal.openTab('定价评审表',ctxZZZC+'zzlx/teachFix/openFix/'+[[*{mainId}]])">此处</a>查看定价评审表</label>
                        <label class="col-sm-12" th:if="${contract}">点击<a class="form_list_a"
                                                                          th:onclick="$.modal.openTab('项目合同',ctxZZZC+'zzlx/teachContract/openContract/'+[[*{mainId}]])">此处</a>查看项目合同</label>
                        <label class="col-sm-12" th:if="${plan}">点击<a class="form_list_a"
                                                                      th:onclick="$.modal.openTab('计划任务书',ctxZZZC+'zzlx/teachPlantask/openPlan/'+[[*{mainId}]])">此处</a>查看计划任务书</label>
                    </div>
                    <div th:if="${#strings.substring(main.projectStatus,1) gt '3'}">
                        <label class="col-sm-12" th:if="${check}">点击<a class="form_list_a"
                                                                       th:onclick="$.modal.openTab('项目验收申请表',ctxZZZC+'zzjt/check/queryDetail/'+[[*{mainId}]])">此处</a>查看项目验收申请表</label>
                        <label class="col-sm-12" th:if="${reportEx}">点击<a class="form_list_a"
                                                                          th:onclick="$.modal.openTab('项目结题报告',ctxZZZC+'zzjt/report/queryDetail/'+[[*{mainId}]])">此处</a>查看项目结题报告</label>
                        <label class="col-sm-12" th:if="${settle}">点击<a class="form_list_a"
                                                                        th:onclick="$.modal.openTab('项目结算表',ctxZZZC+'zzjt/settle/queryDetail/'+[[*{mainId}]])">此处</a>查看项目结算表</label>
                        <label class="col-sm-12" th:if="${assess}">点击<a class="form_list_a"
                                                                          th:onclick="$.modal.openTab('项目评估表',ctxZZZC+'zzjt/assess/queryDetail/'+[[*{mainId}]])">此处</a>查看项目评估表</label>
                        <label class="col-sm-12" th:if="${rewardNotice}">点击<a class="form_list_a"
                                                                          th:onclick="$.modal.openTab('奖励通知单',ctxZZZC+'zzjt/rewardNotice/detail/'+[[*{mainId}]])">此处</a>查看项目奖励通知单</label>
                        <label class="col-sm-12" th:if="${rewardList}">点击<a class="form_list_a"
                                                                              th:onclick="$.modal.open('奖励分配',ctxZZZC+'zzjl/rewardFp/rewardProList/'+[[*{mainId}]],'1000', '590')">此处</a>查看项目奖励分配</label>
                    </div>
                </div>
            </div>
        </div>
        <!-- 项目内容 End -->
        <!--经济效益-->
        <th:block th:include="/component/xyView :: init(ywId=*{mainId},moduleCode='zzjt_benefit',titleName='推广经济效益')"></th:block>
        </th:block>
        <!-- 年度计划启动通知 Start -->
        <div class="panel panel-default panel-group" th:if="${planNotices}">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                       href="#3">年度计划启动通知
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="3">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-12 select-table table-striped">
                            <table id="planNotices"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 年度计划启动通知 End -->

        <!-- 项目月报 Start -->
        <div class="panel panel-default panel-group" th:if="${month}">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                       href="#3">项目月报
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="3">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-12 select-table table-striped">
                            <table id="month"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 项目月报 End -->

        <!-- 任务书变更 Start -->
        <div class="panel panel-default panel-group" th:if="${teach}">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                       href="#4">任务书变更
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="4">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-12 select-table table-striped">
                            <table id="teach"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 任务书变更 End -->

        <!-- 现场支撑报备 Start -->
        <div class="panel panel-default panel-group" th:if="${sceneList}">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                       href="#5">现场支撑报备
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="5">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-12 select-table table-striped">
                            <table id="sceneList"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 现场支撑报备 End -->

        <!-- 远程支撑报备 Start -->
        <div class="panel panel-default panel-group" th:if="${longList}">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                       href="#6">远程支撑报备
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="6">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-12 select-table table-striped">
                            <table id="longList"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 远程支撑报备 End -->

        <!-- 通讯费申请 Start -->
        <div class="panel panel-default panel-group" th:if="${commTxfLsit}">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                       href="#7">通讯费申请
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="7">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-12 select-table table-striped">
                            <table id="commTxfLsit"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 通讯费申请 End -->

        <div th:if="${main.projectStatus eq '99'}" class="panel-group" id="accordion2" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#xgfj" aria-expanded="false" class="collapsed">项目中止信息
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>

                <!--折叠区域-->
                <div id="xgfj" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">相关附件：</label>
                                <div class="col-sm-10" style="margin-bottom: 10px">
                                    <div th:include="/component/attachment :: init(display='none',sourceId=${main.mainId},sourceModule='END_XGFJ',id='endFjId',name='endFjId',see=true)">
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">中止意见：</label>
                                <div class="form-control-static" th:text="${main.extra1}" style="margin-bottom: 10px">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
    <div class="m">
        <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuids})" />
    </div>
</div>
<div class="row form-group">
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button class="btn btn-danger" onclick="closeItem()" type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>
<!--审批历史-->
<input id="processInstanceId" name="processInstanceId" th:value="${processInstanceId}" type="hidden">
<!--<div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
     th:include="include :: includeTaskHistoryList(instanceId=${processInstanceId})">
</div>-->
<div class="row" style="padding: 15px 0;">
    <th:block th:include="component/wfCommentList :: init(businessId=${main.mainId},processInstanceId=${processInstanceId})"></th:block>
</div>
<script th:inline="javascript">
    var statusData = [[${@dict.getDictList('KTTG','status')}]];
    var submitChoose = [[${@dict.getDictList('KTTG','submitChoose')}]];
    /**
     * 初始化数据
     */
    $(function () {
        var month = {// 项目月报
            id: "month",
            data: [[${month}]],
            detailUrl: ctx + "zzgc/teachMonth/openMonth/{id}",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            modalName: "项目月报",
            columns: [
                {
                    field: 'monthId',
                    visible: false
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    align: 'center',
                    title: '年份',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(row.monthCreateDate)) {
                            return $.common.dateFormat(row.monthCreateDate,'yyyy')
                        }
                        return "";
                    }
                },
                {
                    align: 'center',
                    title: '月份',
                    formatter: function (value, row, index) {
                        if ($.common.isNotEmpty(row.monthCreateDate)) {
                            return new Date(row.monthCreateDate).getMonth()+1
                        }
                        return "";
                    }
                },
                {
                    field: 'monthType',
                    align: 'center',
                    title: '类型',
                    formatter: function (value, row, index) {
                        if(value=='1'||value=='tgf'){
                            return "推广方";
                        }else if(value=='0'||value=='srf'){
                            return "受让方";
                        }
                    }
                },
                {
                    field: 'monthStatus',
                    align: 'center',
                    title: '月报状态'
                },
                {
                    field: 'currentOperator',
                    align: 'center',
                    title: '当前操作人'
                },
                {
                    align: 'center',
                    title: '操作',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detail(\'' + row.monthId + '\')"><i class="fa fa-eye"></i>查看明细</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(month);

        var planNotice = {// 年度计划启动通知
            id: "planNotices",
            data: [[${planNotices}]],
            detailUrl: ctx + "zznd/planNotice/detail/{id}",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            modalName: "年度计划启动通知",
            columns: [
                {
                    field: 'noticeId',
                    visible: false
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'createDate',
                    align: 'center',
                    title: '通知下达日期',
                    formatter: function (value, row, index) {
                        return $.common.dateFormat(new Date(value).toDateString());
                    }
                },
                {
                    field: 'submitChoose',
                    align: 'center',
                    title: '确认选项',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(submitChoose, value);
                    }
                },
                {
                    align: 'center',
                    title: '操作',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detail(\'' + row.noticeId + '\')"><i class="fa fa-eye"></i>查看明细</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(planNotice);

        var teach = {// 任务书变更
            id: "teach",
            data: [[${teach}]],
            detailUrl: ctx + "zzgc/teachChangetask/openChange/{id}",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            modalName: "任务书变更",
            queryParams: queryParams,
            columns: [
                {
                    field: 'chtaskId',
                    visible: false
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'applyDate',
                    align: 'center',
                    title: '更改提出日期'
                },
                {
                    field: 'createUserLabel',
                    align: 'center',
                    title: '提出人',
                    formatter: function (value, row, index) {
                        return $.table.queryUserName(value)
                    }
                },
                {
                    align: 'center',
                    title: '操作',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detail(\'' + row.chtaskId + '\')"><i class="fa fa-eye"></i>查看明细</a> ');
                        return actions.join('');
                    }
                }]
        };
        $.table.init(teach);

        var sceneList = {// 现场支撑报备申请
            id: "sceneList",
            data: [[${sceneList}]],
            detailUrl: ctx + "zzfy/scene/detailScene/{id}",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            modalName: "现场支撑报备申请",
            queryParams: queryParams,
            columns: [
                {
                    field: 'sceneId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'sceneDate',
                    align: 'center',
                    title: '申请日期'
                },
                {
                    field: 'sceneFyTotal',
                    align: 'center',
                    title: '差旅费(元)'
                },
                {
                    field: 'sceneBzdh',
                    align: 'center',
                    title: '报支单号'
                },
                {
                    field: 'status',
                    align: 'center',
                    title: '状态',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(statusData, value);
                    }
                },
                {
                    field: 'scenePeopleTotal',
                    align: 'center',
                    title: '人工成本'
                },
                {
                    title: '查看',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detail(\'' + row.sceneId + '\')"><i class="fa fa-eye"></i>查看明细</a> ');
                        return actions.join('');
                    }
                }
            ]
        };
        $.table.init(sceneList);

        var longList = {// 远程支撑报备申请
            id: "longList",
            data: [[${longList}]],
            detailUrl: ctx + "zzfy/long/detailLong/{id}",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            modalName: "远程支撑报备申请",
            queryParams: queryParams,
            columns: [
                {
                    field: 'longId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'longDate',
                    align: 'center',
                    title: '申请日期'
                },
                {
                    field: 'status',
                    align: 'center',
                    title: '状态',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(statusData, value);
                    }
                },
                {
                    field: 'longRgfFyhj',
                    align: 'center',
                    title: '人工成本'
                },
                {
                    title: '查看',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detail(\'' + row.longId + '\')"><i class="fa fa-eye"></i>查看明细</a> ');
                        return actions.join('');
                    }
                }
            ]
        };
        $.table.init(longList);

        var commTxfLsit = {// 通讯费申请
            id: "commTxfLsit",
            data: [[${commTxfLsit}]],
            detailUrl: ctx + "zzfy/commTxf/detailCommTxf/{id}",
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            modalName: "通讯费申请",
            queryParams: queryParams,
            columns: [
                {
                    field: 'commId',
                    title: '主键',
                    visible: false
                },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        return $.table.serialNumber(index);
                    }
                },
                {
                    field: 'commDate',
                    align: 'center',
                    title: '申请日期'
                },
                {
                    field: 'sceneTsfTotal',
                    align: 'center',
                    title: '通讯费(元)'
                },
                {
                    field: 'extra1',
                    align: 'center',
                    title: '报支单号'
                },
                {
                    field: 'extra2',
                    align: 'center',
                    title: '状态',
                    formatter: function (value, row, index) {
                        return $.table.selectDictLabel(statusData, value);
                    }
                },
                {
                    title: '查看',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detail(\'' + row.commId + '\')"><i class="fa fa-eye"></i>查看明细</a> ');
                        return actions.join('');
                    }
                }
            ]
        };
        $.table.init(commTxfLsit);

        function queryParams(params) {
            var search = {
                // 传递参数查询参数
                pageSize: params.limit,
                pageNum: params.offset / params.limit + 1,
                searchValue: params.search,
                orderByColumn: params.sort,
                isAsc: params.order
            };
            search.bizId = [[${main.mainId}]]
            return search;
        }
    });
</script>
</body>
</html>