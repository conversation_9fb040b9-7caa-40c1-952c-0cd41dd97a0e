<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增【请填写功能名称】')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-main-add">
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectSource" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="serialNo" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectName" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectNum" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectHtNum" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectHtName" class="form-control" type="text">
                </div>
            </div>



      <div class="form-group" th:include="include :: initSelectBox(id='projectType', name='projectType',dictType='${dictType}', labelName='${comment}')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectArea" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectKind" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="reportKind" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfDeptCode" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfDeptName" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfDwdeptCode" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfDwdeptName" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfDeptCode" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfDeptName" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfDwdeptCode" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfDwdeptName" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfXmzgCode" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfXmzgName" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfXmzgCode" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfXmzgName" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfFzrCode" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfFzrName" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfFzrCode" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfFzrName" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectJjxy" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectJjxyDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectSubmitDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectSpDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectAttSpDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectStartDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectEndDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectLxDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectDjSpDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectVisDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectIsjs" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectJsDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectJlje" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectJtDate" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="extra1" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="extra2" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="extra3" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="extra4" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="extra5" class="form-control" type="text">
                </div>
            </div>


            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',dictType='${dictType}' ,labelName='${comment}')">
            </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>
	</div>


    <script th:inline="javascript">
        var prefix = ctx + "zzlx/main"

        $("#form-main-add").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/add", $('#form-main-add').serialize());
            }
        }

    </script>
</body>
</html>