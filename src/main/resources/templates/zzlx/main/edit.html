<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('修改【请填写功能名称】')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-main-edit" th:object="${main}">
            <input name="mainId" th:field="*{mainId}" type="hidden">
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectSource" th:field="*{projectSource}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="serialNo" th:field="*{serialNo}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectName" th:field="*{projectName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectNum" th:field="*{projectNum}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectHtNum" th:field="*{projectHtNum}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectHtName" th:field="*{projectHtName}" class="form-control" type="text">
                </div>
            </div>

      <div class="form-group" th:include="include :: initSelectBox(id='projectType', name='projectType',dictType='${dictType}', value=${main.projectType} ,labelName='${comment}')">
       </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectArea" th:field="*{projectArea}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectKind" th:field="*{projectKind}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="reportKind" th:field="*{reportKind}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfDeptCode" th:field="*{srfDeptCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfDeptName" th:field="*{srfDeptName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfDwdeptCode" th:field="*{srfDwdeptCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfDwdeptName" th:field="*{srfDwdeptName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfDeptCode" th:field="*{tgfDeptCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfDeptName" th:field="*{tgfDeptName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfDwdeptCode" th:field="*{tgfDwdeptCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfDwdeptName" th:field="*{tgfDwdeptName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfXmzgCode" th:field="*{tgfXmzgCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfXmzgName" th:field="*{tgfXmzgName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfXmzgCode" th:field="*{srfXmzgCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfXmzgName" th:field="*{srfXmzgName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfFzrCode" th:field="*{tgfFzrCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="tgfFzrName" th:field="*{tgfFzrName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfFzrCode" th:field="*{srfFzrCode}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="srfFzrName" th:field="*{srfFzrName}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectJjxy" th:field="*{projectJjxy}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectJjxyDate" th:field="*{projectJjxyDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectSubmitDate" th:field="*{projectSubmitDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectSpDate" th:field="*{projectSpDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectAttSpDate" th:field="*{projectAttSpDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectStartDate" th:field="*{projectStartDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectEndDate" th:field="*{projectEndDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectLxDate" th:field="*{projectLxDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectDjSpDate" th:field="*{projectDjSpDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectVisDate" th:field="*{projectVisDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectIsjs" th:field="*{projectIsjs}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectJsDate" th:field="*{projectJsDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectJlje" th:field="*{projectJlje}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="projectJtDate" th:field="*{projectJtDate}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="extra1" th:field="*{extra1}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="extra2" th:field="*{extra2}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="extra3" th:field="*{extra3}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="extra4" th:field="*{extra4}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group">    
                <label class="col-sm-3 control-label">${comment}：</label>
                <div class="col-sm-8">
                    <input name="extra5" th:field="*{extra5}" class="form-control" type="text">
                </div>
            </div>
            <div class="form-group" th:include="include :: initRadio(id='delStatus', name='delStatus',dictType='${dictType}' ,value=${main.delStatus} ,labelName='${comment}')">
            </div>
        </form>
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>保 存
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>
	</div>

    <th:block th:include="include :: datetimepicker-js" />
    <script th:inline="javascript">
        var prefix = ctx + "zzlx/main";

        $("#form-main-edit").validate({
            focusCleanup: true
        });

        function submitHandler() {
            if ($.validate.form()) {
                $.operate.saveTab(prefix + "/edit", $('#form-main-edit').serialize());
            }
        }

    </script>
</body>
</html>