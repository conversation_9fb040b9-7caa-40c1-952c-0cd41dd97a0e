<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增技术附件通知书')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <!--框-->
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#2" aria-expanded="false" class="collapsed">基本信息
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                <form class="form-horizontal m" id="form-teachAttsend-add" th:object="${need}">
                    <input id="needId" name="needId"  th:value="${need.needId}" type="hidden">
                    <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
                    <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
                    <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
                    <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
                    <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">推广单位部门</label>
                        <div class="col-sm-8">
                            <div class="form-control-static" th:utext="${need.tgfDeptName}"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-group  col-sm-3"></div>
                        <!-- &#8195; &emsp;&emsp;&#8195; &emsp;&#8195;-->
                        经研究，同意
                        ” <span class="form-control-static" style="font-weight: bold" th:utext="${need.tgfDeptName}"></span>“
                        的” <span class="form-control-static" style="font-weight: bold" th:utext="${need.projectName}"></span>“推广需求，
                        请按公司管理制度的有关规定，在
                        <span class="form-control-static" style="font-weight: bold" th:utext="${need.finishDate}"></span>
                        <input id="tzsJzdate" name="tzsJzdate"  th:value="${need.finishDate}" type="hidden">

                        前组织完成技术附件表。</div>
                    <div class="form-group  col-sm-3"></div>
                    （<span style="color: red">未在截止日期完成技术附件提交，系统自动终止项目</span>）

                    <br/>
                    <div class="form-group" style="text-align: right;margin-right: 200px;">
                        <input id="tzsCode" name="tzsCode"  th:value="${loginCode}" type="hidden">
                        <input id="tzsName" name="tzsName"  th:value="${loginName}" type="hidden">
                        <p class="select-title"  th:utext="${loginName}"></p>
                        日期：<span class="form-control-static" th:utext="${downDate}"></span>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">推广需求表</label>
                        <div class="col-sm-8">
                            <span><a href="#">推广需求表</a></span>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">推广方项目负责人</label>
                        <div class="col-sm-8">
                            <div class="form-control-static" th:utext="${need.tgfFzrName}"></div>
                        </div>
                    </div>
                </form>
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--框end-->
    </div>
    <div class="row">
		<div class="col-sm-offset-5 col-sm-10" >
			<button type="button" class="btn btn-sm btn-primary"
				onclick="submitHandler()">
				<i class="fa fa-check"></i>提交
			</button>
			&nbsp;
			<button type="button" class="btn btn-sm btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返 回
			</button>
		</div>
	</div>


<script th:inline="javascript">
    var prefix = ctx + "zzlx/teachAttsend"
    $("input[name='tzsJzdate']").datetimepicker({
        format : "yyyy-mm-dd",
        minView : "month",
        autoclose : true
    }).on('keypress paste', function (e) {
        e.preventDefault();
        return false;
    });
    $("#form-teachAttsend-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        $.modal.confirm("确认提交吗？", function () {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/submitWF",$('#form-teachAttsend-add').serialize());
            }
        })
    }

</script>
</body>
</html>