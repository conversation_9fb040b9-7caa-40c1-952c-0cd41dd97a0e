<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('技术附件审批')" />
    <th:block th:include="include :: baseJs" />


</head>
<body class="white-bg">


    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="form-group" th:include="include :: step(approveKind='ZZLX_XMLX',currentNode=${activityCode})"></div><br/>
        <form class="form-horizontal m" id="form-teachAtt-add" th:object="${teachAtt}">
            <input id="bizId" name="bizId"  th:value="${businessGuid}" type="hidden">
            <input id="mainId" name="mainId"  th:value="${main.mainId}" type="hidden">
            <input id="needId" name="needId"  th:value="${need.needId}" type="hidden">
            <input id="attId" name="attId"  th:value="${teachAtt.attId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">


            <!--框-->
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#2" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">

                    <div class="form-group">
                        <label class="col-sm-2 control-label">项目名称：</label>
                        <div class="col-sm-10">
                            <div class="form-control-static" th:utext="${need.projectName}"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <label class="col-sm-2 control-label">技术附件名称：</label>
                        <div class="col-sm-10">
                            <div class="form-control-static" th:utext="${teachAtt.attProname}"></div>
                        </div>

                    </div>
                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">项目所属分类：</label>
                            <div class="col-sm-10">
                                <div th:include="/component/select :: init(id='projectKind',name='projectKind',
                    businessType='KTTG',dictCode='payKind',value=${main.projectKind} ,see=true)"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">项目类型：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/radio :: init(id='projectType',name='projectType',
                    businessType='KTTG',dictCode='projectType',value=${need.projectType} ,see=true)"></div>
                                </div>
                            </div>

                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">项目范围：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/radio :: init(id='projectArea',name='projectArea',
                    businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='projectArea',value=${need.projectArea} ,see=true)"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">推广单位部门：</label>
                            <div class="col-sm-4">
                                <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(need.tgfDeptCode)}"></div>
                            </div>

                            <label class="col-sm-2 control-label">受让单位部门：</label>
                            <div class="col-sm-4">
                                <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(need.srfDeptCode)}"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">推广方项目负责人：</label>
                            <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='tgfFzrCode',userNameId='tgfFzrName',value=${need.tgfFzrCode},see=true)"></div>

                            <label class="col-sm-2 control-label">联系电话：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static" th:utext="${need.tgfXmfzrTel}"></div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">受让方项目负责人：</label>
                            <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='srfFzrCode',userNameId='srfFzrName',value=${need.srfFzrCode},see=true)"></div>

                            <label class="col-sm-2 control-label">联系电话：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static" th:utext="${need.srfTel}"></div>
                            </div>
                        </div>
                    </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目开始日期：</label>
                                <div class="col-sm-4" th:include="/component/date :: init(name='prostartDate',id='prostartDate',strValue=${teachAtt.prostartDate},see=true)"></div>
                                <label class="col-sm-2 control-label">项目结束日期：</label>
                                <div class="col-sm-4" th:include="/component/date :: init(name='proendDate',id='proendDate',strValue=${teachAtt.proendDate},see=true)"></div>
                            </div>
                        </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">知识分类：</label>
                                    <div class="col-sm-4">
                                        <div th:include="/component/select :: init(id='attKind',name='attKind',
                            businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='attKind',value=${teachAtt.attKind},see=true)"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">编制日期：</label>
                                    <div class="col-sm-4" th:include="/component/date :: init(name='projectTjDate',id='projectTjDate',strValue=${teachAtt.projectTjDate},see=true)"></div>
                                </div>
                            </div>

                            <div class="form-group">
                                <div th:if="${main.projectSource eq 'gdxqsb'}" class="col-sm-12 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',ctx+'zzlx/need/detailM?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                                <div th:if="${main.projectSource eq 'ndjh'}" class="col-sm-5 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',[[${@environment.getProperty('app-context.ctxKY')}]] +'web/KYKTTGPD01?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-3 ">点击<a class="form_list_a" th:onclick="$.modal.openTab('技术附件通知书',ctx+'zzlx/teachAttsend/query/'+[[${main.mainId}]])">此处</a>查看技术附件通知书</label>
                            </div>


                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->

            <div class="panel-group" id="accordion2"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmgx" class="collapsed">
                                项目概述
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmgx" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${teachAtt.teachAttUmmary}"></div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion3"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmmb" class="collapsed">
                                项目目标
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmmb" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${teachAtt.teachAttTarget}"></div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmfw" class="collapsed">
                                项目范围
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmfw" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${teachAtt.teachAttArea}"></div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion5"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmnr" class="collapsed">
                               项目内容
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmnr" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${teachAtt.teachAttContent}"></div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion71"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#9">技术附件
                                <span class="pull-right">
                                    <i aria-hidden="true" class="fa fa-chevron-down"></i>
                                </span>
                            </a>
                        </h4>
                    </div>
                    <!--折叠区域-->
                    <div aria-expanded="false" class="panel-collapse collapse in" id="9">
                        <div class="panel-body">
                            <div class="row form-group">
                                <div th:include="/component/attachment :: init(display='none',name='jsfjId',id='jsfjId',sourceId=${teachAtt.attId},sourceModule='TEACH_FJ',see=true)">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
<!--            <div class="form-group" th:include="include :: layui-upload(sourceId=${teachAtt.attId},sourceModule='TEACH_FJ',id='jsfjId',name='jsfjId',labelName='技术附件：',see=true)">-->
<!--            </div>-->
            <!--框-->
            <div class="panel-group" id="accordion6" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#4" aria-expanded="false" class="collapsed">
                            双方分工及协作事项
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="4" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <table class="table table-bordered">
                                <tbody>
                                <tr>
                                    <td colspan="4">
                                        <label class=" control-label">
                                            甲方职责</label>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <div class=" col-sm-12 ">
                                            <div class="form-control-static" th:utext="${teachAtt.teachAttJfzz}"></div>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <label class=" control-label">
                                            乙方职责</label>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <div class=" col-sm-12 ">
                                            <div class="form-control-static" th:utext="${teachAtt.teachAttYfzz}"></div>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->

            <div class="panel-group" id="accordion7" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jhjdtb" class="collapsed">
                                双方提交资料及其交付进度
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="jhjdtb" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class=" col-sm-12 ">
                                    <div class="form-control-static" th:utext="${teachAtt.teachAttPate}"></div>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-key"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion8" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#zscqms" class="collapsed">
                                知识产权描述
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="zscqms" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-schedule"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion9"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#yqxg" class="collapsed">
                                预期效果
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="yqxg" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${teachAtt.teachAttResult}"></div>
                        <div class="form-group">
                            <label style="margin-left: 15px" class="col-sm-0 control-label"> <p class="select-title">经济效益算法：</p> </label>
                            <div class="col-sm-12">
                                <div class="form-control-static" th:utext="${teachAtt.teachAttJjxy}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmzcytb" class="collapsed">
                                项目组织体系
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmzcytb" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <p class="select-title">受让方成员</p>
                                       <input id="shouCode"  name="shouCode" type="hidden">
                                    <input id="shouName"  name="shouName" type="hidden">
                                       <div class="col-sm-12 select-table " style="margin-bottom: 30px">
                                        <table id="bootstrap-table-srf"></table>
                                    </div>
                                    <br><br>
                                    <p class="select-title">推广方成员</p>
                                    <input id="teamCode"  name="teamCode" type="hidden">
                                    <input id="teamName"  name="teamName" type="hidden">
                                    <!--<button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>-->
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-member"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion10" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jdjh" class="collapsed">
                                进度计划
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="jdjh" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-plan"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jfystb" class="collapsed">
                                <span style="color: red">*</span>费用估算（单位：万元）
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="jfystb" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <p class="select-title">实施费用估算</p>
                                   <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-ys"></table>
                                        <table class="table table-bordered table-hover">
                                            <tfoot style="margin-right: 0px;">
                                            <tr>
                                                <th style="text-align: center;  width:127px;">
                                                    <div class="th-inner"></div>
                                                    <div class="fht-cell" style="width: auto;"></div>
                                                </th>
                                                <th style="text-align: center; width: 200px;">
                                                    <div class="th-inner">合计（万元）：</div>
                                                    <div class="fht-cell"style="width: 150px;"></div>
                                                </th>
                                                <th style="text-align: left; ">
                                                    <div class="th-inner">
                                                        <input name="costTotal" type="hidden"th:value="${teachAtt.extra2}"/>
                                                        <div class="form-control-static" id="total" th:utext="${teachAtt.extra2}"></div>
                                                    </div>
                                                    <div class="fht-cell" style="width: auto;"></div>
                                                </th>
                                            </tr>
                                            </tfoot>
                                        </table>
                                       <div class="form-group">
                                           <label class="col-sm-2 control-label">现场支撑人日数：</label>
                                           <div class="col-sm-10">
                                               <div class="form-control-static"  th:utext="${teachAtt.payDay}" ></div>
                                           </div>
                                       </div>
                                       <div class="form-group">
                                            <label class="col-sm-2 control-label">知识产权估算：</label>
                                            <div class="col-sm-10">
                                                <div class="form-control-static" th:utext="${#numbers.formatDecimal(teachAtt.estimate,1,2)}"></div>
                                            </div>
                                       </div>
                                       <span  class="xy4" th:style="'display: ' + @{(${teachAtt.estimate!=null && teachAtt.estimate>0 && main.projectKind!=null && main.projectKind eq 'A'} ? 'block' : 'none')} + ';'" >
                                              <div class="form-group">
                                                  <label class="col-sm-2 control-label">效益大类 ：</label>
                                                  <div class="col-sm-3">
                                                      <th:block
                                                              th:include="/component/select :: init(id='xydl',name='xydl',dictCode='CP_DL',value=${teachAtt.xydl},businessType='KXCP',see=true)"></th:block>
                                                  </div>
                                                  <label class="col-sm-3 control-label">效益小类 ：</label>
                                                  <div class="col-sm-3">
                                                      <th:block
                                                              th:include="/component/select :: init(id='xyxl',name='xyxl',dictCode=${teachAtt.xydl},value=${teachAtt.xyxl},businessType='KXCP',see=true)"></th:block>
                                                  </div>
                                              </div>
                                              <div class="form-group">
                                                  <label class="col-sm-2 control-label">效益计算公式：</label>
                                                  <div class="col-sm-8">
                                                      <input name="xyjsgs" id="xyjsgs" th:value="${teachAtt.xyjsgs}" class="form-control" type="hidden">
                                                      <div class="form-control-static" id="xyjsgsName" th:utext="${teachAtt.xyjsgs}"></div>
                                                  </div>
                                              </div>
                                          </span>
                                       <div class="form-group">
                                            <label class="col-sm-2 control-label">经济效益费用估算：</label>
                                            <div class="col-sm-10">
                                                <div class="form-control-static" th:utext="${#numbers.formatDecimal(teachAtt.economyAndEstimate,1,2)}"></div>
                                            </div>
                                       </div>
                                       <div class="form-group">
                                        <label class="col-sm-2 control-label">费用补充说明：</label>
                                        <div class="col-sm-10" style="margin-bottom: 10px">
                                            <div class="form-control-static" th:utext="${teachAtt.teachAttFysm}"></div>
                                        </div>
                                       </div>
                                       <div class="form-group">
                                       <label class="col-sm-2 control-label">费用估算表附件：</label>
                                       <div class="col-sm-10" style="margin-bottom: 10px">
                                       <div th:include="/component/attachment :: init(display='none',name='fygsId',id='fygsId',sourceId=${teachAtt.attId},sourceModule='FYGS_FJ',see=true)">
                                       </div>
<!--                                       <div class="form-group" th:include="include :: layui-upload(sourceId=${teachAtt.attId},sourceModule='FYGS_FJ',id='fygsId',name='fygsId',labelName='费用估算表附件：',see=true)"></div>-->
                                       </div>
                                       </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion11"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#ysbz" class="collapsed">
                                验收标准及其方式
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="ysbz" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${teachAtt.teachAttYsmethod}"></div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion12"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xtsy" class="collapsed">
                               双方协调其它事宜
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xtsy" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${teachAtt.teachAttMatters}"></div>
                    </div>
                </div>
            </div>
<!--            <div class="form-group" th:include="include :: layui-upload(sourceId=${teachAtt.attId},sourceModule='PSCL_FJ',id='psclId',name='psclId',labelName='评审材料附件：',see=true)"></div>-->
            <div class="panel-group" id="accordion72"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#ps">评审材料附件
                                <span class="pull-right">
                                    <i aria-hidden="true" class="fa fa-chevron-down"></i>
                                </span>
                            </a>
                        </h4>
                    </div>
                    <!--折叠区域-->
                    <div aria-expanded="false" class="panel-collapse collapse in" id="ps">
                        <div class="panel-body">
                            <div class="row form-group">
                                <div th:include="/component/attachment :: init(display='none',name='psclId',id='psclId',sourceId=${teachAtt.attId},sourceModule='PSCL_FJ',see=true)">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </div>
            <div th:if="${activityCode eq 'Manual6'}" class="panel-group" id="accordion2" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#xgfj" aria-expanded="false" class="collapsed">项目中止操作
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="xgfj" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">相关附件：</label>
                                    <div class="col-sm-10" style="margin-bottom: 10px">
                                        <div th:include="/component/attachment :: init(display='none',sourceId=${main.mainId},sourceModule='END_XGFJ',id='endFjId',name='endFjId')">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">中止意见：</label>
                                    <div class="col-sm-10" style="margin-bottom: 10px">
                                        <textarea class="form-control" maxlength="300" name="endComment" rows="4"></textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 审批-->
            <th:block th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=${processInstanceId},required='true',freeFlow='false')"></th:block>


        </form>
        <div class="m">
            <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
        </div>
    </div>
    <div class="row">
        <div class="toolbar toolbar-bottom" role="toolbar" >

            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>

            <th:block th:include="component/wfReturn:: init(taskId=${taskId},callback=wfReturn)"/>

            <button th:if="${activityCode eq 'Manual6'}" type="button" class="btn btn-primary" onclick="closeProject()">
                <i class="fa fa-share"></i>中止
            </button>
            <button type="button"  class="btn btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返 回
            </button>
		</div>
	</div>
    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="component/wfCommentList::init(processInstanceId=${processInstanceId})"></div>

    <script type="text/html" id="scheduleThymeleaf">
        <select name="teachSchedule[%s].deType" th:class="form-control" id="teachSchedule[%s].deType"
                th:with="dictData=${@dict.getDictList('KTTG','deType')}">
            <option  value=" ">请选择</option>
            <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
        </select>
    </script>
    <script type="text/html" id="srfPeopleThymeleaf">
        <select name="srfPeople[%s].role" th:class="form-control" id="srfPeople[%s].role"
                th:with="dictData=${@dict.getDictList('KTTG','projectRole')}">
            <option  value=" ">请选择</option>
            <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
        </select>
    </script>
    <script type="text/html" id="tgfPeopleThymeleaf">
        <select name="tgfPeople[%s].role" th:class="form-control" id="tgfPeople[%s].role"
                th:with="dictData=${@dict.getDictList('KTTG','projectRole')}">
            <option  value=" ">请选择</option>
            <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
        </select>
    </script>

    <script th:inline="javascript">
        var prefix = ctx + "zzlx/teachAtt"

        $("#bootstrap-table-key").on("post-body.bs.table", function (e, args) {
            $("input[name$='beginTime']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });

        $("#bootstrap-table-key").on("post-body.bs.table", function (e, args) {
            $("input[name$='endTime']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });

        $("#bootstrap-table-plan").on("post-body.bs.table", function (e, args) {
            $("input[name$='beginTime']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });

        $("#bootstrap-table-plan").on("post-body.bs.table", function (e, args) {
            $("input[name$='endTime']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });


        $("#form-teachAtt-add").validate({
            focusCleanup: true
        });

        //点击查看技术附件通知书
        function attSendDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "技术附件通知书",
                content: ctx + "zzlx/teachAttsend/query/"+[[${main.mainId}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }

        function saveHandler() {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/addSave", $('#form-teachAtt-add').serialize());
            }
        }
        /** 提交*/
        function submitHandler(){
            $.modal.confirm("确认提交吗？", function () {
                if ($.validate.form()) {
                    $.operate.saveTabAlert(prefix + "/submitWF",$('#form-teachAtt-add').serialize());
                }
            })
        }
        //流程跟踪
        function workFlowProcess() {

            window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + [[${processInstanceId}]]);
        }
        //退回流程
        function wfReturn(activityKey) {
            if ($.validate.form()) {
                var data = $('#form-teachAtt-add').serialize();
                data += "&activityKey=" + activityKey;
                var config = {
                    url: ctx + "mpwf/flowInfo/returnFlow",
                    type: "post",
                    dataType: "json",
                    data: data,
                    beforeSend: function () {
                        $.modal.loading("正在处理中，请稍后...");
                    },
                    success: function (result) {
                        $.operate.alertSuccessTabCallback(result);
                    }
                };
                $.ajax(config)
            }
        }

        function closeProject() {
            $.modal.confirm("确认中止吗？", function () {
                $.operate.saveTabAlert(ctx+"zzlx/main/closeProject",$('#form-teachAtt-add').serialize());
            })
        }

        /***********************************支付进度表*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzlx/teachRate/openTeachRate?attId="+[[${teachAtt.attId}]],
                id:"bootstrap-table-key",
                toolbar:"toolbar-key",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='teachRate[%s].orderNum' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachRate[%s].rateId'  value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'beginTime',
                        align: 'center',
                        title: '开始时间'
                    },
                    {
                        field: 'endTime',
                        align: 'center',
                        title: '结束时间'
                    },{
                        field: 'stageName',
                        align: 'center',
                        title: '培训地点'
                    },{
                        field: 'stageTarget',
                        align: 'center',
                        title: '培训岗位'
                    },{
                        field: 'peoply',
                        align: 'center',
                        title: '培训人数'
                    },{
                        field: 'remark',
                        align: 'center',
                        title: '备注'
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });
        function addTeachRateColumn() {
            var row = {
                beginTime: "",
                endTime: "",
                stageName: "",
                stageTarget: "",
                peoply: "",
                remark: "",
            }
            sub.addColumn(row,"bootstrap-table-key");
        }

        /***********************************知识产权描述*******************************************/
        var deTypes = [[${deTypes}]];
        $(function() {
            var options = {
                url: ctx + "zzlx/teachSchedule/openTeachSchedule?attId="+[[${teachAtt.attId}]],
                id:"bootstrap-table-schedule",
                toolbar:"toolbar-schedule",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='teachSchedule[%s].orderNum' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachSchedule[%s].cqId'  value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'deType',
                        align: 'center',
                        title: '类别',
                        formatter: function(value, row, index) {
                            return dictToSelect(deTypes, value);
                        }
                    },
                    {
                        field: 'number',
                        align: 'center',
                        title: '编号'
                    },{
                        field: 'deName',
                        align: 'center',
                        title: '名称'
                    },{
                        field: 'ratio',
                        align: 'center',
                        title: '对本项目的贡献度%'
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });
        function addTeachScheduleColumn() {
            var row = {
                deType: "",
                number: "",
                deName: "",
                ratio: "",
            }
            sub.addColumn(row,"bootstrap-table-schedule");
        }
        /***********************************推广方成员*******************************************/
        var roleDatas = [[${roleDatas}]];
        $(function() {
            var options = {
                url: ctx + "zzgg/teachPropeople/openTeachPeople?bizId="+[[${teachAtt.attId}]]+"&peType=tgf"+"&extra1=A",
                id: "bootstrap-table-member",
                toolbar:"toolbar-xmfz",
                modalName: "推广方成员",
                uniqueId: "userId",
                pagination: false,
                pageSize:50,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        witdh:'5%',
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='tgfPeople[%s].orderNum' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='tgfPeople[%s].peId' value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'userId',
                        align: 'center',
                        title: '工号',
                        witdh:'15%'
                    },
                    {
                        field: 'userName',
                        align: 'center',
                        title: '姓名',
                        witdh:'15%'
                    },
                    {
                        field: 'position',
                        align: 'center',
                        title: '岗位',
                        witdh:'15%'
                    },{
                        field: 'point',
                        align: 'center',
                        title: '特殊技长',
                        witdh:'15%'
                    },
                    {
                        field: 'job',
                        align: 'center',
                        title: '主要任务',
                        witdh:'15%'
                    },
                    {
                        field: 'role',
                        align: 'center',
                        title: '项目角色',
                        witdh:'15%',
                        formatter: function(value, row, index) {
                            return dictToSelect(roleDatas, value);
                        }
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });
        function addProjectMemberColumn(userCode, userName) {
            $("#teamCode").val(userCode);
            $("#teamName").val(userName);
            var teamCodes = $("#teamCode").val();
            var teamNames = $("#teamName").val();
            var teamCode = teamCodes.split(",");
            var teamName = teamNames.split(",");
            $("#bootstrap-table-member tbody").html("");
            for(var i=0;i<teamName.length;i++){
                if(teamName[i]){
                        var orderNum =$("#bootstrap-table-member").find("tr").length;
                        var len =$("#bootstrap-table-member").find("tr").length-1;
                        var tr="<tr data-index='"+len+"'>";
                        /*	tr=tr+"<td style='text-align: center;width: 5%;'><input type='checkbox'></td>";*/
                        tr=tr+"<td style='text-align: center;width: 5%;'><input type='hidden' name='tgfPeople["+len+"].orderNum' value='"+orderNum+"'>"+orderNum+"</td>";
                        tr=tr+"<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople["+len+"].userId' value="+teamCode[i]+"></td>";
                        tr=tr+"<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople["+len+"].userName' value="+teamName[i]+"></td>";
                        tr=tr+"<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople["+len+"].position' ></td>";
                        tr=tr+"<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople["+len+"].point' ></td>";
                        tr=tr+"<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople["+len+"].job'></td>";
                        tr=tr+"<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople["+len+"].role' ></td>";
                        /*tr=tr+"<td style='text-align: center;'><input class='form-control' type='text' name='fzprojectMember["+len+"].extra1' value=''></td>";*/
                        tr=tr+"<td style='text-align: center; width: 5%;'><butten type='button' class='btn btn-sm btn-primary'>查看</butten></td>";
                        tr=tr+"</tr>";
                        $(".no-records-found").remove();
                        $("#bootstrap-table-member tbody").append(tr);
                }
            }
        }

        /***********************************受让方成员*******************************************/
        $(function() {
            var projectArea = [[${need.projectArea}]];
            /*if(projectArea!=null&&projectArea!=""&&projectArea=='gfn') {*/
                var options = {
                    url: ctx + "zzgg/teachPropeople/openTeachPeople?bizId=" + [[${teachAtt.attId}]] + "&peType=srf"+"&extra1=A",
                    id: "bootstrap-table-srf",
                    toolbar: "toolbar-xmfz",
                    modalName: "受让方成员",
                    uniqueId: "userId",
                    pagination: false,
                    pageSize:50,
                    showSearch: false,
                    showRefresh: false,
                    showToggle: false,
                    showColumns: false,
                    sidePagination: "client",
                    columns: [
                        {
                            field: 'index',
                            align: 'center',
                            title: "序号",
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var columnIndex = $.common.sprintf("<input type='hidden' name='srfPeople[%s].orderNum' value='%s'>", $.table.serialNumber(index));
                                var columnId = $.common.sprintf("<input type='hidden' name='srfPeople[%s].peId' value='%s'>", index, row.id);
                                return columnIndex + $.table.serialNumber(index) + columnId;
                            }
                        },
                        {
                            field: 'userId',
                            align: 'center',
                            title: '工号',
                            witdh: '15%'
                        },
                        {
                            field: 'userName',
                            align: 'center',
                            title: '姓名',
                            witdh: '15%'
                        },
                        {
                            field: 'position',
                            align: 'center',
                            title: '岗位',
                            witdh: '15%'
                        }, {
                            field: 'point',
                            align: 'center',
                            title: '特殊技长',
                            witdh: '15%'
                        },
                        {
                            field: 'job',
                            align: 'center',
                            title: '主要任务',
                            witdh: '15%'
                        },
                        {
                            field: 'role',
                            align: 'center',
                            title: '项目角色',
                            witdh: '15%',
                            formatter: function (value, row, index) {
                                return dictToSelect(roleDatas, value);
                            }
                        }
                    ]
                };
          /*  }*/
            $.table.init(options);
            $(".no-records-found").remove();
        });

        function addShouRangColumn() {
            var shouCodes = $("#shouCode").val();
            var shouNames = $("#shouName").val();
            var shouCode = shouCodes.split(",");
            var shouName = shouNames.split(",");
            $("#bootstrap-table-srf tbody").html("");
            for(var i=0;i<shouCode.length;i++){
                if(shouCode[i]){

                var orderNum =$("#bootstrap-table-srf").find("tr").length;
                var len =$("#bootstrap-table-srf").find("tr").length-1;
                var tr="<tr data-index='"+len+"'>";
                    tr=tr+"<td style='text-align: center;width: 5%;'><input type='hidden' name='srfPeople["+len+"].orderNum' value='"+orderNum+"'>"+orderNum+"</td>";
                    tr=tr+"<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople["+len+"].userId' value="+shouCode[i]+"></td>";
                    tr=tr+"<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople["+len+"].userName' value="+shouName[i]+"></td>";
                    tr=tr+"<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople["+len+"].position' ></td>";
                    tr=tr+"<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople["+len+"].point' ></td>";
                    tr=tr+"<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople["+len+"].job'></td>";
                    tr=tr+"<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople["+len+"].role' ></td>";
                    /*tr=tr+"<td style='text-align: center;'><input class='form-control' type='text' name='fzprojectMember["+len+"].extra1' value=''></td>";*/
                    tr=tr+"<td style='text-align: center; width: 5%;'><butten type='button' class='btn btn-sm btn-primary'>查看</butten></td>";
                    tr=tr+"</tr>";
                    $(".no-records-found").remove();
                    $("#bootstrap-table-srf tbody").append(tr);
                }
            }
        }

        function addTeachPeopleColumn() {
            var row = {
                userId: "",
                userName: "",
                position: "",
                point: "",
                job: "",
                role: "",
                check: "",
            }
            sub.addColumn(row,"bootstrap-table-srf");
        }

        /***********************************进度计划表*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzgg/teachPlan/openTeachPlan?bizId="+[[${teachAtt.attId}]],
                id:"bootstrap-table-plan",
                toolbar:"toolbar-plan",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='teachPlans[%s].orderNum' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachPlans[%s].planId'  value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'beginTime',
                        align: 'center',
                        title: '阶段开始时间'
                    },
                    {
                        field: 'endTime',
                        align: 'center',
                        title: '阶段结束时间'
                    },{
                        field: 'stageName',
                        align: 'center',
                        title: '阶段名称'
                    },{
                        field: 'stageTarget',
                        align: 'center',
                        title: '阶段目标'
                    },{
                        field: 'peoply',
                        align: 'center',
                        title: '人数'
                    },{
                        field: 'days',
                        align: 'center',
                        title: '天数'
                    },{
                        field: 'total',
                        align: 'center',
                        title: '分项合计'
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });


        function addTeachPlanColumn() {
            var row = {
                beginTime: "",
                endTime: "",
                stageName: "",
                stageTarget: "",
                peoply: "",
                days: "",
                total: "",
            }
            sub.addColumn(row,"bootstrap-table-plan");
        }

        /***********************************费用估算*******************************************/
        $(function() {
            var year=new Date().getFullYear();
            var options = {
                url: ctx + "zzgg/teachCost/openTeachCost?bizId="+[[${teachAtt.attId}]]+"&costType=att",
                id: "bootstrap-table-ys",
                modalName: "费用估算",
                showFooter: true,
                footerStyle: footerStyle,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        width: "20",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='orderNum' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachCost[%s].orderNum' value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'busiYear',
                        align: 'center',
                        title: '年度',
                        footerFormatter:function (value) {
                            return "小计：";
                        }

                    },
                    {
                        field: 'travelCost',
                        align: 'center',
                        title: '差旅费',
                        formatter: function(value, row, index) {
                            return value;
                        },
                        footerFormatter:function (value) {
                           var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].travelCost!=null&&value[i].travelCost!=''){
                                    sumBalance += parseFloat(value[i].travelCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(2);
                            return  sumBalance;
                        }
                    },{
                        field: 'comCost',
                        align: 'center',
                        title: '通讯费',
                        formatter: function(value, row, index) {
                            return value;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].comCost!=null&&value[i].comCost!=''){
                                    sumBalance += parseFloat(value[i].comCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(2);
                            return  sumBalance;
                        }
                    },{
                        field: 'laborCost',
                        align: 'center',
                        title: '人工费',
                        formatter: function(value, row, index) {
                            return value;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].laborCost!=null&&value[i].laborCost!=''){
                                    sumBalance += parseFloat(value[i].laborCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(2);
                            return  sumBalance;
                        }
                    },{
                        field: 'otherCost',
                        align: 'center',
                        title: '其他费用',
                        formatter: function(value, row, index) {
                            return value;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].otherCost!=null&&value[i].otherCost!=''){
                                    sumBalance += parseFloat(value[i].otherCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(2);
                            return  sumBalance;
                        }
                    }
                ]
            };
            $(".no-records-found").remove();
            $.table.init(options);
        });
        function addTeachCostolumn() {
            var row = {
                busiYear: "",
                travelCost: "",
                comCost: "",
                laborCost: "",
                otherCost: "",

            }
            sub.addColumn(row,"bootstrap-table-ys");
        }

        //自动计算预算

        function ysSum(){
            //纵向和
            var len =$("#bootstrap-table-ys").find("tr").length;
            var countMoneyTotal=0.0;
            $("#costTotal").html(countMoneyTotal.toFixed(3));
        }

        function footerStyle(column) {
            return {
                budgetPerson: {
                    css: {  'font-weight': 'blod' }
                },
                index: {
                    css: {  'font-weight': 'blod' }
                }
            }[column.field]
        }

        // 数据字典转下拉框
        function dictToSelect(datas, value) {
            var actions = [];
            $.each(datas, function (index, dict) {
                if (dict.dictValue == ('' + value)) {
                    actions.push($.common.sprintf("<td style='text-align: center'>%s</td>"),dict.dictName);
                }
            });
            return actions.join('');
        }

    </script>
</body>
</html>