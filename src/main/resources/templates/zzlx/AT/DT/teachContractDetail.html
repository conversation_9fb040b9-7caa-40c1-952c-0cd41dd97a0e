<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('项目合同详情')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-teachContract-add" th:object="${contract}">


            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#2" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">

                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">合同名称：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:text="${contract.conHtName}"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">合同编号：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:text="${main.projectHtNum}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">项目范围：</label>
                                    <div class="col-sm-4">
                                        <div th:include="/component/radio :: init(id='projectArea',name='projectArea',
                                        businessType='KTTG',dictCode='projectArea',value=${main.projectArea} ,see=true)"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">项目名称：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${main.projectName}"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">项目编号：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">受让单位部门：</label>
                                    <div class="col-sm-4">
                                        <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.srfDeptCode)}"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">推广单位部门：</label>
                                    <div class="col-sm-4">
                                        <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.tgfDeptCode)}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">签订地点：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${contract.conSite}"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">签订日期：</label>
                                    <div class="col-sm-4" th:include="/component/date :: init(name='conDate',id='conDate',strValue=${contract.conDate},see=true)"></div>

                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">有效期限：</label>
                                    <div class="col-sm-4">
                                    <span th:utext="${contract.expiryDate}"></span>
                                    至
                                    <span th:utext="${contract.beforeDate}"></span>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">项目所属分类：</label>
                                    <div class="col-sm-4">
                                        <div th:include="/component/radio :: init(id='payKind',name='payKind',
                                        businessType='KTTG',dictCode='payKind',value=${teachFix.payKind} ,see=true)"></div>
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>

            <!--框-->
            <div class="panel-group" id="accordion2" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#fj" aria-expanded="false" class="collapsed">项目材料
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="fj" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="form-group">
                                <label class="col-sm-12 ">点击<a class="form_list_a" th:onclick="projectDetail()">此处</a>查看推广需求表</label>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-12 ">点击<a class="form_list_a" th:onclick="attDetail()">此处</a>查看技术附件</label>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-12">点击<a class="form_list_a" th:onclick="fixDetail()">此处</a>定价审批表</label>
                            </div>
                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->

            <!--框-->
            <div class="panel-group" id="accordion3" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#htfj" aria-expanded="false" class="collapsed">合同文本
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="htfj" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">

                            <div class="form-group">
                                <label class="col-sm-2 control-label">附件上传：</label>
                                <div class="col-sm-10">
                                    <div th:include="/component/attachment :: init(display='none',name='htfjId',id='htfjId',sourceId=${contract.conId},sourceModule='HTFJ',see=true)">
                                    </div>
                                </div>
                            </div>

                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->
        </form>
    </div>
    <div class="row">
        <div class="toolbar toolbar-bottom" role="toolbar">
            <button type="button" class="btn  btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返回
            </button>
        </div>
    </div>

    <script th:inline="javascript">
        var prefix = ctx + "zzlx/teachContract"

        $("#form-teachContract-add").validate({
            focusCleanup: true
        });

        //点击查看技术附件
        function attDetail() {
            layer.open({
                type: 2,
                area: ['900px', '400px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "技术附件",
                content: ctx + "zzlx/teachAtt/openAtt/"+[[${businessGuid}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }

        //点击查看定价评审表
        function fixDetail() {
            layer.open({
                type: 2,
                area: ['900px', '400px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "定价评审",
                content: ctx + "zzlx/teachFix/openFix/"+[[${businessGuid}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }

    </script>
</body>
</html>