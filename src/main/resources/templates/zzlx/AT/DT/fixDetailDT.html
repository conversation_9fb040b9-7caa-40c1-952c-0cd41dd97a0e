<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('立项定价评审')" />

    <th:block th:include="include :: baseJs" />

    <style>
        .table-center{
            text-align: center;
        }
    </style>
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-teachFix-add" th:object="${teachFix}">
            <input id="needId" name="needId"  th:value="${need.needId}" type="hidden">
            <input id="fixId" name="fixId"  th:value="${teachFix.fixId}" type="hidden">
            <input id="mainId" name="mainId"  th:value="${main.mainId}" type="hidden">
        <!--框-->
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#2" aria-expanded="false" class="collapsed">基本信息
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>

                <!--折叠区域-->
                <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目名称：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${need.projectName}"></div>
                                </div>
                                <label class="col-sm-2 control-label">项目编号：</label>
                                <div class="col-sm-4">
                                    <div class="form-control-static" th:utext="${need.projectNum}"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目范围：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/radio :: init(id='projectArea',name='projectArea',
                    businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='projectArea',value=${need.projectArea} ,see=true)"></div>
                                </div>
                            </div>
                        </div>
                        <!--                            <div class="form-group" th:include="include :: initSelectBox(see=true,labelName='项目范围',value=${need.projectArea},businessType='KTTG', dictCode='projectArea')"></div>-->
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">受让单位部门：</label>
                                <div class="col-sm-4">
                                    <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(need.srfDeptCode)}"></div>
                                </div>

                                <label class="col-sm-2 control-label">推广单位部门：</label>
                                <div class="col-sm-4">
                                    <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(need.tgfDeptCode)}"></div>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='tgfFzrCode',userNameId='tgfFzrName',value=${need.tgfFzrCode},see=true)"></div>

                                <label class="col-sm-2 control-label">推广方项目主管：</label>
                                <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='tgfXmzgCode',userNameId='tgfXmzgName',value=${need.tgfXmzgCode},see=true)"></div>
                            </div>
                        </div>
                        <div class="row ">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目类型：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/select :: init(id='projectType',name='projectType',
                                         businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='projectType',value=${need.projectType},see=true)"></div>
                                </div>
                                <label class="col-sm-2 control-label">定价方法：</label>
                                <div class="col-sm-4">
                                    <div th:include="/component/select :: init(id='payWay',name='payWay',
                                        businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='payWay',value=${teachFix.payWay},see=true)"></div>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">项目所属分类：</label>
                                <div class="col-sm-4">
                                   <!-- <select name="payKind" required th:class="form-control" id="payKind" onchange="changeKind(this)"
                                            th:with="dictData=${@dict.getDictList('KTTG','payKind')}">
                                        <option  value="">请选择</option>
                                        <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}" th:field="*{payKind}"></option>
                                    </select>-->
                                    <div th:include="/component/select :: init(id='payKind',name='payKind',
                                    businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='payKind',value=${teachFix.payKind},see=true)"></div>

                                </div>
                                <div id="jjxy" style="display: none">
                                    <label class="col-sm-2 control-label">经济效益类型：</label>
                                    <div class="col-sm-4">
                                        <div th:include="/component/select :: init(id='jjxyType',name='jjxyType',
                                    businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='A',value=${teachFix.extra1},see=true)"></div>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
            <!--框end-->
            <!--框-->
            <div class="panel-group" id="accordion1" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#ck" aria-expanded="false" class="collapsed">项目材料
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="ck" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="form-group">
                                <div th:if="${main.projectSource eq 'gdxqsb'}" class="col-sm-2 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',ctx+'zzlx/need/detailM?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                                <div th:if="${main.projectSource eq 'ndjh'}" class="col-sm-2 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',[[${@environment.getProperty('app-context.ctxKY')}]] +'web/KYKTTGPD01?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 ">点击<a class="form_list_a" th:onclick="$.modal.openTab('技术附件',ctx+'zzlx/teachAtt/openAtt/'+[[${main.mainId}]])">此处</a>查看技术附件</label>
                            </div>
                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>

            <!--框-->
            <div class="panel-group" id="accordion2" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#fj" aria-expanded="false" class="collapsed">项目材料
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="fj" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">1.技术附件：</label>
                                    <div class="col-sm-10" style="margin-bottom: 10px">
                                        <div th:if="${teachFix.isNull eq 'no'}" th:include="/component/attachment :: init(display='none',sourceId=${teachFix.fixId},sourceModule='TEACH_FJ',id='jsfjId',name='jsfjId',see=true)">
                                        </div>
                                        <div th:if="${teachFix.isNull eq 'yes'}" th:include="/component/attachment :: init(display='none',sourceId=${teachAtt.attId},sourceModule='TEACH_FJ',id='jsfjId',name='jsfjId',see=true)">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">2.定价材料：</label>
                                    <div class="col-sm-10" style="margin-bottom: 10px">
                                        <div th:if="${teachFix.isNull eq 'no'}" th:include="/component/attachment :: init(display='none',sourceId=${teachFix.fixId},sourceModule='FYGS_FJ',id='fygsId',name='fygsId',see=true)">
                                        </div>
                                        <div th:if="${teachFix.isNull eq 'yes'}" th:include="/component/attachment :: init(display='none',sourceId=${teachAtt.attId},sourceModule='FYGS_FJ',id='fygsId',name='fygsId',see=true)">
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">3.评审材料：</label>
                                    <div class="col-sm-10" style="margin-bottom: 10px">
                                        <div th:if="${teachFix.isNull eq 'ynoes'}" th:include="/component/attachment :: init(display='none',sourceId=${teachFix.fixId},sourceModule='PSCL_FJ',id='psclId',name='psclId',see=true)">
                                        </div>
                                        <div th:if="${teachFix.isNull eq 'yes'}" th:include="/component/attachment :: init(display='none',sourceId=${teachAtt.attId},sourceModule='FYGS_FJ',id='fygsId',name='fygsId',see=true)">
                                        </div>
                                    </div>
                                </div>
                            </div>
<!--                            <div class="form-group" th:include="include :: layui-upload(sourceId=${teachAtt.attId},sourceModule='TEACH_FJ',id='jsfjId',name='jsfjId',labelName='1.技术附件',see=true)"></div>-->
<!--                            <div class="form-group" th:include="include :: layui-upload(sourceId=${teachAtt.attId},sourceModule='FYGS_FJ',id='fygsId',name='fygsId',labelName='2.定价材料',see=true)"></div>-->
<!--                            <div class="form-group" th:include="include :: layui-upload(sourceId=${teachAtt.attId},sourceModule='PSCL_FJ',id='psclId',name='psclId',labelName='3.评审材料',see=true)"></div>-->

                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->

            <div class="panel-group" id="accordion5"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmnr" class="collapsed">
                                定价依据
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmnr" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${teachFix.fixDjyj}"></div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion3" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jfystb" class="collapsed">
                                费用估算（单位：万元）
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="jfystb" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <p class="select-title">实施费用估算</p>
                                    <input name="costTotal" min="0" class="form-control" type="hidden" />
                                   <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-ys"></table>
                                        <table class="table table-bordered table-hover" style="margin-top:20px;border: 1px solid #e7e7e7;">
                                            <tfoot>
                                                <tr>
                                                    <td style="text-align: center; ">现场支撑人日数</td>
                                                    <td style="text-align: left; ">
                                                        <div class="form-control-static" th:utext="${teachFix.payDay}"></div>
                                                    </td>
                                                    <td style="text-align: center; ">远程支撑系数</td>
                                                    <td style="text-align: left; "}>
                                                        <div class="form-control-static" th:utext="${teachFix.payXs}"></div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; ">实施费用(A)万元</td>
                                                    <td style="text-align: left; ">
                                                        <div class="form-control-static" th:utext="${#numbers.formatDecimal(teachFix.payTotal,1,3)}" style="text-align: left"></div>
                                                    </td>
                                                    <td style="text-align: center; ">管理费(B)%</td>
                                                    <td style="text-align: left; ">
                                                        <div class="form-control-static" th:utext="${#numbers.formatDecimal(teachFix.payManxs,1,3)}"></div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; ">项目费用(万元)</td>
                                                    <td colspan="3" style="text-align: left; ">
                                                        <div class="form-control-static" th:utext="${#numbers.formatDecimal(teachFix.proTotal,1,3)}"></div>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; ">定价公式</td>
                                                    <td colspan="3" style="text-align: left; ">
                                                        项目费用=A*(1 + B%)   &nbsp;&nbsp;&nbsp;  <span style="color: red">注： 上述费用含6%的增值税</span>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td style="text-align: center; ">知识产权估算(万元)</td>
                                                    <td style="text-align: left; ">
                                                        <div class="form-control-static" th:utext="${#numbers.formatDecimal(teachFix.knowledgeEstimate,1,3)}"></div>
                                                    </td>
                                                    <td style="text-align: center; ">经济效益估算(万元)</td>
                                                    <td style="text-align: left; ">
                                                        <div class="form-control-static" th:utext="${#numbers.formatDecimal(teachFix.economyEstimate,1,3)}"></div>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                        <span style="color: red">注：知识产权免费</span>
                                        <p class="select-title">经济效益算法:</p>
                                        <div class="form-control-static" th:utext="${teachFix.fixJjxy}"></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmmb" class="collapsed">
                                项目可能存在的风险及防范措施
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmmb" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${teachFix.fixStep}"></div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion6"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmpj" class="collapsed">
                                项目对安全、环境影响的评价
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmpj" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12 select-table ">
                                    <table class="table table-bordered table-hover table-center">
                                       <tr>
                                           <th style="text-align: center">序号</th>
                                           <th style="text-align: center">内容</th>
                                           <th style="text-align: center">评价</th>
                                       </tr>
                                        <tr>
                                            <td>1</td>
                                            <td>该项目的开展是否没有增加新的环境因素？（注：对水/大气/土壤污染、噪声、能源、资源等的影响）</td>
                                            <td>
                                                <div th:if="${teachFix.isHjys=='1'}">是</div>
                                                <div th:if="${teachFix.isHjys=='0'}">否</div>
                                            </td>
                                        </tr>
                                        <tbody id="pjtr" style="display: none">
                                        <tr>
                                            <td>2</td>
                                            <td>若新的环境因素，是否有配套的污染预防措施？（注：污染预防的潜在利益包括有害的环境影响、
                                                提高效益和降低成本）</td>
                                            <td>
                                                <div th:if="${teachFix.isYfcs=='1'}">是</div>
                                                <div th:if="${teachFix.isYfcs=='0'}">否</div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>3</td>
                                            <td>配套的污染预防措施是否有效，是否符合ISO14001和相关法律、
                                                法规的要求？</td>
                                            <td>
                                                <div th:if="${teachFix.isBzfl=='1'}">是</div>
                                                <div th:if="${teachFix.isBzfl=='0'}">否</div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>4</td>
                                            <td>该项目的开展是否没有增加可能发生事故或职业病的新的危险源？或降低了原危险源对职业健康安全的影响？</td>
                                            <td>
                                                <div th:if="${teachFix.isSgzyb=='1'}">是</div>
                                                <div th:if="${teachFix.isSgzyb=='0'}">否</div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>5</td>
                                            <td>若新的危险源，是否有配套的预防措施？</td>
                                            <td>
                                                <div th:if="${teachFix.isPtcs=='1'}">是</div>
                                                <div th:if="${teachFix.isPtcs=='0'}">否</div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>6</td>
                                            <td>针对新的危险源，配套的预防措施是否有效，是否符合ISO18001和相关法律、法规的要求？</td>
                                            <td>
                                                <div th:if="${teachFix.isFlfg=='1'}">是</div>
                                                <div th:if="${teachFix.isFlfg=='0'}">否</div>
                                            </td>
                                        </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="row">
		<div class="toolbar toolbar-bottom" role="toolbar" >

            <button type="button" class="btn btn-danger"
				onclick="closeItem()">
				<i class="fa fa-reply-all"></i>返回
			</button>
		</div>
	</div>

    <script th:inline="javascript">
        var prefix = ctx + "zzlx/teachFix";

        $("#form-teachFix-add").validate({
            focusCleanup: true
        });


        //点击查看技术附件
        function attDetail() {
            layer.open({
                type: 2,
                area: ['1200px', '590px'],
                fix: false,
                //不固定
                maxmin: true,
                shade: 0.3,
                title: "技术附件",
                content: ctx + "zzlx/teachAtt/openAtt/"+[[${main.mainId}]],
                btn: ['关闭'],
                // 弹层外区域关闭
                shadeClose: true,
                cancel: function(index) {
                    return true;
                }
            });
        }
        /***********************************费用估算*******************************************/
        $(function() {
            var year=new Date().getFullYear();
            var isNull = [[${teachFix.isNull}]];
            var id = "";
            var typeT ="";
            if(isNull=="no"){
                id=[[${teachFix.fixId}]];
                typeT = "pay";
            }else{
                id=[[${teachAtt.attId}]];
                typeT = "att";
            }
            var options = {
                url: ctx + "zzgg/teachCost/teachCostList?bizId="+id+"&costType="+typeT,
                id: "bootstrap-table-ys",
                modalName: "费用估算",
                showFooter: true,
                footerStyle: footerStyle,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        width: "20",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='orderNum' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachCost[%s].orderNum' value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'busiYear',
                        align: 'center',
                        title: '年度',
                        footerFormatter:function (value) {
                            return "总计：";
                        }

                    },
                    {
                        field: 'travelCost',
                        align: 'center',
                        title: '差旅费',
                        formatter: function(value, row, index) {
                            value =value.toFixed(3);
                            return value;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].travelCost!=null&&value[i].travelCost!=''){
                                    sumBalance += parseFloat(value[i].travelCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(3);
                            return  sumBalance;
                        }
                    },{
                        field: 'comCost',
                        align: 'center',
                        title: '通讯费',
                        formatter: function(value, row, index) {
                            value =value.toFixed(3);
                            return value;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].comCost!=null&&value[i].comCost!=''){
                                    sumBalance += parseFloat(value[i].comCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(3);
                            return  sumBalance;
                        }
                    },{
                        field: 'laborCost',
                        align: 'center',
                        title: '人工费',
                        formatter: function(value, row, index) {
                            value =value.toFixed(3);
                            return value;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].laborCost!=null&&value[i].laborCost!=''){
                                    sumBalance += parseFloat(value[i].laborCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(3);
                            return  sumBalance;
                        }
                    },{
                        field: 'otherCost',
                        align: 'center',
                        title: '其他费用',
                        formatter: function(value, row, index) {
                            value =value.toFixed(3);
                            return value;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].otherCost!=null&&value[i].otherCost!=''){
                                    sumBalance += parseFloat(value[i].otherCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(3);
                            return  sumBalance;
                        }
                    }
                ]
            };
            $(".no-records-found").remove();
            $.table.init(options);
        });


        function addTeachCostolumn() {
            var row = {
                busiYear: "",
                travelCost: "",
                comCost: "",
                laborCost: "",
                otherCost: "",

            }
            sub.addColumn(row,"bootstrap-table-ys");
        }

        //自动计算预算
        function ysSum(){
            //纵向和
            var len =$("#bootstrap-table-ys").find("tr").length;
            var travelCostTotal=0.0;
            var comCostTotal=0.0;
            var laborCostTotal=0.0;
            var otherCostTotal=0.0;
            var countMoneyTotal=0.0;
            for(var i=0;i<len-2;i++){
                //差旅费
                var travelCostMoney=$("input[name='teachCost["+i+"].travelCost']").val();
                if(travelCostMoney==null||travelCostMoney==''){
                    travelCostMoney=0.0;
                }
                travelCostTotal+=parseFloat(travelCostMoney);

                //通讯费
                var comCostMoney=$("input[name='teachCost["+i+"].comCost']").val();
                if(comCostMoney==null||comCostMoney==''){
                    comCostMoney=0.0;
                }
                comCostTotal+=parseFloat(comCostMoney);

                //人工费
                var laborCostMoney=$("input[name='teachCost["+i+"].laborCost']").val();
                if(laborCostMoney==null||laborCostMoney==''){
                    laborCostMoney=0.0;
                }
                laborCostTotal+=parseFloat(laborCostMoney);

                //其他费用
                var otherCostMoney=$("input[name='teachCost["+i+"].otherCost']").val();
                if(otherCostMoney==null||otherCostMoney==''){
                    otherCostMoney=0.0;
                }
                otherCostTotal+=parseFloat(otherCostMoney);

                //总和
                countMoneyTotal=parseFloat(travelCostTotal)+parseFloat(comCostTotal)+parseFloat(laborCostTotal)+ parseFloat(otherCostTotal);
                if(isNaN(countMoneyTotal)){
                    countMoneyTotal=0.0;
                }
            }

            $("#bootstrap-table-ys").find("tfoot").find("th").eq(3).find(".th-inner").html(travelCostTotal.toFixed(3));
            $("#bootstrap-table-ys").find("tfoot").find("th").eq(4).find(".th-inner").html(comCostTotal.toFixed(3));
            $("#bootstrap-table-ys").find("tfoot").find("th").eq(5).find(".th-inner").html(laborCostTotal.toFixed(3));
            $("#bootstrap-table-ys").find("tfoot").find("th").eq(6).find(".th-inner").html(otherCostTotal.toFixed(3));
            $("#costTotal").val(countMoneyTotal);
            $("#totala").html(countMoneyTotal);
            $("#payTotal").val(countMoneyTotal);
            changeTotal();
            /* $("#bootstrap-table-ys").find("tfoot").find("th").eq(11).find(".th-inner").html(countMoneyTotal.toFixed(4));
             $("#extra2").val(countMoneyTotal.toFixed(4));*/
        }

        function footerStyle(column) {
            return {
                budgetPerson: {
                    css: {  'font-weight': 'blod' }
                },
                index: {
                    css: {  'font-weight': 'blod' }
                }
            }[column.field]
        }



        function changeRadio(){
            var isHjys = [[${teachFix.isHjys}]];
            var isYfcs = [[${teachFix.isYfcs}]];
            var isBzfl = [[${teachFix.isBzfl}]];
            var isSgzyb = [[${teachFix.isSgzyb}]];
            var isPtcs = [[${teachFix.isPtcs}]];
            var isFlfg = [[${teachFix.isFlfg}]];
            $("input[name='isHjys']").each(function () {
                if($(this).val()==isHjys){
                    $(this).prop('checked',true);
                }
            })
            $("input[name='isYfcs']").each(function () {
                if($(this).val()==isYfcs){
                    $(this).prop('checked',true);
                }
            })
            $("input[name='isBzfl']").each(function () {
                if($(this).val()==isBzfl){
                    $(this).prop('checked',true);
                }
            })
            $("input[name='isSgzyb']").each(function () {
                if($(this).val()==isSgzyb){
                    $(this).prop('checked',true);
                }
            })
            $("input[name='isPtcs']").each(function () {
                if($(this).val()==isPtcs){
                    $(this).prop('checked',true);
                }
            })
            $("input[name='isFlfg']").each(function () {
                if($(this).val()==isFlfg){
                    $(this).prop('checked',true);
                }
            })
        }
        changeKind();
        function changeKind(){
            var pp = [[${teachFix.payKind}]];
            if(pp=='A'){
                $("#jjxy").show();
            }else{
                $("#jjxy").hide();
            }

        }
        changePj();
        function changePj(){
            var rr = [[${teachFix.isHjys}]];
            if(rr=='1'){
                $("#pjtr").show();
            }else{
                $("#pjtr").hide();
            }
        }

    </script>
</body>
</html>