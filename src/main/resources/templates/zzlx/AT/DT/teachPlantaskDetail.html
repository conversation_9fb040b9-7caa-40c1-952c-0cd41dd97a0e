<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('计划任务书详情')" />
    <th:block th:include="include :: baseJs" />


</head>
<body class="white-bg">


    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <form class="form-horizontal m" id="form-planTask-add" th:object="${plan}">

            <!--框-->
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#2" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">

                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">项目名称：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${main.projectName}"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">项目编号：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${main.projectNum}"></div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">项目范围：</label>
                                    <div class="col-sm-4">
                                        <div th:include="/component/radio :: init(id='projectArea',name='projectArea',
                                    businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='projectArea',value=${main.projectArea} ,see=true)"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">受让单位部门：</label>
                                    <div class="col-sm-4">
                                        <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.srfDeptCode)}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">合同名称：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:text="${main.projectHtName}"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">合同编号：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:text="${main.projectHtNum}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">推广单位部门：</label>
                                    <div class="col-sm-4">
                                        <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(main.tgfDeptCode)}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                    <div class="col-sm-4">
                                        <div th:include="/component/selectUser :: init(userCodeId='tgfFzrCode',userNameId='tgfFzrName',value=${main.tgfFzrCode},see=true)"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">联系电话：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${main.tgfXmfzrTel}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">签发部门：</label>
                                    <div class="col-sm-4">
                                        <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(plan.taskDept)}"></div>
                                    </div>
                                </div>
                            </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">签发日期：</label>
                                    <div class="col-sm-4">
                                        <div class="form-control-static" th:utext="${plan.taskDate}"></div>
                                    </div>
                                    <label class="col-sm-2 control-label">编制日期：</label>
                                    <div class="col-sm-4">
                                          <div class="form-control-static" th:utext="${plan.extra1}"></div>
                                    </div>
                                </div>
                             </div>
                            <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label">项目所属分类：</label>
                                    <div class="col-sm-4">
                                        <div th:include="/component/select :: init(id='payKind', name='payKind',
                                businessType=*{constants.BUSINESS_TYPE_KTTG}, dictCode='payKind',value=${teachFix.payKind},isfirst=true,see=true)"></div>
                                    </div>
                                </div>
                            </div>

                    <div>
                        <div class="form-group"><label class="col-sm-2 control-label"><b>项目起止日期</b></label></div>
                    </div>
                     <div class="row">
                        <div class="form-group ">
                            <label class="col-sm-2 control-label">项目开始日期：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static" th:utext="${plan.prostartDate}"></div>
                            </div>
                            <label class="col-sm-2 control-label">项目结束日期：</label>
                            <div class="col-sm-4">
                                <div class="form-control-static" th:utext="${plan.proendDate}"></div>
                            </div>
                        </div>
                      </div>

                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>

            <div class="panel-group" id="accordion2"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmgx" class="collapsed">
                                项目范围
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmgx" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${plan.taskArea}"></div>

                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion3"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmmb" class="collapsed">
                                项目目标
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmmb" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${plan.taskTarget}"></div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmfw" class="collapsed">
                                项目内容
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmfw" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${plan.taskContent}"></div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion10" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jdjh" class="collapsed">
                                进度计划
                                <span class="pull-right">
                <i class="fa fa-chevron-down"></i>
            </span>
                            </a>
                        </h4>
                    </div>
                    <div id="jdjh" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                  <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-plan"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


            <div class="panel-group" id="accordion7" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmzcytb" class="collapsed">
                                项目组织体系
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmzcytb" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <p class="select-title">受让方成员</p>
                                  <!--  <button type="button" class="btn btn-white btn-sm" onclick="addTeachPeopleColumn()"><i class="fa fa-plus"> 增加</i></button>
                                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>
-->
                                    <input id="shouCode"  name="shouCode" type="hidden">
                                    <input id="shouName"  name="shouName" type="hidden">
<!--                                        <button type="button" class="btn btn-white btn-sm"  onclick="choiceUser('shouCode','shouName','M',null,'addShouRangColumn')"><i class="fa fa-plus"> 添加成员</i></button>-->
                                    <div class="col-sm-12 select-table " style="margin-bottom: 30px">
                                        <table id="bootstrap-table-srf"></table>
                                    </div>
                                    <br><br>
                                    <p class="select-title">推广方成员</p>
                                    <input id="teamCode"  name="teamCode" type="hidden">
                                    <input id="teamName"  name="teamName" type="hidden">
<!--                                    <button type="button" class="btn btn-white btn-sm"  onclick="choiceUser('teamCode','teamName','M',null,'addProjectMemberColumn')"><i class="fa fa-plus"> 添加成员</i></button>-->
                                    <!--<button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>-->
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-member"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!--第三块-->
            <div class="panel-group" id="accordion3" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title"><a data-toggle="collapse" data-parent="#version" href="#3"
                                                   aria-expanded="false"
                                                   class="collapsed">项目内部协作及分工
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <span
                                    class="pull-right"><i
                                    class="fa fa-chevron-down" aria-hidden="true"></i></span></a></h4>
                    </div>
                    <!--折叠区域-->
                    <div id="3" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-9">
                                    <textarea name="taskXzfg" class="form-control" th:utext="${plan.taskXzfg}"></textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--第三块end-->


            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jfystb" class="collapsed">
                                <span style="color: red">*</span>费用估算（单位：万元）
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="jfystb" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <p class="select-title">实施费用估算</p>
<!--                                    <button type="button" class="btn btn-white btn-sm" onclick="addTeachCostolumn()"><i class="fa fa-plus"> 增加</i></button>-->
<!--                                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>-->
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-ys"></table>
                                        <table class="table table-bordered table-hover">
                                            <tfoot style="margin-right: 0px;">
                                            <tr>
                                                <th style="text-align: center;  width:127px;">
                                                    <div class="th-inner"></div>
                                                    <div class="fht-cell" style="width: auto;"></div>
                                                </th>
                                                <th style="text-align: left;width: 117px;">
                                                    <div class="th-inner" style="width: 117px;">合计（万元）：</div>
                                                </th>
                                                <th style="text-align: left; ">
                                                    <div class="th-inner">
                                                        <div class="form-control-static" id="costTotal" th:utext="${#numbers.formatDecimal(plan.payTotal,1,3)}" style="text-align: left"></div>
                                                    </div>
                                                </th>
                                            </tr>
                                            </tfoot>
                                        </table>
                                        <table class="table table-bordered table-hover" style="margin-top:20px;border: 1px solid #e7e7e7;">
                                            <tfoot>
                                            <tr>
                                                <td style="text-align: center; ">现场支撑人日数</td>
                                                <td style="text-align: center; ">
                                                    <div class="form-control-static" th:utext="${plan.payDay}" ></div>
                                                </td>
                                                <td style="text-align: center; ">远程支撑系数</td>
                                                <td style="text-align: center; ">
                                                    <div class="form-control-static" th:utext="${#numbers.formatDecimal(plan.payXs,1,2)}"></div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="text-align: center; ">实施费用(A)万元</td>
                                                <td style="text-align: center; ">
                                                    <div class="form-control-static" id="totala" th:utext="${#numbers.formatDecimal(plan.payTotal,1,3)}" style="text-align: left"></div>
                                                </td>
                                                <td style="text-align: center; ">管理费(B)%</td>
                                                <td style="text-align: left; ">
                                                    <div class="form-control-static" th:utext="${plan.payManxs}"></div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="text-align: center; ">项目费用</td>
                                                <td colspan="3" style="text-align: left; ">
                                                    <div class="form-control-static" id="projectTotal" th:utext="${#numbers.formatDecimal(plan.proTotal,1,3)}" style="text-align: left"></div>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="text-align: center; ">定价公式</td>
                                                <td colspan="3" style="text-align: left; ">
                                                    项目费用=A*(1 + B%)   &nbsp;&nbsp;&nbsp;  <span style="color: red">注： 上述费用含6%的增值税</span>
                                                </td>
                                            </tr>
                                            <tr>
                                                <td style="text-align: center; ">知识产权估算(万元)</td>
                                                <td style="text-align: left; ">
                                                    <div class="form-control-static" th:utext="${#numbers.formatDecimal(plan.knowledgeEstimate,1,3)}"></div>
                                                </td>

                                                <td style="text-align: center; ">经济效益估算(万元)</td>
                                                <td style="text-align: left; ">
                                                    <div class="form-control-static" th:utext="${#numbers.formatDecimal(plan.economyEstimate,1,3)}"></div>
                                                </td>

                                            </tr>
                                          <!--  <tr>
                                                <td colspan="4">&nbsp;&nbsp;说明：<br/>
                                                    1、差旅费：按照各基地相关管理制度执行<br/>
                                                    2、通讯费：按照各基地相关管理制度执行<br/>
                                                    3、人工费：按各基地岗位类别计价标准核算<br/>
                                                    4、其他费用是指内部检测费、测试费</td>
                                            </tr>-->
                                            </tfoot>
                                        </table>
                                        <p class="select-title">经济效益算法:</p>
                                        <div class="form-control-static" th:utext="${plan.taskJjxy}"></div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion12"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xtsy" class="collapsed">
                                项目验收标准及方式
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xtsy" class="panel-collapse collapse in">
                        <div class="form-control-static" th:utext="${plan.taskMethod}"></div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion11"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#ysbz" class="collapsed">
                               相关附件
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="ysbz" class="panel-collapse collapse in">
<!--                        <div class="form-group" th:include="include :: layui-upload(see=true,sourceId=${plan.taskId}, sourceModule='PLAN_FJ',id='planFj',name='planFj',labelName='附件：',isrequired=true)"></div>-->
                        <div class="form-group">
                            <label class="col-sm-2 control-label">附件：</label>
                            <div class="col-sm-10">
                                <div th:include="/component/attachment :: init(display='none',name='planFj',id='planFj',sourceId=${plan.taskId},sourceModule='PLAN_FJ',see=true)">
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </form>
    </div>
    <div class="row">
        <div class="toolbar toolbar-bottom" role="toolbar">
            <button type="button" class="btn  btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返回
            </button>
        </div>
    </div>



    <script th:inline="javascript">
        var prefix = ctx + "zzlx/teachPlantask";

        $("#form-teachAtt-add").validate({
            focusCleanup: true
        });


        var roleDatas = [[${roleDatas}]];


        /***********************************推广方成员*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzgg/teachPropeople/teachPeopleList?bizId="+[[${plan.taskId}]]+"&peType=tgf&extra1=P",
                id: "bootstrap-table-member",
                toolbar:"toolbar-xmfz",
                modalName: "推广方成员",
                uniqueId: "userId",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    /*{
                        checkbox: true
                    },*/
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        witdh:'5%',
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='tgfPeople[%s].orderNum' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='tgfPeople[%s].peId' value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'userId',
                        align: 'center',
                        title: '工号'
                    },
                    {
                        field: 'userName',
                        align: 'center',
                        title: '姓名'
                    },
                    {
                        field: 'position',
                        align: 'center',
                        title: '岗位'
                    },{
                        field: 'point',
                        align: 'center',
                        title: '特殊技长'
                    },
                    {
                        field: 'job',
                        align: 'center',
                        title: '主要任务'
                    },
                    {
                        field: 'role',
                        align: 'center',
                        title: '项目角色',
                        witdh:'15%',
                        formatter: function(value, row, index) {
                            return dictToSelect(roleDatas, value);
                        }
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });

        /***********************************受让方成员*******************************************/
        $(function() {
                var options = {
                    url: ctx + "zzgg/teachPropeople/teachPeopleList?bizId=" + [[${plan.taskId}]] + "&peType=srf&extra1=P",
                    id: "bootstrap-table-srf",
                    toolbar: "toolbar-xmfz",
                    modalName: "受让方成员",
                    uniqueId: "userId",
                    pagination: false,
                    showSearch: false,
                    showRefresh: false,
                    showToggle: false,
                    showColumns: false,
                    sidePagination: "client",
                    columns: [
                       /* {
                            checkbox: true
                         },*/
                        {
                            field: 'index',
                            align: 'center',
                            title: "序号",
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var columnIndex = $.common.sprintf("<input type='hidden' name='srfPeople[%s].orderNum' value='%s'>", $.table.serialNumber(index));
                                var columnId = $.common.sprintf("<input type='hidden' name='srfPeople[%s].peId' value='%s'>", index, row.id);
                                return columnIndex + $.table.serialNumber(index) + columnId;
                            }
                        },
                        {
                            field: 'userId',
                            align: 'center',
                            title: '工号'
                        },
                        {
                            field: 'userName',
                            align: 'center',
                            title: '姓名'
                        },
                        {
                            field: 'position',
                            align: 'center',
                            title: '岗位'
                        }, {
                            field: 'point',
                            align: 'center',
                            title: '特殊技长'
                        },
                        {
                            field: 'job',
                            align: 'center',
                            title: '主要任务'
                        },
                        {
                            field: 'role',
                            align: 'center',
                            title: '项目角色',
                            witdh: '15%',
                            formatter: function (value, row, index) {
                                return dictToSelect(roleDatas, value);
                            }
                        }
                    ]
                };
            $.table.init(options);
            $(".no-records-found").remove();
        });
        function addTeachPeopleColumn() {
            var row = {
                userId: "",
                userName: "",
                position: "",
                point: "",
                job: "",
                role: "",
            }
            sub.addColumn(row,"bootstrap-table-srf");
        }

        // 数据字典转下拉框
        function dictToSelect(datas, value) {
            var actions = [];
            $.each(datas, function (index, dict) {
                console.log(value,dict.dictValue == (value));
                if (dict.dictValue == (value)) {
                    actions.push($.common.sprintf("%s", dict.dictName));
                }
            });
            return actions.join('');
        }

        /***********************************进度计划表*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzgg/teachPlan/teachPlanList?bizId="+[[${plan.taskId}]],
                id:"bootstrap-table-plan",
                toolbar:"toolbar-plan",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='teachPlans[%s].orderNum' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachPlans[%s].planId'  value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'beginTime',
                        align: 'center',
                        title: '阶段开始时间'
                    },
                    {
                        field: 'endTime',
                        align: 'center',
                        title: '阶段结束时间'
                    },{
                        field: 'stageName',
                        align: 'center',
                        title: '阶段名称'
                    },{
                        field: 'stageTarget',
                        align: 'center',
                        title: '阶段目标'
                    },{
                        field: 'peoply',
                        align: 'center',
                        title: '人数'
                    },{
                        field: 'days',
                        align: 'center',
                        title: '天数'
                    },{
                        field: 'total',
                        align: 'center',
                        title: '分项合计'
                    }
                ]
            };
            $.table.init(options);
            $(".no-records-found").remove();
        });


        function addTeachPlanColumn() {
            var row = {
                beginTime: "",
                endTime: "",
                stageName: "",
                stageTarget: "",
                peoply: "",
                days: "",
                total: "",
            }
            sub.addColumn(row,"bootstrap-table-plan");
        }

        /***********************************费用估算*******************************************/
        $(function() {
            var year=new Date().getFullYear();
            var planId = [[${plan.taskId}]];
            if(planId==null||planId==""){
                planId = [[${teachFix.fixId}]]
            }
            var options = {
                url: ctx + "zzgg/teachCost/teachCostList?bizId="+planId+"&costType=pay",
                id: "bootstrap-table-ys",
                modalName: "费用估算",
                showFooter: true,
                footerStyle: footerStyle,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        width: "20",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='orderNum' value='%s'>", $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachCost[%s].orderNum' value='%s'>", index, row.id);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'busiYear',
                        align: 'center',
                        title: '年度',
                        footerFormatter:function (value) {
                            return "小计：";
                        }

                    },
                    {
                        field: 'travelCost',
                        align: 'center',
                        title: '差旅费',
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].travelCost!=null&&value[i].travelCost!=''){
                                    sumBalance += parseFloat(value[i].travelCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(3);
                            return  sumBalance;
                        }
                    },{
                        field: 'comCost',
                        align: 'center',
                        title: '通讯费',
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].comCost!=null&&value[i].comCost!=''){
                                    sumBalance += parseFloat(value[i].comCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(3);
                            return  sumBalance;
                        }
                    },{
                        field: 'laborCost',
                        align: 'center',
                        title: '人工费',
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].laborCost!=null&&value[i].laborCost!=''){
                                    sumBalance += parseFloat(value[i].laborCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(3);
                            return  sumBalance;
                        }
                    },{
                        field: 'otherCost',
                        align: 'center',
                        title: '其他费用',
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].otherCost!=null&&value[i].otherCost!=''){
                                    sumBalance += parseFloat(value[i].otherCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(3);
                            return  sumBalance;
                        }
                    }
                ]
            };
            $(".no-records-found").remove();
            $.table.init(options);
        });


        function addTeachCostolumn() {
            var row = {
                busiYear: "",
                travelCost: "",
                comCost: "",
                laborCost: "",
                otherCost: "",

            }
            sub.addColumn(row,"bootstrap-table-ys");
        }

        //自动计算预算
        function ysSum(){
            //纵向和
            var len =$("#bootstrap-table-ys").find("tr").length;
            var travelCostTotal=0.0;
            var comCostTotal=0.0;
            var laborCostTotal=0.0;
            var otherCostTotal=0.0;
            var countMoneyTotal=0.0;
            for(var i=0;i<len-2;i++){
                //差旅费
                var travelCostMoney=$("input[name='teachCost["+i+"].travelCost']").val();
                if(travelCostMoney==null||travelCostMoney==''){
                    travelCostMoney=0.0;
                }
                travelCostTotal+=parseFloat(travelCostMoney);

                //通讯费
                var comCostMoney=$("input[name='teachCost["+i+"].comCost']").val();
                if(comCostMoney==null||comCostMoney==''){
                    comCostMoney=0.0;
                }
                comCostTotal+=parseFloat(comCostMoney);

                //人工费
                var laborCostMoney=$("input[name='teachCost["+i+"].laborCost']").val();
                if(laborCostMoney==null||laborCostMoney==''){
                    laborCostMoney=0.0;
                }
                laborCostTotal+=parseFloat(laborCostMoney);

                //其他费用
                var otherCostMoney=$("input[name='teachCost["+i+"].otherCost']").val();
                if(otherCostMoney==null||otherCostMoney==''){
                    otherCostMoney=0.0;
                }
                otherCostTotal+=parseFloat(otherCostMoney);

                //总和
                countMoneyTotal=parseFloat(travelCostTotal)+parseFloat(comCostTotal)+parseFloat(laborCostTotal)+ parseFloat(otherCostTotal);
                if(isNaN(countMoneyTotal)){
                    countMoneyTotal=0.0;
                }
            }

            $("#bootstrap-table-ys").find("tfoot").find("th").eq(3).find(".th-inner").html(travelCostTotal.toFixed(3));
            $("#bootstrap-table-ys").find("tfoot").find("th").eq(4).find(".th-inner").html(comCostTotal.toFixed(3));
            $("#bootstrap-table-ys").find("tfoot").find("th").eq(5).find(".th-inner").html(laborCostTotal.toFixed(3));
            $("#bootstrap-table-ys").find("tfoot").find("th").eq(6).find(".th-inner").html(otherCostTotal.toFixed(3));
            $("#costTotal").text(countMoneyTotal.toFixed(3));
            $("input[name='payTotal']").val(countMoneyTotal.toFixed(3));
            $("#totala").text(countMoneyTotal.toFixed(3));

            changeTotal();
        }

        function footerStyle(column) {
            return {
                budgetPerson: {
                    css: {  'font-weight': 'blod' }
                },
                index: {
                    css: {  'font-weight': 'blod' }
                }
            }[column.field]
        }

        function changeTotal(){
            var a = $("input[name='payTotal']").val();
            var b = $("input[name='payManxs']").val();
            /*(总计*(1+12/100)*/
            var total = parseFloat(a)*(1+parseFloat(b)/100);
            $("#proTotal").val(total.toFixed(3));
            $("#projectTotal").html(total.toFixed(3));
        }

    </script>
</body>
</html>