<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('技术附件通知书')" />

    <th:block th:include="include :: baseJs" />
</head>
<body class="white-bg">
    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <!--框-->
        <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#2" aria-expanded="false" class="collapsed">基本信息
                        &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                        <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                    </h4>
                </div>
                <!--折叠区域-->
                <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                    <div class="panel-body">

                <form class="form-horizontal m" id="form-teachAttsend-add" th:object="${teachAttsend}">
                    <div class="form-group">
                        <label class="col-sm-3 control-label">推广单位部门：</label>
                        <div class="col-sm-8">
                            <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(need.tgfDeptCode)}"></div>
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="form-group  col-sm-3"></div>
                        <!-- &#8195; &emsp;&emsp;&#8195; &emsp;&#8195;-->
                        经研究，同意
                        ”<span class="form-control-static" style="font-weight: bold" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(need.srfDeptCode)}"></span>“
                        的” <span class="form-control-static" style="font-weight: bold" th:utext="${need.projectName}"></span>“推广需求，
                        请按公司管理制度的有关规定，在
                        <span class="form-control-static" style="font-weight: bold" th:utext="${teachAttsend?.tzsJzdate}"></span>
                        <input id="tzsJzdate" name="tzsJzdate"  th:value="${teachAttsend?.tzsJzdate}" type="hidden">

                        <div class="form-group  col-sm-3"></div> 前组织完成技术附件表。</div>
                    <div class="form-group  col-sm-3"></div>
                    （<span style="color: red">未在截止日期完成技术附件提交，系统自动终止项目</span>）

                    <br/>
                    <div class="form-group" style="text-align: right;margin-right: 200px;">
                        <input id="tzsCode" name="tzsCode"  th:value="${teachAttsend?.tzsCode}" type="hidden">
                        <input id="tzsName" name="tzsName"  th:value="${teachAttsend?.tzsName}" type="hidden">
                       <!-- <p class="select-title"  th:utext="${teachAttsend?.tzsName}"></p>-->
                        <span  th:include="/component/select :: init(see=true,value=${need.tgfDwdeptCode},businessType='KTTG', dictCode='rewardNoticeDept')"></span>

                        日期：<span class="form-control-static" th:utext="${teachAttsend?.tzsDate}"></span>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">推广需求表：</label>
                        <div th:if="${main.projectSource eq 'gdxqsb'}" class="col-sm-5 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',ctx+'zzlx/need/detailM?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                        <div th:if="${main.projectSource eq 'ndjh'}" class="col-sm-5 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',[[${@environment.getProperty('app-context.ctxKY')}]] +'web/KYKTTGPD01?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                    </div>
                    <div class="form-group">
                        <label class="col-sm-3 control-label">推广方项目负责人：</label>
                        <div th:if="${!#strings.isEmpty(need.tgfFzrName)}" class="col-sm-8 form-control-static" th:utext="${need.tgfFzrName}"></div>
                        <div th:if="${#strings.isEmpty(need.tgfFzrName)}" th:include="/component/selectUser :: init(userCodeId='tgfFzrCode',userNameId='tgfFzrCode',value=${need.tgfFzrCode},selectType='S',see=true)"></div>
                    </div>
                </form>
                    </div>
                </div>
                <!--折叠区域end-->
            </div>
        </div>
        <!--框end-->
    </div>
    <div class="row">
        <div class="toolbar toolbar-bottom" role="toolbar">
            <button type="button" class="btn  btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返回
            </button>
        </div>
    </div>

<script th:inline="javascript">
    var prefix = ctx + "zzlx/teachAttsend"
    $("input[name='tzsJzdate']").datetimepicker({
        format : "yyyy-mm-dd",
        minView : "month",
        autoclose : true
    }).on('keypress paste', function (e) {
        e.preventDefault();
        return false;
    });
    $("#form-teachAttsend-add").validate({
        focusCleanup: true
    });

    function submitHandler() {
        $.modal.confirm("确认提交吗？", function () {
            if ($.validate.form()) {
                $.operate.saveTabAlert(prefix + "/submitWF",$('#form-teachAttsend-add').serialize());
            }
        })
    }

</script>
</body>
</html>