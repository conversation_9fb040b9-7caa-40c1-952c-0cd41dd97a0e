<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org" >
<head>
    <th:block th:include="include :: header('新增技术附件')" />
    <th:block th:include="include :: baseJs" />


</head>
<body class="white-bg">


    <div class="wrapper wrapper-content animated fadeInRight ibox-content">
        <div class="form-group" th:include="include :: step(approveKind='ZZLX_XMLX',currentNode=${activityCode})"></div><br/>

        <form class="form-horizontal m" id="form-teachAtt-add" th:object="${teachAtt}">
            <input id="bizId" name="bizId"  th:value="${businessGuid}" type="hidden">
            <input id="mainId" name="mainId"  th:value="${need.mainId}" type="hidden">
            <input id="needId" name="needId"  th:value="${need.needId}" type="hidden">
            <input id="attId" name="attId"  th:value="${teachAtt.attId}" type="hidden">
            <input id="taskId" name="taskId"  th:value="${taskId}" type="hidden">
            <input id="businessGuid" name="businessGuid"  th:value="${businessGuid}" type="hidden">
            <input id="activityCode" name="activityCode"  th:value="${activityCode}" type="hidden">
            <input id="processInstanceId" name="processInstanceId"  th:value="${processInstanceId}" type="hidden">
            <input id="processCode" name="processCode"  th:value="${processCode}" type="hidden">


            <!--框-->
            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#2" aria-expanded="false" class="collapsed">基本信息
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="2" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">

                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">项目名称：</label>
                            <div class="col-sm-10">
                                <input name="projectName"  id="projectName"  th:value="${need.projectName}"   class="form-control" required />
                            </div>
                        </div>
                    </div>

                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label is-required">技术附件名称：</label>
                            <div class="col-sm-10"  th:if="${teachAtt.attProname!=null && teachAtt.attProname!=''}">
                                <input name="attProname" th:value="${teachAtt.attProname}" class="form-control" type="text">
                            </div>
                            <div class="col-sm-10"  th:if="${teachAtt.attProname==null || teachAtt.attProname==''}">
                                <input name="attProname" th:value="${need.projectName}" class="form-control" type="text">
                            </div>
                        </div>
                    </div>
                    <!--<div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">项目所属分类：</label>
                            <div class="col-sm-4">
                                <div th:include="/component/radio :: init(id='projectKind',name='projectKind',
                            businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='payKind',value=${main.projectKind} ,see=true)"></div>
                            </div>
                        </div>
                    </div>-->
                    <div class="row">
                        <div class="form-group">
                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label is-required">项目类型：</label>
                                <div class="col-sm-4">
                                <div th:include="/component/select :: init(id='projectType',name='projectType',
                            businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='projectType',value=${need.projectType} ,isrequired=true)"></div>
                                </div>
                            </div>

                            <div class="col-sm-6">
                                <label class="col-sm-4 control-label">项目范围：</label>
                                <div class="col-sm-4">
                                <div th:include="/component/radio :: init(id='projectArea',name='projectArea',
                            businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='projectArea',value=${need.projectArea} ,see=true)"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="form-group">
                            <label class="col-sm-2 control-label">推广单位部门：</label>
                            <div class="col-sm-4">
                                <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(need.tgfDeptCode)}"></div>
                            </div>

                            <label class="col-sm-2 control-label">受让单位部门：</label>
                            <div class="col-sm-4">
                                <div  class="form-control-static" th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(need.srfDeptCode)}"></div>
                            </div>
                        </div>
                    </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">推广方项目负责人：</label>
                                <!--<div class="col-sm-4">
                                    <div  class="form-control-static" th:utext="${need.tgfFzrName}"></div>
                                </div>-->
                                <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='tgfFzrCode',userNameId='tgfFzrName',value=${need.tgfFzrCode},see=true)"></div>

                                <label class="col-sm-2 control-label is-required ">联系电话：</label>
                                <div class="col-sm-4">
                                    <input name="tgfXmfzrTel" id="tgfXmfzrTel" th:value="${need.tgfXmfzrTel}" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label">受让方项目负责人：</label>
                                <div class="col-sm-4" th:include="/component/selectUser :: init(userCodeId='srfFzrCode',userNameId='srfFzrName',value=${need.srfFzrCode},see=true)"></div>
                               <!-- <div class="col-sm-4">
                                    <div  class="form-control-static" th:utext="${need.srfFzrName}"></div>
                                </div>-->

                                <label class="col-sm-2 control-label is-required">联系电话：</label>
                                <div class="col-sm-4">
                                    <input name="srfTel" id="srfTel" th:value="${need.srfTel}" class="form-control" type="text" required>
                                </div>
                            </div>
                        </div>
                        <div class="row">
                            <div class="form-group">
                                <label class="col-sm-2 control-label is-required">项目开始日期：</label>
                                <div class="col-sm-4" th:include="/component/date :: init(name='prostartDate',id='prostartDate',strValue=${teachAtt.prostartDate},isrequired=true)"></div>
                                <label class="col-sm-2 control-label is-required">项目结束日期：</label>
                                <div class="col-sm-4" th:include="/component/date :: init(name='proendDate',id='proendDate',strValue=${teachAtt.proendDate},isrequired=true)"></div>
                            </div>
                        </div>
                        <div class="row">
                                <div class="form-group">
                                    <label class="col-sm-2 control-label is-required">知识分类：</label>
                                    <div class="col-sm-4">
                                        <div th:include="/component/select :: init(id='attKind',name='attKind',isrequired=true,
                            businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='attKind',value=${teachAtt.attKind},isfirst=true,isrequired=true)"></div>

                                    </div>
                                    <label class="col-sm-2 control-label">编制日期：</label>
                                    <div th:if="${teachAtt.projectTjDate!=null&&teachAtt.projectTjDate!=''}" class="col-sm-4" th:include="/component/date :: init(name='projectTjDate',id='projectTjDate',strValue=${teachAtt.projectTjDate})"></div>
                                    <div th:if="${teachAtt.projectTjDate==null||teachAtt.projectTjDate==''}" class="col-sm-4" th:include="/component/date :: init(name='projectTjDate',id='projectTjDate',strValue=${downDate})"></div>
                                </div>
                        </div>

                        <div class="form-group">
                            <div th:if="${main.projectSource eq 'gdxqsb'}" class="col-sm-12 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',ctx+'zzlx/need/detailM?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                            <div th:if="${main.projectSource eq 'ndjh'}" class="col-sm-5 form-control-static">点击<a class="form_list_a" th:onclick="$.modal.openTab('推广需求',[[${@environment.getProperty('app-context.ctxKY')}]] +'web/KYKTTGPD01?mainId='+[[${main.mainId}]])">此处</a>查看推广需求表</div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-3 ">点击<a class="form_list_a" th:onclick="$.modal.openTab('技术附件通知书',ctx+'zzlx/teachAttsend/query/'+[[${main.mainId}]])">此处</a>查看技术附件通知书</label>
                        </div>
                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->

            <div class="panel-group" id="accordion2"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmgx" class="collapsed">
                                <span style="color: red">*</span>项目概述
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmgx" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="teachAttUmmary"   class="form-control" rows="8" th:text="${teachAtt.teachAttUmmary}" required placeholder="文字描述：装备、工艺技术、目前存在问题、与标杆企业差距等情况描述。
数字描述：技术经济指标现状、标杆指标情况（公司内最好指标）、与指标差距"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion3"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmmb" class="collapsed">
                                <span style="color: red">*</span>项目目标
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmmb" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="teachAttTarget"   class="form-control" rows="8" th:text="${teachAtt.teachAttTarget}" required placeholder="文字描述：学习、消化、吸收能力，管理水平、技术制造能力提升等目标。
数字描述：指标改进、降本增效、提高质量、延长寿命、增加产量等。"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion4"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmfw" class="collapsed">
                                <span style="color: red">*</span>项目范围
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmfw" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="teachAttArea"   class="form-control" rows="8" th:text="${teachAtt.teachAttArea}" required placeholder="钢种、工序、措施、技术资料"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion5"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmnr" class="collapsed">
                                <span style="color: red">*</span>项目内容
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmnr" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="teachAttContent"   class="form-control" rows="8" th:text="${teachAtt.teachAttContent}" required placeholder="文字化描述：工序、资料交付名称"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion71"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#9"> <span style="color: red">*</span>技术附件
                                <span class="pull-right">
                                    <i aria-hidden="true" class="fa fa-chevron-down"></i>
                                </span>
                            </a>
                        </h4>
                    </div>
                    <!--折叠区域-->
                    <div aria-expanded="false" class="panel-collapse collapse in" id="9">
                        <div class="panel-body">
                            <div class="row form-group">
                                <label class="col-sm-2 control-label">上传附件：</label>
                                <div class="col-sm-10" th:include="/component/attachment :: init(display='none',name='jsfjId',id='jsfjId',sourceId=${teachAtt.attId},sourceModule='TEACH_FJ',isrequired=true)">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label" style="color: red">模板下载：</label>
                                <div class="col-sm-8 form-control-static"><a class="form_list_a"  href="javascript:void(0)" th:onclick="downLoad([[${file.fileId}]])" th:text="${file.fileName}"></a>&nbsp;&nbsp;&nbsp;(<span th:text="${file.fileSize}"></span>)</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
<!--            <div class="form-group" th:include="include :: layui-upload(sourceId=${teachAtt.attId},sourceModule='TEACH_FJ',id='jsfjId',name='jsfjId',labelName='技术附件：',isrequired=true)">-->
<!--                </div>-->
            <!--框-->
            <div class="panel-group" id="accordion6" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4  class="panel-title"> <a data-toggle="collapse" data-parent="#version" href="#4" aria-expanded="false" class="collapsed">
                            <span style="color: red">*</span>双方分工及协作事项
                            &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;
                            <span class="pull-right"><i class="fa fa-chevron-down" aria-hidden="true"></i></span></a>
                        </h4>
                    </div>

                    <!--折叠区域-->
                    <div id="4" class="panel-collapse collapse in" aria-expanded="false">
                        <div class="panel-body">
                            <table class="table table-bordered">
                                <tbody>
                                <tr>
                                    <td colspan="4">
                                        <label class=" control-label  is-required">
                                            甲方职责</label>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <div class=" col-sm-12 ">
                                            <textarea name="teachAttJfzz"  id="teachAttJfzz" th:text="${teachAtt.teachAttJfzz}"  maxlength="500"  class="form-control" rows="3" required></textarea>
                                        </div>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <label class=" control-label  is-required">
                                            乙方职责</label>
                                    </td>
                                </tr>
                                <tr>
                                    <td colspan="4">
                                        <div class=" col-sm-12 ">
                                            <textarea name="teachAttYfzz"  id="teachAttYfzz" th:text="${teachAtt.teachAttYfzz}" maxlength="500" class="form-control" rows="3" required></textarea>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <!--折叠区域end-->
                </div>
            </div>
            <!--框end-->

            <div class="panel-group" id="accordion7" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jhjdtb" class="collapsed">
                                双方提交资料及其交付进度
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="jhjdtb" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class=" col-sm-12 ">
                                    <textarea name="teachAttPate"  id="teachAttPate" maxlength="500" th:text="${teachAtt.teachAttPate}" class="form-control" rows="3" required></textarea>
                                </div>
                            </div>
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <button type="button" class="btn btn-white btn-sm" onclick="addTeachRateColumn()"><i class="fa fa-plus"> 增加</i></button>
                                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-key"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion8" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#zscqms" class="collapsed">
                                知识产权描述
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="zscqms" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <button type="button" class="btn btn-white btn-sm" onclick="addTeachScheduleColumn()"><i class="fa fa-plus"> 增加</i></button>
                                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-schedule"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion9"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#yqxg" class="collapsed">
                                <span style="color: red">*</span>预期效果
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="yqxg" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="teachAttResult"   class="form-control" rows="8" th:text="${teachAtt.teachAttResult}" placeholder="预期产生的二次创新内容" required></textarea>
                            </div>
                        </div>
                        <div class="form-group">
                            <label style="margin-left: 15px" class="col-sm-0 control-label"> <p class="select-title">经济效益算法：</p> </label>
                            <div class="col-sm-12">
                                <textarea name="teachAttJjxy"  th:text="${teachAtt.teachAttJjxy}" class="form-control" rows="4" placeholder="文字描述：预期产生的二次创新内容
数字描述：标杆指标、技术经济指标、与标杆指标差距、经济效益（根据公司经济效益评审办法初步测算））"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xmzcytb" class="collapsed">
                                <span style="color: red">*</span>项目组织体系
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xmzcytb" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <p class="select-title">受让方成员</p>
                                    <div th:if="${need.projectArea=='jtn'}">   <!-- 集团内手动输入 -->
                                        <button type="button" class="btn btn-white btn-sm" onclick="addTeachPeopleColumn()"><i class="fa fa-plus"> 增加</i></button>
                                        <button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>
                                    </div>
                                    <input id="shouCode"  name="shouCode" type="hidden">
                                    <input id="shouName"  name="shouName" type="hidden">
                                    <div th:if="${need.projectArea=='gfn'}">   <!-- 股份内选择 -->
                                        <button type="button" class="btn btn-white btn-sm"  onclick="choiceUser('shouCode','shouName','M',null,'addShouRangColumn')"><i class="fa fa-plus"> 添加成员</i></button>
                                    </div>
                                    <div class="col-sm-12 select-table " style="margin-bottom: 30px">
                                        <table id="bootstrap-table-srf"></table>
                                    </div>
                                    <br><br>
                                    <p class="select-title">推广方成员</p>
                                    <input id="teamCode"  name="teamCode" type="hidden">
                                    <input id="teamName"  name="teamName" type="hidden">
                                    <button type="button" class="btn btn-white btn-sm"  onclick="choiceUser('teamCode','teamName','M',null,'addProjectMemberColumn')"><i class="fa fa-plus"> 添加成员</i></button>
                                    <!--<button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>-->
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-member"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion10" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jdjh" class="collapsed">
                                进度计划
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="jdjh" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <button type="button" class="btn btn-white btn-sm" onclick="addTeachPlanColumn()"><i class="fa fa-plus"> 增加</i></button>
                                    <button type="button" class="btn btn-white btn-sm" onclick="sub.delColumn()"><i class="fa fa-minus"> 删除</i></button>
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-plan"></table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="panel-group" id="accordion02" role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#jfystb" class="collapsed">
                                <span style="color: red">*</span>费用估算（单位：万元）
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="jfystb" class="panel-collapse collapse in">
                        <div class="panel-body">
                            <div class="form-group">
                                <div class="col-sm-12">
                                    <p class="select-title">实施费用估算</p>
                                    <button type="button" class="btn btn-white btn-sm" onclick="addTeachCostolumn()"><i class="fa fa-plus"> 增加</i></button>
                                    <button type="button" class="btn btn-white btn-sm" onclick="delCostColumn()"><i class="fa fa-minus"> 删除</i></button>
                                    <div class="col-sm-12 select-table ">
                                        <table id="bootstrap-table-ys"></table>
                                        <table class="table table-bordered table-hover">
                                            <tfoot style="margin-right: 0px;">
                                            <tr>
                                                <th style="text-align: center;  width:127px;">
                                                    <div class="th-inner"></div>
                                                    <div class="fht-cell" style="width: auto;"></div>
                                                </th>
                                                <th style="text-align: center; width: 200px;">
                                                    <div class="th-inner">合计（万元）：</div>
                                                    <div class="fht-cell"style="width: 150px;"></div>
                                                </th>
                                               <th style="text-align: left; ">
                                                    <div class="th-inner">
                                                       <input name="costTotal" type="hidden" th:value="${#numbers.formatDecimal(teachAtt.extra2,1,2)}" />
                                                        <div class="form-control-static" id="total" th:utext="${#numbers.formatDecimal(teachAtt.extra2,1,2)}"></div>
                                                    </div>
                                                    <div class="fht-cell" style="width: auto;"></div>
                                                </th>
                                            </tr>
                                            </tfoot>
                                        </table>
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label" style="margin-top: 10px;">现场支撑人日数：</label>
                                            <div class="col-sm-10" style="margin-bottom: 10px;margin-top: 10px;">
                                                <input name="payDay" id="payDay" class="form-control" min="0" th:value="${teachAtt.payDay}" type="text" >
                                            </div>
                                        </div>
                                         <div class="form-group">
                                         <label class="col-sm-2 control-label">知识产权估算：</label>
                                         <div class="col-sm-10" style="margin-bottom: 10px;margin-top: 10px;">
                                             <input name="estimate" id="estimate" type="text" th:value="${#numbers.formatDecimal(teachAtt.estimate,1,2)}"  class="form-control" /> <span style="color: red">注：知识产权免费</span>
                                         </div>
                                         </div>
                                         <span  class="xy4" th:style="'display: ' + @{(${teachAtt.estimate!=null && teachAtt.estimate>0} ? 'block' : 'none')} + ';'" >
                                              <div class="form-group">
                                                  <label class="col-sm-2 control-label is-required">效益大类 ：</label>
                                                  <div class="col-sm-3">
                                                      <th:block
                                                              th:include="/component/select :: init(id='xydl',name='xydl',dictCode='CP_DL',value=${teachAtt.xydl},businessType='KXCP',isfirst=true)"></th:block>
                                                  </div>
                                                  <label class="col-sm-3 control-label is-required">效益小类 ：</label>
                                                  <div class="col-sm-3">
                                                      <th:block
                                                              th:include="/component/select :: init(id='xyxl',name='xyxl',dictCode=${teachAtt.xydl},value=${teachAtt.xyxl},businessType='KXCP')"></th:block>
                                                  </div>
                                              </div>
                                              <div class="form-group">
                                                  <label class="col-sm-2 control-label is-required">效益计算公式：</label>
                                                  <div class="col-sm-8">
                                                      <input name="xyjsgs" id="xyjsgs" th:value="${teachAtt.xyjsgs}" class="form-control" type="hidden">
                                                      <div class="form-control-static" id="xyjsgsName" th:utext="${teachAtt.xyjsgs}"></div>
                                                  </div>
                                              </div>
                                          </span>
                                          <div class="form-group">
                                          <label class="col-sm-2 control-label">经济效益费用估算：</label>
                                          <div class="col-sm-10" style="margin-bottom: 10px;">
                                              <input name="economyAndEstimate" th:value="${#numbers.formatDecimal(teachAtt.economyAndEstimate,1,2)}" onkeyup="this.value=this.value.replace(/[^\.\d]/g,'')"  type="number"  min="0" class="form-control" />
                                          </div>
                                          </div>
                                          <div class="form-group">
                                          <label class="col-sm-2 control-label">费用补充说明：</label>
                                          <div class="col-sm-10" style="margin-bottom: 10px">
                                              <textarea name="teachAttFysm"  th:utext="${teachAtt.teachAttFysm}" class="form-control" rows="4"></textarea>
                                          </div>
                                          </div>
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label"><span style="color: red">*</span>费用估算表附件：</label>
                                            <div class="col-sm-10" style="margin-bottom: 10px">
                                                <div th:include="/component/attachment :: init(display='none',name='fygsId',id='fygsId',sourceId=${teachAtt.attId},sourceModule='FYGS_FJ',isrequired=true)">
                                                </div>

<!--                                            <div class="form-group" th:include="include :: layui-upload(sourceId=${teachAtt.attId},sourceModule='FYGS_FJ',id='fygsId',name='fygsId',labelName='费用估算表附件：',isrequired=true)"></div>-->
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label class="col-sm-2 control-label" style="color: red">模板下载：</label>
                                            <div class="col-sm-8 form-control-static"><a class="form_list_a"  href="javascript:void(0)" th:onclick="downLoad([[${fileFy.fileId}]])" th:text="${fileFy.fileName}"></a>&nbsp;&nbsp;&nbsp;(<span th:text="${fileFy.fileSize}"></span>)</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion11"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#ysbz" class="collapsed">
                                <span style="color: red">*</span>验收标准及其方式
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="ysbz" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="teachAttYsmethod"   class="form-control" rows="8" th:text="${teachAtt.teachAttYsmethod}" required placeholder="文字描述：技术消化、吸收、掌握情况，管理水平、技术制造能力提升情况。
数字描述：指标改进、降本增效、增加产量、本土化替代、延长寿命、提高产品质量"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion12"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a data-toggle="collapse" href="#xtsy" class="collapsed">
                                <span style="color: red">*</span>双方协调其它事宜
                                <span class="pull-right">
		                   		<i class="fa fa-chevron-down"></i>
		                   	</span>
                            </a>
                        </h4>
                    </div>
                    <div id="xtsy" class="panel-collapse collapse in">
                        <div class="form-group">
                            <div class="col-sm-12 control-label">
                                <textarea name="teachAttMatters"   class="form-control" rows="8" th:text="${teachAtt.teachAttMatters}" required placeholder="1、需要提供的相互帮助事项 2、安全注意事项及责任条款 3、知识产权保护、保密条款等"></textarea>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="panel-group" id="accordion72"  role="tablist" aria-multiselectable="true">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <h4 class="panel-title">
                            <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#ps"><span style="color: red">*</span>评审材料附件
                                <span class="pull-right">
                            <i aria-hidden="true" class="fa fa-chevron-down"></i>
                        </span>
                            </a>
                        </h4>
                    </div>
                    <!--折叠区域-->
                    <div aria-expanded="false" class="panel-collapse collapse in" id="ps">
                        <div class="panel-body">
                            <div class="row form-group">
                                <label class="col-sm-2 control-label">上传附件：</label>
                                <div class="col-sm-10 " th:include="/component/attachment :: init(display='none',name='psclId',id='psclId',sourceId=${teachAtt.attId},sourceModule='PSCL_FJ',isrequired=true)">
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="col-sm-2 control-label" style="color: red">模板下载：</label>
                                <div class="col-sm-8 form-control-static"><a class="form_list_a"  href="javascript:void(0)" th:onclick="downLoad([[${fileCl.fileId}]])" th:text="${fileCl.fileName}"></a>&nbsp;&nbsp;&nbsp;(<span th:text="${fileCl.fileSize}"></span>)</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <div class="m">
            <th:block th:include="component/wfCommentList3 :: init(businessId=${businessGuid})" />
        </div>

    </div>
    <div class="row">
        <div class="toolbar toolbar-bottom" role="toolbar" >

            <button type="button" class="btn btn-primary"
                    onclick="saveHandler()">
                <i class="fa fa-check"></i>暂 存
            </button>

            <th:block th:include="component/wfSubmitOne:: init(taskId=${taskId},callback=submitHandler)"/>
            <button type="button" class="btn btn-danger"
                    onclick="closeItem()">
                <i class="fa fa-reply-all"></i>返 回
            </button>
        </div>
    </div>

    <div class="form-group" th:if="${not #strings.isEmpty(processInstanceId)}"
         th:include="component/wfCommentList::init(processInstanceId=${processInstanceId})"></div>
    <script type="text/html" id="scheduleThymeleaf">
        <select name="teachSchedule[%s].deType" th:class="form-control" id="teachSchedule[%s].deType"
                th:with="dictData=${@dict.getDictList('KTTG','deType')}">
            <option  value=" ">请选择</option>
            <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
        </select>
    </script>
    <script type="text/html" id="srfPeopleThymeleaf">
        <select name="srfPeople[%s].role" th:class="form-control" id="srfPeople[%s].role"
                th:with="dictData=${@dict.getDictList('KTTG','projectRole')}">
            <option  value=" ">请选择</option>
            <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
        </select>
    </script>
    <script type="text/html" id="tgfPeopleThymeleaf">
        <select name="tgfPeople[%s].role" th:class="form-control" id="tgfPeople[%s].role"
                th:with="dictData=${@dict.getDictList('KTTG','projectRole')}">
            <option  value=" ">请选择</option>
            <option th:each="dict : ${dictData}" th:text="${dict.dictName}" th:value="${dict.dictValue}"></option>
        </select>
    </script>

    <script th:inline="javascript">
        var prefix = ctx + "zzlx/teachAtt";

        $("#bootstrap-table-key").on("post-body.bs.table", function (e, args) {
            $("input[name$='beginTime']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });

        $("#bootstrap-table-key").on("post-body.bs.table", function (e, args) {
            $("input[name$='endTime']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });

        $("#bootstrap-table-plan").on("post-body.bs.table", function (e, args) {
            $("input[name$='beginTime']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });

        $("#bootstrap-table-plan").on("post-body.bs.table", function (e, args) {
            $("input[name$='endTime']").datetimepicker({
                format: "yyyy-mm-dd",
                minView: "month",
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });
        $("#bootstrap-table-ys").on("post-body.bs.table", function (e, args) {
            $("input[name$='busiYear']").datetimepicker({
                format: "yyyy",
                weekStart: 1,
                minView: 4,
                startView: 4,
                autoclose: true,
                pickerPosition:'top-right'
            }).on('keypress paste', function (e) {
                e.preventDefault();
                return false;
            });
        });

        $("#form-teachAtt-add").validate({
            focusCleanup: true
        });

        function downLoad(key) {
            window.open(ctx + "attachment/download/" + key);
        }

        //暂存
        function saveHandler() {
            var config = {
                url: prefix + "/addSave",
                type: "post",
                dataType: "json",
                data: $('#form-teachAtt-add').serialize(),
                beforeSend: function () {
                    $.modal.loading("正在处理中，请稍后...");
                },
                success: function (result) {
                    $("#attId").val(result.data.attId);
                    $.modal.alertSuccess(result.msg);
                    $.modal.closeLoading();
                }
            };
            $.ajax(config)
        }

        function submitHandler(transitionKey){
            var projectName = $("#projectName").val();
            if(projectName==""){
                $.modal.alertError("项目名称不能为空！请输入");
                return;
            }
            var attProname = $("input[name='attProname']").val();
            if(attProname==""){
                $.modal.alertError("技术附件名称不能为空！请输入");
                return;
            }
            var len =$("#bootstrap-table-srf").find("tr").length;
            for(var i=0;i<len-1;i++){
                var job =  $("input[name='srfPeople["+i+"].job']").val();
                var role =  $("select[name='srfPeople["+i+"].role']").val();
                if(job==''){
                    $.modal.alertError("请输入受让方成员主要任务！");
                    return;
                }
                if(role==''){
                    $.modal.alertError("请选择受让方成员项目角色！");
                    return;
                }
            }
            var len =$("#bootstrap-table-member").find("tr").length;
            for(var i=0;i<len-1;i++){
                var job =  $("input[name='tgfPeople["+i+"].job']").val();
                var role =  $("select[name='tgfPeople["+i+"].role']").val();
                if(job==''){
                    $.modal.alertError("请输入推广方成员主要任务！");
                    return;
                }
                if(role==''){
                    $.modal.alertError("请选择推广方成员项目角色！");
                    return;
                }
            }
            var len = $("#bootstrap-table-ys tbody").find("tr").length;
            console.log(len)
            if (len == 1) {
                var trStr = $("#bootstrap-table-ys tbody").find("tr:first").find("td:first");
                if(trStr.text()=="没有找到匹配的记录"){
                    $.modal.alert("费用估算信息未填写！");
                    return;
                }
            }
            $.modal.confirm("确认提交技术附件吗？", function () {
                if ($.validate.form()) {
                    $.operate.saveTabAlert(prefix + "/submitWF",$('#form-teachAtt-add').serialize()+"&transitionKey=" + transitionKey);
                }
            })
        }
        //流程跟踪
        function workFlowProcess() {
            window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + [[${processInstanceId}]]);
        }

        $("#xydl").change(function () {
            var value = $(this).val();
            $.post(prefix+"/getCPXL", {"cpdl": value},
                function (data) {
                    $("#xyxl").html('<option value=""> 请选择</option>');
                    $("#xyxl").select2({
                        language: "zh-CN",
                        data: data
                    });

                }
            );
        })


        $("#xyxl").change(function () {
            var value = $(this).val();
            var xydl = $("#xydl").val();
            if (value != null && value != '' && xydl != null && xydl != '') {
                $.post(prefix+"/getGS", {"xyxl": value, "xydl": xydl}, function (data) {
                        if (data != null && data.length > 0) {
                            $("#xyjsgs").val(data[0].formulaCalculate);
                            $("#xyjsgsName").text(data[0].formulaCalculate);

                        } else {
                            $("#xyjsgs").val();
                            $("#xyjsgsName").text("当前类型计算公式为空，请联系管理员配置该类型计算公式！！！");
                        }

                    }
                );
            }
        })

        /** 根据专利编号获取专利名称 */
        function selectNumber (index,obj) {
            var value = $(obj).val();
            var sourceType = "";
            var sourceId = value;
           var options = $("#deType"+index+" option:selected");
            var ovalue = options.val();
            if(ovalue=='zl'||ovalue=='famzl'){
                sourceType = "KYZL";
            }else if(ovalue=='jsmm'){
                sourceType = 'KYMM';
            }
            $.post(prefix+"/getSource", {"sourceType": sourceType,"sourceId":sourceId},
                function (data) {
                    if(data!=null&&data.length!=0){
                        console.log(data);
                        console.log(data.sourceName);
                        $("input[name='teachSchedule["+index+"].deName']").val(data.sourceName);
                    }else{
                        $("input[name='teachSchedule["+index+"].deName']").val("");
                    }
                }
            );
        }

        $("#estimate").change(function(){
            var value = $(this).val();
            var kind = [[${main.projectKind}]];
            if(kind!=null && kind=='A' && parseFloat(value)>0){
                $(".xy4").show();
                $("#xydl").attr("required","true");
                $("#xyxl").attr("required","true");
            }else{
                $(".xy4").hide();
                $("#xydl").removeAttr("required","");
                $("#xyxl").removeAttr("required","");
            }
        })

        var roleDatas = [[${roleDatas}]];
        var deTypes = [[${deTypes}]];

        /***********************************支付进度表*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzlx/teachRate/teachRateList?attId="+[[${teachAtt.attId}]],
                id:"bootstrap-table-key",
                toolbar:"toolbar-key",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: true
                },
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='teachRate[%s].index' value='%s'>", index, $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachRate[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'beginTime',
                        align: 'center',
                        title: '开始时间',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required  type='text' name='teachRate[%s].beginTime'  value='%s' placeholder='yyyy-MM-dd'>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'endTime',
                        align: 'center',
                        title: '结束时间',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  required  type='text'  name='teachRate[%s].endTime'  value='%s' placeholder='yyyy-MM-dd'>", index, value);
                            return html;
                        }
                    },{
                        field: 'stageName',
                        align: 'center',
                        title: '培训地点',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required  type='text'  name='teachRate[%s].stageName' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'stageTarget',
                        align: 'center',
                        title: '培训岗位',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required   type='text'  name='teachRate[%s].stageTarget' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'peoply',
                        align: 'center',
                        title: '培训人数',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required  min='0'  type='number'  name='teachRate[%s].peoply' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'remark',
                        align: 'center',
                        title: '备注',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' type='text'  name='teachRate[%s].remark' value='%s'>", index, value);
                            return html;
                        }
                    }
                ]
            };
            $(".no-records-found").remove();
            $.table.init(options);
        });
        function addTeachRateColumn() {
            var row = {
                beginTime: "",
                endTime: "",
                stageName: "",
                stageTarget: "",
                peoply: "",
                remark: "",
            }
            sub.addColumn(row,"bootstrap-table-key");
        }

        /***********************************知识产权描述*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzlx/teachSchedule/scheduleList?attId="+[[${teachAtt.attId}]],
                id:"bootstrap-table-schedule",
                toolbar:"toolbar-schedule",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: true
                },
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='teachSchedule[%s].index' value='%s'>", index, $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachSchedule[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'deType',
                        align: 'center',
                        title: '类别',
                        formatter: function(value, row, index) {
                           var deType = 'teachSchedule[' + index + '].deType';
                            var id = 'deType' + index;
                            return dictToSelect(deTypes, value, deType, id);
                        }
                    },
                    {
                        field: 'number',
                        align: 'center',
                        title: '编号',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input id='number%s' onblur=\"selectNumber('%s',this)\" class='form-control'  required  type='text'  name='teachSchedule[%s].number'  value='%s'>",index, index, index, value);
                            return html;
                        }
                    },{
                        field: 'deName',
                        align: 'center',
                        title: '名称',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required   type='text'  name='teachSchedule[%s].deName' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'ratio',
                        align: 'center',
                        title: '对本项目的贡献度%',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required  onkeyup=\"this.value=this.value.replace(/[^\\.\\d]/g,'')\"  type='text'  name='teachSchedule[%s].ratio' value='%s'>", index, value);
                            return html;
                        }
                    }
                ]
            };
            $(".no-records-found").remove();
            $.table.init(options);
        });
        function addTeachScheduleColumn() {
            var row = {
                deType: "",
                number: "",
                deName: "",
                ratio: "",
            }
            sub.addColumn(row,"bootstrap-table-schedule");
        }
        // 数据字典转下拉框
        function dictToSelect(datas, value, name, id) {
            var actions = [];
            actions.push($.common.sprintf("<select id='%s' class='form-control' name='%s'><option value=''>请选择</option>", id, name));
            $.each(datas, function (index, dict) {
                actions.push($.common.sprintf("<option value='%s'", dict.dictValue));
                if (dict.dictValue == ('' + value)) {
                    actions.push(' selected');
                }
                actions.push($.common.sprintf(">%s</option>", dict.dictName));
            });
            actions.push('</select>');
            return actions.join('');
        }
        /***********************************推广方成员*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzgg/teachPropeople/teachPeopleList?bizId="+[[${teachAtt.attId}]]+"&peType=tgf"+"&extra1=A",
                id: "bootstrap-table-member",
                toolbar:"toolbar-xmfz",
                modalName: "推广方成员",
                uniqueId: "userId",
                pagination: false,
                pageSize:50,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                onLoadSuccess:function(data){
                    var teamCode = $("#teamCode").val();
                    if(data.length > 0){
                        for (var i = 0; i < data.length; i++) {
                            if(!$.common.isEmpty(teamCode)){
                                teamCode+=",";
                            }
                            teamCode +=data[i].userId;
                        }
                    }
                    $("#teamCode").val(teamCode);
                },
                sidePagination: "client",
                columns: [
                    /*{
                        checkbox: true
                    },*/
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        witdh:'5%',
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='tgfPeople[%s].index' value='%s'>", index, $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='tgfPeople[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'userId',
                        align: 'center',
                        title: '工号',
                        witdh:'5%',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' readonly type='text' name='tgfPeople[%s].userId' value='%s' >", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'userName',
                        align: 'center',
                        title: '姓名',
                        witdh:'5%',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' readonly type='text' name='tgfPeople[%s].userName' value='%s' >", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'position',
                        align: 'center',
                        title: '岗位',
                        witdh:'5%',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  type='text' name='tgfPeople[%s].position' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'point',
                        align: 'center',
                        title: '特殊技长',
                        witdh:'5%',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  type='text' name='tgfPeople[%s].point' value='%s'>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'job',
                        align: 'center',
                        title: '主要任务',
                        witdh:'5%',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required  type='text' name='tgfPeople[%s].job' value='%s'>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'role',
                        align: 'center',
                        title: '项目角色',
                        witdh:'15%',
                        formatter: function(value, row, index) {
                            var role = 'tgfPeople[' + index + '].role';
                            var id = 'tgfrole' + index;
                            return dictToSelect(roleDatas, value, role, id);
                        }
                    }
                ]
            };
            $(".no-records-found").remove();
            $.table.init(options);
        });
        function addProjectMemberColumn(userCode, userName) {
            $("#teamCode").val(userCode);
            $("#teamName").val(userName);
            var teamCodes = $("#teamCode").val();
            var teamNames = $("#teamName").val();
            var teamCode = teamCodes.split(",");
            var teamName = teamNames.split(",");
            $("#bootstrap-table-member tbody").html("");
            var rows = "";
            var config = {          //查询出已有的成员
                url: ctx + "zzgg/teachPropeople/loadList?bizId=" + [[${teachAtt.attId}]] + "&peType=tgf" + "&extra1=A",
                type: "post",
                dataType: "json",
                success: function (result) {
                    var data = result.data;
                    var blocks = data.blocks;
                    var attr = blocks.result.attr;
                    var row = attr.pageData.rows;
                    rows = row;
                    var newRow = new Array();
                    for (var i = 0; i < teamName.length; i++) {
                        if (teamCode[i]) {
                            var orderNum = $("#bootstrap-table-member").find("tr").length;
                            var len = $("#bootstrap-table-member").find("tr").length - 1;
                            if(rows.length>0) {
                                for(var j=0;j<rows.length;j++) {
                                    if (rows[j].userId == teamCode[i]) {
                                        if(rows[j].position==null){
                                            rows[j].position = "";
                                        }
                                        if(rows[j].point==null){
                                            rows[j].point = "";
                                        }
                                        if(rows[j].job==null){
                                            rows[j].job = "";
                                        }
                                        var tr = "<tr data-index='" + len + "'>";
                                        tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='tgfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userId' value=" + rows[j].userId + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userName' value=" + rows[j].userName + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].position' value=" + rows[j].position + " ></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].point' value=" + rows[j].point + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' required  type='text' name='tgfPeople[" + len + "].job' value=" + rows[j].job + "></td>";
                                        tr = tr + "<td style='text-align: center;'>" + $.common.sprintf(dictToSelect(roleDatas, rows[j].role, "tgfPeople[" + len + "].role", "tgfrole" + len)) + "</td>";
                                        tr = tr + "</tr>";
                                        $(".no-records-found").remove();
                                        $("#bootstrap-table-member tbody").append(tr);
                                        newRow.push(rows[j].userId);
                                    }
                                }
                                if(newRow.length>0){
                                    if(teamCode[i]!=newRow[newRow.length-1]){
                                        var tr = "<tr data-index='" + len + "'>";
                                        tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='tgfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userId' value=" + teamCode[i] + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userName' value=" + teamName[i] + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].position' ></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].point' ></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' required type='text' name='tgfPeople[" + len + "].job'></td>";
                                        tr = tr + "<td style='text-align: center;'>" + $.common.sprintf($("#tgfPeopleThymeleaf").html(), len, len) + "</td>";
                                        tr = tr + "</tr>";
                                        $(".no-records-found").remove();
                                        $("#bootstrap-table-member tbody").append(tr);
                                    }
                                }else{
                                    var tr = "<tr data-index='" + len + "'>";
                                    tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='tgfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userId' value=" + teamCode[i] + "></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userName' value=" + teamName[i] + "></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].position' ></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].point' ></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' required type='text' name='tgfPeople[" + len + "].job'></td>";
                                    tr = tr + "<td style='text-align: center;'>" + $.common.sprintf($("#tgfPeopleThymeleaf").html(), len, len) + "</td>";
                                    tr = tr + "</tr>";
                                    $(".no-records-found").remove();
                                    $("#bootstrap-table-member tbody").append(tr);
                                }

                            }else{
                                var tr = "<tr data-index='" + len + "'>";
                                tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='tgfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userId' value=" + teamCode[i] + "></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='tgfPeople[" + len + "].userName' value=" + teamName[i] + "></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].position' ></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='tgfPeople[" + len + "].point' ></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' required type='text' name='tgfPeople[" + len + "].job'></td>";
                                tr = tr + "<td style='text-align: center;'>" + $.common.sprintf($("#tgfPeopleThymeleaf").html(), len, len) + "</td>";
                                tr = tr + "</tr>";
                                $(".no-records-found").remove();
                                $("#bootstrap-table-member tbody").append(tr);
                            }
                        }
                    }
                }
            };
            $.ajax(config)
        }

        /***********************************受让方成员*******************************************/
        $(function() {
            var projectArea = [[${need.projectArea}]];
            if(projectArea=='gfn') {
                var options = {
                    url: ctx + "zzgg/teachPropeople/teachPeopleList?bizId=" + [[${teachAtt.attId}]] + "&peType=srf"+"&extra1=A",
                    id: "bootstrap-table-srf",
                    toolbar: "toolbar-xmfz",
                    modalName: "受让方成员",
                    uniqueId: "userId",
                    pagination: false,
                    pageSize:50,
                    showSearch: false,
                    showRefresh: false,
                    showToggle: false,
                    showColumns: false,
                    sidePagination: "client",
                    onLoadSuccess:function(data){
                        var shouCode = $("#shouCode").val();
                        if(data.length > 0){
                            for (var i = 0; i < data.length; i++) {
                                if(!$.common.isEmpty(shouCode)){
                                    shouCode+=",";
                                }
                                shouCode +=data[i].userId;
                            }
                        }
                        $("#shouCode").val(shouCode);
                    },
                    sidePagination: "client",
                    columns: [
                        /*{
                        checkbox: true
                    },*/
                        {
                            field: 'index',
                            align: 'center',
                            title: "序号",
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var columnIndex = $.common.sprintf("<input type='hidden' name='srfPeople[%s].index' value='%s'>", index, $.table.serialNumber(index));
                                var columnId = $.common.sprintf("<input type='hidden' name='srfPeople[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                                return columnIndex + $.table.serialNumber(index) + columnId;
                            }
                        },
                        {
                            field: 'userId',
                            align: 'center',
                            title: '工号',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control' readonly  type='text' name='srfPeople[%s].userId' value='%s' >", index, value);
                                return html;
                            }
                        },
                        {
                            field: 'userName',
                            align: 'center',
                            title: '姓名',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control' readonly type='text' name='srfPeople[%s].userName' value='%s' >", index, value);
                                return html;
                            }
                        },
                        {
                            field: 'position',
                            align: 'center',
                            title: '岗位',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control'  type='text' name='srfPeople[%s].position' value='%s'>", index, value);
                                return html;
                            }
                        }, {
                            field: 'point',
                            align: 'center',
                            title: '特殊技长',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control' type='text' name='srfPeople[%s].point' value='%s'>", index, value);
                                return html;
                            }
                        },
                        {
                            field: 'job',
                            align: 'center',
                            title: '主要任务',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control' required type='text' name='srfPeople[%s].job' value='%s'>", index, value);
                                return html;
                            }
                        },
                        {
                            field: 'role',
                            align: 'center',
                            title: '项目角色',
                            witdh: '15%',
                            formatter: function (value, row, index) {
                                var role = 'srfPeople[' + index + '].role';
                                var id = 'srfrole' + index;
                                return dictToSelect(roleDatas, value, role, id);
                            }
                        }
                    ]
                };
            }else{
                var options = {
                    url: ctx + "zzgg/teachPropeople/teachPeopleList?bizId=" + [[${teachAtt.attId}]] + "&peType=srf"+"&extra1=A",
                    id: "bootstrap-table-srf",
                    toolbar: "toolbar-xmfz",
                    modalName: "受让方成员",
                    uniqueId: "userId",
                    pagination: false,
                    showSearch: false,
                    showRefresh: false,
                    showToggle: false,
                    showColumns: false,
                    sidePagination: "client",
                    columns: [
                        {
                         checkbox: true
                         },
                        {
                            field: 'index',
                            align: 'center',
                            title: "序号",
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var columnIndex = $.common.sprintf("<input type='hidden' name='srfPeople[%s].index' value='%s'>", index, $.table.serialNumber(index));
                                var columnId = $.common.sprintf("<input type='hidden' name='srfPeople[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                                return columnIndex + $.table.serialNumber(index) + columnId;
                            }
                        },
                        {
                            field: 'userId',
                            align: 'center',
                            title: '工号',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                /*var html = $.common.sprintf("<input class='form-control'  type='text' name='srfPeople[%s].userId' value='%s' >", index, value);
                                return html;*/
                                var html = "";
                                if (value == null || value == "") {
                                    html = $.common.sprintf("<input required onblur=\"addApplyMember('%s',this)\" class='form-control'  type='text' name='srfPeople[%s].userId' value='%s' >", index, index, value);
                                } else {
                                    html = $.common.sprintf("<input readonly class='form-control'  type='text' name='srfPeople[%s].userId' value='%s' >", index, value);
                                }
                                return html;
                            }
                        },
                        {
                            field: 'userName',
                            align: 'center',
                            title: '姓名',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control' readonly type='text' id='srfUserName%s' name='srfPeople[%s].userName' value='%s' >", index,index, value);
                                return html;
                            }
                        },
                        {
                            field: 'position',
                            align: 'center',
                            title: '岗位',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control' readonly type='text' id='srfPosition%s' name='srfPeople[%s].position' value='%s'>",index, index, value);
                                return html;
                            }
                        }, {
                            field: 'point',
                            align: 'center',
                            title: '特殊技长',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control' type='text' id='srfPoint%s' name='srfPeople[%s].point' value='%s'>",index, index, value);
                                return html;
                            }
                        },
                        {
                            field: 'job',
                            align: 'center',
                            title: '主要任务',
                            witdh: '5%',
                            formatter: function (value, row, index) {
                                var html = $.common.sprintf("<input class='form-control' required type='text' id='srfJob%s' name='srfPeople[%s].job' value='%s'>",index, index, value);
                                return html;
                            }
                        },
                        {
                            field: 'role',
                            align: 'center',
                            title: '项目角色',
                            witdh: '15%',
                            formatter: function (value, row, index) {
                                var role = 'srfPeople[' + index + '].role';
                                var id = 'srfRole' + index;
                                return dictToSelect(roleDatas, value, role, id);
                            }
                        }
                    ]
                };
            }
            $(".no-records-found").remove();
            $.table.init(options);
        });

        //新增项目成员带出项目成员信息
        function addApplyMember(index, obj) {
            var value = $(obj).val();
            if (value != "" || value != null) {
                $.ajax({
                    url: prefix + "/getADUser",
                    data: {userCode: value},
                    datatype: "json",
                    success: function (result) {
                        console.log(result);
                        $("#srfUserName" + index).val(result.data.userName);
                        $("#srfPosition" + index).val(result.data.userJob);
                        }
                });

            }
        }

        function addShouRangColumn() {
            var shouCodes = $("#shouCode").val();
            var shouNames = $("#shouName").val();
            var shouCode = shouCodes.split(",");
            var shouName = shouNames.split(",");
            $("#bootstrap-table-srf tbody").html("");
            var rows = [];
            var config = {          //
                url: ctx +"zzgg/teachPropeople/loadList?bizId=" + [[${teachAtt.attId}]] + "&peType=srf"+"&extra1=A",
                type: "post",
                dataType: "json",
                success: function (result) {
                    var data = result.data;
                    var blocks = data.blocks;
                    var attr = blocks.result.attr;
                    var row = attr.pageData.rows;
                    rows = row;
                    var newRow = new Array();
                    for(var i=0;i<shouCode.length;i++){
                        if(shouCode[i]){
                            var orderNum = $("#bootstrap-table-srf").find("tr").length;
                            var len = $("#bootstrap-table-srf").find("tr").length - 1;
                            if(rows.length>0) {
                                for(var j=0;j<rows.length;j++) {
                                    if (rows[j].userId == shouCode[i]) {
                                        if(rows[j].position==null){
                                            rows[j].position = "";
                                        }
                                        if(rows[j].point==null){
                                            rows[j].point = "";
                                        }
                                        if(rows[j].job==null){
                                            rows[j].job = "";
                                        }
                                        var tr = "<tr data-index='" + len + "'>";
                                        tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='srfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='srfPeople[" + len + "].userId' value=" + rows[j].userId + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='srfPeople[" + len + "].userName' value=" + rows[j].userName + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].position'  value=" + rows[j].position + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].point' value=" + rows[j].point + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' required type='text' name='srfPeople[" + len + "].job' value=" + rows[j].job + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'>" + $.common.sprintf(dictToSelect(roleDatas, rows[j].role, "srfPeople[" + len + "].role", "role" + len)) + "</td>";
                                        tr = tr + "</tr>";
                                        $(".no-records-found").remove();
                                        $("#bootstrap-table-srf tbody").append(tr);
                                        newRow.push(rows[j].userId);
                                    }
                                }
                                if(newRow.length>0){
                                    if(shouCode[i]!=newRow[newRow.length-1]){
                                        var tr = "<tr data-index='" + len + "'>";
                                        tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='srfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly  type='text' name='srfPeople[" + len + "].userId' value=" + shouCode[i] + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='srfPeople[" + len + "].userName' value=" + shouName[i] + "></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].position' ></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].point' ></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' required type='text' name='srfPeople[" + len + "].job'></td>";
                                        tr = tr + "<td style='text-align: center;width: 15%;'>" + $.common.sprintf($("#srfPeopleThymeleaf").html(), len, len) + "</td>";
                                        tr = tr + "</tr>";
                                        $(".no-records-found").remove();
                                        $("#bootstrap-table-srf tbody").append(tr);
                                    }
                                }else{
                                    var tr = "<tr data-index='" + len + "'>";
                                    tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='srfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly  type='text' name='srfPeople[" + len + "].userId' value=" + shouCode[i] + "></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='srfPeople[" + len + "].userName' value=" + shouName[i] + "></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].position' ></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].point' ></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' required type='text' name='srfPeople[" + len + "].job'></td>";
                                    tr = tr + "<td style='text-align: center;width: 15%;'>" + $.common.sprintf($("#srfPeopleThymeleaf").html(), len, len) + "</td>";
                                    tr = tr + "</tr>";
                                    $(".no-records-found").remove();
                                    $("#bootstrap-table-srf tbody").append(tr);
                                }
                            }else{
                                var tr = "<tr data-index='" + len + "'>";
                                tr = tr + "<td style='text-align: center;width: 5%;'><input type='hidden' name='srfPeople[" + len + "].orderNum' value='" + orderNum + "'>" + orderNum + "</td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly  type='text' name='srfPeople[" + len + "].userId' value=" + shouCode[i] + "></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' readonly type='text' name='srfPeople[" + len + "].userName' value=" + shouName[i] + "></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].position' ></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control'  type='text' name='srfPeople[" + len + "].point' ></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'><input class='form-control' required type='text' name='srfPeople[" + len + "].job'></td>";
                                tr = tr + "<td style='text-align: center;width: 15%;'>" + $.common.sprintf($("#srfPeopleThymeleaf").html(), len, len) + "</td>";
                                tr = tr + "</tr>";
                                $(".no-records-found").remove();
                                $("#bootstrap-table-srf tbody").append(tr);
                            }
                        }
                    }
                }
            };
            $.ajax(config)
        }

        function addTeachPeopleColumn() {
            var row = {
                userId: "",
                userName: "",
                position: "",
                point: "",
                job: "",
                role: "",
            }
            sub.addColumn(row,"bootstrap-table-srf");
        }

        /***********************************进度计划表*******************************************/
        $(function() {
            var options = {
                url: ctx + "zzgg/teachPlan/teachPlanList?bizId="+[[${teachAtt.attId}]],
                id:"bootstrap-table-plan",
                modalName: "进度计划",
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: true
                },
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='teachPlans[%s].index' value='%s'>", index, $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachPlans[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'beginTime',
                        align: 'center',
                        title: '阶段开始时间',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required  type='text' name='teachPlans[%s].beginTime' placeholder='yyyy-MM-dd'  value='%s' placeholder='yyyy-MM-dd'>", index, value);
                            return html;
                        }
                    },
                    {
                        field: 'endTime',
                        align: 'center',
                        title: '阶段结束时间',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  required  type='text'  name='teachPlans[%s].endTime' placeholder='yyyy-MM-dd'  value='%s' placeholder='yyyy-MM-dd'>", index, value);
                            return html;
                        }
                    },{
                        field: 'stageName',
                        align: 'center',
                        title: '阶段名称',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required   type='text'  name='teachPlans[%s].stageName' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'stageTarget',
                        align: 'center',
                        title: '阶段目标',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required   type='text'  name='teachPlans[%s].stageTarget' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'peoply',
                        align: 'center',
                        title: '人数',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required min='0'  type='number' onblur='fenHeji()' name='teachPlans[%s].peoply' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'days',
                        align: 'center',
                        title: '天数',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required  min='0' type='number' onblur='fenHeji()'  name='teachPlans[%s].days' value='%s'>", index, value);
                            return html;
                        }
                    },{
                        field: 'total',
                        align: 'center',
                        title: '分项合计',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' readonly required min='0'  type='number' name='teachPlans[%s].total' value='%s'>", index, value);
                            return html;
                        }
                    }
                ]
            };
            $(".no-records-found").remove();
            $.table.init(options);
        });

        function fenHeji(){
            $("#bootstrap-table-plan tbody").children().each(function () {
                var self = $(this);
                var peoply = self.find("td:eq(6)").find("input").val();
                var days = self.find("td:eq(7)").find("input").val();
                if(peoply==null||peoply==''){
                    peoply=0;
                }
                if(days==null||days==''){
                    days=0;
                }
                var total = parseInt(peoply)*parseInt(days);
                self.find("td:eq(8)").find("input").attr("value",total);
            })
        }

        function addTeachPlanColumn() {
            var row = {
                beginTime: "",
                endTime: "",
                stageName: "",
                stageTarget: "",
                peoply: "",
                days: "",
                total: "",
            }
            sub.addColumn(row,"bootstrap-table-plan");
        }

        /***********************************费用估算*******************************************/
        $(function() {
            var year=new Date().getFullYear();
            var options = {
                url: ctx + "zzgg/teachCost/teachCostList?bizId="+[[${teachAtt.attId}]]+"&costType=att",
                id: "bootstrap-table-ys",
                modalName: "费用估算",
                showFooter: true,
                footerStyle: footerStyle,
                pagination: false,
                showSearch: false,
                showRefresh: false,
                showToggle: false,
                showColumns: false,
                sidePagination: "client",
                columns: [{
                    checkbox: true
                },
                    {
                        field: 'index',
                        align: 'center',
                        title: "序号",
                        width: "20",
                        formatter: function (value, row, index) {
                            var columnIndex = $.common.sprintf("<input type='hidden' name='teachCost[%s].index' value='%s'>", index, $.table.serialNumber(index));
                            var columnId = $.common.sprintf("<input type='hidden' name='teachCost[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                            return columnIndex + $.table.serialNumber(index) + columnId;
                        }
                    },
                    {
                        field: 'busiYear',
                        align: 'center',
                        title: '年度',
                        formatter: function(value, row, index) {
                            // value= year+$.table.serialNumber(index)-1;
                            var html = $.common.sprintf("<input class='form-control'  required  type='text' name='teachCost[%s].busiYear' value='%s' >", index, value);
                            return html;
                        },
                        footerFormatter:function (value) {
                            return "小计：";
                        }

                    },
                    {
                        field: 'travelCost',
                        align: 'center',
                        title: '差旅费',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  required  min='0' type='text' name='teachCost[%s].travelCost' onchange='ysSum()'  value='%s'>", index, value);
                            return html;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].travelCost!=null&&value[i].travelCost!=''){
                                    sumBalance += parseFloat(value[i].travelCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(2);
                            return  sumBalance;
                        }
                    },{
                        field: 'comCost',
                        align: 'center',
                        title: '通讯费',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required   min='0' type='text' name='teachCost[%s].comCost' onchange='ysSum()'  value='%s'>", index, value);
                            return html;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].comCost!=null&&value[i].comCost!=''){
                                    sumBalance += parseFloat(value[i].comCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(2);
                            return  sumBalance;
                        }
                    },{
                        field: 'laborCost',
                        align: 'center',
                        title: '人工费',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control'  required  min='0' type='text' name='teachCost[%s].laborCost'  onchange='ysSum()' value='%s'>", index, value);
                            return html;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].laborCost!=null&&value[i].laborCost!=''){
                                    sumBalance += parseFloat(value[i].laborCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(2);
                            return  sumBalance;
                        }
                    },{
                        field: 'otherCost',
                        align: 'center',
                        title: '其他费用',
                        formatter: function(value, row, index) {
                            var html = $.common.sprintf("<input class='form-control' required   min='0' type='text' name='teachCost[%s].otherCost' onchange='ysSum()' value='%s'>", index, value);
                            return html;
                        },
                        footerFormatter:function (value) {
                            var sumBalance = 0;
                            for (var i in value) {
                                if(value[i].otherCost!=null&&value[i].otherCost!=''){
                                    sumBalance += parseFloat(value[i].otherCost);
                                }
                            }
                            sumBalance = sumBalance.toFixed(2);
                            return  sumBalance;
                        }
                    }
                ]
            };
            $(".no-records-found").remove();
            $.table.init(options);
        });


        function addTeachCostolumn() {
            var row = {
                busiYear: "",
                travelCost: "",
                comCost: "",
                laborCost: "",
                otherCost: "",
            }
            sub.addColumn(row,"bootstrap-table-ys");
        }

        function delCostColumn() {
            sub.delColumn();
            ysSum();
        }

        //自动计算预算
        function ysSum(){
            //纵向和
            var len =$("#bootstrap-table-ys").find("tr").length;
            var travelCostTotal=0.0;
            var comCostTotal=0.0;
            var laborCostTotal=0.0;
            var otherCostTotal=0.0;
            var countMoneyTotal=0.0;
            for(var i=0;i<len-2;i++){
                //差旅费
                var travelCostMoney=$("input[name='teachCost["+i+"].travelCost']").val();
                if(travelCostMoney==null||travelCostMoney==''){
                    travelCostMoney=0.0;
                }
                travelCostTotal+=parseFloat(travelCostMoney);

                //通讯费
                var comCostMoney=$("input[name='teachCost["+i+"].comCost']").val();
                if(comCostMoney==null||comCostMoney==''){
                    comCostMoney=0.0;
                }
                comCostTotal+=parseFloat(comCostMoney);

                //人工费
                var laborCostMoney=$("input[name='teachCost["+i+"].laborCost']").val();
                if(laborCostMoney==null||laborCostMoney==''){
                    laborCostMoney=0.0;
                }
                laborCostTotal+=parseFloat(laborCostMoney);

                //其他费用
                var otherCostMoney=$("input[name='teachCost["+i+"].otherCost']").val();
                if(otherCostMoney==null||otherCostMoney==''){
                    otherCostMoney=0.0;
                }
                otherCostTotal+=parseFloat(otherCostMoney);

                //总和
                countMoneyTotal=parseFloat(travelCostTotal)+parseFloat(comCostTotal)+parseFloat(laborCostTotal)+ parseFloat(otherCostTotal);
                if(isNaN(countMoneyTotal)){
                    countMoneyTotal=0.0;
                }
            }

            $("#bootstrap-table-ys").find("tfoot").find("th").eq(3).find(".th-inner").html(travelCostTotal.toFixed(2));
            $("#bootstrap-table-ys").find("tfoot").find("th").eq(4).find(".th-inner").html(comCostTotal.toFixed(2));
            $("#bootstrap-table-ys").find("tfoot").find("th").eq(5).find(".th-inner").html(laborCostTotal.toFixed(2));
            $("#bootstrap-table-ys").find("tfoot").find("th").eq(6).find(".th-inner").html(otherCostTotal.toFixed(2));
            $("input[name='costTotal']").val(countMoneyTotal.toFixed(2));
            $("#total").html(countMoneyTotal.toFixed(2));
           /* $("#bootstrap-table-ys").find("tfoot").find("th").eq(11).find(".th-inner").html(countMoneyTotal.toFixed(4));
            $("#extra2").val(countMoneyTotal.toFixed(4));*/
        }

        function footerStyle(column) {
            return {
                budgetPerson: {
                    css: {  'font-weight': 'blod' }
                },
                index: {
                    css: {  'font-weight': 'blod' }
                }
            }[column.field]
        }
    </script>
</body>
</html>