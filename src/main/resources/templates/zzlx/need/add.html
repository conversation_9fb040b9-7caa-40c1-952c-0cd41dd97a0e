<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增滚动需求')"/>
    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: summernote-js"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <!--    <div class="form-group" th:include="include :: step(approveKind=${needEx.constants.ZZLX_GDXQSB},currentNode='MANUAL1')"></div>-->

    <form class="form-horizontal m" id="form-need-addEdit" th:object="${needEx}">
        <input th:field="*{needId}" type="hidden">
        <input th:field="*{mainId}" type="hidden">
        <input th:field="*{activityCode}" type="hidden">
        <input id="taskId" name="taskId" th:value="${taskId}" type="hidden">

        <!-- 基本信息 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#1">基本信息
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">流水号：</label>
                            <div class="col-sm-8">
                                <input class="form-control" th:field="*{serialNo}" type="hidden">
                                <div class="form-control-static" th:if="*{#strings.isEmpty(serialNo)}"
                                     th:text="需求提出后系统自动生成"></div>
                                <div class="form-control-static" th:if="*{not #strings.isEmpty(serialNo)}"
                                     th:text="*{serialNo}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">项目名称：</label>
                            <div class="col-sm-8">
                                <input class="form-control width100" required th:field="*{projectName}" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">提出人：</label>
                            <div class="col-sm-8">
                                <input class="form-control" th:field="*{tcrUserCode}" type="hidden">
                                <input class="form-control" th:field="*{tcrUserName}" type="hidden">
                                <div class="form-control-static" th:text="*{tcrUserName}"></div>
                            </div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">联系电话：</label>
                            <div class="col-sm-8">
                                <input class="form-control width100" maxlength="30" minlength="1" required
                                       th:field="*{tcrContactTel}" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-12">
                            <label class="col-sm-2 control-label is-required">项目所属分类：</label>
                            <div class="col-sm-8" th:include="/component/radio :: init(id='projectKind',name='projectKind',
                                businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='payKind',value=*{projectKind},isrequired=true )"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">项目类型：</label>
                            <div class="col-sm-8" th:include="/component/select :: init(id='projectType', name='projectType',
                                businessType=*{constants.BUSINESS_TYPE_KTTG}, dictCode='projectType',value=*{projectType},isrequired=true,isfirst=true)"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">项目范围：</label>
                            <div class="col-sm-8" th:include="/component/radio :: init(id='projectArea',name='projectArea',callback='projectAreaClick',
                                businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='projectArea',value=*{projectArea},isrequired=true )"></div>
                        </div>
                    </div>

                    <div class="row form-group srfDeptCode">
                        <input th:field="*{srfDeptCode}" type="hidden">
                        <input th:field="*{srfDeptName}" type="hidden">
                        <div class="col-sm-6" th:attr="name=*{constants.PROJECT_AREA_GFN}"
                             th:style="'display:'+@{*{projectArea eq constants.PROJECT_AREA_GFN} ? '' : 'none'}">
                            <label class="col-sm-4 control-label is-required">受让单位部门：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectOrg :: init(orgCodeId='srfDeptCodegfn',orgNameId='srfDeptNamegfn', value=*{projectArea eq constants.PROJECT_AREA_GFN} ? *{srfDeptCode} : '',selectType='S',callback='srfDeptChoice')"></div>
                        </div>
                        <div class="col-sm-6" th:attr="name=*{constants.PROJECT_AREA_JTN}"
                             th:style="'display:'+@{*{projectArea eq constants.PROJECT_AREA_JTN} ? '' : 'none'}">
                            <label class="col-sm-4 control-label is-required">受让单位部门：</label>
                            <div class="col-sm-8">
                                <select class="form-control" id="srfDeptCodejtn" name="srfDeptNamejtn"
                                        th:with="dictData=${@maintain.getJtUnit()}">
                                    <option value="">请选择</option>
                                    <option th:each="dict : ${dictData}"
                                            th:selected="${needEx.srfDeptCode eq dict.dictCode}"
                                            th:text="${dict.dictName}" th:value="${dict.dictCode}"></option>
                                </select>
                            </div>
                        </div>
                    </div>


                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">受让方负责人：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectUser :: init(userCodeId='srfFzrCode',userNameId='srfFzrName',value=*{srfFzrCode},selectType='S',isrequired=true)"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">联系电话：</label>
                            <div class="col-sm-8">
                                <input class="form-control width100" maxlength="30" minlength="1" required
                                       th:field="*{srfTel}" type="text">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 基本信息 End -->

        <!-- 现状及存在问题 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed is-required" data-parent="#version" data-toggle="collapse"
                       href="#2"><span class="txt-impt">*</span>现状及存在问题
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="xzczwtClob" th:utext="*{xzczwtClob}"></div>
                        <input th:field="*{xzczwtClob}" type="hidden">
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#3">目前技术经济指标、产品、质量等情况
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="3">
                <div class="panel-body">
                    <div class="row form-group">
                        <button class="btn btn-white btn-sm" onclick="addColumn('Q')" type="button"><i
                                class="fa fa-plus"> 增加</i></button>
                        <button class="btn btn-white btn-sm" onclick="sub.delColumn()" type="button"><i
                                class="fa fa-minus"> 删除</i></button>
                        <div class="col-sm-12 select-table table-striped">
                            <table id="bootstrap-tableQ"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                       href="#4"><span class="txt-impt">*</span>公司内技术应用情况及发展趋势
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="4">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="yyqkfzqsClob" th:utext="*{yyqkfzqsClob}"></div>
                        <input th:field="*{yyqkfzqsClob}" type="hidden">
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                       href="#5"><span class="txt-impt">*</span>建议目标
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="5">
                <div class="panel-body">
                    <div class="row form-group">
                        <textarea class="form-control width100" required rows="8" th:field="*{jymb}"
                                  type="text"></textarea>
                    </div>
                </div>
            </div>
        </div>


        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#6">推广移植后经济指标、产品、质量等情况
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="6">
                <div class="panel-body">
                    <div class="row form-group">
                        <button class="btn btn-white btn-sm" onclick="addColumn('H')" type="button"><i
                                class="fa fa-plus"> 增加</i></button>
                        <button class="btn btn-white btn-sm" onclick="sub.delColumn()" type="button"><i
                                class="fa fa-minus"> 删除</i></button>
                        <div class="col-sm-12 select-table table-striped">
                            <table id="bootstrap-tableH"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                       href="#7"><span class="txt-impt">*</span>预期产生效果
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="7">
                <div class="panel-body">
                    <div class="row form-group">
                        <textarea class="form-control width100" required rows="8" th:field="*{yqcsxg}"
                                  type="text"></textarea>
                    </div>
                </div>
            </div>
        </div>


        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse"
                       href="#8"><span class="txt-impt">*</span>前期交流情况（交流时间、交流对象（双方）、联系方式、交流结果等）
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="8">
                <div class="panel-body">
                    <div class="row form-group">
                        <textarea class="form-control width100" required rows="8" th:field="*{qqjlqk}"
                                  type="text"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#9">相关附件
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="9">
                <div class="panel-body">
                    <div class="row form-group">
                        <div th:include="/component/attachment :: init(display='none',name=*{constants.ATT_NEED},id=*{constants.ATT_NEED},sourceId=*{needId},sourceModule=*{constants.BUSINESS_TYPE},sourceLabel1=*{constants.ATT_NEED})">
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#10">推广信息
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="10">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">建议完成时间：</label>
                            <div class="col-sm-8"
                                 th:include="/component/date :: init(id='finishDate', name='finishDate',strValue=*{finishDate} ,isrequired=true)"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">其它建议单位：</label>
                            <div class="col-sm-8">
                                <input class="form-control" th:field="*{qtDeptCode}" type="hidden">
                                <input class="form-control" th:field="*{qtDeptName}" type="text">
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label is-required">建议移植单位名称：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectOrg :: init(orgCodeId='promotionDeptCode',orgNameId='promotionDeptName',value=*{promotionDeptCode},selectType='S',isrequired=true)"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="row form-group">
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button class="btn btn-primary" onclick="saveHandler()" type="button">
            <i class="fa fa-check"></i>暂 存
        </button>
        <button class="btn btn-primary" onclick="submitHandler()" type="button">
            <i class="fa fa-check"></i>提 交
        </button>
        &nbsp;
        <button class="btn btn-danger" onclick="closeItem()" type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>

<script th:src="@{/zzzc/js/common.js}"></script>
<script th:inline="javascript">
    var prefix = ctx + "zzlx/need"
    $("#form-need-addEdit").validate({
        ignore: ":hidden",
        focusCleanup: true,
        rules: {}
    });

    /**
     * 初始化数据
     */
    $(function () {
        var optionsQ = {// 目前技术经济指标、产品、质量等情况
            id: "bootstrap-tableQ",
            url: ctx + "zzgg/target/list",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.bizId = [[${needEx.needId}]];
                search.zbType = [[${needEx.constants.ZB_TYPE_Q}]];
                return search;
            },
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='targetQ[%s].index' value='%s'>", index, $.table.serialNumber(index));
                        var columnId = $.common.sprintf("<input type='hidden' name='targetQ[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                        return columnIndex + $.table.serialNumber(index) + columnId;
                    }
                },
                {
                    field: 'zbName',
                    align: 'center',
                    title: '指标名称',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='targetQ[%s].zbName' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'zbNum',
                    align: 'center',
                    title: '指标数值',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='targetQ[%s].zbNum' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'zbUnit',
                    align: 'center',
                    title: '指标单位',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='targetQ[%s].zbUnit' value='%s'>", index, value);
                        return html;
                    }
                }]
        };
        $.table.init(optionsQ);
        var optionsH = { // 推广移植后经济指标、产品、质量等情况
            id: "bootstrap-tableH",
            url: ctx + "zzgg/target/list",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.bizId = [[${needEx.needId}]];
                search.zbType = [[${needEx.constants.ZB_TYPE_H}]];
                return search;
            },
            columns: [{
                checkbox: true
            },
                {
                    field: 'index',
                    align: 'center',
                    title: "序号",
                    formatter: function (value, row, index) {
                        var columnIndex = $.common.sprintf("<input type='hidden' name='targetH[%s].index' value='%s'>", index, $.table.serialNumber(index));
                        var columnId = $.common.sprintf("<input type='hidden' name='targetH[%s].orderNum' value='%s'>", index, $.common.isEmpty(row.orderNum) ? $.table.serialNumber(index) : row.orderNum);
                        return columnIndex + $.table.serialNumber(index) + columnId;
                    }
                },
                {
                    field: 'zbName',
                    align: 'center',
                    title: '指标名称',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='targetH[%s].zbName' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'zbNum',
                    align: 'center',
                    title: '指标数值',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='targetH[%s].zbNum' value='%s'>", index, value);
                        return html;
                    }
                },
                {
                    field: 'zbUnit',
                    align: 'center',
                    title: '指标单位',
                    formatter: function (value, row, index) {
                        var html = $.common.sprintf("<input class='form-control' type='text' name='targetH[%s].zbUnit' value='%s'>", index, value);
                        return html;
                    }
                }]
        };
        $.table.init(optionsH);

        /**
         * 初始化富文本 现状及存在问题
         */
        $(".xzczwtClob").summernote({
            lang: 'zh-CN',
            height: 200,
            callbacks: {
                onImageUpload: function (files) {
                    sendFile(this, files[0], this);
                }
            }
        });
        /**
         * 初始化富文本 公司内技术应用情况及发展趋势
         */
        $(".yyqkfzqsClob").summernote({
            lang: 'zh-CN',
            height: 200,
            callbacks: {
                onImageUpload: function (files) {
                    sendFile(this, files[0], this);
                }
            }
        });
    });

    /**
     * 主子表新增行
     * @param tableId
     */
    function addColumn(tableId) {
        var row = {
            index: "",
            zbName: "",
            zbNum: "",
            zbUnit: ""
        }
        sub.addColumn(row, "bootstrap-table" + tableId);
    }

    /**
     * 单选按钮回调函数 项目范围改变 集团内 下拉配置选择   股份内  单位选择按钮
     * @param str
     */
    function projectAreaClick(str) {
        $(".srfDeptCode").children().filter($("[name='" + str + "']")).show();
        $(".srfDeptCode").children().not($("[name='" + str + "']")).hide();


        $("#srfDeptCode").val($("#srfDeptCode" + str).val());
        $("#srfDeptName").val($("#srfDeptName" + str).val());
    }

    /**
     * 受让方部门选择回调函数
     * @param deptCode
     * @param deptName
     */
    function srfDeptChoice(deptCode, deptName) {
        console.log(deptCode, deptName)
        $("#srfDeptCode").val(deptCode);
        $("#srfDeptName").val(deptName);
    }

    /**
     * 项目范围是集团内，切换部门的时候赋值
     */
    $('#srfDeptCodejtn').on('select2:select', function (e) {
        $("#srfDeptCode").val(e.params.data.id);
        $("#srfDeptName").val(e.params.data.text);
    });

    /**
     * 暂存
     */

    var sumMData = "xzczwtClob,yyqkfzqsClob";

    function saveHandler() {
        if ($.common.isEmpty($("#projectName").val())) {
            $.modal.alertWarning("请输入项目名称！");
            return;
        }
        checkClob(sumMData, false);
        saveAjax(prefix + "/saveHandler", $('#form-need-addEdit').serialize() + "&projectStatus=" + [[${needEx.constants.STATUS_ZC}]], '暂存', true);
    }

    /**
     * 提交
     */

    function submitHandler() {
        verificationJSZB();
        if ($.validate.form() && checkClob(sumMData, true)) {
            $.modal.confirm("确认提交吗？", function () {
                $.operate.saveTabAlert(prefix + "/submitHandler", $('#form-need-addEdit').serialize() + "&projectStatus=" + [[${needEx.constants.STATUS_TJ}]]);
            });
        } else {
            $.modal.alertWarning("请输入必填项！");
        }
    }


    /**
     * 验证经济指标是否有值
     * @returns {boolean}
     */
    function verificationJSZB() {
        if ($("#bootstrap-tableQ tbody tr").attr("data-index") === undefined) {
            $.modal.alertWarning("请填写目前技术经济指标、产品、质量等情况");
            return false;
        }
        if ($("#bootstrap-tableH tbody tr").attr("data-index") === undefined) {
            $.modal.alertWarning("请填写推广移植后经济指标、产品、质量等情况");
            return false;
        }
        return true;
    }


</script>
</body>
</html>