<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('【请填写功能名称】列表')"/>
    <th:block th:include="include :: baseJs"/>
</head>
<body class="gray-bg">
<div class="container-div">
    <div class="row">
        <div class="col-sm-12 search-collapse">
            <form id="formId">
                <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">流水号：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="serialNo" type="text">
                        </div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">项目名称：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="projectName" type="text">
                        </div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">项目负责人：</label>
                        <div class="col-sm-8">
                            <input class="form-control width100" name="tgfFzrName" type="text">
                        </div>
                    </div>
                </div>
                <div class="row form-group">
                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">受让单位部门：</label>
                        <div class="col-sm-8"
                             th:include="/component/selectOrg :: init(orgCodeId='srfDeptCode',orgNameId='srfDeptName',selectType='S')"></div>
                    </div>

                    <div class="col-sm-4">
                        <label class="col-sm-4 control-label">推广单位部门：</label>
                        <div class="col-sm-8"
                             th:include="/component/selectOrg :: init(orgCodeId='tgfDeptCode',orgNameId='tgfDeptName',selectType='S')"></div>
                    </div>
                </div>
                <div class="select-list">
                    <ul>
                        <li>
                            <a class="btn btn-primary btn-rounded btn-sm" onclick="$.table.search()"><i
                                    class="fa fa-search"></i>&nbsp;搜索</a>
                            <a class="btn btn-warning btn-rounded btn-sm" onclick="$.form.reset()"><i
                                    class="fa fa-refresh"></i>&nbsp;重置</a>
                        </li>
                    </ul>
                </div>
            </form>
        </div>

        <div class="btn-group-sm" id="toolbar" role="group">
            <a class="btn btn-success" onclick="$.operate.addTab()">
                <i class="fa fa-plus"></i> 添加
            </a>
        </div>
        <div class="col-sm-12 select-table table-striped">
            <table id="bootstrap-table"></table>
        </div>
    </div>
</div>

<script th:inline="javascript">
    var prefix = ctx + "zzlx/need";

    $(function () {
        var options = {
            url: prefix + "/list",
            createUrl: prefix + "/add",
            updateUrl: prefix + "/add?needId={id}",
            detailUrl: prefix + "/detail?needId={id}",
            removeUrl: prefix + "/remove",
            modalName: "滚动需求",
            columns: [{
                checkbox: true
            },
                {
                    field: 'needId',
                    visible: false
                },
                {
                    field: 'serialNo',
                    title: '流水号'
                },
                {
                    field: 'projectName',
                    title: '项目名称'
                },
                {
                    field: 'tgfFzrName',
                    title: '项目负责人'
                },
                {
                    field: 'srfDeptName',
                    title: '受让单位部门'
                },
                {
                    field: 'tgfDeptName',
                    title: '推广单位部门'
                },
                {
                    title: '操作',
                    align: 'center',
                    formatter: function (value, row, index) {
                        var actions = [];
                        actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.detailTab(\'' + row.needId + '\')"><i class="fa fa-eye"></i>查看</a> ');
                        if ($.common.isEmpty(row.serialNo) && row.tcrUserCode == [[${loginName}]]) {
                            actions.push('<a class="btn btn-success btn-xs " href="javascript:void(0)" onclick="$.operate.editTab(\'' + row.needId + '\')"><i class="fa fa-edit"></i>编辑</a> ');
                            actions.push('<a class="btn btn-danger btn-xs " href="javascript:void(0)" onclick="$.operate.remove(\'' + row.needId + '\')"><i class="fa fa-remove"></i>删除</a>');
                        }
                        return actions.join('');
                    }
                }]
        };
        $.table.init(options);
    });

</script>
</body>
</html>