<!DOCTYPE html>
<html lang="zh" xmlns:th="http://www.thymeleaf.org">
<head>
    <th:block th:include="include :: header('新增滚动需求')"/>

    <th:block th:include="include :: baseJs"/>
    <th:block th:include="include :: summernote-css"/>
    <th:block th:include="include :: summernote-js"/>
</head>
<body class="white-bg">
<div class="wrapper wrapper-content animated fadeInRight ibox-content">
    <!--    <div class="form-group" th:include="include :: step(approveKind=${needEx.constants.ZZLX_GDXQSB},currentNode='MANUAL1')"></div>-->
    <!--    <div aria-multiselectable="true" class="panel-group" id="accordion" role="tablist"></div>-->

    <form class="form-horizontal m" id="form-need-addEdit" th:object="${needEx}">
        <input th:field="*{needId}" type="hidden">
        <input th:field="*{mainId}" type="hidden">
        <input th:field="*{processInstanceId}" type="hidden">
        <input th:field="*{activityCode}" type="hidden">
        <input th:field="*{tgfXmzgCode}" type="hidden">
        <input th:field="*{srfXmzgCode}" type="hidden">
        <input id="taskId" name="taskId" th:value="${taskId}" type="hidden">
        <input id="businessGuid" name="businessGuid" th:value="${businessGuid}" type="hidden">

        <!-- 基本信息 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#1">基本信息
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="1">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">流水号：</label>
                            <div class="col-sm-8">
                                <input class="form-control" th:field="*{serialNo}" type="hidden">
                                <div class="form-control-static" th:if="*{#strings.isEmpty(serialNo)}"
                                     th:text="需求提出后系统自动生成"></div>
                                <div class="form-control-static" th:if="*{not #strings.isEmpty(serialNo)}"
                                     th:text="*{serialNo}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">项目名称：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{projectName}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">提出人：</label>
                            <div class="col-sm-8">
                                <input class="form-control" th:field="*{tcrUserCode}" type="hidden">
                                <input class="form-control" th:field="*{tcrUserName}" type="hidden">
                                <div class="form-control-static" th:text="*{tcrUserName}"></div>
                            </div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">联系电话：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{tcrContactTel}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-12">
                            <label class="col-sm-2 control-label">项目所属分类：</label>
                            <div class="col-sm-8" th:include="/component/radio :: init(id='projectKind',name='projectKind',
                                businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='payKind',value=*{projectKind},isrequired=true,see=true )"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">项目类型：</label>
                            <div class="col-sm-8" th:include="/component/select :: init(id='projectType', name='projectType',
                                businessType=*{constants.BUSINESS_TYPE_KTTG}, dictCode='projectType',value=*{projectType},isfirst=true,see=true)"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">项目范围：</label>
                            <div class="col-sm-8" th:include="/component/radio :: init(id='projectArea',name='projectArea',callback='projectAreaClick',
                                businessType=*{constants.BUSINESS_TYPE_KTTG},dictCode='projectArea',value=*{projectArea} ,see=true)"></div>
                        </div>
                    </div>

                    <div class="row form-group srfDeptCode">
                        <input th:field="*{srfDwdeptCode}" type="hidden">
                        <input th:field="*{srfDeptCode}" type="hidden">
                        <input th:field="*{srfDeptName}" type="hidden">
                        <div class="col-sm-6" th:attr="name=*{constants.PROJECT_AREA_GFN}"
                             th:style="'display:'+@{*{projectArea eq constants.PROJECT_AREA_GFN} ? '' : 'none'}">
                            <label class="col-sm-4 control-label">受让单位部门：</label>
                            <div class="col-sm-8" th:include="/component/selectOrg :: init(orgCodeId='srfDeptCode'+*{constants.PROJECT_AREA_GFN},orgNameId='srfDeptName'+*{constants.PROJECT_AREA_GFN},
                                value=*{srfDeptCode},selectType='S',see=true)"></div>
                        </div>
                        <div class="col-sm-6" th:attr="name=*{constants.PROJECT_AREA_JTN}"
                             th:style="'display:'+@{*{projectArea eq constants.PROJECT_AREA_JTN} ? '' : 'none'}">
                            <label class="col-sm-4 control-label">受让单位部门：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static"
                                     th:utext="${T(com.baosight.bscdkj.mp.ad.utils.OrgUtil).getOrgPathName(srfDeptCode)}"></div>
                            </div>
                        </div>
                    </div>


                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">受让方负责人：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectUser :: init(userCodeId='srfFzrCode',userNameId='srfFzrName',value=*{srfFzrCode},see=true)"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">联系电话：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{srfTel}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- 基本信息 End -->

        <!-- 现状及存在问题 Start -->
        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#2">现状及存在问题
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="2">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="xzczwtClob" th:utext="*{xzczwtClob}"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#3">目前技术经济指标、产品、质量等情况
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="3">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-12 select-table table-striped">
                            <table id="bootstrap-tableQ"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#4">公司内技术应用情况及发展趋势
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="4">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="yyqkfzqsClob" th:utext="*{yyqkfzqsClob}"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#5">建议目标
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="5">
                <div class="panel-body">
                    <div class="row form-group">
                        <textarea class="form-control width100" readonly rows="8" th:field="*{jymb}"
                                  type="text"></textarea>
                    </div>
                </div>
            </div>
        </div>


        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#6">推广移植后经济指标、产品、质量等情况
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="6">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-12 select-table table-striped">
                            <table id="bootstrap-tableH"></table>
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#7">预期产生效果
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="7">
                <div class="panel-body">
                    <div class="row form-group">
                        <textarea class="form-control width100" readonly rows="8" th:field="*{yqcsxg}"
                                  type="text"></textarea>
                    </div>
                </div>
            </div>
        </div>


        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#8">前期交流情况（交流时间、交流对象（双方）、联系方式、交流结果等）
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="8">
                <div class="panel-body">
                    <div class="row form-group">
                        <textarea class="form-control width100" readonly rows="8" th:field="*{qqjlqk}"
                                  type="text"></textarea>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#9">相关附件
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="9">
                <div class="panel-body">
                    <div class="row form-group">
                        <div th:include="/component/attachment :: init(display='none',name=*{constants.ATT_NEED},id=*{constants.ATT_NEED},sourceId=*{needId},sourceModule=*{constants.BUSINESS_TYPE},sourceLabel1=*{constants.ATT_NEED},see=true)">
                        </div>
                    </div>
                </div>
            </div>
        </div>


        <div class="panel panel-default panel-group">
            <div class="panel-heading">
                <h4 class="panel-title">
                    <a aria-expanded="false" class="collapsed" data-parent="#version" data-toggle="collapse" href="#10">推广信息
                        <span class="pull-right">
                                <i aria-hidden="true" class="fa fa-chevron-down"></i>
                            </span>
                    </a>
                </h4>
            </div>
            <!--折叠区域-->
            <div aria-expanded="false" class="panel-collapse collapse in" id="10">
                <div class="panel-body">
                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">建议完成时间：</label>
                            <div th:include="/component/date :: init(id='finishDate', name='finishDate',strValue=*{finishDate} ,labelName='建议完成时间：',see=true)"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">其它建议单位：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{qtDeptName}"></div>
                            </div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <input th:field="*{tgfDwdeptCode}" type="hidden">
                            <label class="col-sm-4 control-label">建议移植单位名称：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectOrg :: init(orgCodeId='promotionDeptCode',orgNameId='promotionDeptName',value=*{promotionDeptCode},selectType='S',see=true)"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">是否关键技术输出：</label>
                            <div th:include="/component/radio :: init(id='isJssc', name='isJssc',businessType=*{constants.BUSINESS_TYPE_MPTY}, dictCode=*{constants.IS_YES_NO},value=*{isJssc} ,see=true)"></div>
                        </div>
                    </div>

                    <div class="row form-group">
                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">推广方项目负责人：</label>
                            <div class="col-sm-8"
                                 th:include="/component/selectUser :: init(userCodeId='tgfFzrCode',userNameId='tgfFzrName',value=*{tgfFzrCode},see=true)"></div>
                        </div>

                        <div class="col-sm-6">
                            <label class="col-sm-4 control-label">联系电话：</label>
                            <div class="col-sm-8">
                                <div class="form-control-static" th:text="*{tgfXmfzrTel}"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!--专家评审信息-->
        <div class="panel-body" th:if="${showPS ne 'false'}">
            <div class="form-group"
                 th:include="/component/expertReview :: init(bizId=*{needId},moduleCode='zzlx_need')">
            </div>
        </div>
        <!--专家评审信息end-->

        <!-- 审批-->
        <!--        <div th:if="${! #strings.isEmpty(processInstanceId)}" th:include="kyInclude:: approveCommet(approve='false',nextStep='false',instanceId=${processInstanceId},required='true',freeFlow='false')"></div>-->
    </form>
</div>
<div class="row form-group">
    <div class="toolbar toolbar-bottom" role="toolbar">
        <button class="btn btn-primary" th:onclick="openProcessTrack([[${needEx.processInstanceId}]])"
                type="button">
            <i class="fa fa-eye"></i>&nbsp;流程跟踪图
        </button>
        <button class="btn btn-danger" onclick="closeItem()" type="button">
            <i class="fa fa-reply-all"></i>返 回
        </button>
    </div>
</div>
<!-- 审批历史 -->
<div th:if="${! #strings.isEmpty(needEx.processInstanceId)}"
     th:include="component/wfCommentList::init(processInstanceId=${needEx.processInstanceId})"></div>

<script th:inline="javascript">
    var prefix = ctx + "zzlx/need"

    /**
     * 初始化数据
     */
    $(function () {
        var optionsQ = {// 目前技术经济指标、产品、质量等情况
            id: "bootstrap-tableQ",
            url: ctx + "zzgg/target/list",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.bizId = [[${needEx.needId}]];
                search.zbType = [[${needEx.constants.ZB_TYPE_Q}]];
                return search;
            },
            columns: [
                {
                    field: 'orderNum',
                    align: 'center',
                    title: "序号"
                },
                {
                    field: 'zbName',
                    align: 'center',
                    title: '指标名称'
                },
                {
                    field: 'zbNum',
                    align: 'center',
                    title: '指标数值'
                },
                {
                    field: 'zbUnit',
                    align: 'center',
                    title: '指标单位'
                }]
        };
        $.table.init(optionsQ);
        var optionsH = { // 推广移植后经济指标、产品、质量等情况
            id: "bootstrap-tableH",
            url: ctx + "zzgg/target/list",
            pagination: false,
            showSearch: false,
            showRefresh: false,
            showToggle: false,
            showColumns: false,
            sidePagination: "client",
            queryParams: function (params) {
                var search = $.table.queryParams(params);
                search.bizId = [[${needEx.needId}]];
                search.zbType = [[${needEx.constants.ZB_TYPE_H}]];
                return search;
            },
            columns: [
                {
                    field: 'orderNum',
                    align: 'center',
                    title: "序号"
                },
                {
                    field: 'zbName',
                    align: 'center',
                    title: '指标名称'
                },
                {
                    field: 'zbNum',
                    align: 'center',
                    title: '指标数值'
                },
                {
                    field: 'zbUnit',
                    align: 'center',
                    title: '指标单位'
                }]
        };
        $.table.init(optionsH);
    });


    //流程跟踪
    function openProcessTrack(processInstanceId) {
        window.open(ctxGGMK + "web/EWPI01?inqu_status-0-processInstanceId=" + processInstanceId);
    }


</script>
</body>
</html>