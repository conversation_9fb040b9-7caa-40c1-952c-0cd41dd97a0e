##redis服务器ip地址
#iplat.redis.host=**********
##redis连接端口
#iplat.redis.port=6379
##redis连接密码，默认为空
#iplat.redis.pass=1pKcvQDu4Vc
#iplat.redis.database=1
##连接额外参数
#iplat.redis.maxWaitMillis=100000
#iplat.redis.maxTotal=300
#iplat.redis.maxIdle=100
#iplat.core.cache.redisExpireTime=28800000
#改变平台默认缓存指向为redis
#本地缓存local redis则使用redis缓存 本地调试因连不了redis需要放开下面注释
iplat.core.cache.type=local
xservices.security.cacheType=local