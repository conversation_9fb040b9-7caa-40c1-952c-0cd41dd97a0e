spring.mvc.servlet.path=/
logging.level.com.baosight=info
spring.main.allow-bean-definition-overriding=true
server.port=8080
spring.mvc.view.suffix=.jsp
spring.mvc.view.prefix=/**

spring.profiles.active=@projectEnv@

projectName=bscdkj
componentEname=bscdkj-zzzc
projectEnv=@projectEnv@

platSchema=IPLAT4J
ggmkSchema=GGMK
zzzcSchema=ZZZC
srmsSchema=KJGL
kjglSchema=KJGL
ywztSchema=YWZT

#"Baosteel Group" unicode encoding in Chinese, and ANY Chinese should be unicode encoded in this file.
enterpriseName=\u5B9D\u4FE1\u8F6F\u4EF6\u5E73\u53F0\u7814\u7A76\u4E00\u6240
customerName=\u4E2D\u56FD\u5B9D\u6B66\u94A2\u94C1\u96C6\u56E2\u6709\u9650\u516C\u53F8

configEx=iplat4j;xservices;
xservices.security.accountExpireDays=36500
xservices.security.pwdExpireDays=36500

spring.jmx.enabled=false

#时间日期格式化设置
spring.jackson.date-format=yyyy-MM-dd HH:mm:ss
spring.jackson.time-zone=GMT+8