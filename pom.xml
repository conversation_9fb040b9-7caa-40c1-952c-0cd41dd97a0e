<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>com.baosight.iplat4j</groupId>
        <artifactId>iplat4j-boot-starter</artifactId>
        <version>6.2.3</version>
    </parent>

    <groupId>com.baosight.bscdkj</groupId>
    <artifactId>bscdkj-zzzc</artifactId>
    <version>1.0.0-SNAPSHOT</version>

    <packaging>war</packaging>
    <name>bscdkj-zzzc</name>

    <properties>
        <skipTests>true</skipTests>
		<maven.deploy.skip>true</maven.deploy.skip>
    </properties>

    <dependencies>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>8.0.14</version>
        </dependency>

        <!-- eplat start -->
        <!-- 共享中心 -->
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>eplat-sdk-standard</artifactId>
            <version>1.2.1</version>
            <type>pom</type>
        </dependency>

        <!-- 定时任务 -->
        <dependency>
            <groupId>com.baosight.iplat4j</groupId>
            <artifactId>xservices-job</artifactId>
        </dependency>

        <!-- 文档上传 -->
        <dependency>
            <groupId>com.baosight.eplat</groupId>
            <artifactId>eplat-sdk-upload</artifactId>
            <version>1.1.0</version>
        </dependency>
        <!-- eplat end -->

        <!-- 引入lombok依赖 -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <scope>provided</scope>
        </dependency>
        <!-- 达梦数据库驱动包 -->
        <dependency>
            <groupId>com.dm</groupId>
            <artifactId>dm8-jdbc-driver-18</artifactId>
            <version>*********</version>
        </dependency>
        <dependency>
            <groupId>com.oracle.ojdbc</groupId>
            <artifactId>ojdbc8</artifactId>
            <version>********</version>
        </dependency>
        <!-- 工具类 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.6.5</version>
        </dependency>
        <!--thymeleaf -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-thymeleaf</artifactId>
        </dependency>

        <!--单元测试-->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
            <exclusions>
                <exclusion>
                    <artifactId>android-json</artifactId>
                    <groupId>com.vaadin.external.google</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>org.xhtmlrenderer</groupId>
            <artifactId>flying-saucer-pdf-itext5</artifactId>
            <version>9.1.20</version>
        </dependency>

        <!--easypoi -->
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-base</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-web</artifactId>
            <version>4.1.2</version>
        </dependency>
        <dependency>
            <groupId>cn.afterturn</groupId>
            <artifactId>easypoi-annotation</artifactId>
            <version>4.1.2</version>
        </dependency>

        <!--poi 版本低 -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>4.1.0</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
            <version>1.19</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-jexl3</artifactId>
            <version>3.2.1</version>
        </dependency>

		<!--html导出为word-->
        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j-ImportXHTML</artifactId>
            <version>8.3.2</version>
            <exclusions>
                <exclusion>
                    <groupId>ch.qos.logback</groupId>
                    <artifactId>logback-classic</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.docx4j</groupId>
            <artifactId>docx4j-JAXB-ReferenceImpl</artifactId>
            <version>8.3.2</version>
        </dependency>
    </dependencies>

    <profiles>
        <profile>
            <id>dev</id>
            <properties>
                <projectEnv>dev</projectEnv>
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <projectEnv>test</projectEnv>
            </properties>
        </profile>
        <profile>
            <id>run</id>
            <properties>
                <projectEnv>run</projectEnv>
            </properties>
        </profile>
        <profile>
            <!-- 对接安全中心 -->
            <id>oauth</id>
            <dependencies>
                <dependency>
                    <groupId>com.baosight.eplat</groupId>
                    <artifactId>eplat-sdk-security</artifactId>
                    <version>2.2.5</version>
                </dependency>
            </dependencies>
        </profile>
    </profiles>

    <build>
        <resources>
            <resource>
                <directory>src/main/java</directory>
                <includes>
                    <include>**/*.xml</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
                <includes>
                    <include>**/*</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/webapp</directory>
                <targetPath>META-INF/resources</targetPath>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
            <resource>
                <directory>src/main/webapp</directory>
                <includes>
                    <include>**/**</include>
                </includes>
            </resource>
        </resources>

        <plugins>
            <plugin>
                <!--用以打war包-->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <configuration>
                	<warName>${project.artifactId}</warName>
                    <warSourceExcludes>src/main/resources/META-INF/**</warSourceExcludes>
                    <packagingExcludes>WEB-INF/classes/META-INF/**</packagingExcludes>
                    <webResources>
                        <resource>
                            <directory>src/main/resources/META-INF/resources</directory>
                            <filtering>false</filtering>
                            <targetPath>/</targetPath>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>

        </plugins>
    </build>

    <repositories>
        <repository>
            <id>baocloud-maven</id>
            <name>baocloud maven</name>
            <url>https://nexus.baocloud.cn/content/groups/public/</url>
        </repository>
    </repositories>

    <pluginRepositories>
        <pluginRepository>
            <id>baocloud-maven</id>
            <name>baocloud maven</name>
            <url>https://nexus.baocloud.cn/content/groups/public/</url>
        </pluginRepository>
    </pluginRepositories>

    <distributionManagement>
        <repository>
            <id>baocloud-maven</id>
            <name>宝之云的 Maven 库</name>
            <url>https://nexus.baocloud.cn/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>baocloud-maven</id>
            <name>宝之云的 Maven SNAPSHOT库</name>
            <url>https://nexus.baocloud.cn/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>

</project>
